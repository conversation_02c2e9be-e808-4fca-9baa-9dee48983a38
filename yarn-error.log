Arguments: 
  /usr/local/bin/node /opt/homebrew/Cellar/yarn/1.22.19/libexec/bin/yarn.js install

PATH: 
  /opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/share/dotnet:~/.dotnet/tools:/Library/Frameworks/Mono.framework/Versions/Current/Commands:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/homebrew/bin:/opt/homebrew/sbin

Yarn version: 
  1.22.19

Node version: 
  19.2.0

Platform: 
  darwin arm64

Trace: 
  Error: EACCES: permission denied, mkdir '/Users/<USER>/MyCode/PandaWxApp/node_modules/bl/node_modules'

npm manifest: 
  {
    "name": "PandaWxApp",
    "version": "1.0.0",
    "private": true,
    "description": "",
    "templateInfo": {
      "name": "default",
      "typescript": true,
      "css": "sass"
    },
    "scripts": {
      "build:weapp": "taro build --type weapp",
      "build:swan": "taro build --type swan",
      "build:alipay": "taro build --type alipay",
      "build:tt": "taro build --type tt",
      "build:h5": "taro build --type h5",
      "build:rn": "taro build --type rn",
      "build:qq": "taro build --type qq",
      "build:jd": "taro build --type jd",
      "build:quickapp": "taro build --type quickapp",
      "dev:weapp": "npm run build:weapp -- --watch",
      "dev:swan": "npm run build:swan -- --watch",
      "dev:alipay": "npm run build:alipay -- --watch",
      "dev:tt": "npm run build:tt -- --watch",
      "dev:h5": "npm run build:h5 -- --watch",
      "dev:rn": "npm run build:rn -- --watch",
      "dev:qq": "npm run build:qq -- --watch",
      "dev:jd": "npm run build:jd -- --watch",
      "dev:quickapp": "npm run build:quickapp -- --watch"
    },
    "browserslist": [
      "last 3 versions",
      "Android >= 4.1",
      "ios >= 8"
    ],
    "author": "",
    "dependencies": {
      "@babel/runtime": "^7.21.5",
      "@nutui/icons-react-taro": "^0.0.1-beta.8",
      "@nutui/nutui-react": "^2.0.0-alpha.11",
      "@nutui/nutui-react-taro": "^v2.0.0-alpha.11",
      "@tarojs/components": "^3.6.5",
      "@tarojs/helper": "^3.6.5",
      "@tarojs/plugin-framework-react": "^3.6.5",
      "@tarojs/plugin-html": "^3.6.5",
      "@tarojs/plugin-platform-alipay": "^3.6.5",
      "@tarojs/plugin-platform-h5": "^3.6.5",
      "@tarojs/plugin-platform-jd": "^3.6.5",
      "@tarojs/plugin-platform-qq": "^3.6.5",
      "@tarojs/plugin-platform-swan": "^3.6.5",
      "@tarojs/plugin-platform-tt": "^3.6.5",
      "@tarojs/plugin-platform-weapp": "^3.6.5",
      "@tarojs/react": "^3.6.5",
      "@tarojs/router": "^3.6.5",
      "@tarojs/runtime": "^3.6.5",
      "@tarojs/shared": "^3.6.5",
      "@tarojs/taro": "^3.6.5",
      "@tarojs/taro-h5": "^3.6.5",
      "moment": "^2.29.4",
      "react": "^18.0.0",
      "react-dom": "^18.0.0",
      "taro-cropper": "^1.2.4"
    },
    "devDependencies": {
      "@babel/core": "^7.21.8",
      "@pmmmwh/react-refresh-webpack-plugin": "^0.5.9",
      "@tarojs/cli": "^3.6.5",
      "@tarojs/webpack5-runner": "^3.6.5",
      "@types/react": "^18.2.6",
      "@types/webpack-env": "^1.13.6",
      "@typescript-eslint/eslint-plugin": "^5.59.5",
      "@typescript-eslint/parser": "^5.59.5",
      "babel-plugin-import": "^1.13.6",
      "babel-preset-taro": "^3.6.5",
      "eslint": "^8.40.0",
      "eslint-config-taro": "^3.6.5",
      "eslint-plugin-import": "^2.27.5",
      "eslint-plugin-react": "^7.32.2",
      "eslint-plugin-react-hooks": "^4.2.0",
      "react-refresh": "^0.14.0",
      "stylelint": "^15.6.1",
      "typescript": "^5.0.3",
      "webpack": "5.82.0"
    }
  }

yarn manifest: 
  No manifest

Lockfile: 
  # THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
  # yarn lockfile v1
  
  
  "@ampproject/remapping@^2.2.0":
    version "2.2.0"
    resolved "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.0.tgz"
    integrity sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w==
    dependencies:
      "@jridgewell/gen-mapping" "^0.1.0"
      "@jridgewell/trace-mapping" "^0.3.9"
  
  "@babel/code-frame@^7.0.0", "@babel/code-frame@^7.18.6", "@babel/code-frame@^7.21.4":
    version "7.21.4"
    resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.21.4.tgz"
    integrity sha512-LYvhNKfwWSPpocw8GI7gpK2nq3HSDuEPC/uSYaALSJu9xjsalaaYFOq0Pwt5KmVqwEbZlDu81aLXwBOmD/Fv9g==
    dependencies:
      "@babel/highlight" "^7.18.6"
  
  "@babel/compat-data@^7.17.7", "@babel/compat-data@^7.18.8", "@babel/compat-data@^7.19.3", "@babel/compat-data@^7.21.5":
    version "7.21.9"
    resolved "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.21.9.tgz"
    integrity sha512-FUGed8kfhyWvbYug/Un/VPJD41rDIgoVVcR+FuzhzOYyRz5uED+Gd3SLZml0Uw2l2aHFb7ZgdW5mGA3G2cCCnQ==
  
  "@babel/core@*", "@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.12.0", "@babel/core@^7.13.0", "@babel/core@^7.14.0", "@babel/core@^7.14.5", "@babel/core@^7.21.8", "@babel/core@^7.4.0-0", "@babel/core@>=7.11.0":
    version "7.21.8"
    resolved "https://registry.npmjs.org/@babel/core/-/core-7.21.8.tgz"
    integrity sha512-YeM22Sondbo523Sz0+CirSPnbj9bG3P0CdHcBZdqUuaeOaYEFbOLoGU7lebvGP6P5J/WE9wOn7u7C4J9HvS1xQ==
    dependencies:
      "@ampproject/remapping" "^2.2.0"
      "@babel/code-frame" "^7.21.4"
      "@babel/generator" "^7.21.5"
      "@babel/helper-compilation-targets" "^7.21.5"
      "@babel/helper-module-transforms" "^7.21.5"
      "@babel/helpers" "^7.21.5"
      "@babel/parser" "^7.21.8"
      "@babel/template" "^7.20.7"
      "@babel/traverse" "^7.21.5"
      "@babel/types" "^7.21.5"
      convert-source-map "^1.7.0"
      debug "^4.1.0"
      gensync "^1.0.0-beta.2"
      json5 "^2.2.2"
      semver "^6.3.0"
  
  "@babel/eslint-parser@^7.17.0":
    version "7.19.1"
    resolved "https://registry.npmjs.org/@babel/eslint-parser/-/eslint-parser-7.19.1.tgz"
    integrity sha512-AqNf2QWt1rtu2/1rLswy6CDP7H9Oh3mMhk177Y67Rg8d7RD9WfOLLv8CGn6tisFvS2htm86yIe1yLF6I1UDaGQ==
    dependencies:
      "@nicolo-ribaudo/eslint-scope-5-internals" "5.1.1-v1"
      eslint-visitor-keys "^2.1.0"
      semver "^6.3.0"
  
  "@babel/generator@^7.21.5":
    version "7.21.9"
    resolved "https://registry.npmjs.org/@babel/generator/-/generator-7.21.9.tgz"
    integrity sha512-F3fZga2uv09wFdEjEQIJxXALXfz0+JaOb7SabvVMmjHxeVTuGW8wgE8Vp1Hd7O+zMTYtcfEISGRzPkeiaPPsvg==
    dependencies:
      "@babel/types" "^7.21.5"
      "@jridgewell/gen-mapping" "^0.3.2"
      "@jridgewell/trace-mapping" "^0.3.17"
      jsesc "^2.5.1"
  
  "@babel/helper-annotate-as-pure@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz"
    integrity sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA==
    dependencies:
      "@babel/types" "^7.18.6"
  
  "@babel/helper-builder-binary-assignment-operator-visitor@^7.18.6":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.18.9.tgz"
    integrity sha512-yFQ0YCHoIqarl8BCRwBL8ulYUaZpz3bNsA7oFepAzee+8/+ImtADXNOmO5vJvsPff3qi+hvpkY/NYBTrBQgdNw==
    dependencies:
      "@babel/helper-explode-assignable-expression" "^7.18.6"
      "@babel/types" "^7.18.9"
  
  "@babel/helper-compilation-targets@^7.17.7", "@babel/helper-compilation-targets@^7.18.9", "@babel/helper-compilation-targets@^7.19.0", "@babel/helper-compilation-targets@^7.19.3", "@babel/helper-compilation-targets@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.21.5.tgz"
    integrity sha512-1RkbFGUKex4lvsB9yhIfWltJM5cZKUftB2eNajaDv3dCMEp49iBG0K14uH8NnX9IPux2+mK7JGEOB0jn48/J6w==
    dependencies:
      "@babel/compat-data" "^7.21.5"
      "@babel/helper-validator-option" "^7.21.0"
      browserslist "^4.21.3"
      lru-cache "^5.1.1"
      semver "^6.3.0"
  
  "@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.19.0":
    version "7.19.0"
    resolved "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.19.0.tgz"
    integrity sha512-NRz8DwF4jT3UfrmUoZjd0Uph9HQnP30t7Ash+weACcyNkiYTywpIjDBgReJMKgr+n86sn2nPVVmJ28Dm053Kqw==
    dependencies:
      "@babel/helper-annotate-as-pure" "^7.18.6"
      "@babel/helper-environment-visitor" "^7.18.9"
      "@babel/helper-function-name" "^7.19.0"
      "@babel/helper-member-expression-to-functions" "^7.18.9"
      "@babel/helper-optimise-call-expression" "^7.18.6"
      "@babel/helper-replace-supers" "^7.18.9"
      "@babel/helper-split-export-declaration" "^7.18.6"
  
  "@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.19.0":
    version "7.19.0"
    resolved "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.19.0.tgz"
    integrity sha512-htnV+mHX32DF81amCDrwIDr8nrp1PTm+3wfBN9/v8QJOLEioOCOG7qNyq0nHeFiWbT3Eb7gsPwEmV64UCQ1jzw==
    dependencies:
      "@babel/helper-annotate-as-pure" "^7.18.6"
      regexpu-core "^5.1.0"
  
  "@babel/helper-define-polyfill-provider@^0.3.3":
    version "0.3.3"
    resolved "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.3.tgz"
    integrity sha512-z5aQKU4IzbqCC1XH0nAqfsFLMVSo22SBKUc0BxGrLkolTdPTructy0ToNnlO2zA4j9Q/7pjMZf0DSY+DSTYzww==
    dependencies:
      "@babel/helper-compilation-targets" "^7.17.7"
      "@babel/helper-plugin-utils" "^7.16.7"
      debug "^4.1.1"
      lodash.debounce "^4.0.8"
      resolve "^1.14.2"
      semver "^6.1.2"
  
  "@babel/helper-environment-visitor@^7.18.9", "@babel/helper-environment-visitor@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.21.5.tgz"
    integrity sha512-IYl4gZ3ETsWocUWgsFZLM5i1BYx9SoemminVEXadgLBa9TdeorzgLKm8wWLA6J1N/kT3Kch8XIk1laNzYoHKvQ==
  
  "@babel/helper-explode-assignable-expression@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.18.6.tgz"
    integrity sha512-eyAYAsQmB80jNfg4baAtLeWAQHfHFiR483rzFK+BhETlGZaQC9bsfrugfXDCbRHLQbIA7U5NxhhOxN7p/dWIcg==
    dependencies:
      "@babel/types" "^7.18.6"
  
  "@babel/helper-function-name@^7.18.9", "@babel/helper-function-name@^7.19.0", "@babel/helper-function-name@^7.21.0":
    version "7.21.0"
    resolved "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.21.0.tgz"
    integrity sha512-HfK1aMRanKHpxemaY2gqBmL04iAPOPRj7DxtNbiDOrJK+gdwkiNRVpCpUJYbUT+aZyemKN8brqTOxzCaG6ExRg==
    dependencies:
      "@babel/template" "^7.20.7"
      "@babel/types" "^7.21.0"
  
  "@babel/helper-hoist-variables@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz"
    integrity sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==
    dependencies:
      "@babel/types" "^7.18.6"
  
  "@babel/helper-member-expression-to-functions@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.18.9.tgz"
    integrity sha512-RxifAh2ZoVU67PyKIO4AMi1wTenGfMR/O/ae0CCRqwgBAt5v7xjdtRw7UoSbsreKrQn5t7r89eruK/9JjYHuDg==
    dependencies:
      "@babel/types" "^7.18.9"
  
  "@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.18.6", "@babel/helper-module-imports@^7.21.4":
    version "7.21.4"
    resolved "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.21.4.tgz"
    integrity sha512-orajc5T2PsRYUN3ZryCEFeMDYwyw09c/pZeaQEZPH0MpKzSvn3e0uXsDBu3k03VI+9DBiRo+l22BfKTpKwa/Wg==
    dependencies:
      "@babel/types" "^7.21.4"
  
  "@babel/helper-module-transforms@^7.18.6", "@babel/helper-module-transforms@^7.19.0", "@babel/helper-module-transforms@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.21.5.tgz"
    integrity sha512-bI2Z9zBGY2q5yMHoBvJ2a9iX3ZOAzJPm7Q8Yz6YeoUjU/Cvhmi2G4QyTNyPBqqXSgTjUxRg3L0xV45HvkNWWBw==
    dependencies:
      "@babel/helper-environment-visitor" "^7.21.5"
      "@babel/helper-module-imports" "^7.21.4"
      "@babel/helper-simple-access" "^7.21.5"
      "@babel/helper-split-export-declaration" "^7.18.6"
      "@babel/helper-validator-identifier" "^7.19.1"
      "@babel/template" "^7.20.7"
      "@babel/traverse" "^7.21.5"
      "@babel/types" "^7.21.5"
  
  "@babel/helper-optimise-call-expression@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.18.6.tgz"
    integrity sha512-HP59oD9/fEHQkdcbgFCnbmgH5vIQTJbxh2yf+CdM89/glUNnuzr87Q8GIjGEnOktTROemO0Pe0iPAYbqZuOUiA==
    dependencies:
      "@babel/types" "^7.18.6"
  
  "@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.18.9", "@babel/helper-plugin-utils@^7.19.0", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
    version "7.19.0"
    resolved "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.19.0.tgz"
    integrity sha512-40Ryx7I8mT+0gaNxm8JGTZFUITNqdLAgdg0hXzeVZxVD6nFsdhQvip6v8dqkRHzsz1VFpFAaOCHNn0vKBL7Czw==
  
  "@babel/helper-remap-async-to-generator@^7.18.6", "@babel/helper-remap-async-to-generator@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.18.9.tgz"
    integrity sha512-dI7q50YKd8BAv3VEfgg7PS7yD3Rtbi2J1XMXaalXO0W0164hYLnh8zpjRS0mte9MfVp/tltvr/cfdXPvJr1opA==
    dependencies:
      "@babel/helper-annotate-as-pure" "^7.18.6"
      "@babel/helper-environment-visitor" "^7.18.9"
      "@babel/helper-wrap-function" "^7.18.9"
      "@babel/types" "^7.18.9"
  
  "@babel/helper-replace-supers@^7.18.6", "@babel/helper-replace-supers@^7.18.9", "@babel/helper-replace-supers@^7.19.1":
    version "7.19.1"
    resolved "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.19.1.tgz"
    integrity sha512-T7ahH7wV0Hfs46SFh5Jz3s0B6+o8g3c+7TMxu7xKfmHikg7EAZ3I2Qk9LFhjxXq8sL7UkP5JflezNwoZa8WvWw==
    dependencies:
      "@babel/helper-environment-visitor" "^7.18.9"
      "@babel/helper-member-expression-to-functions" "^7.18.9"
      "@babel/helper-optimise-call-expression" "^7.18.6"
      "@babel/traverse" "^7.19.1"
      "@babel/types" "^7.19.0"
  
  "@babel/helper-simple-access@^7.18.6", "@babel/helper-simple-access@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.21.5.tgz"
    integrity sha512-ENPDAMC1wAjR0uaCUwliBdiSl1KBJAVnMTzXqi64c2MG8MPR6ii4qf7bSXDqSFbr4W6W028/rf5ivoHop5/mkg==
    dependencies:
      "@babel/types" "^7.21.5"
  
  "@babel/helper-skip-transparent-expression-wrappers@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.18.9.tgz"
    integrity sha512-imytd2gHi3cJPsybLRbmFrF7u5BIEuI2cNheyKi3/iOBC63kNn3q8Crn2xVuESli0aM4KYsyEqKyS7lFL8YVtw==
    dependencies:
      "@babel/types" "^7.18.9"
  
  "@babel/helper-split-export-declaration@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz"
    integrity sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==
    dependencies:
      "@babel/types" "^7.18.6"
  
  "@babel/helper-string-parser@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.21.5.tgz"
    integrity sha512-5pTUx3hAJaZIdW99sJ6ZUUgWq/Y+Hja7TowEnLNMm1VivRgZQL3vpBY3qUACVsvw+yQU6+YgfBVmcbLaZtrA1w==
  
  "@babel/helper-validator-identifier@^7.18.6", "@babel/helper-validator-identifier@^7.19.1":
    version "7.19.1"
    resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz"
    integrity sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==
  
  "@babel/helper-validator-option@^7.18.6", "@babel/helper-validator-option@^7.21.0":
    version "7.21.0"
    resolved "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.0.tgz"
    integrity sha512-rmL/B8/f0mKS2baE9ZpyTcTavvEuWhTTW8amjzXNvYG4AwBsqTLikfXsEofsJEfKHf+HQVQbFOHy6o+4cnC/fQ==
  
  "@babel/helper-wrap-function@^7.18.9":
    version "7.19.0"
    resolved "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.19.0.tgz"
    integrity sha512-txX8aN8CZyYGTwcLhlk87KRqncAzhh5TpQamZUa0/u3an36NtDpUP6bQgBCBcLeBs09R/OwQu3OjK0k/HwfNDg==
    dependencies:
      "@babel/helper-function-name" "^7.19.0"
      "@babel/template" "^7.18.10"
      "@babel/traverse" "^7.19.0"
      "@babel/types" "^7.19.0"
  
  "@babel/helpers@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/helpers/-/helpers-7.21.5.tgz"
    integrity sha512-BSY+JSlHxOmGsPTydUkPf1MdMQ3M81x5xGCOVgWM3G8XH77sJ292Y2oqcp0CbbgxhqBuI46iUz1tT7hqP7EfgA==
    dependencies:
      "@babel/template" "^7.20.7"
      "@babel/traverse" "^7.21.5"
      "@babel/types" "^7.21.5"
  
  "@babel/highlight@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/highlight/-/highlight-7.18.6.tgz"
    integrity sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==
    dependencies:
      "@babel/helper-validator-identifier" "^7.18.6"
      chalk "^2.0.0"
      js-tokens "^4.0.0"
  
  "@babel/parser@^7.14.5", "@babel/parser@^7.16.4", "@babel/parser@^7.20.0", "@babel/parser@^7.20.7", "@babel/parser@^7.21.5", "@babel/parser@^7.21.8":
    version "7.21.9"
    resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.21.9.tgz"
    integrity sha512-q5PNg/Bi1OpGgx5jYlvWZwAorZepEudDMCLtj967aeS7WMont7dUZI46M2XwcIQqvUlMxWfdLFu4S/qSxeUu5g==
  
  "@babel/parser@7.16.4":
    version "7.16.4"
    resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.16.4.tgz"
    integrity sha512-6V0qdPUaiVHH3RtZeLIsc+6pDhbYzHR8ogA8w+f+Wc77DuXto19g2QUwveINoS34Uw+W8/hQDGJCx+i4n7xcng==
  
  "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.18.6.tgz"
    integrity sha512-Dgxsyg54Fx1d4Nge8UnvTrED63vrwOdPmyvPzlNN/boaliRP54pm3pGzZD1SJUwrBA+Cs/xdG8kXX6Mn/RfISQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.18.9.tgz"
    integrity sha512-AHrP9jadvH7qlOj6PINbgSuphjQUAK7AOT7DPjBo9EHoLhQTnnK5u45e1Hd4DbSQEO9nqPWtQ89r+XEOWFScKg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.9"
      "@babel/helper-skip-transparent-expression-wrappers" "^7.18.9"
      "@babel/plugin-proposal-optional-chaining" "^7.18.9"
  
  "@babel/plugin-proposal-async-generator-functions@^7.0.0", "@babel/plugin-proposal-async-generator-functions@^7.19.1":
    version "7.19.1"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.19.1.tgz"
    integrity sha512-0yu8vNATgLy4ivqMNBIwb1HebCelqN7YX8SL3FDXORv/RqT0zEEWUCH4GH44JsSrvCu6GqnAdR5EBFAPeNBB4Q==
    dependencies:
      "@babel/helper-environment-visitor" "^7.18.9"
      "@babel/helper-plugin-utils" "^7.19.0"
      "@babel/helper-remap-async-to-generator" "^7.18.9"
      "@babel/plugin-syntax-async-generators" "^7.8.4"
  
  "@babel/plugin-proposal-class-properties@^7.0.0", "@babel/plugin-proposal-class-properties@^7.14.5", "@babel/plugin-proposal-class-properties@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz"
    integrity sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==
    dependencies:
      "@babel/helper-create-class-features-plugin" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-proposal-class-static-block@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.18.6.tgz"
    integrity sha512-+I3oIiNxrCpup3Gi8n5IGMwj0gOCAjcJUSQEcotNnCCPMEnixawOQ+KeJPlgfjzx+FKQ1QSyZOWe7wmoJp7vhw==
    dependencies:
      "@babel/helper-create-class-features-plugin" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.18.6"
      "@babel/plugin-syntax-class-static-block" "^7.14.5"
  
  "@babel/plugin-proposal-decorators@^7.14.5":
    version "7.19.3"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.19.3.tgz"
    integrity sha512-MbgXtNXqo7RTKYIXVchVJGPvaVufQH3pxvQyfbGvNw1DObIhph+PesYXJTcd8J4DdWibvf6Z2eanOyItX8WnJg==
    dependencies:
      "@babel/helper-create-class-features-plugin" "^7.19.0"
      "@babel/helper-plugin-utils" "^7.19.0"
      "@babel/helper-replace-supers" "^7.19.1"
      "@babel/helper-split-export-declaration" "^7.18.6"
      "@babel/plugin-syntax-decorators" "^7.19.0"
  
  "@babel/plugin-proposal-dynamic-import@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.18.6.tgz"
    integrity sha512-1auuwmK+Rz13SJj36R+jqFPMJWyKEDd7lLSdOj4oJK0UTgGueSAtkrCvz9ewmgyU/P941Rv2fQwZJN8s6QruXw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
      "@babel/plugin-syntax-dynamic-import" "^7.8.3"
  
  "@babel/plugin-proposal-export-default-from@^7.0.0":
    version "7.18.10"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-export-default-from/-/plugin-proposal-export-default-from-7.18.10.tgz"
    integrity sha512-5H2N3R2aQFxkV4PIBUR/i7PUSwgTZjouJKzI8eKswfIjT0PhvzkPn0t0wIS5zn6maQuvtT0t1oHtMUz61LOuow==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.9"
      "@babel/plugin-syntax-export-default-from" "^7.18.6"
  
  "@babel/plugin-proposal-export-namespace-from@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.18.9.tgz"
    integrity sha512-k1NtHyOMvlDDFeb9G5PhUXuGj8m/wiwojgQVEhJ/fsVsMCpLyOP4h0uGEjYJKrRI+EVPlb5Jk+Gt9P97lOGwtA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.9"
      "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
  
  "@babel/plugin-proposal-json-strings@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.18.6.tgz"
    integrity sha512-lr1peyn9kOdbYc0xr0OdHTZ5FMqS6Di+H0Fz2I/JwMzGmzJETNeOFq2pBySw6X/KFL5EWDjlJuMsUGRFb8fQgQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
      "@babel/plugin-syntax-json-strings" "^7.8.3"
  
  "@babel/plugin-proposal-logical-assignment-operators@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.18.9.tgz"
    integrity sha512-128YbMpjCrP35IOExw2Fq+x55LMP42DzhOhX2aNNIdI9avSWl2PI0yuBWarr3RYpZBSPtabfadkH2yeRiMD61Q==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.9"
      "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
  
  "@babel/plugin-proposal-nullish-coalescing-operator@^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz"
    integrity sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
      "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
  
  "@babel/plugin-proposal-numeric-separator@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.18.6.tgz"
    integrity sha512-ozlZFogPqoLm8WBr5Z8UckIoE4YQ5KESVcNudyXOR8uqIkliTEgJ3RoketfG6pmzLdeZF0H/wjE9/cCEitBl7Q==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
      "@babel/plugin-syntax-numeric-separator" "^7.10.4"
  
  "@babel/plugin-proposal-object-rest-spread@^7.0.0", "@babel/plugin-proposal-object-rest-spread@^7.14.5", "@babel/plugin-proposal-object-rest-spread@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.18.9.tgz"
    integrity sha512-kDDHQ5rflIeY5xl69CEqGEZ0KY369ehsCIEbTGb4siHG5BE9sga/T0r0OUwyZNLMmZE79E1kbsqAjwFCW4ds6Q==
    dependencies:
      "@babel/compat-data" "^7.18.8"
      "@babel/helper-compilation-targets" "^7.18.9"
      "@babel/helper-plugin-utils" "^7.18.9"
      "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
      "@babel/plugin-transform-parameters" "^7.18.8"
  
  "@babel/plugin-proposal-optional-catch-binding@^7.0.0", "@babel/plugin-proposal-optional-catch-binding@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.18.6.tgz"
    integrity sha512-Q40HEhs9DJQyaZfUjjn6vE8Cv4GmMHCYuMGIWUnlxH6400VGxOuwWsPt4FxXxJkC/5eOzgn0z21M9gMT4MOhbw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
      "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
  
  "@babel/plugin-proposal-optional-chaining@^7.0.0", "@babel/plugin-proposal-optional-chaining@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.18.9.tgz"
    integrity sha512-v5nwt4IqBXihxGsW2QmCWMDS3B3bzGIk/EQVZz2ei7f3NJl8NzAJVvUmpDW5q1CRNY+Beb/k58UAH1Km1N411w==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.9"
      "@babel/helper-skip-transparent-expression-wrappers" "^7.18.9"
      "@babel/plugin-syntax-optional-chaining" "^7.8.3"
  
  "@babel/plugin-proposal-private-methods@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.18.6.tgz"
    integrity sha512-nutsvktDItsNn4rpGItSNV2sz1XwS+nfU0Rg8aCx3W3NOKVzdMjJRu0O5OkgDp3ZGICSTbgRpxZoWsxoKRvbeA==
    dependencies:
      "@babel/helper-create-class-features-plugin" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-proposal-private-property-in-object@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.18.6.tgz"
    integrity sha512-9Rysx7FOctvT5ouj5JODjAFAkgGoudQuLPamZb0v1TGLpapdNaftzifU8NTWQm0IRjqoYypdrSmyWgkocDQ8Dw==
    dependencies:
      "@babel/helper-annotate-as-pure" "^7.18.6"
      "@babel/helper-create-class-features-plugin" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.18.6"
      "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
  
  "@babel/plugin-proposal-unicode-property-regex@^7.18.6", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.18.6.tgz"
    integrity sha512-2BShG/d5yoZyXZfVePH91urL5wTG6ASZU9M4o03lKK8u8UW1y08OMttBSOADTcJrnPMpvDXRG3G8fyLh4ovs8w==
    dependencies:
      "@babel/helper-create-regexp-features-plugin" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-syntax-async-generators@^7.8.4":
    version "7.8.4"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
    integrity sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.8.0"
  
  "@babel/plugin-syntax-class-properties@^7.12.13":
    version "7.12.13"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
    integrity sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.12.13"
  
  "@babel/plugin-syntax-class-static-block@^7.14.5":
    version "7.14.5"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
    integrity sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.14.5"
  
  "@babel/plugin-syntax-decorators@^7.19.0":
    version "7.19.0"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.19.0.tgz"
    integrity sha512-xaBZUEDntt4faL1yN8oIFlhfXeQAWJW7CLKYsHTUqriCUbj8xOra8bfxxKGi/UwExPFBuPdH4XfHc9rGQhrVkQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.19.0"
  
  "@babel/plugin-syntax-dynamic-import@^7.0.0", "@babel/plugin-syntax-dynamic-import@^7.8.3":
    version "7.8.3"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
    integrity sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.8.0"
  
  "@babel/plugin-syntax-export-default-from@^7.0.0", "@babel/plugin-syntax-export-default-from@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-export-default-from/-/plugin-syntax-export-default-from-7.18.6.tgz"
    integrity sha512-Kr//z3ujSVNx6E9z9ih5xXXMqK07VVTuqPmqGe6Mss/zW5XPeLZeSDZoP9ab/hT4wPKqAgjl2PnhPrcpk8Seew==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-syntax-export-namespace-from@^7.8.3":
    version "7.8.3"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz"
    integrity sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==
    dependencies:
      "@babel/helper-plugin-utils" "^7.8.3"
  
  "@babel/plugin-syntax-flow@^7.18.6", "@babel/plugin-syntax-flow@^7.2.0":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.18.6.tgz"
    integrity sha512-LUbR+KNTBWCUAqRG9ex5Gnzu2IOkt8jRJbHHXFT9q+L9zm7M/QQbEqXyw1n1pohYvOyWC8CjeyjrSaIwiYjK7A==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-syntax-import-assertions@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.18.6.tgz"
    integrity sha512-/DU3RXad9+bZwrgWJQKbr39gYbJpLJHezqEzRzi/BHRlJ9zsQb4CK2CA/5apllXNomwA1qHwzvHl+AdEmC5krQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-syntax-json-strings@^7.8.3":
    version "7.8.3"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
    integrity sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.8.0"
  
  "@babel/plugin-syntax-jsx@^7.14.5", "@babel/plugin-syntax-jsx@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.18.6.tgz"
    integrity sha512-6mmljtAedFGTWu2p/8WIORGwy+61PLgOMPOdazc7YoJ9ZCWUyFy3A6CpPkRKLKD1ToAesxX8KGEViAiLo9N+7Q==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
    version "7.10.4"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
    integrity sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==
    dependencies:
      "@babel/helper-plugin-utils" "^7.10.4"
  
  "@babel/plugin-syntax-nullish-coalescing-operator@^7.0.0", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
    version "7.8.3"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
    integrity sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.8.0"
  
  "@babel/plugin-syntax-numeric-separator@^7.10.4":
    version "7.10.4"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
    integrity sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==
    dependencies:
      "@babel/helper-plugin-utils" "^7.10.4"
  
  "@babel/plugin-syntax-object-rest-spread@^7.8.3":
    version "7.8.3"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
    integrity sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.8.0"
  
  "@babel/plugin-syntax-optional-catch-binding@^7.8.3":
    version "7.8.3"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
    integrity sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==
    dependencies:
      "@babel/helper-plugin-utils" "^7.8.0"
  
  "@babel/plugin-syntax-optional-chaining@^7.0.0", "@babel/plugin-syntax-optional-chaining@^7.8.3":
    version "7.8.3"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
    integrity sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.8.0"
  
  "@babel/plugin-syntax-private-property-in-object@^7.14.5":
    version "7.14.5"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
    integrity sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.14.5"
  
  "@babel/plugin-syntax-top-level-await@^7.14.5":
    version "7.14.5"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
    integrity sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.14.5"
  
  "@babel/plugin-syntax-typescript@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.18.6.tgz"
    integrity sha512-mAWAuq4rvOepWCBid55JuRNvpTNf2UGVgoz4JV0fXEKolsVZDzsa4NqCef758WZJj/GDu0gVGItjKFiClTAmZA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-arrow-functions@^7.0.0", "@babel/plugin-transform-arrow-functions@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.18.6.tgz"
    integrity sha512-9S9X9RUefzrsHZmKMbDXxweEH+YlE8JJEuat9FdvW9Qh1cw7W64jELCtWNkPBPX5En45uy28KGvA/AySqUh8CQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-async-to-generator@^7.0.0", "@babel/plugin-transform-async-to-generator@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.18.6.tgz"
    integrity sha512-ARE5wZLKnTgPW7/1ftQmSi1CmkqqHo2DNmtztFhvgtOWSDfq0Cq9/9L+KnZNYSNrydBekhW3rwShduf59RoXag==
    dependencies:
      "@babel/helper-module-imports" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.18.6"
      "@babel/helper-remap-async-to-generator" "^7.18.6"
  
  "@babel/plugin-transform-block-scoped-functions@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.18.6.tgz"
    integrity sha512-ExUcOqpPWnliRcPqves5HJcJOvHvIIWfuS4sroBUenPuMdmW+SMHDakmtS7qOo13sVppmUijqeTv7qqGsvURpQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-block-scoping@^7.0.0", "@babel/plugin-transform-block-scoping@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.18.9.tgz"
    integrity sha512-5sDIJRV1KtQVEbt/EIBwGy4T01uYIo4KRB3VUqzkhrAIOGx7AoctL9+Ux88btY0zXdDyPJ9mW+bg+v+XEkGmtw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.9"
  
  "@babel/plugin-transform-classes@^7.0.0", "@babel/plugin-transform-classes@^7.19.0":
    version "7.19.0"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.19.0.tgz"
    integrity sha512-YfeEE9kCjqTS9IitkgfJuxjcEtLUHMqa8yUJ6zdz8vR7hKuo6mOy2C05P0F1tdMmDCeuyidKnlrw/iTppHcr2A==
    dependencies:
      "@babel/helper-annotate-as-pure" "^7.18.6"
      "@babel/helper-compilation-targets" "^7.19.0"
      "@babel/helper-environment-visitor" "^7.18.9"
      "@babel/helper-function-name" "^7.19.0"
      "@babel/helper-optimise-call-expression" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.19.0"
      "@babel/helper-replace-supers" "^7.18.9"
      "@babel/helper-split-export-declaration" "^7.18.6"
      globals "^11.1.0"
  
  "@babel/plugin-transform-computed-properties@^7.0.0", "@babel/plugin-transform-computed-properties@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.18.9.tgz"
    integrity sha512-+i0ZU1bCDymKakLxn5srGHrsAPRELC2WIbzwjLhHW9SIE1cPYkLCL0NlnXMZaM1vhfgA2+M7hySk42VBvrkBRw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.9"
  
  "@babel/plugin-transform-destructuring@^7.0.0", "@babel/plugin-transform-destructuring@^7.18.13":
    version "7.18.13"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.18.13.tgz"
    integrity sha512-TodpQ29XekIsex2A+YJPj5ax2plkGa8YYY6mFjCohk/IG9IY42Rtuj1FuDeemfg2ipxIFLzPeA83SIBnlhSIow==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.9"
  
  "@babel/plugin-transform-dotall-regex@^7.18.6", "@babel/plugin-transform-dotall-regex@^7.4.4":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.18.6.tgz"
    integrity sha512-6S3jpun1eEbAxq7TdjLotAsl4WpQI9DxfkycRcKrjhQYzU87qpXdknpBg/e+TdcMehqGnLFi7tnFUBR02Vq6wg==
    dependencies:
      "@babel/helper-create-regexp-features-plugin" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-duplicate-keys@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.18.9.tgz"
    integrity sha512-d2bmXCtZXYc59/0SanQKbiWINadaJXqtvIQIzd4+hNwkWBgyCd5F/2t1kXoUdvPMrxzPvhK6EMQRROxsue+mfw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.9"
  
  "@babel/plugin-transform-exponentiation-operator@^7.0.0", "@babel/plugin-transform-exponentiation-operator@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.18.6.tgz"
    integrity sha512-wzEtc0+2c88FVR34aQmiz56dxEkxr2g8DQb/KfaFa1JYXOFVsbhvAonFN6PwVWj++fKmku8NP80plJ5Et4wqHw==
    dependencies:
      "@babel/helper-builder-binary-assignment-operator-visitor" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-flow-strip-types@^7.0.0":
    version "7.19.0"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.19.0.tgz"
    integrity sha512-sgeMlNaQVbCSpgLSKP4ZZKfsJVnFnNQlUSk6gPYzR/q7tzCgQF2t8RBKAP6cKJeZdveei7Q7Jm527xepI8lNLg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.19.0"
      "@babel/plugin-syntax-flow" "^7.18.6"
  
  "@babel/plugin-transform-for-of@^7.18.8":
    version "7.18.8"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.18.8.tgz"
    integrity sha512-yEfTRnjuskWYo0k1mHUqrVWaZwrdq8AYbfrpqULOJOaucGSp4mNMVps+YtA8byoevxS/urwU75vyhQIxcCgiBQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-function-name@^7.0.0", "@babel/plugin-transform-function-name@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.18.9.tgz"
    integrity sha512-WvIBoRPaJQ5yVHzcnJFor7oS5Ls0PYixlTYE63lCj2RtdQEl15M68FXQlxnG6wdraJIXRdR7KI+hQ7q/9QjrCQ==
    dependencies:
      "@babel/helper-compilation-targets" "^7.18.9"
      "@babel/helper-function-name" "^7.18.9"
      "@babel/helper-plugin-utils" "^7.18.9"
  
  "@babel/plugin-transform-literals@^7.0.0", "@babel/plugin-transform-literals@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.18.9.tgz"
    integrity sha512-IFQDSRoTPnrAIrI5zoZv73IFeZu2dhu6irxQjY9rNjTT53VmKg9fenjvoiOWOkJ6mm4jKVPtdMzBY98Fp4Z4cg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.9"
  
  "@babel/plugin-transform-member-expression-literals@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.18.6.tgz"
    integrity sha512-qSF1ihLGO3q+/g48k85tUjD033C29TNTVB2paCwZPVmOsjn9pClvYYrM2VeJpBY2bcNkuny0YUyTNRyRxJ54KA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-modules-amd@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.18.6.tgz"
    integrity sha512-Pra5aXsmTsOnjM3IajS8rTaLCy++nGM4v3YR4esk5PCsyg9z8NA5oQLwxzMUtDBd8F+UmVza3VxoAaWCbzH1rg==
    dependencies:
      "@babel/helper-module-transforms" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.18.6"
      babel-plugin-dynamic-import-node "^2.3.3"
  
  "@babel/plugin-transform-modules-commonjs@^7.0.0", "@babel/plugin-transform-modules-commonjs@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.18.6.tgz"
    integrity sha512-Qfv2ZOWikpvmedXQJDSbxNqy7Xr/j2Y8/KfijM0iJyKkBTmWuvCA1yeH1yDM7NJhBW/2aXxeucLj6i80/LAJ/Q==
    dependencies:
      "@babel/helper-module-transforms" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.18.6"
      "@babel/helper-simple-access" "^7.18.6"
      babel-plugin-dynamic-import-node "^2.3.3"
  
  "@babel/plugin-transform-modules-systemjs@^7.19.0":
    version "7.19.0"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.19.0.tgz"
    integrity sha512-x9aiR0WXAWmOWsqcsnrzGR+ieaTMVyGyffPVA7F8cXAGt/UxefYv6uSHZLkAFChN5M5Iy1+wjE+xJuPt22H39A==
    dependencies:
      "@babel/helper-hoist-variables" "^7.18.6"
      "@babel/helper-module-transforms" "^7.19.0"
      "@babel/helper-plugin-utils" "^7.19.0"
      "@babel/helper-validator-identifier" "^7.18.6"
      babel-plugin-dynamic-import-node "^2.3.3"
  
  "@babel/plugin-transform-modules-umd@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.18.6.tgz"
    integrity sha512-dcegErExVeXcRqNtkRU/z8WlBLnvD4MRnHgNs3MytRO1Mn1sHRyhbcpYbVMGclAqOjdW+9cfkdZno9dFdfKLfQ==
    dependencies:
      "@babel/helper-module-transforms" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-named-capturing-groups-regex@^7.0.0", "@babel/plugin-transform-named-capturing-groups-regex@^7.19.1":
    version "7.19.1"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.19.1.tgz"
    integrity sha512-oWk9l9WItWBQYS4FgXD4Uyy5kq898lvkXpXQxoJEY1RnvPk4R/Dvu2ebXU9q8lP+rlMwUQTFf2Ok6d78ODa0kw==
    dependencies:
      "@babel/helper-create-regexp-features-plugin" "^7.19.0"
      "@babel/helper-plugin-utils" "^7.19.0"
  
  "@babel/plugin-transform-new-target@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.18.6.tgz"
    integrity sha512-DjwFA/9Iu3Z+vrAn+8pBUGcjhxKguSMlsFqeCKbhb9BAV756v0krzVK04CRDi/4aqmk8BsHb4a/gFcaA5joXRw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-object-super@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.18.6.tgz"
    integrity sha512-uvGz6zk+pZoS1aTZrOvrbj6Pp/kK2mp45t2B+bTDre2UgsZZ8EZLSJtUg7m/no0zOJUWgFONpB7Zv9W2tSaFlA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
      "@babel/helper-replace-supers" "^7.18.6"
  
  "@babel/plugin-transform-parameters@^7.0.0", "@babel/plugin-transform-parameters@^7.18.8":
    version "7.18.8"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.18.8.tgz"
    integrity sha512-ivfbE3X2Ss+Fj8nnXvKJS6sjRG4gzwPMsP+taZC+ZzEGjAYlvENixmt1sZ5Ca6tWls+BlKSGKPJ6OOXvXCbkFg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-property-literals@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.18.6.tgz"
    integrity sha512-cYcs6qlgafTud3PAzrrRNbQtfpQ8+y/+M5tKmksS9+M1ckbH6kzY8MrexEM9mcA6JDsukE19iIRvAyYl463sMg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-react-display-name@^7.0.0", "@babel/plugin-transform-react-display-name@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.18.6.tgz"
    integrity sha512-TV4sQ+T013n61uMoygyMRm+xf04Bd5oqFpv2jAEQwSZ8NwQA7zeRPg1LMVg2PWi3zWBz+CLKD+v5bcpZ/BS0aA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-react-jsx-development@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.18.6.tgz"
    integrity sha512-SA6HEjwYFKF7WDjWcMcMGUimmw/nhNRDWxr+KaLSCrkD/LMDBvWRmHAYgE1HDeF8KUuI8OAu+RT6EOtKxSW2qA==
    dependencies:
      "@babel/plugin-transform-react-jsx" "^7.18.6"
  
  "@babel/plugin-transform-react-jsx-self@^7.0.0":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.18.6.tgz"
    integrity sha512-A0LQGx4+4Jv7u/tWzoJF7alZwnBDQd6cGLh9P+Ttk4dpiL+J5p7NSNv/9tlEFFJDq3kjxOavWmbm6t0Gk+A3Ig==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-react-jsx-source@^7.0.0":
    version "7.19.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.19.6.tgz"
    integrity sha512-RpAi004QyMNisst/pvSanoRdJ4q+jMCWyk9zdw/CyLB9j8RXEahodR6l2GyttDRyEVWZtbN+TpLiHJ3t34LbsQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.19.0"
  
  "@babel/plugin-transform-react-jsx@^7.0.0", "@babel/plugin-transform-react-jsx@^7.18.6":
    version "7.19.0"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.19.0.tgz"
    integrity sha512-UVEvX3tXie3Szm3emi1+G63jyw1w5IcMY0FSKM+CRnKRI5Mr1YbCNgsSTwoTwKphQEG9P+QqmuRFneJPZuHNhg==
    dependencies:
      "@babel/helper-annotate-as-pure" "^7.18.6"
      "@babel/helper-module-imports" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.19.0"
      "@babel/plugin-syntax-jsx" "^7.18.6"
      "@babel/types" "^7.19.0"
  
  "@babel/plugin-transform-react-pure-annotations@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.18.6.tgz"
    integrity sha512-I8VfEPg9r2TRDdvnHgPepTKvuRomzA8+u+nhY7qSI1fR2hRNebasZEETLyM5mAUr0Ku56OkXJ0I7NHJnO6cJiQ==
    dependencies:
      "@babel/helper-annotate-as-pure" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-regenerator@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.18.6.tgz"
    integrity sha512-poqRI2+qiSdeldcz4wTSTXBRryoq3Gc70ye7m7UD5Ww0nE29IXqMl6r7Nd15WBgRd74vloEMlShtH6CKxVzfmQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
      regenerator-transform "^0.15.0"
  
  "@babel/plugin-transform-reserved-words@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.18.6.tgz"
    integrity sha512-oX/4MyMoypzHjFrT1CdivfKZ+XvIPMFXwwxHp/r0Ddy2Vuomt4HDFGmft1TAY2yiTKiNSsh3kjBAzcM8kSdsjA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-runtime@^7.0.0", "@babel/plugin-transform-runtime@^7.14.5":
    version "7.19.1"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.19.1.tgz"
    integrity sha512-2nJjTUFIzBMP/f/miLxEK9vxwW/KUXsdvN4sR//TmuDhe6yU2h57WmIOE12Gng3MDP/xpjUV/ToZRdcf8Yj4fA==
    dependencies:
      "@babel/helper-module-imports" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.19.0"
      babel-plugin-polyfill-corejs2 "^0.3.3"
      babel-plugin-polyfill-corejs3 "^0.6.0"
      babel-plugin-polyfill-regenerator "^0.4.1"
      semver "^6.3.0"
  
  "@babel/plugin-transform-shorthand-properties@^7.0.0", "@babel/plugin-transform-shorthand-properties@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.18.6.tgz"
    integrity sha512-eCLXXJqv8okzg86ywZJbRn19YJHU4XUa55oz2wbHhaQVn/MM+XhukiT7SYqp/7o00dg52Rj51Ny+Ecw4oyoygw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-spread@^7.0.0", "@babel/plugin-transform-spread@^7.19.0":
    version "7.19.0"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.19.0.tgz"
    integrity sha512-RsuMk7j6n+r752EtzyScnWkQyuJdli6LdO5Klv8Yx0OfPVTcQkIUfS8clx5e9yHXzlnhOZF3CbQ8C2uP5j074w==
    dependencies:
      "@babel/helper-plugin-utils" "^7.19.0"
      "@babel/helper-skip-transparent-expression-wrappers" "^7.18.9"
  
  "@babel/plugin-transform-sticky-regex@^7.0.0", "@babel/plugin-transform-sticky-regex@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.18.6.tgz"
    integrity sha512-kfiDrDQ+PBsQDO85yj1icueWMfGfJFKN1KCkndygtu/C9+XUfydLC8Iv5UYJqRwy4zk8EcplRxEOeLyjq1gm6Q==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/plugin-transform-template-literals@^7.0.0", "@babel/plugin-transform-template-literals@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.18.9.tgz"
    integrity sha512-S8cOWfT82gTezpYOiVaGHrCbhlHgKhQt8XH5ES46P2XWmX92yisoZywf5km75wv5sYcXDUCLMmMxOLCtthDgMA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.9"
  
  "@babel/plugin-transform-typeof-symbol@^7.18.9":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.18.9.tgz"
    integrity sha512-SRfwTtF11G2aemAZWivL7PD+C9z52v9EvMqH9BuYbabyPuKUvSWks3oCg6041pT925L4zVFqaVBeECwsmlguEw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.9"
  
  "@babel/plugin-transform-typescript@^7.18.6", "@babel/plugin-transform-typescript@^7.5.0":
    version "7.19.3"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.19.3.tgz"
    integrity sha512-z6fnuK9ve9u/0X0rRvI9MY0xg+DOUaABDYOe+/SQTxtlptaBB/V9JIUxJn6xp3lMBeb9qe8xSFmHU35oZDXD+w==
    dependencies:
      "@babel/helper-create-class-features-plugin" "^7.19.0"
      "@babel/helper-plugin-utils" "^7.19.0"
      "@babel/plugin-syntax-typescript" "^7.18.6"
  
  "@babel/plugin-transform-unicode-escapes@^7.18.10":
    version "7.18.10"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.18.10.tgz"
    integrity sha512-kKAdAI+YzPgGY/ftStBFXTI1LZFju38rYThnfMykS+IXy8BVx+res7s2fxf1l8I35DV2T97ezo6+SGrXz6B3iQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.9"
  
  "@babel/plugin-transform-unicode-regex@^7.0.0", "@babel/plugin-transform-unicode-regex@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.18.6.tgz"
    integrity sha512-gE7A6Lt7YLnNOL3Pb9BNeZvi+d8l7tcRrG4+pwJjK9hD2xX4mEvjlQW60G9EEmfXVYRPv9VRQcyegIVHCql/AA==
    dependencies:
      "@babel/helper-create-regexp-features-plugin" "^7.18.6"
      "@babel/helper-plugin-utils" "^7.18.6"
  
  "@babel/preset-env@^7.14.5":
    version "7.19.3"
    resolved "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.19.3.tgz"
    integrity sha512-ziye1OTc9dGFOAXSWKUqQblYHNlBOaDl8wzqf2iKXJAltYiR3hKHUKmkt+S9PppW7RQpq4fFCrwwpIDj/f5P4w==
    dependencies:
      "@babel/compat-data" "^7.19.3"
      "@babel/helper-compilation-targets" "^7.19.3"
      "@babel/helper-plugin-utils" "^7.19.0"
      "@babel/helper-validator-option" "^7.18.6"
      "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.18.6"
      "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.18.9"
      "@babel/plugin-proposal-async-generator-functions" "^7.19.1"
      "@babel/plugin-proposal-class-properties" "^7.18.6"
      "@babel/plugin-proposal-class-static-block" "^7.18.6"
      "@babel/plugin-proposal-dynamic-import" "^7.18.6"
      "@babel/plugin-proposal-export-namespace-from" "^7.18.9"
      "@babel/plugin-proposal-json-strings" "^7.18.6"
      "@babel/plugin-proposal-logical-assignment-operators" "^7.18.9"
      "@babel/plugin-proposal-nullish-coalescing-operator" "^7.18.6"
      "@babel/plugin-proposal-numeric-separator" "^7.18.6"
      "@babel/plugin-proposal-object-rest-spread" "^7.18.9"
      "@babel/plugin-proposal-optional-catch-binding" "^7.18.6"
      "@babel/plugin-proposal-optional-chaining" "^7.18.9"
      "@babel/plugin-proposal-private-methods" "^7.18.6"
      "@babel/plugin-proposal-private-property-in-object" "^7.18.6"
      "@babel/plugin-proposal-unicode-property-regex" "^7.18.6"
      "@babel/plugin-syntax-async-generators" "^7.8.4"
      "@babel/plugin-syntax-class-properties" "^7.12.13"
      "@babel/plugin-syntax-class-static-block" "^7.14.5"
      "@babel/plugin-syntax-dynamic-import" "^7.8.3"
      "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
      "@babel/plugin-syntax-import-assertions" "^7.18.6"
      "@babel/plugin-syntax-json-strings" "^7.8.3"
      "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
      "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
      "@babel/plugin-syntax-numeric-separator" "^7.10.4"
      "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
      "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
      "@babel/plugin-syntax-optional-chaining" "^7.8.3"
      "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
      "@babel/plugin-syntax-top-level-await" "^7.14.5"
      "@babel/plugin-transform-arrow-functions" "^7.18.6"
      "@babel/plugin-transform-async-to-generator" "^7.18.6"
      "@babel/plugin-transform-block-scoped-functions" "^7.18.6"
      "@babel/plugin-transform-block-scoping" "^7.18.9"
      "@babel/plugin-transform-classes" "^7.19.0"
      "@babel/plugin-transform-computed-properties" "^7.18.9"
      "@babel/plugin-transform-destructuring" "^7.18.13"
      "@babel/plugin-transform-dotall-regex" "^7.18.6"
      "@babel/plugin-transform-duplicate-keys" "^7.18.9"
      "@babel/plugin-transform-exponentiation-operator" "^7.18.6"
      "@babel/plugin-transform-for-of" "^7.18.8"
      "@babel/plugin-transform-function-name" "^7.18.9"
      "@babel/plugin-transform-literals" "^7.18.9"
      "@babel/plugin-transform-member-expression-literals" "^7.18.6"
      "@babel/plugin-transform-modules-amd" "^7.18.6"
      "@babel/plugin-transform-modules-commonjs" "^7.18.6"
      "@babel/plugin-transform-modules-systemjs" "^7.19.0"
      "@babel/plugin-transform-modules-umd" "^7.18.6"
      "@babel/plugin-transform-named-capturing-groups-regex" "^7.19.1"
      "@babel/plugin-transform-new-target" "^7.18.6"
      "@babel/plugin-transform-object-super" "^7.18.6"
      "@babel/plugin-transform-parameters" "^7.18.8"
      "@babel/plugin-transform-property-literals" "^7.18.6"
      "@babel/plugin-transform-regenerator" "^7.18.6"
      "@babel/plugin-transform-reserved-words" "^7.18.6"
      "@babel/plugin-transform-shorthand-properties" "^7.18.6"
      "@babel/plugin-transform-spread" "^7.19.0"
      "@babel/plugin-transform-sticky-regex" "^7.18.6"
      "@babel/plugin-transform-template-literals" "^7.18.9"
      "@babel/plugin-transform-typeof-symbol" "^7.18.9"
      "@babel/plugin-transform-unicode-escapes" "^7.18.10"
      "@babel/plugin-transform-unicode-regex" "^7.18.6"
      "@babel/preset-modules" "^0.1.5"
      "@babel/types" "^7.19.3"
      babel-plugin-polyfill-corejs2 "^0.3.3"
      babel-plugin-polyfill-corejs3 "^0.6.0"
      babel-plugin-polyfill-regenerator "^0.4.1"
      core-js-compat "^3.25.1"
      semver "^6.3.0"
  
  "@babel/preset-modules@^0.1.5":
    version "0.1.5"
    resolved "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.5.tgz"
    integrity sha512-A57th6YRG7oR3cq/yt/Y84MvGgE0eJG2F1JLhKuyG+jFxEgrd/HAMJatiFtmOiZurz+0DkrvbheCLaV5f2JfjA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
      "@babel/plugin-transform-dotall-regex" "^7.4.4"
      "@babel/types" "^7.4.4"
      esutils "^2.0.2"
  
  "@babel/preset-react@^7.14.5":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.18.6.tgz"
    integrity sha512-zXr6atUmyYdiWRVLOZahakYmOBHtWc2WGCkP8PYTgZi0iJXDY2CN180TdrIW4OGOAdLc7TifzDIvtx6izaRIzg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
      "@babel/helper-validator-option" "^7.18.6"
      "@babel/plugin-transform-react-display-name" "^7.18.6"
      "@babel/plugin-transform-react-jsx" "^7.18.6"
      "@babel/plugin-transform-react-jsx-development" "^7.18.6"
      "@babel/plugin-transform-react-pure-annotations" "^7.18.6"
  
  "@babel/preset-typescript@^7.14.5":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.18.6.tgz"
    integrity sha512-s9ik86kXBAnD760aybBucdpnLsAt0jK1xqJn2juOn9lkOvSHV60os5hxoVJsPzMQxvnUJFAlkont2DvvaYEBtQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.18.6"
      "@babel/helper-validator-option" "^7.18.6"
      "@babel/plugin-transform-typescript" "^7.18.6"
  
  "@babel/register@^7.14.5":
    version "7.18.9"
    resolved "https://registry.npmjs.org/@babel/register/-/register-7.18.9.tgz"
    integrity sha512-ZlbnXDcNYHMR25ITwwNKT88JiaukkdVj/nG7r3wnuXkOTHc60Uy05PwMCPre0hSkY68E6zK3xz+vUJSP2jWmcw==
    dependencies:
      clone-deep "^4.0.1"
      find-cache-dir "^2.0.0"
      make-dir "^2.1.0"
      pirates "^4.0.5"
      source-map-support "^0.5.16"
  
  "@babel/runtime-corejs3@^7.14.5":
    version "7.19.1"
    resolved "https://registry.npmjs.org/@babel/runtime-corejs3/-/runtime-corejs3-7.19.1.tgz"
    integrity sha512-j2vJGnkopRzH+ykJ8h68wrHnEUmtK//E723jjixiAl/PPf6FhqY/vYRcMVlNydRKQjQsTsYEjpx+DZMIvnGk/g==
    dependencies:
      core-js-pure "^3.25.1"
      regenerator-runtime "^0.13.4"
  
  "@babel/runtime@^7.1.2", "@babel/runtime@^7.12.13", "@babel/runtime@^7.14.5", "@babel/runtime@^7.16.5", "@babel/runtime@^7.21.0", "@babel/runtime@^7.21.5", "@babel/runtime@^7.5.5", "@babel/runtime@^7.7.6", "@babel/runtime@^7.8.4", "@babel/runtime@^7.8.7":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/runtime/-/runtime-7.21.5.tgz"
    integrity sha512-8jI69toZqqcsnqGGqwGS4Qb1VwLOEp4hz+CXPywcvjs60u3B4Pom/U/7rm4W8tMOYEB+E9wgD0mW1l3r8qlI9Q==
    dependencies:
      regenerator-runtime "^0.13.11"
  
  "@babel/template@^7.0.0", "@babel/template@^7.18.10", "@babel/template@^7.20.7":
    version "7.20.7"
    resolved "https://registry.npmjs.org/@babel/template/-/template-7.20.7.tgz"
    integrity sha512-8SegXApWe6VoNw0r9JHpSteLKTpTiLZ4rMlGIm9JQ18KiCtyQiAMEazujAHrUS5flrcqYZa75ukev3P6QmUwUw==
    dependencies:
      "@babel/code-frame" "^7.18.6"
      "@babel/parser" "^7.20.7"
      "@babel/types" "^7.20.7"
  
  "@babel/traverse@^7.12.5", "@babel/traverse@^7.14.5", "@babel/traverse@^7.19.0", "@babel/traverse@^7.19.1", "@babel/traverse@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/traverse/-/traverse-7.21.5.tgz"
    integrity sha512-AhQoI3YjWi6u/y/ntv7k48mcrCXmus0t79J9qPNlk/lAsFlCiJ047RmbfMOawySTHtywXhbXgpx/8nXMYd+oFw==
    dependencies:
      "@babel/code-frame" "^7.21.4"
      "@babel/generator" "^7.21.5"
      "@babel/helper-environment-visitor" "^7.21.5"
      "@babel/helper-function-name" "^7.21.0"
      "@babel/helper-hoist-variables" "^7.18.6"
      "@babel/helper-split-export-declaration" "^7.18.6"
      "@babel/parser" "^7.21.5"
      "@babel/types" "^7.21.5"
      debug "^4.1.0"
      globals "^11.1.0"
  
  "@babel/types@^7.18.6", "@babel/types@^7.18.9", "@babel/types@^7.19.0", "@babel/types@^7.19.3", "@babel/types@^7.20.7", "@babel/types@^7.21.0", "@babel/types@^7.21.4", "@babel/types@^7.21.5", "@babel/types@^7.4.4":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/types/-/types-7.21.5.tgz"
    integrity sha512-m4AfNvVF2mVC/F7fDEdH2El3HzUg9It/XsCxZiOTTA3m3qYfcSVSbTfM6Q9xG+hYDniZssYhlXKKUMD5m8tF4Q==
    dependencies:
      "@babel/helper-string-parser" "^7.21.5"
      "@babel/helper-validator-identifier" "^7.19.1"
      to-fast-properties "^2.0.0"
  
  "@bem-react/classname@^1.5.10", "@bem-react/classname@^1.6.0":
    version "1.6.0"
    resolved "https://registry.npmjs.org/@bem-react/classname/-/classname-1.6.0.tgz"
    integrity sha512-SFBwUHMcb7TFFK5ld88+JhecoEun3/kHZ6KvLDjj3w5hv/tfRV8mtGHA8N42uMctXLF4bPEcr96xwXXcRFuweg==
  
  "@csstools/css-parser-algorithms@^2.1.1":
    version "2.1.1"
    resolved "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.1.1.tgz"
    integrity sha512-viRnRh02AgO4mwIQb2xQNJju0i+Fh9roNgmbR5xEuG7J3TGgxjnE95HnBLgsFJOJOksvcfxOUCgODcft6Y07cA==
  
  "@csstools/css-tokenizer@^2.1.1":
    version "2.1.1"
    resolved "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.1.1.tgz"
    integrity sha512-GbrTj2Z8MCTUv+52GE0RbFGM527xuXZ0Xa5g0Z+YN573uveS4G0qi6WNOMyz3yrFM/jaILTTwJ0+umx81EzqfA==
  
  "@csstools/media-query-list-parser@^2.0.4":
    version "2.0.4"
    resolved "https://registry.npmjs.org/@csstools/media-query-list-parser/-/media-query-list-parser-2.0.4.tgz"
    integrity sha512-GyYot6jHgcSDZZ+tLSnrzkR7aJhF2ZW6d+CXH66mjy5WpAQhZD4HDke2OQ36SivGRWlZJpAz7TzbW6OKlEpxAA==
  
  "@csstools/selector-specificity@^2.2.0":
    version "2.2.0"
    resolved "https://registry.npmjs.org/@csstools/selector-specificity/-/selector-specificity-2.2.0.tgz"
    integrity sha512-+OJ9konv95ClSTOJCmMZqpd5+YGsB2S+x6w3E1oaM8UuR5j8nTNHYSz8c9BEPGDOCMQYIEEGlVPj/VY64iTbGw==
  
  "@devexpress/error-stack-parser@^2.0.6":
    version "2.0.6"
    resolved "https://registry.npmjs.org/@devexpress/error-stack-parser/-/error-stack-parser-2.0.6.tgz"
    integrity sha512-fneVypElGUH6Be39mlRZeAu00pccTlf4oVuzf9xPJD1cdEqI8NyAiQua/EW7lZdrbMUbgyXcJmfKPefhYius3A==
    dependencies:
      stackframe "^1.1.1"
  
  "@eslint-community/eslint-utils@^4.2.0":
    version "4.4.0"
    resolved "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz"
    integrity sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==
    dependencies:
      eslint-visitor-keys "^3.3.0"
  
  "@eslint-community/regexpp@^4.4.0":
    version "4.5.0"
    resolved "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.5.0.tgz"
    integrity sha512-vITaYzIcNmjn5tF5uxcZ/ft7/RXGrMUIS9HalWckEOF6ESiwXKoMzAQf2UW0aVd6rnOeExTJVd5hmWXucBKGXQ==
  
  "@eslint/eslintrc@^2.0.3":
    version "2.0.3"
    resolved "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.0.3.tgz"
    integrity sha512-+5gy6OQfk+xx3q0d6jGZZC3f3KzAkXc/IanVxd1is/VIIziRqqt3ongQz0FiTUXqTk0c7aDB3OaFuKnuSoJicQ==
    dependencies:
      ajv "^6.12.4"
      debug "^4.3.2"
      espree "^9.5.2"
      globals "^13.19.0"
      ignore "^5.2.0"
      import-fresh "^3.2.1"
      js-yaml "^4.1.0"
      minimatch "^3.1.2"
      strip-json-comments "^3.1.1"
  
  "@eslint/js@8.41.0":
    version "8.41.0"
    resolved "https://registry.npmjs.org/@eslint/js/-/js-8.41.0.tgz"
    integrity sha512-LxcyMGxwmTh2lY9FwHPGWOHmYFCZvbrFCBZL4FzSSsxsRPuhrYUg/49/0KDfW8tnIEaEHtfmn6+NPN+1DqaNmA==
  
  "@hapi/hoek@^9.0.0":
    version "9.3.0"
    resolved "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.3.0.tgz"
    integrity sha512-/c6rf4UJlmHlC9b5BaNvzAcFv7HZ2QHaV0D4/HNlBdvFnvQq8RI4kYdhyPCl7Xj+oWvTWQ8ujhqS53LIgAe6KQ==
  
  "@hapi/topo@^5.0.0":
    version "5.1.0"
    resolved "https://registry.npmjs.org/@hapi/topo/-/topo-5.1.0.tgz"
    integrity sha512-foQZKJig7Ob0BMAYBfcJk8d77QtOe7Wo4ox7ff1lQYoNNAb6jwcY1ncdoy2e9wQZzvNy7ODZCYJkK8kzmcAnAg==
    dependencies:
      "@hapi/hoek" "^9.0.0"
  
  "@humanwhocodes/config-array@^0.11.8":
    version "0.11.8"
    resolved "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.8.tgz"
    integrity sha512-UybHIJzJnR5Qc/MsD9Kr+RpO2h+/P1GhOwdiLPXK5TWk5sgTdu88bTD9UP+CKbPPh5Rni1u0GjAdYQLemG8g+g==
    dependencies:
      "@humanwhocodes/object-schema" "^1.2.1"
      debug "^4.1.1"
      minimatch "^3.0.5"
  
  "@humanwhocodes/module-importer@^1.0.1":
    version "1.0.1"
    resolved "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
    integrity sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==
  
  "@humanwhocodes/object-schema@^1.2.1":
    version "1.2.1"
    resolved "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz"
    integrity sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==
  
  "@jridgewell/gen-mapping@^0.1.0":
    version "0.1.1"
    resolved "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz"
    integrity sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==
    dependencies:
      "@jridgewell/set-array" "^1.0.0"
      "@jridgewell/sourcemap-codec" "^1.4.10"
  
  "@jridgewell/gen-mapping@^0.3.0", "@jridgewell/gen-mapping@^0.3.2":
    version "0.3.2"
    resolved "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz"
    integrity sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==
    dependencies:
      "@jridgewell/set-array" "^1.0.1"
      "@jridgewell/sourcemap-codec" "^1.4.10"
      "@jridgewell/trace-mapping" "^0.3.9"
  
  "@jridgewell/resolve-uri@3.1.0":
    version "3.1.0"
    resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz"
    integrity sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==
  
  "@jridgewell/set-array@^1.0.0", "@jridgewell/set-array@^1.0.1":
    version "1.1.2"
    resolved "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz"
    integrity sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==
  
  "@jridgewell/source-map@^0.3.2":
    version "0.3.2"
    resolved "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.2.tgz"
    integrity sha512-m7O9o2uR8k2ObDysZYzdfhb08VuEml5oWGiosa1VdaPZ/A6QyPkAJuwN0Q1lhULOf6B7MtQmHENS743hWtCrgw==
    dependencies:
      "@jridgewell/gen-mapping" "^0.3.0"
      "@jridgewell/trace-mapping" "^0.3.9"
  
  "@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@1.4.14":
    version "1.4.14"
    resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz"
    integrity sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==
  
  "@jridgewell/trace-mapping@^0.3.17", "@jridgewell/trace-mapping@^0.3.9":
    version "0.3.18"
    resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.18.tgz"
    integrity sha512-w+niJYzMHdd7USdiH2U6869nqhD2nbfZXND5Yp93qIbEmnDNk7PD48o+YchRVpzMU7M6jVCbenTR7PA1FLQ9pA==
    dependencies:
      "@jridgewell/resolve-uri" "3.1.0"
      "@jridgewell/sourcemap-codec" "1.4.14"
  
  "@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1":
    version "5.1.1-v1"
    resolved "https://registry.npmjs.org/@nicolo-ribaudo/eslint-scope-5-internals/-/eslint-scope-5-internals-5.1.1-v1.tgz"
    integrity sha512-54/JRvkLIzzDWshCWfuhadfrfZVPiElY8Fcgmg1HroEly/EDSszzhBAsarCux+D/kOslTRquNzuyGSmUSTTHGg==
    dependencies:
      eslint-scope "5.1.1"
  
  "@nodelib/fs.scandir@2.1.5":
    version "2.1.5"
    resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
    integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
    dependencies:
      "@nodelib/fs.stat" "2.0.5"
      run-parallel "^1.1.9"
  
  "@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
    version "2.0.5"
    resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
    integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==
  
  "@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
    version "1.2.8"
    resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
    integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
    dependencies:
      "@nodelib/fs.scandir" "2.1.5"
      fastq "^1.6.0"
  
  "@nutui/icons-react-taro@^0.0.1-beta.8", "@nutui/icons-react-taro@0.0.1-beta.8":
    version "0.0.1-beta.8"
    resolved "https://registry.npmjs.org/@nutui/icons-react-taro/-/icons-react-taro-0.0.1-beta.8.tgz"
    integrity sha512-6jFqdrnVkw0b4SOIYzWnYASuYk/vxim5w0X6tgYTr3NJyC+pRh217JWkTWUHpycaCmgP6gjpx1Ed0ql1EiFLiw==
    dependencies:
      classnames "^2.3.2"
  
  "@nutui/nutui-react-taro@^v2.0.0-alpha.10":
    version "2.0.0-alpha.11"
    resolved "https://registry.npmjs.org/@nutui/nutui-react-taro/-/nutui-react-taro-2.0.0-alpha.11.tgz"
    integrity sha512-Q0qAv8UQkcCW7OSPxaq4LE9BgDHPeVEHexd5gMFa6Ar5nh6VtdhxSqeNAL+A+UkPf2veNXue+NZaumXeUn8OZQ==
    dependencies:
      "@babel/runtime" "^7.21.0"
      "@bem-react/classname" "^1.6.0"
      "@nutui/icons-react-taro" "0.0.1-beta.8"
      "@react-spring/web" "~9.6.1"
      "@use-gesture/react" "10.2.20"
      async-validator "^4.2.5"
      classnames "^2.3.2"
      lodash.kebabcase "^4.1.1"
      react-transition-group "^4.4.5"
  
  "@nutui/nutui-react@^2.0.0-alpha.10":
    version "2.0.0-beta.0"
    resolved "https://registry.npmjs.org/@nutui/nutui-react/-/nutui-react-2.0.0-beta.0.tgz"
    integrity sha512-p84vDDSFPNTqoUehvvD2qeeuvtWbw19cG++XcMvxzHxmjp7jtYs/XBoWvHmmkK1WDIqXocoZsXIqU6kg9CBwUA==
    dependencies:
      "@babel/runtime" "^7.16.5"
      "@bem-react/classname" "^1.5.10"
      "@react-spring/web" "^9.3.2"
      "@use-gesture/react" "10.2.9"
      classnames "^2.3.1"
      react-router-dom "^5.2.0"
      react-transition-group "^4.4.2"
  
  "@parcel/css@^1.4.0":
    version "1.14.0"
    resolved "https://registry.npmjs.org/@parcel/css/-/css-1.14.0.tgz"
    integrity sha512-r5tJWe6NF6lesfPw1N3g7N7WUKpHqi2ONnw9wl5ccSGGIxkmgcPaPQxfvmhdjXvQnktSuIOR0HjQXVXu+/en/w==
    dependencies:
      lightningcss "^1.14.0"
  
  "@pmmmwh/react-refresh-webpack-plugin@^0.5.9":
    version "0.5.10"
    resolved "https://registry.npmjs.org/@pmmmwh/react-refresh-webpack-plugin/-/react-refresh-webpack-plugin-0.5.10.tgz"
    integrity sha512-j0Ya0hCFZPd4x40qLzbhGsh9TMtdb+CJQiso+WxLOPNasohq9cc5SNUcwsZaRH6++Xh91Xkm/xHCkuIiIu0LUA==
    dependencies:
      ansi-html-community "^0.0.8"
      common-path-prefix "^3.0.0"
      core-js-pure "^3.23.3"
      error-stack-parser "^2.0.6"
      find-up "^5.0.0"
      html-entities "^2.1.0"
      loader-utils "^2.0.4"
      schema-utils "^3.0.0"
      source-map "^0.7.3"
  
  "@react-spring/animated@~9.6.1":
    version "9.6.1"
    resolved "https://registry.npmjs.org/@react-spring/animated/-/animated-9.6.1.tgz"
    integrity sha512-ls/rJBrAqiAYozjLo5EPPLLOb1LM0lNVQcXODTC1SMtS6DbuBCPaKco5svFUQFMP2dso3O+qcC4k9FsKc0KxMQ==
    dependencies:
      "@react-spring/shared" "~9.6.1"
      "@react-spring/types" "~9.6.1"
  
  "@react-spring/core@~9.6.1":
    version "9.6.1"
    resolved "https://registry.npmjs.org/@react-spring/core/-/core-9.6.1.tgz"
    integrity sha512-3HAAinAyCPessyQNNXe5W0OHzRfa8Yo5P748paPcmMowZ/4sMfaZ2ZB6e5x5khQI8NusOHj8nquoutd6FRY5WQ==
    dependencies:
      "@react-spring/animated" "~9.6.1"
      "@react-spring/rafz" "~9.6.1"
      "@react-spring/shared" "~9.6.1"
      "@react-spring/types" "~9.6.1"
  
  "@react-spring/rafz@~9.6.1":
    version "9.6.1"
    resolved "https://registry.npmjs.org/@react-spring/rafz/-/rafz-9.6.1.tgz"
    integrity sha512-v6qbgNRpztJFFfSE3e2W1Uz+g8KnIBs6SmzCzcVVF61GdGfGOuBrbjIcp+nUz301awVmREKi4eMQb2Ab2gGgyQ==
  
  "@react-spring/shared@~9.6.1":
    version "9.6.1"
    resolved "https://registry.npmjs.org/@react-spring/shared/-/shared-9.6.1.tgz"
    integrity sha512-PBFBXabxFEuF8enNLkVqMC9h5uLRBo6GQhRMQT/nRTnemVENimgRd+0ZT4yFnAQ0AxWNiJfX3qux+bW2LbG6Bw==
    dependencies:
      "@react-spring/rafz" "~9.6.1"
      "@react-spring/types" "~9.6.1"
  
  "@react-spring/types@~9.6.1":
    version "9.6.1"
    resolved "https://registry.npmjs.org/@react-spring/types/-/types-9.6.1.tgz"
    integrity sha512-POu8Mk0hIU3lRXB3bGIGe4VHIwwDsQyoD1F394OK7STTiX9w4dG3cTLljjYswkQN+hDSHRrj4O36kuVa7KPU8Q==
  
  "@react-spring/web@^9.3.2", "@react-spring/web@~9.6.1":
    version "9.6.1"
    resolved "https://registry.npmjs.org/@react-spring/web/-/web-9.6.1.tgz"
    integrity sha512-X2zR6q2Z+FjsWfGAmAXlQaoUHbPmfuCaXpuM6TcwXPpLE1ZD4A1eys/wpXboFQmDkjnrlTmKvpVna1MjWpZ5Hw==
    dependencies:
      "@react-spring/animated" "~9.6.1"
      "@react-spring/core" "~9.6.1"
      "@react-spring/shared" "~9.6.1"
      "@react-spring/types" "~9.6.1"
  
  "@sideway/address@^4.1.3":
    version "4.1.4"
    resolved "https://registry.npmjs.org/@sideway/address/-/address-4.1.4.tgz"
    integrity sha512-7vwq+rOHVWjyXxVlR76Agnvhy8I9rpzjosTESvmhNeXOXdZZB15Fl+TI9x1SiHZH5Jv2wTGduSxFDIaq0m3DUw==
    dependencies:
      "@hapi/hoek" "^9.0.0"
  
  "@sideway/formula@^3.0.0":
    version "3.0.0"
    resolved "https://registry.npmjs.org/@sideway/formula/-/formula-3.0.0.tgz"
    integrity sha512-vHe7wZ4NOXVfkoRb8T5otiENVlT7a3IAiw7H5M2+GO+9CDgcVUUsX1zalAztCmwyOr2RUTGJdgB+ZvSVqmdHmg==
  
  "@sideway/pinpoint@^2.0.0":
    version "2.0.0"
    resolved "https://registry.npmjs.org/@sideway/pinpoint/-/pinpoint-2.0.0.tgz"
    integrity sha512-RNiOoTPkptFtSVzQevY/yWtZwf/RxyVnPy/OcA9HBM3MlGDnBEYL5B41H0MTn0Uec8Hi+2qUtTfG2WWZBmMejQ==
  
  "@sindresorhus/is@^0.14.0":
    version "0.14.0"
    resolved "https://registry.npmjs.org/@sindresorhus/is/-/is-0.14.0.tgz"
    integrity sha512-9NET910DNaIPngYnLLPeg+Ogzqsi9uM4mSboU5y6p8S5DzMTVEsJZrawi+BoDNUVBa2DhJqQYUFvMDfgU062LQ==
  
  "@sindresorhus/is@^0.7.0":
    version "0.7.0"
    resolved "https://registry.npmjs.org/@sindresorhus/is/-/is-0.7.0.tgz"
    integrity sha512-ONhaKPIufzzrlNbqtWFFd+jlnemX6lJAgq9ZeiZtS7I1PIf/la7CW4m83rTXRnVnsMbW2k56pGYu7AUFJD9Pow==
  
  "@stencil/core@^2.22.2":
    version "2.22.2"
    resolved "https://registry.npmjs.org/@stencil/core/-/core-2.22.2.tgz"
    integrity sha512-r+vbxsGNcBaV1VDOYW25lv4QfXTlNoIb5GpUX7rZ+cr59yqYCZC5tlV+IzX6YgHKW62ulCc9M3RYtTfHtNbNNw==
  
  "@swc/core-darwin-arm64@1.3.23":
    version "1.3.23"
    resolved "https://registry.npmjs.org/@swc/core-darwin-arm64/-/core-darwin-arm64-1.3.23.tgz"
    integrity sha512-IGOEHmE4aBDX7gQWpanI3A0ni47UcvX7rmcy0H8kE6mm/y7mEMWskvNsYhYzJl4GVZgw38v1/lL/A7MRX6g71A==
  
  "@swc/core-darwin-arm64@1.3.42":
    version "1.3.42"
    resolved "https://registry.npmjs.org/@swc/core-darwin-arm64/-/core-darwin-arm64-1.3.42.tgz"
    integrity sha512-hM6RrZFyoCM9mX3cj/zM5oXwhAqjUdOCLXJx7KTQps7NIkv/Qjvobgvyf2gAb89j3ARNo9NdIoLjTjJ6oALtiA==
  
  "@swc/core@^1.0.46":
    version "1.3.42"
    resolved "https://registry.npmjs.org/@swc/core/-/core-1.3.42.tgz"
    integrity sha512-nVFUd5+7tGniM2cT3LXaqnu3735Cu4az8A9gAKK+8sdpASI52SWuqfDBmjFCK9xG90MiVDVp2PTZr0BWqCIzpw==
    optionalDependencies:
      "@swc/core-darwin-arm64" "1.3.42"
      "@swc/core-darwin-x64" "1.3.42"
      "@swc/core-linux-arm-gnueabihf" "1.3.42"
      "@swc/core-linux-arm64-gnu" "1.3.42"
      "@swc/core-linux-arm64-musl" "1.3.42"
      "@swc/core-linux-x64-gnu" "1.3.42"
      "@swc/core-linux-x64-musl" "1.3.42"
      "@swc/core-win32-arm64-msvc" "1.3.42"
      "@swc/core-win32-ia32-msvc" "1.3.42"
      "@swc/core-win32-x64-msvc" "1.3.42"
  
  "@swc/core@1.3.23":
    version "1.3.23"
    resolved "https://registry.npmjs.org/@swc/core/-/core-1.3.23.tgz"
    integrity sha512-Aa7yw5+7ErOxr+G0J1eU2hkb9nEMSdt1Ye3isdAgg9mrsPuttk+cfLp6nP/Lux/VUnu5k4eOxeTy9UhjJhRAFw==
    optionalDependencies:
      "@swc/core-darwin-arm64" "1.3.23"
      "@swc/core-darwin-x64" "1.3.23"
      "@swc/core-linux-arm-gnueabihf" "1.3.23"
      "@swc/core-linux-arm64-gnu" "1.3.23"
      "@swc/core-linux-arm64-musl" "1.3.23"
      "@swc/core-linux-x64-gnu" "1.3.23"
      "@swc/core-linux-x64-musl" "1.3.23"
      "@swc/core-win32-arm64-msvc" "1.3.23"
      "@swc/core-win32-ia32-msvc" "1.3.23"
      "@swc/core-win32-x64-msvc" "1.3.23"
  
  "@swc/register@^0.1.10":
    version "0.1.10"
    resolved "https://registry.npmjs.org/@swc/register/-/register-0.1.10.tgz"
    integrity sha512-6STwH/q4dc3pitXLVkV7sP0Hiy+zBsU2wOF1aXpXR95pnH3RYHKIsDC+gvesfyB7jxNT9OOZgcqOp9RPxVTx9A==
    dependencies:
      lodash.clonedeep "^4.5.0"
      pirates "^4.0.1"
      source-map-support "^0.5.13"
  
  "@szmarczak/http-timer@^1.1.2":
    version "1.1.2"
    resolved "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-1.1.2.tgz"
    integrity sha512-XIB2XbzHTN6ieIjfIMV9hlVcfPU26s2vafYWQcZHWXHOxiaRZYEDKEwdl129Zyg50+foYV2jCgtrqSA6qNuNSA==
    dependencies:
      defer-to-connect "^1.0.1"
  
  "@tarojs/api@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/api/-/api-3.6.6.tgz"
    integrity sha512-IzOl2pdwL52MkFiVW2WA6iiRzzdBgYWERxsgqDPBlcWTwUypjFIOC+Q6jdxoizDYDW9UrQQJuj63W1EktiG7Pg==
    dependencies:
      "@babel/runtime" "^7.14.5"
      "@tarojs/runtime" "3.6.6"
      "@tarojs/shared" "3.6.6"
  
  "@tarojs/cli@^3.6.5":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/cli/-/cli-3.6.6.tgz"
    integrity sha512-L44ByKB48tdKLOLZchsAe3UioikB9UMR4/AcXqTbacC7lra3OLD8xBFYsuhfNgfgTzT6+Y8c5B1yogIHHK1p5w==
    dependencies:
      "@tarojs/helper" "3.6.6"
      "@tarojs/service" "3.6.6"
      "@tarojs/shared" "3.6.6"
      adm-zip "^0.4.13"
      cli-highlight "^2.1.11"
      dotenv "^16.0.3"
      dotenv-expand "^9.0.0"
      download-git-repo "^2.0.0"
      envinfo "^7.8.1"
      eslint "^8.12.0"
      glob "^7.1.2"
      inquirer "^8.0.0"
      joi "^17.6.0"
      latest-version "^5.1.0"
      lodash "^4.17.21"
      mem-fs "^2.3.0"
      mem-fs-editor "^9.6.0"
      minimist "^1.2.8"
      npm-check "^6.0.1"
      ora "^5.0.0"
      request "^2.88.0"
      semver "^7.3.8"
      validate-npm-package-name "^5.0.0"
      xml2js "^0.4.19"
  
  "@tarojs/components-advanced@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/components-advanced/-/components-advanced-3.6.6.tgz"
    integrity sha512-cAJniZi8iB3d1VinqQ8qrLHc4jo7DmLZQsDnM5qdtn0wS4zdv+mhGw9gl3NscGCe2xhnmLwI1M1sy0K4an8a7w==
    dependencies:
      "@tarojs/components" "3.6.6"
      "@tarojs/runtime" "3.6.6"
      "@tarojs/shared" "3.6.6"
      "@tarojs/taro" "3.6.6"
      memoize-one "^6.0.0"
      postcss "^8.4.18"
  
  "@tarojs/components-react@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/components-react/-/components-react-3.6.6.tgz"
    integrity sha512-FZh6VBFaqaHsuphTiTmmQ70K7/mvXqB7teL1IDn1sniDwMwLLXZRvWojcdxkE1nGPtD+gPmdhZEPWHMUNYpung==
    dependencies:
      "@babel/runtime" "^7.14.5"
      "@tarojs/components" "3.6.6"
      "@tarojs/taro" "3.6.6"
      classnames "^2.2.5"
      intersection-observer "^0.7.0"
      resolve-pathname "^3.0.0"
      swiper "6.8.0"
      weui "^1.1.2"
  
  "@tarojs/components@^3.6.5", "@tarojs/components@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/components/-/components-3.6.6.tgz"
    integrity sha512-GWxFtKeiTNajkIyNjyrkEyvrtqOIcrdWSF46A5pCbtmpjdsloN6NX9ms4fwvSjoyci8VZ+sBimnviIqTpBfL+w==
    dependencies:
      "@stencil/core" "^2.22.2"
      "@tarojs/components-advanced" "3.6.6"
      "@tarojs/router" "3.6.6"
      "@tarojs/taro" "3.6.6"
      classnames "^2.2.5"
      hls.js "^1.1.5"
      intersection-observer "^0.7.0"
      resolve-pathname "^3.0.0"
      swiper "6.8.0"
      weui "^1.1.2"
  
  "@tarojs/helper@^3.6.5", "@tarojs/helper@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/helper/-/helper-3.6.6.tgz"
    integrity sha512-feY1z2rqV+5g8ZBagcOcjbI0P7jq9gIFW8664MXc5iFNxgUbU2w8eNbwRv9zAb1/U9cZoWxx+QGSw4xyTKy/6A==
    dependencies:
      "@babel/core" "^7.14.5"
      "@babel/parser" "^7.14.5"
      "@babel/plugin-proposal-decorators" "^7.14.5"
      "@babel/plugin-proposal-object-rest-spread" "^7.14.5"
      "@babel/plugin-transform-runtime" "^7.14.5"
      "@babel/preset-env" "^7.14.5"
      "@babel/preset-typescript" "^7.14.5"
      "@babel/register" "^7.14.5"
      "@babel/runtime" "^7.14.5"
      "@babel/traverse" "^7.14.5"
      "@swc/core" "1.3.23"
      "@swc/register" "^0.1.10"
      ansi-escapes "^4.3.2"
      chalk "3.0.0"
      chokidar "^3.3.1"
      cross-spawn "^7.0.3"
      debug "4.3.4"
      esbuild "^0.14.27"
      find-yarn-workspace-root "2.0.0"
      fs-extra "^8.0.1"
      lodash "^4.17.21"
      require-from-string "^2.0.2"
      resolve "^1.22.0"
      supports-hyperlinks "^2.2.0"
      yauzl "2.10.0"
  
  "@tarojs/plugin-framework-react@^3.6.5":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/plugin-framework-react/-/plugin-framework-react-3.6.6.tgz"
    integrity sha512-Umr1rB9Sf3QdcWUWYjY2BBUumjGKsy6txYjw8ntDU6R2MqsjiXGVD759D1q6L8hViYW3Zh1LNr8dunGu1lr9uw==
    dependencies:
      "@tarojs/helper" "3.6.6"
      "@tarojs/runtime" "3.6.6"
      "@tarojs/service" "3.6.6"
      "@tarojs/shared" "3.6.6"
      acorn "^8.0.4"
      acorn-walk "^8.0.0"
      lodash "^4.17.21"
  
  "@tarojs/plugin-html@^3.6.5":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/plugin-html/-/plugin-html-3.6.6.tgz"
    integrity sha512-mkdsum+nZbwIYxuBNYRABz664wRQogYqdAjKcnjio8J4NbtM7sBhwlugNjs6MaeA0GM0Jle6Y+biw6RkGrHklA==
    dependencies:
      "@babel/parser" "^7.20.0"
      "@tarojs/runtime" "3.6.6"
      "@tarojs/service" "3.6.6"
      "@tarojs/shared" "3.6.6"
  
  "@tarojs/plugin-platform-alipay@^3.6.5", "@tarojs/plugin-platform-alipay@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/plugin-platform-alipay/-/plugin-platform-alipay-3.6.6.tgz"
    integrity sha512-65SFA8Vmlwf5ZflI+jXN3W2nrh2NYM3GRAjOcZDIPE+6Je33Bi/7jNaTn3t+YwJlcVjY6djp3/dmC+xwc7skfA==
    dependencies:
      "@tarojs/components" "3.6.6"
      "@tarojs/service" "3.6.6"
      "@tarojs/shared" "3.6.6"
  
  "@tarojs/plugin-platform-h5@^3.6.5":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/plugin-platform-h5/-/plugin-platform-h5-3.6.6.tgz"
    integrity sha512-NVn9an5hcdWAtqylDirbCHBGdwEhk0VYOzspeubemNLQ/lGLkJHU50CfOF42RBZA1sAfwwh6JB1LNILMdtCaPA==
    dependencies:
      "@tarojs/components" "3.6.6"
      "@tarojs/components-react" "3.6.6"
      "@tarojs/router" "3.6.6"
      "@tarojs/service" "3.6.6"
      "@tarojs/shared" "3.6.6"
      "@tarojs/taro-h5" "3.6.6"
      babel-plugin-transform-taroapi "3.6.6"
      resolve "^1.22.0"
  
  "@tarojs/plugin-platform-jd@^3.6.5", "@tarojs/plugin-platform-jd@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/plugin-platform-jd/-/plugin-platform-jd-3.6.6.tgz"
    integrity sha512-AlMmnENciAVhI0/tRZsE5sRThVJ1ESR8oxy974XcAeS3K0DhQhAHVNJ9bVm534RZjGhJ05YGARFtQqJZnNfV3g==
    dependencies:
      "@tarojs/service" "3.6.6"
      "@tarojs/shared" "3.6.6"
  
  "@tarojs/plugin-platform-qq@^3.6.5", "@tarojs/plugin-platform-qq@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/plugin-platform-qq/-/plugin-platform-qq-3.6.6.tgz"
    integrity sha512-GPLUNeduZMG7wFmPZspfVTc2oSv9zlanKjJn/7uUDPA3HKIbflcVehkPn2Cq28BuvSvY8q3oqFUPMGvKP7jHnw==
    dependencies:
      "@tarojs/plugin-platform-weapp" "3.6.6"
      "@tarojs/service" "3.6.6"
      "@tarojs/shared" "3.6.6"
  
  "@tarojs/plugin-platform-swan@^3.6.5", "@tarojs/plugin-platform-swan@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/plugin-platform-swan/-/plugin-platform-swan-3.6.6.tgz"
    integrity sha512-ISylMI0/7cZ7S/nKwDvkKke76h2m+TtcCqrhvmm4Fo/RyFdxPtZu/wtJ62WSw39dcU5Jec6UuP/YqsMGYEqcWw==
    dependencies:
      "@tarojs/components" "3.6.6"
      "@tarojs/service" "3.6.6"
      "@tarojs/shared" "3.6.6"
  
  "@tarojs/plugin-platform-tt@^3.6.5", "@tarojs/plugin-platform-tt@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/plugin-platform-tt/-/plugin-platform-tt-3.6.6.tgz"
    integrity sha512-NgHSRX9tejcQQm3ATc4zaG6mtHYr1c7udhaaFrHxCZJeLqsWWlw7f06FIpghKdbhmKCUcqXUgtbQOwdcvb9P1Q==
    dependencies:
      "@tarojs/components" "3.6.6"
      "@tarojs/service" "3.6.6"
      "@tarojs/shared" "3.6.6"
  
  "@tarojs/plugin-platform-weapp@^3.6.5", "@tarojs/plugin-platform-weapp@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/plugin-platform-weapp/-/plugin-platform-weapp-3.6.6.tgz"
    integrity sha512-/feSsGVeXlcuruuSamUvDz/CzttN0E/ziXLjJ6ldBYW7Mscfb1Ro+HnM26eaCGWrJTee4WRvvYerr7Nn6m+PbQ==
    dependencies:
      "@tarojs/components" "3.6.6"
      "@tarojs/service" "3.6.6"
      "@tarojs/shared" "3.6.6"
  
  "@tarojs/react@^3.6.5":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/react/-/react-3.6.6.tgz"
    integrity sha512-FX+hXFmIdwfiRBSTYcWyjRA4T6hjybIu0FZ43HZfddZhVbzYWvED4nv6NI5CJhh+PRhpjMc2JsH3RyXiitMVXA==
    dependencies:
      "@tarojs/runtime" "3.6.6"
      "@tarojs/shared" "3.6.6"
      react-reconciler "0.27.0"
  
  "@tarojs/router@^3.6.5", "@tarojs/router@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/router/-/router-3.6.6.tgz"
    integrity sha512-HfKeOLGB+ufhAIPA3BlQIwwgtopQYOWJD3xhDuqCgZhWhYca/fA6qACteh82t7Ki3sUPJ/T834YNPR18TaBC3w==
    dependencies:
      "@tarojs/runtime" "3.6.6"
      "@tarojs/taro" "3.6.6"
      dingtalk-jsapi "~2.15.2"
      history "^5.1.0"
      mobile-detect "^1.4.2"
      query-string "^7.1.1"
      universal-router "^8.3.0"
  
  "@tarojs/runner-utils@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/runner-utils/-/runner-utils-3.6.6.tgz"
    integrity sha512-9VlvABSgGqUmozJCvol+5djJBx/Zhe66WFDa69COPcDnZoGGw6Y6t2EkML1kD60I406OttfSTovlRxksoLO5WA==
    dependencies:
      "@tarojs/helper" "3.6.6"
      scss-bundle "^3.0.2"
  
  "@tarojs/runtime@^3.6.5", "@tarojs/runtime@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/runtime/-/runtime-3.6.6.tgz"
    integrity sha512-tHqU7/iohzTSKl+cJ2EkVqM3YWE/2kb2rDkqpzHaBYTBe39ClJjlsCNQnQwW6Thxo+jdZw98HXtjdAczj5/u0w==
    dependencies:
      "@tarojs/shared" "3.6.6"
      lodash-es "4.17.21"
  
  "@tarojs/service@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/service/-/service-3.6.6.tgz"
    integrity sha512-5Jf4nFETN8ODHvLmOKa4rO4pfynMATjRqTAoY2KSUE/KtKy7poKiJG7PFhQxuiXxDIPZBZZbzwvq2GUqSilFwA==
    dependencies:
      "@tarojs/helper" "3.6.6"
      "@tarojs/shared" "3.6.6"
      "@tarojs/taro" "3.6.6"
      joi "^17.6.0"
      lodash "^4.17.21"
      ora "^5.0.0"
      resolve "^1.22.0"
      tapable "^1.1.3"
      webpack-merge "^4.2.2"
  
  "@tarojs/shared@^3.6.5", "@tarojs/shared@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/shared/-/shared-3.6.6.tgz"
    integrity sha512-8OwrMReJSAKVyHaUM/pcqEP1V9SD+U3mIyYKIfePuXjAd3YzdXkJxt5JRUjk4ElrJ96yrLPUfISmaE84AYBWmQ==
  
  "@tarojs/taro-h5@^3.6.5", "@tarojs/taro-h5@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/taro-h5/-/taro-h5-3.6.6.tgz"
    integrity sha512-Ze4HzJV9GoY9epB0eIrFzLAkQyh3XH8ZgJcGpo0kC3BUxtDZqS14ca6ZS/H0KOsOIFwdhb4VO7An15YofLEDMw==
    dependencies:
      "@tarojs/api" "3.6.6"
      "@tarojs/components" "3.6.6"
      "@tarojs/router" "3.6.6"
      "@tarojs/runtime" "3.6.6"
      "@tarojs/shared" "3.6.6"
      abortcontroller-polyfill "^1.7.5"
      base64-js "^1.3.0"
      intersection-observer "^0.7.0"
      jsonp-retry "^1.0.3"
      query-string "^7.1.1"
      whatwg-fetch "^3.4.0"
  
  "@tarojs/taro-loader@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/taro-loader/-/taro-loader-3.6.6.tgz"
    integrity sha512-FH/vBfvUV8sYfrBV1nnxtpZ628XaYAiieiUkCYBIaTL//XLuwiqlDJ8+mC07vMzKlLnpLG7aOKBbHaOHhadzbw==
    dependencies:
      "@tarojs/helper" "3.6.6"
      "@tarojs/taro" "3.6.6"
      loader-utils "^1.2.3"
  
  "@tarojs/taro@^3.6.5", "@tarojs/taro@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/taro/-/taro-3.6.6.tgz"
    integrity sha512-24s1JKL0wTmTx0jp21JMY/qO2z+3GbNm2Ch+sCrrr5kihxZdyfMpRByjS0zldGa8aXGJiQklq5iEXCRWCOyXoQ==
    dependencies:
      "@tarojs/api" "3.6.6"
      "@tarojs/runtime" "3.6.6"
  
  "@tarojs/webpack5-prebundle@3.6.6":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/webpack5-prebundle/-/webpack5-prebundle-3.6.6.tgz"
    integrity sha512-prMdaiBSbm/wUMf8sFa/QOK3yFaEsUfoL9PCv1Gbma3OIpvKf6V5dk+I3BBqY2btUpfZfLQqsH8lDbVbw6l+ZA==
    dependencies:
      "@tarojs/helper" "3.6.6"
      "@tarojs/taro" "3.6.6"
      enhanced-resolve "^5.9.3"
      es-module-lexer "^0.10.4"
      lodash "^4.17.21"
      webpack-chain "6.5.1"
      webpack-virtual-modules "^0.5.0"
  
  "@tarojs/webpack5-runner@^3.6.5":
    version "3.6.6"
    resolved "https://registry.npmjs.org/@tarojs/webpack5-runner/-/webpack5-runner-3.6.6.tgz"
    integrity sha512-rIyUZjdUFS1/vW34I2Z1YApTWM0QFXXFSSarGgsMgVTFdzBVIDdmDOY86hSJaKsnMwgUyXTyedqmw1pj1vOv5A==
    dependencies:
      "@parcel/css" "^1.4.0"
      "@tarojs/helper" "3.6.6"
      "@tarojs/plugin-platform-alipay" "3.6.6"
      "@tarojs/plugin-platform-jd" "3.6.6"
      "@tarojs/plugin-platform-qq" "3.6.6"
      "@tarojs/plugin-platform-swan" "3.6.6"
      "@tarojs/plugin-platform-tt" "3.6.6"
      "@tarojs/plugin-platform-weapp" "3.6.6"
      "@tarojs/runner-utils" "3.6.6"
      "@tarojs/runtime" "3.6.6"
      "@tarojs/shared" "3.6.6"
      "@tarojs/taro" "3.6.6"
      "@tarojs/taro-loader" "3.6.6"
      "@tarojs/webpack5-prebundle" "3.6.6"
      acorn-walk "^8.0.0"
      autoprefixer "^9.7.4"
      babel-loader "8.2.1"
      copy-webpack-plugin "10.2.0"
      css-loader "^6.7.1"
      css-minimizer-webpack-plugin "3.4.1"
      csso "^5.0.2"
      detect-port "^1.3.0"
      esbuild "^0.14.27"
      esbuild-loader "2.18.0"
      file-loader "6.0.0"
      html-minifier "^4.0.0"
      html-webpack-plugin "5.5.0"
      jsdom "^21.1.0"
      less "^4.1.0"
      less-loader "10.2.0"
      loader-utils "^1.2.3"
      lodash "^4.17.21"
      md5 "^2.3.0"
      micromatch "^4.0.2"
      mini-css-extract-plugin "2.4.6"
      miniprogram-simulate "^1.1.5"
      mkdirp "^1.0.4"
      ora "^5.0.0"
      postcss-html-transform "3.6.6"
      postcss-import "^14.0.0"
      postcss-loader "^7.0.1"
      postcss-plugin-constparse "3.6.6"
      postcss-pxtransform "3.6.6"
      postcss-url "^10.1.3"
      regenerator-runtime "0.11"
      resolve "^1.22.0"
      resolve-url-loader "^5.0.0"
      sass "1.50.0"
      sass-loader "12.4.0"
      sax "1.2.4"
      style-loader "3.3.1"
      stylus "^0.55.0"
      stylus-loader "6.2.0"
      terser-webpack-plugin "^5.1.3"
      url-loader "4.1.0"
      vm2 "^3.8.4"
      vue-loader "^15.10.1"
      webpack-chain "6.5.1"
      webpack-dev-server "4.7.4"
      webpack-format-messages "^2.0.6"
      webpackbar "^5.0.2"
  
  "@tootallnate/once@2":
    version "2.0.0"
    resolved "https://registry.npmjs.org/@tootallnate/once/-/once-2.0.0.tgz"
    integrity sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==
  
  "@trysound/sax@0.2.0":
    version "0.2.0"
    resolved "https://registry.npmjs.org/@trysound/sax/-/sax-0.2.0.tgz"
    integrity sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==
  
  "@types/archy@^0.0.31":
    version "0.0.31"
    resolved "https://registry.npmjs.org/@types/archy/-/archy-0.0.31.tgz"
    integrity sha512-v+dxizsFVyXgD3EpFuqT9YjdEjbJmPxNf1QIX9ohZOhxh1ZF2yhqv3vYaeum9lg3VghhxS5S0a6yldN9J9lPEQ==
  
  "@types/body-parser@*":
    version "1.19.2"
    resolved "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.2.tgz"
    integrity sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g==
    dependencies:
      "@types/connect" "*"
      "@types/node" "*"
  
  "@types/bonjour@^3.5.9":
    version "3.5.10"
    resolved "https://registry.npmjs.org/@types/bonjour/-/bonjour-3.5.10.tgz"
    integrity sha512-p7ienRMiS41Nu2/igbJxxLDWrSZ0WxM8UQgCeO9KhoVF7cOVFkrKsiDr1EsJIla8vV3oEEjGcz11jc5yimhzZw==
    dependencies:
      "@types/node" "*"
  
  "@types/connect-history-api-fallback@^1.3.5":
    version "1.3.5"
    resolved "https://registry.npmjs.org/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.3.5.tgz"
    integrity sha512-h8QJa8xSb1WD4fpKBDcATDNGXghFj6/3GRWG6dhmRcu0RX1Ubasur2Uvx5aeEwlf0MwblEC2bMzzMQntxnw/Cw==
    dependencies:
      "@types/express-serve-static-core" "*"
      "@types/node" "*"
  
  "@types/connect@*":
    version "3.4.35"
    resolved "https://registry.npmjs.org/@types/connect/-/connect-3.4.35.tgz"
    integrity sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ==
    dependencies:
      "@types/node" "*"
  
  "@types/debug@^4.1.5":
    version "4.1.8"
    resolved "https://registry.npmjs.org/@types/debug/-/debug-4.1.8.tgz"
    integrity sha512-/vPO1EPOs306Cvhwv7KfVfYvOJqA/S/AXjaHQiJboCZzcNDb+TIJFN9/2C9DZ//ijSKWioNyUxD792QmDJ+HKQ==
    dependencies:
      "@types/ms" "*"
  
  "@types/eslint-scope@^3.7.3":
    version "3.7.4"
    resolved "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.4.tgz"
    integrity sha512-9K4zoImiZc3HlIp6AVUDE4CWYx22a+lhSZMYNpbjW04+YF0KWj4pJXnEMjdnFTiQibFFmElcsasJXDbdI/EPhA==
    dependencies:
      "@types/eslint" "*"
      "@types/estree" "*"
  
  "@types/eslint@*":
    version "8.4.6"
    resolved "https://registry.npmjs.org/@types/eslint/-/eslint-8.4.6.tgz"
    integrity sha512-/fqTbjxyFUaYNO7VcW5g+4npmqVACz1bB7RTHYuLj+PRjw9hrCwrUXVQFpChUS0JsyEFvMZ7U/PfmvWgxJhI9g==
    dependencies:
      "@types/estree" "*"
      "@types/json-schema" "*"
  
  "@types/estree@*", "@types/estree@^1.0.0":
    version "1.0.0"
    resolved "https://registry.npmjs.org/@types/estree/-/estree-1.0.0.tgz"
    integrity sha512-WulqXMDUTYAXCjZnk6JtIHPigp55cVtDgDrO2gHRwhyJto21+1zbVCtOYB2L1F9w4qCQ0rOGWBnBe0FNTiEJIQ==
  
  "@types/expect@^1.20.4":
    version "1.20.4"
    resolved "https://registry.npmjs.org/@types/expect/-/expect-1.20.4.tgz"
    integrity sha512-Q5Vn3yjTDyCMV50TB6VRIbQNxSE4OmZR86VSbGaNpfUolm0iePBB4KdEEHmxoY5sT2+2DIvXW0rvMDP2nHZ4Mg==
  
  "@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.18":
    version "4.17.31"
    resolved "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.31.tgz"
    integrity sha512-DxMhY+NAsTwMMFHBTtJFNp5qiHKJ7TeqOo23zVEM9alT1Ml27Q3xcTH0xwxn7Q0BbMcVEJOs/7aQtUWupUQN3Q==
    dependencies:
      "@types/node" "*"
      "@types/qs" "*"
      "@types/range-parser" "*"
  
  "@types/express@*", "@types/express@^4.17.13":
    version "4.17.14"
    resolved "https://registry.npmjs.org/@types/express/-/express-4.17.14.tgz"
    integrity sha512-TEbt+vaPFQ+xpxFLFssxUDXj5cWCxZJjIcB7Yg0k0GMHGtgtQgpvx/MUQUeAkNbA9AAGrwkAsoeItdTgS7FMyg==
    dependencies:
      "@types/body-parser" "*"
      "@types/express-serve-static-core" "^4.17.18"
      "@types/qs" "*"
      "@types/serve-static" "*"
  
  "@types/fs-extra@^8.0.1":
    version "8.1.2"
    resolved "https://registry.npmjs.org/@types/fs-extra/-/fs-extra-8.1.2.tgz"
    integrity sha512-SvSrYXfWSc7R4eqnOzbQF4TZmfpNSM9FrSWLU3EUnWBuyZqNBOrv1B1JA3byUDPUl9z4Ab3jeZG2eDdySlgNMg==
    dependencies:
      "@types/node" "*"
  
  "@types/glob@^7.1.1":
    version "7.2.0"
    resolved "https://registry.npmjs.org/@types/glob/-/glob-7.2.0.tgz"
    integrity sha512-ZUxbzKl0IfJILTS6t7ip5fQQM/J3TJYubDm3nMbgubNNYS62eXeUpoLUC8/7fJNiFYHTrGPQn7hspDUzIHX3UA==
    dependencies:
      "@types/minimatch" "*"
      "@types/node" "*"
  
  "@types/html-minifier-terser@^6.0.0":
    version "6.1.0"
    resolved "https://registry.npmjs.org/@types/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz"
    integrity sha512-oh/6byDPnL1zeNXFrDXFLyZjkr1MsBG667IM792caf1L2UPOOMf65NFzjUH/ltyfwjAGfs1rsX1eftK0jC/KIg==
  
  "@types/http-proxy@^1.17.8":
    version "1.17.9"
    resolved "https://registry.npmjs.org/@types/http-proxy/-/http-proxy-1.17.9.tgz"
    integrity sha512-QsbSjA/fSk7xB+UXlCT3wHBy5ai9wOcNDWwZAtud+jXhwOM3l+EYZh8Lng4+/6n8uar0J7xILzqftJdJ/Wdfkw==
    dependencies:
      "@types/node" "*"
  
  "@types/json-schema@*", "@types/json-schema@^7.0.5", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
    version "7.0.11"
    resolved "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.11.tgz"
    integrity sha512-wOuvG1SN4Us4rez+tylwwwCV1psiNVOkJeM3AUWUNWg/jDQY2+HE/444y5gc+jBmRqASOm2Oeh5c1axHobwRKQ==
  
  "@types/json5@^0.0.29":
    version "0.0.29"
    resolved "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz"
    integrity sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==
  
  "@types/lodash.debounce@^4.0.6":
    version "4.0.7"
    resolved "https://registry.npmjs.org/@types/lodash.debounce/-/lodash.debounce-4.0.7.tgz"
    integrity sha512-X1T4wMZ+gT000M2/91SYj0d/7JfeNZ9PeeOldSNoE/lunLeQXKvkmIumI29IaKMotU/ln/McOIvgzZcQ/3TrSA==
    dependencies:
      "@types/lodash" "*"
  
  "@types/lodash@*", "@types/lodash@^4.14.72":
    version "4.14.191"
    resolved "https://registry.npmjs.org/@types/lodash/-/lodash-4.14.191.tgz"
    integrity sha512-BdZ5BCCvho3EIXw6wUCXHe7rS53AIDPLE+JzwgT+OsJk53oBfbSmZZ7CX4VaRoN78N+TJpFi9QPlfIVNmJYWxQ==
  
  "@types/mime@*":
    version "3.0.1"
    resolved "https://registry.npmjs.org/@types/mime/-/mime-3.0.1.tgz"
    integrity sha512-Y4XFY5VJAuw0FgAqPNd6NNoV44jbq9Bz2L7Rh/J6jLTiHBSBJa9fxqQIvkIld4GsoDOcCbvzOUAbLPsSKKg+uA==
  
  "@types/minimatch@*", "@types/minimatch@^3.0.3":
    version "3.0.5"
    resolved "https://registry.npmjs.org/@types/minimatch/-/minimatch-3.0.5.tgz"
    integrity sha512-Klz949h02Gz2uZCMGwDUSDS1YBlTdDDgbWHi+81l29tQALUtvz4rAYi5uoVhE5Lagoq6DeqAUlbrHvW/mXDgdQ==
  
  "@types/minimist@^1.2.0":
    version "1.2.2"
    resolved "https://registry.npmjs.org/@types/minimist/-/minimist-1.2.2.tgz"
    integrity sha512-jhuKLIRrhvCPLqwPcx6INqmKeiA5EWrsCOPhrlFSrbrmU4ZMPjj5Ul/oLCMDO98XRUIwVm78xICz4EPCektzeQ==
  
  "@types/ms@*":
    version "0.7.31"
    resolved "https://registry.npmjs.org/@types/ms/-/ms-0.7.31.tgz"
    integrity sha512-iiUgKzV9AuaEkZqkOLDIvlQiL6ltuZd9tGcW3gwpnX8JbuiuhFlEGmmFXEXkN50Cvq7Os88IY2v0dkDqXYWVgA==
  
  "@types/node@*":
    version "18.7.23"
    resolved "https://registry.npmjs.org/@types/node/-/node-18.7.23.tgz"
    integrity sha512-DWNcCHolDq0ZKGizjx2DZjR/PqsYwAcYUJmfMWqtVU2MBMG5Mo+xFZrhGId5r/O5HOuMPyQEcM6KUBp5lBZZBg==
  
  "@types/node@^15.6.2":
    version "15.14.9"
    resolved "https://registry.npmjs.org/@types/node/-/node-15.14.9.tgz"
    integrity sha512-qjd88DrCxupx/kJD5yQgZdcYKZKSIGBVDIBE1/LTGcNm3d2Np/jxojkdePDdfnBHJc5W7vSMpbJ1aB7p/Py69A==
  
  "@types/normalize-package-data@^2.4.0":
    version "2.4.1"
    resolved "https://registry.npmjs.org/@types/normalize-package-data/-/normalize-package-data-2.4.1.tgz"
    integrity sha512-Gj7cI7z+98M282Tqmp2K5EIsoouUEzbBJhQQzDE3jSIRk6r9gsz0oUokqIUR4u1R3dMHo0pDHM7sNOHyhulypw==
  
  "@types/parse-json@^4.0.0":
    version "4.0.0"
    resolved "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.0.tgz"
    integrity sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA==
  
  "@types/prop-types@*":
    version "15.7.5"
    resolved "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.5.tgz"
    integrity sha512-JCB8C6SnDoQf0cNycqd/35A7MjcnK+ZTqE7judS6o7utxUCg6imJg3QK2qzHKszlTjcj2cn+NwMB2i96ubpj7w==
  
  "@types/qs@*":
    version "6.9.7"
    resolved "https://registry.npmjs.org/@types/qs/-/qs-6.9.7.tgz"
    integrity sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw==
  
  "@types/range-parser@*":
    version "1.2.4"
    resolved "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.4.tgz"
    integrity sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw==
  
  "@types/react@^18.2.6":
    version "18.2.6"
    resolved "https://registry.npmjs.org/@types/react/-/react-18.2.6.tgz"
    integrity sha512-wRZClXn//zxCFW+ye/D2qY65UsYP1Fpex2YXorHc8awoNamkMZSvBxwxdYVInsHOZZd2Ppq8isnSzJL5Mpf8OA==
    dependencies:
      "@types/prop-types" "*"
      "@types/scheduler" "*"
      csstype "^3.0.2"
  
  "@types/retry@0.12.0":
    version "0.12.0"
    resolved "https://registry.npmjs.org/@types/retry/-/retry-0.12.0.tgz"
    integrity sha512-wWKOClTTiizcZhXnPY4wikVAwmdYHp8q6DmC+EJUzAMsycb7HB32Kh9RN4+0gExjmPmZSAQjgURXIGATPegAvA==
  
  "@types/sass@^1.16.0":
    version "1.45.0"
    resolved "https://registry.npmjs.org/@types/sass/-/sass-1.45.0.tgz"
    integrity sha512-jn7qwGFmJHwUSphV8zZneO3GmtlgLsmhs/LQyVvQbIIa+fzGMUiHI4HXJZL3FT8MJmgXWbLGiVVY7ElvHq6vDA==
    dependencies:
      sass "*"
  
  "@types/scheduler@*":
    version "0.16.2"
    resolved "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.2.tgz"
    integrity sha512-hppQEBDmlwhFAXKJX2KnWLYu5yMfi91yazPb2l+lbJiwW+wdo1gNeRA+3RgNSO39WYX2euey41KEwnqesU2Jew==
  
  "@types/semver@^7.3.12":
    version "7.5.0"
    resolved "https://registry.npmjs.org/@types/semver/-/semver-7.5.0.tgz"
    integrity sha512-G8hZ6XJiHnuhQKR7ZmysCeJWE08o8T0AXtk5darsCaTVsYZhhgUrq53jizaR2FvsoeCwJhlmwTjkXBY5Pn/ZHw==
  
  "@types/serve-index@^1.9.1":
    version "1.9.1"
    resolved "https://registry.npmjs.org/@types/serve-index/-/serve-index-1.9.1.tgz"
    integrity sha512-d/Hs3nWDxNL2xAczmOVZNj92YZCS6RGxfBPjKzuu/XirCgXdpKEb88dYNbrYGint6IVWLNP+yonwVAuRC0T2Dg==
    dependencies:
      "@types/express" "*"
  
  "@types/serve-static@*":
    version "1.15.0"
    resolved "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.0.tgz"
    integrity sha512-z5xyF6uh8CbjAu9760KDKsH2FcDxZ2tFCsA4HIMWE6IkiYMXfVoa+4f9KX+FN0ZLsaMw1WNG2ETLA6N+/YA+cg==
    dependencies:
      "@types/mime" "*"
      "@types/node" "*"
  
  "@types/sockjs@^0.3.33":
    version "0.3.33"
    resolved "https://registry.npmjs.org/@types/sockjs/-/sockjs-0.3.33.tgz"
    integrity sha512-f0KEEe05NvUnat+boPTZ0dgaLZ4SfSouXUgv5noUiefG2ajgKjmETo9ZJyuqsl7dfl2aHlLJUiki6B4ZYldiiw==
    dependencies:
      "@types/node" "*"
  
  "@types/vinyl@^2.0.4":
    version "2.0.7"
    resolved "https://registry.npmjs.org/@types/vinyl/-/vinyl-2.0.7.tgz"
    integrity sha512-4UqPv+2567NhMQuMLdKAyK4yzrfCqwaTt6bLhHEs8PFcxbHILsrxaY63n4wgE/BRLDWDQeI+WcTmkXKExh9hQg==
    dependencies:
      "@types/expect" "^1.20.4"
      "@types/node" "*"
  
  "@types/webpack-env@^1.13.6":
    version "1.18.0"
    resolved "https://registry.npmjs.org/@types/webpack-env/-/webpack-env-1.18.0.tgz"
    integrity sha512-56/MAlX5WMsPVbOg7tAxnYvNYMMWr/QJiIp6BxVSW3JJXUVzzOn64qW8TzQyMSqSUFM2+PVI4aUHcHOzIz/1tg==
  
  "@types/ws@^8.2.2":
    version "8.5.3"
    resolved "https://registry.npmjs.org/@types/ws/-/ws-8.5.3.tgz"
    integrity sha512-6YOoWjruKj1uLf3INHH7D3qTXwFfEsg1kf3c0uDdSBJwfa/llkwIjrAGV7j7mVgGNbzTQ3HiHKKDXl6bJPD97w==
    dependencies:
      "@types/node" "*"
  
  "@typescript-eslint/eslint-plugin@^5.59.5":
    version "5.59.7"
    resolved "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-5.59.7.tgz"
    integrity sha512-BL+jYxUFIbuYwy+4fF86k5vdT9lT0CNJ6HtwrIvGh0PhH8s0yy5rjaKH2fDCrz5ITHy07WCzVGNvAmjJh4IJFA==
    dependencies:
      "@eslint-community/regexpp" "^4.4.0"
      "@typescript-eslint/scope-manager" "5.59.7"
      "@typescript-eslint/type-utils" "5.59.7"
      "@typescript-eslint/utils" "5.59.7"
      debug "^4.3.4"
      grapheme-splitter "^1.0.4"
      ignore "^5.2.0"
      natural-compare-lite "^1.4.0"
      semver "^7.3.7"
      tsutils "^3.21.0"
  
  "@typescript-eslint/parser@^5.0.0", "@typescript-eslint/parser@^5.20.0", "@typescript-eslint/parser@^5.59.5":
    version "5.59.7"
    resolved "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-5.59.7.tgz"
    integrity sha512-VhpsIEuq/8i5SF+mPg9jSdIwgMBBp0z9XqjiEay+81PYLJuroN+ET1hM5IhkiYMJd9MkTz8iJLt7aaGAgzWUbQ==
    dependencies:
      "@typescript-eslint/scope-manager" "5.59.7"
      "@typescript-eslint/types" "5.59.7"
      "@typescript-eslint/typescript-estree" "5.59.7"
      debug "^4.3.4"
  
  "@typescript-eslint/scope-manager@5.59.7":
    version "5.59.7"
    resolved "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-5.59.7.tgz"
    integrity sha512-FL6hkYWK9zBGdxT2wWEd2W8ocXMu3K94i3gvMrjXpx+koFYdYV7KprKfirpgY34vTGzEPPuKoERpP8kD5h7vZQ==
    dependencies:
      "@typescript-eslint/types" "5.59.7"
      "@typescript-eslint/visitor-keys" "5.59.7"
  
  "@typescript-eslint/type-utils@5.59.7":
    version "5.59.7"
    resolved "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-5.59.7.tgz"
    integrity sha512-ozuz/GILuYG7osdY5O5yg0QxXUAEoI4Go3Do5xeu+ERH9PorHBPSdvD3Tjp2NN2bNLh1NJQSsQu2TPu/Ly+HaQ==
    dependencies:
      "@typescript-eslint/typescript-estree" "5.59.7"
      "@typescript-eslint/utils" "5.59.7"
      debug "^4.3.4"
      tsutils "^3.21.0"
  
  "@typescript-eslint/types@5.59.7":
    version "5.59.7"
    resolved "https://registry.npmjs.org/@typescript-eslint/types/-/types-5.59.7.tgz"
    integrity sha512-UnVS2MRRg6p7xOSATscWkKjlf/NDKuqo5TdbWck6rIRZbmKpVNTLALzNvcjIfHBE7736kZOFc/4Z3VcZwuOM/A==
  
  "@typescript-eslint/typescript-estree@5.59.7":
    version "5.59.7"
    resolved "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-5.59.7.tgz"
    integrity sha512-4A1NtZ1I3wMN2UGDkU9HMBL+TIQfbrh4uS0WDMMpf3xMRursDbqEf1ahh6vAAe3mObt8k3ZATnezwG4pdtWuUQ==
    dependencies:
      "@typescript-eslint/types" "5.59.7"
      "@typescript-eslint/visitor-keys" "5.59.7"
      debug "^4.3.4"
      globby "^11.1.0"
      is-glob "^4.0.3"
      semver "^7.3.7"
      tsutils "^3.21.0"
  
  "@typescript-eslint/utils@5.59.7":
    version "5.59.7"
    resolved "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-5.59.7.tgz"
    integrity sha512-yCX9WpdQKaLufz5luG4aJbOpdXf/fjwGMcLFXZVPUz3QqLirG5QcwwnIHNf8cjLjxK4qtzTO8udUtMQSAToQnQ==
    dependencies:
      "@eslint-community/eslint-utils" "^4.2.0"
      "@types/json-schema" "^7.0.9"
      "@types/semver" "^7.3.12"
      "@typescript-eslint/scope-manager" "5.59.7"
      "@typescript-eslint/types" "5.59.7"
      "@typescript-eslint/typescript-estree" "5.59.7"
      eslint-scope "^5.1.1"
      semver "^7.3.7"
  
  "@typescript-eslint/visitor-keys@5.59.7":
    version "5.59.7"
    resolved "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-5.59.7.tgz"
    integrity sha512-tyN+X2jvMslUszIiYbF0ZleP+RqQsFVpGrKI6e0Eet1w8WmhsAtmzaqm8oM8WJQ1ysLwhnsK/4hYHJjOgJVfQQ==
    dependencies:
      "@typescript-eslint/types" "5.59.7"
      eslint-visitor-keys "^3.3.0"
  
  "@use-gesture/core@10.2.20":
    version "10.2.20"
    resolved "https://registry.npmjs.org/@use-gesture/core/-/core-10.2.20.tgz"
    integrity sha512-4lFhHc8so4yIHkBEs641DnEsBxPyhJ5GEjB4PURFDH4p/FcZriH6w99knZgI63zN/MBFfylMyb8+PDuj6RIXKQ==
  
  "@use-gesture/core@10.2.9":
    version "10.2.9"
    resolved "https://registry.npmjs.org/@use-gesture/core/-/core-10.2.9.tgz"
    integrity sha512-MsPUCWZ6BSir8XqSrfQCyrFuBz405YI2D0hfZ7TXX/hhF0kekN+noPhHjg7m85vMyCYf92fjt3J5AbjoGV4eMw==
  
  "@use-gesture/react@10.2.20":
    version "10.2.20"
    resolved "https://registry.npmjs.org/@use-gesture/react/-/react-10.2.20.tgz"
    integrity sha512-KnJq9ZSqprWA6uNhWTUHZqTCh+rfa0j8ehTzqeBhktUPrmTj7yVOBvEQ/vSFU/7d72cGgWSsJ0f5T6GQCHXnvg==
    dependencies:
      "@use-gesture/core" "10.2.20"
  
  "@use-gesture/react@10.2.9":
    version "10.2.9"
    resolved "https://registry.npmjs.org/@use-gesture/react/-/react-10.2.9.tgz"
    integrity sha512-E5BynF6ADi3mRqRiOOSDPpefifZskE03HYHvR+Z8ngWvxf4WztzPVfyCEx2l4I0kpe9gWdf7i4fciEyt/p9bIw==
    dependencies:
      "@use-gesture/core" "10.2.9"
  
  "@vue/compiler-core@3.2.47":
    version "3.2.47"
    resolved "https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.2.47.tgz"
    integrity sha512-p4D7FDnQb7+YJmO2iPEv0SQNeNzcbHdGByJDsT4lynf63AFkOTFN07HsiRSvjGo0QrxR/o3d0hUyNCUnBU2Tig==
    dependencies:
      "@babel/parser" "^7.16.4"
      "@vue/shared" "3.2.47"
      estree-walker "^2.0.2"
      source-map "^0.6.1"
  
  "@vue/compiler-dom@3.2.47":
    version "3.2.47"
    resolved "https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.2.47.tgz"
    integrity sha512-dBBnEHEPoftUiS03a4ggEig74J2YBZ2UIeyfpcRM2tavgMWo4bsEfgCGsu+uJIL/vax9S+JztH8NmQerUo7shQ==
    dependencies:
      "@vue/compiler-core" "3.2.47"
      "@vue/shared" "3.2.47"
  
  "@vue/compiler-sfc@^3.0.5":
    version "3.2.47"
    resolved "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.2.47.tgz"
    integrity sha512-rog05W+2IFfxjMcFw10tM9+f7i/+FFpZJJ5XHX72NP9eC2uRD+42M3pYcQqDXVYoj74kHMSEdQ/WmCjt8JFksQ==
    dependencies:
      "@babel/parser" "^7.16.4"
      "@vue/compiler-core" "3.2.47"
      "@vue/compiler-dom" "3.2.47"
      "@vue/compiler-ssr" "3.2.47"
      "@vue/reactivity-transform" "3.2.47"
      "@vue/shared" "3.2.47"
      estree-walker "^2.0.2"
      magic-string "^0.25.7"
      postcss "^8.1.10"
      source-map "^0.6.1"
  
  "@vue/compiler-ssr@3.2.47":
    version "3.2.47"
    resolved "https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.2.47.tgz"
    integrity sha512-wVXC+gszhulcMD8wpxMsqSOpvDZ6xKXSVWkf50Guf/S+28hTAXPDYRTbLQ3EDkOP5Xz/+SY37YiwDquKbJOgZw==
    dependencies:
      "@vue/compiler-dom" "3.2.47"
      "@vue/shared" "3.2.47"
  
  "@vue/component-compiler-utils@^3.1.0":
    version "3.3.0"
    resolved "https://registry.npmjs.org/@vue/component-compiler-utils/-/component-compiler-utils-3.3.0.tgz"
    integrity sha512-97sfH2mYNU+2PzGrmK2haqffDpVASuib9/w2/noxiFi31Z54hW+q3izKQXXQZSNhtiUpAI36uSuYepeBe4wpHQ==
    dependencies:
      consolidate "^0.15.1"
      hash-sum "^1.0.2"
      lru-cache "^4.1.2"
      merge-source-map "^1.1.0"
      postcss "^7.0.36"
      postcss-selector-parser "^6.0.2"
      source-map "~0.6.1"
      vue-template-es2015-compiler "^1.9.0"
    optionalDependencies:
      prettier "^1.18.2 || ^2.0.0"
  
  "@vue/reactivity-transform@3.2.47":
    version "3.2.47"
    resolved "https://registry.npmjs.org/@vue/reactivity-transform/-/reactivity-transform-3.2.47.tgz"
    integrity sha512-m8lGXw8rdnPVVIdIFhf0LeQ/ixyHkH5plYuS83yop5n7ggVJU+z5v0zecwEnX7fa7HNLBhh2qngJJkxpwEEmYA==
    dependencies:
      "@babel/parser" "^7.16.4"
      "@vue/compiler-core" "3.2.47"
      "@vue/shared" "3.2.47"
      estree-walker "^2.0.2"
      magic-string "^0.25.7"
  
  "@vue/shared@3.2.47":
    version "3.2.47"
    resolved "https://registry.npmjs.org/@vue/shared/-/shared-3.2.47.tgz"
    integrity sha512-BHGyyGN3Q97EZx0taMQ+OLNuZcW3d37ZEVmEAyeoA9ERdGvm9Irc/0Fua8SNyOtV1w6BS4q25wbMzJujO9HIfQ==
  
  "@webassemblyjs/ast@^1.11.5", "@webassemblyjs/ast@1.11.6":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.11.6.tgz"
    integrity sha512-IN1xI7PwOvLPgjcf180gC1bqn3q/QaOCwYUahIOhbYUu8KA/3tw2RT/T0Gidi1l7Hhj5D/INhJxiICObqpMu4Q==
    dependencies:
      "@webassemblyjs/helper-numbers" "1.11.6"
      "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
  
  "@webassemblyjs/floating-point-hex-parser@1.11.6":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.11.6.tgz"
    integrity sha512-ejAj9hfRJ2XMsNHk/v6Fu2dGS+i4UaXBXGemOfQ/JfQ6mdQg/WXtwleQRLLS4OvfDhv8rYnVwH27YJLMyYsxhw==
  
  "@webassemblyjs/helper-api-error@1.11.6":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.6.tgz"
    integrity sha512-o0YkoP4pVu4rN8aTJgAyj9hC2Sv5UlkzCHhxqWj8butaLvnpdc2jOwh4ewE6CX0txSfLn/UYaV/pheS2Txg//Q==
  
  "@webassemblyjs/helper-buffer@1.11.6":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.11.6.tgz"
    integrity sha512-z3nFzdcp1mb8nEOFFk8DrYLpHvhKC3grJD2ardfKOzmbmJvEf/tPIqCY+sNcwZIY8ZD7IkB2l7/pqhUhqm7hLA==
  
  "@webassemblyjs/helper-numbers@1.11.6":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/helper-numbers/-/helper-numbers-1.11.6.tgz"
    integrity sha512-vUIhZ8LZoIWHBohiEObxVm6hwP034jwmc9kuq5GdHZH0wiLVLIPcMCdpJzG4C11cHoQ25TFIQj9kaVADVX7N3g==
    dependencies:
      "@webassemblyjs/floating-point-hex-parser" "1.11.6"
      "@webassemblyjs/helper-api-error" "1.11.6"
      "@xtuc/long" "4.2.2"
  
  "@webassemblyjs/helper-wasm-bytecode@1.11.6":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.11.6.tgz"
    integrity sha512-sFFHKwcmBprO9e7Icf0+gddyWYDViL8bpPjJJl0WHxCdETktXdmtWLGVzoHbqUcY4Be1LkNfwTmXOJUFZYSJdA==
  
  "@webassemblyjs/helper-wasm-section@1.11.6":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.11.6.tgz"
    integrity sha512-LPpZbSOwTpEC2cgn4hTydySy1Ke+XEu+ETXuoyvuyezHO3Kjdu90KK95Sh9xTbmjrCsUwvWwCOQQNta37VrS9g==
    dependencies:
      "@webassemblyjs/ast" "1.11.6"
      "@webassemblyjs/helper-buffer" "1.11.6"
      "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
      "@webassemblyjs/wasm-gen" "1.11.6"
  
  "@webassemblyjs/ieee754@1.11.6":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.11.6.tgz"
    integrity sha512-LM4p2csPNvbij6U1f19v6WR56QZ8JcHg3QIJTlSwzFcmx6WSORicYj6I63f9yU1kEUtrpG+kjkiIAkevHpDXrg==
    dependencies:
      "@xtuc/ieee754" "^1.2.0"
  
  "@webassemblyjs/leb128@1.11.6":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.11.6.tgz"
    integrity sha512-m7a0FhE67DQXgouf1tbN5XQcdWoNgaAuoULHIfGFIEVKA6tu/edls6XnIlkmS6FrXAquJRPni3ZZKjw6FSPjPQ==
    dependencies:
      "@xtuc/long" "4.2.2"
  
  "@webassemblyjs/utf8@1.11.6":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.11.6.tgz"
    integrity sha512-vtXf2wTQ3+up9Zsg8sa2yWiQpzSsMyXj0qViVP6xKGCUT8p8YJ6HqI7l5eCnWx1T/FYdsv07HQs2wTFbbof/RA==
  
  "@webassemblyjs/wasm-edit@^1.11.5":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.11.6.tgz"
    integrity sha512-Ybn2I6fnfIGuCR+Faaz7YcvtBKxvoLV3Lebn1tM4o/IAJzmi9AWYIPWpyBfU8cC+JxAO57bk4+zdsTjJR+VTOw==
    dependencies:
      "@webassemblyjs/ast" "1.11.6"
      "@webassemblyjs/helper-buffer" "1.11.6"
      "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
      "@webassemblyjs/helper-wasm-section" "1.11.6"
      "@webassemblyjs/wasm-gen" "1.11.6"
      "@webassemblyjs/wasm-opt" "1.11.6"
      "@webassemblyjs/wasm-parser" "1.11.6"
      "@webassemblyjs/wast-printer" "1.11.6"
  
  "@webassemblyjs/wasm-gen@1.11.6":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.11.6.tgz"
    integrity sha512-3XOqkZP/y6B4F0PBAXvI1/bky7GryoogUtfwExeP/v7Nzwo1QLcq5oQmpKlftZLbT+ERUOAZVQjuNVak6UXjPA==
    dependencies:
      "@webassemblyjs/ast" "1.11.6"
      "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
      "@webassemblyjs/ieee754" "1.11.6"
      "@webassemblyjs/leb128" "1.11.6"
      "@webassemblyjs/utf8" "1.11.6"
  
  "@webassemblyjs/wasm-opt@1.11.6":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.11.6.tgz"
    integrity sha512-cOrKuLRE7PCe6AsOVl7WasYf3wbSo4CeOk6PkrjS7g57MFfVUF9u6ysQBBODX0LdgSvQqRiGz3CXvIDKcPNy4g==
    dependencies:
      "@webassemblyjs/ast" "1.11.6"
      "@webassemblyjs/helper-buffer" "1.11.6"
      "@webassemblyjs/wasm-gen" "1.11.6"
      "@webassemblyjs/wasm-parser" "1.11.6"
  
  "@webassemblyjs/wasm-parser@^1.11.5", "@webassemblyjs/wasm-parser@1.11.6":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.11.6.tgz"
    integrity sha512-6ZwPeGzMJM3Dqp3hCsLgESxBGtT/OeCvCZ4TA1JUPYgmhAx38tTPR9JaKy0S5H3evQpO/h2uWs2j6Yc/fjkpTQ==
    dependencies:
      "@webassemblyjs/ast" "1.11.6"
      "@webassemblyjs/helper-api-error" "1.11.6"
      "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
      "@webassemblyjs/ieee754" "1.11.6"
      "@webassemblyjs/leb128" "1.11.6"
      "@webassemblyjs/utf8" "1.11.6"
  
  "@webassemblyjs/wast-printer@1.11.6":
    version "1.11.6"
    resolved "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.11.6.tgz"
    integrity sha512-JM7AhRcE+yW2GWYaKeHL5vt4xqee5N2WcezptmgyhNS+ScggqcT1OtXykhAb13Sn5Yas0j2uv9tHgrjwvzAP4A==
    dependencies:
      "@webassemblyjs/ast" "1.11.6"
      "@xtuc/long" "4.2.2"
  
  "@xtuc/ieee754@^1.2.0":
    version "1.2.0"
    resolved "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
    integrity sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==
  
  "@xtuc/long@4.2.2":
    version "4.2.2"
    resolved "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz"
    integrity sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==
  
  abab@^2.0.6:
    version "2.0.6"
    resolved "https://registry.npmjs.org/abab/-/abab-2.0.6.tgz"
    integrity sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA==
  
  abortcontroller-polyfill@^1.7.5:
    version "1.7.5"
    resolved "https://registry.npmjs.org/abortcontroller-polyfill/-/abortcontroller-polyfill-1.7.5.tgz"
    integrity sha512-JMJ5soJWP18htbbxJjG7bG6yuI6pRhgJ0scHHTfkUjf6wjP912xZWvM+A4sJK3gqd9E8fcPbDnOefbA9Th/FIQ==
  
  accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.8:
    version "1.3.8"
    resolved "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz"
    integrity sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==
    dependencies:
      mime-types "~2.1.34"
      negotiator "0.6.3"
  
  acorn-globals@^7.0.0:
    version "7.0.1"
    resolved "https://registry.npmjs.org/acorn-globals/-/acorn-globals-7.0.1.tgz"
    integrity sha512-umOSDSDrfHbTNPuNpC2NSnnA3LUrqpevPb4T9jRx4MagXNS0rs+gwiTcAvqCRmsD6utzsrzNt+ebm00SNWiC3Q==
    dependencies:
      acorn "^8.1.0"
      acorn-walk "^8.0.2"
  
  acorn-import-assertions@^1.7.6:
    version "1.8.0"
    resolved "https://registry.npmjs.org/acorn-import-assertions/-/acorn-import-assertions-1.8.0.tgz"
    integrity sha512-m7VZ3jwz4eK6A4Vtt8Ew1/mNbP24u0FhdyfA7fSvnJR6LMdfOYnmuIrrJAgrYfYJ10F/otaHTtrtrtmHdMNzEw==
  
  acorn-jsx@^5.3.2:
    version "5.3.2"
    resolved "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
    integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==
  
  acorn-walk@^8.0.0, acorn-walk@^8.0.2, acorn-walk@^8.2.0:
    version "8.2.0"
    resolved "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.2.0.tgz"
    integrity sha512-k+iyHEuPgSw6SbuDpGQM+06HQUa04DZ3o+F6CSzXMvvI5KMvnaEqXe+YVe555R9nn6GPt404fos4wcgpw12SDA==
  
  "acorn@^6.0.0 || ^7.0.0 || ^8.0.0", acorn@^8, acorn@^8.0.4, acorn@^8.1.0, acorn@^8.5.0, acorn@^8.7.0, acorn@^8.7.1, acorn@^8.8.0, acorn@^8.8.2:
    version "8.8.2"
    resolved "https://registry.npmjs.org/acorn/-/acorn-8.8.2.tgz"
    integrity sha512-xjIYgE8HBrkpd/sJqOGNspf8uHG+NOHGOw6a/Urj8taM2EXfdNAH2oFcPeIFfsv3+kz/mJrS5VuMqbNLjCa2vw==
  
  address@^1.0.1:
    version "1.2.1"
    resolved "https://registry.npmjs.org/address/-/address-1.2.1.tgz"
    integrity sha512-B+6bi5D34+fDYENiH5qOlA0cV2rAGKuWZ9LeyUUehbXy8e0VS9e498yO0Jeeh+iM+6KbfudHTFjXw2MmJD4QRA==
  
  adjust-sourcemap-loader@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/adjust-sourcemap-loader/-/adjust-sourcemap-loader-4.0.0.tgz"
    integrity sha512-OXwN5b9pCUXNQHJpwwD2qP40byEmSgzj8B4ydSN0uMNYWiFmJ6x6KwUllMmfk8Rwu/HJDFR7U8ubsWBoN0Xp0A==
    dependencies:
      loader-utils "^2.0.0"
      regex-parser "^2.2.11"
  
  adm-zip@^0.4.13:
    version "0.4.16"
    resolved "https://registry.npmjs.org/adm-zip/-/adm-zip-0.4.16.tgz"
    integrity sha512-TFi4HBKSGfIKsK5YCkKaaFG2m4PEDyViZmEwof3MTIgzimHLto6muaHVpbrljdIvIrFZzEq/p4nafOeLcYegrg==
  
  agent-base@6:
    version "6.0.2"
    resolved "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
    integrity sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==
    dependencies:
      debug "4"
  
  aggregate-error@^3.0.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/aggregate-error/-/aggregate-error-3.1.0.tgz"
    integrity sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==
    dependencies:
      clean-stack "^2.0.0"
      indent-string "^4.0.0"
  
  ajv-formats@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz"
    integrity sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==
    dependencies:
      ajv "^8.0.0"
  
  ajv-keywords@^3.5.2:
    version "3.5.2"
    resolved "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
    integrity sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==
  
  ajv-keywords@^5.0.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz"
    integrity sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==
    dependencies:
      fast-deep-equal "^3.1.3"
  
  ajv@^6.10.0, ajv@^6.12.3, ajv@^6.12.4, ajv@^6.12.5, ajv@^6.9.1:
    version "6.12.6"
    resolved "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
    integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
    dependencies:
      fast-deep-equal "^3.1.1"
      fast-json-stable-stringify "^2.0.0"
      json-schema-traverse "^0.4.1"
      uri-js "^4.2.2"
  
  ajv@^8.0.0, ajv@^8.8.0, ajv@^8.8.2:
    version "8.11.0"
    resolved "https://registry.npmjs.org/ajv/-/ajv-8.11.0.tgz"
    integrity sha512-wGgprdCvMalC0BztXvitD2hC04YffAvtsUn93JbGXYLAtCUO4xd17mCCZQxUOItiBwZvJScWo8NIvQMQ71rdpg==
    dependencies:
      fast-deep-equal "^3.1.1"
      json-schema-traverse "^1.0.0"
      require-from-string "^2.0.2"
      uri-js "^4.2.2"
  
  ajv@^8.0.1:
    version "8.12.0"
    resolved "https://registry.npmjs.org/ajv/-/ajv-8.12.0.tgz"
    integrity sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==
    dependencies:
      fast-deep-equal "^3.1.1"
      json-schema-traverse "^1.0.0"
      require-from-string "^2.0.2"
      uri-js "^4.2.2"
  
  ansi-align@^3.0.0:
    version "3.0.1"
    resolved "https://registry.npmjs.org/ansi-align/-/ansi-align-3.0.1.tgz"
    integrity sha512-IOfwwBF5iczOjp/WeY4YxyjqAFMQoZufdQWDd19SEExbVLNXqvpzSJ/M7Za4/sCPmQ0+GRquoA7bGcINcxew6w==
    dependencies:
      string-width "^4.1.0"
  
  ansi-escapes@^4.2.1, ansi-escapes@^4.3.2:
    version "4.3.2"
    resolved "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
    integrity sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==
    dependencies:
      type-fest "^0.21.3"
  
  ansi-html-community@^0.0.8:
    version "0.0.8"
    resolved "https://registry.npmjs.org/ansi-html-community/-/ansi-html-community-0.0.8.tgz"
    integrity sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw==
  
  ansi-regex@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
    integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==
  
  ansi-regex@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.0.1.tgz"
    integrity sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==
  
  ansi-styles@^3.2.1:
    version "3.2.1"
    resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
    integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
    dependencies:
      color-convert "^1.9.0"
  
  ansi-styles@^4.0.0, ansi-styles@^4.1.0:
    version "4.3.0"
    resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
    integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
    dependencies:
      color-convert "^2.0.1"
  
  any-promise@^1.0.0:
    version "1.3.0"
    resolved "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz"
    integrity sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==
  
  anymatch@~3.1.2:
    version "3.1.2"
    resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.2.tgz"
    integrity sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==
    dependencies:
      normalize-path "^3.0.0"
      picomatch "^2.0.4"
  
  archive-type@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/archive-type/-/archive-type-4.0.0.tgz"
    integrity sha512-zV4Ky0v1F8dBrdYElwTvQhweQ0P7Kwc1aluqJsYtOBP01jXcWCyW2IEfI1YiqsG+Iy7ZR+o5LF1N+PGECBxHWA==
    dependencies:
      file-type "^4.2.0"
  
  archy@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/archy/-/archy-1.0.0.tgz"
    integrity sha512-Xg+9RwCg/0p32teKdGMPTPnVXKD0w3DfHnFTficozsAgsvq2XenPJq/MYpzzQ/v8zrOyJn6Ds39VA4JIDwFfqw==
  
  argparse@^1.0.7:
    version "1.0.10"
    resolved "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
    integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
    dependencies:
      sprintf-js "~1.0.2"
  
  argparse@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
    integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==
  
  array-buffer-byte-length@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.0.tgz"
    integrity sha512-LPuwb2P+NrQw3XhxGc36+XSvuBPopovXYTR9Ew++Du9Yb/bx5AzBfrIsBoj0EZUifjQU+sHL21sseZ3jerWO/A==
    dependencies:
      call-bind "^1.0.2"
      is-array-buffer "^3.0.1"
  
  array-differ@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/array-differ/-/array-differ-3.0.0.tgz"
    integrity sha512-THtfYS6KtME/yIAhKjZ2ul7XI96lQGHRputJQHO80LAWQnuGP4iCIN8vdMRboGbIEYBwU33q8Tch1os2+X0kMg==
  
  array-flatten@^2.1.0:
    version "2.1.2"
    resolved "https://registry.npmjs.org/array-flatten/-/array-flatten-2.1.2.tgz"
    integrity sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ==
  
  array-flatten@1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"
    integrity sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==
  
  array-includes@^3.1.5, array-includes@^3.1.6:
    version "3.1.6"
    resolved "https://registry.npmjs.org/array-includes/-/array-includes-3.1.6.tgz"
    integrity sha512-sgTbLvL6cNnw24FnbaDyjmvddQ2ML8arZsgaJhoABMoplz/4QRhtrYS+alr1BUM1Bwp6dhx8vVCBSLG+StwOFw==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
      get-intrinsic "^1.1.3"
      is-string "^1.0.7"
  
  array-union@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz"
    integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==
  
  array-union@^3.0.1:
    version "3.0.1"
    resolved "https://registry.npmjs.org/array-union/-/array-union-3.0.1.tgz"
    integrity sha512-1OvF9IbWwaeiM9VhzYXVQacMibxpXOMYVNIvMtKRyX9SImBXpKcFr8XvFDeEslCyuH/t6KRt7HEO94AlP8Iatw==
  
  array.prototype.flat@^1.3.1:
    version "1.3.1"
    resolved "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.1.tgz"
    integrity sha512-roTU0KWIOmJ4DRLmwKd19Otg0/mT3qPNt0Qb3GWW8iObuZXxrjB/pzn0R3hqpRSWg4HCwqx+0vwOnWnvlOyeIA==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
      es-shim-unscopables "^1.0.0"
  
  array.prototype.flatmap@^1.3.1:
    version "1.3.1"
    resolved "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.1.tgz"
    integrity sha512-8UGn9O1FDVvMNB0UlLv4voxRMze7+FpHyF5mSMRjWHUMlpoDViniy05870VlxhfgTnLbpuwTzvD76MTtWxB/mQ==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
      es-shim-unscopables "^1.0.0"
  
  array.prototype.tosorted@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/array.prototype.tosorted/-/array.prototype.tosorted-1.1.1.tgz"
    integrity sha512-pZYPXPRl2PqWcsUs6LOMn+1f1532nEoPTYowBtqLwAW+W8vSVhkIGnmOX1t/UQjD6YGI0vcD2B1U7ZFGQH9jnQ==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
      es-shim-unscopables "^1.0.0"
      get-intrinsic "^1.1.3"
  
  arrify@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/arrify/-/arrify-1.0.1.tgz"
    integrity sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA==
  
  arrify@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/arrify/-/arrify-2.0.1.tgz"
    integrity sha512-3duEwti880xqi4eAMN8AyR4a0ByT90zoYdLlevfrvU43vb0YZwZVfxOgxWrLXXXpyugL0hNZc9G6BiB5B3nUug==
  
  asn1@~0.2.3:
    version "0.2.6"
    resolved "https://registry.npmjs.org/asn1/-/asn1-0.2.6.tgz"
    integrity sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==
    dependencies:
      safer-buffer "~2.1.0"
  
  assert-plus@^1.0.0, assert-plus@1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz"
    integrity sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==
  
  astral-regex@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/astral-regex/-/astral-regex-2.0.0.tgz"
    integrity sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==
  
  async-validator@^4.2.5:
    version "4.2.5"
    resolved "https://registry.npmjs.org/async-validator/-/async-validator-4.2.5.tgz"
    integrity sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==
  
  async@^2.6.4:
    version "2.6.4"
    resolved "https://registry.npmjs.org/async/-/async-2.6.4.tgz"
    integrity sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==
    dependencies:
      lodash "^4.17.14"
  
  async@^3.2.3:
    version "3.2.4"
    resolved "https://registry.npmjs.org/async/-/async-3.2.4.tgz"
    integrity sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ==
  
  asynckit@^0.4.0:
    version "0.4.0"
    resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
    integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==
  
  atob@^2.1.2:
    version "2.1.2"
    resolved "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz"
    integrity sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==
  
  autoprefixer@^9.7.4:
    version "9.8.8"
    resolved "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.8.8.tgz"
    integrity sha512-eM9d/swFopRt5gdJ7jrpCwgvEMIayITpojhkkSMRsFHYuH5bkSQ4p/9qTEHtmNudUZh22Tehu7I6CxAW0IXTKA==
    dependencies:
      browserslist "^4.12.0"
      caniuse-lite "^1.0.30001109"
      normalize-range "^0.1.2"
      num2fraction "^1.2.2"
      picocolors "^0.2.1"
      postcss "^7.0.32"
      postcss-value-parser "^4.1.0"
  
  available-typed-arrays@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz"
    integrity sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==
  
  aws-sign2@~0.7.0:
    version "0.7.0"
    resolved "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz"
    integrity sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==
  
  aws4@^1.8.0:
    version "1.11.0"
    resolved "https://registry.npmjs.org/aws4/-/aws4-1.11.0.tgz"
    integrity sha512-xh1Rl34h6Fi1DC2WWKfxUTVqRsNnr6LsKz2+hfwDxQJWmrx8+c7ylaqBMcHfl1U1r2dsifOvKX3LQuLNZ+XSvA==
  
  babel-helper-evaluate-path@^0.5.0:
    version "0.5.0"
    resolved "https://registry.npmjs.org/babel-helper-evaluate-path/-/babel-helper-evaluate-path-0.5.0.tgz"
    integrity sha512-mUh0UhS607bGh5wUMAQfOpt2JX2ThXMtppHRdRU1kL7ZLRWIXxoV2UIV1r2cAeeNeU1M5SB5/RSUgUxrK8yOkA==
  
  babel-helper-mark-eval-scopes@^0.4.3:
    version "0.4.3"
    resolved "https://registry.npmjs.org/babel-helper-mark-eval-scopes/-/babel-helper-mark-eval-scopes-0.4.3.tgz"
    integrity sha512-+d/mXPP33bhgHkdVOiPkmYoeXJ+rXRWi7OdhwpyseIqOS8CmzHQXHUp/+/Qr8baXsT0kjGpMHHofHs6C3cskdA==
  
  babel-helper-remove-or-void@^0.4.3:
    version "0.4.3"
    resolved "https://registry.npmjs.org/babel-helper-remove-or-void/-/babel-helper-remove-or-void-0.4.3.tgz"
    integrity sha512-eYNceYtcGKpifHDir62gHJadVXdg9fAhuZEXiRQnJJ4Yi4oUTpqpNY//1pM4nVyjjDMPYaC2xSf0I+9IqVzwdA==
  
  babel-loader@8.2.1:
    version "8.2.1"
    resolved "https://registry.npmjs.org/babel-loader/-/babel-loader-8.2.1.tgz"
    integrity sha512-dMF8sb2KQ8kJl21GUjkW1HWmcsL39GOV5vnzjqrCzEPNY0S0UfMLnumidiwIajDSBmKhYf5iRW+HXaM4cvCKBw==
    dependencies:
      find-cache-dir "^2.1.0"
      loader-utils "^1.4.0"
      make-dir "^2.1.0"
      pify "^4.0.1"
      schema-utils "^2.6.5"
  
  babel-plugin-dynamic-import-node@^2.3.3, babel-plugin-dynamic-import-node@2.3.3:
    version "2.3.3"
    resolved "https://registry.npmjs.org/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz"
    integrity sha512-jZVI+s9Zg3IqA/kdi0i6UDCybUI3aSBLnglhYbSSjKlV7yF1F/5LWv8MakQmvYpnbJDS6fcBL2KzHSxNCMtWSQ==
    dependencies:
      object.assign "^4.1.0"
  
  babel-plugin-global-define@1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/babel-plugin-global-define/-/babel-plugin-global-define-1.0.3.tgz"
    integrity sha512-M8Sby4wRLuLr+9UB8V31knVRf/rl0xkk51A7um6hUCvVPyOvLtI0u0k1OPiMgE2d7CwmeSa33NzGpaALHPQCPg==
  
  babel-plugin-import@^1.13.6:
    version "1.13.6"
    resolved "https://registry.npmjs.org/babel-plugin-import/-/babel-plugin-import-1.13.6.tgz"
    integrity sha512-N7FYnGh0DFsvDRkAPsvFq/metVfVD7P2h1rokOPpEH4cZbdRHCW+2jbXt0nnuqowkm/xhh2ww1anIdEpfYa7ZA==
    dependencies:
      "@babel/helper-module-imports" "^7.0.0"
  
  babel-plugin-jsx-attributes-array-to-object@0.3.0:
    version "0.3.0"
    resolved "https://registry.npmjs.org/babel-plugin-jsx-attributes-array-to-object/-/babel-plugin-jsx-attributes-array-to-object-0.3.0.tgz"
    integrity sha512-XvbCsBFo/y4n2DzRtICQ60Kb3FWPIK359YsUkDPjC4UBCF/FMENKYzxarEhAD1GnrAuui5wOUvli89yqF1IzdA==
  
  babel-plugin-minify-dead-code-elimination@^0.5.2:
    version "0.5.2"
    resolved "https://registry.npmjs.org/babel-plugin-minify-dead-code-elimination/-/babel-plugin-minify-dead-code-elimination-0.5.2.tgz"
    integrity sha512-krq9Lwi0QIzyAlcNBXTL4usqUvevB4BzktdEsb8srcXC1AaYqRJiAQw6vdKdJSaXbz6snBvziGr6ch/aoRCfpA==
    dependencies:
      babel-helper-evaluate-path "^0.5.0"
      babel-helper-mark-eval-scopes "^0.4.3"
      babel-helper-remove-or-void "^0.4.3"
      lodash "^4.17.11"
  
  babel-plugin-polyfill-corejs2@^0.3.3:
    version "0.3.3"
    resolved "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.3.tgz"
    integrity sha512-8hOdmFYFSZhqg2C/JgLUQ+t52o5nirNwaWM2B9LWteozwIvM14VSwdsCAUET10qT+kmySAlseadmfeeSWFCy+Q==
    dependencies:
      "@babel/compat-data" "^7.17.7"
      "@babel/helper-define-polyfill-provider" "^0.3.3"
      semver "^6.1.1"
  
  babel-plugin-polyfill-corejs3@^0.6.0:
    version "0.6.0"
    resolved "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.6.0.tgz"
    integrity sha512-+eHqR6OPcBhJOGgsIar7xoAB1GcSwVUA3XjAd7HJNzOXT4wv6/H7KIdA/Nc60cvUlDbKApmqNvD1B1bzOt4nyA==
    dependencies:
      "@babel/helper-define-polyfill-provider" "^0.3.3"
      core-js-compat "^3.25.1"
  
  babel-plugin-polyfill-regenerator@^0.4.1:
    version "0.4.1"
    resolved "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.4.1.tgz"
    integrity sha512-NtQGmyQDXjQqQ+IzRkBVwEOz9lQ4zxAQZgoAYEtU9dJjnl1Oc98qnN7jcp+bE7O7aYzVpavXE3/VKXNzUbh7aw==
    dependencies:
      "@babel/helper-define-polyfill-provider" "^0.3.3"
  
  babel-plugin-transform-imports-api@1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/babel-plugin-transform-imports-api/-/babel-plugin-transform-imports-api-1.0.0.tgz"
    integrity sha512-EtPFtwUFwXy4HyRwXiR7dCijk2b1dd12bVs39oY8aMZUnypFEMrctGX6/YrhQzhCPFweV7NTPhc+fD4zItWzUA==
    dependencies:
      is-invalid-path "^1.0.2"
  
  babel-plugin-transform-react-jsx-to-rn-stylesheet@3.6.6:
    version "3.6.6"
    resolved "https://registry.npmjs.org/babel-plugin-transform-react-jsx-to-rn-stylesheet/-/babel-plugin-transform-react-jsx-to-rn-stylesheet-3.6.6.tgz"
    integrity sha512-nBEk5gUl3xocC1XflAw+GZhEWQZrZNiQeKMJYWG61q7f2v55JW51/jAWGym28+j5Elb5Z0DbWT6jdJiCpSmsVw==
    dependencies:
      camelize "^1.0.0"
      taro-css-to-react-native "3.6.6"
  
  babel-plugin-transform-taroapi@3.6.6:
    version "3.6.6"
    resolved "https://registry.npmjs.org/babel-plugin-transform-taroapi/-/babel-plugin-transform-taroapi-3.6.6.tgz"
    integrity sha512-vFGc2VMnu+cN2Mefwq0fyuctI8CfnbrViQJ0lEzgpFRUWGLGw/FzRqjTtSlW9BDLiEbXPqxU8DKLtxBuOahpoA==
  
  babel-preset-taro@^3.6.5:
    version "3.6.6"
    resolved "https://registry.npmjs.org/babel-preset-taro/-/babel-preset-taro-3.6.6.tgz"
    integrity sha512-Pij5os7zaZ4Bxhey00zG2Z4SD8XwGku5asWWdyFQ9h1IZG6Bl+6GKFw7kHWi28RgSf4rERmqv6Ygybl2OFIwgA==
    dependencies:
      "@babel/plugin-proposal-class-properties" "^7.14.5"
      "@babel/plugin-proposal-decorators" "^7.14.5"
      "@babel/plugin-syntax-jsx" "^7.14.5"
      "@babel/plugin-transform-runtime" "^7.14.5"
      "@babel/preset-env" "^7.14.5"
      "@babel/preset-react" "^7.14.5"
      "@babel/preset-typescript" "^7.14.5"
      "@babel/runtime" "^7.14.5"
      "@babel/runtime-corejs3" "^7.14.5"
      "@tarojs/helper" "3.6.6"
      "@tarojs/shared" "3.6.6"
      babel-plugin-dynamic-import-node "2.3.3"
      babel-plugin-global-define "1.0.3"
      babel-plugin-jsx-attributes-array-to-object "0.3.0"
      babel-plugin-minify-dead-code-elimination "^0.5.2"
      babel-plugin-transform-imports-api "1.0.0"
      babel-plugin-transform-react-jsx-to-rn-stylesheet "3.6.6"
      babel-plugin-transform-taroapi "3.6.6"
      core-js "^3.6.5"
      metro-react-native-babel-preset "^0.72.1"
      react-refresh "^0.11.0"
  
  balanced-match@^1.0.0:
    version "1.0.2"
    resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
    integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==
  
  balanced-match@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-2.0.0.tgz"
    integrity sha512-1ugUSr8BHXRnK23KfuYS+gVMC3LB8QGH9W1iGtDPsNWoQbgtXSExkBu2aDR4epiGWZOjZsj6lDl/N/AqqTC3UA==
  
  base64-js@^1.3.0, base64-js@^1.3.1:
    version "1.5.1"
    resolved "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz"
    integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==
  
  batch@0.6.1:
    version "0.6.1"
    resolved "https://registry.npmjs.org/batch/-/batch-0.6.1.tgz"
    integrity sha512-x+VAiMRL6UPkx+kudNvxTl6hB2XNNCG2r+7wixVfIYwu/2HKRXimwQyaumLjMveWvT2Hkd/cAJw+QBMfJ/EKVw==
  
  bcrypt-pbkdf@^1.0.0:
    version "1.0.2"
    resolved "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz"
    integrity sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==
    dependencies:
      tweetnacl "^0.14.3"
  
  big.js@^5.2.2:
    version "5.2.2"
    resolved "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz"
    integrity sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==
  
  binary-extensions@^2.0.0:
    version "2.2.0"
    resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.2.0.tgz"
    integrity sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==
  
  binaryextensions@^4.16.0:
    version "4.18.0"
    resolved "https://registry.npmjs.org/binaryextensions/-/binaryextensions-4.18.0.tgz"
    integrity sha512-PQu3Kyv9dM4FnwB7XGj1+HucW+ShvJzJqjuw1JkKVs1mWdwOKVcRjOi+pV9X52A0tNvrPCsPkbFFQb+wE1EAXw==
  
  bl@^1.0.0:
    version "1.2.3"
    resolved "https://registry.npmjs.org/bl/-/bl-1.2.3.tgz"
    integrity sha512-pvcNpa0UU69UT341rO6AYy4FVAIkUHuZXRIWbq+zHnsVcRzDDjIAhGuuYoi0d//cwIwtt4pkpKycWEfjdV+vww==
    dependencies:
      readable-stream "^2.3.5"
      safe-buffer "^5.1.1"
  
  bl@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz"
    integrity sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==
    dependencies:
      buffer "^5.5.0"
      inherits "^2.0.4"
      readable-stream "^3.4.0"
  
  bluebird@^3.1.1:
    version "3.7.2"
    resolved "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz"
    integrity sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==
  
  body-parser@1.20.0:
    version "1.20.0"
    resolved "https://registry.npmjs.org/body-parser/-/body-parser-1.20.0.tgz"
    integrity sha512-DfJ+q6EPcGKZD1QWUjSpqp+Q7bDQTsQIF4zfUAtZ6qk+H/3/QRhg9CEp39ss+/T2vw0+HaidC0ecJj/DRLIaKg==
    dependencies:
      bytes "3.1.2"
      content-type "~1.0.4"
      debug "2.6.9"
      depd "2.0.0"
      destroy "1.2.0"
      http-errors "2.0.0"
      iconv-lite "0.4.24"
      on-finished "2.4.1"
      qs "6.10.3"
      raw-body "2.5.1"
      type-is "~1.6.18"
      unpipe "1.0.0"
  
  bonjour@^3.5.0:
    version "3.5.0"
    resolved "https://registry.npmjs.org/bonjour/-/bonjour-3.5.0.tgz"
    integrity sha512-RaVTblr+OnEli0r/ud8InrU7D+G0y6aJhlxaLa6Pwty4+xoxboF1BsUI45tujvRpbj9dQVoglChqonGAsjEBYg==
    dependencies:
      array-flatten "^2.1.0"
      deep-equal "^1.0.1"
      dns-equal "^1.0.0"
      dns-txt "^2.0.2"
      multicast-dns "^6.0.1"
      multicast-dns-service-types "^1.1.0"
  
  boolbase@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz"
    integrity sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==
  
  boxen@^5.0.0:
    version "5.1.2"
    resolved "https://registry.npmjs.org/boxen/-/boxen-5.1.2.tgz"
    integrity sha512-9gYgQKXx+1nP8mP7CzFyaUARhg7D3n1dF/FnErWmu9l6JvGpNUN278h0aSb+QjoiKSWG+iZ3uHrcqk0qrY9RQQ==
    dependencies:
      ansi-align "^3.0.0"
      camelcase "^6.2.0"
      chalk "^4.1.0"
      cli-boxes "^2.2.1"
      string-width "^4.2.2"
      type-fest "^0.20.2"
      widest-line "^3.1.0"
      wrap-ansi "^7.0.0"
  
  brace-expansion@^1.1.7:
    version "1.1.11"
    resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
    integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
    dependencies:
      balanced-match "^1.0.0"
      concat-map "0.0.1"
  
  brace-expansion@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz"
    integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
    dependencies:
      balanced-match "^1.0.0"
  
  braces@^3.0.2, braces@~3.0.2:
    version "3.0.2"
    resolved "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz"
    integrity sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==
    dependencies:
      fill-range "^7.0.1"
  
  browserslist@^4.0.0, browserslist@^4.12.0, browserslist@^4.14.5, browserslist@^4.16.6, browserslist@^4.20.3, browserslist@^4.21.3, browserslist@^4.21.4, "browserslist@>= 4.21.0":
    version "4.21.4"
    resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.21.4.tgz"
    integrity sha512-CBHJJdDmgjl3daYjN5Cp5kbTf1mUhZoS+beLklHIvkOWscs83YAhLlF3Wsh/lciQYAcbBJgTOD44VtG31ZM4Hw==
    dependencies:
      caniuse-lite "^1.0.30001400"
      electron-to-chromium "^1.4.251"
      node-releases "^2.0.6"
      update-browserslist-db "^1.0.9"
  
  buffer-alloc-unsafe@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/buffer-alloc-unsafe/-/buffer-alloc-unsafe-1.1.0.tgz"
    integrity sha512-TEM2iMIEQdJ2yjPJoSIsldnleVaAk1oW3DBVUykyOLsEsFmEc9kn+SFFPz+gl54KQNxlDnAwCXosOS9Okx2xAg==
  
  buffer-alloc@^1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/buffer-alloc/-/buffer-alloc-1.2.0.tgz"
    integrity sha512-CFsHQgjtW1UChdXgbyJGtnm+O/uLQeZdtbDo8mfUgYXCHSM1wgrVxXm6bSyrUuErEb+4sYVGCzASBRot7zyrow==
    dependencies:
      buffer-alloc-unsafe "^1.1.0"
      buffer-fill "^1.0.0"
  
  buffer-crc32@~0.2.3:
    version "0.2.13"
    resolved "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz"
    integrity sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==
  
  buffer-fill@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/buffer-fill/-/buffer-fill-1.0.0.tgz"
    integrity sha512-T7zexNBwiiaCOGDg9xNX9PBmjrubblRkENuptryuI64URkXDFum9il/JGL8Lm8wYfAXpredVXXZz7eMHilimiQ==
  
  buffer-from@^1.0.0:
    version "1.1.2"
    resolved "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz"
    integrity sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==
  
  buffer-indexof@^1.0.0:
    version "1.1.1"
    resolved "https://registry.npmjs.org/buffer-indexof/-/buffer-indexof-1.1.1.tgz"
    integrity sha512-4/rOEg86jivtPTeOUUT61jJO1Ya1TrR/OkqCSZDyq84WJh3LuuiphBYJN+fm5xufIk4XAFcEwte/8WzC8If/1g==
  
  buffer@^5.2.1, buffer@^5.5.0:
    version "5.7.1"
    resolved "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz"
    integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
    dependencies:
      base64-js "^1.3.1"
      ieee754 "^1.1.13"
  
  builtins@^5.0.0:
    version "5.0.1"
    resolved "https://registry.npmjs.org/builtins/-/builtins-5.0.1.tgz"
    integrity sha512-qwVpFEHNfhYJIzNRBvd2C1kyo6jz3ZSMPyyuR47OPdiKWlbYnZNyDWuyR175qDnAJLiCo5fBBqPb3RiXgWlkOQ==
    dependencies:
      semver "^7.0.0"
  
  bytes@3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz"
    integrity sha512-pMhOfFDPiv9t5jjIXkHosWmkSyQbvsgEVNkz0ERHbuLh2T/7j4Mqqpz523Fe8MVY89KC6Sh/QfS2sM+SjgFDcw==
  
  bytes@3.1.2:
    version "3.1.2"
    resolved "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz"
    integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==
  
  cacheable-request@^2.1.1:
    version "2.1.4"
    resolved "https://registry.npmjs.org/cacheable-request/-/cacheable-request-2.1.4.tgz"
    integrity sha512-vag0O2LKZ/najSoUwDbVlnlCFvhBE/7mGTY2B5FgCBDcRD+oVV1HYTOwM6JZfMg/hIcM6IwnTZ1uQQL5/X3xIQ==
    dependencies:
      clone-response "1.0.2"
      get-stream "3.0.0"
      http-cache-semantics "3.8.1"
      keyv "3.0.0"
      lowercase-keys "1.0.0"
      normalize-url "2.0.1"
      responselike "1.0.2"
  
  cacheable-request@^6.0.0:
    version "6.1.0"
    resolved "https://registry.npmjs.org/cacheable-request/-/cacheable-request-6.1.0.tgz"
    integrity sha512-Oj3cAGPCqOZX7Rz64Uny2GYAZNliQSqfbePrgAQ1wKAihYmCUnraBtJtKcGR4xz7wF+LoJC+ssFZvv5BgF9Igg==
    dependencies:
      clone-response "^1.0.2"
      get-stream "^5.1.0"
      http-cache-semantics "^4.0.0"
      keyv "^3.0.0"
      lowercase-keys "^2.0.0"
      normalize-url "^4.1.0"
      responselike "^1.0.2"
  
  call-bind@^1.0.0, call-bind@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz"
    integrity sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==
    dependencies:
      function-bind "^1.1.1"
      get-intrinsic "^1.0.2"
  
  callsite-record@^4.1.3:
    version "4.1.5"
    resolved "https://registry.npmjs.org/callsite-record/-/callsite-record-4.1.5.tgz"
    integrity sha512-OqeheDucGKifjQRx524URgV4z4NaKjocGhygTptDea+DLROre4ZEecA4KXDq+P7qlGCohYVNOh3qr+y5XH5Ftg==
    dependencies:
      "@devexpress/error-stack-parser" "^2.0.6"
      "@types/lodash" "^4.14.72"
      callsite "^1.0.0"
      chalk "^2.4.0"
      highlight-es "^1.0.0"
      lodash "4.6.1 || ^4.16.1"
      pinkie-promise "^2.0.0"
  
  callsite@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/callsite/-/callsite-1.0.0.tgz"
    integrity sha512-0vdNRFXn5q+dtOqjfFtmtlI9N2eVZ7LMyEV2iKC5mEEFvSg/69Ml6b/WU2qF8W1nLRa0wiSrDT3Y5jOHZCwKPQ==
  
  callsites@^3.0.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
    integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==
  
  camel-case@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/camel-case/-/camel-case-3.0.0.tgz"
    integrity sha512-+MbKztAYHXPr1jNTSKQF52VpcFjwY5RkR7fxksV8Doo4KAYc5Fl4UJRgthBbTmEx8C54DqahhbLJkDwjI3PI/w==
    dependencies:
      no-case "^2.2.0"
      upper-case "^1.1.1"
  
  camel-case@^4.1.2:
    version "4.1.2"
    resolved "https://registry.npmjs.org/camel-case/-/camel-case-4.1.2.tgz"
    integrity sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==
    dependencies:
      pascal-case "^3.1.2"
      tslib "^2.0.3"
  
  camelcase-keys@^6.2.2:
    version "6.2.2"
    resolved "https://registry.npmjs.org/camelcase-keys/-/camelcase-keys-6.2.2.tgz"
    integrity sha512-YrwaA0vEKazPBkn0ipTiMpSajYDSe+KjQfrjhcBMxJt/znbvlHd8Pw/Vamaz5EB4Wfhs3SUR3Z9mwRu/P3s3Yg==
    dependencies:
      camelcase "^5.3.1"
      map-obj "^4.0.0"
      quick-lru "^4.0.1"
  
  camelcase@^5.3.1:
    version "5.3.1"
    resolved "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
    integrity sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==
  
  camelcase@^6.2.0:
    version "6.3.0"
    resolved "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz"
    integrity sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==
  
  camelize@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/camelize/-/camelize-1.0.1.tgz"
    integrity sha512-dU+Tx2fsypxTgtLoE36npi3UqcjSSMNYfkqgmoEhtZrraP5VWq0K7FkWVTYa8eMPtnU/G2txVsfdCJTn9uzpuQ==
  
  caniuse-api@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/caniuse-api/-/caniuse-api-3.0.0.tgz"
    integrity sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==
    dependencies:
      browserslist "^4.0.0"
      caniuse-lite "^1.0.0"
      lodash.memoize "^4.1.2"
      lodash.uniq "^4.5.0"
  
  caniuse-lite@^1.0.0, caniuse-lite@^1.0.30001109, caniuse-lite@^1.0.30001400:
    version "1.0.30001412"
    resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001412.tgz"
    integrity sha512-+TeEIee1gS5bYOiuf+PS/kp2mrXic37Hl66VY6EAfxasIk5fELTktK2oOezYed12H8w7jt3s512PpulQidPjwA==
  
  caseless@~0.12.0:
    version "0.12.0"
    resolved "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz"
    integrity sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw==
  
  caw@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/caw/-/caw-2.0.1.tgz"
    integrity sha512-Cg8/ZSBEa8ZVY9HspcGUYaK63d/bN7rqS3CYCzEGUxuYv6UlmcjzDUz2fCFFHyTvUW5Pk0I+3hkA3iXlIj6guA==
    dependencies:
      get-proxy "^2.0.0"
      isurl "^1.0.0-alpha5"
      tunnel-agent "^0.6.0"
      url-to-options "^1.0.1"
  
  chalk@^2.0.0, chalk@^2.4.0:
    version "2.4.2"
    resolved "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
    integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
    dependencies:
      ansi-styles "^3.2.1"
      escape-string-regexp "^1.0.5"
      supports-color "^5.3.0"
  
  chalk@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz"
    integrity sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==
    dependencies:
      ansi-styles "^4.1.0"
      supports-color "^7.1.0"
  
  chalk@^4.0.0:
    version "4.1.2"
    resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
    integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
    dependencies:
      ansi-styles "^4.1.0"
      supports-color "^7.1.0"
  
  chalk@^4.0.2:
    version "4.1.2"
    resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
    integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
    dependencies:
      ansi-styles "^4.1.0"
      supports-color "^7.1.0"
  
  chalk@^4.1.0:
    version "4.1.2"
    resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
    integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
    dependencies:
      ansi-styles "^4.1.0"
      supports-color "^7.1.0"
  
  chalk@^4.1.1:
    version "4.1.2"
    resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
    integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
    dependencies:
      ansi-styles "^4.1.0"
      supports-color "^7.1.0"
  
  chalk@3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz"
    integrity sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==
    dependencies:
      ansi-styles "^4.1.0"
      supports-color "^7.1.0"
  
  chardet@^0.7.0:
    version "0.7.0"
    resolved "https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz"
    integrity sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==
  
  charenc@0.0.2:
    version "0.0.2"
    resolved "https://registry.npmjs.org/charenc/-/charenc-0.0.2.tgz"
    integrity sha512-yrLQ/yVUFXkzg7EDQsPieE/53+0RlaWTs+wBrvW36cyilJ2SaDWfl4Yj7MtLTXleV9uEKefbAGUPv2/iWSooRA==
  
  chokidar@^3.3.1, chokidar@^3.5.3, "chokidar@>=3.0.0 <4.0.0":
    version "3.5.3"
    resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.5.3.tgz"
    integrity sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==
    dependencies:
      anymatch "~3.1.2"
      braces "~3.0.2"
      glob-parent "~5.1.2"
      is-binary-path "~2.1.0"
      is-glob "~4.0.1"
      normalize-path "~3.0.0"
      readdirp "~3.6.0"
    optionalDependencies:
      fsevents "~2.3.2"
  
  chrome-trace-event@^1.0.2:
    version "1.0.3"
    resolved "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz"
    integrity sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==
  
  ci-info@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/ci-info/-/ci-info-2.0.0.tgz"
    integrity sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ==
  
  classnames@^2.2.5, classnames@^2.3.1, classnames@^2.3.2:
    version "2.3.2"
    resolved "https://registry.npmjs.org/classnames/-/classnames-2.3.2.tgz"
    integrity sha512-CSbhY4cFEJRe6/GQzIk5qXZ4Jeg5pcsP7b5peFSDpffpe1cqjASH/n9UTjBwOp6XpMSTwQ8Za2K5V02ueA7Tmw==
  
  clean-css@^4.2.1:
    version "4.2.4"
    resolved "https://registry.npmjs.org/clean-css/-/clean-css-4.2.4.tgz"
    integrity sha512-EJUDT7nDVFDvaQgAo2G/PJvxmp1o/c6iXLbswsBbUFXi1Nr+AjA2cKmfbKDMjMvzEe75g3P6JkaDDAKk96A85A==
    dependencies:
      source-map "~0.6.0"
  
  clean-css@^5.2.2:
    version "5.3.1"
    resolved "https://registry.npmjs.org/clean-css/-/clean-css-5.3.1.tgz"
    integrity sha512-lCr8OHhiWCTw4v8POJovCoh4T7I9U11yVsPjMWWnnMmp9ZowCxyad1Pathle/9HjaDp+fdQKjO9fQydE6RHTZg==
    dependencies:
      source-map "~0.6.0"
  
  clean-stack@^2.0.0:
    version "2.2.0"
    resolved "https://registry.npmjs.org/clean-stack/-/clean-stack-2.2.0.tgz"
    integrity sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==
  
  cli-boxes@^2.2.1:
    version "2.2.1"
    resolved "https://registry.npmjs.org/cli-boxes/-/cli-boxes-2.2.1.tgz"
    integrity sha512-y4coMcylgSCdVinjiDBuR8PCC2bLjyGTwEmPb9NHR/QaNU6EUOXcTY/s6VjGMD6ENSEaeQYHCY0GNGS5jfMwPw==
  
  cli-cursor@^3.1.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz"
    integrity sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==
    dependencies:
      restore-cursor "^3.1.0"
  
  cli-highlight@^2.1.11:
    version "2.1.11"
    resolved "https://registry.npmjs.org/cli-highlight/-/cli-highlight-2.1.11.tgz"
    integrity sha512-9KDcoEVwyUXrjcJNvHD0NFc/hiwe/WPVYIleQh2O1N2Zro5gWJZ/K+3DGn8w8P/F6FxOgzyC5bxDyHIgCSPhGg==
    dependencies:
      chalk "^4.0.0"
      highlight.js "^10.7.1"
      mz "^2.4.0"
      parse5 "^5.1.1"
      parse5-htmlparser2-tree-adapter "^6.0.0"
      yargs "^16.0.0"
  
  cli-spinners@^2.5.0:
    version "2.7.0"
    resolved "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.7.0.tgz"
    integrity sha512-qu3pN8Y3qHNgE2AFweciB1IfMnmZ/fsNTEE+NOFjmGB2F/7rLhnhzppvpCnN4FovtP26k8lHyy9ptEbNwWFLzw==
  
  cli-width@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/cli-width/-/cli-width-3.0.0.tgz"
    integrity sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==
  
  cliui@^7.0.2:
    version "7.0.4"
    resolved "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz"
    integrity sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==
    dependencies:
      string-width "^4.2.0"
      strip-ansi "^6.0.0"
      wrap-ansi "^7.0.0"
  
  clone-buffer@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/clone-buffer/-/clone-buffer-1.0.0.tgz"
    integrity sha512-KLLTJWrvwIP+OPfMn0x2PheDEP20RPUcGXj/ERegTgdmPEZylALQldygiqrPPu8P45uNuPs7ckmReLY6v/iA5g==
  
  clone-deep@^4.0.1:
    version "4.0.1"
    resolved "https://registry.npmjs.org/clone-deep/-/clone-deep-4.0.1.tgz"
    integrity sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==
    dependencies:
      is-plain-object "^2.0.4"
      kind-of "^6.0.2"
      shallow-clone "^3.0.0"
  
  clone-response@^1.0.2, clone-response@1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/clone-response/-/clone-response-1.0.2.tgz"
    integrity sha512-yjLXh88P599UOyPTFX0POsd7WxnbsVsGohcwzHOLspIhhpalPw1BcqED8NblyZLKcGrL8dTgMlcaZxV2jAD41Q==
    dependencies:
      mimic-response "^1.0.0"
  
  clone-stats@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/clone-stats/-/clone-stats-1.0.0.tgz"
    integrity sha512-au6ydSpg6nsrigcZ4m8Bc9hxjeW+GJ8xh5G3BJCMt4WXe1H10UNaVOamqQTmrx1kjVuxAHIQSNU6hY4Nsn9/ag==
  
  clone@^1.0.2:
    version "1.0.4"
    resolved "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz"
    integrity sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==
  
  clone@^2.1.1:
    version "2.1.2"
    resolved "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz"
    integrity sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==
  
  cloneable-readable@^1.0.0:
    version "1.1.3"
    resolved "https://registry.npmjs.org/cloneable-readable/-/cloneable-readable-1.1.3.tgz"
    integrity sha512-2EF8zTQOxYq70Y4XKtorQupqF0m49MBz2/yf5Bj+MHjvpG3Hy7sImifnqD6UA+TKYxeSV+u6qqQPawN5UvnpKQ==
    dependencies:
      inherits "^2.0.1"
      process-nextick-args "^2.0.0"
      readable-stream "^2.3.5"
  
  co@^4.6.0:
    version "4.6.0"
    resolved "https://registry.npmjs.org/co/-/co-4.6.0.tgz"
    integrity sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==
  
  color-convert@^1.9.0:
    version "1.9.3"
    resolved "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
    integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
    dependencies:
      color-name "1.1.3"
  
  color-convert@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
    integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
    dependencies:
      color-name "~1.1.4"
  
  color-name@~1.1.4:
    version "1.1.4"
    resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
    integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==
  
  color-name@1.1.3:
    version "1.1.3"
    resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
    integrity sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==
  
  colord@^2.9.1, colord@^2.9.3:
    version "2.9.3"
    resolved "https://registry.npmjs.org/colord/-/colord-2.9.3.tgz"
    integrity sha512-jeC1axXpnb0/2nn/Y1LPuLdgXBLH7aDcHu4KEKfqw3CUhX7ZpfBSlPKyqXE6btIgEzfWtrX3/tyBCaCvXvMkOw==
  
  colorette@^2.0.10:
    version "2.0.19"
    resolved "https://registry.npmjs.org/colorette/-/colorette-2.0.19.tgz"
    integrity sha512-3tlv/dIP7FWvj3BsbHrGLJ6l/oKh1O3TcgBqMn+yyCagOxc23fyzDS6HypQbgxWbkpDnf52p1LuR4eWDQ/K9WQ==
  
  combined-stream@^1.0.6, combined-stream@^1.0.8, combined-stream@~1.0.6:
    version "1.0.8"
    resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
    integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
    dependencies:
      delayed-stream "~1.0.0"
  
  commander@^2.19.0, commander@^2.20.0, commander@^2.8.1:
    version "2.20.3"
    resolved "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
    integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==
  
  commander@^4.0.1:
    version "4.1.1"
    resolved "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz"
    integrity sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==
  
  commander@^7.2.0:
    version "7.2.0"
    resolved "https://registry.npmjs.org/commander/-/commander-7.2.0.tgz"
    integrity sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==
  
  commander@^8.3.0:
    version "8.3.0"
    resolved "https://registry.npmjs.org/commander/-/commander-8.3.0.tgz"
    integrity sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==
  
  common-path-prefix@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/common-path-prefix/-/common-path-prefix-3.0.0.tgz"
    integrity sha512-QE33hToZseCH3jS0qN96O/bSh3kaw/h+Tq7ngyY9eWDUnTlTNUyqfqvCXioLe5Na5jFsL78ra/wuBU4iuEgd4w==
  
  commondir@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz"
    integrity sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg==
  
  compressible@~2.0.16:
    version "2.0.18"
    resolved "https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz"
    integrity sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==
    dependencies:
      mime-db ">= 1.43.0 < 2"
  
  compression@^1.7.4:
    version "1.7.4"
    resolved "https://registry.npmjs.org/compression/-/compression-1.7.4.tgz"
    integrity sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==
    dependencies:
      accepts "~1.3.5"
      bytes "3.0.0"
      compressible "~2.0.16"
      debug "2.6.9"
      on-headers "~1.0.2"
      safe-buffer "5.1.2"
      vary "~1.1.2"
  
  concat-map@0.0.1:
    version "0.0.1"
    resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
    integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==
  
  config-chain@^1.1.11:
    version "1.1.13"
    resolved "https://registry.npmjs.org/config-chain/-/config-chain-1.1.13.tgz"
    integrity sha512-qj+f8APARXHrM0hraqXYb2/bOVSV4PvJQlNZ/DVj0QrmNM2q2euizkeuVckQ57J+W0mRH6Hvi+k50M4Jul2VRQ==
    dependencies:
      ini "^1.3.4"
      proto-list "~1.2.1"
  
  configstore@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmjs.org/configstore/-/configstore-5.0.1.tgz"
    integrity sha512-aMKprgk5YhBNyH25hj8wGt2+D52Sw1DRRIzqBwLp2Ya9mFmY8KPvvtvmna8SxVR9JMZ4kzMD68N22vlaRpkeFA==
    dependencies:
      dot-prop "^5.2.0"
      graceful-fs "^4.1.2"
      make-dir "^3.0.0"
      unique-string "^2.0.0"
      write-file-atomic "^3.0.0"
      xdg-basedir "^4.0.0"
  
  connect-history-api-fallback@^1.6.0:
    version "1.6.0"
    resolved "https://registry.npmjs.org/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz"
    integrity sha512-e54B99q/OUoH64zYYRf3HBP5z24G38h5D3qXu23JGRoigpX5Ss4r9ZnDk3g0Z8uQC2x2lPaJ+UlWBc1ZWBWdLg==
  
  consola@^2.15.3:
    version "2.15.3"
    resolved "https://registry.npmjs.org/consola/-/consola-2.15.3.tgz"
    integrity sha512-9vAdYbHj6x2fLKC4+oPH0kFzY/orMZyG2Aj+kNylHxKGJ/Ed4dpNyAQYwJOdqO4zdM7XpVHmyejQDcQHrnuXbw==
  
  consolidate@^0.15.1:
    version "0.15.1"
    resolved "https://registry.npmjs.org/consolidate/-/consolidate-0.15.1.tgz"
    integrity sha512-DW46nrsMJgy9kqAbPt5rKaCr7uFtpo4mSUvLHIUbJEjm0vo+aY5QLwBUq3FK4tRnJr/X0Psc0C4jf/h+HtXSMw==
    dependencies:
      bluebird "^3.1.1"
  
  content-disposition@^0.5.2, content-disposition@0.5.4:
    version "0.5.4"
    resolved "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz"
    integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
    dependencies:
      safe-buffer "5.2.1"
  
  content-type@~1.0.4:
    version "1.0.4"
    resolved "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz"
    integrity sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==
  
  convert-source-map@^1.7.0:
    version "1.8.0"
    resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.8.0.tgz"
    integrity sha512-+OQdjP49zViI/6i7nIJpA8rAl4sV/JdPfU9nZs3VqOwGIgizICvuN2ru6fMd+4llL0tar18UYJXfZ/TWtmhUjA==
    dependencies:
      safe-buffer "~5.1.1"
  
  cookie-signature@1.0.6:
    version "1.0.6"
    resolved "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz"
    integrity sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==
  
  cookie@0.5.0:
    version "0.5.0"
    resolved "https://registry.npmjs.org/cookie/-/cookie-0.5.0.tgz"
    integrity sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==
  
  copy-anything@^2.0.1:
    version "2.0.6"
    resolved "https://registry.npmjs.org/copy-anything/-/copy-anything-2.0.6.tgz"
    integrity sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==
    dependencies:
      is-what "^3.14.1"
  
  copy-webpack-plugin@10.2.0:
    version "10.2.0"
    resolved "https://registry.npmjs.org/copy-webpack-plugin/-/copy-webpack-plugin-10.2.0.tgz"
    integrity sha512-my6iXII95c78w14HzYCNya5TlJYa44lOppAge5GSTMM1SyDxNsVGCJvhP4/ld6snm8lzjn3XOonMZD6s1L86Og==
    dependencies:
      fast-glob "^3.2.7"
      glob-parent "^6.0.1"
      globby "^12.0.2"
      normalize-path "^3.0.0"
      schema-utils "^4.0.0"
      serialize-javascript "^6.0.0"
  
  core-js-compat@^3.25.1:
    version "3.25.3"
    resolved "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.25.3.tgz"
    integrity sha512-xVtYpJQ5grszDHEUU9O7XbjjcZ0ccX3LgQsyqSvTnjX97ZqEgn9F5srmrwwwMtbKzDllyFPL+O+2OFMl1lU4TQ==
    dependencies:
      browserslist "^4.21.4"
  
  core-js-pure@^3.23.3, core-js-pure@^3.25.1:
    version "3.25.3"
    resolved "https://registry.npmjs.org/core-js-pure/-/core-js-pure-3.25.3.tgz"
    integrity sha512-T/7qvgv70MEvRkZ8p6BasLZmOVYKzOaWNBEHAU8FmveCJkl4nko2quqPQOmy6AJIp5MBanhz9no3A94NoRb0XA==
  
  core-js@^3.6.5:
    version "3.25.3"
    resolved "https://registry.npmjs.org/core-js/-/core-js-3.25.3.tgz"
    integrity sha512-y1hvKXmPHvm5B7w4ln1S4uc9eV/O5+iFExSRUimnvIph11uaizFR8LFMdONN8hG3P2pipUfX4Y/fR8rAEtcHcQ==
  
  core-util-is@~1.0.0:
    version "1.0.3"
    resolved "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz"
    integrity sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==
  
  core-util-is@1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz"
    integrity sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==
  
  cosmiconfig@^7.0.0:
    version "7.1.0"
    resolved "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz"
    integrity sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==
    dependencies:
      "@types/parse-json" "^4.0.0"
      import-fresh "^3.2.1"
      parse-json "^5.0.0"
      path-type "^4.0.0"
      yaml "^1.10.0"
  
  cosmiconfig@^8.1.3:
    version "8.1.3"
    resolved "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.1.3.tgz"
    integrity sha512-/UkO2JKI18b5jVMJUp0lvKFMpa/Gye+ZgZjKD+DGEN9y7NRcf/nK1A0sp67ONmKtnDCNMS44E6jrk0Yc3bDuUw==
    dependencies:
      import-fresh "^3.2.1"
      js-yaml "^4.1.0"
      parse-json "^5.0.0"
      path-type "^4.0.0"
  
  cross-spawn@^7.0.2, cross-spawn@^7.0.3:
    version "7.0.3"
    resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
    integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
    dependencies:
      path-key "^3.1.0"
      shebang-command "^2.0.0"
      which "^2.0.1"
  
  crypt@0.0.2:
    version "0.0.2"
    resolved "https://registry.npmjs.org/crypt/-/crypt-0.0.2.tgz"
    integrity sha512-mCxBlsHFYh9C+HVpiEacem8FEBnMXgU9gy4zmNC+SXAZNB/1idgp/aulFJ4FgCi7GPEVbfyng092GqL2k2rmow==
  
  crypto-random-string@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/crypto-random-string/-/crypto-random-string-2.0.0.tgz"
    integrity sha512-v1plID3y9r/lPhviJ1wrXpLeyUIGAZ2SHNYTEapm7/8A9nLPoyvVp3RK/EPFqn5kEznyWgYZNsRtYYIWbuG8KA==
  
  css-color-keywords@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/css-color-keywords/-/css-color-keywords-1.0.0.tgz"
    integrity sha512-FyyrDHZKEjXDpNJYvVsV960FiqQyXc/LlYmsxl2BcdMb2WPx0OGRVgTg55rPSyLSNMqP52R9r8geSp7apN3Ofg==
  
  css-declaration-sorter@^6.3.0:
    version "6.3.1"
    resolved "https://registry.npmjs.org/css-declaration-sorter/-/css-declaration-sorter-6.3.1.tgz"
    integrity sha512-fBffmak0bPAnyqc/HO8C3n2sHrp9wcqQz6ES9koRF2/mLOVAx9zIQ3Y7R29sYCteTPqMCwns4WYQoCX91Xl3+w==
  
  css-functions-list@^3.1.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/css-functions-list/-/css-functions-list-3.1.0.tgz"
    integrity sha512-/9lCvYZaUbBGvYUgYGFJ4dcYiyqdhSjG7IPVluoV8A1ILjkF7ilmhp1OGUz8n+nmBcu0RNrQAzgD8B6FJbrt2w==
  
  css-loader@*, css-loader@^6.7.1:
    version "6.7.3"
    resolved "https://registry.npmjs.org/css-loader/-/css-loader-6.7.3.tgz"
    integrity sha512-qhOH1KlBMnZP8FzRO6YCH9UHXQhVMcEGLyNdb7Hv2cpcmJbW0YrddO+tG1ab5nT41KpHIYGsbeHqxB9xPu1pKQ==
    dependencies:
      icss-utils "^5.1.0"
      postcss "^8.4.19"
      postcss-modules-extract-imports "^3.0.0"
      postcss-modules-local-by-default "^4.0.0"
      postcss-modules-scope "^3.0.0"
      postcss-modules-values "^4.0.0"
      postcss-value-parser "^4.2.0"
      semver "^7.3.8"
  
  css-mediaquery@^0.1.2:
    version "0.1.2"
    resolved "https://registry.npmjs.org/css-mediaquery/-/css-mediaquery-0.1.2.tgz"
    integrity sha512-COtn4EROW5dBGlE/4PiKnh6rZpAPxDeFLaEEwt4i10jpDMFt2EhQGS79QmmrO+iKCHv0PU/HrOWEhijFd1x99Q==
  
  css-minimizer-webpack-plugin@3.4.1:
    version "3.4.1"
    resolved "https://registry.npmjs.org/css-minimizer-webpack-plugin/-/css-minimizer-webpack-plugin-3.4.1.tgz"
    integrity sha512-1u6D71zeIfgngN2XNRJefc/hY7Ybsxd74Jm4qngIXyUEk7fss3VUzuHxLAq/R8NAba4QU9OUSaMZlbpRc7bM4Q==
    dependencies:
      cssnano "^5.0.6"
      jest-worker "^27.0.2"
      postcss "^8.3.5"
      schema-utils "^4.0.0"
      serialize-javascript "^6.0.0"
      source-map "^0.6.1"
  
  css-select@^4.1.3:
    version "4.3.0"
    resolved "https://registry.npmjs.org/css-select/-/css-select-4.3.0.tgz"
    integrity sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==
    dependencies:
      boolbase "^1.0.0"
      css-what "^6.0.1"
      domhandler "^4.3.1"
      domutils "^2.8.0"
      nth-check "^2.0.1"
  
  css-tree@^1.1.2, css-tree@^1.1.3:
    version "1.1.3"
    resolved "https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz"
    integrity sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==
    dependencies:
      mdn-data "2.0.14"
      source-map "^0.6.1"
  
  css-tree@^2.3.1:
    version "2.3.1"
    resolved "https://registry.npmjs.org/css-tree/-/css-tree-2.3.1.tgz"
    integrity sha512-6Fv1DV/TYw//QF5IzQdqsNDjx/wc8TrMBZsqjL9eW01tWb7R7k/mq+/VXfJCl7SoD5emsJop9cOByJZfs8hYIw==
    dependencies:
      mdn-data "2.0.30"
      source-map-js "^1.0.1"
  
  css-tree@~2.2.0:
    version "2.2.1"
    resolved "https://registry.npmjs.org/css-tree/-/css-tree-2.2.1.tgz"
    integrity sha512-OA0mILzGc1kCOCSJerOeqDxDQ4HOh+G8NbOJFOTgOCzpw7fCBubk0fEyxp8AgOL/jvLgYA/uV0cMbe43ElF1JA==
    dependencies:
      mdn-data "2.0.28"
      source-map-js "^1.0.1"
  
  css-tree@1.0.0-alpha.29:
    version "1.0.0-alpha.29"
    resolved "https://registry.npmjs.org/css-tree/-/css-tree-1.0.0-alpha.29.tgz"
    integrity sha512-sRNb1XydwkW9IOci6iB2xmy8IGCj6r/fr+JWitvJ2JxQRPzN3T4AGGVWCMlVmVwM1gtgALJRmGIlWv5ppnGGkg==
    dependencies:
      mdn-data "~1.1.0"
      source-map "^0.5.3"
  
  css-what@^6.0.1:
    version "6.1.0"
    resolved "https://registry.npmjs.org/css-what/-/css-what-6.1.0.tgz"
    integrity sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==
  
  css@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/css/-/css-3.0.0.tgz"
    integrity sha512-DG9pFfwOrzc+hawpmqX/dHYHJG+Bsdb0klhyi1sDneOgGOXy9wQIC8hzyVp1e4NRYDBdxcylvywPkkXCHAzTyQ==
    dependencies:
      inherits "^2.0.4"
      source-map "^0.6.1"
      source-map-resolve "^0.6.0"
  
  cssesc@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
    integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==
  
  cssnano-preset-default@^5.2.12:
    version "5.2.12"
    resolved "https://registry.npmjs.org/cssnano-preset-default/-/cssnano-preset-default-5.2.12.tgz"
    integrity sha512-OyCBTZi+PXgylz9HAA5kHyoYhfGcYdwFmyaJzWnzxuGRtnMw/kR6ilW9XzlzlRAtB6PLT/r+prYgkef7hngFew==
    dependencies:
      css-declaration-sorter "^6.3.0"
      cssnano-utils "^3.1.0"
      postcss-calc "^8.2.3"
      postcss-colormin "^5.3.0"
      postcss-convert-values "^5.1.2"
      postcss-discard-comments "^5.1.2"
      postcss-discard-duplicates "^5.1.0"
      postcss-discard-empty "^5.1.1"
      postcss-discard-overridden "^5.1.0"
      postcss-merge-longhand "^5.1.6"
      postcss-merge-rules "^5.1.2"
      postcss-minify-font-values "^5.1.0"
      postcss-minify-gradients "^5.1.1"
      postcss-minify-params "^5.1.3"
      postcss-minify-selectors "^5.2.1"
      postcss-normalize-charset "^5.1.0"
      postcss-normalize-display-values "^5.1.0"
      postcss-normalize-positions "^5.1.1"
      postcss-normalize-repeat-style "^5.1.1"
      postcss-normalize-string "^5.1.0"
      postcss-normalize-timing-functions "^5.1.0"
      postcss-normalize-unicode "^5.1.0"
      postcss-normalize-url "^5.1.0"
      postcss-normalize-whitespace "^5.1.1"
      postcss-ordered-values "^5.1.3"
      postcss-reduce-initial "^5.1.0"
      postcss-reduce-transforms "^5.1.0"
      postcss-svgo "^5.1.0"
      postcss-unique-selectors "^5.1.1"
  
  cssnano-utils@^3.1.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/cssnano-utils/-/cssnano-utils-3.1.0.tgz"
    integrity sha512-JQNR19/YZhz4psLX/rQ9M83e3z2Wf/HdJbryzte4a3NSuafyp9w/I4U+hx5C2S9g41qlstH7DEWnZaaj83OuEA==
  
  cssnano@^5.0.6:
    version "5.1.13"
    resolved "https://registry.npmjs.org/cssnano/-/cssnano-5.1.13.tgz"
    integrity sha512-S2SL2ekdEz6w6a2epXn4CmMKU4K3KpcyXLKfAYc9UQQqJRkD/2eLUG0vJ3Db/9OvO5GuAdgXw3pFbR6abqghDQ==
    dependencies:
      cssnano-preset-default "^5.2.12"
      lilconfig "^2.0.3"
      yaml "^1.10.2"
  
  csso@^3.5.1:
    version "3.5.1"
    resolved "https://registry.npmjs.org/csso/-/csso-3.5.1.tgz"
    integrity sha512-vrqULLffYU1Q2tLdJvaCYbONStnfkfimRxXNaGjxMldI0C7JPBC4rB1RyjhfdZ4m1frm8pM9uRPKH3d2knZ8gg==
    dependencies:
      css-tree "1.0.0-alpha.29"
  
  csso@^4.2.0:
    version "4.2.0"
    resolved "https://registry.npmjs.org/csso/-/csso-4.2.0.tgz"
    integrity sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA==
    dependencies:
      css-tree "^1.1.2"
  
  csso@^5.0.2:
    version "5.0.5"
    resolved "https://registry.npmjs.org/csso/-/csso-5.0.5.tgz"
    integrity sha512-0LrrStPOdJj+SPCCrGhzryycLjwcgUSHBtxNA8aIDxf0GLsRh1cKYhB00Gd1lDOS4yGH69+SNn13+TWbVHETFQ==
    dependencies:
      css-tree "~2.2.0"
  
  cssstyle@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/cssstyle/-/cssstyle-3.0.0.tgz"
    integrity sha512-N4u2ABATi3Qplzf0hWbVCdjenim8F3ojEXpBDF5hBpjzW182MjNGLqfmQ0SkSPeQ+V86ZXgeH8aXj6kayd4jgg==
    dependencies:
      rrweb-cssom "^0.6.0"
  
  csstype@^3.0.2:
    version "3.1.1"
    resolved "https://registry.npmjs.org/csstype/-/csstype-3.1.1.tgz"
    integrity sha512-DJR/VvkAvSZW9bTouZue2sSxDwdTN92uHjqeKVm+0dAqdfNykRzQ95tay8aXMBAAPpUiq4Qcug2L7neoRh2Egw==
  
  cuint@^0.2.2:
    version "0.2.2"
    resolved "https://registry.npmjs.org/cuint/-/cuint-0.2.2.tgz"
    integrity sha512-d4ZVpCW31eWwCMe1YT3ur7mUDnTXbgwyzaL320DrcRT45rfjYxkt5QWLrmOJ+/UEAI2+fQgKe/fCjR8l4TpRgw==
  
  dashdash@^1.12.0:
    version "1.14.1"
    resolved "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz"
    integrity sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==
    dependencies:
      assert-plus "^1.0.0"
  
  data-urls@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/data-urls/-/data-urls-4.0.0.tgz"
    integrity sha512-/mMTei/JXPqvFqQtfyTowxmJVwr2PVAeCcDxyFf6LhoOu/09TX2OX3kb2wzi4DMXcfj4OItwDOnhl5oziPnT6g==
    dependencies:
      abab "^2.0.6"
      whatwg-mimetype "^3.0.0"
      whatwg-url "^12.0.0"
  
  debug@^3.2.6:
    version "3.2.7"
    resolved "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
    integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
    dependencies:
      ms "^2.1.1"
  
  debug@^3.2.7:
    version "3.2.7"
    resolved "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
    integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
    dependencies:
      ms "^2.1.1"
  
  debug@^4.1.0, debug@^4.1.1, debug@^4.2.0, debug@^4.3.2, debug@^4.3.4, debug@4, debug@4.3.4:
    version "4.3.4"
    resolved "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz"
    integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
    dependencies:
      ms "2.1.2"
  
  debug@~3.1.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz"
    integrity sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==
    dependencies:
      ms "2.0.0"
  
  debug@2.6.9:
    version "2.6.9"
    resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
    integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
    dependencies:
      ms "2.0.0"
  
  decamelize-keys@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/decamelize-keys/-/decamelize-keys-1.1.0.tgz"
    integrity sha512-ocLWuYzRPoS9bfiSdDd3cxvrzovVMZnRDVEzAs+hWIVXGDbHxWMECij2OBuyB/An0FFW/nLuq6Kv1i/YC5Qfzg==
    dependencies:
      decamelize "^1.1.0"
      map-obj "^1.0.0"
  
  decamelize@^1.1.0, decamelize@^1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz"
    integrity sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==
  
  decimal.js@^10.4.3:
    version "10.4.3"
    resolved "https://registry.npmjs.org/decimal.js/-/decimal.js-10.4.3.tgz"
    integrity sha512-VBBaLc1MgL5XpzgIP7ny5Z6Nx3UrRkIViUkPUdtl9aya5amy3De1gsUUSB1g3+3sExYNjCAsAznmukyxCb1GRA==
  
  decode-uri-component@^0.2.0, decode-uri-component@^0.2.2:
    version "0.2.2"
    resolved "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.2.tgz"
    integrity sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ==
  
  decompress-response@^3.3.0:
    version "3.3.0"
    resolved "https://registry.npmjs.org/decompress-response/-/decompress-response-3.3.0.tgz"
    integrity sha512-BzRPQuY1ip+qDonAOz42gRm/pg9F768C+npV/4JOsxRC2sq+Rlk+Q4ZCAsOhnIaMrgarILY+RMUIvMmmX1qAEA==
    dependencies:
      mimic-response "^1.0.0"
  
  decompress-tar@^4.0.0, decompress-tar@^4.1.0, decompress-tar@^4.1.1:
    version "4.1.1"
    resolved "https://registry.npmjs.org/decompress-tar/-/decompress-tar-4.1.1.tgz"
    integrity sha512-JdJMaCrGpB5fESVyxwpCx4Jdj2AagLmv3y58Qy4GE6HMVjWz1FeVQk1Ct4Kye7PftcdOo/7U7UKzYBJgqnGeUQ==
    dependencies:
      file-type "^5.2.0"
      is-stream "^1.1.0"
      tar-stream "^1.5.2"
  
  decompress-tarbz2@^4.0.0:
    version "4.1.1"
    resolved "https://registry.npmjs.org/decompress-tarbz2/-/decompress-tarbz2-4.1.1.tgz"
    integrity sha512-s88xLzf1r81ICXLAVQVzaN6ZmX4A6U4z2nMbOwobxkLoIIfjVMBg7TeguTUXkKeXni795B6y5rnvDw7rxhAq9A==
    dependencies:
      decompress-tar "^4.1.0"
      file-type "^6.1.0"
      is-stream "^1.1.0"
      seek-bzip "^1.0.5"
      unbzip2-stream "^1.0.9"
  
  decompress-targz@^4.0.0:
    version "4.1.1"
    resolved "https://registry.npmjs.org/decompress-targz/-/decompress-targz-4.1.1.tgz"
    integrity sha512-4z81Znfr6chWnRDNfFNqLwPvm4db3WuZkqV+UgXQzSngG3CEKdBkw5jrv3axjjL96glyiiKjsxJG3X6WBZwX3w==
    dependencies:
      decompress-tar "^4.1.1"
      file-type "^5.2.0"
      is-stream "^1.1.0"
  
  decompress-unzip@^4.0.1:
    version "4.0.1"
    resolved "https://registry.npmjs.org/decompress-unzip/-/decompress-unzip-4.0.1.tgz"
    integrity sha512-1fqeluvxgnn86MOh66u8FjbtJpAFv5wgCT9Iw8rcBqQcCo5tO8eiJw7NNTrvt9n4CRBVq7CstiS922oPgyGLrw==
    dependencies:
      file-type "^3.8.0"
      get-stream "^2.2.0"
      pify "^2.3.0"
      yauzl "^2.4.2"
  
  decompress@^4.2.0:
    version "4.2.1"
    resolved "https://registry.npmjs.org/decompress/-/decompress-4.2.1.tgz"
    integrity sha512-e48kc2IjU+2Zw8cTb6VZcJQ3lgVbS4uuB1TfCHbiZIP/haNXm+SVyhu+87jts5/3ROpd82GSVCoNs/z8l4ZOaQ==
    dependencies:
      decompress-tar "^4.0.0"
      decompress-tarbz2 "^4.0.0"
      decompress-targz "^4.0.0"
      decompress-unzip "^4.0.1"
      graceful-fs "^4.1.10"
      make-dir "^1.0.0"
      pify "^2.3.0"
      strip-dirs "^2.0.0"
  
  deep-equal@^1.0.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.1.tgz"
    integrity sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g==
    dependencies:
      is-arguments "^1.0.4"
      is-date-object "^1.0.1"
      is-regex "^1.0.4"
      object-is "^1.0.1"
      object-keys "^1.1.1"
      regexp.prototype.flags "^1.2.0"
  
  deep-extend@^0.6.0:
    version "0.6.0"
    resolved "https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz"
    integrity sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==
  
  deep-is@^0.1.3, deep-is@~0.1.3:
    version "0.1.4"
    resolved "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
    integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==
  
  deepmerge@^1.5.2:
    version "1.5.2"
    resolved "https://registry.npmjs.org/deepmerge/-/deepmerge-1.5.2.tgz"
    integrity sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ==
  
  default-gateway@^6.0.3:
    version "6.0.3"
    resolved "https://registry.npmjs.org/default-gateway/-/default-gateway-6.0.3.tgz"
    integrity sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg==
    dependencies:
      execa "^5.0.0"
  
  defaults@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/defaults/-/defaults-1.0.3.tgz"
    integrity sha512-s82itHOnYrN0Ib8r+z7laQz3sdE+4FP3d9Q7VLO7U+KRT+CR0GsWuyHxzdAY82I7cXv0G/twrqomTJLOssO5HA==
    dependencies:
      clone "^1.0.2"
  
  defer-to-connect@^1.0.1:
    version "1.1.3"
    resolved "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-1.1.3.tgz"
    integrity sha512-0ISdNousHvZT2EiFlZeZAHBUvSxmKswVCEf8hW7KWgG4a8MVEu/3Vb6uWYozkjylyCxe0JBIiRB1jV45S70WVQ==
  
  define-lazy-prop@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz"
    integrity sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==
  
  define-properties@^1.1.3, define-properties@^1.1.4:
    version "1.1.4"
    resolved "https://registry.npmjs.org/define-properties/-/define-properties-1.1.4.tgz"
    integrity sha512-uckOqKcfaVvtBdsVkdPv3XjveQJsNQqmhXgRi8uhvWWuPYZCNlzT8qAyblUgNoXdHdjMTzAqeGjAoli8f+bzPA==
    dependencies:
      has-property-descriptors "^1.0.0"
      object-keys "^1.1.1"
  
  del@^6.0.0:
    version "6.1.1"
    resolved "https://registry.npmjs.org/del/-/del-6.1.1.tgz"
    integrity sha512-ua8BhapfP0JUJKC/zV9yHHDW/rDoDxP4Zhn3AkA6/xT6gY7jYXJiaeyBZznYVujhZZET+UgcbZiQ7sN3WqcImg==
    dependencies:
      globby "^11.0.1"
      graceful-fs "^4.2.4"
      is-glob "^4.0.1"
      is-path-cwd "^2.2.0"
      is-path-inside "^3.0.2"
      p-map "^4.0.0"
      rimraf "^3.0.2"
      slash "^3.0.0"
  
  delayed-stream@~1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
    integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==
  
  depcheck@^1.3.1:
    version "1.4.3"
    resolved "https://registry.npmjs.org/depcheck/-/depcheck-1.4.3.tgz"
    integrity sha512-vy8xe1tlLFu7t4jFyoirMmOR7x7N601ubU9Gkifyr9z8rjBFtEdWHDBMqXyk6OkK+94NXutzddVXJuo0JlUQKQ==
    dependencies:
      "@babel/parser" "7.16.4"
      "@babel/traverse" "^7.12.5"
      "@vue/compiler-sfc" "^3.0.5"
      camelcase "^6.2.0"
      cosmiconfig "^7.0.0"
      debug "^4.2.0"
      deps-regex "^0.1.4"
      ignore "^5.1.8"
      is-core-module "^2.4.0"
      js-yaml "^3.14.0"
      json5 "^2.1.3"
      lodash "^4.17.20"
      minimatch "^3.0.4"
      multimatch "^5.0.0"
      please-upgrade-node "^3.2.0"
      query-ast "^1.0.3"
      readdirp "^3.5.0"
      require-package-name "^2.0.1"
      resolve "^1.18.1"
      sass "^1.29.0"
      scss-parser "^1.0.4"
      semver "^7.3.2"
      yargs "^16.1.0"
  
  depd@~1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz"
    integrity sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==
  
  depd@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
    integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==
  
  deps-regex@^0.1.4:
    version "0.1.4"
    resolved "https://registry.npmjs.org/deps-regex/-/deps-regex-0.1.4.tgz"
    integrity sha512-3tzwGYogSJi8HoG93R5x9NrdefZQOXgHgGih/7eivloOq6yC6O+yoFxZnkgP661twvfILONfoKRdF9GQOGx2RA==
  
  destroy@1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz"
    integrity sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==
  
  detect-libc@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz"
    integrity sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==
  
  detect-node@^2.0.4:
    version "2.1.0"
    resolved "https://registry.npmjs.org/detect-node/-/detect-node-2.1.0.tgz"
    integrity sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==
  
  detect-port@^1.3.0:
    version "1.5.1"
    resolved "https://registry.npmjs.org/detect-port/-/detect-port-1.5.1.tgz"
    integrity sha512-aBzdj76lueB6uUst5iAs7+0H/oOjqI5D16XUWxlWMIMROhcM0rfsNVk93zTngq1dDNpoXRr++Sus7ETAExppAQ==
    dependencies:
      address "^1.0.1"
      debug "4"
  
  dingtalk-jsapi@~2.15.2:
    version "2.15.4"
    resolved "https://registry.npmjs.org/dingtalk-jsapi/-/dingtalk-jsapi-2.15.4.tgz"
    integrity sha512-pPETqUhLJYKNZIewnS9hU1/QqcdRbP2Q9sHySimko0C2nm/n9NoakVLtcKwdqgORq1dkPIP/jqv7RnESxhA2bA==
    dependencies:
      promise-polyfill "^7.1.0"
  
  dir-glob@^3.0.1:
    version "3.0.1"
    resolved "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz"
    integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
    dependencies:
      path-type "^4.0.0"
  
  dns-equal@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/dns-equal/-/dns-equal-1.0.0.tgz"
    integrity sha512-z+paD6YUQsk+AbGCEM4PrOXSss5gd66QfcVBFTKR/HpFL9jCqikS94HYwKww6fQyO7IxrIIyUu+g0Ka9tUS2Cg==
  
  dns-packet@^1.3.1:
    version "1.3.4"
    resolved "https://registry.npmjs.org/dns-packet/-/dns-packet-1.3.4.tgz"
    integrity sha512-BQ6F4vycLXBvdrJZ6S3gZewt6rcrks9KBgM9vrhW+knGRqc8uEdT7fuCwloc7nny5xNoMJ17HGH0R/6fpo8ECA==
    dependencies:
      ip "^1.1.0"
      safe-buffer "^5.0.1"
  
  dns-txt@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmjs.org/dns-txt/-/dns-txt-2.0.2.tgz"
    integrity sha512-Ix5PrWjphuSoUXV/Zv5gaFHjnaJtb02F2+Si3Ht9dyJ87+Z/lMmy+dpNHtTGraNK958ndXq2i+GLkWsWHcKaBQ==
    dependencies:
      buffer-indexof "^1.0.0"
  
  doctrine@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
    integrity sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==
    dependencies:
      esutils "^2.0.2"
  
  doctrine@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
    integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
    dependencies:
      esutils "^2.0.2"
  
  dom-converter@^0.2.0:
    version "0.2.0"
    resolved "https://registry.npmjs.org/dom-converter/-/dom-converter-0.2.0.tgz"
    integrity sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==
    dependencies:
      utila "~0.4"
  
  dom-helpers@^5.0.1:
    version "5.2.1"
    resolved "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz"
    integrity sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==
    dependencies:
      "@babel/runtime" "^7.8.7"
      csstype "^3.0.2"
  
  dom-serializer@^1.0.1:
    version "1.4.1"
    resolved "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.4.1.tgz"
    integrity sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==
    dependencies:
      domelementtype "^2.0.1"
      domhandler "^4.2.0"
      entities "^2.0.0"
  
  dom7@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/dom7/-/dom7-3.0.0.tgz"
    integrity sha512-oNlcUdHsC4zb7Msx7JN3K0Nro1dzJ48knvBOnDPKJ2GV9wl1i5vydJZUSyOfrkKFDZEud/jBsTk92S/VGSAe/g==
    dependencies:
      ssr-window "^3.0.0-alpha.1"
  
  domelementtype@^2.0.1, domelementtype@^2.2.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz"
    integrity sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==
  
  domexception@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/domexception/-/domexception-4.0.0.tgz"
    integrity sha512-A2is4PLG+eeSfoTMA95/s4pvAoSo2mKtiM5jlHkAVewmiO8ISFTFKZjH7UAM1Atli/OT/7JHOrJRJiMKUZKYBw==
    dependencies:
      webidl-conversions "^7.0.0"
  
  domhandler@^4.0.0, domhandler@^4.2.0, domhandler@^4.3.1:
    version "4.3.1"
    resolved "https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz"
    integrity sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==
    dependencies:
      domelementtype "^2.2.0"
  
  domutils@^2.5.2, domutils@^2.8.0:
    version "2.8.0"
    resolved "https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz"
    integrity sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==
    dependencies:
      dom-serializer "^1.0.1"
      domelementtype "^2.2.0"
      domhandler "^4.2.0"
  
  dot-case@^3.0.4:
    version "3.0.4"
    resolved "https://registry.npmjs.org/dot-case/-/dot-case-3.0.4.tgz"
    integrity sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==
    dependencies:
      no-case "^3.0.4"
      tslib "^2.0.3"
  
  dot-prop@^5.2.0:
    version "5.3.0"
    resolved "https://registry.npmjs.org/dot-prop/-/dot-prop-5.3.0.tgz"
    integrity sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q==
    dependencies:
      is-obj "^2.0.0"
  
  dotenv-expand@^9.0.0:
    version "9.0.0"
    resolved "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-9.0.0.tgz"
    integrity sha512-uW8Hrhp5ammm9x7kBLR6jDfujgaDarNA02tprvZdyrJ7MpdzD1KyrIHG4l+YoC2fJ2UcdFdNWNWIjt+sexBHJw==
  
  dotenv@^16.0.3:
    version "16.0.3"
    resolved "https://registry.npmjs.org/dotenv/-/dotenv-16.0.3.tgz"
    integrity sha512-7GO6HghkA5fYG9TYnNxi14/7K9f5occMlp3zXAuSxn7CKCxt9xbNWG7yF8hTCSUchlfWSe3uLmlPfigevRItzQ==
  
  download-git-repo@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/download-git-repo/-/download-git-repo-2.0.0.tgz"
    integrity sha512-al8ZOwpm/DvCd7XC8PupeuNlC2TrvsMxW3FOx1bCbHNBhP1lYjOn9KnPqnZ3o/jz1vxCC5NHGJA7LT+GYMLcHA==
    dependencies:
      download "^7.1.0"
      git-clone "^0.1.0"
      rimraf "^2.6.3"
  
  download@^7.1.0:
    version "7.1.0"
    resolved "https://registry.npmjs.org/download/-/download-7.1.0.tgz"
    integrity sha512-xqnBTVd/E+GxJVrX5/eUJiLYjCGPwMpdL+jGhGU57BvtcA7wwhtHVbXBeUk51kOpW3S7Jn3BQbN9Q1R1Km2qDQ==
    dependencies:
      archive-type "^4.0.0"
      caw "^2.0.1"
      content-disposition "^0.5.2"
      decompress "^4.2.0"
      ext-name "^5.0.0"
      file-type "^8.1.0"
      filenamify "^2.0.0"
      get-stream "^3.0.0"
      got "^8.3.1"
      make-dir "^1.2.0"
      p-event "^2.1.0"
      pify "^3.0.0"
  
  duplexer3@^0.1.4:
    version "0.1.5"
    resolved "https://registry.npmjs.org/duplexer3/-/duplexer3-0.1.5.tgz"
    integrity sha512-1A8za6ws41LQgv9HrE/66jyC5yuSjQ3L/KOpFtoBilsAK2iA2wuS5rTt1OCzIvtS2V7nVmedsUU+DGRcjBmOYA==
  
  ecc-jsbn@~0.1.1:
    version "0.1.2"
    resolved "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz"
    integrity sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==
    dependencies:
      jsbn "~0.1.0"
      safer-buffer "^2.1.0"
  
  ee-first@1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
    integrity sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==
  
  ejs@^3.1.8:
    version "3.1.9"
    resolved "https://registry.npmjs.org/ejs/-/ejs-3.1.9.tgz"
    integrity sha512-rC+QVNMJWv+MtPgkt0y+0rVEIdbtxVADApW9JXrUVlzHetgcyczP/E7DJmWJ4fJCZF2cPcBk0laWO9ZHMG3DmQ==
    dependencies:
      jake "^10.8.5"
  
  electron-to-chromium@^1.4.251:
    version "1.4.265"
    resolved "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.265.tgz"
    integrity sha512-38KaYBNs0oCzWCpr6j7fY/W9vF0vSp4tKFIshQTgdZMhUpkxgotkQgjJP6iGMdmlsgMs3i0/Hkko4UXLTrkYVQ==
  
  emoji-regex@^8.0.0:
    version "8.0.0"
    resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
    integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==
  
  emojis-list@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz"
    integrity sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==
  
  encodeurl@~1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
    integrity sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==
  
  end-of-stream@^1.0.0, end-of-stream@^1.1.0:
    version "1.4.4"
    resolved "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz"
    integrity sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
    dependencies:
      once "^1.4.0"
  
  enhanced-resolve@^5.13.0, enhanced-resolve@^5.9.3:
    version "5.14.0"
    resolved "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.14.0.tgz"
    integrity sha512-+DCows0XNwLDcUhbFJPdlQEVnT2zXlCv7hPxemTz86/O+B/hCQ+mb7ydkPKiflpVraqLPCAfu7lDy+hBXueojw==
    dependencies:
      graceful-fs "^4.2.4"
      tapable "^2.2.0"
  
  entities@^2.0.0:
    version "2.2.0"
    resolved "https://registry.npmjs.org/entities/-/entities-2.2.0.tgz"
    integrity sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==
  
  entities@^4.4.0:
    version "4.4.0"
    resolved "https://registry.npmjs.org/entities/-/entities-4.4.0.tgz"
    integrity sha512-oYp7156SP8LkeGD0GF85ad1X9Ai79WtRsZ2gxJqtBuzH+98YUV6jkHEKlZkMbcrjJjIVJNIDP/3WL9wQkoPbWA==
  
  envinfo@^7.8.1:
    version "7.8.1"
    resolved "https://registry.npmjs.org/envinfo/-/envinfo-7.8.1.tgz"
    integrity sha512-/o+BXHmB7ocbHEAs6F2EnG0ogybVVUdkRunTT2glZU9XAaGmhqskrvKwqXuDfNjEO0LZKWdejEEpnq8aM0tOaw==
  
  errno@^0.1.1:
    version "0.1.8"
    resolved "https://registry.npmjs.org/errno/-/errno-0.1.8.tgz"
    integrity sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==
    dependencies:
      prr "~1.0.1"
  
  error-ex@^1.3.1:
    version "1.3.2"
    resolved "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
    integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
    dependencies:
      is-arrayish "^0.2.1"
  
  error-stack-parser@^2.0.6:
    version "2.1.4"
    resolved "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.1.4.tgz"
    integrity sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==
    dependencies:
      stackframe "^1.3.4"
  
  es-abstract@^1.19.0, es-abstract@^1.20.4:
    version "1.21.2"
    resolved "https://registry.npmjs.org/es-abstract/-/es-abstract-1.21.2.tgz"
    integrity sha512-y/B5POM2iBnIxCiernH1G7rC9qQoM77lLIMQLuob0zhp8C56Po81+2Nj0WFKnd0pNReDTnkYryc+zhOzpEIROg==
    dependencies:
      array-buffer-byte-length "^1.0.0"
      available-typed-arrays "^1.0.5"
      call-bind "^1.0.2"
      es-set-tostringtag "^2.0.1"
      es-to-primitive "^1.2.1"
      function.prototype.name "^1.1.5"
      get-intrinsic "^1.2.0"
      get-symbol-description "^1.0.0"
      globalthis "^1.0.3"
      gopd "^1.0.1"
      has "^1.0.3"
      has-property-descriptors "^1.0.0"
      has-proto "^1.0.1"
      has-symbols "^1.0.3"
      internal-slot "^1.0.5"
      is-array-buffer "^3.0.2"
      is-callable "^1.2.7"
      is-negative-zero "^2.0.2"
      is-regex "^1.1.4"
      is-shared-array-buffer "^1.0.2"
      is-string "^1.0.7"
      is-typed-array "^1.1.10"
      is-weakref "^1.0.2"
      object-inspect "^1.12.3"
      object-keys "^1.1.1"
      object.assign "^4.1.4"
      regexp.prototype.flags "^1.4.3"
      safe-regex-test "^1.0.0"
      string.prototype.trim "^1.2.7"
      string.prototype.trimend "^1.0.6"
      string.prototype.trimstart "^1.0.6"
      typed-array-length "^1.0.4"
      unbox-primitive "^1.0.2"
      which-typed-array "^1.1.9"
  
  es-module-lexer@^0.10.4:
    version "0.10.5"
    resolved "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.10.5.tgz"
    integrity sha512-+7IwY/kiGAacQfY+YBhKMvEmyAJnw5grTUgjG85Pe7vcUI/6b7pZjZG8nQ7+48YhzEAEqrEgD2dCz/JIK+AYvw==
  
  es-module-lexer@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.2.1.tgz"
    integrity sha512-9978wrXM50Y4rTMmW5kXIC09ZdXQZqkE4mxhwkd8VbzsGkXGPgV4zWuqQJgCEzYngdo2dYDa0l8xhX4fkSwJSg==
  
  es-set-tostringtag@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.1.tgz"
    integrity sha512-g3OMbtlwY3QewlqAiMLI47KywjWZoEytKr8pf6iTC8uJq5bIAH52Z9pnQ8pVL6whrCto53JZDuUIsifGeLorTg==
    dependencies:
      get-intrinsic "^1.1.3"
      has "^1.0.3"
      has-tostringtag "^1.0.0"
  
  es-shim-unscopables@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.0.0.tgz"
    integrity sha512-Jm6GPcCdC30eMLbZ2x8z2WuRwAws3zTBBKuusffYVUrNj/GVSUAZ+xKMaUpfNDR5IbyNA5LJbaecoUVbmUcB1w==
    dependencies:
      has "^1.0.3"
  
  es-to-primitive@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
    integrity sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==
    dependencies:
      is-callable "^1.1.4"
      is-date-object "^1.0.1"
      is-symbol "^1.0.2"
  
  esbuild-darwin-arm64@0.14.54:
    version "0.14.54"
    resolved "https://registry.npmjs.org/esbuild-darwin-arm64/-/esbuild-darwin-arm64-0.14.54.tgz"
    integrity sha512-OPafJHD2oUPyvJMrsCvDGkRrVCar5aVyHfWGQzY1dWnzErjrDuSETxwA2HSsyg2jORLY8yBfzc1MIpUkXlctmw==
  
  esbuild-loader@2.18.0:
    version "2.18.0"
    resolved "https://registry.npmjs.org/esbuild-loader/-/esbuild-loader-2.18.0.tgz"
    integrity sha512-AKqxM3bI+gvGPV8o6NAhR+cBxVO8+dh+O0OXBHIXXwuSGumckbPWHzZ17subjBGI2YEGyJ1STH7Haj8aCrwL/w==
    dependencies:
      esbuild "^0.14.6"
      joycon "^3.0.1"
      json5 "^2.2.0"
      loader-utils "^2.0.0"
      tapable "^2.2.0"
      webpack-sources "^2.2.0"
  
  esbuild@^0.14.27, esbuild@^0.14.6:
    version "0.14.54"
    resolved "https://registry.npmjs.org/esbuild/-/esbuild-0.14.54.tgz"
    integrity sha512-Cy9llcy8DvET5uznocPyqL3BFRrFXSVqbgpMJ9Wz8oVjZlh/zUSNbPRbov0VX7VxN2JH1Oa0uNxZ7eLRb62pJA==
    optionalDependencies:
      "@esbuild/linux-loong64" "0.14.54"
      esbuild-android-64 "0.14.54"
      esbuild-android-arm64 "0.14.54"
      esbuild-darwin-64 "0.14.54"
      esbuild-darwin-arm64 "0.14.54"
      esbuild-freebsd-64 "0.14.54"
      esbuild-freebsd-arm64 "0.14.54"
      esbuild-linux-32 "0.14.54"
      esbuild-linux-64 "0.14.54"
      esbuild-linux-arm "0.14.54"
      esbuild-linux-arm64 "0.14.54"
      esbuild-linux-mips64le "0.14.54"
      esbuild-linux-ppc64le "0.14.54"
      esbuild-linux-riscv64 "0.14.54"
      esbuild-linux-s390x "0.14.54"
      esbuild-netbsd-64 "0.14.54"
      esbuild-openbsd-64 "0.14.54"
      esbuild-sunos-64 "0.14.54"
      esbuild-windows-32 "0.14.54"
      esbuild-windows-64 "0.14.54"
      esbuild-windows-arm64 "0.14.54"
  
  escalade@^3.1.1:
    version "3.1.1"
    resolved "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz"
    integrity sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==
  
  escape-goat@^2.0.0:
    version "2.1.1"
    resolved "https://registry.npmjs.org/escape-goat/-/escape-goat-2.1.1.tgz"
    integrity sha512-8/uIhbG12Csjy2JEW7D9pHbreaVaS/OpN3ycnyvElTdwM5n6GY6W6e2IPemfvGZeUMqZ9A/3GqIZMgKnBhAw/Q==
  
  escape-html@~1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
    integrity sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==
  
  escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
    integrity sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==
  
  escape-string-regexp@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
    integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==
  
  escodegen@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/escodegen/-/escodegen-2.0.0.tgz"
    integrity sha512-mmHKys/C8BFUGI+MAWNcSYoORYLMdPzjrknd2Vc+bUsjN5bXcr8EhrNB+UTqfL1y3I9c4fw2ihgtMPQLBRiQxw==
    dependencies:
      esprima "^4.0.1"
      estraverse "^5.2.0"
      esutils "^2.0.2"
      optionator "^0.8.1"
    optionalDependencies:
      source-map "~0.6.1"
  
  eslint-config-taro@^3.6.5:
    version "3.6.6"
    resolved "https://registry.npmjs.org/eslint-config-taro/-/eslint-config-taro-3.6.6.tgz"
    integrity sha512-T4fMrLFgG0zmLteRQV8MdXKqbGE451LU3n9016n7TrbGmySpBYSVrOMa+Po+FVZI+6uA2M2Gobq7RQV5omArDw==
    dependencies:
      "@babel/eslint-parser" "^7.17.0"
      "@typescript-eslint/parser" "^5.20.0"
  
  eslint-import-resolver-node@^0.3.7:
    version "0.3.7"
    resolved "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.7.tgz"
    integrity sha512-gozW2blMLJCeFpBwugLTGyvVjNoeo1knonXAcatC6bjPBZitotxdWf7Gimr25N4c0AAOo4eOUfaG82IJPDpqCA==
    dependencies:
      debug "^3.2.7"
      is-core-module "^2.11.0"
      resolve "^1.22.1"
  
  eslint-module-utils@^2.7.4:
    version "2.7.4"
    resolved "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.7.4.tgz"
    integrity sha512-j4GT+rqzCoRKHwURX7pddtIPGySnX9Si/cgMI5ztrcqOPtk5dDEeZ34CQVPphnqkJytlc97Vuk05Um2mJ3gEQA==
    dependencies:
      debug "^3.2.7"
  
  eslint-plugin-import@^2.27.5:
    version "2.27.5"
    resolved "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.27.5.tgz"
    integrity sha512-LmEt3GVofgiGuiE+ORpnvP+kAm3h6MLZJ4Q5HCyHADofsb4VzXFsRiWj3c0OFiV+3DWFh0qg3v9gcPlfc3zRow==
    dependencies:
      array-includes "^3.1.6"
      array.prototype.flat "^1.3.1"
      array.prototype.flatmap "^1.3.1"
      debug "^3.2.7"
      doctrine "^2.1.0"
      eslint-import-resolver-node "^0.3.7"
      eslint-module-utils "^2.7.4"
      has "^1.0.3"
      is-core-module "^2.11.0"
      is-glob "^4.0.3"
      minimatch "^3.1.2"
      object.values "^1.1.6"
      resolve "^1.22.1"
      semver "^6.3.0"
      tsconfig-paths "^3.14.1"
  
  eslint-plugin-react-hooks@^4.2.0:
    version "4.6.0"
    resolved "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.6.0.tgz"
    integrity sha512-oFc7Itz9Qxh2x4gNHStv3BqJq54ExXmfC+a1NjAta66IAN87Wu0R/QArgIS9qKzX3dXKPI9H5crl9QchNMY9+g==
  
  eslint-plugin-react@^7.32.2:
    version "7.32.2"
    resolved "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.32.2.tgz"
    integrity sha512-t2fBMa+XzonrrNkyVirzKlvn5RXzzPwRHtMvLAtVZrt8oxgnTQaYbU6SXTOO1mwQgp1y5+toMSKInnzGr0Knqg==
    dependencies:
      array-includes "^3.1.6"
      array.prototype.flatmap "^1.3.1"
      array.prototype.tosorted "^1.1.1"
      doctrine "^2.1.0"
      estraverse "^5.3.0"
      jsx-ast-utils "^2.4.1 || ^3.0.0"
      minimatch "^3.1.2"
      object.entries "^1.1.6"
      object.fromentries "^2.0.6"
      object.hasown "^1.1.2"
      object.values "^1.1.6"
      prop-types "^15.8.1"
      resolve "^2.0.0-next.4"
      semver "^6.3.0"
      string.prototype.matchall "^4.0.8"
  
  eslint-scope@^5.1.1, eslint-scope@5.1.1:
    version "5.1.1"
    resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
    integrity sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==
    dependencies:
      esrecurse "^4.3.0"
      estraverse "^4.1.1"
  
  eslint-scope@^7.2.0:
    version "7.2.0"
    resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.0.tgz"
    integrity sha512-DYj5deGlHBfMt15J7rdtyKNq/Nqlv5KfU4iodrQ019XESsRnwXH9KAE0y3cwtUHDo2ob7CypAnCqefh6vioWRw==
    dependencies:
      esrecurse "^4.3.0"
      estraverse "^5.2.0"
  
  eslint-visitor-keys@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
    integrity sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==
  
  eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1:
    version "3.4.1"
    resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.1.tgz"
    integrity sha512-pZnmmLwYzf+kWaM/Qgrvpen51upAktaaiI01nsJD/Yr3lMOdNtq0cxkrrg16w64VtisN6okbs7Q8AfGqj4c9fA==
  
  eslint@*, "eslint@^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8", "eslint@^3 || ^4 || ^5 || ^6 || ^7 || ^8", "eslint@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0", "eslint@^6.0.0 || ^7.0.0 || ^8.0.0", "eslint@^6.0.0 || ^7.0.0 || >=8.0.0", "eslint@^7.5.0 || ^8.0.0", eslint@^8.12.0, eslint@^8.40.0:
    version "8.41.0"
    resolved "https://registry.npmjs.org/eslint/-/eslint-8.41.0.tgz"
    integrity sha512-WQDQpzGBOP5IrXPo4Hc0814r4/v2rrIsB0rhT7jtunIalgg6gYXWhRMOejVO8yH21T/FGaxjmFjBMNqcIlmH1Q==
    dependencies:
      "@eslint-community/eslint-utils" "^4.2.0"
      "@eslint-community/regexpp" "^4.4.0"
      "@eslint/eslintrc" "^2.0.3"
      "@eslint/js" "8.41.0"
      "@humanwhocodes/config-array" "^0.11.8"
      "@humanwhocodes/module-importer" "^1.0.1"
      "@nodelib/fs.walk" "^1.2.8"
      ajv "^6.10.0"
      chalk "^4.0.0"
      cross-spawn "^7.0.2"
      debug "^4.3.2"
      doctrine "^3.0.0"
      escape-string-regexp "^4.0.0"
      eslint-scope "^7.2.0"
      eslint-visitor-keys "^3.4.1"
      espree "^9.5.2"
      esquery "^1.4.2"
      esutils "^2.0.2"
      fast-deep-equal "^3.1.3"
      file-entry-cache "^6.0.1"
      find-up "^5.0.0"
      glob-parent "^6.0.2"
      globals "^13.19.0"
      graphemer "^1.4.0"
      ignore "^5.2.0"
      import-fresh "^3.0.0"
      imurmurhash "^0.1.4"
      is-glob "^4.0.0"
      is-path-inside "^3.0.3"
      js-yaml "^4.1.0"
      json-stable-stringify-without-jsonify "^1.0.1"
      levn "^0.4.1"
      lodash.merge "^4.6.2"
      minimatch "^3.1.2"
      natural-compare "^1.4.0"
      optionator "^0.9.1"
      strip-ansi "^6.0.1"
      strip-json-comments "^3.1.0"
      text-table "^0.2.0"
  
  espree@^9.5.2:
    version "9.5.2"
    resolved "https://registry.npmjs.org/espree/-/espree-9.5.2.tgz"
    integrity sha512-7OASN1Wma5fum5SrNhFMAMJxOUAbhyfQ8dQ//PJaJbNw0URTPWqIghHWt1MmAANKhHZIYOHruW4Kw4ruUWOdGw==
    dependencies:
      acorn "^8.8.0"
      acorn-jsx "^5.3.2"
      eslint-visitor-keys "^3.4.1"
  
  esprima@^4.0.0, esprima@^4.0.1:
    version "4.0.1"
    resolved "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
    integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==
  
  esquery@^1.4.2:
    version "1.5.0"
    resolved "https://registry.npmjs.org/esquery/-/esquery-1.5.0.tgz"
    integrity sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==
    dependencies:
      estraverse "^5.1.0"
  
  esrecurse@^4.3.0:
    version "4.3.0"
    resolved "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
    integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
    dependencies:
      estraverse "^5.2.0"
  
  estraverse@^4.1.1:
    version "4.3.0"
    resolved "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
    integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==
  
  estraverse@^5.1.0:
    version "5.3.0"
    resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
    integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==
  
  estraverse@^5.2.0:
    version "5.3.0"
    resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
    integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==
  
  estraverse@^5.3.0:
    version "5.3.0"
    resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
    integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==
  
  estree-walker@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz"
    integrity sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==
  
  esutils@^2.0.2:
    version "2.0.3"
    resolved "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
    integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==
  
  etag@~1.8.1:
    version "1.8.1"
    resolved "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
    integrity sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==
  
  eventemitter3@^4.0.0:
    version "4.0.7"
    resolved "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz"
    integrity sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==
  
  events@^3.2.0:
    version "3.3.0"
    resolved "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
    integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==
  
  execa@^5.0.0:
    version "5.1.1"
    resolved "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz"
    integrity sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==
    dependencies:
      cross-spawn "^7.0.3"
      get-stream "^6.0.0"
      human-signals "^2.1.0"
      is-stream "^2.0.0"
      merge-stream "^2.0.0"
      npm-run-path "^4.0.1"
      onetime "^5.1.2"
      signal-exit "^3.0.3"
      strip-final-newline "^2.0.0"
  
  expr-parser@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/expr-parser/-/expr-parser-1.0.0.tgz"
    integrity sha512-ncuWTCWH0M5KbaYikXxZ3FG3Q+FTYIEXeXAbxYscdZLFNnR5Le5gRU2r/a/JUZHnxwBDZcxWEWzCoPQlW9Engg==
  
  express@^4.17.1:
    version "4.18.1"
    resolved "https://registry.npmjs.org/express/-/express-4.18.1.tgz"
    integrity sha512-zZBcOX9TfehHQhtupq57OF8lFZ3UZi08Y97dwFCkD8p9d/d2Y3M+ykKcwaMDEL+4qyUolgBDX6AblpR3fL212Q==
    dependencies:
      accepts "~1.3.8"
      array-flatten "1.1.1"
      body-parser "1.20.0"
      content-disposition "0.5.4"
      content-type "~1.0.4"
      cookie "0.5.0"
      cookie-signature "1.0.6"
      debug "2.6.9"
      depd "2.0.0"
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      etag "~1.8.1"
      finalhandler "1.2.0"
      fresh "0.5.2"
      http-errors "2.0.0"
      merge-descriptors "1.0.1"
      methods "~1.1.2"
      on-finished "2.4.1"
      parseurl "~1.3.3"
      path-to-regexp "0.1.7"
      proxy-addr "~2.0.7"
      qs "6.10.3"
      range-parser "~1.2.1"
      safe-buffer "5.2.1"
      send "0.18.0"
      serve-static "1.15.0"
      setprototypeof "1.2.0"
      statuses "2.0.1"
      type-is "~1.6.18"
      utils-merge "1.0.1"
      vary "~1.1.2"
  
  ext-list@^2.0.0:
    version "2.2.2"
    resolved "https://registry.npmjs.org/ext-list/-/ext-list-2.2.2.tgz"
    integrity sha512-u+SQgsubraE6zItfVA0tBuCBhfU9ogSRnsvygI7wht9TS510oLkBRXBsqopeUG/GBOIQyKZO9wjTqIu/sf5zFA==
    dependencies:
      mime-db "^1.28.0"
  
  ext-name@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/ext-name/-/ext-name-5.0.0.tgz"
    integrity sha512-yblEwXAbGv1VQDmow7s38W77hzAgJAO50ztBLMcUyUBfxv1HC+LGwtiEN+Co6LtlqT/5uwVOxsD4TNIilWhwdQ==
    dependencies:
      ext-list "^2.0.0"
      sort-keys-length "^1.0.0"
  
  extend@~3.0.2:
    version "3.0.2"
    resolved "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
    integrity sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==
  
  external-editor@^3.0.3:
    version "3.1.0"
    resolved "https://registry.npmjs.org/external-editor/-/external-editor-3.1.0.tgz"
    integrity sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==
    dependencies:
      chardet "^0.7.0"
      iconv-lite "^0.4.24"
      tmp "^0.0.33"
  
  extsprintf@^1.2.0:
    version "1.4.1"
    resolved "https://registry.npmjs.org/extsprintf/-/extsprintf-1.4.1.tgz"
    integrity sha512-Wrk35e8ydCKDj/ArClo1VrPVmN8zph5V4AtHwIuHhvMXsKf73UT3BOD+azBIW+3wOJ4FhEH7zyaJCFvChjYvMA==
  
  extsprintf@1.3.0:
    version "1.3.0"
    resolved "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz"
    integrity sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==
  
  fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
    version "3.1.3"
    resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
    integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==
  
  fast-glob@^3.2.12, fast-glob@^3.2.7, fast-glob@^3.2.9:
    version "3.2.12"
    resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.12.tgz"
    integrity sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w==
    dependencies:
      "@nodelib/fs.stat" "^2.0.2"
      "@nodelib/fs.walk" "^1.2.3"
      glob-parent "^5.1.2"
      merge2 "^1.3.0"
      micromatch "^4.0.4"
  
  fast-json-stable-stringify@^2.0.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
    integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==
  
  fast-levenshtein@^2.0.6, fast-levenshtein@~2.0.6:
    version "2.0.6"
    resolved "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
    integrity sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==
  
  fastest-levenshtein@^1.0.16:
    version "1.0.16"
    resolved "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz"
    integrity sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==
  
  fastq@^1.6.0:
    version "1.13.0"
    resolved "https://registry.npmjs.org/fastq/-/fastq-1.13.0.tgz"
    integrity sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw==
    dependencies:
      reusify "^1.0.4"
  
  faye-websocket@^0.11.3:
    version "0.11.4"
    resolved "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.4.tgz"
    integrity sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==
    dependencies:
      websocket-driver ">=0.5.1"
  
  fd-slicer@~1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.1.0.tgz"
    integrity sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g==
    dependencies:
      pend "~1.2.0"
  
  figures@^3.0.0:
    version "3.2.0"
    resolved "https://registry.npmjs.org/figures/-/figures-3.2.0.tgz"
    integrity sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==
    dependencies:
      escape-string-regexp "^1.0.5"
  
  file-entry-cache@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
    integrity sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
    dependencies:
      flat-cache "^3.0.4"
  
  file-loader@*, file-loader@6.0.0:
    version "6.0.0"
    resolved "https://registry.npmjs.org/file-loader/-/file-loader-6.0.0.tgz"
    integrity sha512-/aMOAYEFXDdjG0wytpTL5YQLfZnnTmLNjn+AIrJ/6HVnTfDqLsVKUUwkDf4I4kgex36BvjuXEn/TX9B/1ESyqQ==
    dependencies:
      loader-utils "^2.0.0"
      schema-utils "^2.6.5"
  
  file-type@^3.8.0:
    version "3.9.0"
    resolved "https://registry.npmjs.org/file-type/-/file-type-3.9.0.tgz"
    integrity sha512-RLoqTXE8/vPmMuTI88DAzhMYC99I8BWv7zYP4A1puo5HIjEJ5EX48ighy4ZyKMG9EDXxBgW6e++cn7d1xuFghA==
  
  file-type@^4.2.0:
    version "4.4.0"
    resolved "https://registry.npmjs.org/file-type/-/file-type-4.4.0.tgz"
    integrity sha512-f2UbFQEk7LXgWpi5ntcO86OeA/cC80fuDDDaX/fZ2ZGel+AF7leRQqBBW1eJNiiQkrZlAoM6P+VYP5P6bOlDEQ==
  
  file-type@^5.2.0:
    version "5.2.0"
    resolved "https://registry.npmjs.org/file-type/-/file-type-5.2.0.tgz"
    integrity sha512-Iq1nJ6D2+yIO4c8HHg4fyVb8mAJieo1Oloy1mLLaB2PvezNedhBVm+QU7g0qM42aiMbRXTxKKwGD17rjKNJYVQ==
  
  file-type@^6.1.0:
    version "6.2.0"
    resolved "https://registry.npmjs.org/file-type/-/file-type-6.2.0.tgz"
    integrity sha512-YPcTBDV+2Tm0VqjybVd32MHdlEGAtuxS3VAYsumFokDSMG+ROT5wawGlnHDoz7bfMcMDt9hxuXvXwoKUx2fkOg==
  
  file-type@^8.1.0:
    version "8.1.0"
    resolved "https://registry.npmjs.org/file-type/-/file-type-8.1.0.tgz"
    integrity sha512-qyQ0pzAy78gVoJsmYeNgl8uH8yKhr1lVhW7JbzJmnlRi0I4R2eEDEJZVKG8agpDnLpacwNbDhLNG/LMdxHD2YQ==
  
  filelist@^1.0.1:
    version "1.0.4"
    resolved "https://registry.npmjs.org/filelist/-/filelist-1.0.4.tgz"
    integrity sha512-w1cEuf3S+DrLCQL7ET6kz+gmlJdbq9J7yXCSjK/OZCPA+qEN1WyF4ZAf0YYJa4/shHJra2t/d/r8SV4Ji+x+8Q==
    dependencies:
      minimatch "^5.0.1"
  
  filename-reserved-regex@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/filename-reserved-regex/-/filename-reserved-regex-2.0.0.tgz"
    integrity sha512-lc1bnsSr4L4Bdif8Xb/qrtokGbq5zlsms/CYH8PP+WtCkGNF65DPiQY8vG3SakEdRn8Dlnm+gW/qWKKjS5sZzQ==
  
  filenamify@^2.0.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/filenamify/-/filenamify-2.1.0.tgz"
    integrity sha512-ICw7NTT6RsDp2rnYKVd8Fu4cr6ITzGy3+u4vUujPkabyaz+03F24NWEX7fs5fp+kBonlaqPH8fAO2NM+SXt/JA==
    dependencies:
      filename-reserved-regex "^2.0.0"
      strip-outer "^1.0.0"
      trim-repeated "^1.0.0"
  
  fill-range@^7.0.1:
    version "7.0.1"
    resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz"
    integrity sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==
    dependencies:
      to-regex-range "^5.0.1"
  
  filter-obj@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/filter-obj/-/filter-obj-1.1.0.tgz"
    integrity sha512-8rXg1ZnX7xzy2NGDVkBVaAy+lSlPNwad13BtgSlLuxfIslyt5Vg64U7tFcCt4WS1R0hvtnQybT/IyCkGZ3DpXQ==
  
  finalhandler@1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.0.tgz"
    integrity sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==
    dependencies:
      debug "2.6.9"
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      on-finished "2.4.1"
      parseurl "~1.3.3"
      statuses "2.0.1"
      unpipe "~1.0.0"
  
  find-cache-dir@^2.0.0, find-cache-dir@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-2.1.0.tgz"
    integrity sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ==
    dependencies:
      commondir "^1.0.1"
      make-dir "^2.0.0"
      pkg-dir "^3.0.0"
  
  find-up@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/find-up/-/find-up-3.0.0.tgz"
    integrity sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==
    dependencies:
      locate-path "^3.0.0"
  
  find-up@^4.0.0:
    version "4.1.0"
    resolved "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
    integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
    dependencies:
      locate-path "^5.0.0"
      path-exists "^4.0.0"
  
  find-up@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
    integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
    dependencies:
      locate-path "^5.0.0"
      path-exists "^4.0.0"
  
  find-up@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz"
    integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
    dependencies:
      locate-path "^6.0.0"
      path-exists "^4.0.0"
  
  find-yarn-workspace-root@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/find-yarn-workspace-root/-/find-yarn-workspace-root-2.0.0.tgz"
    integrity sha512-1IMnbjt4KzsQfnhnzNd8wUEgXZ44IzZaZmnLYx7D5FZlaHt2gW20Cri8Q+E/t5tIj4+epTBub+2Zxu/vNILzqQ==
    dependencies:
      micromatch "^4.0.2"
  
  find-yarn-workspace-root2@1.2.16:
    version "1.2.16"
    resolved "https://registry.npmjs.org/find-yarn-workspace-root2/-/find-yarn-workspace-root2-1.2.16.tgz"
    integrity sha512-hr6hb1w8ePMpPVUK39S4RlwJzi+xPLuVuG8XlwXU3KD5Yn3qgBWVfy3AzNlDhWvE1EORCE65/Qm26rFQt3VLVA==
    dependencies:
      micromatch "^4.0.2"
      pkg-dir "^4.2.0"
  
  first-chunk-stream@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/first-chunk-stream/-/first-chunk-stream-2.0.0.tgz"
    integrity sha512-X8Z+b/0L4lToKYq+lwnKqi9X/Zek0NibLpsJgVsSxpoYq7JtiCtRb5HqKVEjEw/qAb/4AKKRLOwwKHlWNpm2Eg==
    dependencies:
      readable-stream "^2.0.2"
  
  flat-cache@^3.0.4:
    version "3.0.4"
    resolved "https://registry.npmjs.org/flat-cache/-/flat-cache-3.0.4.tgz"
    integrity sha512-dm9s5Pw7Jc0GvMYbshN6zchCA9RgQlzzEZX3vylR9IqFfS8XciblUXOKfW6SiuJ0e13eDYZoZV5wdrev7P3Nwg==
    dependencies:
      flatted "^3.1.0"
      rimraf "^3.0.2"
  
  flatted@^3.1.0:
    version "3.2.7"
    resolved "https://registry.npmjs.org/flatted/-/flatted-3.2.7.tgz"
    integrity sha512-5nqDSxl8nn5BSNxyR3n4I6eDmbolI6WT+QqR547RwxQapgjQBmtktdP+HTBb/a/zLsbzERTONyUB5pefh5TtjQ==
  
  follow-redirects@^1.0.0:
    version "1.15.2"
    resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.2.tgz"
    integrity sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==
  
  for-each@^0.3.3:
    version "0.3.3"
    resolved "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz"
    integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
    dependencies:
      is-callable "^1.1.3"
  
  forever-agent@~0.6.1:
    version "0.6.1"
    resolved "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz"
    integrity sha512-j0KLYPhm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==
  
  form-data@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz"
    integrity sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==
    dependencies:
      asynckit "^0.4.0"
      combined-stream "^1.0.8"
      mime-types "^2.1.12"
  
  form-data@~2.3.2:
    version "2.3.3"
    resolved "https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz"
    integrity sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==
    dependencies:
      asynckit "^0.4.0"
      combined-stream "^1.0.6"
      mime-types "^2.1.12"
  
  forwarded@0.2.0:
    version "0.2.0"
    resolved "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz"
    integrity sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==
  
  fresh@0.5.2:
    version "0.5.2"
    resolved "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
    integrity sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==
  
  from2@^2.1.1:
    version "2.3.0"
    resolved "https://registry.npmjs.org/from2/-/from2-2.3.0.tgz"
    integrity sha512-OMcX/4IC/uqEPVgGeyfN22LJk6AZrMkRZHxcHBMBvHScDGgwTm2GT2Wkgtocyd3JfZffjj2kYUDXXII0Fk9W0g==
    dependencies:
      inherits "^2.0.1"
      readable-stream "^2.0.0"
  
  fs-constants@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/fs-constants/-/fs-constants-1.0.0.tgz"
    integrity sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==
  
  fs-extra@^8.0.1, fs-extra@^8.1.0:
    version "8.1.0"
    resolved "https://registry.npmjs.org/fs-extra/-/fs-extra-8.1.0.tgz"
    integrity sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==
    dependencies:
      graceful-fs "^4.2.0"
      jsonfile "^4.0.0"
      universalify "^0.1.0"
  
  fs-monkey@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/fs-monkey/-/fs-monkey-1.0.3.tgz"
    integrity sha512-cybjIfiiE+pTWicSCLFHSrXZ6EilF30oh91FDP9S2B051prEa7QWfrVTQm10/dDpswBDXZugPa1Ogu8Yh+HV0Q==
  
  fs.realpath@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
    integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==
  
  fsevents@~2.3.2:
    version "2.3.2"
    resolved "https://registry.npmjs.org/fsevents/-/fsevents-2.3.2.tgz"
    integrity sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==
  
  function-bind@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz"
    integrity sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==
  
  function.prototype.name@^1.1.5:
    version "1.1.5"
    resolved "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.5.tgz"
    integrity sha512-uN7m/BzVKQnCUF/iW8jYea67v++2u7m5UgENbHRtdDVclOUP+FMPlCNdmk0h/ysGyo2tavMJEDqJAkJdRa1vMA==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.3"
      es-abstract "^1.19.0"
      functions-have-names "^1.2.2"
  
  functions-have-names@^1.2.2:
    version "1.2.3"
    resolved "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz"
    integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==
  
  gensync@^1.0.0-beta.2:
    version "1.0.0-beta.2"
    resolved "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
    integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==
  
  get-caller-file@^2.0.5:
    version "2.0.5"
    resolved "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
    integrity sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==
  
  get-intrinsic@^1.0.2, get-intrinsic@^1.1.1, get-intrinsic@^1.1.3, get-intrinsic@^1.2.0:
    version "1.2.1"
    resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.1.tgz"
    integrity sha512-2DcsyfABl+gVHEfCOaTrWgyt+tb6MSEGmKq+kI5HwLbIYgjgmMcV8KQ41uaKz1xxUcn9tJtgFbQUEVcEbd0FYw==
    dependencies:
      function-bind "^1.1.1"
      has "^1.0.3"
      has-proto "^1.0.1"
      has-symbols "^1.0.3"
  
  get-proxy@^2.0.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/get-proxy/-/get-proxy-2.1.0.tgz"
    integrity sha512-zmZIaQTWnNQb4R4fJUEp/FC51eZsc6EkErspy3xtIYStaq8EB/hDIWipxsal+E8rz0qD7f2sL/NA9Xee4RInJw==
    dependencies:
      npm-conf "^1.1.0"
  
  get-stream@^2.2.0:
    version "2.3.1"
    resolved "https://registry.npmjs.org/get-stream/-/get-stream-2.3.1.tgz"
    integrity sha512-AUGhbbemXxrZJRD5cDvKtQxLuYaIbNtDTK8YqupCI393Q2KSTreEsLUN3ZxAWFGiKTzL6nKuzfcIvieflUX9qA==
    dependencies:
      object-assign "^4.0.1"
      pinkie-promise "^2.0.0"
  
  get-stream@^3.0.0, get-stream@3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/get-stream/-/get-stream-3.0.0.tgz"
    integrity sha512-GlhdIUuVakc8SJ6kK0zAFbiGzRFzNnY4jUuEbV9UROo4Y+0Ny4fjvcZFVTeDA4odpFyOQzaw6hXukJSq/f28sQ==
  
  get-stream@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmjs.org/get-stream/-/get-stream-4.1.0.tgz"
    integrity sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==
    dependencies:
      pump "^3.0.0"
  
  get-stream@^5.1.0:
    version "5.2.0"
    resolved "https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz"
    integrity sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==
    dependencies:
      pump "^3.0.0"
  
  get-stream@^6.0.0:
    version "6.0.1"
    resolved "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz"
    integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==
  
  get-symbol-description@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.0.0.tgz"
    integrity sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw==
    dependencies:
      call-bind "^1.0.2"
      get-intrinsic "^1.1.1"
  
  getpass@^0.1.1:
    version "0.1.7"
    resolved "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz"
    integrity sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==
    dependencies:
      assert-plus "^1.0.0"
  
  git-clone@^0.1.0:
    version "0.1.0"
    resolved "https://registry.npmjs.org/git-clone/-/git-clone-0.1.0.tgz"
    integrity sha512-zs9rlfa7HyaJAKG9o+V7C6qfMzyc+tb1IIXdUFcOBcR1U7siKy/uPdauLlrH1mc0vOgUwIv4BF+QxPiiTYz3Rw==
  
  giturl@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/giturl/-/giturl-1.0.1.tgz"
    integrity sha512-wQourBdI13n8tbjcZTDl6k+ZrCRMU6p9vfp9jknZq+zfWc8xXNztpZFM4XkPHVzHcMSUZxEMYYKZjIGkPlei6Q==
  
  glob-parent@^5.1.2, glob-parent@~5.1.2:
    version "5.1.2"
    resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
    integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
    dependencies:
      is-glob "^4.0.1"
  
  glob-parent@^6.0.1:
    version "6.0.2"
    resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
    integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
    dependencies:
      is-glob "^4.0.3"
  
  glob-parent@^6.0.2:
    version "6.0.2"
    resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
    integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
    dependencies:
      is-glob "^4.0.3"
  
  glob-to-regexp@^0.4.1:
    version "0.4.1"
    resolved "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
    integrity sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==
  
  glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.6:
    version "7.2.3"
    resolved "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
    integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
    dependencies:
      fs.realpath "^1.0.0"
      inflight "^1.0.4"
      inherits "2"
      minimatch "^3.1.1"
      once "^1.3.0"
      path-is-absolute "^1.0.0"
  
  global-dirs@^3.0.0:
    version "3.0.1"
    resolved "https://registry.npmjs.org/global-dirs/-/global-dirs-3.0.1.tgz"
    integrity sha512-NBcGGFbBA9s1VzD41QXDG+3++t9Mn5t1FpLdhESY6oKY4gYTFpX4wO3sqGUa0Srjtbfj3szX0RnemmrVRUdULA==
    dependencies:
      ini "2.0.0"
  
  global-modules@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/global-modules/-/global-modules-2.0.0.tgz"
    integrity sha512-NGbfmJBp9x8IxyJSd1P+otYK8vonoJactOogrVfFRIAEY1ukil8RSKDz2Yo7wh1oihl51l/r6W4epkeKJHqL8A==
    dependencies:
      global-prefix "^3.0.0"
  
  global-prefix@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/global-prefix/-/global-prefix-3.0.0.tgz"
    integrity sha512-awConJSVCHVGND6x3tmMaKcQvwXLhjdkmomy2W+Goaui8YPgYgXJZewhg3fWC+DlfqqQuWg8AwqjGTD2nAPVWg==
    dependencies:
      ini "^1.3.5"
      kind-of "^6.0.2"
      which "^1.3.1"
  
  globals@^11.1.0:
    version "11.12.0"
    resolved "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
    integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==
  
  globals@^13.19.0:
    version "13.20.0"
    resolved "https://registry.npmjs.org/globals/-/globals-13.20.0.tgz"
    integrity sha512-Qg5QtVkCy/kv3FUSlu4ukeZDVf9ee0iXLAUYX13gbR17bnejFTzr4iS9bY7kwCf1NztRNm1t91fjOiyx4CSwPQ==
    dependencies:
      type-fest "^0.20.2"
  
  globalthis@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/globalthis/-/globalthis-1.0.3.tgz"
    integrity sha512-sFdI5LyBiNTHjRd7cGPWapiHWMOXKyuBNX/cWJ3NfzrZQVa8GI/8cofCl74AOVqq9W5kNmguTIzJ/1s2gyI9wA==
    dependencies:
      define-properties "^1.1.3"
  
  globby@^11.0.1, globby@^11.0.2, globby@^11.1.0:
    version "11.1.0"
    resolved "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz"
    integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
    dependencies:
      array-union "^2.1.0"
      dir-glob "^3.0.1"
      fast-glob "^3.2.9"
      ignore "^5.2.0"
      merge2 "^1.4.1"
      slash "^3.0.0"
  
  globby@^12.0.2:
    version "12.2.0"
    resolved "https://registry.npmjs.org/globby/-/globby-12.2.0.tgz"
    integrity sha512-wiSuFQLZ+urS9x2gGPl1H5drc5twabmm4m2gTR27XDFyjUHJUNsS8o/2aKyIF6IoBaR630atdher0XJ5g6OMmA==
    dependencies:
      array-union "^3.0.1"
      dir-glob "^3.0.1"
      fast-glob "^3.2.7"
      ignore "^5.1.9"
      merge2 "^1.4.1"
      slash "^4.0.0"
  
  globjoin@^0.1.4:
    version "0.1.4"
    resolved "https://registry.npmjs.org/globjoin/-/globjoin-0.1.4.tgz"
    integrity sha512-xYfnw62CKG8nLkZBfWbhWwDw02CHty86jfPcc2cr3ZfeuK9ysoVPPEUxf21bAD/rWAgk52SuBrLJlefNy8mvFg==
  
  globs@^0.1.4:
    version "0.1.4"
    resolved "https://registry.npmjs.org/globs/-/globs-0.1.4.tgz"
    integrity sha512-D23dWbOq48vlOraoSigbcQV4tWrnhwk+E/Um2cMuDS3/5dwGmdFeA7L/vAvDhLFlQOTDqHcXh35m/71g2A2WzQ==
    dependencies:
      glob "^7.1.1"
  
  gopd@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/gopd/-/gopd-1.0.1.tgz"
    integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
    dependencies:
      get-intrinsic "^1.1.3"
  
  got@^8.3.1:
    version "8.3.2"
    resolved "https://registry.npmjs.org/got/-/got-8.3.2.tgz"
    integrity sha512-qjUJ5U/hawxosMryILofZCkm3C84PLJS/0grRIpjAwu+Lkxxj5cxeCU25BG0/3mDSpXKTyZr8oh8wIgLaH0QCw==
    dependencies:
      "@sindresorhus/is" "^0.7.0"
      cacheable-request "^2.1.1"
      decompress-response "^3.3.0"
      duplexer3 "^0.1.4"
      get-stream "^3.0.0"
      into-stream "^3.1.0"
      is-retry-allowed "^1.1.0"
      isurl "^1.0.0-alpha5"
      lowercase-keys "^1.0.0"
      mimic-response "^1.0.0"
      p-cancelable "^0.4.0"
      p-timeout "^2.0.1"
      pify "^3.0.0"
      safe-buffer "^5.1.1"
      timed-out "^4.0.1"
      url-parse-lax "^3.0.0"
      url-to-options "^1.0.1"
  
  got@^9.6.0:
    version "9.6.0"
    resolved "https://registry.npmjs.org/got/-/got-9.6.0.tgz"
    integrity sha512-R7eWptXuGYxwijs0eV+v3o6+XH1IqVK8dJOEecQfTmkncw9AV4dcw/Dhxi8MdlqPthxxpZyizMzyg8RTmEsG+Q==
    dependencies:
      "@sindresorhus/is" "^0.14.0"
      "@szmarczak/http-timer" "^1.1.2"
      cacheable-request "^6.0.0"
      decompress-response "^3.3.0"
      duplexer3 "^0.1.4"
      get-stream "^4.1.0"
      lowercase-keys "^1.0.1"
      mimic-response "^1.0.1"
      p-cancelable "^1.0.0"
      to-readable-stream "^1.0.0"
      url-parse-lax "^3.0.0"
  
  graceful-fs@^4.1.10, graceful-fs@^4.1.2, graceful-fs@^4.1.5, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.4, graceful-fs@^4.2.6, graceful-fs@^4.2.9:
    version "4.2.10"
    resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.10.tgz"
    integrity sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==
  
  grapheme-splitter@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmjs.org/grapheme-splitter/-/grapheme-splitter-1.0.4.tgz"
    integrity sha512-bzh50DW9kTPM00T8y4o8vQg89Di9oLJVLW/KaOGIXJWP/iqCN6WKYkbNOF04vFLJhwcpYUh9ydh/+5vpOqV4YQ==
  
  graphemer@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz"
    integrity sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==
  
  handle-thing@^2.0.0:
    version "2.0.1"
    resolved "https://registry.npmjs.org/handle-thing/-/handle-thing-2.0.1.tgz"
    integrity sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg==
  
  har-schema@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz"
    integrity sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q==
  
  har-validator@~5.1.3:
    version "5.1.5"
    resolved "https://registry.npmjs.org/har-validator/-/har-validator-5.1.5.tgz"
    integrity sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==
    dependencies:
      ajv "^6.12.3"
      har-schema "^2.0.0"
  
  hard-rejection@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/hard-rejection/-/hard-rejection-2.1.0.tgz"
    integrity sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA==
  
  has-bigints@^1.0.1, has-bigints@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/has-bigints/-/has-bigints-1.0.2.tgz"
    integrity sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==
  
  has-flag@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
    integrity sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==
  
  has-flag@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
    integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==
  
  has-property-descriptors@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz"
    integrity sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ==
    dependencies:
      get-intrinsic "^1.1.1"
  
  has-proto@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/has-proto/-/has-proto-1.0.1.tgz"
    integrity sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==
  
  has-symbol-support-x@^1.4.1:
    version "1.4.2"
    resolved "https://registry.npmjs.org/has-symbol-support-x/-/has-symbol-support-x-1.4.2.tgz"
    integrity sha512-3ToOva++HaW+eCpgqZrCfN51IPB+7bJNVT6CUATzueB5Heb8o6Nam0V3HG5dlDvZU1Gn5QLcbahiKw/XVk5JJw==
  
  has-symbols@^1.0.2, has-symbols@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz"
    integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==
  
  has-to-string-tag-x@^1.2.0:
    version "1.4.1"
    resolved "https://registry.npmjs.org/has-to-string-tag-x/-/has-to-string-tag-x-1.4.1.tgz"
    integrity sha512-vdbKfmw+3LoOYVr+mtxHaX5a96+0f3DljYd8JOqvOLsf5mw2Otda2qCDT9qRqLAhrjyQ0h7ual5nOiASpsGNFw==
    dependencies:
      has-symbol-support-x "^1.4.1"
  
  has-tostringtag@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
    integrity sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==
    dependencies:
      has-symbols "^1.0.2"
  
  has-yarn@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/has-yarn/-/has-yarn-2.1.0.tgz"
    integrity sha512-UqBRqi4ju7T+TqGNdqAO0PaSVGsDGJUBQvk9eUWNGRY1CFGDzYhLWoM7JQEemnlvVcv/YEmc2wNW8BC24EnUsw==
  
  has@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
    integrity sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==
    dependencies:
      function-bind "^1.1.1"
  
  hash-sum@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/hash-sum/-/hash-sum-1.0.2.tgz"
    integrity sha512-fUs4B4L+mlt8/XAtSOGMUO1TXmAelItBPtJG7CyHJfYTdDjwisntGO2JQz7oUsatOY9o68+57eziUVNw/mRHmA==
  
  he@^1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/he/-/he-1.2.0.tgz"
    integrity sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==
  
  highlight-es@^1.0.0:
    version "1.0.3"
    resolved "https://registry.npmjs.org/highlight-es/-/highlight-es-1.0.3.tgz"
    integrity sha512-s/SIX6yp/5S1p8aC/NRDC1fwEb+myGIfp8/TzZz0rtAv8fzsdX7vGl3Q1TrXCsczFq8DI3CBFBCySPClfBSdbg==
    dependencies:
      chalk "^2.4.0"
      is-es2016-keyword "^1.0.0"
      js-tokens "^3.0.0"
  
  highlight.js@^10.7.1:
    version "10.7.3"
    resolved "https://registry.npmjs.org/highlight.js/-/highlight.js-10.7.3.tgz"
    integrity sha512-tzcUFauisWKNHaRkN4Wjl/ZA07gENAjFl3J/c480dprkGTg5EQstgaNFqBfUqCq54kZRIEcreTsAgF/m2quD7A==
  
  history@^4.9.0:
    version "4.10.1"
    resolved "https://registry.npmjs.org/history/-/history-4.10.1.tgz"
    integrity sha512-36nwAD620w12kuzPAsyINPWJqlNbij+hpK1k9XRloDtym8mxzGYl2c17LnV6IAGB2Dmg4tEa7G7DlawS0+qjew==
    dependencies:
      "@babel/runtime" "^7.1.2"
      loose-envify "^1.2.0"
      resolve-pathname "^3.0.0"
      tiny-invariant "^1.0.2"
      tiny-warning "^1.0.0"
      value-equal "^1.0.1"
  
  history@^5.1.0:
    version "5.3.0"
    resolved "https://registry.npmjs.org/history/-/history-5.3.0.tgz"
    integrity sha512-ZqaKwjjrAYUYfLG+htGaIIZ4nioX2L70ZUMIFysS3xvBsSG4x/n1V6TXV3N8ZYNuFGlDirFg32T7B6WOUPDYcQ==
    dependencies:
      "@babel/runtime" "^7.7.6"
  
  hls.js@^1.1.5:
    version "1.2.3"
    resolved "https://registry.npmjs.org/hls.js/-/hls.js-1.2.3.tgz"
    integrity sha512-CC/vHi82ldiiydIhliNI3whlcepRXxI2jdpd/KKb6lyEv+74e7lXs4cGk5PHfTLxZMKOj6+m5LX9VAbvV/r7AQ==
  
  hoist-non-react-statics@^3.1.0:
    version "3.3.2"
    resolved "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
    integrity sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==
    dependencies:
      react-is "^16.7.0"
  
  hosted-git-info@^2.1.4:
    version "2.8.9"
    resolved "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.8.9.tgz"
    integrity sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==
  
  hosted-git-info@^4.0.1:
    version "4.1.0"
    resolved "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-4.1.0.tgz"
    integrity sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==
    dependencies:
      lru-cache "^6.0.0"
  
  hpack.js@^2.1.6:
    version "2.1.6"
    resolved "https://registry.npmjs.org/hpack.js/-/hpack.js-2.1.6.tgz"
    integrity sha512-zJxVehUdMGIKsRaNt7apO2Gqp0BdqW5yaiGHXXmbpvxgBYVZnAql+BJb4RO5ad2MgpbZKn5G6nMnegrH1FcNYQ==
    dependencies:
      inherits "^2.0.1"
      obuf "^1.0.0"
      readable-stream "^2.0.1"
      wbuf "^1.1.0"
  
  html-encoding-sniffer@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-3.0.0.tgz"
    integrity sha512-oWv4T4yJ52iKrufjnyZPkrN0CH3QnrUqdB6In1g5Fe1mia8GmF36gnfNySxoZtxD5+NmYw1EElVXiBk93UeskA==
    dependencies:
      whatwg-encoding "^2.0.0"
  
  html-entities@^2.1.0, html-entities@^2.3.2:
    version "2.3.3"
    resolved "https://registry.npmjs.org/html-entities/-/html-entities-2.3.3.tgz"
    integrity sha512-DV5Ln36z34NNTDgnz0EWGBLZENelNAtkiFA4kyNOG2tDI6Mz1uSWiq1wAKdyjnJwyDiDO7Fa2SO1CTxPXL8VxA==
  
  html-minifier-terser@^6.0.2:
    version "6.1.0"
    resolved "https://registry.npmjs.org/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz"
    integrity sha512-YXxSlJBZTP7RS3tWnQw74ooKa6L9b9i9QYXY21eUEvhZ3u9XLfv6OnFsQq6RxkhHygsaUMvYsZRV5rU/OVNZxw==
    dependencies:
      camel-case "^4.1.2"
      clean-css "^5.2.2"
      commander "^8.3.0"
      he "^1.2.0"
      param-case "^3.0.4"
      relateurl "^0.2.7"
      terser "^5.10.0"
  
  html-minifier@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/html-minifier/-/html-minifier-4.0.0.tgz"
    integrity sha512-aoGxanpFPLg7MkIl/DDFYtb0iWz7jMFGqFhvEDZga6/4QTjneiD8I/NXL1x5aaoCp7FSIT6h/OhykDdPsbtMig==
    dependencies:
      camel-case "^3.0.0"
      clean-css "^4.2.1"
      commander "^2.19.0"
      he "^1.2.0"
      param-case "^2.1.1"
      relateurl "^0.2.7"
      uglify-js "^3.5.1"
  
  html-tags@^3.3.1:
    version "3.3.1"
    resolved "https://registry.npmjs.org/html-tags/-/html-tags-3.3.1.tgz"
    integrity sha512-ztqyC3kLto0e9WbNp0aeP+M3kTt+nbaIveGmUxAtZa+8iFgKLUOD4YKM5j+f3QD89bra7UeumolZHKuOXnTmeQ==
  
  html-webpack-plugin@5.5.0:
    version "5.5.0"
    resolved "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-5.5.0.tgz"
    integrity sha512-sy88PC2cRTVxvETRgUHFrL4No3UxvcH8G1NepGhqaTT+GXN2kTamqasot0inS5hXeg1cMbFDt27zzo9p35lZVw==
    dependencies:
      "@types/html-minifier-terser" "^6.0.0"
      html-minifier-terser "^6.0.2"
      lodash "^4.17.21"
      pretty-error "^4.0.0"
      tapable "^2.0.0"
  
  htmlparser2@^6.1.0:
    version "6.1.0"
    resolved "https://registry.npmjs.org/htmlparser2/-/htmlparser2-6.1.0.tgz"
    integrity sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A==
    dependencies:
      domelementtype "^2.0.1"
      domhandler "^4.0.0"
      domutils "^2.5.2"
      entities "^2.0.0"
  
  http-cache-semantics@^4.0.0:
    version "4.1.1"
    resolved "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.1.1.tgz"
    integrity sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ==
  
  http-cache-semantics@3.8.1:
    version "3.8.1"
    resolved "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.8.1.tgz"
    integrity sha512-5ai2iksyV8ZXmnZhHH4rWPoxxistEexSi5936zIQ1bnNTW5VnA85B6P/VpXiRM017IgRvb2kKo1a//y+0wSp3w==
  
  http-deceiver@^1.2.7:
    version "1.2.7"
    resolved "https://registry.npmjs.org/http-deceiver/-/http-deceiver-1.2.7.tgz"
    integrity sha512-LmpOGxTfbpgtGVxJrj5k7asXHCgNZp5nLfp+hWc8QQRqtb7fUy6kRY3BO1h9ddF6yIPYUARgxGOwB42DnxIaNw==
  
  http-errors@~1.6.2:
    version "1.6.3"
    resolved "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz"
    integrity sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==
    dependencies:
      depd "~1.1.2"
      inherits "2.0.3"
      setprototypeof "1.1.0"
      statuses ">= 1.4.0 < 2"
  
  http-errors@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz"
    integrity sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==
    dependencies:
      depd "2.0.0"
      inherits "2.0.4"
      setprototypeof "1.2.0"
      statuses "2.0.1"
      toidentifier "1.0.1"
  
  http-parser-js@>=0.5.1:
    version "0.5.8"
    resolved "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.5.8.tgz"
    integrity sha512-SGeBX54F94Wgu5RH3X5jsDtf4eHyRogWX1XGT3b4HuW3tQPM4AaBzoUji/4AAJNXCEOWZ5O0DgZmJw1947gD5Q==
  
  http-proxy-agent@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz"
    integrity sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==
    dependencies:
      "@tootallnate/once" "2"
      agent-base "6"
      debug "4"
  
  http-proxy-middleware@^2.0.0:
    version "2.0.6"
    resolved "https://registry.npmjs.org/http-proxy-middleware/-/http-proxy-middleware-2.0.6.tgz"
    integrity sha512-ya/UeJ6HVBYxrgYotAZo1KvPWlgB48kUJLDePFeneHsVujFaW5WNj2NgWCAE//B1Dl02BIfYlpNgBy8Kf8Rjmw==
    dependencies:
      "@types/http-proxy" "^1.17.8"
      http-proxy "^1.18.1"
      is-glob "^4.0.1"
      is-plain-obj "^3.0.0"
      micromatch "^4.0.2"
  
  http-proxy@^1.18.1:
    version "1.18.1"
    resolved "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz"
    integrity sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==
    dependencies:
      eventemitter3 "^4.0.0"
      follow-redirects "^1.0.0"
      requires-port "^1.0.0"
  
  http-signature@~1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz"
    integrity sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==
    dependencies:
      assert-plus "^1.0.0"
      jsprim "^1.2.2"
      sshpk "^1.7.0"
  
  https-proxy-agent@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
    integrity sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==
    dependencies:
      agent-base "6"
      debug "4"
  
  human-signals@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz"
    integrity sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==
  
  iconv-lite@^0.4.24, iconv-lite@0.4.24:
    version "0.4.24"
    resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
    integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
    dependencies:
      safer-buffer ">= 2.1.2 < 3"
  
  iconv-lite@^0.6.3:
    version "0.6.3"
    resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
    integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
    dependencies:
      safer-buffer ">= 2.1.2 < 3.0.0"
  
  iconv-lite@0.6.3:
    version "0.6.3"
    resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
    integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
    dependencies:
      safer-buffer ">= 2.1.2 < 3.0.0"
  
  icss-utils@^5.0.0, icss-utils@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/icss-utils/-/icss-utils-5.1.0.tgz"
    integrity sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==
  
  ieee754@^1.1.13:
    version "1.2.1"
    resolved "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz"
    integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==
  
  ignore@^5.1.8, ignore@^5.1.9, ignore@^5.2.0, ignore@^5.2.4:
    version "5.2.4"
    resolved "https://registry.npmjs.org/ignore/-/ignore-5.2.4.tgz"
    integrity sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==
  
  image-size@~0.5.0:
    version "0.5.5"
    resolved "https://registry.npmjs.org/image-size/-/image-size-0.5.5.tgz"
    integrity sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==
  
  immutable@^4.0.0:
    version "4.1.0"
    resolved "https://registry.npmjs.org/immutable/-/immutable-4.1.0.tgz"
    integrity sha512-oNkuqVTA8jqG1Q6c+UglTOD1xhC1BtjKI7XkCXRkZHrN5m18/XsnUp8Q89GkQO/z+0WjonSvl0FLhDYftp46nQ==
  
  import-fresh@^3.0.0, import-fresh@^3.2.1:
    version "3.3.0"
    resolved "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz"
    integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
    dependencies:
      parent-module "^1.0.0"
      resolve-from "^4.0.0"
  
  import-lazy@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/import-lazy/-/import-lazy-2.1.0.tgz"
    integrity sha512-m7ZEHgtw69qOGw+jwxXkHlrlIPdTGkyh66zXZ1ajZbxkDBNjSY/LGbmjc7h0s2ELsUDTAhFr55TrPSSqJGPG0A==
  
  import-lazy@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/import-lazy/-/import-lazy-4.0.0.tgz"
    integrity sha512-rKtvo6a868b5Hu3heneU+L4yEQ4jYKLtjpnPeUdK7h0yzXGmyBTypknlkCvHFBqfX9YlorEiMM6Dnq/5atfHkw==
  
  imurmurhash@^0.1.4:
    version "0.1.4"
    resolved "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
    integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==
  
  indent-string@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz"
    integrity sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==
  
  inflight@^1.0.4:
    version "1.0.6"
    resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
    integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
    dependencies:
      once "^1.3.0"
      wrappy "1"
  
  inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3, inherits@2, inherits@2.0.4:
    version "2.0.4"
    resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
    integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==
  
  inherits@2.0.3:
    version "2.0.3"
    resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz"
    integrity sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==
  
  ini@^1.3.4, ini@^1.3.5, ini@~1.3.0:
    version "1.3.8"
    resolved "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz"
    integrity sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==
  
  ini@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/ini/-/ini-2.0.0.tgz"
    integrity sha512-7PnF4oN3CvZF23ADhA5wRaYEQpJ8qygSkbtTXWBeXWXmEVRXK+1ITciHWwHhsjv1TmW0MgacIv6hEi5pX5NQdA==
  
  inquirer@^7.3.3:
    version "7.3.3"
    resolved "https://registry.npmjs.org/inquirer/-/inquirer-7.3.3.tgz"
    integrity sha512-JG3eIAj5V9CwcGvuOmoo6LB9kbAYT8HXffUl6memuszlwDC/qvFAJw49XJ5NROSFNPxp3iQg1GqkFhaY/CR0IA==
    dependencies:
      ansi-escapes "^4.2.1"
      chalk "^4.1.0"
      cli-cursor "^3.1.0"
      cli-width "^3.0.0"
      external-editor "^3.0.3"
      figures "^3.0.0"
      lodash "^4.17.19"
      mute-stream "0.0.8"
      run-async "^2.4.0"
      rxjs "^6.6.0"
      string-width "^4.1.0"
      strip-ansi "^6.0.0"
      through "^2.3.6"
  
  inquirer@^8.0.0:
    version "8.2.5"
    resolved "https://registry.npmjs.org/inquirer/-/inquirer-8.2.5.tgz"
    integrity sha512-QAgPDQMEgrDssk1XiwwHoOGYF9BAbUcc1+j+FhEvaOt8/cKRqyLn0U5qA6F74fGhTMGxf92pOvPBeh29jQJDTQ==
    dependencies:
      ansi-escapes "^4.2.1"
      chalk "^4.1.1"
      cli-cursor "^3.1.0"
      cli-width "^3.0.0"
      external-editor "^3.0.3"
      figures "^3.0.0"
      lodash "^4.17.21"
      mute-stream "0.0.8"
      ora "^5.4.1"
      run-async "^2.4.0"
      rxjs "^7.5.5"
      string-width "^4.1.0"
      strip-ansi "^6.0.0"
      through "^2.3.6"
      wrap-ansi "^7.0.0"
  
  internal-slot@^1.0.3, internal-slot@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.5.tgz"
    integrity sha512-Y+R5hJrzs52QCG2laLn4udYVnxsfny9CpOhNhUvk/SSSVyF6T27FzRbF0sroPidSu3X8oEAkOn2K804mjpt6UQ==
    dependencies:
      get-intrinsic "^1.2.0"
      has "^1.0.3"
      side-channel "^1.0.4"
  
  intersection-observer@^0.7.0:
    version "0.7.0"
    resolved "https://registry.npmjs.org/intersection-observer/-/intersection-observer-0.7.0.tgz"
    integrity sha512-Id0Fij0HsB/vKWGeBe9PxeY45ttRiBmhFyyt/geBdDHBYNctMRTE3dC1U3ujzz3lap+hVXlEcVaB56kZP/eEUg==
  
  into-stream@^3.1.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/into-stream/-/into-stream-3.1.0.tgz"
    integrity sha512-TcdjPibTksa1NQximqep2r17ISRiNE9fwlfbg3F8ANdvP5/yrFTew86VcO//jk4QTaMlbjypPBq76HN2zaKfZQ==
    dependencies:
      from2 "^2.1.1"
      p-is-promise "^1.1.0"
  
  invariant@2.2.4:
    version "2.2.4"
    resolved "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz"
    integrity sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==
    dependencies:
      loose-envify "^1.0.0"
  
  ip@^1.1.0:
    version "1.1.8"
    resolved "https://registry.npmjs.org/ip/-/ip-1.1.8.tgz"
    integrity sha512-PuExPYUiu6qMBQb4l06ecm6T6ujzhmh+MeJcW9wa89PoAz5pvd4zPgN5WJV104mb6S2T1AwNIAaB70JNrLQWhg==
  
  ipaddr.js@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-2.0.1.tgz"
    integrity sha512-1qTgH9NG+IIJ4yfKs2e6Pp1bZg8wbDbKHT21HrLIeYBTRLgMYKnMTPAuI3Lcs61nfx5h1xlXnbJtH1kX5/d/ng==
  
  ipaddr.js@1.9.1:
    version "1.9.1"
    resolved "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
    integrity sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==
  
  is-arguments@^1.0.4:
    version "1.1.1"
    resolved "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz"
    integrity sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==
    dependencies:
      call-bind "^1.0.2"
      has-tostringtag "^1.0.0"
  
  is-array-buffer@^3.0.1, is-array-buffer@^3.0.2:
    version "3.0.2"
    resolved "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.2.tgz"
    integrity sha512-y+FyyR/w8vfIRq4eQcM1EYgSTnmHXPqaF+IgzgraytCFq5Xh8lllDVmAZolPJiZttZLeFSINPYMaEJ7/vWUa1w==
    dependencies:
      call-bind "^1.0.2"
      get-intrinsic "^1.2.0"
      is-typed-array "^1.1.10"
  
  is-arrayish@^0.2.1:
    version "0.2.1"
    resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
    integrity sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==
  
  is-bigint@^1.0.1:
    version "1.0.4"
    resolved "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.4.tgz"
    integrity sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==
    dependencies:
      has-bigints "^1.0.1"
  
  is-binary-path@~2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
    integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
    dependencies:
      binary-extensions "^2.0.0"
  
  is-boolean-object@^1.1.0:
    version "1.1.2"
    resolved "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
    integrity sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==
    dependencies:
      call-bind "^1.0.2"
      has-tostringtag "^1.0.0"
  
  is-buffer@~1.1.6:
    version "1.1.6"
    resolved "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz"
    integrity sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==
  
  is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
    version "1.2.7"
    resolved "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz"
    integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==
  
  is-ci@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/is-ci/-/is-ci-2.0.0.tgz"
    integrity sha512-YfJT7rkpQB0updsdHLGWrvhBJfcfzNNawYDNIyQXJz0IViGf75O8EBPKSdvw2rF+LGCsX4FZ8tcr3b19LcZq4w==
    dependencies:
      ci-info "^2.0.0"
  
  is-core-module@^2.11.0, is-core-module@^2.4.0, is-core-module@^2.5.0, is-core-module@^2.9.0:
    version "2.12.1"
    resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.12.1.tgz"
    integrity sha512-Q4ZuBAe2FUsKtyQJoQHlvP8OvBERxO3jEmy1I7hcRXcJBGGHFh/aJBswbXuS9sgrDH2QUO8ilkwNPHvHMd8clg==
    dependencies:
      has "^1.0.3"
  
  is-date-object@^1.0.1:
    version "1.0.5"
    resolved "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz"
    integrity sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-docker@^2.0.0, is-docker@^2.1.1:
    version "2.2.1"
    resolved "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz"
    integrity sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==
  
  is-es2016-keyword@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/is-es2016-keyword/-/is-es2016-keyword-1.0.0.tgz"
    integrity sha512-JtZWPUwjdbQ1LIo9OSZ8MdkWEve198ors27vH+RzUUvZXXZkzXCxFnlUhzWYxy5IexQSRiXVw9j2q/tHMmkVYQ==
  
  is-extglob@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
    integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==
  
  is-fullwidth-code-point@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
    integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==
  
  is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
    version "4.0.3"
    resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
    integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
    dependencies:
      is-extglob "^2.1.1"
  
  is-installed-globally@^0.4.0:
    version "0.4.0"
    resolved "https://registry.npmjs.org/is-installed-globally/-/is-installed-globally-0.4.0.tgz"
    integrity sha512-iwGqO3J21aaSkC7jWnHP/difazwS7SFeIqxv6wEtLU8Y5KlzFTjyqcSIT0d8s4+dDhKytsk9PJZ2BkS5eZwQRQ==
    dependencies:
      global-dirs "^3.0.0"
      is-path-inside "^3.0.2"
  
  is-interactive@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/is-interactive/-/is-interactive-1.0.0.tgz"
    integrity sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==
  
  is-invalid-path@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/is-invalid-path/-/is-invalid-path-1.0.2.tgz"
    integrity sha512-6KLcFrPCEP3AFXMfnWrIFkZpYNBVzZAoBJJDEZKtI3LXkaDjM3uFMJQjxiizUuZTZ9Oh9FNv/soXbx5TcpaDmA==
  
  is-natural-number@^4.0.1:
    version "4.0.1"
    resolved "https://registry.npmjs.org/is-natural-number/-/is-natural-number-4.0.1.tgz"
    integrity sha512-Y4LTamMe0DDQIIAlaer9eKebAlDSV6huy+TWhJVPlzZh2o4tRP5SQWFlLn5N0To4mDD22/qdOq+veo1cSISLgQ==
  
  is-negative-zero@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.2.tgz"
    integrity sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==
  
  is-npm@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/is-npm/-/is-npm-5.0.0.tgz"
    integrity sha512-WW/rQLOazUq+ST/bCAVBp/2oMERWLsR7OrKyt052dNDk4DHcDE0/7QSXITlmi+VBcV13DfIbysG3tZJm5RfdBA==
  
  is-number-object@^1.0.4:
    version "1.0.7"
    resolved "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.7.tgz"
    integrity sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-number@^7.0.0:
    version "7.0.0"
    resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
    integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==
  
  is-obj@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/is-obj/-/is-obj-2.0.0.tgz"
    integrity sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==
  
  is-object@^1.0.1:
    version "1.0.2"
    resolved "https://registry.npmjs.org/is-object/-/is-object-1.0.2.tgz"
    integrity sha512-2rRIahhZr2UWb45fIOuvZGpFtz0TyOZLf32KxBbSoUCeZR495zCKlWUKKUByk3geS2eAs7ZAABt0Y/Rx0GiQGA==
  
  is-path-cwd@^2.2.0:
    version "2.2.0"
    resolved "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-2.2.0.tgz"
    integrity sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ==
  
  is-path-inside@^3.0.2, is-path-inside@^3.0.3:
    version "3.0.3"
    resolved "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz"
    integrity sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==
  
  is-plain-obj@^1.0.0, is-plain-obj@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-1.1.0.tgz"
    integrity sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==
  
  is-plain-obj@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-3.0.0.tgz"
    integrity sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==
  
  is-plain-object@^2.0.4:
    version "2.0.4"
    resolved "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz"
    integrity sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==
    dependencies:
      isobject "^3.0.1"
  
  is-plain-object@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/is-plain-object/-/is-plain-object-5.0.0.tgz"
    integrity sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q==
  
  is-potential-custom-element-name@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz"
    integrity sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==
  
  is-regex@^1.0.4, is-regex@^1.1.4:
    version "1.1.4"
    resolved "https://registry.npmjs.org/is-regex/-/is-regex-1.1.4.tgz"
    integrity sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==
    dependencies:
      call-bind "^1.0.2"
      has-tostringtag "^1.0.0"
  
  is-retry-allowed@^1.1.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/is-retry-allowed/-/is-retry-allowed-1.2.0.tgz"
    integrity sha512-RUbUeKwvm3XG2VYamhJL1xFktgjvPzL0Hq8C+6yrWIswDy3BIXGqCxhxkc30N9jqK311gVU137K8Ei55/zVJRg==
  
  is-shared-array-buffer@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz"
    integrity sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==
    dependencies:
      call-bind "^1.0.2"
  
  is-stream@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz"
    integrity sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==
  
  is-stream@^2.0.0:
    version "2.0.1"
    resolved "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
    integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==
  
  is-string@^1.0.5, is-string@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmjs.org/is-string/-/is-string-1.0.7.tgz"
    integrity sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-symbol@^1.0.2, is-symbol@^1.0.3:
    version "1.0.4"
    resolved "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.4.tgz"
    integrity sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==
    dependencies:
      has-symbols "^1.0.2"
  
  is-typed-array@^1.1.10, is-typed-array@^1.1.9:
    version "1.1.10"
    resolved "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.10.tgz"
    integrity sha512-PJqgEHiWZvMpaFZ3uTc8kHPM4+4ADTlDniuQL7cU/UDA0Ql7F70yGfHph3cLNe+c9toaigv+DFzTJKhc2CtO6A==
    dependencies:
      available-typed-arrays "^1.0.5"
      call-bind "^1.0.2"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-tostringtag "^1.0.0"
  
  is-typedarray@^1.0.0, is-typedarray@~1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
    integrity sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==
  
  is-unicode-supported@^0.1.0:
    version "0.1.0"
    resolved "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz"
    integrity sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==
  
  is-utf8@^0.2.0, is-utf8@^0.2.1:
    version "0.2.1"
    resolved "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.1.tgz"
    integrity sha512-rMYPYvCzsXywIsldgLaSoPlw5PfoB/ssr7hY4pLfcodrA5M/eArza1a9VmTiNIBNMjOGr1Ow9mTyU2o69U6U9Q==
  
  is-weakref@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/is-weakref/-/is-weakref-1.0.2.tgz"
    integrity sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==
    dependencies:
      call-bind "^1.0.2"
  
  is-what@^3.14.1:
    version "3.14.1"
    resolved "https://registry.npmjs.org/is-what/-/is-what-3.14.1.tgz"
    integrity sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==
  
  is-wsl@^2.2.0:
    version "2.2.0"
    resolved "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz"
    integrity sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==
    dependencies:
      is-docker "^2.0.0"
  
  is-yarn-global@^0.3.0:
    version "0.3.0"
    resolved "https://registry.npmjs.org/is-yarn-global/-/is-yarn-global-0.3.0.tgz"
    integrity sha512-VjSeb/lHmkoyd8ryPVIKvOCn4D1koMqY+vqyjjUfc3xyKtP4dYOxM44sZrnqQSzSds3xyOrUTLTC9LVCVgLngw==
  
  isarray@~1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
    integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==
  
  isarray@0.0.1:
    version "0.0.1"
    resolved "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz"
    integrity sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==
  
  isbinaryfile@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/isbinaryfile/-/isbinaryfile-5.0.0.tgz"
    integrity sha512-UDdnyGvMajJUWCkib7Cei/dvyJrrvo4FIrsvSFWdPpXSUorzXrDJ0S+X5Q4ZlasfPjca4yqCNNsjbCeiy8FFeg==
  
  isexe@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
    integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==
  
  isobject@^3.0.1:
    version "3.0.1"
    resolved "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz"
    integrity sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==
  
  isstream@~0.1.2:
    version "0.1.2"
    resolved "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz"
    integrity sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==
  
  isurl@^1.0.0-alpha5:
    version "1.0.0"
    resolved "https://registry.npmjs.org/isurl/-/isurl-1.0.0.tgz"
    integrity sha512-1P/yWsxPlDtn7QeRD+ULKQPaIaN6yF368GZ2vDfv0AL0NwpStafjWCDDdn0k8wgFMWpVAqG7oJhxHnlud42i9w==
    dependencies:
      has-to-string-tag-x "^1.2.0"
      is-object "^1.0.1"
  
  j-component@^1.4.6:
    version "1.4.6"
    resolved "https://registry.npmjs.org/j-component/-/j-component-1.4.6.tgz"
    integrity sha512-yKugOAw8LmalSC9mFXsFf+q5WRUvqaeA0lnOiUJiBhyBCJGhbSLAqyfYv3+u5TQtQVVUrB691ocyJUNIuYkcmA==
    dependencies:
      expr-parser "^1.0.0"
      miniprogram-api-typings "^3.2.2"
      miniprogram-exparser "2.15.0"
  
  jake@^10.8.5:
    version "10.8.5"
    resolved "https://registry.npmjs.org/jake/-/jake-10.8.5.tgz"
    integrity sha512-sVpxYeuAhWt0OTWITwT98oyV0GsXyMlXCF+3L1SuafBVUIr/uILGRB+NqwkzhgXKvoJpDIpQvqkUALgdmQsQxw==
    dependencies:
      async "^3.2.3"
      chalk "^4.0.2"
      filelist "^1.0.1"
      minimatch "^3.0.4"
  
  javascript-stringify@^2.0.1:
    version "2.1.0"
    resolved "https://registry.npmjs.org/javascript-stringify/-/javascript-stringify-2.1.0.tgz"
    integrity sha512-JVAfqNPTvNq3sB/VHQJAFxN/sPgKnsKrCwyRt15zwNCdrMMJDdcEOdubuy+DuJYYdm0ox1J4uzEuYKkN+9yhVg==
  
  jest-worker@^27.0.2, jest-worker@^27.4.5:
    version "27.5.1"
    resolved "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz"
    integrity sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==
    dependencies:
      "@types/node" "*"
      merge-stream "^2.0.0"
      supports-color "^8.0.0"
  
  joi@^17.6.0:
    version "17.6.1"
    resolved "https://registry.npmjs.org/joi/-/joi-17.6.1.tgz"
    integrity sha512-Hl7/iBklIX345OCM1TiFSCZRVaAOLDGlWCp0Df2vWYgBgjkezaR7Kvm3joBciBHQjZj5sxXs859r6eqsRSlG8w==
    dependencies:
      "@hapi/hoek" "^9.0.0"
      "@hapi/topo" "^5.0.0"
      "@sideway/address" "^4.1.3"
      "@sideway/formula" "^3.0.0"
      "@sideway/pinpoint" "^2.0.0"
  
  joycon@^3.0.1:
    version "3.1.1"
    resolved "https://registry.npmjs.org/joycon/-/joycon-3.1.1.tgz"
    integrity sha512-34wB/Y7MW7bzjKRjUKTa46I2Z7eV62Rkhva+KkopW7Qvv/OSWBqvkSY7vusOPrNuZcUG3tApvdVgNB8POj3SPw==
  
  "js-tokens@^3.0.0 || ^4.0.0":
    version "4.0.0"
    resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
    integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==
  
  js-tokens@^3.0.0:
    version "3.0.2"
    resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-3.0.2.tgz"
    integrity sha512-RjTcuD4xjtthQkaWH7dFlH85L+QaVtSoOyGdZ3g6HFhS9dFNDfLyqgm2NFe2X6cQpeFmt0452FJjFG5UameExg==
  
  js-tokens@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
    integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==
  
  js-yaml@^3.13.0, js-yaml@^3.14.0:
    version "3.14.1"
    resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz"
    integrity sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==
    dependencies:
      argparse "^1.0.7"
      esprima "^4.0.0"
  
  js-yaml@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
    integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
    dependencies:
      argparse "^2.0.1"
  
  jsbn@~0.1.0:
    version "0.1.1"
    resolved "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz"
    integrity sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==
  
  jsdom@^21.1.0:
    version "21.1.1"
    resolved "https://registry.npmjs.org/jsdom/-/jsdom-21.1.1.tgz"
    integrity sha512-Jjgdmw48RKcdAIQyUD1UdBh2ecH7VqwaXPN3ehoZN6MqgVbMn+lRm1aAT1AsdJRAJpwfa4IpwgzySn61h2qu3w==
    dependencies:
      abab "^2.0.6"
      acorn "^8.8.2"
      acorn-globals "^7.0.0"
      cssstyle "^3.0.0"
      data-urls "^4.0.0"
      decimal.js "^10.4.3"
      domexception "^4.0.0"
      escodegen "^2.0.0"
      form-data "^4.0.0"
      html-encoding-sniffer "^3.0.0"
      http-proxy-agent "^5.0.0"
      https-proxy-agent "^5.0.1"
      is-potential-custom-element-name "^1.0.1"
      nwsapi "^2.2.2"
      parse5 "^7.1.2"
      rrweb-cssom "^0.6.0"
      saxes "^6.0.0"
      symbol-tree "^3.2.4"
      tough-cookie "^4.1.2"
      w3c-xmlserializer "^4.0.0"
      webidl-conversions "^7.0.0"
      whatwg-encoding "^2.0.0"
      whatwg-mimetype "^3.0.0"
      whatwg-url "^12.0.1"
      ws "^8.13.0"
      xml-name-validator "^4.0.0"
  
  jsesc@^2.5.1:
    version "2.5.2"
    resolved "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz"
    integrity sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==
  
  jsesc@~0.5.0:
    version "0.5.0"
    resolved "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz"
    integrity sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA==
  
  json-buffer@3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.0.tgz"
    integrity sha512-CuUqjv0FUZIdXkHPI8MezCnFCdaTAacej1TZYulLoAg1h/PhwkdXFN4V/gzY4g+fMBCOV2xF+rp7t2XD2ns/NQ==
  
  json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
    version "2.3.1"
    resolved "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
    integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==
  
  json-schema-traverse@^0.4.1:
    version "0.4.1"
    resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
    integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==
  
  json-schema-traverse@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
    integrity sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==
  
  json-schema@0.4.0:
    version "0.4.0"
    resolved "https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz"
    integrity sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==
  
  json-stable-stringify-without-jsonify@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
    integrity sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==
  
  json-stringify-safe@~5.0.1:
    version "5.0.1"
    resolved "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz"
    integrity sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==
  
  json5@^1.0.1:
    version "1.0.2"
    resolved "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz"
    integrity sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==
    dependencies:
      minimist "^1.2.0"
  
  json5@^2.1.2, json5@^2.1.3, json5@^2.2.0, json5@^2.2.2:
    version "2.2.3"
    resolved "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
    integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==
  
  jsonfile@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz"
    integrity sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==
    optionalDependencies:
      graceful-fs "^4.1.6"
  
  jsonp-retry@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/jsonp-retry/-/jsonp-retry-1.0.3.tgz"
    integrity sha512-/jmE9+shtKP+oIt2AWO9Wx+C27NTGpLCEw4QHOqpoV2X6ta374HE9C+EEdgu8r3iLKgFMx7u5j0mCwxWN8UdlA==
    dependencies:
      object-assign "^4.1.1"
  
  jsprim@^1.2.2:
    version "1.4.2"
    resolved "https://registry.npmjs.org/jsprim/-/jsprim-1.4.2.tgz"
    integrity sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==
    dependencies:
      assert-plus "1.0.0"
      extsprintf "1.3.0"
      json-schema "0.4.0"
      verror "1.10.0"
  
  "jsx-ast-utils@^2.4.1 || ^3.0.0":
    version "3.3.3"
    resolved "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.3.3.tgz"
    integrity sha512-fYQHZTZ8jSfmWZ0iyzfwiU4WDX4HpHbMCZ3gPlWYiCl3BoeOTsqKBqnTVfH2rYT7eP5c3sVbeSPHnnJOaTrWiw==
    dependencies:
      array-includes "^3.1.5"
      object.assign "^4.1.3"
  
  keyv@^3.0.0, keyv@3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/keyv/-/keyv-3.0.0.tgz"
    integrity sha512-eguHnq22OE3uVoSYG0LVWNP+4ppamWr9+zWBe1bsNcovIMy6huUJFPgy4mGwCd/rnl3vOLGW1MTlu4c57CT1xA==
    dependencies:
      json-buffer "3.0.0"
  
  kind-of@^6.0.2, kind-of@^6.0.3:
    version "6.0.3"
    resolved "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz"
    integrity sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==
  
  kleur@^3.0.0:
    version "3.0.3"
    resolved "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz"
    integrity sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==
  
  klona@^2.0.4, klona@^2.0.5:
    version "2.0.5"
    resolved "https://registry.npmjs.org/klona/-/klona-2.0.5.tgz"
    integrity sha512-pJiBpiXMbt7dkzXe8Ghj/u4FfXOOa98fPW+bihOJ4SjnoijweJrNThJfd3ifXpXhREjpoF2mZVH1GfS9LV3kHQ==
  
  known-css-properties@^0.27.0:
    version "0.27.0"
    resolved "https://registry.npmjs.org/known-css-properties/-/known-css-properties-0.27.0.tgz"
    integrity sha512-uMCj6+hZYDoffuvAJjFAPz56E9uoowFHmTkqRtRq5WyC5Q6Cu/fTZKNQpX/RbzChBYLLl3lo8CjFZBAZXq9qFg==
  
  latest-version@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/latest-version/-/latest-version-5.1.0.tgz"
    integrity sha512-weT+r0kTkRQdCdYCNtkMwWXQTMEswKrFBkm4ckQOMVhhqhIMI1UT2hMj+1iigIhgSZm5gTmrRXBNoGUgaTY1xA==
    dependencies:
      package-json "^6.3.0"
  
  less-loader@10.2.0:
    version "10.2.0"
    resolved "https://registry.npmjs.org/less-loader/-/less-loader-10.2.0.tgz"
    integrity sha512-AV5KHWvCezW27GT90WATaDnfXBv99llDbtaj4bshq6DvAihMdNjaPDcUMa6EXKLRF+P2opFenJp89BXg91XLYg==
    dependencies:
      klona "^2.0.4"
  
  less@^3.10.3:
    version "3.13.1"
    resolved "https://registry.npmjs.org/less/-/less-3.13.1.tgz"
    integrity sha512-SwA1aQXGUvp+P5XdZslUOhhLnClSLIjWvJhmd+Vgib5BFIr9lMNlQwmwUNOjXThF/A0x+MCYYPeWEfeWiLRnTw==
    dependencies:
      copy-anything "^2.0.1"
      tslib "^1.10.0"
    optionalDependencies:
      errno "^0.1.1"
      graceful-fs "^4.1.2"
      image-size "~0.5.0"
      make-dir "^2.1.0"
      mime "^1.4.1"
      native-request "^1.0.5"
      source-map "~0.6.0"
  
  "less@^3.5.0 || ^4.0.0", less@^4.1.0:
    version "4.1.3"
    resolved "https://registry.npmjs.org/less/-/less-4.1.3.tgz"
    integrity sha512-w16Xk/Ta9Hhyei0Gpz9m7VS8F28nieJaL/VyShID7cYvP6IL5oHeL6p4TXSDJqZE/lNv0oJ2pGVjJsRkfwm5FA==
    dependencies:
      copy-anything "^2.0.1"
      parse-node-version "^1.0.1"
      tslib "^2.3.0"
    optionalDependencies:
      errno "^0.1.1"
      graceful-fs "^4.1.2"
      image-size "~0.5.0"
      make-dir "^2.1.0"
      mime "^1.4.1"
      needle "^3.1.0"
      source-map "~0.6.0"
  
  levn@^0.4.1:
    version "0.4.1"
    resolved "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
    integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
    dependencies:
      prelude-ls "^1.2.1"
      type-check "~0.4.0"
  
  levn@~0.3.0:
    version "0.3.0"
    resolved "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz"
    integrity sha512-0OO4y2iOHix2W6ujICbKIaEQXvFQHue65vUG3pb5EUomzPI90z9hsA1VsO/dbIIpC53J8gxM9Q4Oho0jrCM/yA==
    dependencies:
      prelude-ls "~1.1.2"
      type-check "~0.3.2"
  
  lightningcss-darwin-arm64@1.16.0:
    version "1.16.0"
    resolved "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.16.0.tgz"
    integrity sha512-gIhz6eZFwsC4oVMjBGQ3QWDdLQY7vcXFyM/x91PilgHqu63B9uBa10EZA75YoTEkbKhoz0uDCqyHh/EoF1GrkQ==
  
  lightningcss@^1.14.0:
    version "1.16.0"
    resolved "https://registry.npmjs.org/lightningcss/-/lightningcss-1.16.0.tgz"
    integrity sha512-5+ZS9h+xeADcJTF2oRCT3yNZBlDYyOgQSdrWNBCqsIwm8ucKbF061OBVv/WHP4Zk8FToNhwFklk/hMuOngqsIg==
    dependencies:
      detect-libc "^1.0.3"
    optionalDependencies:
      lightningcss-darwin-arm64 "1.16.0"
      lightningcss-darwin-x64 "1.16.0"
      lightningcss-linux-arm-gnueabihf "1.16.0"
      lightningcss-linux-arm64-gnu "1.16.0"
      lightningcss-linux-arm64-musl "1.16.0"
      lightningcss-linux-x64-gnu "1.16.0"
      lightningcss-linux-x64-musl "1.16.0"
      lightningcss-win32-x64-msvc "1.16.0"
  
  lilconfig@^2.0.3:
    version "2.0.6"
    resolved "https://registry.npmjs.org/lilconfig/-/lilconfig-2.0.6.tgz"
    integrity sha512-9JROoBW7pobfsx+Sq2JsASvCo6Pfo6WWoUW79HuB1BCoBXD4PLWJPqDF6fNj67pqBYTbAHkE57M1kS/+L1neOg==
  
  lines-and-columns@^1.1.6:
    version "1.2.4"
    resolved "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
    integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==
  
  load-yaml-file@^0.2.0:
    version "0.2.0"
    resolved "https://registry.npmjs.org/load-yaml-file/-/load-yaml-file-0.2.0.tgz"
    integrity sha512-OfCBkGEw4nN6JLtgRidPX6QxjBQGQf72q3si2uvqyFEMbycSFFHwAZeXx6cJgFM9wmLrf9zBwCP3Ivqa+LLZPw==
    dependencies:
      graceful-fs "^4.1.5"
      js-yaml "^3.13.0"
      pify "^4.0.1"
      strip-bom "^3.0.0"
  
  loader-runner@^4.2.0:
    version "4.3.0"
    resolved "https://registry.npmjs.org/loader-runner/-/loader-runner-4.3.0.tgz"
    integrity sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==
  
  loader-utils@^1.0.2:
    version "1.4.2"
    resolved "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.2.tgz"
    integrity sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==
    dependencies:
      big.js "^5.2.2"
      emojis-list "^3.0.0"
      json5 "^1.0.1"
  
  loader-utils@^1.1.0:
    version "1.4.2"
    resolved "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.2.tgz"
    integrity sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==
    dependencies:
      big.js "^5.2.2"
      emojis-list "^3.0.0"
      json5 "^1.0.1"
  
  loader-utils@^1.2.3:
    version "1.4.2"
    resolved "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.2.tgz"
    integrity sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==
    dependencies:
      big.js "^5.2.2"
      emojis-list "^3.0.0"
      json5 "^1.0.1"
  
  loader-utils@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.0.tgz"
    integrity sha512-qH0WSMBtn/oHuwjy/NucEgbx5dbxxnxup9s4PVXJUDHZBQY+s0NWA9rJf53RBnQZxfch7euUui7hpoAPvALZdA==
    dependencies:
      big.js "^5.2.2"
      emojis-list "^3.0.0"
      json5 "^1.0.1"
  
  loader-utils@^2.0.0, loader-utils@^2.0.4:
    version "2.0.4"
    resolved "https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.4.tgz"
    integrity sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==
    dependencies:
      big.js "^5.2.2"
      emojis-list "^3.0.0"
      json5 "^2.1.2"
  
  locate-path@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/locate-path/-/locate-path-3.0.0.tgz"
    integrity sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==
    dependencies:
      p-locate "^3.0.0"
      path-exists "^3.0.0"
  
  locate-path@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
    integrity sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==
    dependencies:
      p-locate "^4.1.0"
  
  locate-path@^6.0.0:
    version "6.0.0"
    resolved "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz"
    integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
    dependencies:
      p-locate "^5.0.0"
  
  lodash-es@4.17.21:
    version "4.17.21"
    resolved "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz"
    integrity sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==
  
  lodash.clonedeep@^4.5.0:
    version "4.5.0"
    resolved "https://registry.npmjs.org/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz"
    integrity sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ==
  
  lodash.debounce@^4.0.8:
    version "4.0.8"
    resolved "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
    integrity sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==
  
  lodash.kebabcase@^4.1.1:
    version "4.1.1"
    resolved "https://registry.npmjs.org/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz"
    integrity sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g==
  
  lodash.memoize@^4.1.2:
    version "4.1.2"
    resolved "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
    integrity sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==
  
  lodash.merge@^4.6.2:
    version "4.6.2"
    resolved "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
    integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==
  
  lodash.truncate@^4.4.2:
    version "4.4.2"
    resolved "https://registry.npmjs.org/lodash.truncate/-/lodash.truncate-4.4.2.tgz"
    integrity sha512-jttmRe7bRse52OsWIMDLaXxWqRAmtIUccAQ3garviCqJjafXOfNMO0yMfNpdD6zbGaTU0P5Nz7e7gAT6cKmJRw==
  
  lodash.uniq@^4.5.0:
    version "4.5.0"
    resolved "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
    integrity sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ==
  
  lodash@^4.17.11, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.20, lodash@^4.17.21, lodash@4.17.21, "lodash@4.6.1 || ^4.16.1":
    version "4.17.21"
    resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
    integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==
  
  log-symbols@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz"
    integrity sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==
    dependencies:
      chalk "^4.1.0"
      is-unicode-supported "^0.1.0"
  
  loglevel-plugin-prefix@^0.8.4:
    version "0.8.4"
    resolved "https://registry.npmjs.org/loglevel-plugin-prefix/-/loglevel-plugin-prefix-0.8.4.tgz"
    integrity sha512-WpG9CcFAOjz/FtNht+QJeGpvVl/cdR6P0z6OcXSkr8wFJOsV2GRj2j10JLfjuA4aYkcKCNIEqRGCyTife9R8/g==
  
  loglevel@^1.6.6:
    version "1.8.1"
    resolved "https://registry.npmjs.org/loglevel/-/loglevel-1.8.1.tgz"
    integrity sha512-tCRIJM51SHjAayKwC+QAg8hT8vg6z7GSgLJKGvzuPb1Wc+hLzqtuVLxp6/HzSPOozuK+8ErAhy7U/sVzw8Dgfg==
  
  loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.2.0, loose-envify@^1.3.1, loose-envify@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
    integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
    dependencies:
      js-tokens "^3.0.0 || ^4.0.0"
  
  lower-case@^1.1.1:
    version "1.1.4"
    resolved "https://registry.npmjs.org/lower-case/-/lower-case-1.1.4.tgz"
    integrity sha512-2Fgx1Ycm599x+WGpIYwJOvsjmXFzTSc34IwDWALRA/8AopUKAVPwfJ+h5+f85BCp0PWmmJcWzEpxOpoXycMpdA==
  
  lower-case@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmjs.org/lower-case/-/lower-case-2.0.2.tgz"
    integrity sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==
    dependencies:
      tslib "^2.0.3"
  
  lowercase-keys@^1.0.0, lowercase-keys@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/lowercase-keys/-/lowercase-keys-1.0.1.tgz"
    integrity sha512-G2Lj61tXDnVFFOi8VZds+SoQjtQC3dgokKdDG2mTm1tx4m50NUHBOZSBwQQHyy0V12A0JTG4icfZQH+xPyh8VA==
  
  lowercase-keys@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/lowercase-keys/-/lowercase-keys-2.0.0.tgz"
    integrity sha512-tqNXrS78oMOE73NMxK4EMLQsQowWf8jKooH9g7xPavRT706R6bkQJ6DY2Te7QukaZsulxa30wQ7bk0pm4XiHmA==
  
  lowercase-keys@1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/lowercase-keys/-/lowercase-keys-1.0.0.tgz"
    integrity sha512-RPlX0+PHuvxVDZ7xX+EBVAp4RsVxP/TdDSN2mJYdiq1Lc4Hz7EUSjUI7RZrKKlmrIzVhf6Jo2stj7++gVarS0A==
  
  lru-cache@^4.1.2:
    version "4.1.5"
    resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.5.tgz"
    integrity sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==
    dependencies:
      pseudomap "^1.0.2"
      yallist "^2.1.2"
  
  lru-cache@^5.1.1:
    version "5.1.1"
    resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
    integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
    dependencies:
      yallist "^3.0.2"
  
  lru-cache@^6.0.0:
    version "6.0.0"
    resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
    integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
    dependencies:
      yallist "^4.0.0"
  
  magic-string@^0.25.7:
    version "0.25.9"
    resolved "https://registry.npmjs.org/magic-string/-/magic-string-0.25.9.tgz"
    integrity sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ==
    dependencies:
      sourcemap-codec "^1.4.8"
  
  make-dir@^1.0.0:
    version "1.3.0"
    resolved "https://registry.npmjs.org/make-dir/-/make-dir-1.3.0.tgz"
    integrity sha512-2w31R7SJtieJJnQtGc7RVL2StM2vGYVfqUOvUDxH6bC6aJTxPxTF0GnIgCyu7tjockiUWAYQRbxa7vKn34s5sQ==
    dependencies:
      pify "^3.0.0"
  
  make-dir@^1.2.0:
    version "1.3.0"
    resolved "https://registry.npmjs.org/make-dir/-/make-dir-1.3.0.tgz"
    integrity sha512-2w31R7SJtieJJnQtGc7RVL2StM2vGYVfqUOvUDxH6bC6aJTxPxTF0GnIgCyu7tjockiUWAYQRbxa7vKn34s5sQ==
    dependencies:
      pify "^3.0.0"
  
  make-dir@^2.0.0, make-dir@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/make-dir/-/make-dir-2.1.0.tgz"
    integrity sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==
    dependencies:
      pify "^4.0.1"
      semver "^5.6.0"
  
  make-dir@^3.0.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz"
    integrity sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==
    dependencies:
      semver "^6.0.0"
  
  make-dir@~3.1.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz"
    integrity sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==
    dependencies:
      semver "^6.0.0"
  
  map-obj@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/map-obj/-/map-obj-1.0.1.tgz"
    integrity sha512-7N/q3lyZ+LVCp7PzuxrJr4KMbBE2hW7BT7YNia330OFxIf4d3r5zVpicP2650l7CPN6RM9zOJRl3NGpqSiw3Eg==
  
  map-obj@^4.0.0:
    version "4.3.0"
    resolved "https://registry.npmjs.org/map-obj/-/map-obj-4.3.0.tgz"
    integrity sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==
  
  mathml-tag-names@^2.1.3:
    version "2.1.3"
    resolved "https://registry.npmjs.org/mathml-tag-names/-/mathml-tag-names-2.1.3.tgz"
    integrity sha512-APMBEanjybaPzUrfqU0IMU5I0AswKMH7k8OTLs0vvV4KZpExkTkY87nR/zpbuTPj+gARop7aGUbl11pnDfW6xg==
  
  md5@^2.3.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/md5/-/md5-2.3.0.tgz"
    integrity sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==
    dependencies:
      charenc "0.0.2"
      crypt "0.0.2"
      is-buffer "~1.1.6"
  
  mdn-data@~1.1.0:
    version "1.1.4"
    resolved "https://registry.npmjs.org/mdn-data/-/mdn-data-1.1.4.tgz"
    integrity sha512-FSYbp3lyKjyj3E7fMl6rYvUdX0FBXaluGqlFoYESWQlyUTq8R+wp0rkFxoYFqZlHCvsUXGjyJmLQSnXToYhOSA==
  
  mdn-data@2.0.14:
    version "2.0.14"
    resolved "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz"
    integrity sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==
  
  mdn-data@2.0.28:
    version "2.0.28"
    resolved "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.28.tgz"
    integrity sha512-aylIc7Z9y4yzHYAJNuESG3hfhC+0Ibp/MAMiaOZgNv4pmEdFyfZhhhny4MNiAfWdBQ1RQ2mfDWmM1x8SvGyp8g==
  
  mdn-data@2.0.30:
    version "2.0.30"
    resolved "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.30.tgz"
    integrity sha512-GaqWWShW4kv/G9IEucWScBx9G1/vsFZZJUO+tD26M8J8z3Kw5RDQjaoZe03YAClgeS/SWPOcb4nkFBTEi5DUEA==
  
  media-typer@0.3.0:
    version "0.3.0"
    resolved "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
    integrity sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==
  
  mem-fs-editor@^9.6.0:
    version "9.7.0"
    resolved "https://registry.npmjs.org/mem-fs-editor/-/mem-fs-editor-9.7.0.tgz"
    integrity sha512-ReB3YD24GNykmu4WeUL/FDIQtkoyGB6zfJv60yfCo3QjKeimNcTqv2FT83bP0ccs6uu+sm5zyoBlspAzigmsdg==
    dependencies:
      binaryextensions "^4.16.0"
      commondir "^1.0.1"
      deep-extend "^0.6.0"
      ejs "^3.1.8"
      globby "^11.1.0"
      isbinaryfile "^5.0.0"
      minimatch "^7.2.0"
      multimatch "^5.0.0"
      normalize-path "^3.0.0"
      textextensions "^5.13.0"
  
  mem-fs@^2.1.0, mem-fs@^2.3.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/mem-fs/-/mem-fs-2.3.0.tgz"
    integrity sha512-GftCCBs6EN8sz3BoWO1bCj8t7YBtT713d8bUgbhg9Iel5kFSqnSvCK06TYIDJAtJ51cSiWkM/YemlT0dfoFycw==
    dependencies:
      "@types/node" "^15.6.2"
      "@types/vinyl" "^2.0.4"
      vinyl "^2.0.1"
      vinyl-file "^3.0.0"
  
  memfs@^3.4.3:
    version "3.4.7"
    resolved "https://registry.npmjs.org/memfs/-/memfs-3.4.7.tgz"
    integrity sha512-ygaiUSNalBX85388uskeCyhSAoOSgzBbtVCr9jA2RROssFL9Q19/ZXFqS+2Th2sr1ewNIWgFdLzLC3Yl1Zv+lw==
    dependencies:
      fs-monkey "^1.0.3"
  
  memoize-one@^6.0.0:
    version "6.0.0"
    resolved "https://registry.npmjs.org/memoize-one/-/memoize-one-6.0.0.tgz"
    integrity sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==
  
  meow@^9.0.0:
    version "9.0.0"
    resolved "https://registry.npmjs.org/meow/-/meow-9.0.0.tgz"
    integrity sha512-+obSblOQmRhcyBt62furQqRAQpNyWXo8BuQ5bN7dG8wmwQ+vwHKp/rCFD4CrTP8CsDQD1sjoZ94K417XEUk8IQ==
    dependencies:
      "@types/minimist" "^1.2.0"
      camelcase-keys "^6.2.2"
      decamelize "^1.2.0"
      decamelize-keys "^1.1.0"
      hard-rejection "^2.1.0"
      minimist-options "4.1.0"
      normalize-package-data "^3.0.0"
      read-pkg-up "^7.0.1"
      redent "^3.0.0"
      trim-newlines "^3.0.0"
      type-fest "^0.18.0"
      yargs-parser "^20.2.3"
  
  merge-descriptors@1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
    integrity sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==
  
  merge-source-map@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/merge-source-map/-/merge-source-map-1.1.0.tgz"
    integrity sha512-Qkcp7P2ygktpMPh2mCQZaf3jhN6D3Z/qVZHSdWvQ+2Ef5HgRAPBO57A77+ENm0CPx2+1Ce/MYKi3ymqdfuqibw==
    dependencies:
      source-map "^0.6.1"
  
  merge-stream@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
    integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==
  
  merge2@^1.3.0, merge2@^1.4.1:
    version "1.4.1"
    resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
    integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==
  
  methods@~1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
    integrity sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==
  
  metro-react-native-babel-preset@^0.72.1:
    version "0.72.3"
    resolved "https://registry.npmjs.org/metro-react-native-babel-preset/-/metro-react-native-babel-preset-0.72.3.tgz"
    integrity sha512-uJx9y/1NIqoYTp6ZW1osJ7U5ZrXGAJbOQ/Qzl05BdGYvN1S7Qmbzid6xOirgK0EIT0pJKEEh1s8qbassYZe4cw==
    dependencies:
      "@babel/core" "^7.14.0"
      "@babel/plugin-proposal-async-generator-functions" "^7.0.0"
      "@babel/plugin-proposal-class-properties" "^7.0.0"
      "@babel/plugin-proposal-export-default-from" "^7.0.0"
      "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
      "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
      "@babel/plugin-proposal-optional-catch-binding" "^7.0.0"
      "@babel/plugin-proposal-optional-chaining" "^7.0.0"
      "@babel/plugin-syntax-dynamic-import" "^7.0.0"
      "@babel/plugin-syntax-export-default-from" "^7.0.0"
      "@babel/plugin-syntax-flow" "^7.2.0"
      "@babel/plugin-syntax-nullish-coalescing-operator" "^7.0.0"
      "@babel/plugin-syntax-optional-chaining" "^7.0.0"
      "@babel/plugin-transform-arrow-functions" "^7.0.0"
      "@babel/plugin-transform-async-to-generator" "^7.0.0"
      "@babel/plugin-transform-block-scoping" "^7.0.0"
      "@babel/plugin-transform-classes" "^7.0.0"
      "@babel/plugin-transform-computed-properties" "^7.0.0"
      "@babel/plugin-transform-destructuring" "^7.0.0"
      "@babel/plugin-transform-exponentiation-operator" "^7.0.0"
      "@babel/plugin-transform-flow-strip-types" "^7.0.0"
      "@babel/plugin-transform-function-name" "^7.0.0"
      "@babel/plugin-transform-literals" "^7.0.0"
      "@babel/plugin-transform-modules-commonjs" "^7.0.0"
      "@babel/plugin-transform-named-capturing-groups-regex" "^7.0.0"
      "@babel/plugin-transform-parameters" "^7.0.0"
      "@babel/plugin-transform-react-display-name" "^7.0.0"
      "@babel/plugin-transform-react-jsx" "^7.0.0"
      "@babel/plugin-transform-react-jsx-self" "^7.0.0"
      "@babel/plugin-transform-react-jsx-source" "^7.0.0"
      "@babel/plugin-transform-runtime" "^7.0.0"
      "@babel/plugin-transform-shorthand-properties" "^7.0.0"
      "@babel/plugin-transform-spread" "^7.0.0"
      "@babel/plugin-transform-sticky-regex" "^7.0.0"
      "@babel/plugin-transform-template-literals" "^7.0.0"
      "@babel/plugin-transform-typescript" "^7.5.0"
      "@babel/plugin-transform-unicode-regex" "^7.0.0"
      "@babel/template" "^7.0.0"
      react-refresh "^0.4.0"
  
  micromatch@^4.0.2, micromatch@^4.0.4, micromatch@^4.0.5:
    version "4.0.5"
    resolved "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz"
    integrity sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==
    dependencies:
      braces "^3.0.2"
      picomatch "^2.3.1"
  
  mime-db@^1.28.0, "mime-db@>= 1.43.0 < 2", mime-db@1.52.0:
    version "1.52.0"
    resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
    integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==
  
  mime-types@^2.1.12, mime-types@^2.1.26, mime-types@^2.1.27, mime-types@^2.1.31, mime-types@~2.1.17, mime-types@~2.1.19, mime-types@~2.1.24, mime-types@~2.1.34:
    version "2.1.35"
    resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
    integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
    dependencies:
      mime-db "1.52.0"
  
  mime@^1.4.1, mime@1.6.0:
    version "1.6.0"
    resolved "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
    integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==
  
  mime@~2.5.2:
    version "2.5.2"
    resolved "https://registry.npmjs.org/mime/-/mime-2.5.2.tgz"
    integrity sha512-tqkh47FzKeCPD2PUiPB6pkbMzsCasjxAfC62/Wap5qrUWcb+sFasXUC5I3gYM5iBM8v/Qpn4UK0x+j0iHyFPDg==
  
  mimic-fn@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
    integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==
  
  mimic-response@^1.0.0, mimic-response@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/mimic-response/-/mimic-response-1.0.1.tgz"
    integrity sha512-j5EctnkH7amfV/q5Hgmoal1g2QHFJRraOtmx0JpIqkxhBhI/lJSl1nMpQ45hVarwNETOoWEimndZ4QK0RHxuxQ==
  
  min-indent@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/min-indent/-/min-indent-1.0.1.tgz"
    integrity sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==
  
  mini-css-extract-plugin@2.4.6:
    version "2.4.6"
    resolved "https://registry.npmjs.org/mini-css-extract-plugin/-/mini-css-extract-plugin-2.4.6.tgz"
    integrity sha512-khHpc29bdsE9EQiGSLqQieLyMbGca+bkC42/BBL1gXC8yAS0nHpOTUCBYUK6En1FuRdfE9wKXhGtsab8vmsugg==
    dependencies:
      schema-utils "^4.0.0"
  
  minimalistic-assert@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
    integrity sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==
  
  minimatch@^3.0.2, minimatch@^3.0.4, minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
    version "3.1.2"
    resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
    integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
    dependencies:
      brace-expansion "^1.1.7"
  
  minimatch@^5.0.1:
    version "5.1.6"
    resolved "https://registry.npmjs.org/minimatch/-/minimatch-5.1.6.tgz"
    integrity sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==
    dependencies:
      brace-expansion "^2.0.1"
  
  minimatch@^7.2.0:
    version "7.4.5"
    resolved "https://registry.npmjs.org/minimatch/-/minimatch-7.4.5.tgz"
    integrity sha512-OzOamaOmNBJZUv2qqY1OSWa+++4YPpOkLgkc0w30Oov5ufKlWWXnFUl0l4dgmSv5Shq/zRVkEOXAe2NaqO4l5Q==
    dependencies:
      brace-expansion "^2.0.1"
  
  minimatch@~3.0.4:
    version "3.0.8"
    resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.0.8.tgz"
    integrity sha512-6FsRAQsxQ61mw+qP1ZzbL9Bc78x2p5OqNgNpnoAFLTrX8n5Kxph0CsnhmKKNXTWjXqU5L0pGPR7hYk+XWZr60Q==
    dependencies:
      brace-expansion "^1.1.7"
  
  minimist-options@4.1.0:
    version "4.1.0"
    resolved "https://registry.npmjs.org/minimist-options/-/minimist-options-4.1.0.tgz"
    integrity sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A==
    dependencies:
      arrify "^1.0.1"
      is-plain-obj "^1.1.0"
      kind-of "^6.0.3"
  
  minimist@^1.2.0, minimist@^1.2.6, minimist@^1.2.8:
    version "1.2.8"
    resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
    integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==
  
  miniprogram-api-typings@^3.2.2:
    version "3.6.0"
    resolved "https://registry.npmjs.org/miniprogram-api-typings/-/miniprogram-api-typings-3.6.0.tgz"
    integrity sha512-xwK3PzhhxnfWqDfBikHLdAbj7Wy4F887nBcQrzwuF758Fw2qC4ivpKPL9t0uJZk5QYnU28+NqA7Q3lzYGMHQnA==
  
  miniprogram-compiler@latest:
    version "0.2.1"
    resolved "https://registry.npmjs.org/miniprogram-compiler/-/miniprogram-compiler-0.2.1.tgz"
    integrity sha512-AHQgJFTnVPMcVBUUdKHlCRC3guK212XkWME0awMQiUgpzp/eIbb3jgFdkl3ib9nDbbEK+m2fSBs0++vwetWJ9Q==
    dependencies:
      glob "^7.1.3"
      unescape-js "^1.1.1"
  
  miniprogram-exparser@2.15.0:
    version "2.15.0"
    resolved "https://registry.npmjs.org/miniprogram-exparser/-/miniprogram-exparser-2.15.0.tgz"
    integrity sha512-W6aS1R3oVTwYw5hPguRqICFqx3wk2dtPAcwT6269WeWRjuQslbVPZRW/nlN16bg0NM5eQFmfU49PM6/PQ5DE8w==
  
  miniprogram-simulate@^1.1.5:
    version "1.5.7"
    resolved "https://registry.npmjs.org/miniprogram-simulate/-/miniprogram-simulate-1.5.7.tgz"
    integrity sha512-RFfZSoDIc+PQTtDmkg9D1/Su+UVCzuWbTV/KM1Zr8IUfFNg6uxsKLdeUem9H5sk0a4dlH/o1Qv7QXZPFfcbAvA==
    dependencies:
      csso "^3.5.1"
      j-component "^1.4.6"
      less "^3.10.3"
      miniprogram-compiler latest
      postcss "^7.0.23"
  
  mkdirp@^0.5.6:
    version "0.5.6"
    resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz"
    integrity sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==
    dependencies:
      minimist "^1.2.6"
  
  mkdirp@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz"
    integrity sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==
  
  mkdirp@~1.0.4:
    version "1.0.4"
    resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz"
    integrity sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==
  
  mobile-detect@^1.4.2:
    version "1.4.5"
    resolved "https://registry.npmjs.org/mobile-detect/-/mobile-detect-1.4.5.tgz"
    integrity sha512-yc0LhH6tItlvfLBugVUEtgawwFU2sIe+cSdmRJJCTMZ5GEJyLxNyC/NIOAOGk67Fa8GNpOttO3Xz/1bHpXFD/g==
  
  moment@^2.29.4:
    version "2.29.4"
    resolved "https://registry.npmjs.org/moment/-/moment-2.29.4.tgz"
    integrity sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==
  
  ms@^2.1.1:
    version "2.1.3"
    resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
    integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==
  
  ms@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
    integrity sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==
  
  ms@2.1.2:
    version "2.1.2"
    resolved "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
    integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==
  
  ms@2.1.3:
    version "2.1.3"
    resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
    integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==
  
  multicast-dns-service-types@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz"
    integrity sha512-cnAsSVxIDsYt0v7HmC0hWZFwwXSh+E6PgCrREDuN/EsjgLwA5XRmlMHhSiDPrt6HxY1gTivEa/Zh7GtODoLevQ==
  
  multicast-dns@^6.0.1:
    version "6.2.3"
    resolved "https://registry.npmjs.org/multicast-dns/-/multicast-dns-6.2.3.tgz"
    integrity sha512-ji6J5enbMyGRHIAkAOu3WdV8nggqviKCEKtXcOqfphZZtQrmHKycfynJ2V7eVPUA4NhJ6V7Wf4TmGbTwKE9B6g==
    dependencies:
      dns-packet "^1.3.1"
      thunky "^1.0.2"
  
  multimatch@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/multimatch/-/multimatch-5.0.0.tgz"
    integrity sha512-ypMKuglUrZUD99Tk2bUQ+xNQj43lPEfAeX2o9cTteAmShXy2VHDJpuwu1o0xqoKCt9jLVAvwyFKdLTPXKAfJyA==
    dependencies:
      "@types/minimatch" "^3.0.3"
      array-differ "^3.0.0"
      array-union "^2.1.0"
      arrify "^2.0.1"
      minimatch "^3.0.4"
  
  mute-stream@0.0.8:
    version "0.0.8"
    resolved "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.8.tgz"
    integrity sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==
  
  mz@^2.4.0:
    version "2.7.0"
    resolved "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz"
    integrity sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==
    dependencies:
      any-promise "^1.0.0"
      object-assign "^4.0.1"
      thenify-all "^1.0.0"
  
  nanoid@^3.3.6:
    version "3.3.6"
    resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.6.tgz"
    integrity sha512-BGcqMMJuToF7i1rt+2PWSNVnWIkGCU78jBG3RxO/bZlnZPK2Cmi2QaffxGO/2RvWi9sL+FAiRiXMgsyxQ1DIDA==
  
  native-request@^1.0.5:
    version "1.1.0"
    resolved "https://registry.npmjs.org/native-request/-/native-request-1.1.0.tgz"
    integrity sha512-uZ5rQaeRn15XmpgE0xoPL8YWqcX90VtCFglYwAgkvKM5e8fog+vePLAhHxuuv/gRkrQxIeh5U3q9sMNUrENqWw==
  
  natural-compare-lite@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmjs.org/natural-compare-lite/-/natural-compare-lite-1.4.0.tgz"
    integrity sha512-Tj+HTDSJJKaZnfiuw+iaF9skdPpTo2GtEly5JHnWV/hfv2Qj/9RKsGISQtLh2ox3l5EAGw487hnBee0sIJ6v2g==
  
  natural-compare@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
    integrity sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==
  
  needle@^3.1.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/needle/-/needle-3.1.0.tgz"
    integrity sha512-gCE9weDhjVGCRqS8dwDR/D3GTAeyXLXuqp7I8EzH6DllZGXSUyxuqqLh+YX9rMAWaaTFyVAg6rHGL25dqvczKw==
    dependencies:
      debug "^3.2.6"
      iconv-lite "^0.6.3"
      sax "^1.2.4"
  
  negotiator@0.6.3:
    version "0.6.3"
    resolved "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz"
    integrity sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==
  
  neo-async@^2.6.2:
    version "2.6.2"
    resolved "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz"
    integrity sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==
  
  no-case@^2.2.0:
    version "2.3.2"
    resolved "https://registry.npmjs.org/no-case/-/no-case-2.3.2.tgz"
    integrity sha512-rmTZ9kz+f3rCvK2TD1Ue/oZlns7OGoIWP4fc3llxxRXlOkHKoWPPWJOfFYpITabSow43QJbRIoHQXtt10VldyQ==
    dependencies:
      lower-case "^1.1.1"
  
  no-case@^3.0.4:
    version "3.0.4"
    resolved "https://registry.npmjs.org/no-case/-/no-case-3.0.4.tgz"
    integrity sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==
    dependencies:
      lower-case "^2.0.2"
      tslib "^2.0.3"
  
  node-emoji@^1.10.0:
    version "1.11.0"
    resolved "https://registry.npmjs.org/node-emoji/-/node-emoji-1.11.0.tgz"
    integrity sha512-wo2DpQkQp7Sjm2A0cq+sN7EHKO6Sl0ctXeBdFZrL9T9+UywORbufTcTZxom8YqpLQt/FqNMUkOpkZrJVYSKD3A==
    dependencies:
      lodash "^4.17.21"
  
  node-forge@^1:
    version "1.3.1"
    resolved "https://registry.npmjs.org/node-forge/-/node-forge-1.3.1.tgz"
    integrity sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==
  
  node-releases@^2.0.6:
    version "2.0.6"
    resolved "https://registry.npmjs.org/node-releases/-/node-releases-2.0.6.tgz"
    integrity sha512-PiVXnNuFm5+iYkLBNeq5211hvO38y63T0i2KKh2KnUs3RpzJ+JtODFjkD8yjLwnDkTYF1eKXheUwdssR+NRZdg==
  
  normalize-package-data@^2.5.0:
    version "2.5.0"
    resolved "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
    integrity sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==
    dependencies:
      hosted-git-info "^2.1.4"
      resolve "^1.10.0"
      semver "2 || 3 || 4 || 5"
      validate-npm-package-license "^3.0.1"
  
  normalize-package-data@^3.0.0:
    version "3.0.3"
    resolved "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-3.0.3.tgz"
    integrity sha512-p2W1sgqij3zMMyRC067Dg16bfzVH+w7hyegmpIvZ4JNjqtGOVAIvLmjBx3yP7YTe9vKJgkoNOPjwQGogDoMXFA==
    dependencies:
      hosted-git-info "^4.0.1"
      is-core-module "^2.5.0"
      semver "^7.3.4"
      validate-npm-package-license "^3.0.1"
  
  normalize-path@^3.0.0, normalize-path@~3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
    integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==
  
  normalize-range@^0.1.2:
    version "0.1.2"
    resolved "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
    integrity sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==
  
  normalize-url@^4.1.0:
    version "4.5.1"
    resolved "https://registry.npmjs.org/normalize-url/-/normalize-url-4.5.1.tgz"
    integrity sha512-9UZCFRHQdNrfTpGg8+1INIg93B6zE0aXMVFkw1WFwvO4SlZywU6aLg5Of0Ap/PgcbSw4LNxvMWXMeugwMCX0AA==
  
  normalize-url@^6.0.1:
    version "6.1.0"
    resolved "https://registry.npmjs.org/normalize-url/-/normalize-url-6.1.0.tgz"
    integrity sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A==
  
  normalize-url@2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/normalize-url/-/normalize-url-2.0.1.tgz"
    integrity sha512-D6MUW4K/VzoJ4rJ01JFKxDrtY1v9wrgzCX5f2qj/lzH1m/lW6MhUZFKerVsnyjOhOsYzI9Kqqak+10l4LvLpMw==
    dependencies:
      prepend-http "^2.0.0"
      query-string "^5.0.1"
      sort-keys "^2.0.0"
  
  npm-check@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmjs.org/npm-check/-/npm-check-6.0.1.tgz"
    integrity sha512-tlEhXU3689VLUHYEZTS/BC61vfeN2xSSZwoWDT6WLuenZTpDmGmNT5mtl15erTR0/A15ldK06/NEKg9jYJ9OTQ==
    dependencies:
      callsite-record "^4.1.3"
      chalk "^4.1.0"
      co "^4.6.0"
      depcheck "^1.3.1"
      execa "^5.0.0"
      giturl "^1.0.0"
      global-modules "^2.0.0"
      globby "^11.0.2"
      inquirer "^7.3.3"
      is-ci "^2.0.0"
      lodash "^4.17.20"
      meow "^9.0.0"
      minimatch "^3.0.2"
      node-emoji "^1.10.0"
      ora "^5.3.0"
      package-json "^6.5.0"
      path-exists "^4.0.0"
      pkg-dir "^5.0.0"
      preferred-pm "^3.0.3"
      rc-config-loader "^4.0.0"
      semver "^7.3.4"
      semver-diff "^3.1.1"
      strip-ansi "^6.0.0"
      text-table "^0.2.0"
      throat "^6.0.1"
      update-notifier "^5.1.0"
      xtend "^4.0.2"
  
  npm-conf@^1.1.0:
    version "1.1.3"
    resolved "https://registry.npmjs.org/npm-conf/-/npm-conf-1.1.3.tgz"
    integrity sha512-Yic4bZHJOt9RCFbRP3GgpqhScOY4HH3V2P8yBj6CeYq118Qr+BLXqT2JvpJ00mryLESpgOxf5XlFv4ZjXxLScw==
    dependencies:
      config-chain "^1.1.11"
      pify "^3.0.0"
  
  npm-run-path@^4.0.1:
    version "4.0.1"
    resolved "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz"
    integrity sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==
    dependencies:
      path-key "^3.0.0"
  
  nth-check@^2.0.1:
    version "2.1.1"
    resolved "https://registry.npmjs.org/nth-check/-/nth-check-2.1.1.tgz"
    integrity sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==
    dependencies:
      boolbase "^1.0.0"
  
  num2fraction@^1.2.2:
    version "1.2.2"
    resolved "https://registry.npmjs.org/num2fraction/-/num2fraction-1.2.2.tgz"
    integrity sha512-Y1wZESM7VUThYY+4W+X4ySH2maqcA+p7UR+w8VWNWVAd6lwuXXWz/w/Cz43J/dI2I+PS6wD5N+bJUF+gjWvIqg==
  
  nwsapi@^2.2.2:
    version "2.2.3"
    resolved "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.3.tgz"
    integrity sha512-jscxIO4/VKScHlbmFBdV1Z6LXnLO+ZR4VMtypudUdfwtKxUN3TQcNFIHLwKtrUbDyHN4/GycY9+oRGZ2XMXYPw==
  
  oauth-sign@~0.9.0:
    version "0.9.0"
    resolved "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz"
    integrity sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==
  
  object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
    version "4.1.1"
    resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
    integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==
  
  object-inspect@^1.12.3, object-inspect@^1.9.0:
    version "1.12.3"
    resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.3.tgz"
    integrity sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==
  
  object-is@^1.0.1:
    version "1.1.5"
    resolved "https://registry.npmjs.org/object-is/-/object-is-1.1.5.tgz"
    integrity sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.3"
  
  object-keys@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
    integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==
  
  object.assign@^4.1.0, object.assign@^4.1.3, object.assign@^4.1.4:
    version "4.1.4"
    resolved "https://registry.npmjs.org/object.assign/-/object.assign-4.1.4.tgz"
    integrity sha512-1mxKf0e58bvyjSCtKYY4sRe9itRk3PJpquJOjeIkz885CczcI4IvJJDLPS72oowuSh+pBxUFROpX+TU++hxhZQ==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      has-symbols "^1.0.3"
      object-keys "^1.1.1"
  
  object.entries@^1.1.6:
    version "1.1.6"
    resolved "https://registry.npmjs.org/object.entries/-/object.entries-1.1.6.tgz"
    integrity sha512-leTPzo4Zvg3pmbQ3rDK69Rl8GQvIqMWubrkxONG9/ojtFE2rD9fjMKfSI5BxW3osRH1m6VdzmqK8oAY9aT4x5w==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
  
  object.fromentries@^2.0.6:
    version "2.0.6"
    resolved "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.6.tgz"
    integrity sha512-VciD13dswC4j1Xt5394WR4MzmAQmlgN72phd/riNp9vtD7tp4QQWJ0R4wvclXcafgcYK8veHRed2W6XeGBvcfg==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
  
  object.hasown@^1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/object.hasown/-/object.hasown-1.1.2.tgz"
    integrity sha512-B5UIT3J1W+WuWIU55h0mjlwaqxiE5vYENJXIXZ4VFe05pNYrkKuK0U/6aFcb0pKywYJh7IhfoqUfKVmrJJHZHw==
    dependencies:
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
  
  object.values@^1.1.6:
    version "1.1.6"
    resolved "https://registry.npmjs.org/object.values/-/object.values-1.1.6.tgz"
    integrity sha512-FVVTkD1vENCsAcwNs9k6jea2uHC/X0+JcjG8YA60FN5CMaJmG95wT9jek/xX9nornqGRrBkKtzuAu2wuHpKqvw==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
  
  obuf@^1.0.0, obuf@^1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz"
    integrity sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==
  
  on-finished@2.4.1:
    version "2.4.1"
    resolved "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz"
    integrity sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==
    dependencies:
      ee-first "1.1.1"
  
  on-headers@~1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz"
    integrity sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==
  
  once@^1.3.0, once@^1.3.1, once@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
    integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
    dependencies:
      wrappy "1"
  
  onetime@^5.1.0, onetime@^5.1.2:
    version "5.1.2"
    resolved "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz"
    integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
    dependencies:
      mimic-fn "^2.1.0"
  
  open@^8.0.9:
    version "8.4.0"
    resolved "https://registry.npmjs.org/open/-/open-8.4.0.tgz"
    integrity sha512-XgFPPM+B28FtCCgSb9I+s9szOC1vZRSwgWsRUA5ylIxRTgKozqjOCrVOqGsYABPYK5qnfqClxZTFBa8PKt2v6Q==
    dependencies:
      define-lazy-prop "^2.0.0"
      is-docker "^2.1.1"
      is-wsl "^2.2.0"
  
  optionator@^0.8.1:
    version "0.8.3"
    resolved "https://registry.npmjs.org/optionator/-/optionator-0.8.3.tgz"
    integrity sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==
    dependencies:
      deep-is "~0.1.3"
      fast-levenshtein "~2.0.6"
      levn "~0.3.0"
      prelude-ls "~1.1.2"
      type-check "~0.3.2"
      word-wrap "~1.2.3"
  
  optionator@^0.9.1:
    version "0.9.1"
    resolved "https://registry.npmjs.org/optionator/-/optionator-0.9.1.tgz"
    integrity sha512-74RlY5FCnhq4jRxVUPKDaRwrVNXMqsGsiW6AJw4XK8hmtm10wC0ypZBLw5IIp85NZMr91+qd1RvvENwg7jjRFw==
    dependencies:
      deep-is "^0.1.3"
      fast-levenshtein "^2.0.6"
      levn "^0.4.1"
      prelude-ls "^1.2.1"
      type-check "^0.4.0"
      word-wrap "^1.2.3"
  
  ora@^5.0.0, ora@^5.3.0, ora@^5.4.1:
    version "5.4.1"
    resolved "https://registry.npmjs.org/ora/-/ora-5.4.1.tgz"
    integrity sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==
    dependencies:
      bl "^4.1.0"
      chalk "^4.1.0"
      cli-cursor "^3.1.0"
      cli-spinners "^2.5.0"
      is-interactive "^1.0.0"
      is-unicode-supported "^0.1.0"
      log-symbols "^4.1.0"
      strip-ansi "^6.0.0"
      wcwidth "^1.0.1"
  
  os-tmpdir@~1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
    integrity sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==
  
  p-cancelable@^0.4.0:
    version "0.4.1"
    resolved "https://registry.npmjs.org/p-cancelable/-/p-cancelable-0.4.1.tgz"
    integrity sha512-HNa1A8LvB1kie7cERyy21VNeHb2CWJJYqyyC2o3klWFfMGlFmWv2Z7sFgZH8ZiaYL95ydToKTFVXgMV/Os0bBQ==
  
  p-cancelable@^1.0.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/p-cancelable/-/p-cancelable-1.1.0.tgz"
    integrity sha512-s73XxOZ4zpt1edZYZzvhqFa6uvQc1vwUa0K0BdtIZgQMAJj9IbebH+JkgKZc9h+B05PKHLOTl4ajG1BmNrVZlw==
  
  p-event@^2.1.0:
    version "2.3.1"
    resolved "https://registry.npmjs.org/p-event/-/p-event-2.3.1.tgz"
    integrity sha512-NQCqOFhbpVTMX4qMe8PF8lbGtzZ+LCiN7pcNrb/413Na7+TRoe1xkKUzuWa/YEJdGQ0FvKtj35EEbDoVPO2kbA==
    dependencies:
      p-timeout "^2.0.1"
  
  p-finally@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz"
    integrity sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==
  
  p-is-promise@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/p-is-promise/-/p-is-promise-1.1.0.tgz"
    integrity sha512-zL7VE4JVS2IFSkR2GQKDSPEVxkoH43/p7oEnwpdCndKYJO0HVeRB7fA8TJwuLOTBREtK0ea8eHaxdwcpob5dmg==
  
  p-limit@^2.0.0, p-limit@^2.2.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
    integrity sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
    dependencies:
      p-try "^2.0.0"
  
  p-limit@^3.0.2:
    version "3.1.0"
    resolved "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
    integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
    dependencies:
      yocto-queue "^0.1.0"
  
  p-locate@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/p-locate/-/p-locate-3.0.0.tgz"
    integrity sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==
    dependencies:
      p-limit "^2.0.0"
  
  p-locate@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
    integrity sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==
    dependencies:
      p-limit "^2.2.0"
  
  p-locate@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz"
    integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
    dependencies:
      p-limit "^3.0.2"
  
  p-map@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/p-map/-/p-map-4.0.0.tgz"
    integrity sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==
    dependencies:
      aggregate-error "^3.0.0"
  
  p-retry@^4.5.0:
    version "4.6.2"
    resolved "https://registry.npmjs.org/p-retry/-/p-retry-4.6.2.tgz"
    integrity sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ==
    dependencies:
      "@types/retry" "0.12.0"
      retry "^0.13.1"
  
  p-timeout@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/p-timeout/-/p-timeout-2.0.1.tgz"
    integrity sha512-88em58dDVB/KzPEx1X0N3LwFfYZPyDc4B6eF38M1rk9VTZMbxXXgjugz8mmwpS9Ox4BDZ+t6t3QP5+/gazweIA==
    dependencies:
      p-finally "^1.0.0"
  
  p-try@^2.0.0:
    version "2.2.0"
    resolved "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
    integrity sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==
  
  package-json@^6.3.0, package-json@^6.5.0:
    version "6.5.0"
    resolved "https://registry.npmjs.org/package-json/-/package-json-6.5.0.tgz"
    integrity sha512-k3bdm2n25tkyxcjSKzB5x8kfVxlMdgsbPr0GkZcwHsLpba6cBjqCt1KlcChKEvxHIcTB1FVMuwoijZ26xex5MQ==
    dependencies:
      got "^9.6.0"
      registry-auth-token "^4.0.0"
      registry-url "^5.0.0"
      semver "^6.2.0"
  
  param-case@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmjs.org/param-case/-/param-case-2.1.1.tgz"
    integrity sha512-eQE845L6ot89sk2N8liD8HAuH4ca6Vvr7VWAWwt7+kvvG5aBcPmmphQ68JsEG2qa9n1TykS2DLeMt363AAH8/w==
    dependencies:
      no-case "^2.2.0"
  
  param-case@^3.0.4:
    version "3.0.4"
    resolved "https://registry.npmjs.org/param-case/-/param-case-3.0.4.tgz"
    integrity sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==
    dependencies:
      dot-case "^3.0.4"
      tslib "^2.0.3"
  
  parent-module@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
    integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
    dependencies:
      callsites "^3.0.0"
  
  parse-json@^5.0.0:
    version "5.2.0"
    resolved "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
    integrity sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
    dependencies:
      "@babel/code-frame" "^7.0.0"
      error-ex "^1.3.1"
      json-parse-even-better-errors "^2.3.0"
      lines-and-columns "^1.1.6"
  
  parse-node-version@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/parse-node-version/-/parse-node-version-1.0.1.tgz"
    integrity sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==
  
  parse5-htmlparser2-tree-adapter@^6.0.0:
    version "6.0.1"
    resolved "https://registry.npmjs.org/parse5-htmlparser2-tree-adapter/-/parse5-htmlparser2-tree-adapter-6.0.1.tgz"
    integrity sha512-qPuWvbLgvDGilKc5BoicRovlT4MtYT6JfJyBOMDsKoiT+GiuP5qyrPCnR9HcPECIJJmZh5jRndyNThnhhb/vlA==
    dependencies:
      parse5 "^6.0.1"
  
  parse5@^5.1.1:
    version "5.1.1"
    resolved "https://registry.npmjs.org/parse5/-/parse5-5.1.1.tgz"
    integrity sha512-ugq4DFI0Ptb+WWjAdOK16+u/nHfiIrcE+sh8kZMaM0WllQKLI9rOUq6c2b7cwPkXdzfQESqvoqK6ug7U/Yyzug==
  
  parse5@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmjs.org/parse5/-/parse5-6.0.1.tgz"
    integrity sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==
  
  parse5@^7.1.2:
    version "7.1.2"
    resolved "https://registry.npmjs.org/parse5/-/parse5-7.1.2.tgz"
    integrity sha512-Czj1WaSVpaoj0wbhMzLmWD69anp2WH7FXMB9n1Sy8/ZFF9jolSQVMu1Ij5WIyGmcBmhk7EOndpO4mIpihVqAXw==
    dependencies:
      entities "^4.4.0"
  
  parseurl@~1.3.2, parseurl@~1.3.3:
    version "1.3.3"
    resolved "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
    integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==
  
  pascal-case@^3.1.2:
    version "3.1.2"
    resolved "https://registry.npmjs.org/pascal-case/-/pascal-case-3.1.2.tgz"
    integrity sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==
    dependencies:
      no-case "^3.0.4"
      tslib "^2.0.3"
  
  path-exists@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz"
    integrity sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ==
  
  path-exists@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
    integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==
  
  path-is-absolute@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
    integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==
  
  path-key@^3.0.0, path-key@^3.1.0:
    version "3.1.1"
    resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
    integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==
  
  path-parse@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
    integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==
  
  path-to-regexp@^1.7.0:
    version "1.8.0"
    resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.8.0.tgz"
    integrity sha512-n43JRhlUKUAlibEJhPeir1ncUID16QnEjNpwzNdO3Lm4ywrBpBZ5oLD0I6br9evr1Y9JTqwRtAh7JLoOzAQdVA==
    dependencies:
      isarray "0.0.1"
  
  path-to-regexp@^3.1.0:
    version "3.2.0"
    resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-3.2.0.tgz"
    integrity sha512-jczvQbCUS7XmS7o+y1aEO9OBVFeZBQ1MDSEqmO7xSoPgOPoowY/SxLpZ6Vh97/8qHZOteiCKb7gkG9gA2ZUxJA==
  
  path-to-regexp@0.1.7:
    version "0.1.7"
    resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
    integrity sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==
  
  path-type@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
    integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==
  
  pend@~1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz"
    integrity sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==
  
  performance-now@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
    integrity sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==
  
  picocolors@^0.2.1:
    version "0.2.1"
    resolved "https://registry.npmjs.org/picocolors/-/picocolors-0.2.1.tgz"
    integrity sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==
  
  picocolors@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz"
    integrity sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==
  
  picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
    version "2.3.1"
    resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
    integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==
  
  pify@^2.3.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
    integrity sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==
  
  pify@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/pify/-/pify-3.0.0.tgz"
    integrity sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg==
  
  pify@^4.0.1:
    version "4.0.1"
    resolved "https://registry.npmjs.org/pify/-/pify-4.0.1.tgz"
    integrity sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==
  
  pinkie-promise@^2.0.0:
    version "2.0.1"
    resolved "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz"
    integrity sha512-0Gni6D4UcLTbv9c57DfxDGdr41XfgUjqWZu492f0cIGr16zDU06BWP/RAEvOuo7CQ0CNjHaLlM59YJJFm3NWlw==
    dependencies:
      pinkie "^2.0.0"
  
  pinkie@^2.0.0:
    version "2.0.4"
    resolved "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz"
    integrity sha512-MnUuEycAemtSaeFSjXKW/aroV7akBbY+Sv+RkyqFjgAe73F+MR0TBWKBRDkmfWq/HiFmdavfZ1G7h4SPZXaCSg==
  
  pirates@^4.0.1, pirates@^4.0.5:
    version "4.0.5"
    resolved "https://registry.npmjs.org/pirates/-/pirates-4.0.5.tgz"
    integrity sha512-8V9+HQPupnaXMA23c5hvl69zXvTwTzyAYasnkb0Tts4XvO4CliqONMOnvlq26rkhLC3nWDFBJf73LU1e1VZLaQ==
  
  pkg-dir@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/pkg-dir/-/pkg-dir-3.0.0.tgz"
    integrity sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw==
    dependencies:
      find-up "^3.0.0"
  
  pkg-dir@^4.2.0:
    version "4.2.0"
    resolved "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz"
    integrity sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==
    dependencies:
      find-up "^4.0.0"
  
  pkg-dir@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/pkg-dir/-/pkg-dir-5.0.0.tgz"
    integrity sha512-NPE8TDbzl/3YQYY7CSS228s3g2ollTFnc+Qi3tqmqJp9Vg2ovUpixcJEo2HJScN2Ez+kEaal6y70c0ehqJBJeA==
    dependencies:
      find-up "^5.0.0"
  
  please-upgrade-node@^3.2.0:
    version "3.2.0"
    resolved "https://registry.npmjs.org/please-upgrade-node/-/please-upgrade-node-3.2.0.tgz"
    integrity sha512-gQR3WpIgNIKwBMVLkpMUeR3e1/E1y42bqDQZfql+kDeXd8COYfM8PQA4X6y7a8u9Ua9FHmsrrmirW2vHs45hWg==
    dependencies:
      semver-compare "^1.0.0"
  
  portfinder@^1.0.28:
    version "1.0.32"
    resolved "https://registry.npmjs.org/portfinder/-/portfinder-1.0.32.tgz"
    integrity sha512-on2ZJVVDXRADWE6jnQaX0ioEylzgBpQk8r55NE4wjXW1ZxO+BgDlY6DXwj20i0V8eB4SenDQ00WEaxfiIQPcxg==
    dependencies:
      async "^2.6.4"
      debug "^3.2.7"
      mkdirp "^0.5.6"
  
  postcss-calc@^8.2.3:
    version "8.2.4"
    resolved "https://registry.npmjs.org/postcss-calc/-/postcss-calc-8.2.4.tgz"
    integrity sha512-SmWMSJmB8MRnnULldx0lQIyhSNvuDl9HfrZkaqqE/WHAhToYsAvDq+yAsA/kIyINDszOp3Rh0GFoNuH5Ypsm3Q==
    dependencies:
      postcss-selector-parser "^6.0.9"
      postcss-value-parser "^4.2.0"
  
  postcss-colormin@^5.3.0:
    version "5.3.0"
    resolved "https://registry.npmjs.org/postcss-colormin/-/postcss-colormin-5.3.0.tgz"
    integrity sha512-WdDO4gOFG2Z8n4P8TWBpshnL3JpmNmJwdnfP2gbk2qBA8PWwOYcmjmI/t3CmMeL72a7Hkd+x/Mg9O2/0rD54Pg==
    dependencies:
      browserslist "^4.16.6"
      caniuse-api "^3.0.0"
      colord "^2.9.1"
      postcss-value-parser "^4.2.0"
  
  postcss-convert-values@^5.1.2:
    version "5.1.2"
    resolved "https://registry.npmjs.org/postcss-convert-values/-/postcss-convert-values-5.1.2.tgz"
    integrity sha512-c6Hzc4GAv95B7suy4udszX9Zy4ETyMCgFPUDtWjdFTKH1SE9eFY/jEpHSwTH1QPuwxHpWslhckUQWbNRM4ho5g==
    dependencies:
      browserslist "^4.20.3"
      postcss-value-parser "^4.2.0"
  
  postcss-discard-comments@^5.1.2:
    version "5.1.2"
    resolved "https://registry.npmjs.org/postcss-discard-comments/-/postcss-discard-comments-5.1.2.tgz"
    integrity sha512-+L8208OVbHVF2UQf1iDmRcbdjJkuBF6IS29yBDSiWUIzpYaAhtNl6JYnYm12FnkeCwQqF5LeklOu6rAqgfBZqQ==
  
  postcss-discard-duplicates@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/postcss-discard-duplicates/-/postcss-discard-duplicates-5.1.0.tgz"
    integrity sha512-zmX3IoSI2aoenxHV6C7plngHWWhUOV3sP1T8y2ifzxzbtnuhk1EdPwm0S1bIUNaJ2eNbWeGLEwzw8huPD67aQw==
  
  postcss-discard-empty@^5.1.1:
    version "5.1.1"
    resolved "https://registry.npmjs.org/postcss-discard-empty/-/postcss-discard-empty-5.1.1.tgz"
    integrity sha512-zPz4WljiSuLWsI0ir4Mcnr4qQQ5e1Ukc3i7UfE2XcrwKK2LIPIqE5jxMRxO6GbI3cv//ztXDsXwEWT3BHOGh3A==
  
  postcss-discard-overridden@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/postcss-discard-overridden/-/postcss-discard-overridden-5.1.0.tgz"
    integrity sha512-21nOL7RqWR1kasIVdKs8HNqQJhFxLsyRfAnUDm4Fe4t4mCWL9OJiHvlHPjcd8zc5Myu89b/7wZDnOSjFgeWRtw==
  
  postcss-html-transform@3.6.6:
    version "3.6.6"
    resolved "https://registry.npmjs.org/postcss-html-transform/-/postcss-html-transform-3.6.6.tgz"
    integrity sha512-M8QO8vwgfS3zAug1O8XW1ETKoRwPXs8jai+Otn0kIxALZXV2vKo8vbu1f3JNbav6U6yn7xjtv3mtiKfFKfygnw==
  
  postcss-import@^14.0.0:
    version "14.1.0"
    resolved "https://registry.npmjs.org/postcss-import/-/postcss-import-14.1.0.tgz"
    integrity sha512-flwI+Vgm4SElObFVPpTIT7SU7R3qk2L7PyduMcokiaVKuWv9d/U+Gm/QAd8NDLuykTWTkcrjOeD2Pp1rMeBTGw==
    dependencies:
      postcss-value-parser "^4.0.0"
      read-cache "^1.0.0"
      resolve "^1.1.7"
  
  postcss-loader@^7.0.1:
    version "7.0.2"
    resolved "https://registry.npmjs.org/postcss-loader/-/postcss-loader-7.0.2.tgz"
    integrity sha512-fUJzV/QH7NXUAqV8dWJ9Lg4aTkDCezpTS5HgJ2DvqznexTbSTxgi/dTECvTZ15BwKTtk8G/bqI/QTu2HPd3ZCg==
    dependencies:
      cosmiconfig "^7.0.0"
      klona "^2.0.5"
      semver "^7.3.8"
  
  postcss-media-query-parser@^0.2.3:
    version "0.2.3"
    resolved "https://registry.npmjs.org/postcss-media-query-parser/-/postcss-media-query-parser-0.2.3.tgz"
    integrity sha512-3sOlxmbKcSHMjlUXQZKQ06jOswE7oVkXPxmZdoB1r5l0q6gTFTQSHxNxOrCccElbW7dxNytifNEo8qidX2Vsig==
  
  postcss-merge-longhand@^5.1.6:
    version "5.1.6"
    resolved "https://registry.npmjs.org/postcss-merge-longhand/-/postcss-merge-longhand-5.1.6.tgz"
    integrity sha512-6C/UGF/3T5OE2CEbOuX7iNO63dnvqhGZeUnKkDeifebY0XqkkvrctYSZurpNE902LDf2yKwwPFgotnfSoPhQiw==
    dependencies:
      postcss-value-parser "^4.2.0"
      stylehacks "^5.1.0"
  
  postcss-merge-rules@^5.1.2:
    version "5.1.2"
    resolved "https://registry.npmjs.org/postcss-merge-rules/-/postcss-merge-rules-5.1.2.tgz"
    integrity sha512-zKMUlnw+zYCWoPN6yhPjtcEdlJaMUZ0WyVcxTAmw3lkkN/NDMRkOkiuctQEoWAOvH7twaxUUdvBWl0d4+hifRQ==
    dependencies:
      browserslist "^4.16.6"
      caniuse-api "^3.0.0"
      cssnano-utils "^3.1.0"
      postcss-selector-parser "^6.0.5"
  
  postcss-minify-font-values@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/postcss-minify-font-values/-/postcss-minify-font-values-5.1.0.tgz"
    integrity sha512-el3mYTgx13ZAPPirSVsHqFzl+BBBDrXvbySvPGFnQcTI4iNslrPaFq4muTkLZmKlGk4gyFAYUBMH30+HurREyA==
    dependencies:
      postcss-value-parser "^4.2.0"
  
  postcss-minify-gradients@^5.1.1:
    version "5.1.1"
    resolved "https://registry.npmjs.org/postcss-minify-gradients/-/postcss-minify-gradients-5.1.1.tgz"
    integrity sha512-VGvXMTpCEo4qHTNSa9A0a3D+dxGFZCYwR6Jokk+/3oB6flu2/PnPXAh2x7x52EkY5xlIHLm+Le8tJxe/7TNhzw==
    dependencies:
      colord "^2.9.1"
      cssnano-utils "^3.1.0"
      postcss-value-parser "^4.2.0"
  
  postcss-minify-params@^5.1.3:
    version "5.1.3"
    resolved "https://registry.npmjs.org/postcss-minify-params/-/postcss-minify-params-5.1.3.tgz"
    integrity sha512-bkzpWcjykkqIujNL+EVEPOlLYi/eZ050oImVtHU7b4lFS82jPnsCb44gvC6pxaNt38Els3jWYDHTjHKf0koTgg==
    dependencies:
      browserslist "^4.16.6"
      cssnano-utils "^3.1.0"
      postcss-value-parser "^4.2.0"
  
  postcss-minify-selectors@^5.2.1:
    version "5.2.1"
    resolved "https://registry.npmjs.org/postcss-minify-selectors/-/postcss-minify-selectors-5.2.1.tgz"
    integrity sha512-nPJu7OjZJTsVUmPdm2TcaiohIwxP+v8ha9NehQ2ye9szv4orirRU3SDdtUmKH+10nzn0bAyOXZ0UEr7OpvLehg==
    dependencies:
      postcss-selector-parser "^6.0.5"
  
  postcss-modules-extract-imports@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.0.0.tgz"
    integrity sha512-bdHleFnP3kZ4NYDhuGlVK+CMrQ/pqUm8bx/oGL93K6gVwiclvX5x0n76fYMKuIGKzlABOy13zsvqjb0f92TEXw==
  
  postcss-modules-local-by-default@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.0.0.tgz"
    integrity sha512-sT7ihtmGSF9yhm6ggikHdV0hlziDTX7oFoXtuVWeDd3hHObNkcHRo9V3yg7vCAY7cONyxJC/XXCmmiHHcvX7bQ==
    dependencies:
      icss-utils "^5.0.0"
      postcss-selector-parser "^6.0.2"
      postcss-value-parser "^4.1.0"
  
  postcss-modules-scope@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-3.0.0.tgz"
    integrity sha512-hncihwFA2yPath8oZ15PZqvWGkWf+XUfQgUGamS4LqoP1anQLOsOJw0vr7J7IwLpoY9fatA2qiGUGmuZL0Iqlg==
    dependencies:
      postcss-selector-parser "^6.0.4"
  
  postcss-modules-values@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz"
    integrity sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==
    dependencies:
      icss-utils "^5.0.0"
  
  postcss-normalize-charset@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/postcss-normalize-charset/-/postcss-normalize-charset-5.1.0.tgz"
    integrity sha512-mSgUJ+pd/ldRGVx26p2wz9dNZ7ji6Pn8VWBajMXFf8jk7vUoSrZ2lt/wZR7DtlZYKesmZI680qjr2CeFF2fbUg==
  
  postcss-normalize-display-values@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/postcss-normalize-display-values/-/postcss-normalize-display-values-5.1.0.tgz"
    integrity sha512-WP4KIM4o2dazQXWmFaqMmcvsKmhdINFblgSeRgn8BJ6vxaMyaJkwAzpPpuvSIoG/rmX3M+IrRZEz2H0glrQNEA==
    dependencies:
      postcss-value-parser "^4.2.0"
  
  postcss-normalize-positions@^5.1.1:
    version "5.1.1"
    resolved "https://registry.npmjs.org/postcss-normalize-positions/-/postcss-normalize-positions-5.1.1.tgz"
    integrity sha512-6UpCb0G4eofTCQLFVuI3EVNZzBNPiIKcA1AKVka+31fTVySphr3VUgAIULBhxZkKgwLImhzMR2Bw1ORK+37INg==
    dependencies:
      postcss-value-parser "^4.2.0"
  
  postcss-normalize-repeat-style@^5.1.1:
    version "5.1.1"
    resolved "https://registry.npmjs.org/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-5.1.1.tgz"
    integrity sha512-mFpLspGWkQtBcWIRFLmewo8aC3ImN2i/J3v8YCFUwDnPu3Xz4rLohDO26lGjwNsQxB3YF0KKRwspGzE2JEuS0g==
    dependencies:
      postcss-value-parser "^4.2.0"
  
  postcss-normalize-string@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/postcss-normalize-string/-/postcss-normalize-string-5.1.0.tgz"
    integrity sha512-oYiIJOf4T9T1N4i+abeIc7Vgm/xPCGih4bZz5Nm0/ARVJ7K6xrDlLwvwqOydvyL3RHNf8qZk6vo3aatiw/go3w==
    dependencies:
      postcss-value-parser "^4.2.0"
  
  postcss-normalize-timing-functions@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-5.1.0.tgz"
    integrity sha512-DOEkzJ4SAXv5xkHl0Wa9cZLF3WCBhF3o1SKVxKQAa+0pYKlueTpCgvkFAHfk+Y64ezX9+nITGrDZeVGgITJXjg==
    dependencies:
      postcss-value-parser "^4.2.0"
  
  postcss-normalize-unicode@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/postcss-normalize-unicode/-/postcss-normalize-unicode-5.1.0.tgz"
    integrity sha512-J6M3MizAAZ2dOdSjy2caayJLQT8E8K9XjLce8AUQMwOrCvjCHv24aLC/Lps1R1ylOfol5VIDMaM/Lo9NGlk1SQ==
    dependencies:
      browserslist "^4.16.6"
      postcss-value-parser "^4.2.0"
  
  postcss-normalize-url@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/postcss-normalize-url/-/postcss-normalize-url-5.1.0.tgz"
    integrity sha512-5upGeDO+PVthOxSmds43ZeMeZfKH+/DKgGRD7TElkkyS46JXAUhMzIKiCa7BabPeIy3AQcTkXwVVN7DbqsiCew==
    dependencies:
      normalize-url "^6.0.1"
      postcss-value-parser "^4.2.0"
  
  postcss-normalize-whitespace@^5.1.1:
    version "5.1.1"
    resolved "https://registry.npmjs.org/postcss-normalize-whitespace/-/postcss-normalize-whitespace-5.1.1.tgz"
    integrity sha512-83ZJ4t3NUDETIHTa3uEg6asWjSBYL5EdkVB0sDncx9ERzOKBVJIUeDO9RyA9Zwtig8El1d79HBp0JEi8wvGQnA==
    dependencies:
      postcss-value-parser "^4.2.0"
  
  postcss-ordered-values@^5.1.3:
    version "5.1.3"
    resolved "https://registry.npmjs.org/postcss-ordered-values/-/postcss-ordered-values-5.1.3.tgz"
    integrity sha512-9UO79VUhPwEkzbb3RNpqqghc6lcYej1aveQteWY+4POIwlqkYE21HKWaLDF6lWNuqCobEAyTovVhtI32Rbv2RQ==
    dependencies:
      cssnano-utils "^3.1.0"
      postcss-value-parser "^4.2.0"
  
  postcss-plugin-constparse@3.6.6:
    version "3.6.6"
    resolved "https://registry.npmjs.org/postcss-plugin-constparse/-/postcss-plugin-constparse-3.6.6.tgz"
    integrity sha512-Bx5Sr4pDPFpYgGtLz/tiJUo/mmgSExAqtnVG8lo8AQcLWxuaJ5Yv6+z8MUegBcSA3A+Oqs7GUm76JDV7GG91eA==
  
  postcss-pxtransform@3.6.6:
    version "3.6.6"
    resolved "https://registry.npmjs.org/postcss-pxtransform/-/postcss-pxtransform-3.6.6.tgz"
    integrity sha512-NTEnE43Z41f9F53vVDt1r+llrlmWqy5fH12ObJ0Q2MF4KjvdGqnbE1yDg7XqmiLFWbUJ3iEe17zIaSlNVyiQLQ==
  
  postcss-reduce-initial@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/postcss-reduce-initial/-/postcss-reduce-initial-5.1.0.tgz"
    integrity sha512-5OgTUviz0aeH6MtBjHfbr57tml13PuedK/Ecg8szzd4XRMbYxH4572JFG067z+FqBIf6Zp/d+0581glkvvWMFw==
    dependencies:
      browserslist "^4.16.6"
      caniuse-api "^3.0.0"
  
  postcss-reduce-transforms@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/postcss-reduce-transforms/-/postcss-reduce-transforms-5.1.0.tgz"
    integrity sha512-2fbdbmgir5AvpW9RLtdONx1QoYG2/EtqpNQbFASDlixBbAYuTcJ0dECwlqNqH7VbaUnEnh8SrxOe2sRIn24XyQ==
    dependencies:
      postcss-value-parser "^4.2.0"
  
  postcss-resolve-nested-selector@^0.1.1:
    version "0.1.1"
    resolved "https://registry.npmjs.org/postcss-resolve-nested-selector/-/postcss-resolve-nested-selector-0.1.1.tgz"
    integrity sha512-HvExULSwLqHLgUy1rl3ANIqCsvMS0WHss2UOsXhXnQaZ9VCc2oBvIpXrl00IUFT5ZDITME0o6oiXeiHr2SAIfw==
  
  postcss-safe-parser@^6.0.0:
    version "6.0.0"
    resolved "https://registry.npmjs.org/postcss-safe-parser/-/postcss-safe-parser-6.0.0.tgz"
    integrity sha512-FARHN8pwH+WiS2OPCxJI8FuRJpTVnn6ZNFiqAM2aeW2LwTHWWmWgIyKC6cUo0L8aeKiF/14MNvnpls6R2PBeMQ==
  
  postcss-selector-parser@^6.0.10, postcss-selector-parser@^6.0.12, postcss-selector-parser@^6.0.2, postcss-selector-parser@^6.0.4, postcss-selector-parser@^6.0.5, postcss-selector-parser@^6.0.9:
    version "6.0.13"
    resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.13.tgz"
    integrity sha512-EaV1Gl4mUEV4ddhDnv/xtj7sxwrwxdetHdWUGnT4VJQf+4d05v6lHYZr8N573k5Z0BViss7BDhfWtKS3+sfAqQ==
    dependencies:
      cssesc "^3.0.0"
      util-deprecate "^1.0.2"
  
  postcss-svgo@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/postcss-svgo/-/postcss-svgo-5.1.0.tgz"
    integrity sha512-D75KsH1zm5ZrHyxPakAxJWtkyXew5qwS70v56exwvw542d9CRtTo78K0WeFxZB4G7JXKKMbEZtZayTGdIky/eA==
    dependencies:
      postcss-value-parser "^4.2.0"
      svgo "^2.7.0"
  
  postcss-unique-selectors@^5.1.1:
    version "5.1.1"
    resolved "https://registry.npmjs.org/postcss-unique-selectors/-/postcss-unique-selectors-5.1.1.tgz"
    integrity sha512-5JiODlELrz8L2HwxfPnhOWZYWDxVHWL83ufOv84NrcgipI7TaeRsatAhK4Tr2/ZiYldpK/wBvw5BD3qfaK96GA==
    dependencies:
      postcss-selector-parser "^6.0.5"
  
  postcss-url@^10.1.3:
    version "10.1.3"
    resolved "https://registry.npmjs.org/postcss-url/-/postcss-url-10.1.3.tgz"
    integrity sha512-FUzyxfI5l2tKmXdYc6VTu3TWZsInayEKPbiyW+P6vmmIrrb4I6CGX0BFoewgYHLK+oIL5FECEK02REYRpBvUCw==
    dependencies:
      make-dir "~3.1.0"
      mime "~2.5.2"
      minimatch "~3.0.4"
      xxhashjs "~0.2.2"
  
  postcss-value-parser@^3.3.0:
    version "3.3.1"
    resolved "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz"
    integrity sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ==
  
  postcss-value-parser@^4.0.0, postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
    version "4.2.0"
    resolved "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
    integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==
  
  "postcss@^7.0.0 || ^8.0.1", postcss@^8.0.0, postcss@^8.0.9, postcss@^8.1.0, postcss@^8.1.10, postcss@^8.2.14, postcss@^8.2.15, postcss@^8.2.2, postcss@^8.3.3, postcss@^8.3.5, postcss@^8.4.18, postcss@^8.4.19, postcss@^8.4.23:
    version "8.4.23"
    resolved "https://registry.npmjs.org/postcss/-/postcss-8.4.23.tgz"
    integrity sha512-bQ3qMcpF6A/YjR55xtoTr0jGOlnPOKAIMdOWiv0EIT6HVPEaJiJB4NLljSbiHoC2RX7DN5Uvjtpbg1NPdwv1oA==
    dependencies:
      nanoid "^3.3.6"
      picocolors "^1.0.0"
      source-map-js "^1.0.2"
  
  postcss@^7.0.23:
    version "7.0.39"
    resolved "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz"
    integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
    dependencies:
      picocolors "^0.2.1"
      source-map "^0.6.1"
  
  postcss@^7.0.32:
    version "7.0.39"
    resolved "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz"
    integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
    dependencies:
      picocolors "^0.2.1"
      source-map "^0.6.1"
  
  postcss@^7.0.36:
    version "7.0.39"
    resolved "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz"
    integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
    dependencies:
      picocolors "^0.2.1"
      source-map "^0.6.1"
  
  preferred-pm@^3.0.3:
    version "3.0.3"
    resolved "https://registry.npmjs.org/preferred-pm/-/preferred-pm-3.0.3.tgz"
    integrity sha512-+wZgbxNES/KlJs9q40F/1sfOd/j7f1O9JaHcW5Dsn3aUUOZg3L2bjpVUcKV2jvtElYfoTuQiNeMfQJ4kwUAhCQ==
    dependencies:
      find-up "^5.0.0"
      find-yarn-workspace-root2 "1.2.16"
      path-exists "^4.0.0"
      which-pm "2.0.0"
  
  prelude-ls@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
    integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==
  
  prelude-ls@~1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz"
    integrity sha512-ESF23V4SKG6lVSGZgYNpbsiaAkdab6ZgOxe52p7+Kid3W3u3bxR4Vfd/o21dmN7jSt0IwgZ4v5MUd26FEtXE9w==
  
  prepend-http@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/prepend-http/-/prepend-http-2.0.0.tgz"
    integrity sha512-ravE6m9Atw9Z/jjttRUZ+clIXogdghyZAuWJ3qEzjT+jI/dL1ifAqhZeC5VHzQp1MSt1+jxKkFNemj/iO7tVUA==
  
  "prettier@^1.18.2 || ^2.0.0":
    version "2.8.8"
    resolved "https://registry.npmjs.org/prettier/-/prettier-2.8.8.tgz"
    integrity sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==
  
  pretty-bytes@^5.3.0:
    version "5.6.0"
    resolved "https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-5.6.0.tgz"
    integrity sha512-FFw039TmrBqFK8ma/7OL3sDz/VytdtJr044/QUJtH0wK9lb9jLq9tJyIxUwtQJHwar2BqtiA4iCWSwo9JLkzFg==
  
  pretty-error@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/pretty-error/-/pretty-error-4.0.0.tgz"
    integrity sha512-AoJ5YMAcXKYxKhuJGdcvse+Voc6v1RgnsR3nWcYU7q4t6z0Q6T86sv5Zq8VIRbOWWFpvdGE83LtdSMNd+6Y0xw==
    dependencies:
      lodash "^4.17.20"
      renderkid "^3.0.0"
  
  pretty-time@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/pretty-time/-/pretty-time-1.1.0.tgz"
    integrity sha512-28iF6xPQrP8Oa6uxE6a1biz+lWeTOAPKggvjB8HAs6nVMKZwf5bG++632Dx614hIWgUPkgivRfG+a8uAXGTIbA==
  
  process-nextick-args@^2.0.0, process-nextick-args@~2.0.0:
    version "2.0.1"
    resolved "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
    integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==
  
  promise-polyfill@^7.1.0:
    version "7.1.2"
    resolved "https://registry.npmjs.org/promise-polyfill/-/promise-polyfill-7.1.2.tgz"
    integrity sha512-FuEc12/eKqqoRYIGBrUptCBRhobL19PS2U31vMNTfyck1FxPyMfgsXyW4Mav85y/ZN1hop3hOwRlUDok23oYfQ==
  
  prop-types@^15.6.2, prop-types@^15.8.1:
    version "15.8.1"
    resolved "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz"
    integrity sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==
    dependencies:
      loose-envify "^1.4.0"
      object-assign "^4.1.1"
      react-is "^16.13.1"
  
  proto-list@~1.2.1:
    version "1.2.4"
    resolved "https://registry.npmjs.org/proto-list/-/proto-list-1.2.4.tgz"
    integrity sha512-vtK/94akxsTMhe0/cbfpR+syPuszcuwhqVjJq26CuNDgFGj682oRBXOP5MJpv2r7JtE8MsiepGIqvvOTBwn2vA==
  
  proxy-addr@~2.0.7:
    version "2.0.7"
    resolved "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz"
    integrity sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==
    dependencies:
      forwarded "0.2.0"
      ipaddr.js "1.9.1"
  
  prr@~1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/prr/-/prr-1.0.1.tgz"
    integrity sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==
  
  pseudomap@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz"
    integrity sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ==
  
  psl@^1.1.28, psl@^1.1.33:
    version "1.9.0"
    resolved "https://registry.npmjs.org/psl/-/psl-1.9.0.tgz"
    integrity sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==
  
  pump@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz"
    integrity sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==
    dependencies:
      end-of-stream "^1.1.0"
      once "^1.3.1"
  
  punycode@^2.1.0, punycode@^2.1.1, punycode@^2.3.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/punycode/-/punycode-2.3.0.tgz"
    integrity sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==
  
  pupa@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmjs.org/pupa/-/pupa-2.1.1.tgz"
    integrity sha512-l1jNAspIBSFqbT+y+5FosojNpVpF94nlI+wDUpqP9enwOTfHx9f0gh5nB96vl+6yTpsJsypeNrwfzPrKuHB41A==
    dependencies:
      escape-goat "^2.0.0"
  
  qs@~6.5.2:
    version "6.5.3"
    resolved "https://registry.npmjs.org/qs/-/qs-6.5.3.tgz"
    integrity sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==
  
  qs@6.10.3:
    version "6.10.3"
    resolved "https://registry.npmjs.org/qs/-/qs-6.10.3.tgz"
    integrity sha512-wr7M2E0OFRfIfJZjKGieI8lBKb7fRCH4Fv5KNPEs7gJ8jadvotdsS08PzOKR7opXhZ/Xkjtt3WF9g38drmyRqQ==
    dependencies:
      side-channel "^1.0.4"
  
  query-ast@^1.0.3:
    version "1.0.5"
    resolved "https://registry.npmjs.org/query-ast/-/query-ast-1.0.5.tgz"
    integrity sha512-JK+1ma4YDuLjvKKcz9JZ70G+CM9qEOs/l1cZzstMMfwKUabTJ9sud5jvDGrUNuv03yKUgs82bLkHXJkDyhRmBw==
    dependencies:
      invariant "2.2.4"
      lodash "^4.17.21"
  
  query-string@^5.0.1:
    version "5.1.1"
    resolved "https://registry.npmjs.org/query-string/-/query-string-5.1.1.tgz"
    integrity sha512-gjWOsm2SoGlgLEdAGt7a6slVOk9mGiXmPFMqrEhLQ68rhQuBnpfs3+EmlvqKyxnCo9/PPlF+9MtY02S1aFg+Jw==
    dependencies:
      decode-uri-component "^0.2.0"
      object-assign "^4.1.0"
      strict-uri-encode "^1.0.0"
  
  query-string@^7.1.1:
    version "7.1.3"
    resolved "https://registry.npmjs.org/query-string/-/query-string-7.1.3.tgz"
    integrity sha512-hh2WYhq4fi8+b+/2Kg9CEge4fDPvHS534aOOvOZeQ3+Vf2mCFsaFBYj0i+iXcAq6I9Vzp5fjMFBlONvayDC1qg==
    dependencies:
      decode-uri-component "^0.2.2"
      filter-obj "^1.1.0"
      split-on-first "^1.0.0"
      strict-uri-encode "^2.0.0"
  
  querystringify@^2.1.1:
    version "2.2.0"
    resolved "https://registry.npmjs.org/querystringify/-/querystringify-2.2.0.tgz"
    integrity sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==
  
  queue-microtask@^1.2.2:
    version "1.2.3"
    resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
    integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==
  
  quick-lru@^4.0.1:
    version "4.0.1"
    resolved "https://registry.npmjs.org/quick-lru/-/quick-lru-4.0.1.tgz"
    integrity sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g==
  
  randombytes@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz"
    integrity sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==
    dependencies:
      safe-buffer "^5.1.0"
  
  range-parser@^1.2.1, range-parser@~1.2.1:
    version "1.2.1"
    resolved "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
    integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==
  
  raw-body@2.5.1:
    version "2.5.1"
    resolved "https://registry.npmjs.org/raw-body/-/raw-body-2.5.1.tgz"
    integrity sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==
    dependencies:
      bytes "3.1.2"
      http-errors "2.0.0"
      iconv-lite "0.4.24"
      unpipe "1.0.0"
  
  rc-config-loader@^4.0.0:
    version "4.1.2"
    resolved "https://registry.npmjs.org/rc-config-loader/-/rc-config-loader-4.1.2.tgz"
    integrity sha512-qKTnVWFl9OQYKATPzdfaZIbTxcHziQl92zYSxYC6umhOqyAsoj8H8Gq/+aFjAso68sBdjTz3A7omqeAkkF1MWg==
    dependencies:
      debug "^4.3.4"
      js-yaml "^4.1.0"
      json5 "^2.2.2"
      require-from-string "^2.0.2"
  
  rc@^1.2.8, rc@1.2.8:
    version "1.2.8"
    resolved "https://registry.npmjs.org/rc/-/rc-1.2.8.tgz"
    integrity sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==
    dependencies:
      deep-extend "^0.6.0"
      ini "~1.3.0"
      minimist "^1.2.0"
      strip-json-comments "~2.0.1"
  
  "react-dom@^16.8.0 || ^17.0.0 || ^18.0.0", react-dom@^18.0.0, react-dom@>=16.6.0:
    version "18.2.0"
    resolved "https://registry.npmjs.org/react-dom/-/react-dom-18.2.0.tgz"
    integrity sha512-6IMTriUmvsjHUjNtEDudZfuDQUoWXVxKHhlEGSk81n4YFS+r/Kl99wXiwlVXtPBtJenozv2P+hxDsw9eA7Xo6g==
    dependencies:
      loose-envify "^1.1.0"
      scheduler "^0.23.0"
  
  react-is@^16.13.1, react-is@^16.6.0, react-is@^16.7.0:
    version "16.13.1"
    resolved "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
    integrity sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==
  
  react-reconciler@0.27.0:
    version "0.27.0"
    resolved "https://registry.npmjs.org/react-reconciler/-/react-reconciler-0.27.0.tgz"
    integrity sha512-HmMDKciQjYmBRGuuhIaKA1ba/7a+UsM5FzOZsMO2JYHt9Jh8reCb7j1eDC95NOyUlKM9KRyvdx0flBuDvYSBoA==
    dependencies:
      loose-envify "^1.1.0"
      scheduler "^0.21.0"
  
  react-refresh@^0.11.0:
    version "0.11.0"
    resolved "https://registry.npmjs.org/react-refresh/-/react-refresh-0.11.0.tgz"
    integrity sha512-F27qZr8uUqwhWZboondsPx8tnC3Ct3SxZA3V5WyEvujRyyNv0VYPhoBg1gZ8/MV5tubQp76Trw8lTv9hzRBa+A==
  
  react-refresh@^0.14.0, "react-refresh@>=0.10.0 <1.0.0":
    version "0.14.0"
    resolved "https://registry.npmjs.org/react-refresh/-/react-refresh-0.14.0.tgz"
    integrity sha512-wViHqhAd8OHeLS/IRMJjTSDHF3U9eWi62F/MledQGPdJGDhodXJ9PBLNGr6WWL7qlH12Mt3TyTpbS+hGXMjCzQ==
  
  react-refresh@^0.4.0:
    version "0.4.3"
    resolved "https://registry.npmjs.org/react-refresh/-/react-refresh-0.4.3.tgz"
    integrity sha512-Hwln1VNuGl/6bVwnd0Xdn1e84gT/8T9aYNL+HAKDArLCS7LWjwr7StE30IEYbIkx0Vi3vs+coQxe+SQDbGbbpA==
  
  react-router-dom@^5.2.0:
    version "5.3.4"
    resolved "https://registry.npmjs.org/react-router-dom/-/react-router-dom-5.3.4.tgz"
    integrity sha512-m4EqFMHv/Ih4kpcBCONHbkT68KoAeHN4p3lAGoNryfHi0dMy0kCzEZakiKRsvg5wHZ/JLrLW8o8KomWiz/qbYQ==
    dependencies:
      "@babel/runtime" "^7.12.13"
      history "^4.9.0"
      loose-envify "^1.3.1"
      prop-types "^15.6.2"
      react-router "5.3.4"
      tiny-invariant "^1.0.2"
      tiny-warning "^1.0.0"
  
  react-router@5.3.4:
    version "5.3.4"
    resolved "https://registry.npmjs.org/react-router/-/react-router-5.3.4.tgz"
    integrity sha512-Ys9K+ppnJah3QuaRiLxk+jDWOR1MekYQrlytiXxC1RyfbdsZkS5pvKAzCCr031xHixZwpnsYNT5xysdFHQaYsA==
    dependencies:
      "@babel/runtime" "^7.12.13"
      history "^4.9.0"
      hoist-non-react-statics "^3.1.0"
      loose-envify "^1.3.1"
      path-to-regexp "^1.7.0"
      prop-types "^15.6.2"
      react-is "^16.6.0"
      tiny-invariant "^1.0.2"
      tiny-warning "^1.0.0"
  
  react-transition-group@^4.4.2, react-transition-group@^4.4.5:
    version "4.4.5"
    resolved "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz"
    integrity sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==
    dependencies:
      "@babel/runtime" "^7.5.5"
      dom-helpers "^5.0.1"
      loose-envify "^1.4.0"
      prop-types "^15.6.2"
  
  "react@^16.8.0 || ^17.0.0 || ^18.0.0", react@^18.0.0, react@^18.2.0, "react@>= 16.8.0", react@>=15, react@>=16.6.0, react@>=17:
    version "18.2.0"
    resolved "https://registry.npmjs.org/react/-/react-18.2.0.tgz"
    integrity sha512-/3IjMdb2L9QbBdWiW5e3P2/npwMBaU9mHCSCUzNln0ZCYbcfTsGbTJrU/kGemdH2IWmB2ioZ+zkxtmq6g09fGQ==
    dependencies:
      loose-envify "^1.1.0"
  
  read-cache@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz"
    integrity sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==
    dependencies:
      pify "^2.3.0"
  
  read-pkg-up@^7.0.1:
    version "7.0.1"
    resolved "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-7.0.1.tgz"
    integrity sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==
    dependencies:
      find-up "^4.1.0"
      read-pkg "^5.2.0"
      type-fest "^0.8.1"
  
  read-pkg@^5.2.0:
    version "5.2.0"
    resolved "https://registry.npmjs.org/read-pkg/-/read-pkg-5.2.0.tgz"
    integrity sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==
    dependencies:
      "@types/normalize-package-data" "^2.4.0"
      normalize-package-data "^2.5.0"
      parse-json "^5.0.0"
      type-fest "^0.6.0"
  
  readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.3.0, readable-stream@^2.3.5:
    version "2.3.7"
    resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
    integrity sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==
    dependencies:
      core-util-is "~1.0.0"
      inherits "~2.0.3"
      isarray "~1.0.0"
      process-nextick-args "~2.0.0"
      safe-buffer "~5.1.1"
      string_decoder "~1.1.1"
      util-deprecate "~1.0.1"
  
  readable-stream@^3.0.6:
    version "3.6.0"
    resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz"
    integrity sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==
    dependencies:
      inherits "^2.0.3"
      string_decoder "^1.1.1"
      util-deprecate "^1.0.1"
  
  readable-stream@^3.4.0:
    version "3.6.2"
    resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
    integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
    dependencies:
      inherits "^2.0.3"
      string_decoder "^1.1.1"
      util-deprecate "^1.0.1"
  
  readdirp@^3.5.0, readdirp@~3.6.0:
    version "3.6.0"
    resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
    integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
    dependencies:
      picomatch "^2.2.1"
  
  redent@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/redent/-/redent-3.0.0.tgz"
    integrity sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==
    dependencies:
      indent-string "^4.0.0"
      strip-indent "^3.0.0"
  
  regenerate-unicode-properties@^10.1.0:
    version "10.1.0"
    resolved "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.0.tgz"
    integrity sha512-d1VudCLoIGitcU/hEg2QqvyGZQmdC0Lf8BqdOMXGFSvJP4bNV1+XqbPQeHHLD51Jh4QJJ225dlIFvY4Ly6MXmQ==
    dependencies:
      regenerate "^1.4.2"
  
  regenerate@^1.4.2:
    version "1.4.2"
    resolved "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz"
    integrity sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==
  
  regenerator-runtime@^0.13.11:
    version "0.13.11"
    resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"
    integrity sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==
  
  regenerator-runtime@^0.13.4:
    version "0.13.9"
    resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz"
    integrity sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA==
  
  regenerator-runtime@0.11:
    version "0.11.1"
    resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz"
    integrity sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==
  
  regenerator-transform@^0.15.0:
    version "0.15.0"
    resolved "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.15.0.tgz"
    integrity sha512-LsrGtPmbYg19bcPHwdtmXwbW+TqNvtY4riE3P83foeHRroMbH6/2ddFBfab3t7kbzc7v7p4wbkIecHImqt0QNg==
    dependencies:
      "@babel/runtime" "^7.8.4"
  
  regex-parser@^2.2.11:
    version "2.2.11"
    resolved "https://registry.npmjs.org/regex-parser/-/regex-parser-2.2.11.tgz"
    integrity sha512-jbD/FT0+9MBU2XAZluI7w2OBs1RBi6p9M83nkoZayQXXU9e8Robt69FcZc7wU4eJD/YFTjn1JdCk3rbMJajz8Q==
  
  regexp.prototype.flags@^1.2.0, regexp.prototype.flags@^1.4.3:
    version "1.4.3"
    resolved "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.4.3.tgz"
    integrity sha512-fjggEOO3slI6Wvgjwflkc4NFRCTZAu5CnNfBd5qOMYhWdn67nJBBu34/TkD++eeFmd8C9r9jfXJ27+nSiRkSUA==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.3"
      functions-have-names "^1.2.2"
  
  regexpu-core@^5.1.0:
    version "5.2.1"
    resolved "https://registry.npmjs.org/regexpu-core/-/regexpu-core-5.2.1.tgz"
    integrity sha512-HrnlNtpvqP1Xkb28tMhBUO2EbyUHdQlsnlAhzWcwHy8WJR53UWr7/MAvqrsQKMbV4qdpv03oTMG8iIhfsPFktQ==
    dependencies:
      regenerate "^1.4.2"
      regenerate-unicode-properties "^10.1.0"
      regjsgen "^0.7.1"
      regjsparser "^0.9.1"
      unicode-match-property-ecmascript "^2.0.0"
      unicode-match-property-value-ecmascript "^2.0.0"
  
  registry-auth-token@^4.0.0:
    version "4.2.2"
    resolved "https://registry.npmjs.org/registry-auth-token/-/registry-auth-token-4.2.2.tgz"
    integrity sha512-PC5ZysNb42zpFME6D/XlIgtNGdTl8bBOCw90xQLVMpzuuubJKYDWFAEuUNc+Cn8Z8724tg2SDhDRrkVEsqfDMg==
    dependencies:
      rc "1.2.8"
  
  registry-url@^5.0.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/registry-url/-/registry-url-5.1.0.tgz"
    integrity sha512-8acYXXTI0AkQv6RAOjE3vOaIXZkT9wo4LOFbBKYQEEnnMNBpKqdUrI6S4NT0KPIo/WVvJ5tE/X5LF/TQUf0ekw==
    dependencies:
      rc "^1.2.8"
  
  regjsgen@^0.7.1:
    version "0.7.1"
    resolved "https://registry.npmjs.org/regjsgen/-/regjsgen-0.7.1.tgz"
    integrity sha512-RAt+8H2ZEzHeYWxZ3H2z6tF18zyyOnlcdaafLrm21Bguj7uZy6ULibiAFdXEtKQY4Sy7wDTwDiOazasMLc4KPA==
  
  regjsparser@^0.9.1:
    version "0.9.1"
    resolved "https://registry.npmjs.org/regjsparser/-/regjsparser-0.9.1.tgz"
    integrity sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==
    dependencies:
      jsesc "~0.5.0"
  
  relateurl@^0.2.7:
    version "0.2.7"
    resolved "https://registry.npmjs.org/relateurl/-/relateurl-0.2.7.tgz"
    integrity sha512-G08Dxvm4iDN3MLM0EsP62EDV9IuhXPR6blNz6Utcp7zyV3tr4HVNINt6MpaRWbxoOHT3Q7YN2P+jaHX8vUbgog==
  
  remove-trailing-separator@^1.0.1:
    version "1.1.0"
    resolved "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz"
    integrity sha512-/hS+Y0u3aOfIETiaiirUFwDBDzmXPvO+jAfKTitUngIPzdKc6Z0LoFjM/CK5PL4C+eKwHohlHAb6H0VFfmmUsw==
  
  renderkid@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/renderkid/-/renderkid-3.0.0.tgz"
    integrity sha512-q/7VIQA8lmM1hF+jn+sFSPWGlMkSAeNYcPLmDQx2zzuiDfaLrOmumR8iaUKlenFgh0XRPIUeSPlH3A+AW3Z5pg==
    dependencies:
      css-select "^4.1.3"
      dom-converter "^0.2.0"
      htmlparser2 "^6.1.0"
      lodash "^4.17.21"
      strip-ansi "^6.0.1"
  
  replace-ext@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/replace-ext/-/replace-ext-1.0.1.tgz"
    integrity sha512-yD5BHCe7quCgBph4rMQ+0KkIRKwWCrHDOX1p1Gp6HwjPM5kVoCdKGNhN7ydqqsX6lJEnQDKZ/tFMiEdQ1dvPEw==
  
  request@^2.88.0:
    version "2.88.2"
    resolved "https://registry.npmjs.org/request/-/request-2.88.2.tgz"
    integrity sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==
    dependencies:
      aws-sign2 "~0.7.0"
      aws4 "^1.8.0"
      caseless "~0.12.0"
      combined-stream "~1.0.6"
      extend "~3.0.2"
      forever-agent "~0.6.1"
      form-data "~2.3.2"
      har-validator "~5.1.3"
      http-signature "~1.2.0"
      is-typedarray "~1.0.0"
      isstream "~0.1.2"
      json-stringify-safe "~5.0.1"
      mime-types "~2.1.19"
      oauth-sign "~0.9.0"
      performance-now "^2.1.0"
      qs "~6.5.2"
      safe-buffer "^5.1.2"
      tough-cookie "~2.5.0"
      tunnel-agent "^0.6.0"
      uuid "^3.3.2"
  
  require-directory@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
    integrity sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==
  
  require-from-string@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz"
    integrity sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==
  
  require-package-name@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/require-package-name/-/require-package-name-2.0.1.tgz"
    integrity sha512-uuoJ1hU/k6M0779t3VMVIYpb2VMJk05cehCaABFhXaibcbvfgR8wKiozLjVFSzJPmQMRqIcO0HMyTFqfV09V6Q==
  
  requires-port@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz"
    integrity sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==
  
  resolve-from@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
    integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==
  
  resolve-from@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz"
    integrity sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==
  
  resolve-pathname@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/resolve-pathname/-/resolve-pathname-3.0.0.tgz"
    integrity sha512-C7rARubxI8bXFNB/hqcp/4iUeIXJhJZvFPFPiSPRnhU5UPxzMFIl+2E6yY6c4k9giDJAhtV+enfA+G89N6Csng==
  
  resolve-url-loader@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/resolve-url-loader/-/resolve-url-loader-5.0.0.tgz"
    integrity sha512-uZtduh8/8srhBoMx//5bwqjQ+rfYOUq8zC9NrMUGtjBiGTtFJM42s58/36+hTqeqINcnYe08Nj3LkK9lW4N8Xg==
    dependencies:
      adjust-sourcemap-loader "^4.0.0"
      convert-source-map "^1.7.0"
      loader-utils "^2.0.0"
      postcss "^8.2.14"
      source-map "0.6.1"
  
  resolve@^1.1.7, resolve@^1.10.0, resolve@^1.14.2, resolve@^1.18.1, resolve@^1.22.0, resolve@^1.22.1:
    version "1.22.1"
    resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.1.tgz"
    integrity sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==
    dependencies:
      is-core-module "^2.9.0"
      path-parse "^1.0.7"
      supports-preserve-symlinks-flag "^1.0.0"
  
  resolve@^2.0.0-next.4:
    version "2.0.0-next.4"
    resolved "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.4.tgz"
    integrity sha512-iMDbmAWtfU+MHpxt/I5iWI7cY6YVEZUQ3MBgPQ++XD1PELuJHIl82xBmObyP2KyQmkNB2dsqF7seoQQiAn5yDQ==
    dependencies:
      is-core-module "^2.9.0"
      path-parse "^1.0.7"
      supports-preserve-symlinks-flag "^1.0.0"
  
  responselike@^1.0.2, responselike@1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/responselike/-/responselike-1.0.2.tgz"
    integrity sha512-/Fpe5guzJk1gPqdJLJR5u7eG/gNY4nImjbRDaVWVMRhne55TCmj2i9Q+54PBRfatRC8v/rIiv9BN0pMd9OV5EQ==
    dependencies:
      lowercase-keys "^1.0.0"
  
  restore-cursor@^3.1.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz"
    integrity sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==
    dependencies:
      onetime "^5.1.0"
      signal-exit "^3.0.2"
  
  retry@^0.13.1:
    version "0.13.1"
    resolved "https://registry.npmjs.org/retry/-/retry-0.13.1.tgz"
    integrity sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==
  
  reusify@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
    integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==
  
  rimraf@^2.6.3:
    version "2.7.1"
    resolved "https://registry.npmjs.org/rimraf/-/rimraf-2.7.1.tgz"
    integrity sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==
    dependencies:
      glob "^7.1.3"
  
  rimraf@^3.0.2:
    version "3.0.2"
    resolved "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
    integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
    dependencies:
      glob "^7.1.3"
  
  rrweb-cssom@^0.6.0:
    version "0.6.0"
    resolved "https://registry.npmjs.org/rrweb-cssom/-/rrweb-cssom-0.6.0.tgz"
    integrity sha512-APM0Gt1KoXBz0iIkkdB/kfvGOwC4UuJFeG/c+yV7wSc7q96cG/kJ0HiYCnzivD9SB53cLV1MlHFNfOuPaadYSw==
  
  run-async@^2.4.0:
    version "2.4.1"
    resolved "https://registry.npmjs.org/run-async/-/run-async-2.4.1.tgz"
    integrity sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==
  
  run-parallel@^1.1.9:
    version "1.2.0"
    resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
    integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
    dependencies:
      queue-microtask "^1.2.2"
  
  rxjs@^6.6.0:
    version "6.6.7"
    resolved "https://registry.npmjs.org/rxjs/-/rxjs-6.6.7.tgz"
    integrity sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ==
    dependencies:
      tslib "^1.9.0"
  
  rxjs@^7.5.5:
    version "7.8.0"
    resolved "https://registry.npmjs.org/rxjs/-/rxjs-7.8.0.tgz"
    integrity sha512-F2+gxDshqmIub1KdvZkaEfGDwLNpPvk9Fs6LD/MyQxNgMds/WH9OdDDXOmxUZpME+iSK3rQCctkL0DYyytUqMg==
    dependencies:
      tslib "^2.1.0"
  
  safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@>=5.1.0, safe-buffer@~5.2.0, safe-buffer@5.2.1:
    version "5.2.1"
    resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
    integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==
  
  safe-buffer@~5.1.0:
    version "5.1.2"
    resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
    integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==
  
  safe-buffer@~5.1.1:
    version "5.1.2"
    resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
    integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==
  
  safe-buffer@5.1.2:
    version "5.1.2"
    resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
    integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==
  
  safe-regex-test@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.0.0.tgz"
    integrity sha512-JBUUzyOgEwXQY1NuPtvcj/qcBDbDmEvWufhlnXZIm75DEHp+afM1r1ujJpJsV/gSM4t59tpDyPi1sd6ZaPFfsA==
    dependencies:
      call-bind "^1.0.2"
      get-intrinsic "^1.1.3"
      is-regex "^1.1.4"
  
  safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@^2.1.2, "safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", safer-buffer@~2.1.0:
    version "2.1.2"
    resolved "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
    integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==
  
  sass-loader@12.4.0:
    version "12.4.0"
    resolved "https://registry.npmjs.org/sass-loader/-/sass-loader-12.4.0.tgz"
    integrity sha512-7xN+8khDIzym1oL9XyS6zP6Ges+Bo2B2xbPrjdMHEYyV3AQYhd/wXeru++3ODHF0zMjYmVadblSKrPrjEkL8mg==
    dependencies:
      klona "^2.0.4"
      neo-async "^2.6.2"
  
  sass@*, sass@^1.23.7, sass@^1.29.0, sass@^1.3.0, sass@1.50.0:
    version "1.50.0"
    resolved "https://registry.npmjs.org/sass/-/sass-1.50.0.tgz"
    integrity sha512-cLsD6MEZ5URXHStxApajEh7gW189kkjn4Rc8DQweMyF+o5HF5nfEz8QYLMlPsTOD88DknatTmBWkOcw5/LnJLQ==
    dependencies:
      chokidar ">=3.0.0 <4.0.0"
      immutable "^4.0.0"
      source-map-js ">=0.6.2 <2.0.0"
  
  sax@^1.2.4, sax@>=0.6.0, sax@~1.2.4, sax@1.2.4:
    version "1.2.4"
    resolved "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz"
    integrity sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==
  
  saxes@^6.0.0:
    version "6.0.0"
    resolved "https://registry.npmjs.org/saxes/-/saxes-6.0.0.tgz"
    integrity sha512-xAg7SOnEhrm5zI3puOOKyy1OMcMlIJZYNJY7xLBwSze0UjhPLnWfj2GF2EpT0jmzaJKIWKHLsaSSajf35bcYnA==
    dependencies:
      xmlchars "^2.2.0"
  
  scheduler@^0.21.0:
    version "0.21.0"
    resolved "https://registry.npmjs.org/scheduler/-/scheduler-0.21.0.tgz"
    integrity sha512-1r87x5fz9MXqswA2ERLo0EbOAU74DpIUO090gIasYTqlVoJeMcl+Z1Rg7WHz+qtPujhS/hGIt9kxZOYBV3faRQ==
    dependencies:
      loose-envify "^1.1.0"
  
  scheduler@^0.23.0:
    version "0.23.0"
    resolved "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz"
    integrity sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==
    dependencies:
      loose-envify "^1.1.0"
  
  schema-utils@^2.6.5:
    version "2.7.1"
    resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.1.tgz"
    integrity sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg==
    dependencies:
      "@types/json-schema" "^7.0.5"
      ajv "^6.12.4"
      ajv-keywords "^3.5.2"
  
  schema-utils@^3.0.0:
    version "3.1.1"
    resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-3.1.1.tgz"
    integrity sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw==
    dependencies:
      "@types/json-schema" "^7.0.8"
      ajv "^6.12.5"
      ajv-keywords "^3.5.2"
  
  schema-utils@^3.1.1:
    version "3.1.1"
    resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-3.1.1.tgz"
    integrity sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw==
    dependencies:
      "@types/json-schema" "^7.0.8"
      ajv "^6.12.5"
      ajv-keywords "^3.5.2"
  
  schema-utils@^3.1.2:
    version "3.1.2"
    resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-3.1.2.tgz"
    integrity sha512-pvjEHOgWc9OWA/f/DE3ohBWTD6EleVLf7iFUkoSwAxttdBhB9QUebQgxER2kWueOvRJXPHNnyrvvh9eZINB8Eg==
    dependencies:
      "@types/json-schema" "^7.0.8"
      ajv "^6.12.5"
      ajv-keywords "^3.5.2"
  
  schema-utils@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-4.0.0.tgz"
    integrity sha512-1edyXKgh6XnJsJSQ8mKWXnN/BVaIbFMLpouRUrXgVq7WYne5kw3MW7UPhO44uRXQSIpTSXoJbmrR2X0w9kUTyg==
    dependencies:
      "@types/json-schema" "^7.0.9"
      ajv "^8.8.0"
      ajv-formats "^2.1.1"
      ajv-keywords "^5.0.0"
  
  scss-bundle@^3.0.2:
    version "3.1.2"
    resolved "https://registry.npmjs.org/scss-bundle/-/scss-bundle-3.1.2.tgz"
    integrity sha512-lvxTwCKDLgzmRWhGwJ834ggtnEhs0G9FxSJRWte+NwlshVvBcQ/kOHHkpAGDpCxIMNGz/Utl0yd/MWyQAOBhqg==
    dependencies:
      "@types/archy" "^0.0.31"
      "@types/debug" "^4.1.5"
      "@types/fs-extra" "^8.0.1"
      "@types/glob" "^7.1.1"
      "@types/lodash.debounce" "^4.0.6"
      "@types/sass" "^1.16.0"
      archy "^1.0.0"
      chalk "^3.0.0"
      chokidar "^3.3.1"
      commander "^4.0.1"
      fs-extra "^8.1.0"
      globs "^0.1.4"
      lodash.debounce "^4.0.8"
      loglevel "^1.6.6"
      loglevel-plugin-prefix "^0.8.4"
      pretty-bytes "^5.3.0"
      sass "^1.23.7"
      tslib "^1.10.0"
  
  scss-parser@^1.0.4:
    version "1.0.6"
    resolved "https://registry.npmjs.org/scss-parser/-/scss-parser-1.0.6.tgz"
    integrity sha512-SH3TaoaJFzfAtqs3eG1j5IuHJkeEW5rKUPIjIN+ZorLAyJLHItQGnsgwHk76v25GtLtpT9IqfAcqK4vFWdiw+w==
    dependencies:
      invariant "2.2.4"
      lodash "4.17.21"
  
  seek-bzip@^1.0.5:
    version "1.0.6"
    resolved "https://registry.npmjs.org/seek-bzip/-/seek-bzip-1.0.6.tgz"
    integrity sha512-e1QtP3YL5tWww8uKaOCQ18UxIT2laNBXHjV/S2WYCiK4udiv8lkG89KRIoCjUagnAmCBurjF4zEVX2ByBbnCjQ==
    dependencies:
      commander "^2.8.1"
  
  select-hose@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/select-hose/-/select-hose-2.0.0.tgz"
    integrity sha512-mEugaLK+YfkijB4fx0e6kImuJdCIt2LxCRcbEYPqRGCs4F2ogyfZU5IAZRdjCP8JPq2AtdNoC/Dux63d9Kiryg==
  
  selfsigned@^2.0.0:
    version "2.1.1"
    resolved "https://registry.npmjs.org/selfsigned/-/selfsigned-2.1.1.tgz"
    integrity sha512-GSL3aowiF7wa/WtSFwnUrludWFoNhftq8bUkH9pkzjpN2XSPOAYEgg6e0sS9s0rZwgJzJiQRPU18A6clnoW5wQ==
    dependencies:
      node-forge "^1"
  
  semver-compare@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/semver-compare/-/semver-compare-1.0.0.tgz"
    integrity sha512-YM3/ITh2MJ5MtzaM429anh+x2jiLVjqILF4m4oyQB18W7Ggea7BfqdH/wGMK7dDiMghv/6WG7znWMwUDzJiXow==
  
  semver-diff@^3.1.1:
    version "3.1.1"
    resolved "https://registry.npmjs.org/semver-diff/-/semver-diff-3.1.1.tgz"
    integrity sha512-GX0Ix/CJcHyB8c4ykpHGIAvLyOwOobtM/8d+TQkAd81/bEjgPHrfba41Vpesr7jX/t8Uh+R3EX9eAS5be+jQYg==
    dependencies:
      semver "^6.3.0"
  
  semver@^5.6.0:
    version "5.7.1"
    resolved "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz"
    integrity sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==
  
  semver@^6.0.0, semver@^6.1.1, semver@^6.1.2, semver@^6.2.0, semver@^6.3.0:
    version "6.3.0"
    resolved "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz"
    integrity sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==
  
  semver@^7.0.0:
    version "7.5.1"
    resolved "https://registry.npmjs.org/semver/-/semver-7.5.1.tgz"
    integrity sha512-Wvss5ivl8TMRZXXESstBA4uR5iXgEN/VC5/sOcuXdVLzcdkz4HWetIoRfG5gb5X+ij/G9rw9YoGn3QoQ8OCSpw==
    dependencies:
      lru-cache "^6.0.0"
  
  semver@^7.3.2:
    version "7.3.8"
    resolved "https://registry.npmjs.org/semver/-/semver-7.3.8.tgz"
    integrity sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==
    dependencies:
      lru-cache "^6.0.0"
  
  semver@^7.3.4:
    version "7.3.7"
    resolved "https://registry.npmjs.org/semver/-/semver-7.3.7.tgz"
    integrity sha512-QlYTucUYOews+WeEujDoEGziz4K6c47V/Bd+LjSSYcA94p+DmINdf7ncaUinThfvZyu13lN9OY1XDxt8C0Tw0g==
    dependencies:
      lru-cache "^6.0.0"
  
  semver@^7.3.7:
    version "7.3.7"
    resolved "https://registry.npmjs.org/semver/-/semver-7.3.7.tgz"
    integrity sha512-QlYTucUYOews+WeEujDoEGziz4K6c47V/Bd+LjSSYcA94p+DmINdf7ncaUinThfvZyu13lN9OY1XDxt8C0Tw0g==
    dependencies:
      lru-cache "^6.0.0"
  
  semver@^7.3.8:
    version "7.3.8"
    resolved "https://registry.npmjs.org/semver/-/semver-7.3.8.tgz"
    integrity sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==
    dependencies:
      lru-cache "^6.0.0"
  
  "semver@2 || 3 || 4 || 5":
    version "5.7.1"
    resolved "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz"
    integrity sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==
  
  send@0.18.0:
    version "0.18.0"
    resolved "https://registry.npmjs.org/send/-/send-0.18.0.tgz"
    integrity sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==
    dependencies:
      debug "2.6.9"
      depd "2.0.0"
      destroy "1.2.0"
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      etag "~1.8.1"
      fresh "0.5.2"
      http-errors "2.0.0"
      mime "1.6.0"
      ms "2.1.3"
      on-finished "2.4.1"
      range-parser "~1.2.1"
      statuses "2.0.1"
  
  serialize-javascript@^6.0.0, serialize-javascript@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.1.tgz"
    integrity sha512-owoXEFjWRllis8/M1Q+Cw5k8ZH40e3zhp/ovX+Xr/vi1qj6QesbyXXViFbpNvWvPNAD62SutwEXavefrLJWj7w==
    dependencies:
      randombytes "^2.1.0"
  
  serve-index@^1.9.1:
    version "1.9.1"
    resolved "https://registry.npmjs.org/serve-index/-/serve-index-1.9.1.tgz"
    integrity sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw==
    dependencies:
      accepts "~1.3.4"
      batch "0.6.1"
      debug "2.6.9"
      escape-html "~1.0.3"
      http-errors "~1.6.2"
      mime-types "~2.1.17"
      parseurl "~1.3.2"
  
  serve-static@1.15.0:
    version "1.15.0"
    resolved "https://registry.npmjs.org/serve-static/-/serve-static-1.15.0.tgz"
    integrity sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==
    dependencies:
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      parseurl "~1.3.3"
      send "0.18.0"
  
  setprototypeof@1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz"
    integrity sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==
  
  setprototypeof@1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz"
    integrity sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==
  
  shallow-clone@^3.0.0:
    version "3.0.1"
    resolved "https://registry.npmjs.org/shallow-clone/-/shallow-clone-3.0.1.tgz"
    integrity sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==
    dependencies:
      kind-of "^6.0.2"
  
  shebang-command@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
    integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
    dependencies:
      shebang-regex "^3.0.0"
  
  shebang-regex@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
    integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==
  
  side-channel@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz"
    integrity sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==
    dependencies:
      call-bind "^1.0.0"
      get-intrinsic "^1.0.2"
      object-inspect "^1.9.0"
  
  signal-exit@^3.0.2, signal-exit@^3.0.3:
    version "3.0.7"
    resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
    integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==
  
  signal-exit@^4.0.1:
    version "4.0.2"
    resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-4.0.2.tgz"
    integrity sha512-MY2/qGx4enyjprQnFaZsHib3Yadh3IXyV2C321GY0pjGfVBu4un0uDJkwgdxqO+Rdx8JMT8IfJIRwbYVz3Ob3Q==
  
  slash@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz"
    integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==
  
  slash@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/slash/-/slash-4.0.0.tgz"
    integrity sha512-3dOsAHXXUkQTpOYcoAxLIorMTp4gIQr5IW3iVb7A7lFIp0VHhnynm9izx6TssdrIcVIESAlVjtnO2K8bg+Coew==
  
  slice-ansi@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/slice-ansi/-/slice-ansi-4.0.0.tgz"
    integrity sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==
    dependencies:
      ansi-styles "^4.0.0"
      astral-regex "^2.0.0"
      is-fullwidth-code-point "^3.0.0"
  
  sockjs@^0.3.21:
    version "0.3.24"
    resolved "https://registry.npmjs.org/sockjs/-/sockjs-0.3.24.tgz"
    integrity sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==
    dependencies:
      faye-websocket "^0.11.3"
      uuid "^8.3.2"
      websocket-driver "^0.7.4"
  
  sort-keys-length@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/sort-keys-length/-/sort-keys-length-1.0.1.tgz"
    integrity sha512-GRbEOUqCxemTAk/b32F2xa8wDTs+Z1QHOkbhJDQTvv/6G3ZkbJ+frYWsTcc7cBB3Fu4wy4XlLCuNtJuMn7Gsvw==
    dependencies:
      sort-keys "^1.0.0"
  
  sort-keys@^1.0.0:
    version "1.1.2"
    resolved "https://registry.npmjs.org/sort-keys/-/sort-keys-1.1.2.tgz"
    integrity sha512-vzn8aSqKgytVik0iwdBEi+zevbTYZogewTUM6dtpmGwEcdzbub/TX4bCzRhebDCRC3QzXgJsLRKB2V/Oof7HXg==
    dependencies:
      is-plain-obj "^1.0.0"
  
  sort-keys@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/sort-keys/-/sort-keys-2.0.0.tgz"
    integrity sha512-/dPCrG1s3ePpWm6yBbxZq5Be1dXGLyLn9Z791chDC3NFrpkVbWGzkBwPN1knaciexFXgRJ7hzdnwZ4stHSDmjg==
    dependencies:
      is-plain-obj "^1.0.0"
  
  source-list-map@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/source-list-map/-/source-list-map-2.0.1.tgz"
    integrity sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw==
  
  source-map-js@^1.0.1, source-map-js@^1.0.2, "source-map-js@>=0.6.2 <2.0.0":
    version "1.0.2"
    resolved "https://registry.npmjs.org/source-map-js/-/source-map-js-1.0.2.tgz"
    integrity sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==
  
  source-map-resolve@^0.6.0:
    version "0.6.0"
    resolved "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.6.0.tgz"
    integrity sha512-KXBr9d/fO/bWo97NXsPIAW1bFSBOuCnjbNTBMO7N59hsv5i9yzRDfcYwwt0l04+VqnKC+EwzvJZIP/qkuMgR/w==
    dependencies:
      atob "^2.1.2"
      decode-uri-component "^0.2.0"
  
  source-map-support@^0.5.13, source-map-support@^0.5.16, source-map-support@~0.5.20:
    version "0.5.21"
    resolved "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz"
    integrity sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==
    dependencies:
      buffer-from "^1.0.0"
      source-map "^0.6.0"
  
  source-map@^0.5.3:
    version "0.5.7"
    resolved "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
    integrity sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==
  
  source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1, source-map@0.6.1:
    version "0.6.1"
    resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
    integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==
  
  source-map@^0.7.3:
    version "0.7.4"
    resolved "https://registry.npmjs.org/source-map/-/source-map-0.7.4.tgz"
    integrity sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==
  
  sourcemap-codec@^1.4.8:
    version "1.4.8"
    resolved "https://registry.npmjs.org/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz"
    integrity sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==
  
  spdx-correct@^3.0.0:
    version "3.1.1"
    resolved "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.1.1.tgz"
    integrity sha512-cOYcUWwhCuHCXi49RhFRCyJEK3iPj1Ziz9DpViV3tbZOwXD49QzIN3MpOLJNxh2qwq2lJJZaKMVw9qNi4jTC0w==
    dependencies:
      spdx-expression-parse "^3.0.0"
      spdx-license-ids "^3.0.0"
  
  spdx-exceptions@^2.1.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz"
    integrity sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A==
  
  spdx-expression-parse@^3.0.0:
    version "3.0.1"
    resolved "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
    integrity sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==
    dependencies:
      spdx-exceptions "^2.1.0"
      spdx-license-ids "^3.0.0"
  
  spdx-license-ids@^3.0.0:
    version "3.0.12"
    resolved "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.12.tgz"
    integrity sha512-rr+VVSXtRhO4OHbXUiAF7xW3Bo9DuuF6C5jH+q/x15j2jniycgKbxU09Hr0WqlSLUs4i4ltHGXqTe7VHclYWyA==
  
  spdy-transport@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/spdy-transport/-/spdy-transport-3.0.0.tgz"
    integrity sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw==
    dependencies:
      debug "^4.1.0"
      detect-node "^2.0.4"
      hpack.js "^2.1.6"
      obuf "^1.1.2"
      readable-stream "^3.0.6"
      wbuf "^1.7.3"
  
  spdy@^4.0.2:
    version "4.0.2"
    resolved "https://registry.npmjs.org/spdy/-/spdy-4.0.2.tgz"
    integrity sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA==
    dependencies:
      debug "^4.1.0"
      handle-thing "^2.0.0"
      http-deceiver "^1.2.7"
      select-hose "^2.0.0"
      spdy-transport "^3.0.0"
  
  split-on-first@^1.0.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/split-on-first/-/split-on-first-1.1.0.tgz"
    integrity sha512-43ZssAJaMusuKWL8sKUBQXHWOpq8d6CfN/u1p4gUzfJkM05C8rxTmYrkIPTXapZpORA6LkkzcUulJ8FqA7Uudw==
  
  sprintf-js@~1.0.2:
    version "1.0.3"
    resolved "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
    integrity sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==
  
  sshpk@^1.7.0:
    version "1.17.0"
    resolved "https://registry.npmjs.org/sshpk/-/sshpk-1.17.0.tgz"
    integrity sha512-/9HIEs1ZXGhSPE8X6Ccm7Nam1z8KcoCqPdI7ecm1N33EzAetWahvQWVqLZtaZQ+IDKX4IyA2o0gBzqIMkAagHQ==
    dependencies:
      asn1 "~0.2.3"
      assert-plus "^1.0.0"
      bcrypt-pbkdf "^1.0.0"
      dashdash "^1.12.0"
      ecc-jsbn "~0.1.1"
      getpass "^0.1.1"
      jsbn "~0.1.0"
      safer-buffer "^2.0.2"
      tweetnacl "~0.14.0"
  
  ssr-window@^3.0.0, ssr-window@^3.0.0-alpha.1:
    version "3.0.0"
    resolved "https://registry.npmjs.org/ssr-window/-/ssr-window-3.0.0.tgz"
    integrity sha512-q+8UfWDg9Itrg0yWK7oe5p/XRCJpJF9OBtXfOPgSJl+u3Xd5KI328RUEvUqSMVM9CiQUEf1QdBzJMkYGErj9QA==
  
  stable@^0.1.8:
    version "0.1.8"
    resolved "https://registry.npmjs.org/stable/-/stable-0.1.8.tgz"
    integrity sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==
  
  stackframe@^1.1.1, stackframe@^1.3.4:
    version "1.3.4"
    resolved "https://registry.npmjs.org/stackframe/-/stackframe-1.3.4.tgz"
    integrity sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==
  
  "statuses@>= 1.4.0 < 2":
    version "1.5.0"
    resolved "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz"
    integrity sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==
  
  statuses@2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz"
    integrity sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==
  
  std-env@^3.0.1:
    version "3.2.1"
    resolved "https://registry.npmjs.org/std-env/-/std-env-3.2.1.tgz"
    integrity sha512-D/uYFWkI/31OrnKmXZqGAGK5GbQRPp/BWA1nuITcc6ICblhhuQUPHS5E2GSCVS7Hwhf4ciq8qsATwBUxv+lI6w==
  
  strict-uri-encode@^1.0.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-1.1.0.tgz"
    integrity sha512-R3f198pcvnB+5IpnBlRkphuE9n46WyVl8I39W/ZUTZLz4nqSP/oLYUrcnJrw462Ds8he4YKMov2efsTIw1BDGQ==
  
  strict-uri-encode@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-2.0.0.tgz"
    integrity sha512-QwiXZgpRcKkhTj2Scnn++4PKtWsH0kpzZ62L2R6c/LUVYv7hVnZqcg2+sMuT6R7Jusu1vviK/MFsu6kNJfWlEQ==
  
  string_decoder@^1.1.1, string_decoder@~1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
    integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
    dependencies:
      safe-buffer "~5.1.0"
  
  string-width@^4.0.0, string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.2, string-width@^4.2.3:
    version "4.2.3"
    resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
    integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
    dependencies:
      emoji-regex "^8.0.0"
      is-fullwidth-code-point "^3.0.0"
      strip-ansi "^6.0.1"
  
  string.fromcodepoint@^0.2.1:
    version "0.2.1"
    resolved "https://registry.npmjs.org/string.fromcodepoint/-/string.fromcodepoint-0.2.1.tgz"
    integrity sha512-n69H31OnxSGSZyZbgBlvYIXlrMhJQ0dQAX1js1QDhpaUH6zmU3QYlj07bCwCNlPOu3oRXIubGPl2gDGnHsiCqg==
  
  string.prototype.matchall@^4.0.8:
    version "4.0.8"
    resolved "https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.8.tgz"
    integrity sha512-6zOCOcJ+RJAQshcTvXPHoxoQGONa3e/Lqx90wUA+wEzX78sg5Bo+1tQo4N0pohS0erG9qtCqJDjNCQBjeWVxyg==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
      get-intrinsic "^1.1.3"
      has-symbols "^1.0.3"
      internal-slot "^1.0.3"
      regexp.prototype.flags "^1.4.3"
      side-channel "^1.0.4"
  
  string.prototype.trim@^1.2.7:
    version "1.2.7"
    resolved "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.7.tgz"
    integrity sha512-p6TmeT1T3411M8Cgg9wBTMRtY2q9+PNy9EV1i2lIXUN/btt763oIfxwN3RR8VU6wHX8j/1CFy0L+YuThm6bgOg==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
  
  string.prototype.trimend@^1.0.6:
    version "1.0.6"
    resolved "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.6.tgz"
    integrity sha512-JySq+4mrPf9EsDBEDYMOb/lM7XQLulwg5R/m1r0PXEFqrV0qHvl58sdTilSXtKOflCsK2E8jxf+GKC0T07RWwQ==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
  
  string.prototype.trimstart@^1.0.6:
    version "1.0.6"
    resolved "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.6.tgz"
    integrity sha512-omqjMDaY92pbn5HOX7f9IccLA+U1tA9GvtU4JrodiXFfYB7jPzzHpRzpglLAjtUV6bB557zwClJezTqnAiYnQA==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
  
  strip-ansi@^6.0.0, strip-ansi@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
    integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
    dependencies:
      ansi-regex "^5.0.1"
  
  strip-ansi@^7.0.0:
    version "7.0.1"
    resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.0.1.tgz"
    integrity sha512-cXNxvT8dFNRVfhVME3JAe98mkXDYN2O1l7jmcwMnOslDeESg1rF/OZMtK0nRAhiari1unG5cD4jG3rapUAkLbw==
    dependencies:
      ansi-regex "^6.0.1"
  
  strip-bom-buf@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/strip-bom-buf/-/strip-bom-buf-1.0.0.tgz"
    integrity sha512-1sUIL1jck0T1mhOLP2c696BIznzT525Lkub+n4jjMHjhjhoAQA6Ye659DxdlZBr0aLDMQoTxKIpnlqxgtwjsuQ==
    dependencies:
      is-utf8 "^0.2.1"
  
  strip-bom-stream@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/strip-bom-stream/-/strip-bom-stream-2.0.0.tgz"
    integrity sha512-yH0+mD8oahBZWnY43vxs4pSinn8SMKAdml/EOGBewoe1Y0Eitd0h2Mg3ZRiXruUW6L4P+lvZiEgbh0NgUGia1w==
    dependencies:
      first-chunk-stream "^2.0.0"
      strip-bom "^2.0.0"
  
  strip-bom@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz"
    integrity sha512-kwrX1y7czp1E69n2ajbG65mIo9dqvJ+8aBQXOGVxqwvNbsXdFM6Lq37dLAY3mknUwru8CfcCbfOLL/gMo+fi3g==
    dependencies:
      is-utf8 "^0.2.0"
  
  strip-bom@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
    integrity sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==
  
  strip-dirs@^2.0.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/strip-dirs/-/strip-dirs-2.1.0.tgz"
    integrity sha512-JOCxOeKLm2CAS73y/U4ZeZPTkE+gNVCzKt7Eox84Iej1LT/2pTWYpZKJuxwQpvX1LiZb1xokNR7RLfuBAa7T3g==
    dependencies:
      is-natural-number "^4.0.1"
  
  strip-final-newline@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
    integrity sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==
  
  strip-indent@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/strip-indent/-/strip-indent-3.0.0.tgz"
    integrity sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==
    dependencies:
      min-indent "^1.0.0"
  
  strip-json-comments@^3.1.0:
    version "3.1.1"
    resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
    integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==
  
  strip-json-comments@^3.1.1:
    version "3.1.1"
    resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
    integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==
  
  strip-json-comments@~2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz"
    integrity sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==
  
  strip-outer@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/strip-outer/-/strip-outer-1.0.1.tgz"
    integrity sha512-k55yxKHwaXnpYGsOzg4Vl8+tDrWylxDEpknGjhTiZB8dFRU5rTo9CAzeycivxV3s+zlTKwrs6WxMxR95n26kwg==
    dependencies:
      escape-string-regexp "^1.0.2"
  
  style-loader@3.3.1:
    version "3.3.1"
    resolved "https://registry.npmjs.org/style-loader/-/style-loader-3.3.1.tgz"
    integrity sha512-GPcQ+LDJbrcxHORTRes6Jy2sfvK2kS6hpSfI/fXhPt+spVzxF6LJ1dHLN9zIGmVaaP044YKaIatFaufENRiDoQ==
  
  style-search@^0.1.0:
    version "0.1.0"
    resolved "https://registry.npmjs.org/style-search/-/style-search-0.1.0.tgz"
    integrity sha512-Dj1Okke1C3uKKwQcetra4jSuk0DqbzbYtXipzFlFMZtowbF1x7BKJwB9AayVMyFARvU8EDrZdcax4At/452cAg==
  
  stylehacks@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/stylehacks/-/stylehacks-5.1.0.tgz"
    integrity sha512-SzLmvHQTrIWfSgljkQCw2++C9+Ne91d/6Sp92I8c5uHTcy/PgeHamwITIbBW9wnFTY/3ZfSXR9HIL6Ikqmcu6Q==
    dependencies:
      browserslist "^4.16.6"
      postcss-selector-parser "^6.0.4"
  
  stylelint@^15.6.1:
    version "15.6.2"
    resolved "https://registry.npmjs.org/stylelint/-/stylelint-15.6.2.tgz"
    integrity sha512-fjQWwcdUye4DU+0oIxNGwawIPC5DvG5kdObY5Sg4rc87untze3gC/5g/ikePqVjrAsBUZjwMN+pZsAYbDO6ArQ==
    dependencies:
      "@csstools/css-parser-algorithms" "^2.1.1"
      "@csstools/css-tokenizer" "^2.1.1"
      "@csstools/media-query-list-parser" "^2.0.4"
      "@csstools/selector-specificity" "^2.2.0"
      balanced-match "^2.0.0"
      colord "^2.9.3"
      cosmiconfig "^8.1.3"
      css-functions-list "^3.1.0"
      css-tree "^2.3.1"
      debug "^4.3.4"
      fast-glob "^3.2.12"
      fastest-levenshtein "^1.0.16"
      file-entry-cache "^6.0.1"
      global-modules "^2.0.0"
      globby "^11.1.0"
      globjoin "^0.1.4"
      html-tags "^3.3.1"
      ignore "^5.2.4"
      import-lazy "^4.0.0"
      imurmurhash "^0.1.4"
      is-plain-object "^5.0.0"
      known-css-properties "^0.27.0"
      mathml-tag-names "^2.1.3"
      meow "^9.0.0"
      micromatch "^4.0.5"
      normalize-path "^3.0.0"
      picocolors "^1.0.0"
      postcss "^8.4.23"
      postcss-media-query-parser "^0.2.3"
      postcss-resolve-nested-selector "^0.1.1"
      postcss-safe-parser "^6.0.0"
      postcss-selector-parser "^6.0.12"
      postcss-value-parser "^4.2.0"
      resolve-from "^5.0.0"
      string-width "^4.2.3"
      strip-ansi "^6.0.1"
      style-search "^0.1.0"
      supports-hyperlinks "^3.0.0"
      svg-tags "^1.0.0"
      table "^6.8.1"
      v8-compile-cache "^2.3.0"
      write-file-atomic "^5.0.1"
  
  stylus-loader@6.2.0:
    version "6.2.0"
    resolved "https://registry.npmjs.org/stylus-loader/-/stylus-loader-6.2.0.tgz"
    integrity sha512-5dsDc7qVQGRoc6pvCL20eYgRUxepZ9FpeK28XhdXaIPP6kXr6nI1zAAKFQgP5OBkOfKaURp4WUpJzspg1f01Gg==
    dependencies:
      fast-glob "^3.2.7"
      klona "^2.0.4"
      normalize-path "^3.0.0"
  
  stylus@^0.55.0, stylus@>=0.52.4:
    version "0.55.0"
    resolved "https://registry.npmjs.org/stylus/-/stylus-0.55.0.tgz"
    integrity sha512-MuzIIVRSbc8XxHH7FjkvWqkIcr1BvoMZoR/oFuAJDlh7VSaNJzrB4uJ38GRQa+mWjLXODAMzeDe0xi9GYbGwnw==
    dependencies:
      css "^3.0.0"
      debug "~3.1.0"
      glob "^7.1.6"
      mkdirp "~1.0.4"
      safer-buffer "^2.1.2"
      sax "~1.2.4"
      semver "^6.3.0"
      source-map "^0.7.3"
  
  supports-color@^5.3.0:
    version "5.5.0"
    resolved "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
    integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
    dependencies:
      has-flag "^3.0.0"
  
  supports-color@^7.0.0, supports-color@^7.1.0:
    version "7.2.0"
    resolved "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
    integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
    dependencies:
      has-flag "^4.0.0"
  
  supports-color@^8.0.0:
    version "8.1.1"
    resolved "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz"
    integrity sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==
    dependencies:
      has-flag "^4.0.0"
  
  supports-hyperlinks@^2.2.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-2.3.0.tgz"
    integrity sha512-RpsAZlpWcDwOPQA22aCH4J0t7L8JmAvsCxfOSEwm7cQs3LshN36QaTkwd70DnBOXDWGssw2eUoc8CaRWT0XunA==
    dependencies:
      has-flag "^4.0.0"
      supports-color "^7.0.0"
  
  supports-hyperlinks@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-3.0.0.tgz"
    integrity sha512-QBDPHyPQDRTy9ku4URNGY5Lah8PAaXs6tAAwp55sL5WCsSW7GIfdf6W5ixfziW+t7wh3GVvHyHHyQ1ESsoRvaA==
    dependencies:
      has-flag "^4.0.0"
      supports-color "^7.0.0"
  
  supports-preserve-symlinks-flag@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
    integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==
  
  svg-tags@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/svg-tags/-/svg-tags-1.0.0.tgz"
    integrity sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA==
  
  svgo@^2.7.0:
    version "2.8.0"
    resolved "https://registry.npmjs.org/svgo/-/svgo-2.8.0.tgz"
    integrity sha512-+N/Q9kV1+F+UeWYoSiULYo4xYSDQlTgb+ayMobAXPwMnLvop7oxKMo9OzIrX5x3eS4L4f2UHhc9axXwY8DpChg==
    dependencies:
      "@trysound/sax" "0.2.0"
      commander "^7.2.0"
      css-select "^4.1.3"
      css-tree "^1.1.3"
      csso "^4.2.0"
      picocolors "^1.0.0"
      stable "^0.1.8"
  
  swiper@6.8.0:
    version "6.8.0"
    resolved "https://registry.npmjs.org/swiper/-/swiper-6.8.0.tgz"
    integrity sha512-6H3e7VOihasMp8sPXNhRDkc61UD0XeFlefbWfUHecBLBTtmA+9WxJiKDBMdzgetK1cny+5+mKfVcsmxYgnEDSw==
    dependencies:
      dom7 "^3.0.0"
      ssr-window "^3.0.0"
  
  symbol-tree@^3.2.4:
    version "3.2.4"
    resolved "https://registry.npmjs.org/symbol-tree/-/symbol-tree-3.2.4.tgz"
    integrity sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==
  
  table@^6.8.1:
    version "6.8.1"
    resolved "https://registry.npmjs.org/table/-/table-6.8.1.tgz"
    integrity sha512-Y4X9zqrCftUhMeH2EptSSERdVKt/nEdijTOacGD/97EKjhQ/Qs8RTlEGABSJNNN8lac9kheH+af7yAkEWlgneA==
    dependencies:
      ajv "^8.0.1"
      lodash.truncate "^4.4.2"
      slice-ansi "^4.0.0"
      string-width "^4.2.3"
      strip-ansi "^6.0.1"
  
  tapable@^1.1.3:
    version "1.1.3"
    resolved "https://registry.npmjs.org/tapable/-/tapable-1.1.3.tgz"
    integrity sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA==
  
  tapable@^2.0.0, tapable@^2.1.1, tapable@^2.2.0:
    version "2.2.1"
    resolved "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz"
    integrity sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==
  
  tar-stream@^1.5.2:
    version "1.6.2"
    resolved "https://registry.npmjs.org/tar-stream/-/tar-stream-1.6.2.tgz"
    integrity sha512-rzS0heiNf8Xn7/mpdSVVSMAWAoy9bfb1WOTYC78Z0UQKeKa/CWS8FOq0lKGNa8DWKAn9gxjCvMLYc5PGXYlK2A==
    dependencies:
      bl "^1.0.0"
      buffer-alloc "^1.2.0"
      end-of-stream "^1.0.0"
      fs-constants "^1.0.0"
      readable-stream "^2.3.0"
      to-buffer "^1.1.1"
      xtend "^4.0.0"
  
  taro-cropper@^1.2.4:
    version "1.2.4"
    resolved "https://registry.npmjs.org/taro-cropper/-/taro-cropper-1.2.4.tgz"
    integrity sha512-Jdf6+AquRLCj6pqlafSl6+LQV5EAaDNVRmzyb7F71EKXNSeJojJtStWCGMRfi9WTCQ4esnSmZI7si7qqfn+04A==
  
  taro-css-to-react-native@3.6.6:
    version "3.6.6"
    resolved "https://registry.npmjs.org/taro-css-to-react-native/-/taro-css-to-react-native-3.6.6.tgz"
    integrity sha512-Xstzd/eEmDnHNET8QMapiNltBTJGeH1n5o0iM4GItJOhDUboPH1GhMkBLpOH6aidpq9rU7J70uNmpK/GtvR06w==
    dependencies:
      camelize "^1.0.0"
      css "^3.0.0"
      css-color-keywords "^1.0.0"
      css-mediaquery "^0.1.2"
      postcss-value-parser "^3.3.0"
  
  terser-webpack-plugin@^5.1.3, terser-webpack-plugin@^5.3.7:
    version "5.3.9"
    resolved "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.9.tgz"
    integrity sha512-ZuXsqE07EcggTWQjXUj+Aot/OMcD0bMKGgF63f7UxYcu5/AJF53aIpK1YoP5xR9l6s/Hy2b+t1AM0bLNPRuhwA==
    dependencies:
      "@jridgewell/trace-mapping" "^0.3.17"
      jest-worker "^27.4.5"
      schema-utils "^3.1.1"
      serialize-javascript "^6.0.1"
      terser "^5.16.8"
  
  terser@^5.10.0, terser@^5.16.8:
    version "5.17.6"
    resolved "https://registry.npmjs.org/terser/-/terser-5.17.6.tgz"
    integrity sha512-V8QHcs8YuyLkLHsJO5ucyff1ykrLVsR4dNnS//L5Y3NiSXpbK1J+WMVUs67eI0KTxs9JtHhgEQpXQVHlHI92DQ==
    dependencies:
      "@jridgewell/source-map" "^0.3.2"
      acorn "^8.5.0"
      commander "^2.20.0"
      source-map-support "~0.5.20"
  
  text-table@^0.2.0:
    version "0.2.0"
    resolved "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
    integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==
  
  textextensions@^5.13.0:
    version "5.15.0"
    resolved "https://registry.npmjs.org/textextensions/-/textextensions-5.15.0.tgz"
    integrity sha512-MeqZRHLuaGamUXGuVn2ivtU3LA3mLCCIO5kUGoohTCoGmCBg/+8yPhWVX9WSl9telvVd8erftjFk9Fwb2dD6rw==
  
  thenify-all@^1.0.0:
    version "1.6.0"
    resolved "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz"
    integrity sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==
    dependencies:
      thenify ">= 3.1.0 < 4"
  
  "thenify@>= 3.1.0 < 4":
    version "3.3.1"
    resolved "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz"
    integrity sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==
    dependencies:
      any-promise "^1.0.0"
  
  throat@^6.0.1:
    version "6.0.2"
    resolved "https://registry.npmjs.org/throat/-/throat-6.0.2.tgz"
    integrity sha512-WKexMoJj3vEuK0yFEapj8y64V0A6xcuPuK9Gt1d0R+dzCSJc0lHqQytAbSB4cDAK0dWh4T0E2ETkoLE2WZ41OQ==
  
  through@^2.3.6, through@^2.3.8:
    version "2.3.8"
    resolved "https://registry.npmjs.org/through/-/through-2.3.8.tgz"
    integrity sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==
  
  thunky@^1.0.2:
    version "1.1.0"
    resolved "https://registry.npmjs.org/thunky/-/thunky-1.1.0.tgz"
    integrity sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==
  
  timed-out@^4.0.1:
    version "4.0.1"
    resolved "https://registry.npmjs.org/timed-out/-/timed-out-4.0.1.tgz"
    integrity sha512-G7r3AhovYtr5YKOWQkta8RKAPb+J9IsO4uVmzjl8AZwfhs8UcUwTiD6gcJYSgOtzyjvQKrKYn41syHbUWMkafA==
  
  tiny-invariant@^1.0.2:
    version "1.3.1"
    resolved "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.1.tgz"
    integrity sha512-AD5ih2NlSssTCwsMznbvwMZpJ1cbhkGd2uueNxzv2jDlEeZdU04JQfRnggJQ8DrcVBGjAsCKwFBbDlVNtEMlzw==
  
  tiny-warning@^1.0.0:
    version "1.0.3"
    resolved "https://registry.npmjs.org/tiny-warning/-/tiny-warning-1.0.3.tgz"
    integrity sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA==
  
  tmp@^0.0.33:
    version "0.0.33"
    resolved "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz"
    integrity sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==
    dependencies:
      os-tmpdir "~1.0.2"
  
  to-buffer@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/to-buffer/-/to-buffer-1.1.1.tgz"
    integrity sha512-lx9B5iv7msuFYE3dytT+KE5tap+rNYw+K4jVkb9R/asAb+pbBSM17jtunHplhBe6RRJdZx3Pn2Jph24O32mOVg==
  
  to-fast-properties@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
    integrity sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==
  
  to-readable-stream@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/to-readable-stream/-/to-readable-stream-1.0.0.tgz"
    integrity sha512-Iq25XBt6zD5npPhlLVXGFN3/gyR2/qODcKNNyTMd4vbm39HUaOiAM4PMq0eMVC/Tkxz+Zjdsc55g9yyz+Yq00Q==
  
  to-regex-range@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
    integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
    dependencies:
      is-number "^7.0.0"
  
  toidentifier@1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz"
    integrity sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==
  
  tough-cookie@^4.1.2:
    version "4.1.2"
    resolved "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.2.tgz"
    integrity sha512-G9fqXWoYFZgTc2z8Q5zaHy/vJMjm+WV0AkAeHxVCQiEB1b+dGvWzFW6QV07cY5jQ5gRkeid2qIkzkxUnmoQZUQ==
    dependencies:
      psl "^1.1.33"
      punycode "^2.1.1"
      universalify "^0.2.0"
      url-parse "^1.5.3"
  
  tough-cookie@~2.5.0:
    version "2.5.0"
    resolved "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.5.0.tgz"
    integrity sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==
    dependencies:
      psl "^1.1.28"
      punycode "^2.1.1"
  
  tr46@^4.1.1:
    version "4.1.1"
    resolved "https://registry.npmjs.org/tr46/-/tr46-4.1.1.tgz"
    integrity sha512-2lv/66T7e5yNyhAAC4NaKe5nVavzuGJQVVtRYLyQ2OI8tsJ61PMLlelehb0wi2Hx6+hT/OJUWZcw8MjlSRnxvw==
    dependencies:
      punycode "^2.3.0"
  
  trim-newlines@^3.0.0:
    version "3.0.1"
    resolved "https://registry.npmjs.org/trim-newlines/-/trim-newlines-3.0.1.tgz"
    integrity sha512-c1PTsA3tYrIsLGkJkzHF+w9F2EyxfXGo4UyJc4pFL++FMjnq0HJS69T3M7d//gKrFKwy429bouPescbjecU+Zw==
  
  trim-repeated@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/trim-repeated/-/trim-repeated-1.0.0.tgz"
    integrity sha512-pkonvlKk8/ZuR0D5tLW8ljt5I8kmxp2XKymhepUeOdCEfKpZaktSArkLHZt76OB1ZvO9bssUsDty4SWhLvZpLg==
    dependencies:
      escape-string-regexp "^1.0.2"
  
  tsconfig-paths@^3.14.1:
    version "3.14.1"
    resolved "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.14.1.tgz"
    integrity sha512-fxDhWnFSLt3VuTwtvJt5fpwxBHg5AdKWMsgcPOOIilyjymcYVZoCQF8fvFRezCNfblEXmi+PcM1eYHeOAgXCOQ==
    dependencies:
      "@types/json5" "^0.0.29"
      json5 "^1.0.1"
      minimist "^1.2.6"
      strip-bom "^3.0.0"
  
  tslib@^1.10.0:
    version "1.14.1"
    resolved "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
    integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==
  
  tslib@^1.8.1:
    version "1.14.1"
    resolved "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
    integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==
  
  tslib@^1.9.0:
    version "1.14.1"
    resolved "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
    integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==
  
  tslib@^2.0.3, tslib@^2.1.0, tslib@^2.3.0:
    version "2.4.0"
    resolved "https://registry.npmjs.org/tslib/-/tslib-2.4.0.tgz"
    integrity sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==
  
  tsutils@^3.21.0:
    version "3.21.0"
    resolved "https://registry.npmjs.org/tsutils/-/tsutils-3.21.0.tgz"
    integrity sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==
    dependencies:
      tslib "^1.8.1"
  
  tunnel-agent@^0.6.0:
    version "0.6.0"
    resolved "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz"
    integrity sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==
    dependencies:
      safe-buffer "^5.0.1"
  
  tweetnacl@^0.14.3, tweetnacl@~0.14.0:
    version "0.14.5"
    resolved "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz"
    integrity sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==
  
  type-check@^0.4.0, type-check@~0.4.0:
    version "0.4.0"
    resolved "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
    integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
    dependencies:
      prelude-ls "^1.2.1"
  
  type-check@~0.3.2:
    version "0.3.2"
    resolved "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz"
    integrity sha512-ZCmOJdvOWDBYJlzAoFkC+Q0+bUyEOS1ltgp1MGU03fqHG+dbi9tBFU2Rd9QKiDZFAYrhPh2JUf7rZRIuHRKtOg==
    dependencies:
      prelude-ls "~1.1.2"
  
  type-fest@^0.18.0, "type-fest@>=0.17.0 <4.0.0":
    version "0.18.1"
    resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.18.1.tgz"
    integrity sha512-OIAYXk8+ISY+qTOwkHtKqzAuxchoMiD9Udx+FSGQDuiRR+PJKJHc2NJAXlbhkGwTt/4/nKZxELY1w3ReWOL8mw==
  
  type-fest@^0.20.2:
    version "0.20.2"
    resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz"
    integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==
  
  type-fest@^0.21.3:
    version "0.21.3"
    resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz"
    integrity sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==
  
  type-fest@^0.6.0:
    version "0.6.0"
    resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.6.0.tgz"
    integrity sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==
  
  type-fest@^0.8.1:
    version "0.8.1"
    resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.8.1.tgz"
    integrity sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==
  
  type-is@~1.6.18:
    version "1.6.18"
    resolved "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz"
    integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
    dependencies:
      media-typer "0.3.0"
      mime-types "~2.1.24"
  
  typed-array-length@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.4.tgz"
    integrity sha512-KjZypGq+I/H7HI5HlOoGHkWUUGq+Q0TPhQurLbyrVrvnKTBgzLhIJ7j6J/XTQOi0d1RjyZ0wdas8bKs2p0x3Ng==
    dependencies:
      call-bind "^1.0.2"
      for-each "^0.3.3"
      is-typed-array "^1.1.9"
  
  typedarray-to-buffer@^3.1.5:
    version "3.1.5"
    resolved "https://registry.npmjs.org/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz"
    integrity sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==
    dependencies:
      is-typedarray "^1.0.0"
  
  typescript@^5.0.3, "typescript@>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta":
    version "5.0.4"
    resolved "https://registry.npmjs.org/typescript/-/typescript-5.0.4.tgz"
    integrity sha512-cW9T5W9xY37cc+jfEnaUvX91foxtHkza3Nw3wkoF4sSlKn0MONdkdEndig/qPBWXNkmplh3NzayQzCiHM4/hqw==
  
  uglify-js@^3.5.1:
    version "3.17.2"
    resolved "https://registry.npmjs.org/uglify-js/-/uglify-js-3.17.2.tgz"
    integrity sha512-bbxglRjsGQMchfvXZNusUcYgiB9Hx2K4AHYXQy2DITZ9Rd+JzhX7+hoocE5Winr7z2oHvPsekkBwXtigvxevXg==
  
  unbox-primitive@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.0.2.tgz"
    integrity sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==
    dependencies:
      call-bind "^1.0.2"
      has-bigints "^1.0.2"
      has-symbols "^1.0.3"
      which-boxed-primitive "^1.0.2"
  
  unbzip2-stream@^1.0.9:
    version "1.4.3"
    resolved "https://registry.npmjs.org/unbzip2-stream/-/unbzip2-stream-1.4.3.tgz"
    integrity sha512-mlExGW4w71ebDJviH16lQLtZS32VKqsSfk80GCfUlwT/4/hNRFsoscrF/c++9xinkMzECL1uL9DDwXqFWkruPg==
    dependencies:
      buffer "^5.2.1"
      through "^2.3.8"
  
  unescape-js@^1.1.1:
    version "1.1.4"
    resolved "https://registry.npmjs.org/unescape-js/-/unescape-js-1.1.4.tgz"
    integrity sha512-42SD8NOQEhdYntEiUQdYq/1V/YHwr1HLwlHuTJB5InVVdOSbgI6xu8jK5q65yIzuFCfczzyDF/7hbGzVbyCw0g==
    dependencies:
      string.fromcodepoint "^0.2.1"
  
  unicode-canonical-property-names-ecmascript@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz"
    integrity sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ==
  
  unicode-match-property-ecmascript@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
    integrity sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==
    dependencies:
      unicode-canonical-property-names-ecmascript "^2.0.0"
      unicode-property-aliases-ecmascript "^2.0.0"
  
  unicode-match-property-value-ecmascript@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.0.0.tgz"
    integrity sha512-7Yhkc0Ye+t4PNYzOGKedDhXbYIBe1XEQYQxOPyhcXNMJ0WCABqqj6ckydd6pWRZTHV4GuCPKdBAUiMc60tsKVw==
  
  unicode-property-aliases-ecmascript@^2.0.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz"
    integrity sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==
  
  unique-string@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/unique-string/-/unique-string-2.0.0.tgz"
    integrity sha512-uNaeirEPvpZWSgzwsPGtU2zVSTrn/8L5q/IexZmH0eH6SA73CmAA5U4GwORTxQAZs95TAXLNqeLoPPNO5gZfWg==
    dependencies:
      crypto-random-string "^2.0.0"
  
  universal-router@^8.3.0:
    version "8.3.0"
    resolved "https://registry.npmjs.org/universal-router/-/universal-router-8.3.0.tgz"
    integrity sha512-cBkihRoHvRQAjdUnDE1GGuuw/TPAIi8z2pEsSmUVAWLeZdgjHzzAb1+0VOO6NvBOvySItOTQikzaGlRxRdJBnA==
    dependencies:
      path-to-regexp "^3.1.0"
  
  universalify@^0.1.0:
    version "0.1.2"
    resolved "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz"
    integrity sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==
  
  universalify@^0.2.0:
    version "0.2.0"
    resolved "https://registry.npmjs.org/universalify/-/universalify-0.2.0.tgz"
    integrity sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==
  
  unpipe@~1.0.0, unpipe@1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
    integrity sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==
  
  update-browserslist-db@^1.0.9:
    version "1.0.9"
    resolved "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.9.tgz"
    integrity sha512-/xsqn21EGVdXI3EXSum1Yckj3ZVZugqyOZQ/CxYPBD/R+ko9NSUScf8tFF4dOKY+2pvSSJA/S+5B8s4Zr4kyvg==
    dependencies:
      escalade "^3.1.1"
      picocolors "^1.0.0"
  
  update-notifier@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmjs.org/update-notifier/-/update-notifier-5.1.0.tgz"
    integrity sha512-ItnICHbeMh9GqUy31hFPrD1kcuZ3rpxDZbf4KUDavXwS0bW5m7SLbDQpGX3UYr072cbrF5hFUs3r5tUsPwjfHw==
    dependencies:
      boxen "^5.0.0"
      chalk "^4.1.0"
      configstore "^5.0.1"
      has-yarn "^2.1.0"
      import-lazy "^2.1.0"
      is-ci "^2.0.0"
      is-installed-globally "^0.4.0"
      is-npm "^5.0.0"
      is-yarn-global "^0.3.0"
      latest-version "^5.1.0"
      pupa "^2.1.1"
      semver "^7.3.4"
      semver-diff "^3.1.1"
      xdg-basedir "^4.0.0"
  
  upper-case@^1.1.1:
    version "1.1.3"
    resolved "https://registry.npmjs.org/upper-case/-/upper-case-1.1.3.tgz"
    integrity sha512-WRbjgmYzgXkCV7zNVpy5YgrHgbBv126rMALQQMrmzOVC4GM2waQ9x7xtm8VU+1yF2kWyPzI9zbZ48n4vSxwfSA==
  
  uri-js@^4.2.2:
    version "4.4.1"
    resolved "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
    integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
    dependencies:
      punycode "^2.1.0"
  
  url-loader@4.1.0:
    version "4.1.0"
    resolved "https://registry.npmjs.org/url-loader/-/url-loader-4.1.0.tgz"
    integrity sha512-IzgAAIC8wRrg6NYkFIJY09vtktQcsvU8V6HhtQj9PTefbYImzLB1hufqo4m+RyM5N3mLx5BqJKccgxJS+W3kqw==
    dependencies:
      loader-utils "^2.0.0"
      mime-types "^2.1.26"
      schema-utils "^2.6.5"
  
  url-parse-lax@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/url-parse-lax/-/url-parse-lax-3.0.0.tgz"
    integrity sha512-NjFKA0DidqPa5ciFcSrXnAltTtzz84ogy+NebPvfEgAck0+TNg4UJ4IN+fB7zRZfbgUf0syOo9MDxFkDSMuFaQ==
    dependencies:
      prepend-http "^2.0.0"
  
  url-parse@^1.5.3:
    version "1.5.10"
    resolved "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz"
    integrity sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==
    dependencies:
      querystringify "^2.1.1"
      requires-port "^1.0.0"
  
  url-to-options@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/url-to-options/-/url-to-options-1.0.1.tgz"
    integrity sha512-0kQLIzG4fdk/G5NONku64rSH/x32NOA39LVQqlK8Le6lvTF6GGRJpqaQFGgU+CLwySIqBSMdwYM0sYcW9f6P4A==
  
  util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
    version "1.0.2"
    resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
    integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==
  
  utila@~0.4:
    version "0.4.0"
    resolved "https://registry.npmjs.org/utila/-/utila-0.4.0.tgz"
    integrity sha512-Z0DbgELS9/L/75wZbro8xAnT50pBVFQZ+hUEueGDU5FN51YSCYM+jdxsfCiHjwNP/4LCDD0i/graKpeBnOXKRA==
  
  utils-merge@1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
    integrity sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==
  
  uuid@^3.3.2:
    version "3.4.0"
    resolved "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz"
    integrity sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==
  
  uuid@^8.3.2:
    version "8.3.2"
    resolved "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
    integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==
  
  v8-compile-cache@^2.3.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz"
    integrity sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA==
  
  validate-npm-package-license@^3.0.1:
    version "3.0.4"
    resolved "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
    integrity sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==
    dependencies:
      spdx-correct "^3.0.0"
      spdx-expression-parse "^3.0.0"
  
  validate-npm-package-name@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/validate-npm-package-name/-/validate-npm-package-name-5.0.0.tgz"
    integrity sha512-YuKoXDAhBYxY7SfOKxHBDoSyENFeW5VvIIQp2TGQuit8gpK6MnWaQelBKxso72DoxTZfZdcP3W90LqpSkgPzLQ==
    dependencies:
      builtins "^5.0.0"
  
  value-equal@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/value-equal/-/value-equal-1.0.1.tgz"
    integrity sha512-NOJ6JZCAWr0zlxZt+xqCHNTEKOsrks2HQd4MqhP1qy4z1SkbEP467eNx6TgDKXMvUOb+OENfJCZwM+16n7fRfw==
  
  vary@~1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
    integrity sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==
  
  verror@1.10.0:
    version "1.10.0"
    resolved "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz"
    integrity sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==
    dependencies:
      assert-plus "^1.0.0"
      core-util-is "1.0.2"
      extsprintf "^1.2.0"
  
  vinyl-file@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/vinyl-file/-/vinyl-file-3.0.0.tgz"
    integrity sha512-BoJDj+ca3D9xOuPEM6RWVtWQtvEPQiQYn82LvdxhLWplfQsBzBqtgK0yhCP0s1BNTi6dH9BO+dzybvyQIacifg==
    dependencies:
      graceful-fs "^4.1.2"
      pify "^2.3.0"
      strip-bom-buf "^1.0.0"
      strip-bom-stream "^2.0.0"
      vinyl "^2.0.1"
  
  vinyl@^2.0.1:
    version "2.2.1"
    resolved "https://registry.npmjs.org/vinyl/-/vinyl-2.2.1.tgz"
    integrity sha512-LII3bXRFBZLlezoG5FfZVcXflZgWP/4dCwKtxd5ky9+LOtM4CS3bIRQsmR1KMnMW07jpE8fqR2lcxPZ+8sJIcw==
    dependencies:
      clone "^2.1.1"
      clone-buffer "^1.0.0"
      clone-stats "^1.0.0"
      cloneable-readable "^1.0.0"
      remove-trailing-separator "^1.0.1"
      replace-ext "^1.0.0"
  
  vm2@^3.8.4:
    version "3.9.11"
    resolved "https://registry.npmjs.org/vm2/-/vm2-3.9.11.tgz"
    integrity sha512-PFG8iJRSjvvBdisowQ7iVF580DXb1uCIiGaXgm7tynMR1uTBlv7UJlB1zdv5KJ+Tmq1f0Upnj3fayoEOPpCBKg==
    dependencies:
      acorn "^8.7.0"
      acorn-walk "^8.2.0"
  
  vue-hot-reload-api@^2.3.0:
    version "2.3.4"
    resolved "https://registry.npmjs.org/vue-hot-reload-api/-/vue-hot-reload-api-2.3.4.tgz"
    integrity sha512-BXq3jwIagosjgNVae6tkHzzIk6a8MHFtzAdwhnV5VlvPTFxDCvIttgSiHWjdGoTJvXtmRu5HacExfdarRcFhog==
  
  vue-loader@^15.10.1:
    version "15.10.1"
    resolved "https://registry.npmjs.org/vue-loader/-/vue-loader-15.10.1.tgz"
    integrity sha512-SaPHK1A01VrNthlix6h1hq4uJu7S/z0kdLUb6klubo738NeQoLbS6V9/d8Pv19tU0XdQKju3D1HSKuI8wJ5wMA==
    dependencies:
      "@vue/component-compiler-utils" "^3.1.0"
      hash-sum "^1.0.2"
      loader-utils "^1.1.0"
      vue-hot-reload-api "^2.3.0"
      vue-style-loader "^4.1.0"
  
  vue-style-loader@^4.1.0:
    version "4.1.3"
    resolved "https://registry.npmjs.org/vue-style-loader/-/vue-style-loader-4.1.3.tgz"
    integrity sha512-sFuh0xfbtpRlKfm39ss/ikqs9AbKCoXZBpHeVZ8Tx650o0k0q/YCM7FRvigtxpACezfq6af+a7JeqVTWvncqDg==
    dependencies:
      hash-sum "^1.0.2"
      loader-utils "^1.0.2"
  
  vue-template-es2015-compiler@^1.9.0:
    version "1.9.1"
    resolved "https://registry.npmjs.org/vue-template-es2015-compiler/-/vue-template-es2015-compiler-1.9.1.tgz"
    integrity sha512-4gDntzrifFnCEvyoO8PqyJDmguXgVPxKiIxrBKjIowvL9l+N66196+72XVYR8BBf1Uv1Fgt3bGevJ+sEmxfZzw==
  
  w3c-xmlserializer@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-4.0.0.tgz"
    integrity sha512-d+BFHzbiCx6zGfz0HyQ6Rg69w9k19nviJspaj4yNscGjrHu94sVP+aRm75yEbCh+r2/yR+7q6hux9LVtbuTGBw==
    dependencies:
      xml-name-validator "^4.0.0"
  
  watchpack@^2.4.0:
    version "2.4.0"
    resolved "https://registry.npmjs.org/watchpack/-/watchpack-2.4.0.tgz"
    integrity sha512-Lcvm7MGST/4fup+ifyKi2hjyIAwcdI4HRgtvTpIUxBRhB+RFtUh8XtDOxUfctVCnhVi+QQj49i91OyvzkJl6cg==
    dependencies:
      glob-to-regexp "^0.4.1"
      graceful-fs "^4.1.2"
  
  wbuf@^1.1.0, wbuf@^1.7.3:
    version "1.7.3"
    resolved "https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz"
    integrity sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==
    dependencies:
      minimalistic-assert "^1.0.0"
  
  wcwidth@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz"
    integrity sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==
    dependencies:
      defaults "^1.0.3"
  
  webidl-conversions@^7.0.0:
    version "7.0.0"
    resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz"
    integrity sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==
  
  webpack-chain@6.5.1:
    version "6.5.1"
    resolved "https://registry.npmjs.org/webpack-chain/-/webpack-chain-6.5.1.tgz"
    integrity sha512-7doO/SRtLu8q5WM0s7vPKPWX580qhi0/yBHkOxNkv50f6qB76Zy9o2wRTrrPULqYTvQlVHuvbA8v+G5ayuUDsA==
    dependencies:
      deepmerge "^1.5.2"
      javascript-stringify "^2.0.1"
  
  webpack-dev-middleware@^5.3.1:
    version "5.3.3"
    resolved "https://registry.npmjs.org/webpack-dev-middleware/-/webpack-dev-middleware-5.3.3.tgz"
    integrity sha512-hj5CYrY0bZLB+eTO+x/j67Pkrquiy7kWepMHmUMoPsmcUaeEnQJqFzHJOyxgWlq746/wUuA64p9ta34Kyb01pA==
    dependencies:
      colorette "^2.0.10"
      memfs "^3.4.3"
      mime-types "^2.1.31"
      range-parser "^1.2.1"
      schema-utils "^4.0.0"
  
  "webpack-dev-server@3.x || 4.x", webpack-dev-server@4.7.4:
    version "4.7.4"
    resolved "https://registry.npmjs.org/webpack-dev-server/-/webpack-dev-server-4.7.4.tgz"
    integrity sha512-nfdsb02Zi2qzkNmgtZjkrMOcXnYZ6FLKcQwpxT7MvmHKc+oTtDsBju8j+NMyAygZ9GW1jMEUpy3itHtqgEhe1A==
    dependencies:
      "@types/bonjour" "^3.5.9"
      "@types/connect-history-api-fallback" "^1.3.5"
      "@types/express" "^4.17.13"
      "@types/serve-index" "^1.9.1"
      "@types/sockjs" "^0.3.33"
      "@types/ws" "^8.2.2"
      ansi-html-community "^0.0.8"
      bonjour "^3.5.0"
      chokidar "^3.5.3"
      colorette "^2.0.10"
      compression "^1.7.4"
      connect-history-api-fallback "^1.6.0"
      default-gateway "^6.0.3"
      del "^6.0.0"
      express "^4.17.1"
      graceful-fs "^4.2.6"
      html-entities "^2.3.2"
      http-proxy-middleware "^2.0.0"
      ipaddr.js "^2.0.1"
      open "^8.0.9"
      p-retry "^4.5.0"
      portfinder "^1.0.28"
      schema-utils "^4.0.0"
      selfsigned "^2.0.0"
      serve-index "^1.9.1"
      sockjs "^0.3.21"
      spdy "^4.0.2"
      strip-ansi "^7.0.0"
      webpack-dev-middleware "^5.3.1"
      ws "^8.4.2"
  
  webpack-format-messages@^2.0.6:
    version "2.0.6"
    resolved "https://registry.npmjs.org/webpack-format-messages/-/webpack-format-messages-2.0.6.tgz"
    integrity sha512-JOUviZSCupGTf6uJjrxKMEyOawWws566e3phwSyuWBsQxuBU6Gm4QV5wdU8UfkPIhWyhAqSGKeq8fNE9Q4rs9Q==
    dependencies:
      kleur "^3.0.0"
  
  webpack-merge@^4.2.2:
    version "4.2.2"
    resolved "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.2.2.tgz"
    integrity sha512-TUE1UGoTX2Cd42j3krGYqObZbOD+xF7u28WB7tfUordytSjbWTIjK/8V0amkBfTYN4/pB/GIDlJZZ657BGG19g==
    dependencies:
      lodash "^4.17.15"
  
  webpack-sources@^2.2.0:
    version "2.3.1"
    resolved "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.3.1.tgz"
    integrity sha512-y9EI9AO42JjEcrTJFOYmVywVZdKVUfOvDUPsJea5GIr1JOEGFVqwlY2K098fFoIjOkDzHn2AjRvM8dsBZu+gCA==
    dependencies:
      source-list-map "^2.0.1"
      source-map "^0.6.1"
  
  webpack-sources@^3.2.3:
    version "3.2.3"
    resolved "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz"
    integrity sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==
  
  webpack-virtual-modules@^0.5.0:
    version "0.5.0"
    resolved "https://registry.npmjs.org/webpack-virtual-modules/-/webpack-virtual-modules-0.5.0.tgz"
    integrity sha512-kyDivFZ7ZM0BVOUteVbDFhlRt7Ah/CSPwJdi8hBpkK7QLumUqdLtVfm/PX/hkcnrvr0i77fO5+TjZ94Pe+C9iw==
  
  "webpack@^3.0.0 || ^4.1.0 || ^5.0.0-0", "webpack@^4.0.0 || ^5.0.0", "webpack@^4.37.0 || ^5.0.0", "webpack@^4.40.0 || ^5.0.0", webpack@^5.0.0, webpack@^5.1.0, webpack@^5.20.0, webpack@^5.78.0, webpack@>=2, "webpack@>=4.43.0 <6.0.0", "webpack@3 || 4 || 5", webpack@5.82.0:
    version "5.82.0"
    resolved "https://registry.npmjs.org/webpack/-/webpack-5.82.0.tgz"
    integrity sha512-iGNA2fHhnDcV1bONdUu554eZx+XeldsaeQ8T67H6KKHl2nUSwX8Zm7cmzOA46ox/X1ARxf7Bjv8wQ/HsB5fxBg==
    dependencies:
      "@types/eslint-scope" "^3.7.3"
      "@types/estree" "^1.0.0"
      "@webassemblyjs/ast" "^1.11.5"
      "@webassemblyjs/wasm-edit" "^1.11.5"
      "@webassemblyjs/wasm-parser" "^1.11.5"
      acorn "^8.7.1"
      acorn-import-assertions "^1.7.6"
      browserslist "^4.14.5"
      chrome-trace-event "^1.0.2"
      enhanced-resolve "^5.13.0"
      es-module-lexer "^1.2.1"
      eslint-scope "5.1.1"
      events "^3.2.0"
      glob-to-regexp "^0.4.1"
      graceful-fs "^4.2.9"
      json-parse-even-better-errors "^2.3.1"
      loader-runner "^4.2.0"
      mime-types "^2.1.27"
      neo-async "^2.6.2"
      schema-utils "^3.1.2"
      tapable "^2.1.1"
      terser-webpack-plugin "^5.3.7"
      watchpack "^2.4.0"
      webpack-sources "^3.2.3"
  
  webpackbar@^5.0.2:
    version "5.0.2"
    resolved "https://registry.npmjs.org/webpackbar/-/webpackbar-5.0.2.tgz"
    integrity sha512-BmFJo7veBDgQzfWXl/wwYXr/VFus0614qZ8i9znqcl9fnEdiVkdbi0TedLQ6xAK92HZHDJ0QmyQ0fmuZPAgCYQ==
    dependencies:
      chalk "^4.1.0"
      consola "^2.15.3"
      pretty-time "^1.1.0"
      std-env "^3.0.1"
  
  websocket-driver@^0.7.4, websocket-driver@>=0.5.1:
    version "0.7.4"
    resolved "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz"
    integrity sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==
    dependencies:
      http-parser-js ">=0.5.1"
      safe-buffer ">=5.1.0"
      websocket-extensions ">=0.1.1"
  
  websocket-extensions@>=0.1.1:
    version "0.1.4"
    resolved "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.4.tgz"
    integrity sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==
  
  weui@^1.1.2:
    version "1.1.3"
    resolved "https://registry.npmjs.org/weui/-/weui-1.1.3.tgz"
    integrity sha512-vC6eWUvG1MYoE8yLsvBBmLB2+4DZWynQOL47MUscHMwPVltOZPGsiRb2PE7y3z+w3ElF1SsmJsyhr40wiXgP5A==
  
  whatwg-encoding@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-2.0.0.tgz"
    integrity sha512-p41ogyeMUrw3jWclHWTQg1k05DSVXPLcVxRTYsXUk+ZooOCZLcoYgPZ/HL/D/N+uQPOtcp1me1WhBEaX02mhWg==
    dependencies:
      iconv-lite "0.6.3"
  
  whatwg-fetch@^3.4.0:
    version "3.6.2"
    resolved "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.2.tgz"
    integrity sha512-bJlen0FcuU/0EMLrdbJ7zOnW6ITZLrZMIarMUVmdKtsGvZna8vxKYaexICWPfZ8qwf9fzNq+UEIZrnSaApt6RA==
  
  whatwg-mimetype@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-3.0.0.tgz"
    integrity sha512-nt+N2dzIutVRxARx1nghPKGv1xHikU7HKdfafKkLNLindmPU/ch3U31NOCGGA/dmPcmb1VlofO0vnKAcsm0o/Q==
  
  whatwg-url@^12.0.0, whatwg-url@^12.0.1:
    version "12.0.1"
    resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-12.0.1.tgz"
    integrity sha512-Ed/LrqB8EPlGxjS+TrsXcpUond1mhccS3pchLhzSgPCnTimUCKj3IZE75pAs5m6heB2U2TMerKFUXheyHY+VDQ==
    dependencies:
      tr46 "^4.1.1"
      webidl-conversions "^7.0.0"
  
  which-boxed-primitive@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
    integrity sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==
    dependencies:
      is-bigint "^1.0.1"
      is-boolean-object "^1.1.0"
      is-number-object "^1.0.4"
      is-string "^1.0.5"
      is-symbol "^1.0.3"
  
  which-pm@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/which-pm/-/which-pm-2.0.0.tgz"
    integrity sha512-Lhs9Pmyph0p5n5Z3mVnN0yWcbQYUAD7rbQUiMsQxOJ3T57k7RFe35SUwWMf7dsbDZks1uOmw4AecB/JMDj3v/w==
    dependencies:
      load-yaml-file "^0.2.0"
      path-exists "^4.0.0"
  
  which-typed-array@^1.1.9:
    version "1.1.9"
    resolved "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.9.tgz"
    integrity sha512-w9c4xkx6mPidwp7180ckYWfMmvxpjlZuIudNtDf4N/tTAUB8VJbX25qZoAsrtGuYNnGw3pa0AXgbGKRB8/EceA==
    dependencies:
      available-typed-arrays "^1.0.5"
      call-bind "^1.0.2"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-tostringtag "^1.0.0"
      is-typed-array "^1.1.10"
  
  which@^1.3.1:
    version "1.3.1"
    resolved "https://registry.npmjs.org/which/-/which-1.3.1.tgz"
    integrity sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==
    dependencies:
      isexe "^2.0.0"
  
  which@^2.0.1:
    version "2.0.2"
    resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
    integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
    dependencies:
      isexe "^2.0.0"
  
  widest-line@^3.1.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/widest-line/-/widest-line-3.1.0.tgz"
    integrity sha512-NsmoXalsWVDMGupxZ5R08ka9flZjjiLvHVAWYOKtiKM8ujtZWr9cRffak+uSE48+Ob8ObalXpwyeUiyDD6QFgg==
    dependencies:
      string-width "^4.0.0"
  
  word-wrap@^1.2.3, word-wrap@~1.2.3:
    version "1.2.3"
    resolved "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.3.tgz"
    integrity sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==
  
  wrap-ansi@^7.0.0:
    version "7.0.0"
    resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
    integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
    dependencies:
      ansi-styles "^4.0.0"
      string-width "^4.1.0"
      strip-ansi "^6.0.0"
  
  wrappy@1:
    version "1.0.2"
    resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
    integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==
  
  write-file-atomic@^3.0.0:
    version "3.0.3"
    resolved "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.3.tgz"
    integrity sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==
    dependencies:
      imurmurhash "^0.1.4"
      is-typedarray "^1.0.0"
      signal-exit "^3.0.2"
      typedarray-to-buffer "^3.1.5"
  
  write-file-atomic@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-5.0.1.tgz"
    integrity sha512-+QU2zd6OTD8XWIJCbffaiQeH9U73qIqafo1x6V1snCWYGJf6cVE0cDR4D8xRzcEnfI21IFrUPzPGtcPf8AC+Rw==
    dependencies:
      imurmurhash "^0.1.4"
      signal-exit "^4.0.1"
  
  ws@^8.13.0, ws@^8.4.2:
    version "8.13.0"
    resolved "https://registry.npmjs.org/ws/-/ws-8.13.0.tgz"
    integrity sha512-x9vcZYTrFPC7aSIbj7sRCYo7L/Xb8Iy+pW0ng0wt2vCJv7M9HOMy0UoN3rr+IFC7hb7vXoqS+P9ktyLLLhO+LA==
  
  xdg-basedir@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/xdg-basedir/-/xdg-basedir-4.0.0.tgz"
    integrity sha512-PSNhEJDejZYV7h50BohL09Er9VaIefr2LMAf3OEmpCkjOi34eYyQYAXUTjEQtZJTKcF0E2UKTh+osDLsgNim9Q==
  
  xml-name-validator@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-4.0.0.tgz"
    integrity sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==
  
  xml2js@^0.4.19:
    version "0.4.23"
    resolved "https://registry.npmjs.org/xml2js/-/xml2js-0.4.23.tgz"
    integrity sha512-ySPiMjM0+pLDftHgXY4By0uswI3SPKLDw/i3UXbnO8M/p28zqexCUoPmQFrYD+/1BzhGJSs2i1ERWKJAtiLrug==
    dependencies:
      sax ">=0.6.0"
      xmlbuilder "~11.0.0"
  
  xmlbuilder@~11.0.0:
    version "11.0.1"
    resolved "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-11.0.1.tgz"
    integrity sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==
  
  xmlchars@^2.2.0:
    version "2.2.0"
    resolved "https://registry.npmjs.org/xmlchars/-/xmlchars-2.2.0.tgz"
    integrity sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==
  
  xtend@^4.0.0, xtend@^4.0.2:
    version "4.0.2"
    resolved "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
    integrity sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==
  
  xxhashjs@~0.2.2:
    version "0.2.2"
    resolved "https://registry.npmjs.org/xxhashjs/-/xxhashjs-0.2.2.tgz"
    integrity sha512-AkTuIuVTET12tpsVIQo+ZU6f/qDmKuRUcjaqR+OIvm+aCBsZ95i7UVY5WJ9TMsSaZ0DA2WxoZ4acu0sPH+OKAw==
    dependencies:
      cuint "^0.2.2"
  
  y18n@^5.0.5:
    version "5.0.8"
    resolved "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz"
    integrity sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==
  
  yallist@^2.1.2:
    version "2.1.2"
    resolved "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz"
    integrity sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==
  
  yallist@^3.0.2:
    version "3.1.1"
    resolved "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
    integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==
  
  yallist@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
    integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==
  
  yaml@^1.10.0, yaml@^1.10.2:
    version "1.10.2"
    resolved "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz"
    integrity sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==
  
  yargs-parser@^20.2.2, yargs-parser@^20.2.3:
    version "20.2.9"
    resolved "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz"
    integrity sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==
  
  yargs@^16.0.0, yargs@^16.1.0:
    version "16.2.0"
    resolved "https://registry.npmjs.org/yargs/-/yargs-16.2.0.tgz"
    integrity sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==
    dependencies:
      cliui "^7.0.2"
      escalade "^3.1.1"
      get-caller-file "^2.0.5"
      require-directory "^2.1.1"
      string-width "^4.2.0"
      y18n "^5.0.5"
      yargs-parser "^20.2.2"
  
  yauzl@^2.4.2, yauzl@2.10.0:
    version "2.10.0"
    resolved "https://registry.npmjs.org/yauzl/-/yauzl-2.10.0.tgz"
    integrity sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g==
    dependencies:
      buffer-crc32 "~0.2.3"
      fd-slicer "~1.1.0"
  
  yocto-queue@^0.1.0:
    version "0.1.0"
    resolved "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
    integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==
