<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="dev:weapp" type="js.build_tools.npm" nameIsGenerated="true">
    <package-json value="$PROJECT_DIR$/package.json" />
    <command value="run" />
    <scripts>
      <script value="dev:weapp" />
    </scripts>
    <node-interpreter value="project" />
    <envs />
    <method v="2" />
  </configuration>
</component>