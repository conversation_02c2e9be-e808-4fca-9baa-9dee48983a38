import Taro from "@tarojs/taro";

interface RequestOptions {
    url: string;
    method?: "GET" | "POST" | "PUT" | "DELETE";
    data?: any;
    header?: Record<string, string>;
}

interface ResponseData<T> {
    code: number;
    message: string;
    data: T;
}

export async function request<T>(options: RequestOptions): Promise<ResponseData<T>> {
    const { url, method = "GET", data, header = {} } = options;

    try {
        const token = Taro.getStorageSync("token");
        if (token) {
            header["Authorization"] = `Bearer ${token}`;
        }

        const response = await Taro.request({
            url: `${process.env.API_BASE_URL || ""}${url}`,
            method,
            data,
            header: {
                "Content-Type": "application/json",
                ...header,
            },
        });

        console.log("response.statusCode:" + response.statusCode);

        if (response.statusCode >= 200 && response.statusCode < 300) {
            return response.data as ResponseData<T>;
        } else {
            throw new Error(response.data?.message || "请求失败");
        }
    } catch (error) {
        console.error("Request error:", error);
        throw error;
    }
}
