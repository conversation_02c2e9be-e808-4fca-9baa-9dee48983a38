const util = {};

import Taro from "@tarojs/taro";

import { Loading1 } from "@nutui/icons-react-taro";
import { Current } from "@tarojs/runtime";
import request from "@/service/request";

// import { Current } from '@tarojs/runtime';

// /**
//  * 返回页面的参数
//  * @returns
//  */
// export const params = () => {
//     return Current.router?.params;
// }

/**
 * URL 二维码  扫码进来的时候  q  参数的解析
 * @param options
 * @param name
 * @returns
 */
export const QValue = (name?: string | undefined) => {
    const options: any = Current.router?.params;

    if (!options) {
        console.log("QValue no options");
        return "";
    }

    if (!name) {
        return sceneValue(options);
    }

    if (options[name] !== undefined) {
        console.log("Found with exact case:", options[name]);
        return options[name];
    }

    // Try case-insensitive match
    const lowerName = name.toLowerCase();
    for (const key in options) {
        if (key.toLowerCase() === lowerName) {
            console.log("Found with case-insensitive match:", options[key]);
            return options[key];
        }
    }

    // Handle scene parameter if present
    if (options["scene"]) {
        console.log("Checking scene");
        return sceneValue(options, name);
    }

    // Handle URL query parameter if present
    if (options.q) {
        console.log("Checking query");
        let q = decodeURIComponent(options.q);
        q = q.split("?")[1];

        if (q) {
            let qArray = q.split("&");
            for (let i = 0; i < qArray.length; i++) {
                if (qArray[i].split("=")[0] == name) {
                    return qArray[i].split("=")[1];
                }
            }
        }
    }

    console.log("No value found, returning empty string");
    return "";
};

/**
 * 小程序二维码 的参数提取
 * @param options
 * @param name
 * @returns
 */
export const sceneValue = (options: any, name?: string | undefined) => {
    if (!options) {
        console.log("no options");
        return "";
    } else {
        let scene = options["scene"];
        if (!scene) {
            return "";
        }

        scene = decodeURIComponent(scene);

        if (!name) {
            return scene;
        }

        let sceneArray = scene.split("&");

        for (let i = 0; i < sceneArray.length; i++) {
            if (sceneArray[i].split("=")[0] == name) {
                return sceneArray[i].split("=")[1];
            }
        }
        return "";
    }
};

/**
 * 获得 用户定位 权限
 * @returns
 */
export const getUserLocation = () => {
    return new Promise(function (resolve, reject) {
        Taro.getSetting({
            success: (res) => {
                if (!res.authSetting["scope.userLocation"]) {
                    Taro.authorize({
                        scope: "scope.userLocation",
                        success: () => {
                            resolve(true);
                        },
                        fail: () => {
                            reject(false);
                        },
                    });
                } else {
                    resolve(true);
                }
            },
            fail: () => {
                reject(false);
            },
        });
    });
};

/**
 * 获得 拍照 权限
 * @returns
 */
export const getCamera = () => {
    return new Promise(function (resolve, reject) {
        Taro.getSetting({
            success: (res) => {
                if (!res.authSetting["scope.camera"]) {
                    Taro.authorize({
                        scope: "scope.camera",
                        success: () => {
                            resolve(true);
                        },
                        fail: () => {
                            reject(false);
                        },
                    });
                } else {
                    resolve(true);
                }
            },
            fail: () => {
                reject(false);
            },
        });
    });
};

/**
 * 弹出面包屑 系统原生的 Toast
 * @param { String } title 标题
 * @param { String } icon 图标内容
 * @param { String } success 成功回调
 */
export const showToast = (title, icon, fn?: Function): any => {
    Taro.showToast({
        title: title,
        icon: icon,
        duration: 2000,
        success: function (res) {
            if (fn) {
                fn(res);
            }
        },
    });
};

export declare type TagType = "text" | "fail" | "success" | "warn" | "loading";
/**
 * 返回 Toast 的类型
 * @param msg
 * @param type
 * @param fn
 * @returns
 */
export const getToast = (msg: string, type: TagType, fn?: Function): API.pdToast => {
    return {
        msg: msg,
        type: type,
        duration: 2,
        fn: (res: any) => {
            if (fn) {
                fn(res);
            }
        },
        icon: "",
        cover: true,
    };
};

/**
 * 返回 Loading 的 NutUi 的
 * @param msg
 * @param fn
 * @returns
 */
export const getLoading = (msg: string, fn?: Function): API.pdToast => {
    // var icon = 'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjY2NzY1MTkwODI4IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjM4MDg3IiBkYXRhLXNwbS1hbmNob3ItaWQ9ImEzMTN4Ljc3ODEwNjkuMC5pNiIgd2lkdGg9IjEyOCIgaGVpZ2h0PSIxMjgiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48cGF0aCBkPSJNOTIzLjQyNCA0NDcuNzQ0IiBwLWlkPSIzODA4OCI+PC9wYXRoPjxwYXRoIGQ9Ik01MTIuMDY0IDk2My4yOTZjLTk2LjE2IDAtMTg5LjM0NC0zMC44MTYtMjY3LjY4LTg5LjQ3Mi05NS43NDQtNzEuNzEyLTE1Ny44NTYtMTc2LjQ4LTE3NC44NDgtMjk0LjkxMi0xNi45OTItMTE4LjQ2NCAxMy4xNTItMjM2LjQ0OCA4NC44NjQtMzMyLjIyNCAxNDguMDk2LTE5Ny43NiA0MjkuNDQtMjM4LjA4IDYyNy4xMzYtOTAuMDggODIuODggNjIuMDggMTQyLjAxNiAxNTEuNTg0IDE2Ni41NiAyNTIgNC4xOTIgMTcuMTg0LTYuMzM2IDM0LjQ5Ni0yMy40ODggMzguNjg4LTE3LjE1MiA0LjA2NC0zNC40OTYtNi4zMDQtMzguNjg4LTIzLjQ4OC0yMC45OTItODYuMDQ4LTcxLjY4LTE2Mi43NTItMTQyLjc1Mi0yMTUuOTY4QzU3My43OTIgODAuOTYgMzMyLjYwOCAxMTUuNTUyIDIwNS42MzIgMjg1LjA1NmMtNjEuNDcyIDgyLjA4LTg3LjI5NiAxODMuMi03Mi43MDQgMjg0LjczNiAxNC41NiAxMDEuNTM2IDY3LjgwOCAxOTEuMjk2IDE0OS44ODggMjUyLjczNiAxNjkuNTM2IDEyNy4wNCA0MTAuNjg4IDkyLjM4NCA1MzcuNi03Ny4xMiAzMy4yMTYtNDQuMzg0IDU2LTk0LjExMiA2Ny42NDgtMTQ3Ljg0IDMuNzc2LTE3LjI4IDIwLjg5Ni0yOC4yNTYgMzguMDQ4LTI0LjUxMiAxNy4yOCAzLjc0NCAyOC4yNTYgMjAuOCAyNC41MTIgMzguMDQ4LTEzLjY2NCA2Mi43ODQtNDAuMjI0IDEyMC44MzItNzguOTc2IDE3Mi42NzItNzEuNzEyIDk1Ljc0NC0xNzYuNDggMTU3Ljg4OC0yOTQuOTc2IDE3NC44NDhDNTU1LjA3MiA5NjEuNzYgNTMzLjUwNCA5NjMuMjk2IDUxMi4wNjQgOTYzLjI5NnoiIHAtaWQ9IjM4MDg5IiBkYXRhLXNwbS1hbmNob3ItaWQ9ImEzMTN4Ljc3ODEwNjkuMC5pNyIgY2xhc3M9InNlbGVjdGVkIiBmaWxsPSIjZmZmZmZmIj48L3BhdGg+PC9zdmc+';
    return {
        msg: msg,
        type: "loading",
        duration: 0,
        fn: (res: any) => {
            if (fn) {
                fn(res);
            }
        },
        icon: Loading1,
        cover: true,
    };
};

/**
 * 弹出对话框  系统原生的  Modal
 * @param { String } title 标题
 * @param { String } content 内容
 * @param { Function } confirm 点确定的方法
 * @param { Function } cancel 点取消的方法，如果方法为空，不显示取消内容
 */
export const showModal = (title, content, confirm?: Function, cancel?: Function) => {
    console.log("showModal");

    Taro.showModal({
        title: title,
        content: content,
        showCancel: cancel && cancel.toString() !== (() => {}).toString(),
        success: function (res) {
            if (res.confirm) {
                confirm && confirm();
            } else if (res.cancel) {
                cancel && cancel();
            }
        },
    });
};

/**
 * 返回 Dialog 的  NutUI 的 Dialog
 * @param title
 * @param msg
 * @param okText
 * @param cancelText
 * @param ok
 * @param cancel
 * @returns
 */
export const getDialog = (title: string, msg: string, okText: string, cancelText: string, ok?: Function, cancel?: Function, textAlign?: string): API.pdDialog => {
    if (okText === "") {
        okText = "确认";
    }

    // if (cancelText === "") {
    //     cancelText = "取消";
    // }
    return {
        title: title,
        msg: msg,
        okText: okText,
        cancelText: cancelText,
        noCancelBtn: cancelText === "",
        textAlign: textAlign ? textAlign : "center",
        ok: (res) => {
            if (ok) {
                ok(res);
            }
        },
        cancel: (res) => {
            if (cancel) {
                cancel(res);
            }
        },
    };
};

/**
 * 没有 取消键 的提示框 NutUI 的 Dialog
 * @param title
 * @param msg
 * @param okText
 * @param ok
 * @returns
 */
export const getAlert = (title: string, msg: string, okText: string, ok?: Function, textAlign?: string): API.pdDialog => {
    if (okText === "") {
        okText = "确认";
    }

    return {
        title: title,
        msg: msg,
        okText: okText,
        cancelText: "",
        noCancelBtn: true,
        textAlign: textAlign ? textAlign : "center",
        ok: (res) => {
            if (ok) {
                ok(res);
            }
        },
        cancel: () => {},
    };
};

/**
 * 直接快速 返回 错误的提示 NutUI 的 Dialog
 * @param msg
 * @returns
 */
export const getMsg = (msg: string, title?: string, fn?: Function, textAlign?: string): API.pdDialog => {
    if (!title) {
        title = "错误";
    }
    return {
        title: title,
        msg: msg,
        okText: "确认",
        cancelText: "",
        noCancelBtn: true,
        textAlign: textAlign ? textAlign : "center",
        ok: (res) => {
            if (fn) {
                fn(res);
            }
        },
        cancel: () => {},
    };
};

/**
 * 验证身份证号码
 * @param {String} code 身份证号码
 * @returns
 */
export const identityIDCard = (code) => {
    // 身份证号前两位代表区域
    const city = {
        11: "北京",
        12: "天津",
        13: "河北",
        14: "山西",
        15: "内蒙古",
        21: "辽宁",
        22: "吉林",
        23: "黑龙江",
        31: "上海",
        32: "江苏",
        33: "浙江",
        34: "安徽",
        35: "福建",
        36: "江西",
        37: "山东",
        41: "河南",
        42: "湖北 ",
        43: "湖南",
        44: "广东",
        45: "广西",
        46: "海南",
        50: "重庆",
        51: "四川",
        52: "贵州",
        53: "云南",
        54: "西藏 ",
        61: "陕西",
        62: "甘肃",
        63: "青海",
        64: "宁夏",
        65: "新疆",
        71: "台湾",
        81: "香港",
        82: "澳门",
        83: "台湾",
        91: "国外",
    };
    const idCardReg = /^[1-9]\d{5}(19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i; // 身份证格式正则表达式
    let errorMessage = ""; // 错误提示信息
    let isPass = true; // 身份证验证是否通过（true通过、false未通过）

    // 如果身份证不满足格式正则表达式
    if (!code) {
        errorMessage = "请输入身份证号码";
        isPass = false;
    } else if (!code.match(idCardReg)) {
        errorMessage = "请输入正确的身份证号码";
        isPass = false;
    } else if (!city[code.substr(0, 2)]) {
        // 区域数组中不包含需验证的身份证前两位
        errorMessage = "请输入正确的身份证号码";
        isPass = false;
    } else if (code.length === 18) {
        // 18位身份证需要验证最后一位校验位
        code = code.split("");
        // ∑(ai×Wi)(mod 11)
        // 加权因子
        const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        // 校验位
        const parity = [1, 0, "X", 9, 8, 7, 6, 5, 4, 3, 2];
        let sum = 0;
        let ai = 0;
        let wi = 0;
        for (let i = 0; i < 17; i++) {
            ai = parseInt(code[i]);
            wi = factor[i];
            sum += ai * wi; // 开始计算并相加
        }
        const last = parity[sum % 11]; // 求余
        if (last.toString() !== code[17]) {
            errorMessage = "请输入正确的身份证号码";
            isPass = false;
        }
    }
    return {
        errorMessage,
        isPass,
    };
};

/**
 * 验证手机号码
 * @param {String} phone 传入手机号码
 * @returns
 */
export const isPoneAvailable = (phone) => {
    var myreg = /^[1][3,4,5,7,8][0-9]{9}$/;
    if (!myreg.test(phone)) {
        return false;
    } else {
        return true;
    }
};
// Base64 to Guid function
export const base64ToGuid = (base64String) => {
    if (!base64String || base64String == undefined) {
        return undefined;
    }
    base64String = decodeURIComponent(base64String);
    // Decode base64 string to ArrayBuffer
    const arrayBuffer = Taro.base64ToArrayBuffer(base64String);

    // Convert ArrayBuffer to Uint8Array
    const byteArray = new Uint8Array(arrayBuffer);

    // Helper function to convert byte array to hex string
    function byteToHex(byte) {
        return byte.toString(16).padStart(2, "0");
    }
    const part1 = Array.from(byteArray.slice(0, 4)).reverse().map(byteToHex).join("");
    const part2 = Array.from(byteArray.slice(4, 6)).reverse().map(byteToHex).join("");
    const part3 = Array.from(byteArray.slice(6, 8)).reverse().map(byteToHex).join("");
    const part4 = Array.from(byteArray.slice(8, 10)).map(byteToHex).join("");
    const part5 = Array.from(byteArray.slice(10, 16)).map(byteToHex).join("");
    // 4. Combine parts into GUID string
    const guid = `${part1}-${part2}-${part3}-${part4}-${part5}`;
    return guid;
};

/**
 * 检查用户是否已关注公众号
 * 通过调用后端API检查用户的公众号关注状态
 * 如果用户未关注公众号，则重定向到关注页面
 * @returns {Promise<void>}
 */
export const checkOfficialAccountSubscription = async () => {
    try {
        // 调用后端API检查公众号关注状态
        const response = await request.post<any>("/Wx/Subscribe/checkSubscribe", {
            MpAppId: "wxca4614f69b2279a4", // 公众号的AppID
        });

        console.log("检查公众号关注状态:", response);

        // 如果用户未关注公众号，重定向到关注页面
        if (!(response.success && response.data && response.data.isSubscribed)) {
            return false;
        } else {
            return true;
        }
    } catch (error) {
        console.error("检查公众号关注状态失败:", error);
        return false;
    } finally {
        return false;
    }
};

/**
 * 重定向到公众号关注页面
 * 保存当前页面路径和参数，以便用户关注后可以返回
 * 使用Taro.navigateTo进行页面跳转
 */
export const redirectToFollowPage = () => {
    const appId = "wxca4614f69b2279a4"; // 公众号的AppID

    // 获取当前页面栈信息
    const pages = Taro.getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentPath = currentPage?.route || "";
    const currentParams = currentPage?.options || {};

    // 构建完整的当前页面路径（包含查询参数）
    let fullPath = currentPath;
    if (Object.keys(currentParams).length > 0) {
        const queryParams = Object.keys(currentParams)
            .map((key) => `${key}=${currentParams[key]}`)
            .join("&");
        if (queryParams) {
            fullPath = `${currentPath}?${queryParams}`;
        }
    }

    // 跳转到关注页面，并传递当前页面路径和AppID作为参数
    Taro.navigateTo({
        url: `/pages/follow-official-account/index?redirectUrl=${encodeURIComponent(fullPath)}&appId=${appId}`,
    });
};

export default util;
