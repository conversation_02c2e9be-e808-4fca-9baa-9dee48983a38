import Taro from "@tarojs/taro";

// Canvas ID for printing
const PRINT_CANVAS_ID = "printCanvas";

/**
 * Canvas打印工具类
 */
export default class CanvasPrinter {
    /**
     * 将文本绘制到Canvas上并转换为图片
     * @param text 要绘制的文本
     * @param options 选项（宽度、字体大小、颜色等）
     * @returns 返回图片路径的Promise
     */
    static generateTextImage(text: string, options: any = {}): Promise<string> {
        return new Promise((resolve, reject) => {
            try {
                // 默认值调整，增大字体大小和行高
                const { fontSize = 28, color = "#000", fontFamily = "Microsoft YaHei", width = 384, height = 0, lineHeight = 40, padding = 20 } = options;

                // 计算文本行数
                const lines = text.split("\n");
                // 注意：这里的高度单位是像素，不是rpx
                const calculatedHeight = height || lines.length * lineHeight + padding * 2;

                // 创建Canvas上下文
                const ctx = Taro.createCanvasContext(PRINT_CANVAS_ID);

                // 清空画布
                ctx.clearRect(0, 0, width, calculatedHeight);
                ctx.fillStyle = "#ffffff";
                ctx.fillRect(0, 0, width, calculatedHeight);

                // 设置样式
                ctx.fillStyle = color;
                // 尝试设置字体粗细
                ctx.font = `bold ${fontSize}px ${fontFamily}`;
                ctx.textBaseline = "top";

                // 尝试设置抗锯齿
                if (typeof ctx.setTextRenderingHint === "function") {
                    ctx.setTextRenderingHint("optimizeLegibility");
                }

                // 绘制文本（逐行绘制）
                let y = padding;
                for (const line of lines) {
                    if (line.trim()) {
                        // 只对非空行处理
                        // 先设置字体，再绘制文本
                        // 如果是数字或字母，尝试使用粗体
                        if (/^[0-9A-Za-z]+$/.test(line)) {
                            ctx.font = `bold ${fontSize}px ${fontFamily}`;
                        } else {
                            ctx.font = `${fontSize}px ${fontFamily}`;
                        }

                        // 绘制文本主体
                        ctx.fillText(line, padding, y);
                    }
                    y += lineHeight;
                }

                // 执行绘制并转换为图片
                ctx.draw(false, () => {
                    // 等待绘制完成
                    setTimeout(() => {
                        Taro.canvasToTempFilePath({
                            canvasId: PRINT_CANVAS_ID,
                            x: 0,
                            y: 0,
                            width: width,
                            height: calculatedHeight,
                            // 增大目标图片尺寸以提高清晰度
                            destWidth: width * 2,
                            destHeight: calculatedHeight * 2,
                            fileType: "png", // 使用PNG格式保持透明度
                            quality: 1, // 最高质量
                            success(res) {
                                console.log("Canvas转图片成功:", res.tempFilePath);
                                resolve(res.tempFilePath);
                            },
                            fail(err) {
                                console.error("Canvas转图片失败:", err);
                                reject(err);
                            },
                        });
                    }, 300); // 给Canvas足够的时间完成绘制
                });
            } catch (error) {
                console.error("生成文本图片失败:", error);
                reject(error);
            }
        });
    }

    /**
     * 将图片转换为打印机可识别的点阵数据
     * @param imagePath 图片路径
     * @returns 返回打印命令数组
     */
    static async convertImageToPrintCommands(imagePath: string): Promise<number[]> {
        try {
            // 获取图片信息
            const imageInfo = await Taro.getImageInfo({ src: imagePath });
            console.log("图片信息:", imageInfo);

            // 获取图片数据
            const fileData = await new Promise<ArrayBuffer>((resolve, reject) => {
                Taro.getFileSystemManager().readFile({
                    filePath: imagePath,
                    success: (res) => {
                        resolve(res.data as ArrayBuffer);
                    },
                    fail: (err) => {
                        console.error("读取文件失败:", err);
                        reject(err);
                    },
                });
            });

            // 构建打印命令
            const commands: number[] = [];

            // 初始化打印机
            commands.push(0x1b); // ESC
            commands.push(0x40); // @ 初始化命令

            // 设置行间距
            commands.push(0x1b); // ESC
            commands.push(0x33); // 3
            commands.push(0x00); // 行间距为0

            // 设置打印模式为位图模式
            commands.push(0x1d); // GS
            commands.push(0x76); // v
            commands.push(0x30); // 0 普通打印模式

            // 设置位图宽度（以点为单位）
            const width = imageInfo.width;
            const widthBytes = Math.ceil(width / 8); // 每8个点为1个字节

            commands.push(widthBytes & 0xff); // 宽度低字节
            commands.push((widthBytes >> 8) & 0xff); // 宽度高字节

            // 设置位图高度（以点为单位）
            const height = imageInfo.height;
            commands.push(height & 0xff); // 高度低字节
            commands.push((height >> 8) & 0xff); // 高度高字节

            // 将图片数据转换为打印机可识别的点阵数据
            // 简化处理，直接发送原始数据
            const dataView = new Uint8Array(fileData);
            for (let i = 0; i < dataView.length; i++) {
                commands.push(dataView[i]);
            }

            // 打印并切纸
            commands.push(0x1d); // GS
            commands.push(0x56); // V
            commands.push(0x00); // 0 全切纸

            return commands;
        } catch (error) {
            console.error("转换图片数据失败:", error);
            throw error;
        }
    }

    /**
     * 打印文本（通过Canvas绘制）
     * @param text 要打印的文本
     * @param options 选项（字体大小、颜色等）
     * @returns 返回打印命令数组的Promise
     */
    static async printText(text: string, options: any = {}): Promise<number[]> {
        try {
            // 生成文本图片
            const imagePath = await this.generateTextImage(text, options);

            // 转换为打印命令
            return await this.convertImageToPrintCommands(imagePath);
        } catch (error) {
            console.error("打印文本失败:", error);
            throw error;
        }
    }
}
