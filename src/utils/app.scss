@use "@nutui/nutui-react-taro/dist/styles/variables.scss" as *;

.nut-picker__control {
    height: 20px;
    padding: 10px 15px 15px 15px;
}
.nut-picker__control .nut-picker__cancel-btn {
    color: #969799;
}
.nut-button .text {
    margin-left: 0;
}

.input-cell .nut-cell__title {
    width: 80px;
    text-align: left;
    flex: none;
}

.nut-cell-group__title {
    margin-top: 10px;
}

.nut-picker__control {
    .nut-picker__title {
        font-size: 16px;
    }

    .nut-picker__confirm-btn {
        font-size: 14px;
        color: #576b95;
    }

    .nut-picker__cancel-btn {
        color: #969799;
        font-size: 14px;
    }
}

.nut-cell-group__title {
    margin-top: 10px;
}
