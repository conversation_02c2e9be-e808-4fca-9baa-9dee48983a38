.tui-container {
    width: 100%;
    overflow: hidden;
    padding: 0rpx 50rpx 120rpx;
    box-sizing: border-box;
    background: #fff;
    position: relative;
    z-index: 2;
}

.nut-cell-group__title {
    margin-top: 20px;
}

.tui-form {
    width: 100%;
    padding-top: 168rpx;
    padding-bottom: 20rpx;
}

.bg-img image {
    position: absolute;
    width: 100%;
    height: 440rpx;
    z-index: -1;
    background-image: url("https://cdn.51panda.com/WxAppImage/mine_bg_3x.png");
}

.nut-input {
    border-radius: 6px;
    border-bottom: 0px solid #eaf0fb;
}

page {
    background: #fff;
    height: 100%;
}

.tui-container {
    height: 100vh;
}

.tui-third__box {
    width: 100%;
    padding: 0 75rpx;
    box-sizing: border-box;
    position: fixed;
    left: 0;
    bottom: 80rpx;
    z-index: 10;
}

.tui-auth__login {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 80rpx;
    box-sizing: border-box;
}

.tui-icon__platform {
    width: 88rpx;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border-radius: 50%;
    position: relative;
}

.tui-icon__platform::after {
    content: " ";
    position: absolute;
    width: 200%;
    height: 200%;
    transform-origin: 0 0;
    transform: scale(0.5, 0.5) translateZ(0);
    box-sizing: border-box;
    left: 0;
    top: 0;
    border-radius: 50%;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.tui-auth__logo {
    width: 60rpx;
    height: 60rpx;
}

.tui-input__wrap {
    width: 100%;
    display: flex;
    align-items: center;
    position: relative;
    box-sizing: border-box;
}

.tui-input__wrap::before {
    content: " ";
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    border-top: 1px solid var(--thorui-line-color, rgba(0, 0, 0, 0.1));
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    transform-origin: 0 0;
    z-index: 2;
    pointer-events: none;
}

.tui-input__wrap::after {
    content: " ";
    position: absolute;
    border-bottom: 1px solid var(--thorui-line-color, rgba(0, 0, 0, 0.1));
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    transform-origin: 0 100%;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 2;
    pointer-events: none;
}

.tui-line__left::after {
    left: 30rpx !important;
}

.tui-border__top::before {
    border-top: 0;
}

.tui-border__bottom::after {
    border-bottom: 0;
}

.tui-input__required {
    height: 30rpx;
    position: absolute;
    left: 0;
    top: 50%;
    left: 12rpx;
    transform: translateY(-50%);
}

.tui-input__label {
    padding-right: 12rpx;
    flex-shrink: 0;
}

.tui-input__self {
    flex: 1;
    padding-right: 12rpx;
    box-sizing: border-box;
    overflow: visible;
}

.tui-input__placeholder {
    color: var(--thorui-text-color-placeholder, #ccc);
    overflow: visible;
}

.tui-input__border {
    border-radius: 4rpx;
    position: relative;
}

.tui-input__border::after {
    content: " ";
    position: absolute;
    height: 200%;
    width: 200%;
    border: 1px solid var(--thorui-border-color, #d1d1d1);
    transform-origin: 0 0;
    transform: scale(0.5);
    left: 0;
    top: 0;
    border-radius: 8rpx;
    pointer-events: none;
}

.tui-radius__fillet {
    border-radius: 100px !important;
}

.tui-radius__fillet::after {
    border-radius: 100px !important;
}

.tui-text__right {
    text-align: right;
}

.tui-countdown__verify {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    box-sizing: border-box;
}

.tui-verify__opacity {
    opacity: 0.5;
}

.tui-verify__line {
    position: absolute;
    width: 200%;
    height: 200%;
    transform-origin: 0 0;
    transform: scale(0.5, 0.5) translateZ(0);
    box-sizing: border-box;
    border-style: solid;
    left: 0;
    top: 0;
    pointer-events: none;
}

.tui-icon__pr {
    padding-right: 20rpx;
}

.tui-divider {
    width: 100%;
    position: relative;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
    box-sizing: border-box;
    overflow: hidden;
}

.tui-divider-line {
    position: absolute;
    height: 1rpx;
    top: 50%;
    left: 50%;
    -webkit-transform: scaleY(0.5) translateX(-50%) translateZ(0);
    transform: scaleY(0.5) translateX(-50%) translateZ(0);
    z-index: 1;
}

.tui-divider-text {
    position: relative;
    text-align: center;
    padding: 0 18rpx;
    z-index: 2;
}

.max-width-dialog .nut-dialog {
    width: 90vw;
}
