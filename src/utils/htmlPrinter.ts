import Taro from "@tarojs/taro";
import CanvasPrinter from "./canvasPrinter";

/**
 * HTML打印工具类
 */
export default class HtmlPrinter {
    /**
     * 将HTML内容转换为纯文本
     * @param htmlContent HTML内容
     * @returns 纯文本内容
     */
    static htmlToText(htmlContent: string): string {
        // 在小程序环境中，我们没有DOM，所以需要手动解析HTML
        // 这是一个简化版的实现，只处理基本的HTML标签
        let text = htmlContent;
        
        // 移除HTML标签
        text = text.replace(/<[^>]*>/g, '');
        
        // 解码HTML实体
        text = text.replace(/&nbsp;/g, ' ')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&amp;/g, '&')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'");
        
        // 处理换行
        text = text.replace(/<br\s*\/?>/gi, '\n')
            .replace(/<\/p>/gi, '\n')
            .replace(/<\/div>/gi, '\n')
            .replace(/<\/h[1-6]>/gi, '\n');
        
        // 移除多余的空白
        text = text.replace(/\s+/g, ' ').trim();
        
        // 处理列表项
        text = text.replace(/<li>/gi, '• ');
        
        return text;
    }
    
    /**
     * 打印HTML内容
     * @param htmlContent HTML内容
     * @param options 打印选项
     * @returns Promise对象
     */
    static async printHtml(htmlContent: string, options: any = {}): Promise<number[]> {
        try {
            // 将HTML转换为纯文本
            const textContent = this.htmlToText(htmlContent);
            console.log('转换后的纯文本:', textContent);
            
            // 使用Canvas打印纯文本
            return await CanvasPrinter.printText(textContent, {
                fontSize: 28,
                fontFamily: "Microsoft YaHei",
                width: 384,
                lineHeight: 40,
                padding: 20,
                color: "#000000",
                ...options
            });
        } catch (error) {
            console.error('打印HTML内容失败:', error);
            throw error;
        }
    }
}
