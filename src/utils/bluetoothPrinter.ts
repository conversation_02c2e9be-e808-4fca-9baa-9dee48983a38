import Taro from "@tarojs/taro";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import { convertToPrinterBytes } from "./chineseCharMapping";
import CanvasPrinter from "./canvasPrinter";
import HtmlPrinter from "./htmlPrinter";

// 定义Canvas ID，用于文本转图片
const PRINT_CANVAS_ID = "printCanvas";

// 蓝牙状态锁，避免重复初始化
let isBluetoothOpen = false; // 记录蓝牙是否已经初始化

/**
 * 蓝牙打印工具类
 */
export default class BluetoothPrinter {
    // 允许的打印机设备名称数组
    static readonly PRINTER_DEVICE_NAMES = ["Printer001", "MPT-II", "MPT-III", "Bluetooth Printer", "TSPL"];

    /**
     * 打印二维码
     * @param qrCodeData 二维码数据
     * @param onSuccess 成功回调
     * @param onFail 失败回调
     */
    static printQRCode(qrCodeData: any, onSuccess: () => void = () => {}, onFail: (error: any) => void = () => {}) {
        // 显示加载提示
        message.openLoading("准备打印...");

        try {
            // 直接处理蓝牙状态并初始化
            this.handleBluetoothState(qrCodeData, onSuccess, onFail);
        } catch (error) {
            message.closeLoading();
            message.error("蓝牙初始化异常");
            console.error("蓝牙初始化异常:", error);
            onFail(error);
        }
    }

    /**
     * 简单打印二维码（用于测试）
     * @param qrCodeUrl 二维码URL
     * @param onSuccess 成功回调
     * @param onFail 失败回调
     */
    static printSimpleQRCode(qrCodeUrl: string, onSuccess: () => void = () => {}, onFail: (error: any) => void = () => {}) {
        // 显示加载提示
        message.openLoading("准备打印简单二维码...");

        try {
            // 准备简单的打印数据 - 只包含二维码URL
            const printData = {
                qrCodeUrl: qrCodeUrl,
                // 不需要其他信息，只打印二维码
            };

            // 处理蓝牙状态并初始化
            this.handleBluetoothState(printData, onSuccess, onFail);
        } catch (error) {
            message.closeLoading();
            message.error("蓝牙初始化异常");
            console.error("蓝牙初始化异常:", error);
            onFail(error);
        }
    }

    /**
     * 简单打印纯文本（用于测试）
     * @param text 要打印的纯文本
     * @param onSuccess 成功回调
     * @param onFail 失败回调
     */
    static printPlainText(text: string, onSuccess: () => void = () => {}, onFail: (error: any) => void = () => {}) {
        // 显示加载提示
        message.openLoading("准备打印纯文本...");

        try {
            // 准备简单的打印数据 - 只包含纯文本
            const printData = {
                plainText: text,
                isPlainText: true, // 标记为纯文本模式
            };

            // 处理蓝牙状态并初始化
            this.handleBluetoothState(printData, onSuccess, onFail);
        } catch (error) {
            message.closeLoading();
            message.error("蓝牙初始化异常");
            console.error("蓝牙初始化异常:", error);
            onFail(error);
        }
    }

    /**
     * 使用Canvas打印雅黑字体文本
     * @param text 要打印的文本
     * @param options 选项（字体大小、颜色等）
     * @param onSuccess 成功回调
     * @param onFail 失败回调
     */
    static printCanvasText(text: string, options: any = {}, onSuccess: () => void = () => {}, onFail: (error: any) => void = () => {}) {
        // 显示加载提示
        message.openLoading("准备打印雅黑字体...");

        // 设置默认选项
        const printOptions = {
            fontFamily: "Microsoft YaHei",
            fontSize: 24,
            width: 384, // 打印纸宽度，通常为58mm热敏打印机的点数
            ...options,
        };

        // 使用CanvasPrinter生成打印命令
        CanvasPrinter.printText(text, printOptions)
            .then((commands) => {
                // 生成打印命令成功
                message.openLoading("正在连接打印机...");

                // 创建一个特殊的打印数据对象
                const printData = {
                    isCanvasData: true,
                    canvasCommands: commands,
                };

                // 处理蓝牙状态并初始化
                this.handleBluetoothState(printData, onSuccess, onFail);
            })
            .catch((error) => {
                message.closeLoading();
                message.error("生成打印命令失败");
                console.error("生成打印命令失败:", error);
                onFail(error);
            });
    }

    /**
     * 打印HTML内容
     * @param htmlContent HTML内容
     * @param options 选项（字体大小、颜色等）
     * @param onSuccess 成功回调
     * @param onFail 失败回调
     */
    static printHtmlContent(htmlContent: string, options: any = {}, onSuccess: () => void = () => {}, onFail: (error: any) => void = () => {}) {
        // 显示加载提示
        message.openLoading("准备打印HTML内容...");

        // 设置默认选项
        const printOptions = {
            fontFamily: "Microsoft YaHei",
            fontSize: 28,
            width: 384, // 打印纸宽度，通常为58mm热敏打印机的点数
            lineHeight: 40,
            padding: 20,
            ...options,
        };

        // 使用HtmlPrinter生成打印命令
        HtmlPrinter.printHtml(htmlContent, printOptions)
            .then((commands) => {
                // 生成打印命令成功
                message.openLoading("正在连接打印机...");

                // 创建一个特殊的打印数据对象
                const printData = {
                    isCanvasData: true,
                    canvasCommands: commands,
                };

                // 处理蓝牙状态并初始化
                this.handleBluetoothState(printData, onSuccess, onFail);
            })
            .catch((error) => {
                message.closeLoading();
                message.error("生成打印命令失败");
                console.error("生成打印命令失败:", error);
                onFail(error);
            });
    }

    /**
     * 处理蓝牙状态
     */
    private static handleBluetoothState(qrCodeData: any, onSuccess: () => void, onFail: (error: any) => void) {
        // 检查蓝牙是否已经初始化
        if (isBluetoothOpen) {
            console.log("蓝牙已经初始化，直接搜索设备");
            this.searchAndConnectPrinter(qrCodeData, onSuccess, onFail);
            return;
        }

        // 直接尝试初始化蓝牙适配器
        console.log("尝试初始化蓝牙适配器");

        // 先关闭现有蓝牙连接，避免冲突
        Taro.closeBluetoothAdapter({
            complete: () => {
                // 无论关闭成功与否，都尝试重新打开
                setTimeout(() => {
                    Taro.openBluetoothAdapter({
                        success: () => {
                            console.log("蓝牙适配器初始化成功");
                            isBluetoothOpen = true;
                            this.searchAndConnectPrinter(qrCodeData, onSuccess, onFail);
                        },
                        fail: (err) => {
                            console.error("蓝牙适配器初始化失败:", err);

                            // 如果错误信息包含"already opened"，说明蓝牙已经打开
                            if (err.errMsg && err.errMsg.includes("already opened")) {
                                console.log("蓝牙已经打开，直接搜索设备");
                                isBluetoothOpen = true;
                                this.searchAndConnectPrinter(qrCodeData, onSuccess, onFail);
                            } else {
                                // 其他错误，可能是用户未开启蓝牙或权限问题
                                message.closeLoading();
                                message.error("请确保蓝牙已开启并授予应用蓝牙权限");
                                console.error("蓝牙初始化失败，请检查设备蓝牙是否开启:", err);
                                onFail(err);
                            }
                        },
                    });
                }, 300); // 等待一小段时间再打开，避免可能的冲突
            },
        });
    }

    /**
     * 搜索并连接打印机
     */
    private static searchAndConnectPrinter(qrCodeData: any, onSuccess: () => void, onFail: (error: any) => void) {
        // 开始搜索蓝牙设备
        Taro.startBluetoothDevicesDiscovery({
            success: () => {
                console.log("开始搜索蓝牙设备");

                // 搜索一段时间后获取设备列表
                setTimeout(() => {
                    Taro.getBluetoothDevices({
                        success: (res) => {
                            // 停止搜索
                            Taro.stopBluetoothDevicesDiscovery();

                            const devices = res.devices || [];
                            console.log("找到的蓝牙设备:", devices);

                            // 过滤出允许的打印机设备，根据设备名称匹配
                            const allowedPrinters = devices.filter((device) => {
                                // 获取设备名称，优先使用name，如果没有则使用localName
                                const deviceName = (device.name || device.localName || "").toLowerCase();
                                // 检查设备名称是否包含允许的打印机名称中的任何一个
                                return deviceName && this.PRINTER_DEVICE_NAMES.some((name) => deviceName.includes(name.toLowerCase()));
                            });

                            console.log("允许的打印机设备:", allowedPrinters);

                            if (allowedPrinters.length > 0) {
                                // 按信号强度排序，选择信号最强的打印机
                                allowedPrinters.sort((a, b) => {
                                    const signalA = a.RSSI || -100;
                                    const signalB = b.RSSI || -100;
                                    return signalB - signalA; // 信号强度从大到小排序
                                });

                                const bestPrinter = allowedPrinters[0];
                                console.log("选择信号最强的打印机:", bestPrinter);

                                // 连接并打印
                                this.connectAndPrint(
                                    bestPrinter,
                                    qrCodeData,
                                    () => {
                                        message.closeLoading();
                                        message.success("打印成功");
                                        onSuccess();
                                    },
                                    (error) => {
                                        message.closeLoading();
                                        message.error("打印失败，请确认打印机已连接");
                                        console.error("打印失败:", error);
                                        onFail(error);
                                    }
                                );
                            } else {
                                message.closeLoading();
                                message.error("未找到指定的打印机，请确认打印机已开启");
                                console.error("未找到指定的打印机，支持的打印机名称:", this.PRINTER_DEVICE_NAMES);
                                onFail(new Error("未找到指定的打印机"));
                            }
                        },
                        fail: (err) => {
                            Taro.stopBluetoothDevicesDiscovery();
                            message.closeLoading();
                            message.error("获取蓝牙设备失败");
                            console.error("获取蓝牙设备失败:", err);
                            onFail(err);
                        },
                    });
                }, 2000); // 搜索2秒钟
            },
            fail: (err) => {
                message.closeLoading();
                message.error("搜索蓝牙设备失败");
                console.error("搜索蓝牙设备失败:", err);
                onFail(err);
            },
        });
    }

    /**
     * 连接到打印机并发送数据
     * @param device 蓝牙设备
     * @param printData 要打印的数据
     * @param onSuccess 成功回调
     * @param onFail 失败回调
     */
    private static connectAndPrint(device: any, printData: any, onSuccess: () => void, onFail: (error: any) => void) {
        if (!device || !device.deviceId) {
            message.error("设备信息不完整");
            onFail(new Error("设备信息不完整"));
            return;
        }

        message.openLoading("连接打印机...");

        // 检查设备名称是否匹配允许的打印机名称
        const deviceName = (device.name || device.localName || "").toLowerCase();
        const isPrinter = deviceName && this.PRINTER_DEVICE_NAMES.some((name) => deviceName.includes(name.toLowerCase()));

        if (!isPrinter) {
            message.closeLoading();
            message.error("非指定的打印机设备，无法连接");
            console.error("非指定的打印机设备:", device.name || device.localName || device.deviceId);
            onFail(new Error("非指定的打印机设备"));
            return;
        }

        // 连接设备
        Taro.createBLEConnection({
            deviceId: device.deviceId,
            success: () => {
                console.log("连接设备成功:", device.name || device.deviceId);

                // 获取设备的服务
                Taro.getBLEDeviceServices({
                    deviceId: device.deviceId,
                    success: (res) => {
                        console.log("设备服务列表:", res.services);

                        // 模拟打印过程
                        message.closeLoading();
                        message.openLoading("正在打印...");

                        // 实际的打印逻辑，根据设备服务和特性发送打印数据
                        console.log("准备打印数据:", printData);

                        // 寻找打印服务
                        const printService = this.findPrintService(res.services);

                        if (!printService) {
                            message.closeLoading();
                            message.error("未找到打印服务，请确认打印机型号");
                            this.disconnectDevice(device.deviceId);
                            onFail(new Error("未找到打印服务"));
                            return;
                        }

                        // 获取服务的特性
                        Taro.getBLEDeviceCharacteristics({
                            deviceId: device.deviceId,
                            serviceId: printService.uuid,
                            success: (charRes) => {
                                console.log("服务特性列表:", charRes.characteristics);

                                // 寻找可写的特性
                                const writableChar = this.findWritableCharacteristic(charRes.characteristics);

                                if (!writableChar) {
                                    message.closeLoading();
                                    message.error("未找到可写的特性，无法打印");
                                    this.disconnectDevice(device.deviceId);
                                    onFail(new Error("未找到可写的特性"));
                                    return;
                                }

                                // 准备打印数据
                                const printBuffer = this.preparePrintData(printData);

                                // 分包发送数据，因为蓝牙传输有大小限制
                                this.sendPrintDataInChunks(
                                    device.deviceId,
                                    printService.uuid,
                                    writableChar.uuid,
                                    printBuffer,
                                    () => {
                                        message.closeLoading();
                                        message.success("打印成功");

                                        // 断开连接
                                        this.disconnectDevice(device.deviceId, () => {
                                            onSuccess();
                                        });
                                    },
                                    (error) => {
                                        message.closeLoading();
                                        message.error("打印失败: " + error.message);
                                        this.disconnectDevice(device.deviceId);
                                        onFail(error);
                                    }
                                );
                            },
                            fail: (err) => {
                                console.error("获取服务特性失败:", err);
                                message.closeLoading();
                                message.error("获取服务特性失败");
                                this.disconnectDevice(device.deviceId);
                                onFail(err);
                            },
                        });
                    },
                    fail: (err) => {
                        console.error("获取设备服务失败:", err);
                        message.closeLoading();
                        message.error("获取设备服务失败");

                        // 断开连接
                        this.disconnectDevice(device.deviceId);
                        onFail(err);
                    },
                });
            },
            fail: (err) => {
                console.error("连接设备失败:", err);
                message.closeLoading();
                message.error("连接打印机失败，请确认打印机已开启");
                onFail(err);
            },
        });
    }

    /**
     * 断开设备连接
     * @param deviceId 设备ID
     * @param callback 回调函数
     */
    private static disconnectDevice(deviceId: string, callback?: () => void) {
        Taro.closeBLEConnection({
            deviceId: deviceId,
            success: () => {
                console.log("断开设备连接成功");
                // 关闭蓝牙适配器，完全重置状态
                this.resetBluetoothState();
                if (callback) callback();
            },
            fail: (err) => {
                console.error("断开设备连接失败:", err);
                // 即使断开连接失败，仍然尝试重置蓝牙状态
                this.resetBluetoothState();
                if (callback) callback(); // 即使断开连接失败，仍然执行回调
            },
        });
    }

    /**
     * 重置蓝牙状态
     */
    private static resetBluetoothState() {
        // 关闭蓝牙适配器，重置状态
        Taro.closeBluetoothAdapter({
            success: () => {
                console.log("蓝牙适配器关闭成功");
                isBluetoothOpen = false;
            },
            fail: (err) => {
                console.error("蓝牙适配器关闭失败:", err);
                // 即使关闭失败，仍然重置状态标志
                isBluetoothOpen = false;
            },
        });
    }

    /**
     * 寻找打印服务
     * @param services 服务列表
     * @returns 打印服务对象或空
     */
    private static findPrintService(services: any[]): any {
        // 常见的打印机服务UUID
        const PRINT_SERVICE_UUIDS = [
            "49535343-FE7D-4AE5-8FA9-9FAFD205E455", // 常见的打印机服务UUID
            "E7810A71-73AE-499D-8C15-FAA9AEF0C3F2", // 另一种常见的打印机服务
            "000018F0-0000-1000-8000-00805F9B34FB", // 通用打印服务
            "1812", // 人机界面设备服务
            "1801", // 通用属性服务
            "1800", // 通用访问服务
            "FFF0", // 常见的自定义服务
            "FF00", // 常见的自定义服务
        ];

        // 先尝试匹配已知的打印机服务UUID
        for (const service of services) {
            const uuid = service.uuid.toUpperCase();
            if (PRINT_SERVICE_UUIDS.some((id) => uuid.includes(id))) {
                return service;
            }
        }

        // 如果没有匹配到已知的打印机服务，则返回第一个服务（如果有的话）
        return services.length > 0 ? services[0] : null;
    }

    /**
     * 寻找可写的特性
     * @param characteristics 特性列表
     * @returns 可写的特性对象或空
     */
    private static findWritableCharacteristic(characteristics: any[]): any {
        // 常见的打印机可写特性UUID
        const WRITE_CHARACTERISTIC_UUIDS = [
            "*************-43F4-A8D4-ECBE34729BB3", // 常见的打印机可写特性
            "49535343-1E4D-4BD9-BA61-23C647249616", // 另一种常见的打印机可写特性
            "00002AF1-0000-1000-8000-00805F9B34FB", // 通用打印特性
            "FFF2", // 常见的自定义特性
            "FF02", // 常见的自定义特性
        ];

        // 先尝试匹配已知的可写特性UUID
        for (const characteristic of characteristics) {
            const uuid = characteristic.uuid.toUpperCase();
            // 检查是否可写并且匹配已知的UUID
            if (characteristic.properties.write && WRITE_CHARACTERISTIC_UUIDS.some((id) => uuid.includes(id))) {
                return characteristic;
            }
        }

        // 如果没有匹配到已知的可写特性，则返回第一个可写的特性（如果有的话）
        return characteristics.find((c) => c.properties.write) || null;
    }

    /**
     * 准备打印数据
     * @param printData 打印数据对象
     * @returns 处理后的打印数据缓冲区
     */
    private static preparePrintData(printData: any): ArrayBuffer {
        // 使用ESC/POS命令打印
        return this.prepareESCPOSPrintData(printData);
    }

    /**
     * 使用ESC/POS命令准备打印数据
     * @param printData 打印数据对象
     * @returns 处理后的打印数据缓冲区
     */
    private static prepareESCPOSPrintData(printData: any): ArrayBuffer {
        console.log("使用ESC/POS命令准备打印数据");

        // 检查是否是图片数据
        if (printData.isImageData && printData.imageBuffer) {
            console.log("检测到图片数据，直接使用预处理的图片数据");
            return printData.imageBuffer;
        }

        // 检查是否是Canvas生成的数据
        if (printData.isCanvasData && printData.canvasCommands) {
            console.log("检测到Canvas生成的数据，直接使用预处理的命令");
            return new Uint8Array(printData.canvasCommands).buffer;
        }

        // 生成打印命令
        const commands = this.generatePrintCommands(printData);

        // 将命令数组转换为字符串，然后转换为ArrayBuffer
        // 这里我们使用两种方式来演示
        // 方式1：直接使用Uint8Array
        const buffer1 = new Uint8Array(commands).buffer;

        // 方式2：先转换为字符串，然后使用stringToArrayBuffer
        // 这里只是演示使用stringToArrayBuffer方法，实际上方式1更高效
        let commandStr = "";
        for (const cmd of commands) {
            commandStr += String.fromCharCode(cmd);
        }

        // 使用stringToArrayBuffer方法转换
        const buffer2 = this.stringToArrayBuffer(commandStr);

        // 打印两种方式的结果大小以便比较
        console.log(`方式1 buffer大小: ${buffer1.byteLength}`);
        console.log(`方式2 buffer大小: ${buffer2.byteLength}`);

        // 返回第一种方式的结果，因为它更高效
        return buffer1;
    }

    /**
     * 生成打印命令
     * @param printData 打印数据
     * @returns 打印命令数组
     */
    private static generatePrintCommands(printData: any): number[] {
        // 初始化打印机
        let commands: number[] = [];
        commands.push(0x1b); // ESC
        commands.push(0x40); // @ 初始化命令

        // 检查是否为纯文本模式
        if (printData.isPlainText) {
            // 纯文本模式，只设置基本的代码页和字体
            commands.push(0x1b); // ESC
            commands.push(0x74); // t 选择字符集
            commands.push(0x0f); // 15 表示简体中文

            // 打印纯文本
            if (printData.plainText) {
                commands = commands.concat(this.stringToBytes(printData.plainText));
                commands = commands.concat(this.stringToBytes("\n\n"));
            }

            // 打印换行和切纸
            commands = commands.concat(this.stringToBytes("\n\n\n"));
            commands.push(0x1d); // GS
            commands.push(0x56); // V
            commands.push(0x00); // 0 全切纸

            return commands;
        }

        // 正常模式，设置完整的代码页和字体
        // 设置代码页为简体中文
        // 方式1：使用 ESC t 命令
        commands.push(0x1b); // ESC
        commands.push(0x74); // t 选择字符集
        commands.push(0x0f); // 15 表示简体中文

        // 方式2：使用 FS & 命令选择汉字模式
        commands.push(0x1c); // FS
        commands.push(0x26); // & 选择汉字模式

        // 方式3：使用 ESC R 命令选择国际字符集
        commands.push(0x1b); // ESC
        commands.push(0x52); // R 选择国际字符集
        commands.push(0x0f); // 15 表示简体中文

        // 设置雅黑字体（需要打印机支持）
        commands.push(0x1b); // ESC
        commands.push(0x4d); // M 选择字体
        commands.push(0x01); // 1 表示中文雅黑字体

        // 检查是否只有二维码数据（简单模式）
        const isSimpleMode = !printData.itemName && !printData.sysId && !printData.orderNum;

        if (!isSimpleMode) {
            // 完整模式 - 打印标题和详细信息
            // 打印标题 - 居中、加粗、大号字体
            commands.push(0x1b); // ESC
            commands.push(0x61); // a 对齐方式
            commands.push(0x01); // 1 居中

            commands.push(0x1b); // ESC
            commands.push(0x21); // ! 设置字体大小和加粗
            commands.push(0x30); // 0x30 表示大号加粗字体

            // 打印标题
            commands = commands.concat(this.stringToBytes("电子券\n"));

            // 恢复默认字体
            commands.push(0x1b);
            commands.push(0x21);
            commands.push(0x00);

            // 左对齐
            commands.push(0x1b);
            commands.push(0x61);
            commands.push(0x00);

            // 打印信息内容
            if (printData.itemName) {
                commands = commands.concat(this.stringToBytes(`项目名称: ${printData.itemName}\n`));
            }

            if (printData.sysId) {
                commands = commands.concat(this.stringToBytes(`系统编号: ${printData.sysId}\n`));
            }

            if (printData.orderNum) {
                const orderNumStr = String(printData.orderNum).padStart(4, "0");
                commands = commands.concat(this.stringToBytes(`序号: ${orderNumStr}\n`));
            }

            // 打印时间
            const now = new Date();
            const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}-${String(now.getDate()).padStart(2, "0")}`;
            const timeStr = `${String(now.getHours()).padStart(2, "0")}:${String(now.getMinutes()).padStart(2, "0")}`;
            commands = commands.concat(this.stringToBytes(`打印时间: ${dateStr} ${timeStr}\n`));

            // 打印分隔线
            commands = commands.concat(this.stringToBytes("----------------------------\n"));
        } else {
            // 简单模式 - 只打印二维码
            // 居中对齐
            commands.push(0x1b); // ESC
            commands.push(0x61); // a 对齐方式
            commands.push(0x01); // 1 居中

            // 打印标题
            commands.push(0x1b); // ESC
            commands.push(0x21); // ! 设置字体大小和加粗
            commands.push(0x30); // 0x30 表示大号加粗字体
            commands = commands.concat(this.stringToBytes("扫码\n\n"));

            // 恢复默认字体
            commands.push(0x1b);
            commands.push(0x21);
            commands.push(0x00);
        }

        // 打印二维码
        if (printData.qrCodeUrl) {
            // 居中对齐二维码
            commands.push(0x1b);
            commands.push(0x61);
            commands.push(0x01);

            // 添加二维码命令
            commands = this.addQrCodeCommand(commands, printData.qrCodeUrl);

            // 恢复左对齐
            commands.push(0x1b);
            commands.push(0x61);
            commands.push(0x00);
        }

        // 打印换行和切纸
        commands = commands.concat(this.stringToBytes("\n\n\n"));
        commands.push(0x1d); // GS
        commands.push(0x56); // V
        commands.push(0x00); // 0 全切纸

        return commands;
    }

    /**
     * 字符串转字节数组
     * @param str 要转换的字符串
     * @returns 字节数组
     */
    private static stringToBytes(str: string): number[] {
        // 使用预定义的中文字符映射表进行转换
        return convertToPrinterBytes(str);
    }

    /**
     * 添加二维码打印命令
     * @param commands 当前命令数组
     * @param qrData 二维码数据
     * @returns 添加二维码命令后的数组
     */
    private static addQrCodeCommand(commands: number[], qrData: string): number[] {
        // 选择QR码模型
        commands.push(0x1d); // GS
        commands.push(0x28); // (
        commands.push(0x6b); // k
        commands.push(0x04); // 数据长度
        commands.push(0x00); //
        commands.push(0x31); // 模型1
        commands.push(0x41); //
        commands.push(0x32); // 模型2

        // 设置QR码大小
        commands.push(0x1d); // GS
        commands.push(0x28); // (
        commands.push(0x6b); // k
        commands.push(0x03); // 数据长度
        commands.push(0x00); //
        commands.push(0x31); //
        commands.push(0x43); // C
        commands.push(0x06); // 大小(1-16)

        // 设置QR码纠错等级
        commands.push(0x1d); // GS
        commands.push(0x28); // (
        commands.push(0x6b); // k
        commands.push(0x03); // 数据长度
        commands.push(0x00); //
        commands.push(0x31); //
        commands.push(0x45); // E
        commands.push(0x31); // 纠错等级(L=0x30, M=0x31, Q=0x32, H=0x33)

        // 存储QR码数据
        const length = qrData.length + 3;
        commands.push(0x1d); // GS
        commands.push(0x28); // (
        commands.push(0x6b); // k
        commands.push(length % 256); // 数据长度低位
        commands.push(Math.floor(length / 256)); // 数据长度高位
        commands.push(0x31); //
        commands.push(0x50); // P
        commands.push(0x30); // 0
        commands = commands.concat(this.stringToBytes(qrData));

        // 打印QR码
        commands.push(0x1d); // GS
        commands.push(0x28); // (
        commands.push(0x6b); // k
        commands.push(0x03); // 数据长度
        commands.push(0x00); //
        commands.push(0x31); //
        commands.push(0x51); // Q
        commands.push(0x30); // 0

        return commands;
    }

    /**
     * 将文本绘制到Canvas上并转换为图片
     * @param text 要绘制的文本
     * @param options 选项（宽度、字体大小、颜色等）
     * @returns 返回图片路径的Promise
     */
    private static generateTextImage(text: string, options: any = {}): Promise<string> {
        return new Promise((resolve, reject) => {
            try {
                const { fontSize = 24, color = "#000", fontFamily = "Microsoft YaHei", width = 384 } = options;

                // 创建Canvas上下文
                const ctx = Taro.createCanvasContext(PRINT_CANVAS_ID);

                // 在Taro中不能直接设置画布大小
                // 在canvasToTempFilePath中设置宽高

                // 设置样式
                ctx.setFillStyle(color);
                ctx.setFontSize(fontSize);
                ctx.setTextBaseline("top");

                // 尝试设置字体（如果支持）
                if (typeof ctx.font === "string") {
                    ctx.font = `${fontSize}px ${fontFamily}`;
                }

                // 绘制文本
                ctx.fillText(text, 0, 0);

                // 执行绘制并转换为图片
                ctx.draw(false, () => {
                    // 等待绘制完成
                    setTimeout(() => {
                        Taro.canvasToTempFilePath({
                            canvasId: PRINT_CANVAS_ID,
                            width: width, // 使用这里设置宽度
                            height: fontSize * 2, // 高度设置为字体大小的两倍
                            success(res) {
                                resolve(res.tempFilePath);
                            },
                            fail(err) {
                                console.error("转换图片失败:", err);
                                reject(err);
                            },
                        });
                    }, 200); // 给一些时间让Canvas完成绘制
                });
            } catch (error) {
                console.error("生成文本图片失败:", error);
                reject(error);
            }
        });
    }

    /**
     * 将图片转换为打印机可识别的点阵数据
     * @param imagePath 图片路径
     * @returns 返回图片数据的Promise
     */
    private static async convertImageToPrintData(imagePath: string): Promise<ArrayBuffer> {
        try {
            // 获取图片信息
            const imageInfo = await Taro.getImageInfo({ src: imagePath });
            console.log("图片信息:", imageInfo);

            // 获取图片数据
            // 注意：这里需要使用Promise包装以正确处理返回值
            const fileData = await new Promise<ArrayBuffer>((resolve, reject) => {
                Taro.getFileSystemManager().readFile({
                    filePath: imagePath,
                    success: (res) => {
                        // 返回类型是ArrayBuffer
                        resolve(res.data as ArrayBuffer);
                    },
                    fail: (err) => {
                        console.error("读取文件失败:", err);
                        reject(err);
                    },
                });
            });

            // 构建打印命令
            const commands: number[] = [];

            // 初始化打印机
            commands.push(0x1b); // ESC
            commands.push(0x40); // @ 初始化命令

            // 设置行间距
            commands.push(0x1b); // ESC
            commands.push(0x33); // 3
            commands.push(0x00); // 行间距为0

            // 设置打印模式为位图模式
            commands.push(0x1d); // GS
            commands.push(0x76); // v
            commands.push(0x30); // 0 普通打印模式

            // 设置位图宽度（以点为单位）
            const width = imageInfo.width;
            const widthBytes = Math.ceil(width / 8); // 每8个点为1个字节

            commands.push(widthBytes & 0xff); // 宽度低字节
            commands.push((widthBytes >> 8) & 0xff); // 宽度高字节

            // 设置位图高度（以点为单位）
            const height = imageInfo.height;
            commands.push(height & 0xff); // 高度低字节
            commands.push((height >> 8) & 0xff); // 高度高字节

            // 这里需要将图片数据转换为点阵数据
            // 简化处理，直接发送原始数据
            // 实际应用中需要将图片转换为黑白二值图并进行点阵处理

            // 将ArrayBuffer转换为Uint8Array
            const dataView = new Uint8Array(fileData);
            for (let i = 0; i < dataView.length; i++) {
                commands.push(dataView[i]);
            }

            // 打印并切纸
            commands.push(0x1d); // GS
            commands.push(0x56); // V
            commands.push(0x00); // 0 全切纸

            // 转换为ArrayBuffer
            return new Uint8Array(commands).buffer;
        } catch (error) {
            console.error("转换图片数据失败:", error);
            throw error;
        }
    }

    /**
     * 打印雅黑字体文本（通过图片方式）
     * @param text 要打印的文本
     * @param options 选项（字体大小、颜色等）
     * @param onSuccess 成功回调
     * @param onFail 失败回调
     */
    static printYaHeiText(text: string, options: any = {}, onSuccess: () => void = () => {}, onFail: (error: any) => void = () => {}) {
        // 显示加载提示
        message.openLoading("准备打印雅黑字体...");

        // 设置默认选项
        const printOptions = {
            fontFamily: "Microsoft YaHei",
            fontSize: 24,
            ...options,
        };

        // 开始打印流程
        this.generateTextImage(text, printOptions)
            .then((imagePath) => {
                // 图片生成成功，准备打印数据
                message.openLoading("正在处理图片数据...");
                return this.convertImageToPrintData(imagePath);
            })
            .then((printData) => {
                // 图片数据准备完成，开始打印
                message.openLoading("正在连接打印机...");

                // 创建一个特殊的打印数据对象
                const imageDataObj = {
                    isImageData: true,
                    imageBuffer: printData,
                };

                // 处理蓝牙状态并初始化
                this.handleBluetoothState(imageDataObj, onSuccess, onFail);
            })
            .catch((error) => {
                message.closeLoading();
                message.error("打印准备失败");
                console.error("打印准备失败:", error);
                onFail(error);
            });
    }

    // 下面的方法已经不再使用，改为使用ESC/POS命令

    /**
     * 将字符串转换为ArrayBuffer
     * @param str 要转换的字符串
     * @returns ArrayBuffer对象
     */
    private static stringToArrayBuffer(str: string): ArrayBuffer {
        // 在微信小程序中直接将字符串转换为ArrayBuffer
        const buffer = new ArrayBuffer(str.length);
        const dataView = new DataView(buffer);
        for (let i = 0; i < str.length; i++) {
            dataView.setUint8(i, str.charCodeAt(i));
        }
        return buffer;
    }

    /**
     * 分包发送打印数据
     * @param deviceId 设备ID
     * @param serviceId 服务ID
     * @param characteristicId 特性ID
     * @param buffer 数据缓冲区
     * @param onSuccess 成功回调
     * @param onFail 失败回调
     */
    private static sendPrintDataInChunks(
        deviceId: string,
        serviceId: string,
        characteristicId: string,
        buffer: ArrayBuffer,
        onSuccess: () => void,
        onFail: (error: Error) => void
    ) {
        // 蓝牙传输大小限制，一般为20字节
        const CHUNK_SIZE = 20;
        const totalLength = buffer.byteLength;
        let sentLength = 0;

        // 递归发送数据包
        const sendChunk = () => {
            if (sentLength >= totalLength) {
                // 所有数据已发送完成
                onSuccess();
                return;
            }

            // 计算当前包的大小
            const chunkSize = Math.min(CHUNK_SIZE, totalLength - sentLength);
            const chunk = buffer.slice(sentLength, sentLength + chunkSize);

            // 发送数据
            Taro.writeBLECharacteristicValue({
                deviceId: deviceId,
                serviceId: serviceId,
                characteristicId: characteristicId,
                value: chunk,
                success: () => {
                    // 更新已发送数据长度
                    sentLength += chunkSize;

                    // 打印进度
                    const progress = Math.floor((sentLength / totalLength) * 100);
                    console.log(`打印进度: ${progress}%`);

                    // 等待一小段时间再发送下一包，避免数据丢失
                    // 增加延时，给打印机更多时间处理数据
                    setTimeout(sendChunk, 50);
                },
                fail: (err) => {
                    console.error("发送数据失败:", err);
                    onFail(new Error("发送打印数据失败: " + err.errMsg));
                },
            });
        };

        // 开始发送第一包数据
        sendChunk();
    }
}
