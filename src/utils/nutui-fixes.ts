/**
 * Utility functions to fix NutUI compatibility issues with mini-programs
 *
 * This fixes the "Right-hand side of 'instanceof' is not an object" error
 * in CSSTransition component by patching the instanceof check at runtime.
 */

export function applyNutUIFixes() {
    if (process.env.TARO_ENV === "weapp" || process.env.TARO_ENV === "jd") {
        // Mock the CSSTransition function to avoid instanceof checks
        // This addresses the "Right-hand side of 'instanceof' is not an object" error
        if (global && typeof global === "object") {
            // Create a proper constructor function that can be used with instanceof
            function MockCSSTransition() {
                this.isReactComponent = true;
                this._reactInternals = {};
                return this;
            }

            // Add necessary prototype methods
            MockCSSTransition.prototype = {
                constructor: MockCSSTransition,
                render: function () {
                    return null;
                },
            };

            // Add static properties
            MockCSSTransition.defaultProps = {};

            // @ts-ignore
            global.CSSTransition = MockCSSTransition;
        }
    }
}

export default applyNutUIFixes;
