/**
 * 中文字符到打印机字符集的映射
 * 这是一个简化的映射表，只包含应用中常用的中文字符
 * 实际应用中，应该使用完整的GBK编码表
 */
export const chineseCharMapping: Record<string, number[]> = {
    // 常用标题和标签
    "电": [0xB5, 0xE7],
    "子": [0xD7, 0xD3],
    "券": [0xC8, 0xB7],
    "扫": [0xC9, 0xA8],
    "码": [0xC2, 0xEB],
    "项": [0xCF, 0xEE],
    "目": [0xC4, 0xBF],
    "名": [0xC3, 0xFB],
    "称": [0xB3, 0xC6],
    "系": [0xCF, 0xB5],
    "统": [0xCD, 0xB3],
    "编": [0xB1, 0xE0],
    "号": [0xBA, 0xC5],
    "序": [0xD0, 0xF2],
    "打": [0xB4, 0xF2],
    "印": [0xD3, 0xA1],
    "时": [0xCA, 0xB1],
    "间": [0xBC, 0xE4],
    
    // 常用标点符号
    ":": [0x3A],
    "：": [0xA3, 0xBA],
    "-": [0x2D],
    "—": [0xA1, 0xAA],
    " ": [0x20],
    "\n": [0x0A],
    
    // 数字和字母使用ASCII码，不需要特殊映射
    
    // 如果遇到未知字符，使用空格替代
    "?": [0x3F] // 问号作为未知字符的替代
};

/**
 * 将中文字符转换为打印机可识别的字节数组
 * @param text 要转换的文本
 * @returns 转换后的字节数组
 */
export function convertToPrinterBytes(text: string): number[] {
    const result: number[] = [];
    
    for (let i = 0; i < text.length; i++) {
        const char = text[i];
        
        // 检查是否是ASCII字符
        if (char.charCodeAt(0) < 128) {
            result.push(char.charCodeAt(0));
        } else {
            // 查找映射表
            const bytes = chineseCharMapping[char];
            if (bytes) {
                // 如果找到映射，添加对应的字节
                result.push(...bytes);
            } else {
                // 如果没有找到映射，使用问号替代
                result.push(0x3F); // 问号的ASCII码
            }
        }
    }
    
    return result;
}
