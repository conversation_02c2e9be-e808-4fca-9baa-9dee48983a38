/**
 *
 */
declare namespace API {
    type UserInfo = {
        UnionId: string;
        OpenId: string;
        Authorization: string;
        refreshToken: string;
        createTime: string;
        refresh_token?: string;
        access_token?: string;
        UserId?: string;
        StudentId?: string;
        // accessToken: string;
        profile: {
            avatarUrl: string;
            backgroundUrl: string;
            nickname: string;
            phone: string;
            eventCount: number;
            newFollows: number;
            followeds: number;
            userId: string;
            point: number; //积分
            sale: number; //电券
            balance: number; //余额
        };
        RealName: "";
        TenantName: "";
        Account: "";
        UserSysId: "";
        IsTenantAdmin: false;
        Avatar: "";
    };

    type pdToast = {
        msg: string;
        type: string;
        duration: number;
        icon: any;
        cover: boolean;
        fn: Function;
    };

    type pdDialog = {
        title: string;
        msg: string;
        okText: string;
        cancelText: string;
        ok: Function;
        cancel: Function;
        noCancelBtn: boolean;
        textAlign: string;
    };

    /**
     * 系统默认返回的类型
     */
    type Result<T> = {
        data: T;
        code: number;
        errorCode: string;
        message: string;
        success: boolean;
        timestamp: number;
    };

    /**
     * 下拉框
     */
    type SelectItem = {
        label: string;
        text: string;
        value: string;
        children?: SelectItem[];
    };

    /**
     * 下拉框
     */
    type SelectResult = {
        success: boolean;
        message: string;
        data: SelectItem[];
        detail: [];
    };
}
