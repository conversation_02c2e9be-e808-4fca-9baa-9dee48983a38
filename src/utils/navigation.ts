import Taro from "@tarojs/taro";
import { resetMessageApi } from "@/components/MessageApi/MessageApiSingleton";

/**
 * 增强版的页面回退函数，确保组件状态正确
 * @param delta 回退的页面数量，默认为1
 * @param closeLoading 是否关闭loading，默认为false
 */
export function safeNavigateBack(delta: number = 1, closeLoading: boolean = false) {
    // 先重置消息API状态
    resetMessageApi(closeLoading);

    // 触发一个事件通知页面即将回退
    Taro.eventCenter.trigger("pageWillGoBack");

    // 使用Taro的导航回退
    Taro.navigateBack({
        delta,
        fail: () => {
            // 如果失败(例如已经是第一个页面)，则返回首页
            Taro.reLaunch({
                url: "/pages/index/index",
            });
        },
    });
}

/**
 * 增强版的页面重启函数，确保组件状态正确
 * @param url 目标页面URL
 * @param closeLoading 是否关闭loading，默认为false
 */
export function safeReLaunch(url: string, closeLoading: boolean = false) {
    // 先重置消息API状态
    resetMessageApi(closeLoading);

    // 触发一个事件通知页面即将重启
    Taro.eventCenter.trigger("pageWillReLaunch");

    // 使用Taro的重启函数
    Taro.reLaunch({
        url,
    });
}

/**
 * 增强版的页面导航函数，确保组件状态正确
 * @param url 目标页面URL
 * @param closeLoading 是否关闭loading，默认为false
 */
export function safeNavigateTo(url: string, closeLoading: boolean = false) {
    // 先重置消息API状态
    resetMessageApi(closeLoading);

    // 使用Taro的导航函数
    Taro.navigateTo({
        url,
    });
}

/**
 * 强制刷新当前页面的函数
 * 这个方法会重新加载整个页面以解决组件不显示的问题
 * @param closeLoading 是否关闭loading，默认为false
 */
export function forceRefreshCurrentPage(closeLoading: boolean = false) {
    const pages = Taro.getCurrentPages();
    if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        const route = currentPage.route;
        const options = currentPage.options || {};

        // 将options对象转换为查询字符串
        const queryParams = Object.keys(options)
            .map((key) => `${key}=${options[key]}`)
            .join("&");

        // 构建完整URL
        const url = queryParams ? `/${route}?${queryParams}` : `/${route}`;

        console.log("Force refreshing current page:", url);

        // 重置消息API状态
        resetMessageApi(closeLoading);

        // 重新启动当前页面
        Taro.reLaunch({
            url,
        });
    }
}
