import Taro from "@tarojs/taro";
// import moment from "moment";
import { tool } from "./tool";
import request from "@service/request";
import sysConfig from "@config/index";

// 添加一个事件发布订阅来控制loading状态
export const LOGIN_LOADING_STATUS = {
    START: "login-loading-start",
    END: "login-loading-end",
};

const accessTokenKey = sysConfig.ACCESS_TOKEN_KEY;

/**
 * 获取用户信息
 * @param forceUpdate 是否强制从服务器获取最新数据，默认为false
 * 如果forceUpdate为true，则直接从服务器获取新数据
 * 如果forceUpdate为false，且本地存储的用户信息有效（3天内），则使用本地数据
 * 否则从服务器获取新数据
//  */
// export const getOpenId = async (): Promise<any> => {
//     return new Promise((resolve, reject) => {
//         const openId = tool.data.get("openId");
//         var accessToken = tool.data.get(accessTokenKey);

//         if (!openId || !accessToken) {
//             // 否则重新登录获取
//             Taro.login()
//                 .then((loginResult) => {
//                     request
//                         .post<any>("/Wx/WxAuth/authLogin/" + loginResult.code, {}, undefined, undefined, false)
//                         .then((json) => {
//                             if (json.data?.OpenId && json.data?.OpenId != "" && json.data?.OpenId != null) {
//                                 tool.data.set("openId", json.data?.OpenId);
//                             }
//                             if (!json.success) {
//                                 Taro.showModal({
//                                     title: "微信状态获取失败",
//                                     content: json.message || "未知错误",
//                                     showCancel: false,
//                                 });
//                                 reject(new Error(json.message || "未知错误"));
//                                 return;
//                             }
//                             resolve(json.data);
//                         })
//                         .catch((error) => {
//                             reject(error);
//                         });
//                 })
//                 .catch((error) => {
//                     reject(error);
//                 });
//         } else {
//             resolve(openId);
//         }
//     });
// };

/**
 * 登录方法
 * @param isStudent 是否是学生登录，默认为false
 * @returns Promise<any> 返回登录结果
 */
export const login = async (isStudent: boolean = false): Promise<any> => {
    return new Promise(async (resolve, reject) => {
        try {
            // 发布登录开始事件
            Taro.eventCenter.trigger(LOGIN_LOADING_STATUS.START);

            const loginResult = await Taro.login();

            if (isStudent) {
                // 学生登录
                const response = await request.post<any>(`/Jx/Student/WxLogin/getStudentInfo/${loginResult.code}`, {});

                if (response && response.success) {
                    resolve(response.data);
                } else {
                    // Taro.showModal({
                    //     title: "学生登录失败",
                    //     content: response?.message || "登录失败，请检查登录信息",
                    //     showCancel: false,
                    // });
                    reject(new Error(response?.message || "学生登录失败"));
                }
            } else {
                // 普通微信授权登录
                const response = await request.post<any>("/Wx/WxAuth/authLogin/" + loginResult.code, {}, undefined, undefined, false);

                if (response.data?.OpenId && response.data?.OpenId !== "" && response.data?.OpenId !== null) {
                    tool.data.set("openId", response.data?.OpenId);
                }

                if (!response.success) {
                    // Taro.showModal({
                    //     title: "微信状态获取失败",
                    //     content: response.message || "未知错误",
                    //     showCancel: false,
                    // });
                    reject(new Error(response.message || "微信登录失败"));
                    return;
                }

                resolve(response.data);
            }
        } catch (error) {
            Taro.showModal({
                title: "登录失败",
                content: "网络错误或服务异常",
                showCancel: false,
            });
            reject(error);
        } finally {
            // 发布登录结束事件
            Taro.eventCenter.trigger(LOGIN_LOADING_STATUS.END);
        }
    });
};
