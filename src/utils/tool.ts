import Taro from "@tarojs/taro";

interface Tool {
    data: {
        set(key: string, value: any): void;
        get(key: string): any;
        remove(key: string): void;
        clear(): void;
    };
}

export const tool: Tool = {
    // localStorage
    data: {
        set(key, value) {
            console.log("tool.data.set");
            console.log(key);
            console.log(JSON.stringify(value));
            const _set = JSON.stringify(value);
            return Taro.setStorageSync(key, _set);
            // return localStorage.setItem(table, _set)
        },
        get(key) {
            let data = Taro.getStorageSync(key);
            try {
                // @ts-ignore
                data = JSON.parse(data);
            } catch (err) {
                return null;
            }
            return data;
        },
        remove(key) {
            return Taro.removeStorageSync(key);
        },

        clear() {
            const storage = Taro.getStorageInfoSync();
            storage.keys.forEach((key) => {
                if (!key.startsWith("longtime-") && key !== "openId") {
                    Taro.removeStorageSync(key);
                }
            });
        },
    },
};
