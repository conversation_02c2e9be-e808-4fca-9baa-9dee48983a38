interface RefundConfig {
    subject1FirstExamRefund: number;
    subject1RetakeExamRefund: number;
    subject2FirstExamRefund: number;
    subject2RetakeExamRefund: number;
    subject3FirstExamRefund: number;
    subject3RetakeExamRefund: number;
    refundMethod: string;
}

let refundConfig: RefundConfig | null = null;

export const setRefundConfig = (config: RefundConfig) => {
    refundConfig = config;
};

export const getRefundConfig = () => {
    return refundConfig;
};
