import React from "react";
import { View, Text, Image } from "@tarojs/components";
import { Badge } from "@nutui/nutui-react-taro";
import "./index.scss";

const TestStudentList: React.FC = () => {
    // 模拟学员数据
    const mockData = [
        {
            Id: "1",
            TenantId: "test",
            xm: "李尚盛",
            Ywzt: "2025-07-16 科目二 合格",
            Age: 18,
            CarType: "C1",
            NoPay: 0
        },
        {
            Id: "2", 
            TenantId: "test",
            xm: "白乾花",
            Ywzt: "2025-07-16 科目二 合格",
            Age: 27,
            CarType: "C2",
            NoPay: 1
        }
    ];

    return (
        <View className="test-page">
            <View className="list">
                {mockData.map((item, index) => (
                    <View key={item.Id} className="flex-col flex-1 group_2">
                        <View className={item.NoPay > 0 ? "flex-row justify-between relative mt-12 list-item qianfei" : "flex-row justify-between relative mt-12 list-item"}>
                            <View className="flex-row self-center">
                                <Badge style={{ marginInlineEnd: "10rpx" }} value={index + 1} max={999999}>
                                    <Image
                                        className="student-avatar"
                                        src={`https://api.51panda.com/Jx/Image/StudentImage/getStudentZp?Id=${item.Id}&TenantId=${item.TenantId}`}
                                        mode="aspectFill"
                                        onError={() => {
                                            console.log("图片加载失败");
                                        }}
                                    />
                                </Badge>
                                <View className="flex-col shrink-0 group_5 ml-13">
                                    <Text className="self-start font_3">{item.xm}</Text>
                                    <Text className="mt-6 self-stretch font_4">{item.Ywzt}</Text>
                                </View>
                            </View>
                            <View className="right-info">
                                <Text className="age-text">{item.Age} 岁</Text>
                                <Text className="car-type-text">{item.CarType}</Text>
                            </View>
                        </View>
                    </View>
                ))}
            </View>
        </View>
    );
};

export default TestStudentList;
