.docker-container {
    padding: 20px;
    min-height: 100vh;
    background-color: #f5f5f5;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .title {
            font-size: 20px;
            font-weight: bold;
        }

        .config-btn {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 0 20px;
            height: 36px;
            line-height: 36px;
            border-radius: 4px;
        }
    }

    .content {
        .refresh-btn {
            margin-bottom: 20px;
            background-color: #52c41a;
            color: white;
            border: none;
            padding: 0 20px;
            height: 36px;
            line-height: 36px;
            border-radius: 4px;
        }

        .docker-list {
            height: calc(100vh - 200px);

            .docker-item {
                background-color: white;
                padding: 15px;
                margin-bottom: 10px;
                border-radius: 8px;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .container-info {
                    flex: 1;

                    .container-name {
                        font-size: 16px;
                        font-weight: bold;
                        margin-bottom: 5px;
                        display: block;
                    }

                    .container-status {
                        font-size: 14px;
                        padding: 2px 8px;
                        border-radius: 4px;

                        &.running {
                            background-color: #f6ffed;
                            color: #52c41a;
                        }

                        &.exited {
                            background-color: #fff1f0;
                            color: #f5222d;
                        }

                        &.created {
                            background-color: #e6f7ff;
                            color: #1890ff;
                        }
                    }
                }

                .container-actions {
                    display: flex;
                    gap: 10px;

                    .action-btn {
                        font-size: 14px;
                        padding: 0 10px;
                        height: 32px;
                        line-height: 32px;
                        border-radius: 4px;
                        background-color: #f0f0f0;
                        color: #333;
                    }
                }
            }
        }
    }

    .modal-content {
        padding: 20px;

        .input-group {
            margin-bottom: 15px;

            .input-label {
                display: block;
                margin-bottom: 8px;
                font-size: 14px;
                color: #333;
            }

            .input-field {
                width: 100%;
                height: 40px;
                padding: 0 10px;
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                background-color: white;
            }
        }
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        padding: 15px 20px;
        border-top: 1px solid #f0f0f0;

        .modal-btn {
            margin-left: 10px;
            padding: 0 20px;
            height: 36px;
            line-height: 36px;
            border-radius: 4px;
            background-color: #f0f0f0;
            color: #333;

            &.primary {
                background-color: #1890ff;
                color: white;
            }
        }
    }

    .log-content {
        max-height: 60vh;
        background-color: #000;
        color: #fff;
        padding: 10px;
        font-family: monospace;
        white-space: pre-wrap;
        word-break: break-all;
    }
}

.modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-container {
    width: 80%;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 18px;
    font-weight: bold;
}

.modal-close {
    font-size: 24px;
    color: #999;
    padding: 0 10px;
}

.modal-content {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.modal-btn {
    padding: 0 20px;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    background-color: #f5f5f5;
    border-radius: 4px;

    &.primary {
        background-color: #07c160;
        color: #fff;
    }
}
