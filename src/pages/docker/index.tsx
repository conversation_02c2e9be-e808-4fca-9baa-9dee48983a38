import { Component } from "react";
import Taro from "@tarojs/taro";
import { View, Text, Input, Button, ScrollView } from "@tarojs/components";
import "./index.scss";
import Layout from "@/components/Layout";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import request from "@/service/request";

interface IState {
    sshConfig: {
        host: string;
        port: string;
        username: string;
        password: string;
    };
    dockerList: Array<{
        id: string;
        name: string;
        status: string;
        created: string;
    }>;
    showConfigModal: boolean;
    showLogModal: boolean;
    currentLogs: string;
    loading: boolean;
}

export default class DockerManagement extends Component<{}, IState> {
    constructor(props: any) {
        super(props);
        this.state = {
            sshConfig: {
                host: "",
                port: "22",
                username: "",
                password: "",
            },
            dockerList: [],
            showConfigModal: false,
            showLogModal: false,
            currentLogs: "",
            loading: false,
        };
    }

    componentDidMount() {
        // 从本地存储加载配置
        const savedConfig = Taro.getStorageSync("dockerSshConfig");
        if (savedConfig) {
            this.setState({ sshConfig: savedConfig });
        }
    }

    // 保存 SSH 配置
    handleSaveConfig = () => {
        const { sshConfig } = this.state;
        Taro.setStorageSync("dockerSshConfig", sshConfig);
        this.setState({ showConfigModal: false });
        message.success("配置保存成功");
    };

    // 获取 Docker 列表
    fetchDockerList = async () => {
        this.setState({ loading: true });
        try {
            const { sshConfig } = this.state;
            const response: any = await request.post("/Docker/List", {
                host: sshConfig.host,
                port: sshConfig.port,
                username: sshConfig.username,
                password: sshConfig.password,
            });

            if (response.success) {
                this.setState({ dockerList: response.data });
            } else {
                message.error(response.message || "获取Docker列表失败");
            }
        } catch (error) {
            console.error("获取Docker列表失败:", error);
            message.error("获取Docker列表失败，请检查配置");
        } finally {
            this.setState({ loading: false });
        }
    };

    // 重启 Docker 容器
    handleRestartContainer = async (containerId: string) => {
        try {
            const { sshConfig } = this.state;
            const response: any = await request.post("/Docker/Restart", {
                host: sshConfig.host,
                port: sshConfig.port,
                username: sshConfig.username,
                password: sshConfig.password,
                containerId,
            });

            if (response.success) {
                message.success("容器重启成功");
                this.fetchDockerList();
            } else {
                message.error(response.message || "重启容器失败");
            }
        } catch (error) {
            console.error("重启容器失败:", error);
            message.error("重启容器失败，请稍后重试");
        }
    };

    // 查看容器日志
    handleViewLogs = async (containerId: string) => {
        try {
            const { sshConfig } = this.state;
            const response: any = await request.post("/Docker/Logs", {
                host: sshConfig.host,
                port: sshConfig.port,
                username: sshConfig.username,
                password: sshConfig.password,
                containerId,
            });

            if (response.success) {
                this.setState({
                    showLogModal: true,
                    currentLogs: response.data,
                });
            } else {
                message.error(response.message || "获取日志失败");
            }
        } catch (error) {
            console.error("获取日志失败:", error);
            message.error("获取日志失败，请稍后重试");
        }
    };

    render() {
        const { sshConfig, dockerList, showConfigModal, showLogModal, currentLogs, loading } = this.state;

        return (
            <Layout>
                <View className="docker-container">
                    <View className="header">
                        <Text className="title">Docker 管理</Text>
                        <Button className="config-btn" onClick={() => this.setState({ showConfigModal: true })}>
                            配置 SSH
                        </Button>
                    </View>

                    <View className="content">
                        <Button className="refresh-btn" onClick={this.fetchDockerList} loading={loading}>
                            刷新列表
                        </Button>

                        <ScrollView className="docker-list" scrollY>
                            {dockerList.map((container) => (
                                <View key={container.id} className="docker-item">
                                    <View className="container-info">
                                        <Text className="container-name">{container.name}</Text>
                                        <Text className={`container-status ${container.status.toLowerCase()}`}>{container.status}</Text>
                                    </View>
                                    <View className="container-actions">
                                        <Button className="action-btn" onClick={() => this.handleRestartContainer(container.id)}>
                                            重启
                                        </Button>
                                        <Button className="action-btn" onClick={() => this.handleViewLogs(container.id)}>
                                            日志
                                        </Button>
                                    </View>
                                </View>
                            ))}
                        </ScrollView>
                    </View>

                    {/* SSH 配置弹窗 */}
                    {showConfigModal && (
                        <View className="modal-mask">
                            <View className="modal-container">
                                <View className="modal-header">
                                    <Text className="modal-title">SSH 配置</Text>
                                    <View className="modal-close" onClick={() => this.setState({ showConfigModal: false })}>
                                        ×
                                    </View>
                                </View>
                                <View className="modal-content">
                                    <View className="input-group">
                                        <Text className="input-label">主机地址</Text>
                                        <Input
                                            className="input-field"
                                            type="text"
                                            value={sshConfig.host}
                                            onInput={(e) =>
                                                this.setState({
                                                    sshConfig: { ...sshConfig, host: e.detail.value },
                                                })
                                            }
                                        />
                                    </View>
                                    <View className="input-group">
                                        <Text className="input-label">端口</Text>
                                        <Input
                                            className="input-field"
                                            type="number"
                                            value={sshConfig.port}
                                            onInput={(e) =>
                                                this.setState({
                                                    sshConfig: { ...sshConfig, port: e.detail.value },
                                                })
                                            }
                                        />
                                    </View>
                                    <View className="input-group">
                                        <Text className="input-label">用户名</Text>
                                        <Input
                                            className="input-field"
                                            type="text"
                                            value={sshConfig.username}
                                            onInput={(e) =>
                                                this.setState({
                                                    sshConfig: { ...sshConfig, username: e.detail.value },
                                                })
                                            }
                                        />
                                    </View>
                                    <View className="input-group">
                                        <Text className="input-label">密码</Text>
                                        <Input
                                            className="input-field"
                                            value={sshConfig.password}
                                            onInput={(e) =>
                                                this.setState({
                                                    sshConfig: { ...sshConfig, password: e.detail.value },
                                                })
                                            }
                                        />
                                    </View>
                                </View>
                                <View className="modal-footer">
                                    <Button className="modal-btn" onClick={() => this.setState({ showConfigModal: false })}>
                                        取消
                                    </Button>
                                    <Button className="modal-btn primary" onClick={this.handleSaveConfig}>
                                        确定
                                    </Button>
                                </View>
                            </View>
                        </View>
                    )}

                    {/* 日志查看弹窗 */}
                    {showLogModal && (
                        <View className="modal-mask">
                            <View className="modal-container">
                                <View className="modal-header">
                                    <Text className="modal-title">容器日志</Text>
                                    <View className="modal-close" onClick={() => this.setState({ showLogModal: false })}>
                                        ×
                                    </View>
                                </View>
                                <ScrollView className="log-content" scrollY>
                                    <Text className="log-text">{currentLogs}</Text>
                                </ScrollView>
                                <View className="modal-footer">
                                    <Button className="modal-btn" onClick={() => this.setState({ showLogModal: false })}>
                                        关闭
                                    </Button>
                                </View>
                            </View>
                        </View>
                    )}
                </View>
            </Layout>
        );
    }
}
