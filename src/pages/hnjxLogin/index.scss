page {
    width: 100%;
    height: 100%;
    font-family: BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Microsoft Yahei", sans-serif;
}

view,
image,
text {
    box-sizing: border-box;
    flex-shrink: 0;
}

.page {
    background-color: #ffffff;
    width: 100%;
    min-height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
}

.group {
    flex: 1;
    position: relative;
    overflow: visible;
}

.section {
    padding: 120rpx 0 180rpx;
    background: linear-gradient(125deg, #4b7bef 0%, #3264ed 40%, #2851da 100%);
    position: relative;
    width: 100%;
    overflow: hidden;

    &::before,
    &::after {
        content: "";
        position: absolute;
        width: 600rpx;
        height: 600rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
    }

    &::before {
        top: -200rpx;
        left: -200rpx;
        animation: float 8s infinite ease-in-out;
    }

    &::after {
        bottom: -300rpx;
        right: -200rpx;
        animation: float 6s infinite ease-in-out reverse;
    }
}

@keyframes float {
    0%,
    100% {
        transform: translateY(0) scale(1);
    }
    50% {
        transform: translateY(-20rpx) scale(1.05);
    }
}

.title-container {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx;
}

.main-title {
    font-size: 68rpx;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 16rpx;
    letter-spacing: 6rpx;
    text-shadow: 2rpx 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.sub-title {
    font-size: 32rpx;
    color: rgba(255, 255, 255, 0.9);
    letter-spacing: 4rpx;
    text-shadow: 1rpx 1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.group_2 {
    width: 327.32rpx;
}

.font {
    font-size: 48rpx;
    font-family: Yuanti SC, STYuanti-SC-Regular;
    line-height: 58rpx;
    color: #383838;
}

.text {
    font-weight: 700;
}

.group_3 {
    margin-top: -164rpx;
}

.section_2 {
    margin-left: 42rpx;
    margin-right: 42rpx;
    padding: 26rpx 20rpx 150rpx;
    position: relative;
    // background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(246, 249, 255, 0.95) 100%);
    background: #ffffff;
    border-radius: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(43, 93, 224, 0.08);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(229, 232, 237, 0.6);
    margin: 0 auto;
    max-width: 666rpx;
    width: calc(100% - 84rpx);
    z-index: 1;
    margin-bottom: 40rpx;

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 200rpx;
        // background: linear-gradient(180deg, rgba(43, 93, 224, 0.05) 0%, transparent 100%);
        border-radius: 24rpx 24rpx 0 0;
        z-index: 0;
    }

    &::after {
        content: "";
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        width: 160rpx;
        height: 160rpx;
        // background: linear-gradient(45deg, transparent 0%, rgba(43, 93, 224, 0.03) 50%, rgba(43, 93, 224, 0.08) 100%);
        border-radius: 50%;
        opacity: 0.8;
    }

    .decoration {
        position: absolute;
        bottom: 20rpx;
        left: 20rpx;
        width: 120rpx;
        height: 120rpx;
        background: linear-gradient(-45deg, rgba(43, 93, 224, 0.06) 0%, transparent 100%);
        border-radius: 16rpx;
        transform: rotate(-15deg);
    }

    .content-wrapper {
        position: relative;
        z-index: 1;
        overflow: visible;
    }
}

@keyframes float {
    0% {
        transform: translateY(0) rotate(-15deg);
    }
    50% {
        transform: translateY(-10rpx) rotate(-12deg);
    }
    100% {
        transform: translateY(0) rotate(-15deg);
    }
}

.section_2 .decoration {
    animation: float 6s ease-in-out infinite;
}

.group_4 {
    padding: 0 68rpx;
}

.group_5 {
    padding-bottom: 20rpx;
}

.font_2 {
    font-size: 36rpx;
    font-weight: 600;
    font-family: Yuanti SC, STYuanti-SC-Regular;
    line-height: 1.2;
    margin-bottom: 12rpx;
}

.text_2 {
    color: #3264ed;
    position: relative;
    display: inline-block;

    &::after {
        content: "";
        position: absolute;
        bottom: -8rpx;
        left: 0;
        width: 100%;
        height: 4rpx;
        background: #3264ed;
        border-radius: 4rpx;
    }
}

.text_3 {
    margin-right: 16rpx;
    color: #9e9e9e;
    line-height: 32.3rpx;
}

.text_2_tab2 {
    color: #3264ed;
    line-height: 32.3rpx;
    margin-right: 16rpx;
}

.text_3_tab2 {
    color: #9e9e9e;
}

.section_3 {
    margin-top: -164rpx;
}

.section_3_tab2 {
    margin-right: 48rpx;
    background-color: #3264ed;
    border-radius: 228rpx;
    width: 58rpx;
    height: 8rpx;
}

.group_6 {
    margin-top: 100rpx;
}

.text-wrapper {
    padding: 36rpx 0;
    background-color: #eef3ff;
    border-radius: 20rpx;
    border: 2rpx solid #3264ed;
    transition: all 0.3s ease;
    margin-bottom: 24rpx;

    &:focus-within {
        background-color: #f7fafc;
        box-shadow: 0 4rpx 12rpx rgba(50, 100, 237, 0.1);
    }
}

.font_3 {
    font-size: 28rpx;
    font-family: Yuanti SC, STYuanti-SC-Regular;
    line-height: 28.38rpx;
}

.text_4 {
    margin-left: 52rpx;
    color: #202020;
    line-height: 22.92rpx;
    font-weight: 500;
}

.section_4 {
    padding: 19rpx 18rpx 19rpx 36rpx;
    background-color: #eef3ff;
    border-radius: 20rpx;
    border: 2rpx solid transparent;
    transition: all 0.3s ease;

    &:focus-within {
        border-color: #3264ed;
        background-color: #f7fafc;
    }
}

.text_5 {
    color: #8f95a1;
    line-height: 28rpx;
}

.text-wrapper_2 {
    padding: 20rpx 0;
    background-color: #ffffff;
    border-radius: 12rpx;
    width: 228rpx;
    height: 72rpx;
}

.text_6 {
    color: #3264ed;
    line-height: 29.72rpx;
}

.text-wrapper_3 {
    margin-right: 0;
    margin-top: 80rpx;
    padding: 32rpx 0;
    background-color: #3264ed;
    border-radius: 20rpx;
    width: 100%;
    text-align: center;
}

.text_7 {
    color: #ffffff;
}

.font_3.text_7.text-wrapper_3 {
    background-color: #3264ed !important;
    border-radius: 20rpx !important;

    &:active {
        opacity: 0.9;
    }
}

.group_7 {
    margin-top: 60rpx;
    margin-bottom: 120rpx;
    position: relative;
    z-index: 2;
}

.group_8 {
    border-radius: 344rpx;
    width: 30rpx;
    height: 30rpx;
    border-left: solid 2rpx #3264ed;
    border-right: solid 2rpx #3264ed;
    border-top: solid 2rpx #3264ed;
    border-bottom: solid 2rpx #3264ed;
}

.group_9 {
    line-height: 22.32rpx;
    height: auto;
    position: relative;
    z-index: 2;
}

.font_4 {
    font-size: 24rpx;
    font-family: Yuanti SC, STYuanti-SC-Regular;
    line-height: 22.32rpx;
    color: #000000;
}

.font_5 {
    font-size: 24rpx;
    font-family: Yuanti SC, STYuanti-SC-Regular;
    line-height: 22.32rpx;
    color: #3264ed;
}

.text_8 {
    line-height: 21.24rpx;
}

.text_9 {
    line-height: 22.18rpx;
}

.section_5 {
    background-color: #ffffff;
    height: 68rpx;
}

.pos {
    position: absolute;
    left: 0;
    right: 0;
    top: 1132rpx;
}

.group_8_bottom {
    display: none;
}

.bottom-text-container {
    position: fixed;
    bottom: calc(env(safe-area-inset-bottom) + 30rpx);
    left: 0;
    right: 0;
    width: 100%;

    .flex-row {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .section_4_bottom,
    .section_5_bottom {
        width: 32rpx;
        height: 2rpx;
        background-color: #999;
        margin: 0 16rpx;
    }

    .text_13_bottom {
        color: #999;
        font-size: 24rpx;
        line-height: 32rpx;
    }
}

.section_4_bottom {
    background-image: linear-gradient(90deg, #cccccc00 0%, #cccccc 100%);
    width: 116rpx;
    height: 2rpx;
}

.section_5_bottom {
    background-image: linear-gradient(90deg, #cccccc 0%, #cccccc00 100%);
    width: 116rpx;
    height: 2rpx;
}

.font_4 {
    font-size: 24rpx;
    font-family: Yuanti SC, STYuanti-SC-Regular;
    line-height: 22.28rpx;
    color: #cccccc;
}

.nut-picker-control {
    height: 80rpx;
}

.nut-popup-bottom.nut-popup-round {
    border-radius: 24rpx 24rpx 0 0;
}

.nut-picker-roller-item-title {
    font-size: 30rpx;
}

.nut-picker-view-panel {
    height: 40vh;
}

.nut-popup {
    font-family: Yuanti SC, STYuanti-SC-Regular;
}

:global {
    .nut-dialog {
        z-index: 99999 !important;
    }

    .nut-overlay {
        z-index: 99998 !important;
    }

    .nut-dialog__content {
        max-height: 70vh;
        overflow-y: auto;
    }
}

.loading-spinner {
    display: inline-block;
    width: 60rpx;
    height: 60rpx;
    border: 5rpx solid rgba(22, 119, 255, 0.3);
    border-radius: 50%;
    border-top-color: #1677ff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
