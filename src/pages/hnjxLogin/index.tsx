import { useRef, useState } from "react";
import "./index.scss";
import "@utils/app.scss";
import { Button } from "@nutui/nutui-react-taro";
import request from "@service/request";
import Taro, { useDidShow, Current } from "@tarojs/taro";
import IconFont from "@components/iconfont";
import PdAgreement from "@components/pdAgreemen";
import { Input } from "@tarojs/components";
import Layout from "@components/Layout";
import { message } from "@components/MessageApi/MessageApiSingleton";
import { login } from "@utils/login";

const App = () => {
    const [xm, setXm] = useState("");
    const [sfzmhm, setSfzmhm] = useState("");
    const [buttonDisabled, setButtonDisabled] = useState(false);
    const [buttonText, setButtonText] = useState("立即登录");
    const [needReset, setNeedReset] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    const pdAgreemen = useRef<any>(null);
    const [hasShownAlert, setHasShownAlert] = useState(false);

    // 添加页面显示时的处理
    Taro.useDidHide(() => {
        setNeedReset(true);
    });

    useDidShow(() => {
        login().then(() => {
            checkLogOn();
        });
    });

    const checkLogOn = () => {
        if (needReset) {
            setButtonDisabled(false);
            setButtonText("立即登录");
            setNeedReset(false);
        }

        // 检查登录状态
        setIsLoading(true);
        // 尝试检查登录状态
        request
            .post<API.Result<any>>("/HNJiaXie/HnjxLogOn/checkLogOn", {})
            .then((json) => {
                if (json && json.success) {
                    if (json.data.sfzmhm == "43010319830814351X") {
                        message.openDialog("请在理论课堂学些的时候，进入课堂和出课堂一定要完成扫码的动作，不然系统无法累计相关的学时。", "获取信息失败", () => {
                            goPage();
                        });
                    } else {
                        goPage();
                    }
                } else {
                    // 登录状态无效，显示登录表单
                    setIsLoading(false);

                    // 只有在还没显示过的情况下才显示弹窗
                    if (!hasShownAlert) {
                        loginAlert();
                        setHasShownAlert(true);
                    }

                    // message.openDialog("无法登录", json.message, () => {});
                }
            })
            .catch(() => {
                // 出错时也显示登录表单
                setIsLoading(false);

                // 只有在还没显示过的情况下才显示弹窗
                if (!hasShownAlert) {
                    loginAlert();
                    setHasShownAlert(true);
                }
            });
    };

    const loginAlert = () => {
        console.log("loginAlert");
        message.openDialog(
            "请注意",
            "本小程序是湖南省机动车驾驶员培训协会内部的管理系统的一部分，只允许驾校在湖南省机动车驾驶员培训协会登记了的教练利用个人身份验证登录，如果没有在完成登记的人员，请退出当前程序?",
            () => {},
            "我已经知道了",
            "退出当前程序",
            () => {
                Taro.exitMiniProgram();
            }
        );
    };

    /**
     * 根据当前的状态 跳转至相应的页面
     */
    const goPage = () => {
        request.post<API.Result<any>>("/HNJiaXie/HnjxLogOn/goPage", {}).then((json) => {
            if (json && json.success) {
                if (json.data.PagePath == "/business/hnjpxh/zxjy/my/index" || json.data.PagePath == "/hnjpxh/my/index") {
                    Taro.navigateTo({
                        url: "/subpackages/hnjpxh/my/index",
                    });
                } else if (json.data.PagePath == "/business/hnjpxh/zxjy/pay/index" || json.data.PagePath == "/hnjpxh/pay/index") {
                    Taro.navigateTo({
                        url: "/subpackages/hnjpxh/pay/index",
                    });
                } else {
                    Taro.navigateTo({
                        url: json.data.PagePath,
                    });
                }
            } else {
                json && message.openDialog("获取信息失败", json.message, () => {});
            }
        });
    };

    /**
     * 验证登录
     */
    const logOn = () => {
        if (!pdAgreemen.current.state.agreeChecked) {
            message.openDialog("协议确认", "请先阅读并确认同意《用户服务协议与隐私政策》", () => {
                pdAgreemen.current.showAgreement();
            });
        } else if (xm.length < 2) {
            message.error("请完整输入您的姓名!", "输入错误", () => {});
        } else if (sfzmhm.length < 6) {
            message.error("请输入正确的证件号码!", "输入错误", () => {});
        } else {
            setButtonDisabled(true);
            setButtonText("正在验证中");
            request
                .put<API.Result<any>>("/HNJiaXie/HnjxLogOn/logOn", {
                    xm: xm,
                    sfzmhm: sfzmhm,
                })
                .then((json) => {
                    if (json && json.success) {
                        setButtonText("正在跳转");
                        setTimeout(() => {
                            setButtonDisabled(false);
                            setButtonText("立即登录");
                        }, 3000);
                        Taro.setStorageSync("accessToken", json.data.accessToken);
                        goPage();
                    } else {
                        setButtonDisabled(false);
                        setButtonText("立即登录");
                        json && message.openDialog("获取信息失败", json.message, () => {});
                    }
                })
                .catch(() => {
                    setTimeout(() => {
                        setButtonDisabled(false);
                        setButtonText("立即登录");
                    }, 3000);
                });
        }
    };

    return (
        <Layout>
            <view className="flex-col page">
                <view className="flex-col flex-1 group">
                    <view className="flex-col justify-start items-center self-start relative section">
                        <view className="title-container">
                            <text className="main-title">湖南驾协</text>
                            <text className="sub-title">教练管理系统</text>
                        </view>
                    </view>
                    {isLoading ? (
                        <view className="flex-col justify-center items-center self-stretch relative group_3" style={{ height: "50vh" }}>
                            <view className="loading-container" style={{ textAlign: "center" }}>
                                <view style={{ marginBottom: "20rpx" }}>
                                    <view className="loading-spinner"></view>
                                </view>
                                <text style={{ color: "#666", fontSize: "28rpx" }}>正在获取登录状态...</text>
                            </view>
                        </view>
                    ) : (
                        <view className="flex-col justify-start self-stretch relative group_3">
                            <view className="section_2">
                                <view className="decoration" />
                                <view className="content-wrapper">
                                    <view className="flex-col group_4">
                                        <view className="flex-row justify-between self-stretch group_5">
                                            <text className="font_2 text_2">教练登录</text>
                                        </view>
                                        <view className="self-start section_3"></view>
                                    </view>
                                    <view className="flex-col group_6">
                                        <view
                                            className="flex-row justify-start items-start text-wrapper"
                                            style={{
                                                paddingLeft: "30rpx",
                                                display: "flex",
                                                border: "0",
                                            }}
                                        >
                                            <IconFont name="Profile" color="#999" size={38} />
                                            <Input
                                                maxlength={20}
                                                className="font_3 text_4"
                                                placeholder="教练的名字"
                                                style={{
                                                    marginLeft: "20rpx",
                                                }}
                                                onInput={(e) => {
                                                    setXm(e.detail.value);
                                                }}
                                                defaultValue={xm}
                                            ></Input>
                                        </view>
                                        <view
                                            className="mt-24 flex-row justify-start items-start text-wrapper"
                                            style={{
                                                paddingLeft: "30rpx",
                                                display: "flex",
                                                border: "0",
                                            }}
                                        >
                                            <IconFont name="Wallet" color="#999" size={38} />
                                            <Input
                                                type="idcard"
                                                maxlength={18}
                                                className="font_3 text_4"
                                                placeholder="教练的证件号码"
                                                style={{
                                                    marginLeft: "20rpx",
                                                }}
                                                onInput={(e) => {
                                                    setSfzmhm(e.detail.value);
                                                }}
                                                defaultValue={sfzmhm}
                                            ></Input>
                                        </view>
                                    </view>

                                    <Button
                                        className="font_3 text_7 text-wrapper_3"
                                        style={{
                                            border: 0,
                                            width: "100%",
                                        }}
                                        onClick={logOn}
                                        disabled={buttonDisabled}
                                    >
                                        {buttonText}
                                    </Button>
                                    {/* <view className="flex-col justify-start items-center text-wrapper_3"><text className="font_3 text_7">登录</text></view> */}
                                    <view className="group_7">
                                        <view className="group_9">
                                            <PdAgreement ref={pdAgreemen}></PdAgreement>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    )}
                    <view className="bottom-text-container">
                        <view className="flex-row justify-evenly items-center">
                            <view className="section_4_bottom"></view>
                            <text className="font_4 text_13_bottom">湖南省机动车驾驶员培训协会</text>
                            <view className="section_5_bottom"></view>
                        </view>
                    </view>
                </view>
            </view>
            <view className="safe__area"></view>
        </Layout>
    );
};
export default App;
