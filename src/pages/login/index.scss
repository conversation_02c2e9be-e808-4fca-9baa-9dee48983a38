// /* Base Styles */
// page {
//     width: 100%;
//     height: 100%;
//     font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
// }

/* 基础设置 */
page {
    width: 100%;
    height: 100%;
    font-family: BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Microsoft Yahei", sans-serif;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #fff;
}

/* Reset */
view,
image,
text {
    box-sizing: border-box;
    flex-shrink: 0;
}

/* Page Container */
.page {
    background-color: #ffffff;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    flex: none;
}

/* Main Container */
.main-container {
    overflow-y: auto;
    flex: none;
}

/* Header Section */
.header-section {
    padding: 200rpx 0 240rpx;
    background: linear-gradient(135deg, #3264ed 0%, #1e40af 100%);
    width: 100%;
    position: relative;
    overflow: hidden;

    &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        background: linear-gradient(to bottom right, transparent 49%, #ffffff 50%);
    }

    /* 主装饰元素 */
    .header-decoration {
        position: absolute;
        top: 0;
        left: 0;
        width: 320rpx;
        height: 320rpx;
        opacity: 0.1;
        overflow: hidden;

        &::before {
            content: "";
            position: absolute;
            top: -160rpx;
            left: -160rpx;
            width: 320rpx;
            height: 320rpx;
            border: 60rpx solid rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            transform: rotate(-45deg);
        }

        &::after {
            content: "";
            position: absolute;
            top: -80rpx;
            left: -80rpx;
            width: 240rpx;
            height: 240rpx;
            border: 40rpx solid rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            transform: rotate(-45deg);
        }
    }

    /* Logo文字 */
    .header-title {
        position: absolute;
        top: 60rpx;
        left: 40rpx;
        color: rgba(255, 255, 255, 0.9);
        font-size: 36rpx;
        font-weight: 600;
        z-index: 1;
        letter-spacing: 2rpx;

        &::after {
            content: "";
            position: absolute;
            bottom: -16rpx;
            left: 0;
            width: 40rpx;
            height: 4rpx;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 2rpx;
        }
    }
}

/* Login Card Styles */
.login-wrapper {
    margin-top: -164rpx;
    position: relative;
    z-index: 1;
}

.login-card,
.login-card-student {
    margin: 0 42rpx;
    padding: 40rpx 30rpx 150rpx;
    background: #ffffff;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08), 0 -1px 0 0 rgba(0, 0, 0, 0.05);
}

.login-card-student {
    border-top: 4rpx solid #3264ed;
}

/* Tab Styles */
.tab-container {
    padding: 0 68rpx;

    .tab-header {
        padding-bottom: 20rpx;
    }

    .tab-text {
        font-size: 32rpx;
        line-height: 33.2rpx;
        transition: color 0.3s ease;
    }
}

.tab-active,
.tab-active-student {
    color: #3264ed;
    font-weight: 500;
}

.tab-inactive,
.tab-inactive-student {
    color: #9e9e9e;
}

.tab-indicator,
.tab-indicator-student {
    background-color: #3264ed;
    border-radius: 228rpx;
    width: 58rpx;
    height: 4rpx;
    transition: all 0.3s ease;
}

.tab-indicator {
    margin-left: 40rpx;
}

.tab-indicator-student {
    margin-right: 48rpx;
}

/* Form Styles */
.form-container {
    margin-top: 60rpx;
}

/* 图标样式 */
.iconFont {
    width: 40rpx;
    height: 40rpx;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
}

/* Login Button */
.login-button {
    margin: 48rpx 18rpx 0;
    padding: 32rpx 0;
    background-color: #3264ed;
    border-radius: 36rpx;
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 500;
    border: none;
    width: calc(100% - 36rpx);
    transition: background-color 0.3s ease;

    &:active {
        background-color: #2851cb;
    }

    &.ghost {
        background-color: transparent;
        border: 2rpx solid #3264ed;
        color: #3264ed;

        &:active {
            background-color: rgba(50, 100, 237, 0.1);
        }
    }
}

/* Agreement Section */
.agreement-container {
    margin-top: 60rpx;
    display: flex;
    justify-content: center;
}

.agreement-wrapper {
    line-height: 22rpx;
}

/* Footer */
.footer {
    position: fixed;
    bottom: 100rpx;
    width: 100%;
    padding: 40rpx 0;
    background-color: transparent;
    flex: none;

    .footer-text {
        color: #9e9e9e;
        font-size: 24rpx;
    }

    .footer-divider-left,
    .footer-divider-right {
        width: 116rpx;
        height: 1rpx;
    }

    .footer-divider-left {
        background: linear-gradient(90deg, transparent, #e2e8f0);
    }

    .footer-divider-right {
        background: linear-gradient(90deg, #e2e8f0, transparent);
    }
}

/* NutUI Overrides */
.nut-picker {
    &-control {
        height: 80rpx;
    }

    &-roller-item-title {
        font-size: 30rpx;
    }

    &-view-panel {
        height: 40vh;
    }
}

.nut-popup {
    &-bottom.nut-popup-round {
        border-radius: 24rpx 24rpx 0 0;
    }
}

/* Utility Classes */
.mt-24 {
    margin-top: 24rpx;
}
.mt-10 {
    margin-top: 10rpx;
}
.flex-col {
    display: flex;
    flex-direction: column;
}
.flex-row {
    display: flex;
    flex-direction: row;
}
.justify-between {
    justify-content: space-between;
}
.justify-evenly {
    justify-content: space-evenly;
}
.items-center {
    align-items: center;
}
.items-start {
    align-items: flex-start;
}
.justify-start {
    justify-content: flex-start;
}
.self-start {
    align-self: flex-start;
}
.self-end {
    align-self: flex-end;
}
.self-stretch {
    align-self: stretch;
}

/* Safe Area */
.safe__area {
    flex: 1;
    width: 100%;
    background-color: #fff;
    min-height: 100rpx;
}

/* 输入框基础样式 */
.input-wrapper {
    height: 92rpx;
    background-color: #f8fafc;
    border-radius: 16rpx;
    border: 2rpx solid #f0f0f0;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    position: relative;
}

/* 图标样式 */
.iconFont {
    width: 40rpx;
    height: 40rpx;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
}

/* 输入框文本样式 */
.input-text {
    flex: 1;
    height: 48rpx;
    font-size: 28rpx;
    line-height: 92rpx;
    font-size: 28rpx;
    margin-left: 20rpx;
    color: #333;
    background: transparent;
    border: none;

    &::placeholder {
        color: #999;
    }
}

/* 验证码输入框特殊样式 */
.code-container {
    height: 92rpx;
    background-color: #f8fafc;
    border-radius: 16rpx;
    border: 2rpx solid #f0f0f0;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    padding-right: 10rpx;

    .input-wrapper {
        flex: 1;
        margin: 0;
        padding: 0;
        border: none;
        background: transparent;
    }

    .code-button {
        min-width: 200rpx;
        height: 72rpx;
        line-height: 72rpx;
        text-align: center;
        border-radius: 12rpx;
        background: rgba(50, 100, 237, 0.1);
        color: #3264ed;
        font-size: 28rpx;
        margin-left: 20rpx;
        flex-shrink: 0;
        border: none;
        padding: 0 20rpx;

        &:active {
            background: rgba(50, 100, 237, 0.2);
        }
    }
}

.login-tip {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 32rpx;
    padding: 0 48rpx;
}

.user-list-popup {
    .popup-title {
        font-size: 32rpx;
        color: #333;
        text-align: center;
        padding: 0rpx 0 30rpx;
        font-weight: 500;
    }

    .nut-cell {
        margin-bottom: 20rpx;
        border-radius: 12rpx;
        background-color: #f8f8f8;

        &:active {
            background-color: #f0f0f0;
        }
    }
}

.loading-icon {
    display: inline-block;
    width: 28rpx;
    height: 28rpx;
    margin-left: 16rpx;
    vertical-align: middle;
    animation: rotate 1s linear infinite;
    border: 3rpx solid rgba(255, 255, 255, 0.3);
    border-top-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
