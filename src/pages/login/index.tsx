import { useRef, useState, useEffect } from "react";
import "./index.scss";
import "@utils/app.scss";
import { Button } from "@nutui/nutui-react-taro";
import request from "@service/request";
import { tool } from "@utils/tool";
import Taro, { useReady } from "@tarojs/taro";
import IconFont from "@components/iconfont";
import Layout from "@components/Layout";
import { message } from "@components/MessageApi/MessageApiSingleton";
import { login } from "@utils/login";
import PdAgreement from "@components/pdAgreemen";
import UserListPopup from "@components/UserListPopup";
import { LOGIN_LOADING_STATUS } from "@utils/login";

interface LoginFormData {
    phone: string;
    phoneCode: string;
    name: string;
    idCard: string;
}

const Login = () => {
    const [tabIndex, setTabIndex] = useState<string | number>(1);
    const [showLoginButtons, setShowLoginButtons] = useState(false);
    const [showPhoneLogin, setShowPhoneLogin] = useState(false);
    const [bindedPhone, setBindedPhone] = useState("");
    const [formData, setFormData] = useState<LoginFormData>({
        phone: "",
        phoneCode: "",
        name: "",
        idCard: "",
    });

    const [userId, setUserId] = useState<string | undefined>(undefined);
    const [showUserListPopup, setShowUserListPopup] = useState(false);
    const [userList, setUserList] = useState<
        Array<{
            UserId: string;
            UserName: string;
            Phone: string;
            TenantName: string;
            Account: string;
        }>
    >([]);
    const pdAgreemen = useRef<any>();
    const [isLoading, setIsLoading] = useState(false);

    // Add loading state at the top with other state declarations
    const [directLoginLoading, setDirectLoginLoading] = useState(false);
    const [useCurrentPhoneLoading, setUseCurrentPhoneLoading] = useState(false);

    // 添加一个状态来标识当前是哪种场景
    const [userListMode, setUserListMode] = useState<"sendCode" | "directLogin">("sendCode");

    const handleInputChange = (field: keyof LoginFormData, value: string) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
    };

    // Update the handleDirectLogin function
    const handleDirectLogin = () => {
        if (!pdAgreemen.current?.state?.agreeChecked) {
            pdAgreemen.current.setState({ showDetail: true });
            return;
        }
        setDirectLoginLoading(true);
        // 直接跳转，不需要选择账号
        Taro.navigateTo({
            url: "/subpackages/student/user/index/index",
        }).finally(() => {
            setDirectLoginLoading(false);
        });
    };

    const sendCode = (userId: string) => {
        if (!userId) {
            setUserId(undefined);
        } else {
            setUserId(userId);
        }

        message.openLoading("正在发送验证码");
        request
            .put<API.Result<{ accessToken: string }>>("/Wx/Login/sendVerifyCode", {
                Phone: formData.phone,
                ValidCode: formData.phoneCode,
                UserId: userId,
            })
            .then((json) => {
                message.closeLoading();
                if (json?.success) {
                    message.openToast(json.message);
                    // 不需要设置 accessToken，因为这只是发送验证码
                    handleInputChange("phoneCode", "");
                    // 关闭用户列表弹窗，显示验证码输入界面
                    setShowUserListPopup(false);
                    setShowLoginButtons(false);
                    setShowPhoneLogin(false);
                } else {
                    message.openDialog("发送失败", json.message);
                }
            })
            .catch(console.error);
    };

    const handleStaffLogin = () => {
        if (formData.phoneCode.length !== 4) {
            return message.openToast("请输入正确的验证码");
        }
        if (formData.phone.length !== 11) {
            return message.openToast("请输入正确的手机号码");
        }

        message.openLoading("正在验证验证码");
        request
            .put<API.Result<{ accessToken: string }>>("/Wx/Login/loginByPhoneCode", {
                Phone: formData.phone,
                ValidCode: formData.phoneCode,
                UserId: userId, // 使用之前选择的 userId
            })
            .then((json) => {
                message.closeLoading();
                if (json?.success) {
                    tool.data.set("accessToken", json.data.accessToken);
                    handleStaffLoginSuccess();
                } else {
                    message.openDialog("验证失败", json.message);
                }
            })
            .catch(console.error);
    };

    const handleStudentLogin = () => {
        if (!formData.name || !formData.idCard) {
            return message.openToast("请输入姓名和身份证号码");
        }

        message.openLoading("正在验证身份信息");
        request
            .put<API.Result<{ accessToken: string }>>("/Wx/Login/loginByStudent", {
                Name: formData.name,
                IdCard: formData.idCard,
            })
            .then((json) => {
                if (json?.success) {
                    tool.data.set("accessToken", json.data.accessToken);
                    Taro.navigateTo({
                        url: "/subpackages/student/index/index",
                    });
                } else {
                    message.openDialog("验证失败", json.message);
                }
            })
            .catch(console.error);
    };

    const handleSendCode = () => {
        if (!pdAgreemen.current?.state?.agreeChecked) {
            pdAgreemen.current.setState({ showDetail: true });
            return;
        }

        if (formData.phone.length !== 11) {
            return message.openToast("请输入正确的手机号码");
        }

        message.openLoading("检查关联账号");
        request
            .post<API.Result<any[]>>("/Wx/Login/getUserList", {
                Phone: formData.phone,
            })
            .then((json) => {
                message.closeLoading();
                if (json?.success) {
                    if (json.data.length > 0) {
                        setUserListMode("sendCode"); // 设置为发送验证码模式
                        setUserList(json.data);
                        setShowUserListPopup(true);
                    } else {
                        message.openToast("未找到关联账号");
                    }
                } else {
                    message.openDialog("获取关联账号失败", json.message);
                }
            })
            .catch((error) => {
                message.closeLoading();
                console.error("获取关联账号失败:", error);
                message.openDialog("获取关联账号失败", "请稍后重试");
            });
    };

    const handleUseCurrentPhone = () => {
        // 先检查协议是否同意
        if (!pdAgreemen.current?.state.agreeChecked) {
            pdAgreemen.current?.showAgreement();
            return;
        }

        setUseCurrentPhoneLoading(true);
        handleLoginAfterAgreement();
    };

    const handleSelectUser = async (userId: string) => {
        if (userListMode === "sendCode") {
            // 发送验证码模式：只发送验证码
            sendCode(userId);
        } else {
            // 直接登录模式：尝试直接登录
            setShowUserListPopup(false);
            setUseCurrentPhoneLoading(true);
            try {
                message.openLoading("正在登录");
                const response = await request.put<API.Result<any>>("/Wx/Login/loginByPhone", {
                    userId: userId,
                    phone: formData.phone,
                });

                if (response?.success) {
                    message.openToast("登录成功");
                    setTimeout(() => {
                        handleStaffLoginSuccess();
                    }, 1500);
                } else {
                    message.closeLoading();
                    setUseCurrentPhoneLoading(false);
                    message.openToast("登录失败，请使用验证码登录");
                    setShowLoginButtons(false);
                    setShowPhoneLogin(false);
                    setFormData((prev) => ({ ...prev }));
                }
            } catch (error) {
                message.closeLoading();
                setUseCurrentPhoneLoading(false);
                console.error("登录失败:", error);
                message.openToast("登录失败，请使用验证码登录");
                setShowLoginButtons(false);
                setShowPhoneLogin(false);
                setFormData((prev) => ({ ...prev, phone: bindedPhone || "" }));
            }
        }
    };

    const loginAlert = () => {
        message.openDialog(
            "请注意",
            "本小程序是驾校内部的管理系统的一部分，只允许驾校内部员工和教练和已经完成登记的学员利用个人身份验证登录，如果没有在驾校完成登记的人员，请退出当前程序?",
            () => {},
            "我已经知道了",
            "退出当前程序",
            () => {
                Taro.exitMiniProgram();
            }
        );
    };

    // 处理员工登录成功后的跳转
    const handleStaffLoginSuccess = () => {
        Taro.navigateTo({
            url: "/subpackages/student/user/index/index",
        });
    };

    const handleLoginAfterAgreement = () => {
        message.openLoading("检查关联账号");
        request
            .post<API.Result<any[]>>("/Wx/Login/getUserList", {
                Phone: formData.phone,
            })
            .then((json) => {
                message.closeLoading();
                if (json?.success) {
                    if (json.data.length > 0) {
                        setUserListMode("directLogin"); // 设置为直接登录模式
                        setUserList(json.data);
                        setShowUserListPopup(true);
                    } else {
                        setUseCurrentPhoneLoading(false);
                        message.openToast("未找到关联账号");
                    }
                } else {
                    setUseCurrentPhoneLoading(false);
                    message.openDialog("获取关联账号失败", json.message);
                }
            })
            .catch((error) => {
                message.closeLoading();
                setUseCurrentPhoneLoading(false);
                console.error("获取关联账号失败:", error);
                message.openDialog("获取关联账号失败", "请稍后重试");
            });
    };

    // 修改 UserListPopup 的 onClose 处理函数
    const handleUserListClose = () => {
        setShowUserListPopup(false);
        if (useCurrentPhoneLoading) {
            setUseCurrentPhoneLoading(false);
        }
    };

    // 获取用户信息
    useReady(async () => {
        try {
            const userInfo = await login();
            if (!userInfo) {
                // 如果没有用户信息，检查是否有绑定的手机号
                const response = await request.post<API.Result<string[]>>("/Wx/Login/phones");
                if (response?.success && Array.isArray(response.data) && response.data.length > 0) {
                    const firstPhone = response.data[0];
                    setBindedPhone(firstPhone);
                    setFormData((prev) => ({ ...prev, phone: firstPhone }));
                    setShowPhoneLogin(true);
                } else {
                    // 既没有用户信息也没有绑定手机号，显示登录提示
                    loginAlert();
                }
                return;
            }
            if (userInfo.UserId) {
                setShowLoginButtons(true);
            }

            // 同时获取手机号信息
            const response = await request.post<API.Result<string[]>>("/Wx/Login/phones");
            if (response?.success && Array.isArray(response.data) && response.data.length > 0) {
                const firstPhone = response.data[0];
                setBindedPhone(firstPhone);
                setFormData((prev) => ({ ...prev, phone: firstPhone }));
                setShowPhoneLogin(true);
            }
        } catch (error) {
            console.error("获取用户信息失败:", error);
        }
    });

    useEffect(() => {
        const startLoadingHandler = () => setIsLoading(true);
        const endLoadingHandler = () => setIsLoading(false);

        Taro.eventCenter.on(LOGIN_LOADING_STATUS.START, startLoadingHandler);
        Taro.eventCenter.on(LOGIN_LOADING_STATUS.END, endLoadingHandler);

        return () => {
            Taro.eventCenter.off(LOGIN_LOADING_STATUS.START, startLoadingHandler);
            Taro.eventCenter.off(LOGIN_LOADING_STATUS.END, endLoadingHandler);
        };
    }, []);

    const renderStaffLogin = () => (
        <view className="flex-col form-container">
            {showLoginButtons ? (
                <view className="flex-col justify-center items-center">
                    <text className="login-tip">您已经登录了一个账号，是否要直接登录操作？</text>
                    <Button
                        className="login-button"
                        loading={directLoginLoading}
                        onClick={handleDirectLogin}
                        style={{
                            border: directLoginLoading ? "0.5rpx solid #1890ff" : "none",
                            backgroundColor: directLoginLoading ? "#f0f5ff" : "",
                            position: "relative",
                        }}
                    >
                        直接登录
                    </Button>
                    <Button
                        className="login-button ghost"
                        onClick={() => {
                            setShowLoginButtons(false);
                            if (bindedPhone) {
                                setShowPhoneLogin(true);
                            }
                        }}
                    >
                        其他账号
                    </Button>
                </view>
            ) : showPhoneLogin ? (
                <view className="flex-col justify-center items-center">
                    <text className="login-tip">检测到您绑定了手机号 {bindedPhone}，是否使用该手机号登录？</text>
                    <Button
                        className="login-button"
                        onClick={handleUseCurrentPhone}
                        loading={useCurrentPhoneLoading}
                        style={{
                            border: useCurrentPhoneLoading ? "0.5rpx solid #1890ff" : "none",
                            backgroundColor: useCurrentPhoneLoading ? "#f0f5ff" : "",
                            position: "relative",
                        }}
                    >
                        使用该手机号登录
                    </Button>
                    <Button
                        className="login-button ghost"
                        onClick={() => {
                            setShowPhoneLogin(false);
                            setFormData((prev) => ({ ...prev, phone: "" }));
                        }}
                    >
                        使用其他手机号
                    </Button>
                </view>
            ) : (
                <>
                    <view className="flex-row justify-start items-start input-wrapper">
                        <IconFont name="cellphone-iphone" color="#999" size={38} />
                        <input
                            type="number"
                            maxLength={11}
                            className="input-text input-default"
                            placeholder="注册的手机号码"
                            value={formData.phone}
                            onChange={(e: any) => handleInputChange("phone", e.detail.value)}
                        />
                    </view>

                    <view className="mt-24 flex-row justify-between items-center code-container input-wrapper">
                        <IconFont name="comment-text-outline" color="#999" size={38} />
                        <input
                            type="number"
                            maxLength={4}
                            className="input-text input-default"
                            placeholder="验证码"
                            value={formData.phoneCode}
                            onChange={(e: any) => handleInputChange("phoneCode", e.detail.value)}
                        />
                        <Button className="code-button" onClick={handleSendCode}>
                            发送验证码
                        </Button>
                    </view>
                    <Button
                        className="login-button"
                        style={{
                            border: 0,
                            marginTop: "40rpx",
                            borderRadius: "36px",
                        }}
                        onClick={tabIndex == 1 ? handleStaffLogin : handleStudentLogin}
                    >
                        立即登录
                    </Button>
                </>
            )}
        </view>
    );

    const renderStudentLogin = () => (
        <>
            <view className="flex-col form-container">
                <view className="flex-row justify-start items-start input-wrapper">
                    <IconFont name="comment-account-outline" color="#999" size={38} />
                    <input
                        maxLength={20}
                        className="input-text input-default"
                        placeholder="用户的姓名"
                        value={formData.name}
                        onChange={(e: any) => handleInputChange("name", e.detail.value)}
                    />
                </view>

                <view className="mt-24 flex-row justify-start items-start input-wrapper">
                    <IconFont name="comment-check-outline" color="#999" size={38} />
                    <input
                        maxLength={18}
                        className="input-text input-default"
                        placeholder="身份证号码"
                        value={formData.idCard}
                        onChange={(e: any) => handleInputChange("idCard", e.detail.value)}
                    />
                </view>
            </view>
            <Button
                className="login-button"
                style={{
                    border: 0,
                    marginTop: "40rpx",
                    borderRadius: "36px",
                }}
                onClick={tabIndex == 1 ? handleStaffLogin : handleStudentLogin}
            >
                立即登录
            </Button>
        </>
    );

    return (
        <Layout>
            <view className="flex-col page">
                <view className="flex-col flex-1 main-container">
                    <view className="flex-col justify-start items-center self-start relative header-section">
                        <view className="header-decoration"></view>
                        <view className="header-title" style={{ marginTop: "40rpx" }}>
                            全能考场
                            {isLoading && <view className="loading-icon"></view>}
                        </view>
                    </view>

                    <view className="flex-col justify-start self-stretch relative login-wrapper">
                        <view className={`${tabIndex == 1 ? "flex-col login-card" : "flex-col login-card-student"}`}>
                            <view className="flex-col tab-container">
                                <view className="flex-row justify-between self-stretch tab-header">
                                    <text className={`tab-text ${tabIndex == 1 ? "tab-active" : "tab-inactive"}`} onClick={() => setTabIndex(1)}>
                                        员工登录
                                    </text>
                                    <text className={`tab-text ${tabIndex == 2 ? "tab-active-student" : "tab-inactive-student"}`} onClick={() => setTabIndex(2)}>
                                        学员登录
                                    </text>
                                </view>
                                <view className={`${tabIndex == 1 ? "self-start tab-indicator" : "self-end tab-indicator-student"}`} />
                            </view>

                            {tabIndex == 1 ? renderStaffLogin() : renderStudentLogin()}

                            <view className="flex-row agreement-container">
                                <view className="agreement-wrapper">
                                    <PdAgreement
                                        ref={pdAgreemen}
                                        onConfirm={() => {
                                            // 如果是直接登录状态，确认协议后直接跳转
                                            if (showLoginButtons) {
                                                handleDirectLogin();
                                            } else {
                                                // 其他情况保持原有逻辑
                                                handleLoginAfterAgreement();
                                            }
                                        }}
                                    />
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <view className="flex-col footer">
                    <view className="flex-row justify-evenly items-center">
                        <view className="footer-divider-left" />
                        <text className="footer-text">盼达软件</text>
                        <view className="footer-divider-right" />
                    </view>
                </view>

                <view className="safe__area" />

                <UserListPopup visible={showUserListPopup} userList={userList} onClose={handleUserListClose} onSelect={handleSelectUser} />
            </view>
        </Layout>
    );
};

export default Login;
