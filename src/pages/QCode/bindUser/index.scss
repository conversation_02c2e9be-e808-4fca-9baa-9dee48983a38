page {
    --primary-color: #2b5de0;
    --primary-light: #eef3ff;
    --text-primary: #1c1d21;
    --text-secondary: #646a73;
    --text-tertiary: #9096a2;
    --border-color: #e5e8ed;
    --background: #f5f7fa;
    --white: #ffffff;

    background: var(--background);
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
}

.page {
    min-height: 100vh;
    padding-bottom: 240rpx;
}

.page-header {
    background: var(--white);
    padding: 32rpx 26rpx;
    border-bottom: 1px solid var(--border-color);
}

.page-title {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-primary);
}

.section {
    padding: 26rpx;
}

.card {
    background: var(--white);
    border-radius: 12rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.card-header {
    padding: 26rpx;
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 26rpx;
    font-weight: 500;
    color: var(--text-primary);
}

.info-group {
    padding: 6rpx 0;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 22rpx 26rpx;

    &:not(:last-child) {
        border-bottom: 1px solid var(--border-color);
    }
}

.info-label {
    font-size: 26rpx;
    color: var(--text-secondary);
}

.info-value {
    font-size: 26rpx;
    color: var(--text-primary);
    font-weight: 500;

    &.primary {
        color: var(--primary-color);
    }
}

.divider {
    height: 12rpx;
    background: var(--background);
}

.fixed-bottom {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--white);
    padding: 22rpx 26rpx calc(22rpx + env(safe-area-inset-bottom));
    box-shadow: 0 -2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.notice {
    text-align: center;
    margin-bottom: 22rpx;
}

.notice-text {
    font-size: 18rpx;
    color: var(--text-tertiary);
}

.primary-button {
    width: 100% !important;
    height: 78rpx !important;
    font-size: 30rpx !important;
    font-weight: 500 !important;
    color: var(--white) !important;
    background: var(--primary-color) !important;
    border: none !important;
    border-radius: 8rpx !important;

    &:active {
        opacity: 0.9;
    }
}

// 键盘弹出时的适配
@media (max-height: 600px) {
    .fixed-bottom {
        position: relative;
        margin-top: 42rpx;
    }

    .page {
        padding-bottom: 26rpx;
    }
}
