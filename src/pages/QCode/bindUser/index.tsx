// index.tsx
import { Current, useDidShow, useLaunch } from "@tarojs/taro";
import { useState, useCallback } from "react";
import { View, Text } from "@tarojs/components";
import { Button } from "@nutui/nutui-react-taro";
import request from "@service/request";
import Taro from "@tarojs/taro";
import { tool } from "@utils/tool";
import { base64ToGuid } from "@utils/util";
import { message } from "@components/MessageApi/MessageApiSingleton";
import "./index.scss";
import Layout from "@components/Layout";
import { login } from "@utils/login";

const UserBindPage = () => {
    const [user, setUser] = useState<any>({});

    const getUserInfo = useCallback(async (scene: string) => {
        if (!scene) return;
        try {
            const id = base64ToGuid(scene);
            if (!id) return;

            message.openLoading("加载中");
            const response = await request.post<any>("/Auth/WxBindUser/getUserInfo", { Id: id });
            message.closeLoading();

            if (response?.success) {
                setUser(response.data);
            } else {
                message.openAlert("获取失败", "获取用户信息失败，请重新扫码");
            }
        } catch (error) {
            console.error(error);
            message.openDialog("参数错误", error.message);
        }
    }, []);

    const handleBindUser = useCallback(() => {
        console.log("handleBindUser");
        message.openDialog(
            "确认绑定",
            "是否确认绑定当前微信账号？绑定后可直接使用微信登录。",
            () => {
                message.openLoading("处理中");
                request.post<any>("/Auth/WxBindUser/bindUserInfo", { Id: user.Id }).then((response) => {
                    message.closeLoading();
                    if (response?.success) {
                        tool.data.clear();
                        message.success(response.message, "绑定成功", () => {
                            Taro.navigateTo({ url: "/subpackages/student/user/index/index" });
                        });
                    } else {
                        message.openAlert("绑定失败", response.message);
                    }
                });
            },
            "确认",
            "取消"
        );
    }, [user]);

    useLaunch(() => {
        getUserInfo(Current.router?.params?.scene ?? "");
        login();
    });
    useDidShow(() => {
        getUserInfo(Current.router?.params?.scene ?? "");
        login();
    });

    const maskIdCard = (idCard: string) => {
        if (!idCard) return "未设置";
        return idCard.length === 18 ? `${idCard.slice(0, 4)}****${idCard.slice(-4)}` : idCard;
    };

    return (
        <Layout>
            <View className="page">
                {/* <View className="page-header">
                    <Text className="page-title">账号绑定</Text>
                </View> */}

                <View className="section">
                    <View className="card">
                        <View className="card-header">
                            <Text className="card-title">账号信息</Text>
                        </View>

                        <View className="info-group">
                            <View className="info-item">
                                <Text className="info-label">账号</Text>
                                <Text className="info-value">{user.Account || "未设置"}</Text>
                            </View>
                            <View className="info-item">
                                <Text className="info-label">系统编号</Text>
                                <Text className="info-value">{user.SysId || "未设置"}</Text>
                            </View>
                        </View>

                        <View className="divider" />

                        <View className="info-group">
                            <View className="info-item">
                                <Text className="info-label">姓名</Text>
                                <Text className="info-value primary">{user.RealName || "未设置"}</Text>
                            </View>
                            <View className="info-item">
                                <Text className="info-label">手机号码</Text>
                                <Text className="info-value">{user.Phone || "未设置"}</Text>
                            </View>
                            <View className="info-item">
                                <Text className="info-label">证件号码</Text>
                                <Text className="info-value">{maskIdCard(user.IdCard)}</Text>
                            </View>
                        </View>
                    </View>
                </View>

                <View className="fixed-bottom">
                    <View className="notice">
                        <Text className="notice-text">绑定后可使用微信扫码快速登录系统</Text>
                    </View>
                    <Button className="primary-button" onClick={handleBindUser}>
                        确认绑定
                    </Button>
                </View>
            </View>
        </Layout>
    );
};

export default UserBindPage;
