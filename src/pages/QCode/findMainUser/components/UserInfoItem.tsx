import React from "react";
import { View, Text } from "@tarojs/components";

interface UserInfoItemProps {
    label: string;
    value: string;
    isLast?: boolean;
}

const UserInfoItem: React.FC<UserInfoItemProps> = ({ label, value, isLast }) => (
    <View className={`info-item ${!isLast ? "info-item--border" : ""}`}>
        <Text className="info-item__label">{label}</Text>
        <Text className="info-item__value">{value}</Text>
    </View>
);

export default UserInfoItem;
