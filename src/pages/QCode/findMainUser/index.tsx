// index.tsx
import { Current, useDidShow, useLaunch, useReady } from "@tarojs/taro";
import React, { useState, useCallback } from "react";
import { View, Text } from "@tarojs/components";
import { Button, Input } from "@nutui/nutui-react-taro";
import request from "@service/request";
import Taro from "@tarojs/taro";
import { tool } from "@utils/tool";
import { message } from "@components/MessageApi/MessageApiSingleton";
import "./index.scss";
import Layout from "@components/Layout";
import { login } from "@utils/login";

const UserBindPage = () => {
    // 表单数据状态
    const [formData, setFormData] = useState({
        realName: "",
        phone: "",
        idCard: "",
    });

    const [userInfo, setUserInfo] = useState<API.UserInfo>();

    // 获取用户信息
    useReady(async () => {
        try {
            const userInfo = await login();
            setUserInfo(userInfo);
        } catch {}
    });

    // 处理输入变化
    const handleInputChange = (value: string, field: string) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    // 表单验证
    const validateForm = () => {
        if (!formData.realName) {
            message.error("请输入姓名");
            return false;
        }
        if (!formData.phone) {
            message.error("请输入手机号码");
            return false;
        }
        if (!formData.idCard) {
            message.error("请输入证件号码");
            return false;
        }
        // 可以添加更多验证逻辑，如手机号格式、身份证格式等
        return true;
    };

    const handleBindUser = useCallback(() => {
        if (!validateForm()) return;

        message.openDialog(
            "确认绑定",
            "是否确认绑定当前微信账号？绑定后领取红包将登记到该账户下。",
            () => {
                message.openLoading("处理中");
                request
                    .post<any>("/Auth/WxBindUser/findMainUserInfo", {
                        RealName: formData.realName,
                        Phone: formData.phone,
                        IdCard: formData.idCard,
                    })
                    .then((response) => {
                        message.closeLoading();
                        if (response?.success) {
                            tool.data.clear();
                            message.success(response.message, "绑定成功", () => {
                                Taro.navigateBack();
                            });
                        } else {
                            message.error(response.message, "绑定失败");
                        }
                    });
            },
            "确认",
            "取消"
        );
    }, [formData]);

    return (
        <Layout>
            <View className="page">
                <View className="section">
                    <View className="card">
                        <View className="card-header">
                            <Text className="card-title">注册信息</Text>
                        </View>
                        <View className="info-group">
                            <View className="info-item">
                                <Text className="info-label">真实姓名</Text>
                                <Input className="info-input" value={formData.realName} placeholder="请输入真实姓名" onChange={(value) => handleInputChange(value, "realName")} />
                            </View>
                            <View className="info-item">
                                <Text className="info-label">手机号码</Text>
                                <Input
                                    className="info-input"
                                    value={formData.phone}
                                    placeholder="请输入手机号码"
                                    type="number"
                                    onChange={(value) => handleInputChange(value, "phone")}
                                />
                            </View>
                            <View className="info-item">
                                <Text className="info-label">证件号码</Text>
                                <Input
                                    className="info-input"
                                    type="idcard"
                                    value={formData.idCard}
                                    placeholder="请输入证件号码"
                                    onChange={(value) => handleInputChange(value, "idCard")}
                                />
                            </View>
                        </View>
                    </View>
                </View>

                <View className="fixed-bottom">
                    <View className="notice">
                        <Text className="notice-text">请输入注册时候登记的信息完整账户认证</Text>
                    </View>
                    <Button className="primary-button" onClick={handleBindUser}>
                        提交信息
                    </Button>
                </View>
            </View>
        </Layout>
    );
};

export default UserBindPage;
