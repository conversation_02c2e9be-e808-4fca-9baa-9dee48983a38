page {
    --primary-color: #2b5de0;
    --primary-light: #eef3ff;
    --text-primary: #1c1d21;
    --text-secondary: #646a73;
    --text-tertiary: #9096a2;
    --border-color: #e5e8ed;
    --background: #f5f7fa;
    --white: #ffffff;

    background: linear-gradient(135deg, #f6f9ff 0%, #ffffff 100%);
    position: relative;
    overflow: hidden;

    &::before {
        content: "";
        position: fixed;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(43, 93, 224, 0.03) 0%, transparent 60%);
        animation: rotate 30s linear infinite;
        z-index: 0;
    }

    &::after {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(43, 93, 224, 0.02) 0%, transparent 70%);
        z-index: 0;
    }

    font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
}

.page {
    min-height: 100vh;
    padding-bottom: 240rpx;
    position: relative;
    z-index: 1;
}

.page-header {
    background: var(--white);
    padding: 32rpx 26rpx;
    border-bottom: 1px solid var(--border-color);
}

.page-title {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-primary);
}

.section {
    padding: 26rpx;
}

.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(229, 232, 237, 0.6);
    box-shadow: 0 8rpx 32rpx rgba(43, 93, 224, 0.08);
    border-radius: 12rpx;
}

.card-header {
    padding: 26rpx;
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 26rpx;
    font-weight: 500;
    color: var(--text-primary);
}

.info-group {
    padding: 6rpx 0;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8rpx 26rpx;
    transition: background-color 0.3s ease;

    &:not(:last-child) {
        border-bottom: 1px solid var(--border-color);
    }

    &:hover {
        background-color: rgba(238, 243, 255, 0.5);
    }
}

.info-label {
    font-size: 26rpx;
    color: var(--text-secondary);
}

.info-value {
    font-size: 26rpx;
    color: var(--text-primary);
    font-weight: 500;

    &.primary {
        color: var(--primary-color);
    }
}

.divider {
    height: 12rpx;
    background: var(--background);
}

.fixed-bottom {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(229, 232, 237, 0.6);
    padding: 22rpx 26rpx calc(22rpx + env(safe-area-inset-bottom));
    box-shadow: 0 -2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.notice {
    text-align: center;
    margin-bottom: 22rpx;
}

.notice-text {
    font-size: 18rpx;
    color: var(--text-tertiary);
}

.primary-button {
    width: 100% !important;
    height: 78rpx !important;
    font-size: 30rpx !important;
    font-weight: 500 !important;
    color: var(--white) !important;
    background: linear-gradient(135deg, var(--primary-color) 0%, #4875e8 100%) !important;
    border: none !important;
    border-radius: 8rpx !important;
    box-shadow: 0 4rpx 12rpx rgba(43, 93, 224, 0.2) !important;
    transition: all 0.3s ease !important;

    &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 6rpx rgba(43, 93, 224, 0.1) !important;
    }
}

// 键盘弹出时的适配
@media (max-height: 600px) {
    .fixed-bottom {
        position: relative;
        margin-top: 42rpx;
    }

    .page {
        padding-bottom: 26rpx;
    }
}

// 添加新的动画和样式
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
