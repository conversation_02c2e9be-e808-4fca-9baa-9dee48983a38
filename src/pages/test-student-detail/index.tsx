import React, { useRef } from "react";
import { View, Button } from "@tarojs/components";
import StudentDetail, { StudentDetailRef } from "@/components/StudentDetail";
import "./index.scss";

const TestStudentDetail: React.FC = () => {
    const studentDetailRef = useRef<StudentDetailRef>(null);

    const handleOpenStudentDetail = () => {
        // 使用一个测试的学员ID
        studentDetailRef.current?.open("test-student-id");
    };

    return (
        <View className="test-page">
            <View className="test-content">
                <Button 
                    className="test-button" 
                    onClick={handleOpenStudentDetail}
                    type="primary"
                >
                    打开学员详情
                </Button>
            </View>
            
            <StudentDetail 
                ref={studentDetailRef}
                fullScreen={true}
            />
        </View>
    );
};

export default TestStudentDetail;
