@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(74, 108, 247, 0.4);
    }
    70% {
        box-shadow: 0 0 0 20rpx rgba(74, 108, 247, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(74, 108, 247, 0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
// @font-face {
//     font-family: "PandaFont";
//     src: url("https://cdn.51panda.com/fonts/FangZhengCuYuan.ttf");
//     font-weight: normal;
//     font-style: normal;
// }

.role-selection-container {
    font-family: Yuan<PERSON> SC, STYuanti-SC-Regular, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
        "Microsoft Yahei", sans-serif;

    // font-family: PandaFont;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7ff 0%, #ffffff 100%);
    padding: 150rpx 30rpx 40rpx 30rpx;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;

    &::before {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        width: 300rpx;
        height: 300rpx;
        background: radial-gradient(circle at center, rgba(74, 108, 247, 0.1) 0%, rgba(74, 108, 247, 0) 70%);
        border-radius: 50%;
        z-index: 0;
    }

    &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 250rpx;
        height: 250rpx;
        background: radial-gradient(circle at center, rgba(110, 63, 245, 0.1) 0%, rgba(110, 63, 245, 0) 70%);
        border-radius: 50%;
        z-index: 0;
    }

    .header {
        margin-top: 40rpx;
        margin-bottom: 40rpx;
        text-align: center;
        z-index: 1;
        animation: fadeIn 0.8s ease-out;

        .title {
            display: block;
            font-size: 48rpx;
            font-weight: 500;
            color: #333333;
            margin-bottom: 20rpx;
            letter-spacing: 2rpx;
        }

        .subtitle {
            display: block;
            font-size: 28rpx;
            color: #666666;
            font-weight: 400;
        }
    }

    .roles-container {
        display: flex;
        flex-direction: column;
        gap: 30rpx;
        padding: 20rpx 10rpx;
        z-index: 1;

        .role-card {
            display: flex;
            align-items: center;
            padding: 40rpx 30rpx;
            background: #ffffff;
            border-radius: 20rpx;
            box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.05);
            position: relative;
            transition: all 0.3s ease;
            animation: fadeIn 0.8s ease-out;
            overflow: hidden;

            &::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                width: 8rpx;
                height: 100%;
                background: linear-gradient(to bottom, #4a6cf7, #6e3ff5);
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            &:active {
                transform: scale(0.98);
            }

            &.selected {
                box-shadow: 0 15rpx 30rpx rgba(74, 108, 247, 0.15);

                &::before {
                    opacity: 1;
                }

                .selection-indicator {
                    opacity: 1;
                }

                .icon-circle {
                    animation: pulse 1.5s infinite;
                }
            }

            &.disabled {
                opacity: 0.7;
                cursor: not-allowed;
                pointer-events: none;
                background: #f5f5f5;

                .role-icon-container {
                    filter: grayscale(100%);
                }

                .role-info {
                    position: relative;
                    text-align: center;

                    .role-name,
                    .role-desc {
                        color: #999;
                    }
                }

                .coming-soon {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-size: 32rpx;
                    color: #999;
                    font-weight: 500;
                    background: rgba(255, 255, 255, 0.9);
                    padding: 10rpx 30rpx;
                    border-radius: 30rpx;
                    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
                    z-index: 2;
                }
            }

            .role-icon-container {
                margin-right: 30rpx;

                .icon-circle {
                    width: 120rpx;
                    height: 120rpx;
                    border-radius: 60rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.3s ease;

                    &::before {
                        content: "";
                        position: absolute;
                        width: 120rpx;
                        height: 120rpx;
                        border-radius: 60rpx;
                        z-index: -1;
                        transition: all 0.3s ease;
                    }
                }

                .role-icon {
                    width: 80rpx;
                    height: 80rpx;
                }
                &.student .icon-circle {
                    background-color: rgba(64, 169, 255, 0.1);

                    &::before {
                        background: linear-gradient(135deg, rgba(64, 169, 255, 0.2) 0%, rgba(64, 169, 255, 0.05) 100%);
                    }
                }

                &.vip .icon-circle {
                    background-color: rgba(147, 112, 219, 0.1);

                    &::before {
                        background: linear-gradient(135deg, rgba(147, 112, 219, 0.2) 0%, rgba(147, 112, 219, 0.05) 100%);
                    }
                }

                &.coach .icon-circle {
                    background-color: rgba(255, 107, 107, 0.1);

                    &::before {
                        background: linear-gradient(135deg, rgba(255, 107, 107, 0.2) 0%, rgba(255, 107, 107, 0.05) 100%);
                    }
                }

                &.order .icon-circle {
                    background-color: rgba(95, 184, 120, 0.1);

                    &::before {
                        background: linear-gradient(135deg, rgba(95, 184, 120, 0.2) 0%, rgba(95, 184, 120, 0.05) 100%);
                    }
                    // .role-icon {
                    //     width: 85rpx;
                    //     height: 85rpx;
                    // }
                }
            }

            .role-info {
                flex: 1;

                .role-name {
                    display: block;
                    font-size: 34rpx;
                    font-weight: 600;
                    color: #333333;
                    margin-bottom: 8rpx;
                }

                .role-desc {
                    display: block;
                    font-size: 24rpx;
                    color: #888888;
                }

                .coming-soon {
                    display: block;
                    font-size: 28rpx;
                    color: #ff6b6b;
                    margin-top: 8rpx;
                    font-weight: 500;
                }
            }

            .selection-indicator {
                width: 30rpx;
                height: 30rpx;
                border-radius: 50%;
                background: linear-gradient(to bottom, #4a6cf7, #6e3ff5);
                opacity: 0;
                margin-right: 10rpx;
                transition: opacity 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;

                &::after {
                    content: "";
                    width: 12rpx;
                    height: 6rpx;
                    border-left: 3rpx solid #fff;
                    border-bottom: 3rpx solid #fff;
                    transform: rotate(-45deg) translateY(-1rpx);
                }
            }
        }
    }

    .footer {
        margin-top: auto;
        padding: 40rpx 0 20rpx;
        text-align: center;
        z-index: 1;

        .footer-text {
            font-size: 24rpx;
            color: #999999;
        }
    }
}

// 弹窗覆盖层
.custom-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

// 弹窗主体
.custom-dialog {
    background-color: #fff;
    border-radius: 24rpx;
    width: 85%;
    max-width: 650rpx;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

// 弹窗标题区域
.custom-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 30rpx;
    border-bottom: 2rpx solid #eee;
}

// 标题文字
.custom-dialog-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
}

// 关闭按钮
.custom-dialog-close {
    font-size: 40rpx;
    color: #999;
    cursor: pointer;
    padding: 10rpx;
}

// 弹窗内容区域
.custom-dialog-content {
    padding: 40rpx;
    font-size: 28rpx;
    color: #555;
    line-height: 1.6;

    // 协议内容区域样式
    &.agreement-content {
        max-height: 60vh;
        overflow-y: auto;

        view {
            margin-bottom: 16rpx;
        }
    }
}

// 弹窗消息文字
.custom-dialog-message {
    // font-size: 30rpx;
    color: #666;
    line-height: 1.6;
    display: block;
    margin-bottom: 16rpx;
}

// 列表容器
.dialog-list {
    margin: 16rpx 0 20rpx 10rpx;
}

// 列表项
.dialog-list-item {
    // font-size: 30rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 8rpx;
    display: flex;
    align-items: flex-start;
}

// 弹窗按钮区域
.custom-dialog-footer {
    display: flex;
    justify-content: space-between;
    padding: 20rpx;
    border-top: 2rpx solid #eee;
}

// 弹窗按钮
.custom-dialog-btn {
    flex: 1;
    padding: 20rpx 0;
    text-align: center;
    font-size: 30rpx;
    transition: all 0.3s ease;
    border-radius: 8rpx;
    margin: 0 10rpx;
}

// 确认按钮
.custom-dialog-btn.confirm {
    background-color: #2f54eb;
    color: #fff;

    &:active {
        opacity: 0.8;
    }
}

// 退出按钮
.custom-dialog-btn.exit {
    background-color: #fff;
    color: #2f54eb;
    border: 2rpx solid #2f54eb;

    &:active {
        opacity: 0.8;
    }
}

// 如果需要为订单角色添加选中状态的特殊样式
.role-card.selected {
    .role-icon-container {
        &.student .icon-circle {
            background-color: rgba(64, 169, 255, 0.2);
        }

        &.coach .icon-circle {
            background-color: rgba(255, 107, 107, 0.2);
        }

        &.order .icon-circle {
            background-color: rgba(95, 184, 120, 0.2);
        }
    }
}

.quick-access-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    background: #ffffff;
    border-radius: 20rpx;
    box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.05);
    margin-top: 20rpx;
    transition: all 0.3s ease;
    animation: fadeIn 0.8s ease-out;

    &:active {
        transform: scale(0.98);
    }

    .link-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8rpx;
    }

    .link-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
    }

    .link-desc {
        font-size: 24rpx;
        color: #888888;
    }

    .link-arrow {
        font-size: 36rpx;
        color: #2f54eb;
        margin-left: 20rpx;
    }
}
