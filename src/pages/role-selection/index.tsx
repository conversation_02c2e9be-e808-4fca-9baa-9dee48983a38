import React, { useState, useEffect } from "react";
import { View, Text, Image } from "@tarojs/components";
import Taro, { useReady, useDidShow } from "@tarojs/taro";
import "./index.scss";
import Layout from "@/components/Layout";
// import { login } from "@utils/login";
import UserNoticeDialog from "@/components/UserNoticeDialog";
import UserAgreementDialog from "@/components/UserAgreementDialog";
import { tool } from "@/utils/tool";
import IconFont from "@/components/iconfont";

// 快速访问链接组件
const QuickAccessLink: React.FC = () => {
    const [tenantName, setTenantName] = useState<string>("");
    const [pageAddress, setPageAddress] = useState<string>("");
    const [pageParams, setPageParams] = useState<any>({});

    useEffect(() => {
        const storedTenantName = tool.data.get("longtime-lastScan-tenantName");
        const storedPageAddress = tool.data.get("longtime-lastScan-pageAddress");
        const storedPageParams = tool.data.get("longtime-lastScan-pageParams");

        if (storedTenantName && storedPageAddress) {
            setTenantName(storedTenantName);
            setPageAddress(storedPageAddress);
            setPageParams(storedPageParams || {});
        }
    }, []);

    if (!tenantName || !pageAddress) return null;

    const handleQuickAccess = () => {
        const params = new URLSearchParams();
        Object.entries(pageParams).forEach(([key, value]) => {
            params.append(key, String(value));
        });
        const url = `${pageAddress}${params.toString() ? "?" + params.toString() : ""}`;
        Taro.navigateTo({ url });
    };

    return (
        <View className="quick-access-link" onClick={handleQuickAccess}>
            <View className="link-content">
                <Text className="link-name">{tenantName}</Text>
                <Text className="link-desc">快速进入考场买券页面</Text>
            </View>
            {/* <View className="link-arrow">→</View> */}

            <IconFont name="arrow-right-circle-line" size={60} />
        </View>
    );
};

const RoleSelection: React.FC = () => {
    const [selectedRole, setSelectedRole] = useState<string | null>(null);
    const [showDialog, setShowDialog] = useState<boolean>(false);
    const [showAgreementDialog, setShowAgreementDialog] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    // Add useDidShow hook to reset state when page is shown
    Taro.useDidShow(() => {
        setSelectedRole(null);

        // login().then(async () => {});
    });

    const handleRoleSelect = (role: string) => {
        if (selectedRole) return; // 如果已经选择了角色，则不允许再次选择
        setSelectedRole(role);
        // Add a brief animation effect before navigation
        setTimeout(() => {
            // Navigate to the appropriate page based on role selection
            if (role === "student") {
                tool.data.clear();
                Taro.navigateTo({ url: "/subpackages/student/index/index" });
            } else if (role === "order") {
                tool.data.clear();
                Taro.navigateTo({ url: "/pages/my-orders/index" }); // 需要创建对应的订单页面
            } else if (role === "vip") {
                tool.data.clear();
                Taro.navigateTo({ url: "/subpackages/theoretical/index/index" });
            } else {
                tool.data.clear();
                Taro.navigateTo({ url: "/subpackages/student/user/index/index" });
            }
        }, 300);
    };

    // 获取用户信息
    useReady(async () => {
        try {
            // Check if user has already seen the notice and agreement
            const hasSeenNotice = tool.data.get("longtime-hasSeenNotice");
            const hasAgreedToTerms = tool.data.get("longtime-hasAgreedToTerms");

            if (!hasSeenNotice) {
                setShowDialog(true);
            } else if (!hasAgreedToTerms) {
                setShowAgreementDialog(true);
            }
        } catch (error) {
            console.error("获取用户信息失败:", error);
        }
    });

    // 处理确认按钮
    const handleConfirm = () => {
        setShowDialog(false);
        tool.data.set("longtime-hasSeenNotice", true);
        setShowAgreementDialog(true); // 显示协议窗口
        tool.data.set("longtime-hasAgreedToTerms", true);
    };

    // 处理退出按钮
    const handleExit = () => {
        Taro.exitMiniProgram();
    };

    // 处理同意按钮
    const handleAgree = () => {
        setShowAgreementDialog(false);
        tool.data.set("longtime-hasAgreedToTerms", true);
    };

    // 处理不同意按钮
    const handleDisagree = () => {
        setShowAgreementDialog(false);
        Taro.exitMiniProgram();
    };

    return (
        <Layout>
            <UserNoticeDialog show={showDialog} isLoading={isLoading} onConfirm={handleConfirm} onExit={handleExit} />

            <UserAgreementDialog show={showAgreementDialog} onAgree={handleAgree} onDisagree={handleDisagree} />

            <View className="role-selection-container">
                <View className="header">
                    <Text className="title">选择您的身份</Text>
                    <Text className="subtitle">请选择您要使用的身份进入系统</Text>
                </View>

                <View className="roles-container">
                    <View
                        className={`role-card ${selectedRole === "coach" ? "selected" : ""} ${selectedRole && selectedRole !== "coach" ? "disabled" : ""}`}
                        onClick={() => handleRoleSelect("coach")}
                    >
                        <View className="role-icon-container coach">
                            <View className="icon-circle">
                                <Image className="role-icon" src={"https://cdn.51panda.com/coach.png"} />
                            </View>
                        </View>
                        <View className="role-info">
                            <Text className="role-name">职员 & 教练</Text>
                            <Text className="role-desc">内部管理，指导学员，管理计划</Text>
                        </View>
                        <View className="selection-indicator"></View>
                    </View>

                    <View
                        className={`role-card ${selectedRole === "student" ? "selected" : ""} ${selectedRole && selectedRole !== "student" ? "disabled" : ""}`}
                        onClick={() => handleRoleSelect("student")}
                    >
                        <View className="role-icon-container student">
                            <View className="icon-circle">
                                <Image className="role-icon" src={"https://cdn.51panda.com/student.png"} />
                            </View>
                        </View>
                        <View className="role-info">
                            <Text className="role-name">学员</Text>
                            <Text className="role-desc">查看课程，预约训练，跟踪进度</Text>
                        </View>
                        <View className="selection-indicator"></View>
                    </View>

                    <View className="role-card disabled">
                        <View className="role-icon-container vip">
                            <View className="icon-circle">
                                <Image className="role-icon" src={"https://cdn.51panda.com/vip.png"} />
                            </View>
                        </View>
                        <View className="role-info">
                            <Text className="role-name">理论 VIP</Text>
                            <Text className="role-desc">理论课程学习，考试练习</Text>
                        </View>
                        <Text className="coming-soon">敬请期待</Text>
                        <View className="selection-indicator"></View>
                    </View>

                    <QuickAccessLink />
                </View>
            </View>
        </Layout>
    );
};

export default RoleSelection;
