.user-create-page {
    padding: 280rpx 0 0;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    height: 100vh;
    position: relative;
    font-family: BlinkMacSystemFont, "Segoe UI", "Robot<PERSON>", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Microsoft Yahei", sans-serif;
    display: flex;
    flex-direction: column;

    .header-image {
        filter: drop-shadow(0rpx -56rpx 34rpx #0000000a);
        width: 100vw;
        height: 35.7333vw;
    }

    // .section {
    //     padding: 80rpx 0 96rpx;
    //     // background-color: #ffffff;
    // }

    .margin-top-large {
        margin-top: 40rpx;
    }

    .welcome-section {
        padding: 0 0 8rpx;
        position: relative;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 0 0 40rpx 40rpx;
        overflow: hidden;

        &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
            z-index: 1;
        }

        &::after {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
            background-size: 30rpx 30rpx;
            animation: float 20s linear infinite;
            z-index: 2;
        }
    }

    .welcome-text-wrapper {
        padding: 60rpx 0 80rpx;
        filter: none;
        background: none;
        position: relative;
        z-index: 3;
    }

    .welcome-icon-wrapper {
        margin-bottom: 30rpx;
        position: relative;

        .welcome-icon {
            font-size: 80rpx;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 120rpx;
            height: 120rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10rpx);
            border: 2rpx solid rgba(255, 255, 255, 0.3);
            animation: bounce 2s ease-in-out infinite;
            box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
        }
    }

    .welcome-text {
        color: #ffffff;
        font-size: 48rpx;
        font-weight: bold;
        line-height: 56rpx;
        margin-bottom: 16rpx;
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
        letter-spacing: 2rpx;
    }

    .welcome-subtext {
        color: rgba(255, 255, 255, 0.9);
        font-size: 28rpx;
        line-height: 36rpx;
        margin-bottom: 40rpx;
        opacity: 0.95;
        font-weight: 400;
    }

    .welcome-decoration {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 16rpx;

        .decoration-dot {
            width: 12rpx;
            height: 12rpx;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            animation: pulse 2s ease-in-out infinite;

            &:nth-child(2) {
                animation-delay: 0.5s;
            }

            &:nth-child(3) {
                animation-delay: 1s;
            }
        }

        .decoration-line {
            width: 60rpx;
            height: 2rpx;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
        }
    }

    // 动画定义
    @keyframes bounce {
        0%,
        20%,
        50%,
        80%,
        100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10rpx);
        }
        60% {
            transform: translateY(-5rpx);
        }
    }

    @keyframes pulse {
        0%,
        100% {
            opacity: 0.6;
            transform: scale(1);
        }
        50% {
            opacity: 1;
            transform: scale(1.2);
        }
    }

    @keyframes float {
        0% {
            transform: translate(-50%, -50%) rotate(0deg);
        }
        100% {
            transform: translate(-50%, -50%) rotate(360deg);
        }
    }

    .avatar-wrapper {
        padding: 40rpx 0;
        filter: drop-shadow(0rpx 32rpx 32rpx #00000017);
        background-image: url("https://cdn.51panda.com/wx/create/17390884273373367835.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 200rpx;
    }

    .avatar-image {
        width: 116rpx;
        height: 116rpx;
    }

    .pos-center {
        position: absolute;
        left: 50%;
        top: 0;
        transform: translateX(-50%);
    }

    .font {
        font-size: 28rpx;
        line-height: 26.24rpx;
        color: #868686;
    }

    .form-section {
        padding: 0 30rpx;
        // background-color: #ffffff;
        border-radius: 40rpx 40rpx 0 0;
        margin-top: -40rpx;
        position: relative;
        z-index: 1;
        flex: 1;
        // min-height: 80vh;
        padding-bottom: calc(env(safe-area-inset-bottom) + 350rpx);
    }

    // .form-item {
    //     display: flex;
    //     flex-direction: column; /* Ensure the label and input stack vertically */
    //     width: 100%; /* Ensure the container takes full width */
    //     margin-bottom: 10rpx;
    //     padding: 0 12rpx 12rpx;
    //     // border-bottom: solid 2rpx #d0d0d0;
    // }

    // .form-input {
    //     width: 100%;
    //     padding: 15rpx 15rpx 15rpx 0;
    //     background-image: url("https://cdn.51panda.com/wx/create/17390996657852558454.png");
    //     background-size: 100% 100%;
    //     background-repeat: no-repeat;
    //     font-size: 26rpx;
    //     line-height: 24rpx;
    //     color: #474747;
    //     opacity: 0.6;
    //     margin-top: 20rpx;
    //     // margin-left: 20rpx;
    //     line-height: 22.08rpx;
    //     border: none;
    //     background-color: transparent;
    //     .nut-input-native {
    //         padding: 0 20rpx;
    //     }
    // }

    // .picker-text {
    //     flex: 1;
    //     border: none;
    //     background: transparent;
    //     font-size: 26rpx;
    //     color: #474747;
    //     font-family:  BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
    //         "Helvetica Neue", "Microsoft Yahei", sans-serif;
    // }
    .icon {
        width: 32rpx;
        height: 32rpx;
    }
    .pos {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
    }
}

.flex-row {
    display: flex;
    flex-direction: row;
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.justify-between {
    justify-content: space-between;
}

.justify-center {
    justify-content: center;
}

.items-center {
    align-items: center;
}

.margin-top-medium {
    margin-top: 20rpx;
}

.submit-button {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 16rpx 24rpx calc(16rpx + env(safe-area-inset-bottom));
    background: #fff;
    box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
    z-index: 100;

    .company-info {
        text-align: center;
        font-size: 20rpx;
        color: #999;
        margin-bottom: 16rpx;
        position: relative;

        &::before,
        &::after {
            content: "";
            position: absolute;
            top: 50%;
            width: 80rpx;
            height: 1rpx;
            background: #eee;
        }

        &::before {
            left: 30%;
        }

        &::after {
            right: 30%;
        }
    }

    .submit-btn {
        width: 100%;
        height: 80rpx;
        border: none;
        border-radius: 40rpx;
        background: #2f54eb;
        // background-image: linear-gradient(to right, #4c13d9, #7049ea);
        color: #fff;
        font-size: 30rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4rpx 12rpx rgba(47, 84, 235, 0.2);
        padding: 20rpx 0;

        &:active {
            opacity: 0.9;
            transform: translateY(2rpx);
        }
    }
}

.group_3 {
    margin-top: 104rpx;
    .font_2 {
        font-size: 20rpx;
        font-family: HarmonyOSSansSC;
        line-height: 18.54rpx;
        color: #2f58bc;
    }
    .text_5 {
        margin-left: 20rpx;
        text-transform: uppercase;
    }
    .text_6 {
        line-height: 18.74rpx;
    }
    .text-wrapper_2 {
        padding: 24rpx 0 24rpx;
        background-image: url("https://cdn.51panda.com/wx/create/17390996657852558454.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        .font_3 {
            font-size: 26rpx;
            font-family: HarmonyOSSansSC;
            line-height: 24rpx;
            color: #474747;
        }
        .text_7 {
            opacity: 0.6;
        }
        .text_8 {
            margin-left: 20rpx;
            line-height: 22.08rpx;
        }
    }
}

.input-group {
    margin-top: 10rpx;
    .label {
        font-size: 20rpx;
        font-family: HarmonyOSSansSC;
        line-height: 18.54rpx;
        color: #2f58bc;
        margin-left: 20rpx;
        text-transform: uppercase;
        line-height: 18.74rpx;
    }
    .input-wrapper {
        padding: 24rpx 0 24rpx;
        background-image: url("https://cdn.51panda.com/wx/create/17390996657852558454.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        .input-text {
            font-size: 26rpx;
            font-family: HarmonyOSSansSC;
            line-height: 24rpx;
            color: #474747;
            opacity: 0.6;
            margin-left: 20rpx;
            line-height: 22.08rpx;
        }
    }
}

.form-item-section {
    margin-left: 1rpx;
    margin-right: 1rpx;
    margin-bottom: 10rpx;
    width: 98%;
    padding: 4rpx;
    background-color: #ffffff;
    border-radius: 24rpx;
    box-shadow: 2rpx 2rpx 10rpx 2rpx #0000001a;

    .form-label {
        font-size: 28rpx;
        // font-family: MiSans;
        color: #000000;
        padding: 27rpx;
    }

    .label-text {
        line-height: 25.74rpx;
    }

    .form-input {
        font-size: 26rpx;
        // font-family: MiSans;
        // line-height: 29.28rpx;
        // color: #cccccc;
        padding: 22rpx;
        margin-right: 10rpx;
    }

    // .input-text {
    // line-height: 29.4rpx;
    // }
}

// 新的统一表单样式
.form-container {
    background: #ffffff;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    margin: 20rpx 10rpx;
    overflow: hidden;

    .form-item-unified {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 12rpx 30rpx;
        position: relative;

        .form-label-unified {
            font-size: 28rpx;
            color: #333333;
            font-weight: 500;
            width: 140rpx;
            flex-shrink: 0;
        }

        .form-input-unified {
            flex: 1;
            margin-left: 0rpx;
            font-size: 28rpx;
            color: #333333;

            .nut-input-native {
                border: none;
                outline: none;
                background: transparent;
                font-size: 28rpx;
                color: #333333;

                &::placeholder {
                    color: #999999;
                    font-size: 26rpx;
                }
            }
        }
    }

    .form-divider {
        height: 1rpx;
        background: #f0f0f0;
        margin: 0 30rpx;
    }
}
