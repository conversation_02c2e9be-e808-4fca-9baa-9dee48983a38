// index.tsx
import { useReady } from "@tarojs/taro";
import React, { useState, useCallback, useEffect } from "react";
import { View, Text } from "@tarojs/components";
import { ConfigProvider, Input } from "@nutui/nutui-react-taro";
import request from "@service/request";
import Taro from "@tarojs/taro";
import { tool } from "@utils/tool";
import { message } from "@components/MessageApi/MessageApiSingleton";
import "./index.scss";
import Layout from "@components/Layout";
import { login } from "@utils/login";
import { QValue } from "@/utils/util";
import TopNavBar from "@/components/topNavBar";

const UserBindPage = () => {
    // 表单数据状态
    const [formData, setFormData] = useState({
        realName: "",
        phone: "",
        idCard: "",
    });
    const [tenantId, setTenantId] = useState<string>("");
    const [companyName, setCompanyName] = useState("");

    const [userInfo, setUserInfo] = useState<API.UserInfo>();
    const [statusBarHeight, setStatusBarHeight] = useState(0);

    // 获取用户信息和状态栏高度
    useReady(async () => {
        try {
            const userInfo = await login();
            setUserInfo(userInfo);
            const systemInfo = Taro.getSystemInfoSync();
            setStatusBarHeight(systemInfo.statusBarHeight || 0);
        } catch {}
    });

    useEffect(() => {
        const tenantId = QValue("TenantId");
        setTenantId(tenantId);
    }, []);

    useEffect(() => {
        const fetchCompanyName = async () => {
            if (!tenantId) return;

            try {
                const response = await request.post<API.Result<any>>(`/Tenant/TenantInfo/${tenantId}`);

                if (response.success) {
                    setCompanyName(response.data.TenantName);
                } else {
                    throw new Error("获取租户信息失败");
                }
            } catch (error) {
                console.error("获取公司名称失败:", error);
                message.openToast("获取公司信息失败");
            }
        };

        fetchCompanyName();
    }, [tenantId]);

    // 处理输入变化
    const handleInputChange = (value: string, field: string) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    // 表单验证
    const validateForm = () => {
        if (!formData.realName) {
            message.error("请输入姓名");
            return false;
        }
        if (!formData.phone) {
            message.error("请输入手机号码");
            return false;
        }
        if (!formData.idCard) {
            message.error("请输入证件号码");
            return false;
        }
        // 可以添加更多验证逻辑，如手机号格式、身份证格式等
        return true;
    };

    const handleBindUser = useCallback(() => {
        if (!validateForm()) return;

        message.openDialog(
            "确认绑定",
            "是否确认绑定当前微信账号？绑定后领取红包将登记到该账户下。",
            () => {
                message.openLoading("处理中");
                request
                    .post<any>("/Auth/WxBindUser/findMainUserInfo", {
                        RealName: formData.realName,
                        Phone: formData.phone,
                        IdCard: formData.idCard,
                        TenantId: tenantId,
                    })
                    .then((response) => {
                        message.closeLoading();
                        if (response?.success) {
                            tool.data.clear();
                            message.success(response.message, "绑定成功", () => {
                                message.openLoading("正在初始化会话");
                                login().then(() => {
                                    message.closeLoading();
                                    Taro.navigateBack();
                                });
                            });
                        } else {
                            message.error(response.message, "绑定失败");
                        }
                    });
            },
            "确认",
            "取消"
        );
    }, [formData]);

    return (
        <ConfigProvider
            theme={{
                nutuiPickerListHeight: "40vh",
            }}
        >
            <Layout>
                <TopNavBar title="用户绑定" homeClick={() => Taro.navigateBack()} />
                <View className="user-create-page">
                    <View className="pos section">
                        <View className="flex-col">
                            <View className="flex-col justify-start relative welcome-section">
                                <View className="flex-col justify-start items-center welcome-text-wrapper">
                                    <View className="welcome-icon-wrapper">
                                        <View className="welcome-icon">👋</View>
                                    </View>
                                    <Text className="welcome-text">用户绑定</Text>
                                    <Text className="welcome-subtext">请输入注册信息完成绑定</Text>
                                    <View className="welcome-decoration">
                                        <View className="decoration-dot"></View>
                                        <View className="decoration-line"></View>
                                        <View className="decoration-dot"></View>
                                    </View>
                                </View>
                            </View>

                            <View className="margin-top-medium form-section">
                                <View className="form-container">
                                    <View className="form-item-unified">
                                        <text className="form-label-unified">真实姓名</text>
                                        <Input
                                            className="form-input-unified"
                                            placeholder="请输入真实姓名"
                                            value={formData.realName}
                                            onChange={(val) => handleInputChange(val, "realName")}
                                        />
                                    </View>

                                    <View className="form-divider"></View>

                                    <View className="form-item-unified">
                                        <text className="form-label-unified">手机号码</text>
                                        <Input
                                            className="form-input-unified"
                                            placeholder="请输入手机号码"
                                            type="number"
                                            value={formData.phone}
                                            maxLength={11}
                                            onChange={(val) => handleInputChange(val, "phone")}
                                        />
                                    </View>

                                    <View className="form-divider"></View>

                                    <View className="form-item-unified">
                                        <text className="form-label-unified">证件号码</text>
                                        <Input
                                            className="form-input-unified"
                                            type="idcard"
                                            value={formData.idCard}
                                            placeholder="请输入证件号码"
                                            onChange={(val) => handleInputChange(val, "idCard")}
                                        />
                                    </View>
                                </View>

                                <View className="footer">
                                    <View className="submit-button">
                                        <View className="company-info">{companyName}</View>
                                        <button className="font submit-btn" onClick={handleBindUser}>
                                            提交信息
                                        </button>
                                    </View>
                                </View>
                            </View>
                        </View>
                    </View>
                </View>
            </Layout>
        </ConfigProvider>
    );
};

export default UserBindPage;
