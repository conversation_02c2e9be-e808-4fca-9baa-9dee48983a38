import React, { useState, useEffect } from "react";
import { View, Image, Text } from "@tarojs/components";
import { Form, Input, Picker, ConfigProvider, NoticeBar } from "@nutui/nutui-react-taro";
import { ArrowDown } from "@nutui/icons-react-taro";
import "./index.scss";
import Layout from "@/components/Layout";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import request from "@/service/request";
import { QValue } from "@/utils/util";
import { useReady } from "@tarojs/taro";
import { login } from "@utils/login";
import Taro from "@tarojs/taro";

interface UserForm {
    RealName: string;
    IdCard: string;
    Phone: string;
    CompanyName: string;
    Gender: string;
    AuditStatus: number;
    AuditStatusText: string;
}

interface AuditStatus {
    status: number; // 0: pending, 1: approved, 2: rejected
    message: string;
}

const UserCreatePage: React.FC = () => {
    const [tenantId, setTenantId] = useState<string>("");
    const [form, setForm] = useState<UserForm>({
        RealName: "",
        IdCard: "",
        Phone: "",
        CompanyName: "",
        Gender: "",
        AuditStatus: 0,
        AuditStatusText: "",
    });
    const [companyName, setCompanyName] = useState("");
    const [isPopupVisible, setPopupVisible] = useState(false);
    const [userInfo, setUserInfo] = useState<API.UserInfo>();
    const [statusBarHeight, setStatusBarHeight] = useState(0);
    const [audit, setAudit] = useState(false);
    const [showVerificationModal, setShowVerificationModal] = useState(true);
    const [auditInfo, setAuditInfo] = useState<UserForm | undefined>(undefined);
    const [showAuditDetailsModal, setShowAuditDetailsModal] = useState(false);

    // 获取用户信息和状态栏高度
    useReady(async () => {
        try {
            const userInfo = await login();
            setUserInfo(userInfo);
            const systemInfo = Taro.getSystemInfoSync();
            setStatusBarHeight(systemInfo.statusBarHeight || 0);
        } catch (error) {
            console.error("获取用户信息失败:", error);
        }
    });

    useEffect(() => {
        const tenantId = QValue("TenantId");
        setTenantId(tenantId);

        const audit = QValue("Audit");
        setAudit(audit);
    }, []);

    useEffect(() => {
        const fetchCompanyName = async () => {
            if (!tenantId) return;

            try {
                const response = await request.post<API.Result<any>>(`/Tenant/TenantInfo/${tenantId}`);

                if (response.success) {
                    setCompanyName(response.data.TenantName);
                } else {
                    throw new Error("获取租户信息失败");
                }
            } catch (error) {
                console.error("获取公司名称失败:", error);
                message.openToast("获取公司信息失败");
            }
        };

        fetchCompanyName();
    }, [tenantId]);

    useEffect(() => {
        const fetchAuditStatus = async () => {
            if (!tenantId) return;

            try {
                const response = await request.post<API.Result<any>>("/Auth/Create/CreateUser/getMyAudit", {
                    TenantId: tenantId,
                });

                if (response.success && response.data) {
                    setAuditInfo(response.data);
                }
            } catch (error) {
                console.error("获取审核状态失败:", error);
            }
        };

        fetchAuditStatus();
    }, [tenantId]);

    const getAuditStatusText = () => {
        if (!auditInfo) return "";

        switch (auditInfo.AuditStatus) {
            case 0:
                return "你有一条信息在审核";
            case 1:
                return "你已经有一个审核通过的记录";
            case 2:
                return "你有一个审核失败的记录";
            default:
                return "";
        }
    };

    const handleViewDetails = () => {
        setShowAuditDetailsModal(true);
    };

    const GenderOptions = [
        { value: "1", text: "男" },
        { value: "2", text: "女" },
    ];

    const requestSubscribeMessage = async () => {
        try {
            const tmplIds = [
                "YDxrRATd2Aju6uNKNllzQQMOUcLM0sbSg6gmDvYSXKw", // 审核通过
                "zusywVlM2yVEAW9VWJdWv7GUxCZ2aKbU4edfPrXN-AM", // 审核驳回
            ];

            const res = await Taro.requestSubscribeMessage({
                tmplIds: tmplIds,
                entityIds: tmplIds, // 添加必需的 entityIds 参数
            });

            console.log("res");
            console.log(res);

            // 检查订阅结果
            const subscribeResults = {
                approved: res[tmplIds[0]] === "accept",
                rejected: res[tmplIds[1]] === "accept",
            };

            return subscribeResults;
        } catch (error) {
            console.error("订阅消息失败:", error);
            message.openToast("订阅消息失败");
            return null;
        }
    };

    const handleSubmit = async () => {
        try {
            if (!form.RealName || !form.IdCard || !form.Phone || !form.CompanyName || !form.Gender) {
                message.openToast("请填写必填项");
                return;
            }

            // 请求订阅消息
            const subscribeResults = await requestSubscribeMessage();
            if (!subscribeResults) {
                message.openToast("订阅消息失败，请重试");
                return;
            }

            message.openLoading("正在提交数据");
            Taro.login({
                success: async (res) => {
                    const response = await request.put<any>(`/Auth/Create/CreateUser/createUser/${res.code}`, {
                        ...form,
                        TenantId: tenantId,
                    });

                    message.closeLoading();

                    if (response.success) {
                        message.success(response.message);
                        setForm({
                            RealName: "",
                            IdCard: "",
                            Phone: "",
                            CompanyName: "",
                            Gender: "",
                            AuditStatus: 0,
                            AuditStatusText: "",
                        });
                        Taro.navigateBack();
                    } else {
                        message.error(response.message);
                    }
                },
            });
        } catch (error) {
            console.error("创建用户失败:", error);
            message.openToast("创建失败");
        }
    };

    const handleInputChange = (name: keyof UserForm, value: string) => {
        setForm((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleGenderConfirm = (list: any[], values: (string | number)[]) => {
        handleInputChange("Gender", String(values[0]));
        setPopupVisible(false);
    };

    return (
        <ConfigProvider
            theme={{
                nutuiPickerListHeight: "40vh",
            }}
        >
            <Layout>
                <View className="user-create-page" style={{ marginTop: `${80 + statusBarHeight}rpx` }}>
                    <View className="pos section">
                        <View className="flex-col">
                            <View className="flex-col justify-start relative welcome-section">
                                <View className="flex-col justify-start items-center welcome-text-wrapper">
                                    <Text className="welcome-text">欢迎注册</Text>
                                    {/* <Text className="welcome-subtext">请填写以下信息完成注册</Text> */}
                                </View>
                            </View>

                            <View className="form-section">
                                <View className="form-item">
                                    <text className="form-label">真实姓名</text>
                                    <View className="form-input-wrapper">
                                        <input
                                            className="form-input"
                                            placeholder="请输入您的真实姓名"
                                            value={form.RealName}
                                            onChange={(e) => handleInputChange("RealName", e.target.value)}
                                        />
                                    </View>
                                </View>

                                <View className="form-item">
                                    <text className="form-label">证件号码</text>
                                    <View className="form-input-wrapper">
                                        <input
                                            className="form-input"
                                            placeholder="请输入证件号码"
                                            value={form.IdCard}
                                            onChange={(e) => handleInputChange("IdCard", e.target.value)}
                                            type="idcard"
                                        />
                                    </View>
                                </View>

                                <View className="form-item">
                                    <text className="form-label">手机号码</text>
                                    <View className="form-input-wrapper">
                                        <input
                                            className="form-input"
                                            placeholder="请输入手机号码"
                                            type="tel"
                                            value={form.Phone}
                                            onChange={(e) => handleInputChange("Phone", e.target.value)}
                                        />
                                    </View>
                                </View>

                                <View className="form-item">
                                    <text className="form-label">所属单位</text>
                                    <View className="form-input-wrapper">
                                        <input
                                            className="form-input"
                                            placeholder="请输入所属单位"
                                            value={form.CompanyName}
                                            onChange={(e) => handleInputChange("CompanyName", e.target.value)}
                                        />
                                    </View>
                                </View>

                                <View className="form-item">
                                    <text className="form-label">选择性别</text>
                                    <View className="form-input-wrapper">
                                        <Input
                                            className="form-input"
                                            onClick={() => setPopupVisible(true)}
                                            disabled
                                            value={form.Gender ? GenderOptions.find((item) => item.value === form.Gender)?.text : "选择性别"}
                                        />
                                        <ArrowDown className="input-icon" />
                                    </View>
                                </View>
                            </View>

                            <View className="footer">
                                {auditInfo && (
                                    <>
                                        <View className={`audit-status-bar status-${auditInfo?.AuditStatus}`}>
                                            <Text className="audit-status-text">{getAuditStatusText()}</Text>
                                            <Text className="view-details" onClick={handleViewDetails}>
                                                查看详情
                                            </Text>
                                        </View>
                                    </>
                                )}
                                <View className="submit-button">
                                    <View className="company-info">{companyName}</View>
                                    <button className="font submit-btn" onClick={handleSubmit}>
                                        提交信息
                                    </button>
                                </View>
                            </View>
                        </View>
                    </View>
                </View>
                <Picker
                    title="选择性别"
                    visible={isPopupVisible}
                    options={GenderOptions}
                    onConfirm={(list, values) => handleGenderConfirm(list, values)}
                    onClose={() => setPopupVisible(false)}
                    value={form.Gender ? [form.Gender] : []}
                />
                {showVerificationModal && (
                    <View className="verification-modal">
                        <View className="custom-modal-overlay">
                            <View className="custom-modal">
                                <View className="custom-modal-header">
                                    <Text className="custom-modal-title">温馨提示</Text>
                                </View>
                                <View className="custom-modal-content">
                                    <Text className="custom-modal-text">请确保填写的姓名、身份证号和手机号与您微信绑定的银行卡实名信息保持一致，否则后期将无法使用!</Text>
                                </View>
                                <View className="custom-modal-footer">
                                    <View className="custom-modal-button" onClick={() => setShowVerificationModal(false)}>
                                        我知道了
                                    </View>
                                </View>
                            </View>
                        </View>
                    </View>
                )}
                {showAuditDetailsModal && (
                    <View className="verification-modal">
                        <View className="custom-modal-overlay">
                            <View className="custom-modal">
                                <View className="custom-modal-header">
                                    <Text className="custom-modal-title">申请详情</Text>
                                </View>
                                <View className="custom-modal-content">
                                    <View className="audit-details">
                                        <View className="audit-detail-item">
                                            <Text className="detail-label">真实姓名：</Text>
                                            <Text className="detail-value">{auditInfo?.RealName}</Text>
                                        </View>
                                        <View className="audit-detail-item">
                                            <Text className="detail-label">证件号码：</Text>
                                            <Text className="detail-value">{auditInfo?.IdCard}</Text>
                                        </View>
                                        <View className="audit-detail-item">
                                            <Text className="detail-label">手机号码：</Text>
                                            <Text className="detail-value">{auditInfo?.Phone}</Text>
                                        </View>
                                        <View className="audit-detail-item">
                                            <Text className="detail-label">所属单位：</Text>
                                            <Text className="detail-value">{auditInfo?.CompanyName}</Text>
                                        </View>
                                        <View className="audit-detail-item">
                                            <Text className="detail-label">选择性别：</Text>
                                            <Text className="detail-value">{auditInfo?.Gender == "1" ? "男" : "女"}</Text>
                                        </View>
                                        <View className="audit-detail-item">
                                            <Text className="detail-label">审核状态：</Text>
                                            <Text className="detail-value">{auditInfo?.AuditStatusText || "暂无审核信息"}</Text>
                                        </View>
                                    </View>
                                </View>
                                <View className="custom-modal-footer">
                                    <View className="custom-modal-button" onClick={() => setShowAuditDetailsModal(false)}>
                                        我知道了
                                    </View>
                                </View>
                            </View>
                        </View>
                    </View>
                )}
            </Layout>
        </ConfigProvider>
    );
};

export default UserCreatePage;
