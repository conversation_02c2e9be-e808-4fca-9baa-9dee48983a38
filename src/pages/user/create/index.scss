.user-create-page {
    padding: 280rpx 0 0;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    height: 100vh;
    position: relative;
    // font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Robot<PERSON>", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    display: flex;
    flex-direction: column;
    // background: linear-gradient(180deg, #f0f2f5 0%, #f8f9fa 100%);

    .header-image {
        filter: drop-shadow(0rpx -56rpx 34rpx #0000000a);
        width: 100vw;
        height: 35.7333vw;
    }

    // .section {
    //     padding: 80rpx 0 96rpx;
    //     // background-color: #ffffff;
    // }

    .margin-top-large {
        margin-top: 40rpx;
    }

    .welcome-section {
        padding: 0 0 8rpx;
        position: relative;
        animation: fadeInDown 0.6s ease-out;
    }

    .welcome-text-wrapper {
        padding: 60rpx 0 40rpx;
        text-align: center;
        position: relative;

        &::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60rpx;
            height: 4rpx;
            background: linear-gradient(90deg, #2f54eb, #1d39c4);
            border-radius: 2rpx;
        }

        .verification-tip {
            font-size: 24rpx;
            color: #ff4d4f;
            text-align: center;
            margin-top: 20rpx;
            padding: 0 40rpx;
            line-height: 1.5;
        }
    }

    .welcome-text {
        color: #1a1a1a;
        font-size: 48rpx;
        font-weight: 600;
        line-height: 1.4;
        margin-bottom: 16rpx;
        background: linear-gradient(135deg, #1a1a1a 0%, #333 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .welcome-subtext {
        color: #666;
        font-size: 28rpx;
        line-height: 1.5;
        opacity: 0.8;
    }

    .avatar-wrapper {
        padding: 40rpx 0;
        filter: drop-shadow(0rpx 32rpx 32rpx #00000017);
        background-image: url("https://cdn.51panda.com/wx/create/17390884273373367835.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 200rpx;
    }

    .avatar-image {
        width: 116rpx;
        height: 116rpx;
    }

    .pos-center {
        position: absolute;
        left: 50%;
        top: 0;
        transform: translateX(-50%);
    }

    .font {
        font-size: 28rpx;
        line-height: 26.24rpx;
        color: #868686;
    }

    .form-section {
        margin: 0rpx 40rpx;
        padding: 40rpx 32rpx;
        background: #ffffff;
        border-radius: 24rpx;
        position: relative;
        z-index: 1;
        flex: 1;
        box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
        animation: slideUp 0.6s ease-out;
    }

    .form-item {
        position: relative;
        margin-bottom: 28rpx;
        animation: fadeIn 0.5s ease-out;
        animation-fill-mode: both;

        @for $i from 1 through 5 {
            &:nth-child(#{$i}) {
                animation-delay: #{$i * 0.1}s;
            }
        }

        &:last-child {
            margin-bottom: 0;
        }
    }

    .form-label {
        font-size: 26rpx;
        color: #333;
        font-weight: 500;
        display: inline-block;
        margin-bottom: 10rpx;
        position: relative;
        padding-left: 16rpx;

        &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 6rpx;
            height: 24rpx;
            background: linear-gradient(135deg, #2f54eb, #597ef7);
            border-radius: 3rpx;
        }
    }

    .form-input-wrapper {
        position: relative;
        width: 100%;

        .input-icon {
            position: absolute;
            right: 24rpx;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 32rpx;
            pointer-events: none;
        }
    }

    .form-input {
        width: 100%;
        height: 88rpx;
        background: #f8f9fa;
        border-radius: 16rpx;
        padding: 0 32rpx;
        font-size: 28rpx;
        color: #333;
        border: 2rpx solid #edf0f5;
        transition: all 0.3s ease;
        box-sizing: border-box;

        &:focus,
        &:active {
            border-color: #2f54eb;
            background: #fff;
            box-shadow: 0 0 0 4rpx rgba(47, 84, 235, 0.1);
            outline: none;
        }

        &::placeholder {
            color: #bbb;
            font-size: 26rpx;
        }
    }

    .input-text {
        // width: 100%;
        height: 84rpx;
        background: #f5f7fa;
        border-radius: 12rpx;
        padding: 0 32rpx;
        font-size: 28rpx;
        color: #333;
        border: 2rpx solid transparent;
        transition: all 0.2s ease;
        box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.02);
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
        position: relative;
        z-index: 2;
        -webkit-appearance: none;
        appearance: none;
        outline: none;

        &:active {
            border-color: #2f54eb;
            background: #fff;
            box-shadow: 0 0 0 2rpx rgba(47, 84, 235, 0.1);
        }
    }

    .ml-33 {
        margin-left: 0;
    }

    .icon {
        width: 32rpx;
        height: 32rpx;
    }
    .pos {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
    }
}

.flex-row {
    display: flex;
    flex-direction: row;
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.justify-between {
    justify-content: space-between;
}

.justify-center {
    justify-content: center;
}

.items-center {
    align-items: center;
}

.margin-top-medium {
    margin-top: 20rpx;
}

.footer-area {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    background: #fff;
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
    box-shadow: 0 -4rpx 24rpx rgba(0, 0, 0, 0.08);
    z-index: 10;
    padding-bottom: env(safe-area-inset-bottom);
}

.audit-status-bar {
    text-align: center;
    padding: 16rpx 16rpx 16rpx;
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
    margin: 0;
    background: #f5f5f5;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .audit-status-text {
        font-size: 24rpx;
        text-align: left;
    }

    .view-details {
        font-size: 24rpx;
        text-align: right;
    }
}
.audit-status-bar.status-0 {
    background: rgba(255, 215, 0, 0.1);
    color: #ffd700;
}
.audit-status-bar.status-1 {
    background: rgba(0, 255, 127, 0.1);
    color: #00ff7f;
}
.audit-status-bar.status-2 {
    background: rgba(255, 77, 79, 0.1);
    color: #ff4d4f;
}

.footer {
    margin: 0;
    padding: 0;
}

.submit-button {
    padding: 24rpx 40rpx calc(24rpx + env(safe-area-inset-bottom));
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 -4rpx 24rpx rgba(0, 0, 0, 0.08);
    z-index: 100;
    backdrop-filter: blur(10px);
    animation: slideUp 0.6s ease-out;
    display: flex;
    flex-direction: column;
    align-items: center;

    .company-info {
        text-align: center;
        font-size: 24rpx;
        color: #666;
        margin-bottom: 24rpx;
        position: relative;
        opacity: 0.8;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 20rpx;
        width: 100%;
        padding: 0 40rpx;

        &::before,
        &::after {
            content: "";
            display: block;
            width: 100rpx;
            height: 2rpx;
            background: #e5e5e5;
            flex: none;
        }
    }

    .submit-btn {
        width: 100%;
        height: 92rpx;
        border: none;
        border-radius: 46rpx;
        background: linear-gradient(135deg, #2f54eb, #1d39c4);
        color: #fff;
        font-size: 32rpx;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8rpx 16rpx rgba(47, 84, 235, 0.25);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }
}

.group_3 {
    margin-top: 104rpx;
    .font_2 {
        font-size: 20rpx;
        font-family: HarmonyOSSansSC;
        line-height: 18.54rpx;
        color: #2f58bc;
    }
    .text_5 {
        margin-left: 20rpx;
        text-transform: uppercase;
    }
    .text_6 {
        line-height: 18.74rpx;
    }
    .text-wrapper_2 {
        padding: 24rpx 0 24rpx;
        background-image: url("https://cdn.51panda.com/wx/create/17390996657852558454.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        .font_3 {
            font-size: 26rpx;
            font-family: HarmonyOSSansSC;
            line-height: 24rpx;
            color: #474747;
        }
        .text_7 {
            opacity: 0.6;
        }
        .text_8 {
            margin-left: 20rpx;
            line-height: 22.08rpx;
        }
    }
}

.input-group {
    margin-top: 10rpx;
    .label {
        font-size: 20rpx;
        font-family: HarmonyOSSansSC;
        line-height: 18.54rpx;
        color: #2f58bc;
        margin-left: 20rpx;
        text-transform: uppercase;
        line-height: 18.74rpx;
    }
    .input-wrapper {
        padding: 24rpx 0 24rpx;
        background-image: url("https://cdn.51panda.com/wx/create/17390996657852558454.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        .input-text {
            font-size: 26rpx;
            font-family: HarmonyOSSansSC;
            line-height: 24rpx;
            color: #474747;
            opacity: 0.6;
            margin-left: 20rpx;
            line-height: 22.08rpx;
        }
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(40rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.verification-modal {
    .custom-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.75);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        animation: fadeIn 0.3s ease-out;
        backdrop-filter: blur(4px);

        .custom-modal {
            width: 560rpx;
            background: #fff;
            border-radius: 32rpx;
            overflow: hidden;
            animation: slideUp 0.3s ease-out;
            box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
            position: relative;

            &::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 6rpx;
                background: linear-gradient(90deg, #2f54eb, #1d39c4);
            }

            .custom-modal-content {
                padding: 48rpx 40rpx;

                .audit-details {
                    .audit-detail-item {
                        display: flex;
                        margin-bottom: 24rpx;
                        font-size: 28rpx;
                        line-height: 1.5;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .detail-label {
                            color: #666;
                            width: 160rpx;
                            flex-shrink: 0;
                        }

                        .detail-value {
                            color: #333;
                            flex: 1;
                        }
                    }
                }
            }
        }

        .custom-modal-header {
            padding: 28rpx 40rpx 24rpx;
            text-align: center;
            position: relative;
        }

        .custom-modal-title {
            // font-size: 40rpx;
            // font-weight: 600;
            // color: #1a1a1a;
            // position: relative;
            // display: inline-block;
            // letter-spacing: 2rpx;

            &::after {
                content: "";
                position: absolute;
                bottom: -16rpx;
                left: 50%;
                transform: translateX(-50%);
                width: 48rpx;
                height: 6rpx;
                background: linear-gradient(90deg, #2f54eb, #1d39c4);
                border-radius: 3rpx;
                opacity: 0.8;
            }
        }

        .custom-modal-content {
            padding: 48rpx 40rpx;
        }

        .custom-modal-text {
            font-size: 30rpx;
            color: #4a4a4a;
            line-height: 1.8;
            text-align: center;
            letter-spacing: 1rpx;
        }

        .custom-modal-footer {
            padding: 0 40rpx 48rpx;
            display: flex;
            justify-content: center;
        }

        .custom-modal-button {
            width: 100%;
            height: 80rpx;
            background: linear-gradient(135deg, #2f54eb, #1d39c4);
            border-radius: 48rpx;
            color: #fff;
            font-size: 34rpx;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 12rpx 24rpx rgba(47, 84, 235, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            &::after {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            &:active {
                transform: translateY(2rpx) scale(0.98);
                box-shadow: 0 6rpx 12rpx rgba(47, 84, 235, 0.2);

                &::after {
                    opacity: 1;
                }
            }
        }
    }
}

.audit-notice {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 140rpx;
    z-index: 99;
    margin: 0;
    border-radius: 0;

    :global {
        .nut-noticebar {
            padding: 16rpx 24rpx;
            font-size: 28rpx;
            line-height: 1.5;
            border-radius: 0;
        }

        .view-details {
            color: #1989fa;
            margin-left: 20rpx;
            text-decoration: underline;
            font-size: 28rpx;
        }
    }
}

.view-details {
    color: #2f54eb;
    font-size: 26rpx;
    cursor: pointer;
    text-decoration: underline;
}
