import React, { useState, useEffect } from "react";
import { View } from "@tarojs/components";
import { Cell, Button, Tag } from "@nutui/nutui-react-taro";
import "./index.scss";

interface UserAudit {
    id: string;
    username: string;
    phone: string;
    status: "pending" | "approved" | "rejected";
    createTime: string;
}

const UserAuditPage: React.FC = () => {
    const [auditList, setAuditList] = useState<UserAudit[]>([]);

    useEffect(() => {
        fetchAuditList();
    }, []);

    const fetchAuditList = async () => {
        try {
            // TODO: 调用获取待审核用户列表的API
            // const response = await getUserAuditList();
            // setAuditList(response.data);
        } catch (error) {
            console.error("获取审核列表失败:", error);
        }
    };

    const handleAudit = async (userId: string, status: "approved" | "rejected") => {
        try {
            // TODO: 调用审核API
            // await auditUser(userId, status);
            await fetchAuditList(); // 刷新列表
        } catch (error) {
            console.error("审核操作失败:", error);
        }
    };

    const getStatusTag = (status: string) => {
        const statusMap = {
            pending: { type: "warning", text: "待审核" },
            approved: { type: "success", text: "已通过" },
            rejected: { type: "danger", text: "已拒绝" },
        };
        const currentStatus = statusMap[status] || statusMap.pending;
        return <Tag type={currentStatus.type}>{currentStatus.text}</Tag>;
    };

    return (
        <View className="user-audit-page">
            {auditList.map((user) => (
                <Cell key={user.id}>
                    <View className="user-item">
                        <View className="user-info">
                            <View className="username">{user.username}</View>
                            <View className="phone">{user.phone}</View>
                            <View className="status">{getStatusTag(user.status)}</View>
                        </View>
                        {user.status === "pending" && (
                            <View className="action-buttons">
                                <Button type="primary" size="small" onClick={() => handleAudit(user.id, "approved")}>
                                    通过
                                </Button>
                                <Button type="danger" size="small" onClick={() => handleAudit(user.id, "rejected")}>
                                    拒绝
                                </Button>
                            </View>
                        )}
                    </View>
                </Cell>
            ))}
        </View>
    );
};

export default UserAuditPage;
