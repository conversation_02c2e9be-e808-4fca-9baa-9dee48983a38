import React from "react";
import { View, Text, Button } from "@tarojs/components";
import Taro from "@tarojs/taro";
import "./index.scss";

const OfflineNotice = () => {
    const handleNavigateToTarget = () => {
        Taro.navigateToMiniProgram({
            appId: "wxc77987296fe617c5",
            path: "",
            extraData: {},
            envVersion: "release",
            success: () => {
                console.log("跳转成功");
            },
            fail: (err) => {
                console.error("跳转失败", err);
                Taro.showToast({
                    title: "跳转失败，请稍后重试",
                    icon: "none",
                    duration: 2000,
                });
            },
        });
    };

    return (
        <View className="offline-notice-page">
            <View className="content-container">
                {/* 图标区域 */}
                <View className="icon-section">
                    <View className="warning-icon">⚠️</View>
                </View>

                {/* 标题 */}
                <View className="title-section">
                    <Text className="main-title">服务迁移通知</Text>
                </View>

                {/* 说明文字 */}
                <View className="description-section">
                    <Text className="description-text">当前小程序已下线</Text>
                    <Text className="description-text">请前往小程序</Text>
                    <Text className="highlight-text">考场助手</Text>
                    <Text className="description-text">继续使用相关服务</Text>
                </View>

                {/* 跳转按钮 */}
                <View className="button-section">
                    <Button className="jump-button" onClick={handleNavigateToTarget}>
                        前往考场助手
                    </Button>
                </View>

                {/* 底部提示 */}
                <View className="footer-section">
                    <Text className="footer-text">感谢您的理解与支持</Text>
                </View>
            </View>
        </View>
    );
};

export default OfflineNotice;
