page {
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.offline-notice-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 20rpx;
    box-sizing: border-box;
}

.content-container {
    background: #ffffff;
    border-radius: 32rpx;
    padding: 80rpx 60rpx;
    box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 600rpx;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.content-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 8rpx;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.icon-section {
    margin-bottom: 40rpx;
}

.warning-icon {
    font-size: 120rpx;
    line-height: 1;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20rpx);
    }
    60% {
        transform: translateY(-10rpx);
    }
}

.title-section {
    margin-bottom: 50rpx;
}

.main-title {
    font-size: 48rpx;
    font-weight: 600;
    color: #333333;
    line-height: 1.3;
}

.description-section {
    margin-bottom: 60rpx;
    line-height: 1.6;
}

.description-text {
    display: block;
    font-size: 32rpx;
    color: #666666;
    margin-bottom: 10rpx;
}

.highlight-text {
    display: inline-block;
    font-size: 36rpx;
    font-weight: 600;
    color: #667eea;
    margin: 0 8rpx;
    padding: 8rpx 16rpx;
    background: linear-gradient(90deg, #667eea15, #764ba215);
    border-radius: 12rpx;
    border: 2rpx solid #667eea30;
}

.button-section {
    margin-bottom: 50rpx;
}

.jump-button {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 600;
    border: none;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.jump-button:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.jump-button::after {
    border: none;
}

.footer-section {
    .footer-text {
        font-size: 24rpx;
        color: #999999;
        line-height: 1.4;
    }
}

/* 响应式设计 */
@media screen and (max-width: 400px) {
    .content-container {
        padding: 60rpx 40rpx;
    }

    .main-title {
        font-size: 42rpx;
    }

    .description-text {
        font-size: 28rpx;
    }

    .highlight-text {
        font-size: 32rpx;
    }
}
