import { useRouter } from '@tarojs/taro'
import { useEffect } from 'react'
import './index.scss'
import '@utils/app.scss'
import Taro from '@tarojs/taro';
import { WebView } from '@tarojs/components';


const App = () => {
    const router = useRouter();
    const { url } = router.params;

    useEffect(() => {
        if (!url) {
            Taro.navigateBack();
        }
    }, [url]);
    return (
        url ? <WebView src={decodeURIComponent(url)} /> : null
    );
};
export default App;
