import { Component } from "react";
import Taro from "@tarojs/taro";
import { View, WebView, Button, Text } from "@tarojs/components";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import Layout from "@/components/Layout";
import { login } from "@utils/login";
import request from "@/service/request";
import { tool } from "@/utils/tool";
import "./index.scss";

interface IState {
    url: string;
    loading: boolean;
    showTips: boolean;
    redirectUrl: string;
    accountName: string;
    appId: string;
}

export default class FollowOfficialAccount extends Component<{}, IState> {
    constructor(props: any) {
        super(props);
        this.state = {
            url: "",
            loading: true,
            showTips: false,
            redirectUrl: "",
            accountName: "盼达软件",
            appId: "",
        };
    }

    componentDidMount() {
        // 获取路由参数
        const router = Taro.getCurrentInstance().router;
        const redirectUrl = decodeURIComponent(router?.params?.redirectUrl || "");
        const accountName = router?.params?.accountName || "盼达软件";
        const appId = router?.params?.appId || "";

        // 如果有redirectUrl，存储到缓存中
        if (redirectUrl) {
            tool.data.set("follow-redirect-url", redirectUrl);
        }

        // 使用新的URL格式
        let subscribeUrl = "";

        if (appId) {
            // 使用指定的关注页面URL
            subscribeUrl = `https://www.51jx.cc/WeiXin/subscribe?appid=${appId}`;

            console.log(`使用指定的关注页面URL: ${subscribeUrl}`);

            this.setState({
                url: subscribeUrl,
                redirectUrl,
                accountName,
                appId,
                loading: false,
            });

            console.log(`关注公众号页面加载，appId=${appId}, 返回路径=${redirectUrl}, 页面URL=${subscribeUrl}`);
        } else {
            // 如果没有提供appId参数，使用默认的公众号关注页面
            const defaultAppId = "wxca4614f69b2279a4";
            subscribeUrl = `https://www.51jx.cc/WeiXin/subscribe?appid=${defaultAppId}`;

            console.log(`使用默认的关注页面URL: ${subscribeUrl}`);
            this.setState({
                url: subscribeUrl,
                loading: false,
            });
        }
    }

    async componentDidShow() {
        // 页面显示时的逻辑
        const { appId } = this.state;
        if (appId) {
            // 检查是否关注了公众号
            const response = await request.post<any>("/Wx/Subscribe/checkSubscribe", {
                MpAppId: appId,
            });

            message.closeLoading();

            if (response.success && response.data && response.data.isSubscribed) {
                // 已关注，返回原页面
                message.success("关注成功，正在返回...");
                this.navigateBack();
            } else {
                // 未关注，提示用户
                message.error("请先关注公众号后再返回");
            }
        }
    }

    // 处理WebView加载错误
    handleWebViewError = () => {
        // message.error("页面加载失败，请稍后重试");
        this.setState({ loading: false });
    };

    // 处理WebView加载完成
    handleWebViewLoad = () => {
        this.setState({ loading: false });

        // 显示关注提示
        setTimeout(() => {
            this.setState({ showTips: true });
        }, 3000);
    };

    // 返回上一页或首页
    handleBack = async () => {
        const { redirectUrl, appId } = this.state;

        // 如果有appId，尝试检查是否已关注
        if (appId) {
            try {
                // 显示加载提示
                message.openLoading("正在验证关注状态...");

                // 先获取OpenID
                await login(); // 确保用户登录状态

                // 检查是否关注了公众号
                const response = await request.post<any>("/Wx/Subscribe/checkSubscribe", {
                    MpAppId: appId,
                });

                message.closeLoading();

                if (response.success && response.data && response.data.isSubscribed) {
                    // 已关注，返回原页面
                    message.success("关注成功，正在返回...");
                    this.navigateBack();
                } else {
                    // 未关注，提示用户
                    message.error("请先关注公众号后再返回");
                }
            } catch (error) {
                console.error("检查关注状态失败:", error);
                message.closeLoading();
                // 出错时仍然允许返回
                this.navigateBack();
            }
        } else {
            // 没有appId，直接返回
            this.navigateBack();
        }
    };

    // 导航返回方法
    navigateBack = () => {
        const { redirectUrl } = this.state;
        const cachedRedirectUrl = tool.data.get("follow-redirect-url");

        if (cachedRedirectUrl) {
            tool.data.remove("follow-redirect-url"); // 清除缓存
            Taro.redirectTo({ url: cachedRedirectUrl });
        } else if (redirectUrl) {
            Taro.redirectTo({ url: redirectUrl });
        } else {
            // 如果没有重定向URL，尝试返回上一页，如果没有上一页则跳转到公众号首页
            const pages = Taro.getCurrentPages();
            if (pages.length > 1) {
                Taro.navigateBack();
            } else {
                Taro.redirectTo({ url: "/pages/official-account/index" });
            }
        }
    };

    // 复制公众号名称
    handleCopyAccountName = () => {
        const { accountName } = this.state;

        Taro.setClipboardData({
            data: accountName,
            success: () => {
                message.success("公众号名称已复制到剪贴板");
            },
        });
    };

    render() {
        const { url, loading, showTips, accountName } = this.state;

        return (
            <Layout>
                {/* <TopNavBar title="关注公众号" homeClick={this.handleBack} /> */}
                <View className="follow-container">
                    {loading && (
                        <View className="loading-wrapper">
                            <View className="loading-spinner"></View>
                            <Text className="loading-text">加载中...</Text>
                        </View>
                    )}

                    {url && <WebView src={url} className="webview" onLoad={this.handleWebViewLoad} onError={this.handleWebViewError} />}

                    {showTips && (
                        <View className="tips-overlay">
                            <View className="tips-content">
                                <View className="tips-header">
                                    <Text className="tips-title">关注公众号</Text>
                                </View>

                                <View className="tips-body">
                                    <Text className="tips-text">请按照以下步骤操作：</Text>
                                    <Text className="tips-step">1. 点击右上角的"..."按钮</Text>
                                    <Text className="tips-step">2. 选择"复制链接"或"在浏览器中打开"</Text>
                                    <Text className="tips-step">3. 关注公众号 "{accountName}"</Text>
                                    <Text className="tips-step">4. 关注成功后，请重新扫描二维码进行操作</Text>

                                    <View className="tips-buttons">
                                        <Button className="copy-button" onClick={this.handleCopyAccountName}>
                                            复制公众号名称
                                        </Button>

                                        <Button className="back-button" onClick={this.handleBack}>
                                            我已关注，返回
                                        </Button>
                                    </View>
                                </View>
                            </View>
                        </View>
                    )}
                </View>
            </Layout>
        );
    }
}
