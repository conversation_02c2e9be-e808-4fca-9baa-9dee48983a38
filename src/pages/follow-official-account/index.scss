page {
    background-color: #f7f8fa;
    height: 100%;
}

.follow-container {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

.webview {
    width: 100%;
    height: 100%;
}

.loading-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #f7f8fa;
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #2563eb;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.loading-text {
    font-size: 28px;
    color: #666;
}

.tips-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 100;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 32px;
}

.tips-content {
    width: 100%;
    max-width: 600px;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tips-header {
    padding: 24px;
    background-color: #f8f8f8;
    border-bottom: 1px solid #eee;
}

.tips-title {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    text-align: center;
    display: block;
}

.tips-body {
    padding: 32px;
}

.tips-text {
    font-size: 28px;
    color: #333;
    margin-bottom: 24px;
    display: block;
}

.tips-step {
    font-size: 26px;
    color: #666;
    margin-bottom: 16px;
    display: block;
    line-height: 1.5;
    padding-left: 16px;
    position: relative;
    
    &:before {
        content: "•";
        position: absolute;
        left: 0;
        color: #2563eb;
    }
    
    &:last-of-type {
        margin-bottom: 32px;
    }
}

.tips-buttons {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 32px;
}

.copy-button {
    width: 100%;
    height: 88px;
    background-color: #f5f5f5;
    color: #333;
    font-size: 28px;
    border: none;
    border-radius: 44px;
    
    &::after {
        border: none;
    }
    
    &:active {
        opacity: 0.8;
    }
}

.back-button {
    width: 100%;
    height: 88px;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: #fff;
    font-size: 28px;
    border: none;
    border-radius: 44px;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    
    &::after {
        border: none;
    }
    
    &:active {
        opacity: 0.8;
        transform: translateY(2px);
        box-shadow: 0 2px 6px rgba(37, 99, 235, 0.2);
    }
}
