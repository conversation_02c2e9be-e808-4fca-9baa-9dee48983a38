import React, { useState } from "react";
import { View, Text, Textarea } from "@tarojs/components";
import { Dialog, Button } from "@nutui/nutui-react-taro";

interface PaymentConfirmModalProps {
    visible: boolean;
    amount: string;
    merchantName: string;
    remark?: string;
    onClose: () => void;
    onConfirm: () => void;
    onRemarkChange: (remark: string) => void;
}

const PaymentConfirmModal: React.FC<PaymentConfirmModalProps> = ({ visible, amount, merchantName, remark, onClose, onConfirm, onRemarkChange }) => {
    const [inputRemark, setInputRemark] = useState(remark || "");

    const handleRemarkChange = (e: any) => {
        const value = e.detail.value;
        if (value.length <= 200) {
            setInputRemark(value);
            onRemarkChange(value);
        }
    };

    return (
        <Dialog visible={visible} onClose={onClose}>
            <View className="payment-details">
                <Text className="merchant-name">{merchantName}</Text>
                <Text className="amount">¥{amount}</Text>
                <View className="remark-section">
                    <Text className="remark-label">备注</Text>
                    <Textarea className="remark-textarea" value={inputRemark} onInput={handleRemarkChange} maxlength={200} placeholder="请输入备注信息（选填）" />
                    <Text className="remark-count">{inputRemark.length}/200</Text>
                </View>
                <View className="button-group">
                    <Button onClick={onClose}>取消</Button>
                    <Button type="primary" onClick={onConfirm}>
                        确认
                    </Button>
                </View>
            </View>
        </Dialog>
    );
};

export default PaymentConfirmModal;
