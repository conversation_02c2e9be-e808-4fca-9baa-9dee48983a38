import React, { useState, useEffect } from "react";
import { View, Text, Input } from "@tarojs/components";
import { Button } from "@nutui/nutui-react-taro";
import Taro, { useRouter } from "@tarojs/taro";
import Layout from "@/components/Layout";
import PaymentConfirmModal from "@/pages/cashier/components/PaymentConfirmModal";
import "./index.scss";
import { base64ToGuid, QValue } from "@/utils/util";
import { login } from "@/utils/login";
import request from "@/service/request";
import { message } from "@/components/MessageApi/MessageApiSingleton";

interface CashierInfo {
    name: string;
    type: "dept" | "account";
}

const Cashier: React.FC = () => {
    const router = useRouter();
    const [amount, setAmount] = useState<string>("");
    const [cashierInfo, setCashierInfo] = useState<CashierInfo | null>(null);
    const [showPaymentModal, setShowPaymentModal] = useState(false);
    const [remark, setRemark] = useState<string>("");
    const [showRemarkInput, setShowRemarkInput] = useState(false);
    const [accountId, setAccountId] = useState<string>("");

    useEffect(() => {
        login();
        const sence = QValue();
        const decodedAccountId = base64ToGuid(sence) || "";
        setAccountId(decodedAccountId);

        request
            .post<any>("/Pay/Base/Account/getAccountInfo", {
                Id: decodedAccountId,
            })
            .then((res) => {
                if (res && res.success) {
                    setCashierInfo({
                        name: res.data.Name || "商户",
                        type: res.data.Type || "account",
                    });
                }
            });
    }, [router.params]);

    // 修改验证金额的函数
    const isValidAmount = (value: string): boolean => {
        const numberValue = parseFloat(value);
        return !isNaN(numberValue) && numberValue > 0 && numberValue <= 50000;
    };

    // 修改处理金额输入的函数
    const handleAmountChange = (value: string) => {
        // 只允许输入数字和小数点
        const reg = /^\d*\.?\d{0,2}$/;
        if (reg.test(value) || value === "") {
            // 检查金额是否超过50000
            const numberValue = parseFloat(value);
            if (!isNaN(numberValue) && numberValue > 50000) {
                message.error("单笔交易不能超过50000元");
                return;
            }
            setAmount(value);
        }
    };

    // 修改处理支付函数
    const handlePayment = () => {
        if (!isValidAmount(amount)) return;
        setShowPaymentModal(true);
    };

    // 添加确认支付处理函数
    const handleConfirmPayment = () => {
        setAmount("");
        setRemark("");
        setShowRemarkInput(false);
    };

    // 添加处理备注的函数
    const handleRemarkChange = (e: any) => {
        setRemark(e.detail.value);
    };

    const toggleRemarkInput = () => {
        setShowRemarkInput(!showRemarkInput);
    };

    return (
        <View className="cashier-page">
            <Layout>
                <View className="header">
                    {cashierInfo && (
                        <View className="merchant-info">
                            <View className="merchant-avatar">
                                <View className="avatar-placeholder" />
                            </View>
                            <View className="merchant-detail">
                                <Text className="merchant-name">{cashierInfo.name}</Text>
                                <Text className="merchant-tag">{cashierInfo.type === "dept" ? "门店收款" : "账户收款"}</Text>
                            </View>
                        </View>
                    )}
                </View>

                <View className="amount-card">
                    <View className="amount-display">
                        <Text className="currency-symbol">¥</Text>
                        <Text className="amount-text">{amount || "0.00"}</Text>
                    </View>

                    <View className="card-options">
                        <View className="remark-section" onClick={toggleRemarkInput}>
                            <Text className="remark-icon">{showRemarkInput ? "-" : "+"}</Text>
                            <Text className="remark-text">{remark ? `备注：${remark}` : "添加付款备注"}</Text>
                        </View>
                        {showRemarkInput && (
                            <View className="remark-input-container">
                                <Input className="remark-input" type="text" placeholder="请输入付款备注（选填）" value={remark} onInput={handleRemarkChange} maxlength={50} />
                            </View>
                        )}
                    </View>
                </View>

                <View className="keypad">
                    <View className="keypad-grid">
                        <View className="keypad-left">
                            {["1", "2", "3", "4", "5", "6", "7", "8", "9", ".", "0"].map((key) => (
                                <View key={key} className="key-item" onClick={() => handleAmountChange(amount + key)}>
                                    {key}
                                </View>
                            ))}
                        </View>
                        <View className="keypad-right">
                            <View className="delete-key" onClick={() => setAmount((prev) => prev.slice(0, -1))}>
                                ×
                            </View>
                            <Button
                                className={`confirm-payment-btn ${!isValidAmount(amount) ? "disabled" : ""}`}
                                block
                                type="primary"
                                disabled={!isValidAmount(amount)}
                                onClick={handlePayment}
                            >
                                {amount ? (
                                    <View className="vertical-text">
                                        <Text>确</Text>
                                        <Text>认</Text>
                                        <Text>支</Text>
                                        <Text>付</Text>
                                    </View>
                                ) : (
                                    <View className="vertical-text">
                                        <Text>输</Text>
                                        <Text>入</Text>
                                        <Text>金</Text>
                                        <Text>额</Text>
                                    </View>
                                )}
                            </Button>
                        </View>
                    </View>
                    <View className="safe-area-block" />
                </View>
            </Layout>

            <PaymentConfirmModal
                visible={showPaymentModal}
                amount={amount}
                merchantName={cashierInfo?.name || ""}
                remark={remark}
                onClose={() => setShowPaymentModal(false)}
                onConfirm={handleConfirmPayment}
                onRemarkChange={handleRemarkChange}
            />
        </View>
    );
};

export default Cashier;
