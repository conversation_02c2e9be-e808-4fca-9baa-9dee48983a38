page {
    font-family: "AlibabaPuHuiTi";
    // font-family: <PERSON>ti <PERSON>, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
}

.cashier-page {
    min-height: 100vh;
    background: #f7f8fa;

    .header {
        padding: 20px 24px;
        background: #fff;
        border-bottom: 1px solid #f0f0f0;

        .merchant-info {
            display: flex;
            align-items: center;

            .merchant-avatar {
                width: 48px;
                height: 48px;
                margin-right: 12px;

                .avatar-placeholder {
                    width: 100%;
                    height: 100%;
                    background: #ff6b2b;
                    border-radius: 8px;
                }
            }

            .merchant-detail {
                flex: 1;

                .merchant-name {
                    font-size: 30px;
                    color: #333;
                    font-weight: 500;
                    margin-bottom: 4px;
                    display: block;
                }

                .merchant-tag {
                    font-size: 22px;
                    color: #ff6b2b;
                    background: rgba(255, 107, 43, 0.1);
                    padding: 2px 8px;
                    border-radius: 4px;
                }
            }
        }
    }

    .amount-card {
        margin: 24px 20px;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
        overflow: hidden;

        .amount-display {
            padding: 60rpx 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fff;
            border-bottom: 1px solid #f5f5f5;

            .currency-symbol {
                font-size: 40px;
                margin-right: 8px;
            }

            .amount-text {
                font-size: 60rpx;
                font-weight: bold;
            }
        }

        .card-options {
            .remark-section {
                display: flex;
                align-items: center;
                padding: 16px 24px;
                border-bottom: 1px solid #f5f5f5;

                .remark-icon {
                    font-size: 24px;
                    color: #999;
                    margin-right: 8px;
                }

                .remark-text {
                    color: #999;
                    font-size: 26px;
                }
            }

            .limit-tip {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px 24px;
                font-size: 24px;
                color: #666;

                .arrow {
                    color: #999;
                }
            }
        }
    }

    .payment-options {
        background: #fff;
        padding: 0 24px;

        .remark-section {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #f0f0f0;

            .remark-icon {
                font-size: 24px;
                color: #999;
                margin-right: 8px;
            }

            .remark-text {
                color: #999;
                font-size: 26px;
            }
        }

        .limit-tip {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            font-size: 24px;
            color: #666;

            .arrow {
                color: #999;
            }
        }
    }

    .quick-amounts {
        margin-top: 16px;
        background: #fff;
        padding: 20px 24px;

        .section-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 16px;
            display: block;
        }

        .quick-amount-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;

            .quick-amount-item {
                background: #f7f8fa;
                border-radius: 8px;
                padding: 12px 8px;
                text-align: center;

                &.active {
                    background: #ff6b2b;
                    .amount,
                    .label {
                        color: #fff;
                    }
                }

                .amount {
                    font-size: 24px;
                    color: #333;
                    display: block;
                    margin-bottom: 4px;
                }

                .label {
                    font-size: 22px;
                    color: #666;
                }
            }
        }
    }

    .keypad {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #f5f5f5;
        padding: 24rpx;

        .keypad-grid {
            display: flex;
            gap: 24rpx;

            .keypad-left {
                flex: 3;
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 24rpx;

                .key-item {
                    background: #fff;
                    height: 108rpx;
                    border-radius: 16rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 48rpx;
                    font-weight: 500;

                    &:active {
                        background: #eee;
                    }

                    &.disabled {
                        opacity: 0.5;
                        pointer-events: none;
                    }
                }
            }

            .keypad-right {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 24rpx;

                .delete-key {
                    background: #fff;
                    height: 108rpx;
                    border-radius: 16rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 48rpx;

                    &:active {
                        background: #eee;
                    }
                }

                .confirm-payment-btn {
                    flex: 1;
                    min-height: 360rpx !important;
                    font-size: 36rpx !important;
                    font-weight: 500 !important;
                    border-radius: 16rpx !important;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .vertical-text {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        line-height: 1.5;

                        Text {
                            display: block;
                            writing-mode: vertical-lr;
                            letter-spacing: 8rpx;
                            text-align: center;
                        }
                    }

                    &:not(:disabled) {
                        background: #2b65d9 !important;
                        color: #fff !important;
                    }

                    &.disabled {
                        background: #cccccc !important;
                        color: #ffffff !important;
                        opacity: 1 !important;
                        cursor: not-allowed;
                    }
                }
            }
        }

        .safe-area-block {
            height: constant(safe-area-inset-bottom);
            height: env(safe-area-inset-bottom);
            width: 100%;
        }
    }
}

.remark-section {
    display: flex;
    align-items: center;
    padding: 12px 0;

    .remark-icon {
        margin-right: 8px;
        font-size: 20px;
        color: #666;
    }

    .remark-text {
        color: #666;
        font-size: 14px;
    }
}

.remark-input-container {
    padding: 16px 24px;
    background: #fff;

    .remark-input {
        width: 100%;
        height: 80rpx;
        background: #f7f8fa;
        border: 2rpx solid #e5e5e5;
        border-radius: 8rpx;
        padding: 0 24rpx;
        font-size: 28rpx;
        color: #333;
        box-sizing: border-box;

        &::placeholder {
            color: #999;
        }

        &:focus {
            border-color: #ff6b2b;
            background: #fff;
        }
    }
}

.payment-details {
    padding: 20px;

    .merchant-name {
        font-size: 16px;
        color: #333;
        margin-bottom: 12px;
    }

    .amount {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-bottom: 20px;
    }

    .remark-section {
        margin-bottom: 20px;

        .remark-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
            display: block;
        }

        .remark-textarea {
            width: 100%;
            height: 100px;
            padding: 8px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            font-size: 14px;
            background: #f5f5f5;
        }

        .remark-count {
            font-size: 12px;
            color: #999;
            text-align: right;
            margin-top: 4px;
            display: block;
        }
    }

    .button-group {
        display: flex;
        justify-content: flex-end;
        gap: 12px;

        .nutui-button {
            min-width: 80px;
        }
    }
}
