import { useRef, useState } from "react";
import "./index.scss";
import "@utils/app.scss";
import { Button } from "@nutui/nutui-react-taro";
import request from "@service/request";
import Taro, { useDidShow } from "@tarojs/taro";
import IconFont from "@components/iconfont";
import PdAgreement from "@components/pdAgreemen";
import { Input } from "@tarojs/components";
import Layout from "@components/Layout";
import { message } from "@components/MessageApi/MessageApiSingleton";
import { login } from "@utils/login";

const App = () => {
    const [phone, setPhone] = useState("");
    const [verificationCode, setVerificationCode] = useState("");
    const [buttonDisabled, setButtonDisabled] = useState(false);
    const [buttonText, setButtonText] = useState("立即登录");
    const [needReset, setNeedReset] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [tenantName, setTenantName] = useState("");
    const [tenantId, setTenantId] = useState("");
    const [countdown, setCountdown] = useState(0);
    const [isSendingCode, setIsSendingCode] = useState(false);

    const pdAgreemen = useRef<any>(null);
    const [hasShownAlert, setHasShownAlert] = useState(false);

    // 添加页面显示时的处理
    Taro.useDidHide(() => {
        setNeedReset(true);
    });

    useDidShow(() => {
        login().then(() => {
            getTenantInfo().then(() => {
                checkLogOn();
            });
        });
    });

    const getTenantInfo = () => {
        return request.post<API.Result<any>>("/Wx/WxConfig/getBindTenantInfo", {}).then((json) => {
            if (json && json.success) {
                setTenantName(json.data.TenantName || "");
                setTenantId(json.data.TenantId || "");
            }
        });
    };

    const checkLogOn = () => {
        if (needReset) {
            setButtonDisabled(false);
            setButtonText("立即登录");
            setNeedReset(false);
        }

        // 检查登录状态
        setIsLoading(true);
        if (!hasShownAlert) {
            login().then((data) => {
                // 检查UserId是否有效
                if (data && data.UserId && data.UserId !== "" && data.UserId !== "00000000-0000-0000-0000-000000000000") {
                    setIsLoading(false);
                    goPage();
                } else {
                    loginAlert();
                    setHasShownAlert(true);
                    setIsLoading(false);
                }
            });
        }
    };

    const loginAlert = () => {
        console.log("loginAlert");
        message.openDialog(
            "请注意",
            `本小程序是${tenantName}内部的管理系统的一部分，只允许驾校在${tenantName}登记了的教练利用个人身份验证登录，如果没有在完成登记的人员，请退出当前程序?`,
            () => {},
            "我已经知道了",
            "退出当前程序",
            () => {
                Taro.exitMiniProgram();
            }
        );
    };

    const startCountdown = () => {
        setCountdown(60);
        const timer = setInterval(() => {
            setCountdown((prev) => {
                if (prev <= 1) {
                    clearInterval(timer);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);
    };

    const sendVerificationCode = () => {
        if (tenantId == "" || !tenantId) {
            message.error("系统暂时不支持当前小程序", "提示", () => {});
            return;
        }
        if (!pdAgreemen.current.state.agreeChecked) {
            message.openDialog("协议确认", "请先阅读并确认同意《用户服务协议与隐私政策》", () => {
                pdAgreemen.current.showAgreement();
            });
            return;
        }
        if (!phone || phone.length !== 11) {
            message.error("请输入正确的手机号码!", "输入错误", () => {});
            return;
        }
        setVerificationCode("");
        setIsSendingCode(true);
        Taro.login({
            success: (res) => {
                if (res.code) {
                    request
                        .put<API.Result<any>>(`/Wx/Phone/sendVerificationCode/${res.code}`, {
                            phone: phone,
                            TenantId: tenantId,
                        })
                        .then((json) => {
                            if (json && json.success) {
                                message.success(json.message, "提示", () => {});
                                startCountdown();
                            } else {
                                json && message.error(json.message, "发送失败", () => {});
                            }
                        })
                        .catch(() => {
                            message.error("发送失败，请稍后重试", "错误", () => {});
                        })
                        .finally(() => {
                            setIsSendingCode(false);
                        });
                } else {
                    message.error("获取登录凭证失败", "错误", () => {});
                    setIsSendingCode(false);
                }
            },
            fail: () => {
                message.error("登录失败，请重试", "错误", () => {});
                setIsSendingCode(false);
            },
        });
    };

    const logOn = () => {
        if (!pdAgreemen.current.state.agreeChecked) {
            message.openDialog("协议确认", "请先阅读并确认同意《用户服务协议与隐私政策》", () => {
                pdAgreemen.current.showAgreement(); //.showDetail = true;
            });
        } else if (!phone || phone.length !== 11) {
            message.error("请输入正确的手机号码!", "输入错误", () => {});
        } else if (!verificationCode || verificationCode.length !== 4) {
            message.error("请输入正确的验证码!", "输入错误", () => {});
        } else {
            setButtonDisabled(true);
            setButtonText("正在验证中");
            Taro.login({
                success: (res) => {
                    if (res.code) {
                        request
                            .put<API.Result<any>>(`/Wx/Phone/verifyAndLogin/${res.code}`, {
                                phone: phone,
                                ValidCode: verificationCode,
                                TenantId: tenantId,
                            })
                            .then((json) => {
                                if (json && json.success) {
                                    setButtonText("正在跳转");
                                    setTimeout(() => {
                                        setButtonDisabled(false);
                                        setButtonText("立即登录");
                                    }, 3000);
                                    Taro.setStorageSync("accessToken", json.data.accessToken);
                                    goPage();
                                } else {
                                    setButtonDisabled(false);
                                    setButtonText("立即登录");
                                    json && message.error(json.message, "登录失败", () => {});
                                }
                            })
                            .catch(() => {
                                setTimeout(() => {
                                    setButtonDisabled(false);
                                    setButtonText("立即登录");
                                }, 3000);
                            });
                    }
                },
            });
        }
    };

    /**
     * 根据当前的状态 跳转至相应的页面
     */
    const goPage = () => {
        Taro.navigateTo({
            url: "/subpackages/student/user/student/tenantList/menu/index",
        });
    };

    return (
        <Layout>
            <view className="flex-col page">
                <view className="flex-col flex-1 group">
                    <view className="flex-col justify-start items-center self-start relative section">
                        <view className="title-container">
                            <text className="main-title">{tenantName}</text>
                            <text className="sub-title">教练管理系统</text>
                        </view>
                    </view>
                    {isLoading ? (
                        <view className="flex-col justify-center items-center self-stretch relative group_3" style={{ height: "50vh" }}>
                            <view className="loading-container" style={{ textAlign: "center" }}>
                                <view style={{ marginBottom: "20rpx" }}>
                                    <view className="loading-spinner"></view>
                                </view>
                                <text style={{ color: "#666", fontSize: "28rpx" }}>正在获取登录状态...</text>
                            </view>
                        </view>
                    ) : (
                        <view className="flex-col justify-start self-stretch relative group_3">
                            <view className="section_2">
                                <view className="decoration" />
                                <view className="content-wrapper">
                                    <view className="flex-col group_4">
                                        <view className="flex-row justify-between self-stretch group_5">
                                            <text className="font_2 text_2">教练登录</text>
                                        </view>
                                        <view className="self-start section_3"></view>
                                    </view>
                                    <view className="flex-col group_6">
                                        <view
                                            className="flex-row justify-start items-center text-wrapper"
                                            style={{
                                                paddingLeft: "30rpx",
                                                paddingRight: "30rpx",
                                                display: "flex",
                                                border: "0",
                                                height: "88rpx",
                                            }}
                                        >
                                            <IconFont name="cellphone-iphone" color="#999" size={38} />
                                            <Input
                                                type="number"
                                                maxlength={11}
                                                className="font_3 text_4"
                                                placeholder="请输入手机号码"
                                                style={{
                                                    marginLeft: "20rpx",
                                                    flex: 1,
                                                    height: "88rpx",
                                                }}
                                                onInput={(e) => {
                                                    setPhone(e.detail.value);
                                                }}
                                                defaultValue={phone}
                                            ></Input>
                                        </view>
                                        <view
                                            className="mt-24 flex-row justify-start items-center text-wrapper"
                                            style={{
                                                paddingLeft: "30rpx",
                                                paddingRight: "30rpx",
                                                display: "flex",
                                                border: "0",
                                                height: "88rpx",
                                            }}
                                        >
                                            <IconFont name="comment-text-outline" color="#999" size={38} />
                                            <Input
                                                type="number"
                                                maxlength={6}
                                                className="font_3 text_4"
                                                placeholder="请输入验证码"
                                                style={{
                                                    marginLeft: "20rpx",
                                                    flex: 1,
                                                    height: "88rpx",
                                                }}
                                                onInput={(e) => {
                                                    setVerificationCode(e.detail.value);
                                                }}
                                                defaultValue={verificationCode}
                                            ></Input>
                                            <Button
                                                size="small"
                                                type="primary"
                                                disabled={countdown > 0 || isSendingCode}
                                                loading={isSendingCode}
                                                onClick={sendVerificationCode}
                                                style={{
                                                    width: "200rpx",
                                                    height: "60rpx",
                                                    fontSize: "24rpx",
                                                    marginLeft: "20rpx",
                                                    padding: "0",
                                                    lineHeight: "60rpx",
                                                }}
                                            >
                                                {countdown > 0 ? `${countdown}秒后重发` : "获取验证码"}
                                            </Button>
                                        </view>
                                    </view>

                                    <Button
                                        className="font_3 text_7 text-wrapper_3"
                                        style={{
                                            border: 0,
                                            width: "100%",
                                        }}
                                        onClick={logOn}
                                        disabled={buttonDisabled}
                                    >
                                        {buttonText}
                                    </Button>
                                    <view className="group_7">
                                        <view className="group_9">
                                            <PdAgreement ref={pdAgreemen}></PdAgreement>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    )}
                    <view className="bottom-text-container">
                        <view className="flex-row justify-evenly items-center">
                            <view className="section_4_bottom"></view>
                            <text className="font_4 text_13_bottom">{tenantName}</text>
                            <view className="section_5_bottom"></view>
                        </view>
                    </view>
                </view>
            </view>
            <view className="safe__area"></view>
        </Layout>
    );
};
export default App;
