.free-pay-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%);
    padding: 30rpx;
    font-family: <PERSON>, <PERSON><PERSON>, STYuanti-SC-Regular;
    position: relative;
    overflow: hidden;

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 20% 20%, rgba(24, 144, 255, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(54, 207, 201, 0.03) 0%, transparent 50%),
            linear-gradient(
                45deg,
                rgba(255, 255, 255, 0.1) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(255, 255, 255, 0.1) 75%,
                transparent 75%,
                transparent
            );
        background-size: 100% 100%, 100% 100%, 60rpx 60rpx;
        z-index: 0;
    }

    .loading {
        text-align: center;
        padding: 80rpx;
        color: #666;
        font-size: 28rpx;
        position: relative;
        z-index: 1;
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 120rpx 40rpx;
        text-align: center;
        position: relative;
        z-index: 1;
        min-height: 400rpx;

        .loading-spinner {
            margin-bottom: 40rpx;

            .spinner {
                width: 80rpx;
                height: 80rpx;
                border: 6rpx solid rgba(24, 144, 255, 0.1);
                border-top: 6rpx solid #1890ff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
        }

        .loading-text {
            color: #666;
            font-size: 28rpx;
            margin-bottom: 40rpx;
            font-weight: 400;
            letter-spacing: 1rpx;
        }

        .loading-dots {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12rpx;

            .dot {
                width: 12rpx;
                height: 12rpx;
                background: #1890ff;
                border-radius: 50%;
                animation: pulse 1.4s ease-in-out infinite both;

                &.dot1 {
                    animation-delay: -0.32s;
                }

                &.dot2 {
                    animation-delay: -0.16s;
                }

                &.dot3 {
                    animation-delay: 0s;
                }
            }
        }
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

    @keyframes pulse {
        0%,
        80%,
        100% {
            transform: scale(0.6);
            opacity: 0.5;
        }
        40% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .order-card {
        background-color: rgba(255, 255, 255, 0.95);
        padding: 24rpx 40rpx;
        margin-top: 24rpx;
        // margin: 24rpx;
        border-radius: 24rpx;
        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
        position: relative;
        overflow: hidden;
        z-index: 1;
        backdrop-filter: blur(10px);

        &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6rpx;
            background: linear-gradient(90deg, #1890ff, #36cfc9);
        }

        .order-info {
            .info-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 24rpx 0;
                border-bottom: 1rpx solid #f0f0f0;

                &:last-child {
                    border-bottom: none;
                }

                .label {
                    color: #999;
                    font-size: 24rpx;
                    font-weight: 400;
                }

                .value {
                    color: #666;
                    font-size: 24rpx;
                    font-weight: 400;

                    &.amount {
                        color: #1890ff;
                        font-size: 48rpx;
                        font-weight: 600;
                        letter-spacing: 1rpx;
                    }
                }
            }
        }
    }

    .payment-button {
        padding: 40rpx 0rpx;
        margin-top: 40rpx;
        position: relative;
        z-index: 1;

        .confirm-payment-btn {
            height: 88rpx;
            border-radius: 44rpx;
            font-size: 32rpx;
            background: #1890ff;
            border: none;
            box-shadow: none;
            line-height: normal;

            &:active {
                transform: none;
                box-shadow: none;
            }
        }
    }
}
