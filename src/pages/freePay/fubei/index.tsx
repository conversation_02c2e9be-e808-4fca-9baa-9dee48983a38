import React, { useState, useEffect } from "react";
import { View, Text, Ad } from "@tarojs/components";
import { Button } from "@nutui/nutui-react-taro";
import Taro, { useRouter } from "@tarojs/taro";
import Layout from "@/components/Layout";
import "./index.scss";
import { login } from "@utils/login";
import request from "@/service/request";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import { QValue } from "@/utils/util";
import TopNavBar from "@/components/topNavBar";

interface OrderInfo {
    orderNo: string;
    price: number;
    remark: string;
    appId: string;
    callback?: string;
}

const FreePay: React.FC = () => {
    const router = useRouter();
    const [orderInfo, setOrderInfo] = useState<OrderInfo | null>(null);
    const [loading, setLoading] = useState(false);
    const [secondAdLoaded, setSecondAdLoaded] = useState(false);
    const [showContent, setShowContent] = useState(false);

    useEffect(() => {
        login();
        const appId = QValue("appid");
        const ordernum = QValue("ordernum");
        const price = QValue("price");
        const remark = QValue("remark");
        const callback = QValue("callback");

        if (!appId || !ordernum) {
            message.error("参数错误");
            return;
        }

        // 直接使用URL参数设置订单信息
        setOrderInfo({
            orderNo: ordernum,
            price: Number(price) || 0,
            remark: remark || "",
            appId: appId,
            callback: callback,
        });
        setShowContent(true);
    }, [router.params]);

    // 第二个广告加载完成处理
    const handleSecondAdLoad = () => {
        if (!secondAdLoaded) {
            setSecondAdLoaded(true);
        }
    };

    // 第二个广告加载失败处理
    const handleSecondAdError = () => {
        if (!secondAdLoaded) {
            setSecondAdLoaded(true);
        }
    };

    // 处理支付
    const handlePayment = () => {
        if (!orderInfo) return;

        setLoading(true);
        request
            .post<any>("/Pay/FbPay/freePay", {
                AppId: orderInfo.appId,
                OrderNo: orderInfo.orderNo,
                Price: orderInfo.price,
                Remark: orderInfo.remark,
                Callback: orderInfo.callback,
            })
            .then((res) => {
                if (res && res.success) {
                    const payData = res.data.sign_package;
                    // 调用微信支付
                    Taro.requestPayment({
                        timeStamp: payData.timeStamp,
                        nonceStr: payData.nonceStr,
                        package: payData.package,
                        signType: payData.signType,
                        paySign: payData.paySign,
                        success: () => {
                            message.success("支付成功");
                            // 支付成功后返回上一页
                            Taro.navigateBack();
                        },
                        fail: (err) => {
                            console.error("支付失败:", err);
                            message.error("支付失败，请重试");
                        },
                    });
                } else {
                    message.error(res?.message || "支付失败");
                }
            })
            .catch(() => {
                message.error("支付失败，请重试");
            })
            .finally(() => {
                setLoading(false);
            });
    };

    if (!orderInfo) {
        return (
            <View className="free-pay-page">
                <Layout>
                    <View className="loading-container">
                        <View className="loading-spinner">
                            <View className="spinner"></View>
                        </View>
                        <Text className="loading-text">页面加载中...</Text>
                        <View className="loading-dots">
                            <View className="dot dot1"></View>
                            <View className="dot dot2"></View>
                            <View className="dot dot3"></View>
                        </View>
                    </View>
                </Layout>
            </View>
        );
    }

    return (
        <Layout>
            <TopNavBar title="在线付款" homeClick={() => Taro.navigateBack()} />
            <View className="free-pay-page">
                {!showContent ? (
                    <View className="loading-container">
                        <View className="loading-spinner">
                            <View className="spinner"></View>
                        </View>
                        <Text className="loading-text">订单信息加载中...</Text>
                        <View className="loading-dots">
                            <View className="dot dot1"></View>
                            <View className="dot dot2"></View>
                            <View className="dot dot3"></View>
                        </View>
                    </View>
                ) : (
                    <>
                        <View className="order-card">
                            <View className="order-info">
                                <View className="info-item">
                                    <Text className="label">订单编号</Text>
                                    <Text className="value">{orderInfo.orderNo}</Text>
                                </View>
                                <View className="info-item">
                                    <Text className="label">订单金额</Text>
                                    <Text className="value amount">¥{orderInfo.price.toFixed(2)}</Text>
                                </View>
                                {orderInfo.remark && (
                                    <View className="info-item">
                                        <Text className="label">订单备注</Text>
                                        <Text className="value">{orderInfo.remark}</Text>
                                    </View>
                                )}
                            </View>
                        </View>

                        <View className="payment-button">
                            <Button className="confirm-payment-btn" block type="primary" loading={loading} onClick={handlePayment}>
                                确认支付
                            </Button>
                        </View>
                    </>
                )}
                <View className="ad-container">
                    <Ad unitId="adunit-baaef7f62cb3c7e0" onLoad={handleSecondAdLoad} onError={handleSecondAdError} />
                </View>
            </View>
        </Layout>
    );
};

export default FreePay;
