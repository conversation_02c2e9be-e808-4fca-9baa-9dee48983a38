import Taro, { useReady } from "@tarojs/taro";
import React, { useEffect, useState } from "react";
import "./index.scss";
import "@utils/app.scss";
import { login } from "@utils/login";
import Layout from "@components/Layout";
import { getUserInfoById, QrCodeLogin, confirmLogin, getUserList, loginByPhone } from "./service";
import { message } from "@components/MessageApi/MessageApiSingleton";
import { Picker } from "@nutui/nutui-react-taro";

interface WxUserInfo {
    Account: string;
    RealName: string;
    TenantName: string;
}

const App = () => {
    const [error, setError] = useState<string>("");
    const [loading, setLoading] = useState<boolean>(true);
    const [wxUserInfo, setWxUserInfo] = useState<WxUserInfo | null>(null);
    const [loadingText, setLoadingText] = useState<string>("加载中...");

    const [userInfo, setUserInfo] = useState<API.UserInfo>(Object);
    const [isSuccess, setIsSuccess] = useState<boolean | null>(null);
    const [resultMessage, setResultMessage] = useState<string>("");
    const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
    const [qrCodeId, setQrCodeId] = useState<string>("");

    // 添加扫码loading状态
    const [scanLoading, setScanLoading] = useState<boolean>(false);

    const [userList, setUserList] = useState<any[]>([]);
    const [selectUserOpen, setSelectUserOpen] = useState(false);

    const checkQrCode = async (id: string) => {
        try {
            setQrCodeId(id);
            setLoading(true); // 确保进入加载状态
            setError(""); // 先清除错误状态

            const response = await QrCodeLogin.scanConfirm({ qrCodeId: id });
            if (response.success) {
                setLoadingText("正在加载当前登录用户信息");
                try {
                    const user = await login();
                    setUserInfo(user);
                    const userResponse = await getUserInfoById(user?.UserId || "");
                    if (userResponse.success) {
                        setWxUserInfo({
                            Account: userResponse.data.Account,
                            RealName: userResponse.data.RealName,
                            TenantName: userResponse.data.TenantName,
                        });
                        setLoading(false);
                    } else {
                        setError(userResponse.message || "获取用户信息失败");
                        setWxUserInfo(null);
                        setLoading(false);
                    }
                } catch (userErr) {
                    console.error("获取用户信息失败:", userErr);
                    setError("获取用户信息失败，请稍后重试");
                    setWxUserInfo(null);
                    setLoading(false);
                }
            } else {
                setError(response.message || "二维码状态查询失败");
                setWxUserInfo(null);
                setLoading(false);
            }
        } catch (err) {
            console.error("扫码确认失败:", err);
            setError("网络请求失败，请稍后重试");
            setWxUserInfo(null);
            setLoading(false);
        }
    };

    useEffect(() => {
        const router = Taro.getCurrentInstance().router;
        const initialQrCodeId = router?.params?.qrCodeId;

        if (initialQrCodeId) {
            checkQrCode(initialQrCodeId);
        } else {
            setError("页面参数错误：缺少二维码ID");
            setLoading(false);
        }
    }, []);

    const handleScan = () => {
        setScanLoading(true);
        Taro.scanCode({
            success: (res: any) => {
                console.log("scanCode", res);
                const path = res.path || "";

                // 直接使用字符串处理
                const [pathPart, queryPart] = path.split("?");
                if (pathPart.endsWith("pages/webLogin/index") && queryPart) {
                    const qrCodeId = queryPart.split("=")[1];
                    if (qrCodeId) {
                        setLoadingText("正在处理扫码结果...");
                        checkQrCode(qrCodeId);
                    } else {
                        message.error("无效的二维码参数");
                    }
                } else {
                    message.error("无效的二维码");
                }
            },
            fail: (err) => {
                console.error("扫码失败:", err);
                message.openToast("扫码失败，请重试");
            },
            complete: () => {
                setScanLoading(false);
            },
        });
    };

    // 处理切换账号
    const handleSwitchAccount = async () => {
        try {
            message.openLoading("加载中...");
            // 先获取用户列表
            const response = await getUserList();
            message.closeLoading();

            if (response.success && response.data) {
                if (response.data.length > 0) {
                    // 有账号列表，显示选择器
                    const users = response.data.map((user) => ({
                        value: user.UserId,
                        text: `${user.TenantName} (${user.Account})`,
                    }));
                    setUserList(users);
                    setSelectUserOpen(true);
                } else {
                    // 没有账号，提示错误
                    message.openToast("没有可用的账号");
                }
            } else {
                message.openToast("获取账号列表失败");
            }
        } catch (error) {
            console.error("获取账号列表失败:", error);
            message.openToast("获取账号列表失败");
            message.closeLoading();
        }
    };

    // 处理选择用户
    const handleSelectUser = async (userId: string) => {
        try {
            message.openLoading("登录中...");
            const response = await loginByPhone({ userId });

            if (response.success) {
                // 登录成功后重新获取用户信息
                const user = await login();
                setUserInfo(user);
                const userResponse = await getUserInfoById(user?.UserId || "");
                if (userResponse.success) {
                    setWxUserInfo({
                        Account: userResponse.data.Account,
                        RealName: userResponse.data.RealName,
                        TenantName: userResponse.data.TenantName,
                    });
                    message.openToast("切换成功");
                } else {
                    message.openToast("获取用户信息失败");
                }
            } else {
                message.openToast(response.message || "切换账号失败");
            }
        } catch (error) {
            console.error("切换账号失败:", error);
            message.openToast("切换账号失败");
        } finally {
            message.closeLoading();
        }
    };

    if (loading) {
        return (
            <Layout>
                <view className="page">
                    <view className="section"></view>
                    <view className="loading-container">
                        <view className="loading-content">
                            <view className="loading-icon" style={{ marginBottom: "70rpx" }}></view>
                            <view className="loading-text">{loadingText}</view>
                        </view>
                    </view>
                </view>
            </Layout>
        );
    }

    if (error) {
        return (
            <Layout>
                <view className="page">
                    <view className="section"></view>
                    <view className="error-page">
                        <view className="decoration-dots" />
                        <view className="decoration-circles">
                            <view className="circle circle-1" />
                            <view className="circle circle-2" />
                            <view className="circle circle-3" />
                        </view>

                        <view className="error-content">
                            <view className="error-icon">
                                <i className="iconfont icon-warning" />
                            </view>
                            <h2 className="error-title">操作提示</h2>
                            <p className="error-message">{error}</p>
                            <view className="error-actions">
                                <view className={`text-wrapper ${scanLoading ? "disabled" : ""}`} onClick={!scanLoading ? handleScan : undefined}>
                                    <view className="font_2">{scanLoading ? "扫码处理中..." : "重新扫码"}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </Layout>
        );
    }

    if (isSuccess !== null) {
        return (
            <Layout>
                <view className="page">
                    <view className="section"></view>
                    <view className="result-page">
                        <view className="result-content">
                            <view className={`result-icon ${isSuccess ? "success" : "error"}`} />
                            <view className="result-title">{isSuccess ? "登录成功" : "登录失败"}</view>
                            <view className="result-message">{isSuccess ? "请返回网页进行操作" : resultMessage}</view>
                        </view>
                    </view>
                </view>
            </Layout>
        );
    }

    if (wxUserInfo) {
        return (
            <Layout>
                <view className="page">
                    <view className="section"></view>
                    <view className="user-info-container">
                        <view className="user-info-content">
                            <view className="title">当前登录账户</view>
                            <view className="info-content">
                                <view className="info-item">
                                    <text className="label">账号：</text>
                                    <text className="value">{wxUserInfo.Account}</text>
                                </view>
                                <view className="info-item">
                                    <text className="label">姓名：</text>
                                    <text className="value">{wxUserInfo.RealName}</text>
                                </view>
                                <view className="info-item">
                                    <text className="label">公司：</text>
                                    <text className="value">{wxUserInfo.TenantName}</text>
                                </view>
                            </view>
                        </view>
                        <view className="switch-account-btn" onClick={handleSwitchAccount}>
                            <text className="btn-text">切换账号</text>
                        </view>
                        <view
                            className={`confirm-btn ${confirmLoading ? "loading" : ""}`}
                            onClick={async () => {
                                if (confirmLoading) return;
                                try {
                                    setConfirmLoading(true);

                                    if (!qrCodeId || !userInfo?.UserId) {
                                        setIsSuccess(false);
                                        setResultMessage("参数错误，请重试");
                                        return;
                                    }

                                    const response = await confirmLogin({
                                        qrCodeId,
                                        userId: userInfo.UserId,
                                    });

                                    if (response.success) {
                                        setIsSuccess(true);
                                    } else {
                                        setIsSuccess(false);
                                        setResultMessage(response.message || "确认登录失败");
                                    }
                                } catch (err) {
                                    console.error("确认登录失败:", err);
                                    setIsSuccess(false);
                                    setResultMessage("网络请求失败，请稍后重试");
                                } finally {
                                    setConfirmLoading(false);
                                }
                            }}
                        >
                            <text className="btn-text">{confirmLoading ? "登录中..." : "确认登录"}</text>
                        </view>
                        <Picker
                            visible={selectUserOpen}
                            options={userList}
                            onConfirm={(list, values) => {
                                setSelectUserOpen(false);
                                if (values[0]) {
                                    handleSelectUser(values[0].toString());
                                }
                            }}
                            onClose={() => setSelectUserOpen(false)}
                        />
                    </view>
                </view>
            </Layout>
        );
    }

    return (
        <Layout>
            <view className="page">
                <view className="section"></view>
            </view>
        </Layout>
    );
};

export default App;
