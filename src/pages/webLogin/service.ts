import request from "@service/request";

interface ScanConfirmResponse {
    success: boolean;
    message?: string;
}

export const QrCodeLogin = {
    scanConfirm: (data: { qrCodeId: string }) => request.post<ScanConfirmResponse>("/Auth/QrCodeStatus/scanConfirm", data),
};

interface WxUserResponse {
    success: boolean;
    data: {
        UserId?: string;
    };
}

export const WxAuthService = {
    getWxUser: () => request.post<WxUserResponse>("/Wx/WxAuth/getWxUser"),
};

interface UserInfo {
    userName: string;
    RealName: string;
    TenantName: string;
    Account: string;
}

interface UserInfoResponse {
    success: boolean;
    message?: string;
    data: UserInfo;
}

/**
 * 通过用户ID获取用户信息
 * @param userId 用户ID
 * @returns 用户信息
 */
export const getUserInfoById = async (userId: string): Promise<UserInfoResponse> => {
    try {
        const response = await request.post<UserInfoResponse>(`/Auth/UserInfo/${userId}`);
        return response;
    } catch (error) {
        console.error("获取用户信息失败:", error);
        throw error;
    }
};

// 添加确认登录接口
export const confirmLogin = async (params: { qrCodeId: string; userId: string }) => {
    return request.post<API.Result<any>>("/Auth/QrCodeStatus/confirmLogin", params);
};

// 修改获取用户列表的接口，添加返回类型
interface UserListItem {
    UserId: string;
    TenantName: string;
    Account: string;
}

export const getUserList = () => {
    return request.post<API.Result<any[]>>("/Wx/Login/getUserList");
};

// 添加手机号登录的接口
export const loginByPhone = (params: { userId: string }) => {
    return request.put<API.Result<any>>("/Wx/Login/loginByPhone", params);
};
