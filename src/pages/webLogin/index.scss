.page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: #ffffff;
    z-index: 1;

    font-family: "AlibabaPuHuiTi";
    // font-family: <PERSON><PERSON>, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
    display: flex;
    flex-direction: column;
}

.section {
    width: 100%;
    padding-bottom: 56.25%;
    background-image: url("https://cdn.51panda.com/wx/index/17372846228951652320.png");
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-position: center top;
    position: relative;
    flex-shrink: 0;
}

.error-page {
    // min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #ffffff;
    padding: 0 40rpx;
    position: relative;
    overflow: hidden;

    font-family: "AlibabaPuHuiTi";
    // font-family: <PERSON><PERSON>, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";

    .error-content {
        text-align: center;
    }

    .error-icon {
        margin-bottom: 48rpx;

        .iconfont {
            font-size: 180rpx;
            color: #ff4d4f;
        }
    }

    .error-title {
        font-size: 52rpx;
        color: #333;
        margin-bottom: 32rpx;
        font-weight: 600;

        font-family: "AlibabaPuHuiTi";
        // font-family: Yuanti SC, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
    }

    .error-message {
        font-size: 32rpx;
        color: #666;
        margin-bottom: 100rpx;

        font-family: "AlibabaPuHuiTi";
        // font-family: Yuanti SC, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
    }

    .error-actions {
        .text-wrapper {
            margin-right: 20rpx;
            padding: 32rpx 0;
            background-color: #3264ed;
            border-radius: 20rpx;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            &.disabled {
                pointer-events: none;
                cursor: not-allowed;
                opacity: 0.7;
            }

            &.loading {
                position: relative;

                .loading-dots {
                    display: inline-block;
                    &:after {
                        content: "...";
                        position: absolute;
                        animation: loading 1.5s infinite;
                    }
                }

                .loading-icon {
                    display: inline-block;
                    width: 32rpx;
                    height: 32rpx;
                    margin-left: 8rpx;
                    border: 3rpx solid #fff;
                    border-top-color: transparent;
                    border-radius: 50%;
                    animation: spin 0.8s linear infinite;
                    vertical-align: middle;
                }
            }
        }

        .font_2 {
            font-size: 32rpx;

            font-family: "AlibabaPuHuiTi";
            // font-family: Yuanti SC, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
            line-height: 29.16rpx;
            color: #ffffff;
            display: flex;
            align-items: center;

            .iconfont {
                margin-left: 12rpx;
                font-size: 28rpx;
            }
        }
    }
}

@keyframes float {
    0%,
    100% {
        transform: translateY(0) rotate(0);
    }
    50% {
        transform: translateY(-16rpx) rotate(2deg);
    }
}

@keyframes bgFloat {
    0% {
        transform: rotate(-8deg) scale(1);
    }
    100% {
        transform: rotate(-6deg) scale(1.05);
    }
}

@keyframes dotFloat {
    0%,
    100% {
        transform: translate(0, 0);
    }
    50% {
        transform: translate(10rpx, -10rpx);
    }
}

@keyframes pulse {
    0%,
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.5;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.3;
    }
}

@keyframes circleFloat {
    0%,
    100% {
        transform: translate(0, 0) scale(1);
    }
    50% {
        transform: translate(20rpx, -20rpx) scale(1.1);
    }
}

@keyframes borderPulse {
    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.5;
    }
}

.loading-container {
    margin-bottom: 40rpx;
    // display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx;
    flex: 1;
}

.loading-content {
    text-align: center;
}

.loading-icon {
    width: 80rpx;
    height: 80rpx;
    margin: 0 auto 20rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #3264ed;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    font-size: 28rpx;
    color: #666;

    font-family: "AlibabaPuHuiTi";
    // font-family: Yuanti SC, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.user-info-container {
    padding: 40rpx;
    flex: 1;

    .user-info-content {
        background: #ffffff;
        border-radius: 20rpx;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
        overflow: hidden;

        .title {
            height: 88rpx;
            padding: 0 40rpx;
            display: flex;
            align-items: center;
            font-size: 32rpx;
            color: #333;
            font-weight: 500;

            font-family: "AlibabaPuHuiTi";
            // font-family: Yuanti SC, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
            border-bottom: 2rpx solid #f5f5f5;
        }

        .info-content {
            padding: 40rpx;

            .info-item {
                margin-bottom: 30rpx;
                display: flex;
                align-items: center;

                .label {
                    color: #666;
                    font-size: 28rpx;
                    width: 120rpx;
                }

                .value {
                    color: #333;
                    font-size: 28rpx;
                    flex: 1;
                    font-weight: 500;
                }
            }
        }
    }

    .confirm-btn {
        margin-top: 24rpx;
        width: 100%;
        height: 88rpx;
        background: #3264ed;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
        line-height: normal;

        &.loading {
            opacity: 0.8;
            pointer-events: none;

            &::after {
                content: "";
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                animation: loading 1.5s infinite;
            }
        }

        .btn-text {
            color: #ffffff;
            font-size: 32rpx;
            font-weight: 500;

            font-family: "AlibabaPuHuiTi";
            // font-family: Yuanti SC, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
            line-height: normal;
        }
    }

    .switch-account-btn {
        margin-top: 50rpx;
        width: 100%;
        height: 88rpx;
        background: #f5f5f5;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: normal;

        .btn-text {
            color: #666666;
            font-size: 32rpx;
            font-weight: 500;

            font-family: "AlibabaPuHuiTi";
            // font-family: Yuanti SC, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
            line-height: normal;
        }
    }
}

@keyframes loading {
    from {
        left: -100%;
    }
    to {
        left: 100%;
    }
}

.result-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx;
    width: 100%;
    // margin-top: 200rpx;

    .result-content {
        text-align: center;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .result-icon {
        margin-bottom: 88rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        &.error {
            width: 160rpx;
            height: 160rpx;
            border-radius: 50%;
            background: rgba(255, 77, 79, 0.08);
            position: relative;
            margin: 0 auto;

            &::before,
            &::after {
                content: "";
                position: absolute;
                width: 80rpx;
                height: 6rpx;
                background: #ff4d4f;
                border-radius: 3rpx;
                transform-origin: center;
            }

            &::before {
                transform: rotate(45deg);
            }

            &::after {
                transform: rotate(-45deg);
            }
        }

        &.success {
            width: 160rpx;
            height: 160rpx;
            border-radius: 50%;
            background: #07c160;
            position: relative;
            margin: 0 auto;

            &::after {
                content: "";
                position: absolute;
                top: 50%;
                left: 50%;
                width: 36rpx;
                height: 72rpx;
                border-right: 12rpx solid #fff;
                border-bottom: 12rpx solid #fff;
                transform: translate(-50%, -58%) rotate(45deg);
                transform-origin: center;
            }
        }
    }

    .result-title {
        font-size: 40rpx;
        color: #333;
        margin-top: 60rpx;
        margin-bottom: 24rpx;
        font-weight: 500;
    }

    .result-message {
        font-size: 28rpx;
        color: #666;
    }
}
