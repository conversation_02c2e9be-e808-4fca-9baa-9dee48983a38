import React from "react";
import { View, Text } from "@tarojs/components";
import "./index.scss";

const TestStyleIsolation: React.FC = () => {
    return (
        <View className="test-isolation-page">
            <Text className="test-title">样式隔离测试页面</Text>
            
            {/* 这些类名应该不会被学员列表页面的样式影响 */}
            <View className="flex-col">
                <Text>这个flex-col应该没有样式</Text>
            </View>
            
            <View className="list">
                <Text>这个list应该没有样式</Text>
            </View>
            
            <View className="font_3">
                <Text>这个font_3应该没有样式</Text>
            </View>
            
            <View className="group_2">
                <Text>这个group_2应该没有样式</Text>
            </View>
            
            <Text className="success-message">
                如果上面的元素都没有特殊样式，说明样式封装成功！
            </Text>
        </View>
    );
};

export default TestStyleIsolation;
