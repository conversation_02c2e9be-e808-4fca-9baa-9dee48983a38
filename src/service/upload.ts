import Taro, { uploadFile } from "@tarojs/taro";

import { getDomain } from "./base";
import { tool } from "@utils/tool";
import sysConfig from "../config";
import { checkAndStoreAuthentication } from "./request";

declare namespace RequestProps {
    interface Options {
        url: string;
        formData: any;
        filePath: string;
        fileName: string;
        contentType?: string;
        tips?: boolean;
    }
}

// token 键定义
const accessTokenKey = sysConfig.ACCESS_TOKEN_KEY;

class upload {
    baseOptions<T>(options: RequestProps.Options) {
        let { url, formData, filePath, fileName } = options;

        const accountInfo = Taro.getAccountInfoSync();
        const appId = accountInfo.miniProgram ? accountInfo.miniProgram.appId : "";

        url = getDomain() + url;

        return new Promise<T>((resolve, reject) => {
            var headers = {
                openId: tool.data.get("openId") ? tool.data.get("openId") : "",
                UnionId: tool.data.get("unionId") ? tool.data.get("unionId") : "",
                appId: appId ? appId : "",
                "Content-Type": "multipart/form-data",
            };

            const token = tool.data.get(accessTokenKey);
            if (token) {
                // Taro.showModal({
                //     title: "token" + url,
                //     content: token,
                // });
                headers[sysConfig.TOKEN_NAME] = sysConfig.TOKEN_PREFIX + token;
            }

            formData.appId = appId ? appId : "";
            formData.openId = tool.data.get("openId") ? tool.data.get("openId") : "";
            formData.UnionId = tool.data.get("unionId") ? tool.data.get("unionId") : "";

            uploadFile({
                url,
                formData: formData,
                filePath: filePath,
                name: fileName,
                header: headers,
                // 成功回调
                success(res: any) {
                    checkAndStoreAuthentication(res);
                    if (res.statusCode === 200) {
                        var data = JSON.parse(res.data);
                        resolve(data);
                    } else {
                        reject(res.data);
                    }
                },
                // 失败回调
                fail(res: any) {
                    console.log("uploadFile fail");
                    console.log(res);
                    reject(res.data);
                },
            });
        });
    }

    post<T>(url: string, formData: any, filePath: string, fileName: string, contentType?: string, tips?: boolean) {
        return this.baseOptions<T>({
            url,
            formData,
            filePath,
            fileName,
            contentType,
            tips,
        });
    }
}

export default new upload();
