import Taro, { request } from "@tarojs/taro";

import { getDomain } from "./base";

import sysConfig from "@config/index";
import { tool } from "@utils/tool";
import { generateValidationHash, VALIDATION_HEADER } from "@utils/encryption";
import { md5 } from "@utils/encryption";
import { login } from "@utils/login";

declare namespace RequestProps {
    interface Method {
        GET;
        POST;
        PUT;
        DELETE;
    }

    interface Options {
        url: string;
        method: keyof Method;
        data: any;
        contentType?: string;
        tips?: boolean;
        checkToken?: boolean;
    }

    interface requestParams {
        url: string;
        method: keyof Method;
        data: any;
        header: any;
    }
}

export interface ApiResponse<T> {
    success: boolean;
    data: T;
    message?: string;
    code?: number;
}
// token 键定义
const accessTokenKey = sysConfig.ACCESS_TOKEN_KEY;

/**
 * 检查并存储授权信息
 * @param res 响应对象
 */
export const checkAndStoreAuthentication = (res: any) => {
    // 检查并存储 token
    if (res.header[accessTokenKey]) {
        // 读取响应报文头 token 信息
        var accessToken = res.header[accessTokenKey] ? res.header[accessTokenKey] : "";

        if (accessToken && accessToken != "" && accessToken !== "invalid_token" && accessToken != tool.data.get(accessTokenKey)) {
            tool.data.set(accessTokenKey, accessToken);
        }
    }

    // 检查并存储 openId
    if (res.header["X-openId"]) {
        const openId = res.header["X-openId"];
        if (openId && openId !== "" && openId !== tool.data.get("openId")) {
            tool.data.set("openId", openId);
        }
    }
};
/**
 * 解密 JWT token 的信息
 * @param token jwt token 字符串
 * @returns <any>object
 */
export const decryptJWT = (token: string) => {
    token = token.replace(/_/g, "/").replace(/-/g, "+");
    var json = decodeURIComponent(escape(window.atob(token.split(".")[1])));
    return JSON.parse(json);
};

/**
 * 将 JWT 时间戳转换成 Date
 * @description 主要重对 `exp`，`iat`，`nbf`
 * @param timestamp 时间戳
 * @returns Date 对象
 */
export const getJWTDate = (timestamp: number) => {
    return new Date(timestamp * 1000);
};

class Request {
    private handleResponseUUID(obj: any) {
        if (!obj || typeof obj !== "object") return obj;

        if (Array.isArray(obj)) {
            return obj.map((item) => this.handleResponseUUID(item));
        }

        const result = { ...obj };
        for (const key in result) {
            if (result[key] === "********-0000-0000-0000-************") {
                result[key] = undefined;
            } else if (typeof result[key] === "object" && result[key] !== null) {
                result[key] = this.handleResponseUUID(result[key]);
            }
        }
        return result;
    }
    baseOptions<T>(options: RequestProps.Options) {
        let { url, method, data, contentType, checkToken = true } = options;
        console.log("checkToken", checkToken);

        const accountInfo = Taro.getAccountInfoSync();
        const appId = accountInfo.miniProgram ? accountInfo.miniProgram.appId : "";

        if (url.substring(0, 6) != "https:" && url.substring(0, 5) != "http:") {
            if (!url.startsWith("/") && !url.startsWith("\\")) {
                url = "/" + url;
            }
            url = getDomain() + url;
        }

        // 处理请求数据中的无效 UUID
        const replaceInvalidUUID = (obj: any) => {
            for (const key in obj) {
                if (obj[key] === "********-0000-0000-0000-************") {
                    obj[key] = undefined;
                } else if (typeof obj[key] === "object" && obj[key] !== null) {
                    replaceInvalidUUID(obj[key]);
                }
            }
        };

        replaceInvalidUUID(data);

        // 封装执行请求的函数
        const executeRequest = async (headers: any) => {
            console.log("url    =====>");
            console.log(url);

            // 生成时间戳
            const timestamp = Date.now().toString();
            headers["X-Request-Timestamp"] = timestamp;

            // 生成验证哈希
            const validationHash = await generateValidationHash(url, method === "GET" ? data : data, timestamp);
            headers[VALIDATION_HEADER] = validationHash;

            return new Promise<T>((resolve) => {
                request({
                    url,
                    data,
                    method,
                    header: headers,
                    success: (res: { data: T & { code?: number; message?: string }; header: any }): void => {
                        // 检查服务器时间和本地时间的差异
                        const serverTime = res.header["date"] ? new Date(res.header["date"]).getTime() : null;
                        const localTime = new Date().getTime();

                        if (serverTime) {
                            const timeDiff = Math.abs(serverTime - localTime);
                            // 如果时间差异超过5分钟（300000毫秒）
                            if (timeDiff > 300000) {
                                resolve({
                                    success: false,
                                    message: `本地时间与服务器时间差异过大（${Math.round(timeDiff / 1000 / 60)}分钟），请校准本地时间后重试`,
                                    code: 400,
                                } as any);
                                return;
                            }
                        }

                        // 处理 401 状态码
                        if (res.data?.code === 401) {
                            // 清空所有缓存
                            tool.data.clear();
                            // 修改返回的 message
                            res.data.message = "登录超时，请重新操作";
                            // 401 状态码时执行 getOpenId 方法获取新的 openId
                            import("@utils/login").then(() => {
                                login().catch((err) => {
                                    console.error("Failed to get OpenId after 401:", err);
                                });
                            });
                        } else {
                            // 检查并存储授权信息
                            checkAndStoreAuthentication(res);
                        }

                        // 处理响应数据中的无效 UUID
                        const processedData = this.handleResponseUUID(res.data);
                        resolve(processedData);
                    },
                    fail: (res: any): void => {
                        resolve(res.data);
                    },
                });
            });
        };

        return new Promise<T>(async (resolve) => {
            const openId = tool.data.get("openId") == null ? undefined : tool.data.get("openId");
            const unionId = tool.data.get("unionId") == null ? undefined : tool.data.get("unionId");

            const headers = {
                ...data.header,
                "content-type": contentType || "application/json",
                appId: appId ? appId : "",
                openId: openId ? openId : "",
                unionId: unionId ? unionId : "",
            };

            // 添加 accessToken 到请求头
            const token = tool.data.get(accessTokenKey);
            if (token) {
                // Taro.showModal({
                //     title: "token" + url,
                //     content: token,
                // });
                headers[sysConfig.TOKEN_NAME] = sysConfig.TOKEN_PREFIX + token;
            }

            // 执行请求
            const result = await executeRequest(headers);
            resolve(result);
        });
    }

    get<T>(url: string, tips?: boolean) {
        return this.baseOptions<T>({ url, method: "GET", data: {}, tips });
    }

    post<T>(url: string, data?: any, contentType?: string, tips?: boolean, checkToken?: boolean) {
        if (!data) {
            data = {};
        }

        return this.baseOptions<T>({
            url,
            method: "POST",
            data,
            contentType,
            tips,
            checkToken,
        });
    }

    put<T>(url: string, data?: any, contentType?: string, tips?: boolean) {
        if (!data) {
            data = {};
        }
        return this.baseOptions<T>({
            url,
            method: "PUT",
            data,
            contentType,
            tips,
        });
    }

    delete<T>(url: string, data?: any, contentType?: string, tips?: boolean) {
        if (!data) {
            data = {};
        }
        return this.baseOptions<T>({
            url,
            method: "DELETE",
            data,
            contentType,
            tips,
        });
    }

    uploadFile<T>(url: string, filePath: string, name: string, formData?: any) {
        return new Promise<T>(async (resolve, reject) => {
            if (url.substring(0, 6) != "https:" && url.substring(0, 5) != "http:") {
                if (!url.startsWith("/") && !url.startsWith("\\")) {
                    url = "/" + url;
                }
                url = getDomain() + url;
            }

            const accountInfo = Taro.getAccountInfoSync();
            const appId = accountInfo.miniProgram ? accountInfo.miniProgram.appId : "";
            const openId = tool.data.get("openId");
            const unionId = tool.data.get("unionId");
            const token = tool.data.get(accessTokenKey);

            const headers: any = {
                "content-type": "multipart/form-data",
                appId: appId ? appId : "",
                openId: openId ? openId : "",
            };

            if (token) {
                headers[sysConfig.TOKEN_NAME] = sysConfig.TOKEN_PREFIX + token;
            }

            if (openId) {
                headers.openId = openId;
            }
            if (unionId) {
                headers.unionId = unionId;
            }

            try {
                // 读取文件内容并计算MD5
                const fs = Taro.getFileSystemManager();
                const fileInfo = await new Promise<{ size: number }>((resolve, reject) => {
                    fs.getFileInfo({
                        filePath,
                        success: resolve,
                        fail: reject,
                    });
                });

                // 使用文件大小作为MD5输入，避免读取大文件内容
                const fileMD5 = md5(fileInfo.size.toString());

                // 生成时间戳
                const timestamp = Date.now().toString();
                headers["X-Request-Timestamp"] = timestamp;

                // 将文件MD5添加到formData中
                if (!formData) {
                    formData = {};
                }
                formData[name] = fileMD5;

                console.log("formData");
                console.log(formData);
                // 生成验证哈希
                const validationHash = await generateValidationHash(url, formData, timestamp);
                headers[VALIDATION_HEADER] = validationHash;

                Taro.uploadFile({
                    url,
                    filePath,
                    name,
                    formData: formData,
                    header: headers,
                    success: (res: { statusCode: number; data: string; header?: { [key: string]: string } }) => {
                        // 检查服务器时间和本地时间的差异
                        const serverTime = res.header?.["date"] ? new Date(res.header["date"]).getTime() : null;
                        const localTime = new Date().getTime();

                        if (serverTime) {
                            const timeDiff = Math.abs(serverTime - localTime);
                            // 如果时间差异超过5分钟（300000毫秒）
                            if (timeDiff > 300000) {
                                reject(new Error(`本地时间与服务器时间差异过大（${Math.round(timeDiff / 1000 / 60)}分钟），请校准本地时间后重试`));
                                return;
                            }
                        }

                        if (res.statusCode === 200) {
                            try {
                                const data = JSON.parse(res.data);
                                // 检查并存储授权信息
                                checkAndStoreAuthentication(res);
                                resolve(data);
                            } catch (error) {
                                reject(error);
                            }
                        } else {
                            reject(new Error(`Upload failed with status ${res.statusCode}`));
                        }
                    },
                    fail: (error) => {
                        reject(error);
                    },
                });
            } catch (error) {
                reject(error);
            }
        });
    }
}

export default new Request();
