import Taro from "@tarojs/taro";

/**
 * 获取版本 retrun 对应环境域名
 * develop: '开发版', trial: '体验版', release: '正式版'
 * 支持扩展 - 思路 可通过 process.env.NODE_ENV 判断当前打包是 生产模式或工厂模式 进而判断 适合多环境 dev -> beta -> uat -> pro
 * @returns 域名
 */
const getDomain = () => {
    var sys = Taro.getSystemInfoSync();
    const env = Taro.getEnv();
    if (env === "WEAPP") {
        if (sys.platform == "devtools") {
            return "http://localhost:5566";
        } else {
            return "https://api.51panda.com";
        }
    } else {
        return "https://api.51panda.com";
    }
};

const getJtDomain = () => {
    const env = Taro.getEnv();
    if (env === "WEAPP") {
        return "http://localhost:5000";
    } else {
        return "https://jt808.51panda.com";
    }
};
export { getDomain, getJtDomain };
