//wx7065d977094dcbc4

import { useGlobalIconFont } from "./components/iconfont/helper";

export default defineAppConfig({
    usingComponents: Object.assign(useGlobalIconFont()),
    // entryPagePath: "pages/pay/tenantCharge/index",
    entryPagePath: "pages/role-selection/index",
    // entryPagePath: "subpackages/student/user/student/list/index",
    // entryPagePath: "subpackages/student/user/exam/list/index",
    // entryPagePath: "subpackages/student/user/index/index",
    // entryPagePath: "pages/login/index",
    // entryPagePath: "pages/hnjxLogin/index",
    // entryPagePath: "subpackages/student/user/count/student/index",
    // entryPagePath: "subpackages/student/user/student/add/index",
    // entryPagePath: "subpackages/zxjy/login/index",
    // entryPagePath: "systemManage/pages/auth/user/add/index",
    // entryPagePath: "pages/create/contract/index",
    // entryPagePath: "pages/echengExam/index",
    // entryPagePath: "pay/pages/freePay/index",
    // entryPagePath: 'business/csjpxh/student/contract/signContract/index',
    // entryPagePath: 'business/csjpxh/student/contract/index/index',
    // entryPagePath: 'business/csjpxh/student/contract/signContract/index',
    // entryPagePath: 'business/csjpxh/Base/Car/index',
    // entryPagePath: 'business/csjpxh/jxjy/login/index',
    // entryPagePath: 'subpackages/csjpxh/student/contract/readContract/index',
    // entryPagePath: "business/auth/user/add/index",
    // entryPagePath: 'examSite/pages/study/index',
    // entryPagePath: 'subpackages/student/login/index',
    // entryPagePath: 'business/csjpxh/student/register/showDetail/index',
    // entryPagePath: 'business/student/login/index',
    // entryPagePath: "business/auth/jiaxiao/user/add/index",
    // entryPagePath: "business/auth/jiaxiao/user/login/index",
    // entryPagePath: "subpackages/child/login/index",
    // entryPagePath: 'business/csjpxh/jxDept/setLocation/index',
    // entryPagePath: 'pages/auth/user/setPassWord/index',
    // entryPagePath: "subpackages/student/examSite/selfLogin/index",
    // entryPagePath: "subpackages/student/examSite/coupon/index",
    // entryPagePath: "subpackages/pay/tenantCharge/index",
    // entryPagePath: 'subpackages/study/device/addFace/select/index',
    pages: [
        "pages/index/index",
        "pages/h5/index",
        "pages/login/index",
        "pages/hnjxLogin/index",
        "pages/webLogin/index", //网页扫码登录的方法
        // "pages/auth/user/add/index",
        // "pages/auth/user/setPassWord/index",
        // "pages/auth/jiaxiao/user/add/index",

        //下面主要是用于 小程序二维码 用的地址
        "pages/QCode/bindUser/index", //绑定用户
        "pages/QCode/bindMainUser/index", //绑定用户
        "pages/QCode/findMainUser/index", //找到主账号并绑定

        "pages/user/create/index", // 注册账户
        "pages/user/audit/index", // 注册账户 审核

        "pages/role-selection/index",
        // "pages/my-orders/index", //我的订单
        "pages/cashier/index", //收银台
        "pages/follow-official-account/index", //关注公众号页面
    ],
    subpackages: [
        {
            root: "subpackages/theoretical",
            pages: [
                "index/index", // 理论考试页面
                "practice/index", // 顺序练习页面
            ],
        },
        {
            root: "subpackages/hnjpxh",
            pages: [
                // "login/index", // 驾校登录
                "pay/index",
                "study/index",
                "studyList/index",
                "order/index",
                "exam/index",
                "examResult/index",
                "my/index",
                "paybill/index",
            ],
        },
        {
            root: "subpackages/child",
            pages: [
                "login/index", // 账号登录
                "index/index",
                "order/index",
                "mall/index",
            ],
        },
        {
            root: "subpackages/student",
            pages: [
                //考场相关的
                "examSite/index/index",
                "examSite/study/index",
                "examSite/selfLogin/index", //考场身份录入
                "examSite/selfLogin/success/index", // 支付成功以后得
                "examSite/selfLogin/share/index", // 分享教练  分润
                "examSite/selfLogin/coupon/old/index", // 分享教练  分润
                "examSite/order/index", // 我的订单
                "examSite/charge/index", // 考场收费管理   subpackages/student/examSite/charge/index
                "examSite/coupon/index",
                "examSite/coupon/rechargeReview/index", // 充值审核
                "examSite/door/index", // 闸门控制
                "examSite/myQueue/index", // 我的排队
                "examSite/mySale/index", // 我的电券

                "examSite/coupon/index",

                "pay/index/index",

                //驾校相关的
                "addStudent/index",
                "index/index",
                "photo/index",
                "appDocument/index",

                "user/index/index",
                "user/student/add/index",
                "user/student/list/index",
                "user/student/list/menu/index",
                "user/student/list/selectUser/index",
                "user/student/list/selectCar/index",
                "user/student/face/index",
                "user/student/index/index",
                "user/exam/list/index",
                "user/study/add/index", //上车
                "user/study/list/index", //上车 列表
                "user/audit/menu/index",
                "user/audit/list/index",
                "user/audit/account/index",

                "user/study/scan/index", //上车带训
                "user/study/order/index", //预约训练
                "user/car/list/index", //车辆管理
                "user/wage/index", //我的工资

                "user/count/exam/index",
                "user/count/pay/index",
                "user/count/student/index",

                "study/device/addFace/select/index", //添加 Face 之前 身份的选择
                "study/device/addFace/student/index", //学员的的 Face 数据的添加
                "study/device/addFace/staff/index", //员工的 Face 数据的添加

                "hnRefund/index", //湖南申请退款页面
                "hnRefund/list/index", //湖南退款列表页面
                "createStudentVersion2025/index/index", //2025版学员创建页面
                "contract/index/index", //合同签署页面
            ],
        },
    ],
    window: {
        navigationBarBackgroundColor: "#ffffff",
        navigationBarTextStyle: "black",
        navigationBarTitleText: "盼达软件",
        backgroundColor: "#eeeeee",
        backgroundTextStyle: "light",
        navigationStyle: "custom",
    },
    permission: {
        "scope.bluetooth": {
            desc: "需要蓝牙权限连接打印机",
        },
        "scope.userLocation": {
            desc: "签署合同需定位，预约训练需定位，现场练车需定位",
        },
        // "scope.nfc": {
        //     desc: "需要使用 NFC 功能，读身份证或者其他 NFC 卡片信息",
        // },
    },
    requiredPrivateInfos: ["getLocation", "onLocationChange", "startLocationUpdate"],
    navigateToMiniProgramAppIdList: ["wxc77987296fe617c5", "wx7065d977094dcbc4"],
});
