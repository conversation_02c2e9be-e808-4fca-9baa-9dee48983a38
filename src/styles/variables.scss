// NutUI 变量覆盖
$badge-background-color: #fa2c19;
$badge-color: #fff;
$badge-border: 0;
$badge-border-radius: 12px;
$badge-padding: 0 4px;
$badge-font-size: 12px;
$badge-icon-padding: 0 3px;
$badge-icon-size: 10px;
$badge-height: 18px;
$badge-min-width: 18px;
$badge-dot-width: 8px;
$badge-dot-border: 0;
$badge-content-transform: translateY(-50%) translateX(100%);
$badge-z-index: 1;

// 主题色
$color-primary: #fa2c19;
$color-primary-text: #fff;

// 引入 NutUI 变量
// @use "@nutui/nutui-react-taro/dist/styles/variables.scss" as *;
