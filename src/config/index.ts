const DEFAULT_CONFIG = {
    // 请求超时
    TIMEOUT: 15000,
    //token key
    ACCESS_TOKEN_KEY: "access-token",

    //刷新token key
    REFRESH_TOKEN_KEY: `x-access-token`,

    // TokenName // Authorization
    TOKEN_NAME: "Authorization",

    // Token前缀，注意最后有个空格，如不需要需设置空字符串 // Bearer
    TOKEN_PREFIX: "Bearer ",

    // 追加其他头
    HEADERS: {},

    // 请求是否开启缓存
    REQUEST_CACHE: false,
};

export default DEFAULT_CONFIG;
