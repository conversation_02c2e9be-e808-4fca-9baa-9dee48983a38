// @import "~taro-ui/dist/style/index.scss";

// @import "@nutui/nutui-react-taro/dist/style.css";

// @import "./styles/variables.scss";

// @use "tailwindcss/base" as *;
// @use "tailwindcss/components" as *;
// @use "tailwindcss/utilities" as *;

// @font-face {
//     font-family: "AlibabaPuHuiTi";
//     src: url("https://cdn.51panda.com/fonts/Yuanti-SC-Regular.ttf");
//     font-weight: normal;
//     font-style: normal;
// }

.nav-bar-top {
    margin-top: 88px;
}

input {
    font-family: Yuan<PERSON> SC, STYuanti-SC-Regular, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

page {
    // overflow-y: scroll;
    background: #f7f8fa;
    // min-height: 100vh;
    font-size: 28rpx;
    font-family: <PERSON><PERSON> SC, STYuanti-SC-Regular, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-weight: 350;
    // font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
    //     "Noto Color Emoji";

    --nutui-form-item-label-font-size: 28rpx;

    --nutui-brand-1: #edf4ff; // 梯度1
    --nutui-brand-2: #dce9ff; // 梯度2
    --nutui-brand-3: #ccdefb; // 梯度3
    --nutui-brand-4: #a6c8ff; // 梯度4
    --nutui-brand-5: #82b1ff; // 梯度5
    --nutui-brand-6: #5c99ff; // 梯度6
    --nutui-brand-7: #317fff; // 梯度7
    --nutui-brand-8: #165dff; // 梯度8 * 主题色 primary brand
    --nutui-brand-9: #1b58ba; // 梯度9
    --nutui-brand-10: #244986; // 梯度10
    --nutui-brand-11: #193666; // 梯度11
    --nutui-brand-12: #16243e; // 梯度12

    --nutui-brand-stop-1: var(--nutui-brand-8); // 渐变色起
    --nutui-brand-stop-2: var(--nutui-brand-8); // 渐变色止

    --nutui-color-primary: var(--nutui-brand-8);
    --nutui-color-primary-stop-1: var(--nutui-brand-stop-1);
    --nutui-color-primary-stop-2: var(--nutui-brand-stop-2);
    --nutui-color-primary-pressed: var(--nutui-brand-9);
    --nutui-color-primary-disabled: var(--nutui-brand-3);
    --nutui-color-primary-text: #ffffff;
    --nutui-color-primary-light: var(--nutui-brand-1);
    --nutui-color-primary-light-pressed: var(--nutui-brand-2);
    --nutui-color-text-link: var(--nutui-brand-8);

    --nutui-button-border-radius: 28px;
    --nutui-button-default-height: 84px;
    --nutui-button-default-font-size: 32px;
    --nutui-form-item-label-width: 80px;

    --nutui-dialog-padding: 35rpx 35rpx 25rpx 35rpx;

    .nut-toast-inner {
        min-width: 10%;
    }

    --nutui-form-item-label-font-size: 32rpx;
    --nutui-input-font-size: 28rpx;

    .nut-form-item-label {
        font-weight: 350;
    }
}

// button {
//     line-height: normal;
// }

.my-dialog {
    --nutui-button-default-height: 68rpx;
    --nutui-button-default-font-size: 28rpx;
}

.foot-view {
    position: fixed;
    bottom: 0px;
    width: 100%;
}

.foot-padding {
    padding-bottom: calc(env(safe-area-inset-bottom) / 2 + 20rpx);
}

.footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    font-size: 30rpx;
    color: #969799;
    padding-top: 20rpx;
    padding-bottom: calc(env(safe-area-inset-bottom) / 2 + 20rpx);
    background-color: #fff;
    // z-index: 2;
    border-top-left-radius: 20px;
    /* 左上角圆角 */
    border-top-right-radius: 20px;
    /* 右上角圆角 */
    z-index: 9;
}

.footer button {
    // line-height: 90rpx;
    // height: 90rpx;
    // font-size: 36rpx;
    // font-weight: 500;
    width: auto;
    // margin-left: 20rpx;
    // margin-right: 20rpx;
    place-items: center;
}

.footer .nut-button .nut-button-wrap {
    // height: 90rpx;
    line-height: normal;
}

.footer-placeholder {
    padding-bottom: calc(env(safe-area-inset-bottom) / 2);
}

// .nut-dialog__footer .nut-button--round {
//     border-radius: 5px;
// }
// .nut-dialog__footer .nut-button--round {
//     border-radius: 5px;
// }

.nut-input {
    padding: 11px 25px;
}

.leftcell {
    // border-bottom: 1px solid #eaf0fb;
    .nut-input {
        padding: 10px 25px;
        border-bottom: 1px solid #eaf0fb;
    }

    .nut-cell {
        padding: 13px 25px;
    }

    .nut-cell__title {
        width: 80px;
        margin-right: 6px;
        display: flex;
        -webkit-flex-direction: initial;
        -ms-flex-direction: initial;
        flex-direction: initial;
        -webkit-flex: initial;
        -ms-flex: initial;
        flex: initial;
    }
}

.tui-top__circle {
    width: 576rpx;
    height: 576rpx;
    background: #614dec;
    border-radius: 50%;
    position: absolute;
    right: -288rpx;
    top: -288rpx;
    z-index: 6;
}

.tui-bottom__circle {
    width: 576rpx;
    height: 576rpx;
    background: rgba(97, 77, 236, 0.3);
    border-radius: 50%;
    position: absolute;
    right: -348rpx;
    top: -188rpx;
    z-index: 2;
}

.tui-header__box {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.tui-header__title {
    font-size: 50rpx;
}

.tui-header__descr {
    font-size: 30rpx;
    color: #999;
    padding-top: 8rpx;
}

.tui-header__icon {
    width: 100%;
    position: fixed;
    top: 0;
    padding: 0 12rpx;
    display: flex;
    align-items: center;
    height: 32px;
    transform: translateZ(0);
    z-index: 20;
    box-sizing: border-box;
}

.tui-form {
    width: 100%;
    padding-top: 168rpx;
    padding-bottom: 20rpx;
}

.tui-icon__pr {
    padding-right: 20rpx;
}

.tui-btn__box {
    width: 100%;
    padding-top: 30rpx;
}

.tui-link__box {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.tui-link__text {
    font-size: 26rpx;
    padding: 20rpx 32rpx;
    color: #666;
}

.tui-color__primary {
    color: #614dec;
}

.tui-active:active {
    opacity: 0.5;
}

.pandaButton button {
    font-size: 34rpx;
    margin: 0;
    background: linear-gradient(90deg, #614dec 0%, #768df2 100%);
    border-color: linear-gradient(90deg, #614dec 0%, #768df2 100%);
}

.safe__area {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}

.success-container {
    .tui-bg {
        width: 100%;
        height: 200rpx;
        padding-top: 0rpx;
        // background: linear-gradient(20deg, #E41F19, #F34B0B);
        background-color: #e41f19;
        border-bottom-left-radius: 42rpx;
    }

    .tui-content {
        padding: 0 35rpx;
        box-sizing: border-box;
    }

    .tui-success-form {
        // background: #fff;
        // height: 500rpx;
        // box-shadow: 0 10rpx 14rpx 0 rgba(0, 0, 0, 0.08);
        // border-radius: 10rpx;
        // margin-top: -160rpx;
        // position: relative;
        // z-index: 10;
        // display: flex;
        // align-items: center;
        // flex-direction: column;
        // padding-top: 180rpx;

        // background: #fff;
        // height: 500rpx;
        // -webkit-box-shadow: 0 10rpx 14rpx 0 rgba(0, 0, 0, 0.08);
        // box-shadow: 0 10rpx 14rpx 0 rgba(0, 0, 0, 0.08);
        // border-radius: 10rpx;
        // margin-top: -160rpx;
        // position: relative;
        // z-index: 10;
        // display: -webkit-flex;
        // display: -ms-flexbox;
        // display: flex;
        // -webkit-align-items: center;
        // -ms-flex-align: center;
        // align-items: center;
        // -webkit-flex-direction: column;
        // -ms-flex-direction: column;
        // flex-direction: column;

        background: #fff;
        height: 500rpx;
        box-shadow: 0 10rpx 14rpx 0 rgba(0, 0, 0, 0.08);
        border-radius: 10rpx;
        margin-top: -160rpx;
        position: relative;
        z-index: 10;
        display: flex;
        align-items: center;
        flex-direction: column;
        width: 100%;
        padding-top: 168rpx;
        padding-bottom: 20rpx;
    }

    .tui-icon {
        width: 100rpx;
        height: 100rpx;
        display: block;
        margin-bottom: 60rpx;
    }

    .tui-title {
        font-size: 42rpx;
        line-height: 42rpx;
        padding-top: 28rpx;
    }

    .tui-sub-title {
        color: #666666;
        font-size: 28rpx;
        line-height: 28rpx;
        padding-top: 20rpx;
    }

    .tui-btn-box {
        width: 580rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 88rpx;
    }

    .tui-tips {
        font-size: 26rpx;
        padding: 48rpx 90rpx;
        box-sizing: border-box;
        text-align: justify;
        line-height: 48rpx;
    }

    .tui-grey {
        color: #555;
        padding-bottom: 8rpx;
    }

    .tui-light-grey {
        color: #888;
        line-height: 40rpx;
    }

    .tui-danger-hover {
        background: #c80808 !important;
        color: #e5e5e5 !important;
    }

    .tui-danger-outline {
        color: #eb0909 !important;
        background: transparent;
    }

    .tui-danger-outline::after {
        border: 1px solid #eb0909 !important;
    }

    .tui-btn-danger {
        background: #eb0909 !important;
        color: #fff;
    }

    .tui-shadow-danger {
        box-shadow: 0 10rpx 14rpx 0 rgba(235, 9, 9, 0.2);
    }

    .tui-btn {
        width: 100%;
        position: relative;
        border: 0 !important;
        border-radius: 6rpx;
        padding-left: 0;
        padding-right: 0;
        overflow: visible;
    }

    /*圆角 */

    .tui-fillet {
        border-radius: 50rpx;
    }

    .tui-btn-white.tui-fillet::after {
        border-radius: 98rpx;
    }

    .tui-outline-fillet::after {
        border-radius: 98rpx;
    }
}

.tui-form-button {
    width: 100%;
    position: relative;
    padding-left: 0;
    border: 1rpx solid;
    padding-right: 0;
    overflow: hidden;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

.tui-flex {
    width: 100%;
    display: flex;
}

.tui-flex__between {
    width: auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.tui-list-cell {
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

.tui-text__inline {
    display: inline-flex;
}

.tui-text__wrap {
    align-items: center;
    flex-direction: row;
}

.tui-spacing {
    width: 100%;
    padding: 0 30rpx;
    box-sizing: border-box;
}

.tui-size--24 {
    font-size: 24rpx;
}

.tui-bold {
    font-weight: bold;
}

.tui-color--price {
    color: #f55726;
}

.tui-flex__center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.nut-cell__title {
    white-space: nowrap;
    padding-right: 20rpx;
}

.nut-cell__value {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    // max-width: 60vw;
}

// =================================================================

.nut-toast__overlay-default {
    --nutui-overlay-bg-color: rgba(6, 6, 6, 0.8);
}

.tui-container {
    padding-bottom: 24rpx;
    background-color: #f1f1f1;
}

.tui-order--list {
    width: 100%;
    padding: 30rpx 30rpx 0;
    box-sizing: border-box;
}

.tui-btn--box {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    padding-top: 18rpx;
    padding-bottom: 18rpx;
    background-color: #fff;
}

x .tui-list--hidden {
    opacity: 0;
    visibility: hidden;
    position: fixed;
    transform: translateX(100%);
}

.tui-item__header {
    width: 100%;
    font-size: 24rpx;
    display: flex;
    justify-content: space-between;
    padding-bottom: 30rpx;
}

.tui-primary__color {
    color: var(--tui-primary, #40ae36);
}

.tui-order__item {
    width: 100%;
    flex: 1;
    background: #fff;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
}

.tui-order__inner {
    width: 100%;
    display: flex;
}

.tui-order__item-radius {
    border-radius: 20rpx;
    overflow: hidden;
}

.tui-goods__img {
    width: 148rpx;
    height: 148rpx;
    margin-right: 30rpx;
    display: block;
    border-radius: 16rpx;
}

.tui-goods__content {
    flex: 1;
    position: relative;
}

.tui-goods__title {
    width: 100%;
    font-size: 26rpx;
    font-weight: 500;
    color: #333;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    margin-bottom: 12rpx;
}

.tui-goods__descr {
    font-size: 27rpx;
    color: #999999;
    zoom: 0.8;
    margin-bottom: 2rpx;
}

.tui-price__sign {
    font-size: 24rpx;
    line-height: 24rpx;
}

.tui-price__num {
    font-size: 32rpx;
    line-height: 32rpx;
    font-weight: 500;
}

.tui-price__box {
    width: 202rpx;
    padding-left: 16rpx;
    flex-shrink: 0;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tui-list-cell {
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

.tui-radius {
    border-radius: 6rpx;
    overflow: hidden;
}

.tui-cell-hover {
    background-color: #f1f1f1 !important;
}

.tui-list-cell::after {
    content: "";
    position: absolute;
    border-bottom: 1px solid #eaeef1;
    -webkit-transform: scaleY(0.5) translateZ(0);
    transform: scaleY(0.5) translateZ(0);
    transform-origin: 0 100%;
    bottom: 0;
    right: 0;
    left: 0;
    pointer-events: none;
}

.tui-line-left::after {
    left: 30rpx !important;
}

.tui-line-right::after {
    right: 30rpx !important;
}

.tui-cell-unlined::after {
    border-bottom: 0 !important;
}

.tui-cell-arrow::before {
    content: " ";
    height: 10px;
    width: 10px;
    border-width: 2px 2px 0 0;
    border-color: #c0c0c0;
    border-style: solid;
    -webkit-transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
    transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
    position: absolute;
    top: 50%;
    margin-top: -6px;
    right: 30rpx;
    pointer-events: none;
}

.tui-arrow-right::before {
    right: 0 !important;
}

.tui-arrow-gray::before {
    border-color: #666666 !important;
}

.tui-arrow-white::before {
    border-color: #ffffff !important;
}

.tui-arrow-warning::before {
    border-color: #ff7900 !important;
}

.tui-arrow-success::before {
    border-color: #19be6b !important;
}

.tui-arrow-danger::before {
    border-color: #eb0909 !important;
}

.tui-card__wrap {
    padding-bottom: 30rpx;
}

.nut-cell-group__title {
    margin-top: 20px;
}

.tui-container {
    padding-bottom: 130rpx;
}

.bg-img image {
    position: absolute;
    width: 100%;
    height: 440rpx;
    z-index: -1;
    background-image: url("https://cdn.51panda.com/WxAppImage/mine_bg_3x.png");
}

.nut-input {
    border-radius: 6px;
    border-bottom: 0px solid #eaf0fb;
}

.tui-bg {
    width: 100%;
    height: 200rpx;
    padding-top: 0rpx;
    // background: linear-gradient(20deg, #E41F19, #F34B0B);
    background-color: #e41f19;
    border-bottom-left-radius: 42rpx;
}

.tui-content {
    padding: 0 35rpx;
    box-sizing: border-box;
    margin-top: -160rpx;
}

.tui-form {
    // background: #fff;
    // height: 500rpx;
    // box-shadow: 0 10rpx 14rpx 0 rgba(0, 0, 0, 0.08);
    border-radius: 10rpx;
    // margin-top: -160rpx;
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    flex-direction: column;
    // padding: 0 40rpx 40rpx 40rpx;
    width: auto;
}

.tui-btn-box {
    width: 580rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 88rpx;
}

.tui-grey {
    color: #555;
    padding-bottom: 8rpx;
}

.tui-light-grey {
    color: #888;
    line-height: 40rpx;
}

.tui-danger-hover {
    background: #c80808 !important;
    color: #e5e5e5 !important;
}

.tui-danger-outline {
    color: #eb0909 !important;
    background: transparent;
}

.tui-danger-outline::after {
    border: 1px solid #eb0909 !important;
}

.tui-btn-danger {
    background: #eb0909 !important;
    color: #fff;
}

.tui-shadow-danger {
    box-shadow: 0 10rpx 14rpx 0 rgba(235, 9, 9, 0.2);
}

.tui-btn {
    width: 100%;
    position: relative;
    border: 0 !important;
    border-radius: 6rpx;
    padding-left: 0;
    padding-right: 0;
    overflow: visible;
}

/*圆角 */

.tui-fillet {
    border-radius: 50rpx;
}

.tui-btn-white.tui-fillet::after {
    border-radius: 98rpx;
}

.tui-outline-fillet::after {
    border-radius: 98rpx;
}

.tui-invoice__box {
    background-color: #ffffff;
    margin-top: 20rpx;
    border-radius: 20rpx;
    overflow: hidden;
}

.tui-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.tui-notice {
    font-size: 24rpx;
    font-weight: 400;
    color: #999;
}

.tui-bold {
    font-weight: bold;
}

.tui-attr__box {
    padding: 4rpx 0 12rpx 0;
}

.tui-pbtm__0 {
    padding-bottom: 0;
}

.tui-attr-item {
    max-width: 100%;
    min-width: 180rpx;
    height: 64rpx;
    display: -webkit-inline-flex;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #f7f7f7;
    border: 1rpx solid #f7f7f7;
    padding: 0 26rpx;
    box-sizing: border-box;
    border-radius: 12rpx;
    margin-right: 32rpx;
    font-size: 26rpx;
}

.tui-attr-active {
    background-color: #fcedea;
    border-color: #e41f19;
    color: #e41f19;
    font-weight: bold;
}

.tui-tips {
    color: #999;
    font-size: 24rpx;
    font-weight: 400;
    padding: 10rpx 0;
}

.tui-input__item {
    width: 100%;
    display: flex;
    align-items: center;
    font-size: 28rpx;
    padding-top: 40rpx;
}

.tui-input__title {
    width: 156rpx;
    flex-shrink: 0;
}

.tui-input__item input {
    font-size: 28rpx;
    flex: 1;
}

.tui-placeholder {
    color: #bfbfbf;
}

.tui-more__optional {
    font-size: 24rpx;
    display: flex;
    align-items: center;
    color: #999;
}

.tui-between {
    justify-content: space-between;
}

.tui-btn__box {
    padding: 60rpx 30rpx 80rpx;
}

.tui-modal__title {
    text-align: center;
    font-weight: bold;
    padding-bottom: 8rpx;
}

.tui-modal__p {
    font-size: 26rpx;
    color: #888;
    padding-top: 20rpx;
}

.tui-modal__btn {
    width: 100%;
    padding: 60rpx 0 20rpx;
    display: flex;
    justify-content: center;
}

.tui-list-cell {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 30rpx 0;
}

.tui-radius {
    border-radius: 6rpx;
    overflow: hidden;
}

.tui-cell-hover {
    background-color: #f1f1f1 !important;
}

.tui-list-cell::after {
    content: "";
    position: absolute;
    border-bottom: 1px solid #eaeef1;
    -webkit-transform: scaleY(0.5) translateZ(0);
    transform: scaleY(0.5) translateZ(0);
    transform-origin: 0 100%;
    bottom: 0;
    right: 0;
    left: 0;
}

.tui-line-left::after {
    left: 30rpx !important;
}

.tui-line-right::after {
    right: 30rpx !important;
}

.tui-cell-unlined::after {
    border-bottom: 0 !important;
}

.tui-cell-arrow::before {
    content: " ";
    height: 10px;
    width: 10px;
    border-width: 2px 2px 0 0;
    border-color: #c0c0c0;
    border-style: solid;
    -webkit-transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
    transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
    position: absolute;
    top: 50%;
    margin-top: -6px;
    right: 30rpx;
}

.tui-arrow-right::before {
    right: 0 !important;
}

.tui-arrow-gray::before {
    border-color: #666666 !important;
}

.tui-arrow-white::before {
    border-color: #ffffff !important;
}

.tui-arrow-warning::before {
    border-color: #ff7900 !important;
}

.tui-arrow-success::before {
    border-color: #19be6b !important;
}

.tui-arrow-danger::before {
    border-color: #eb0909 !important;
}

.tui-goods__bar {
    position: fixed;
    z-index: 10;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
}

.tui-goods__bar::before {
    content: "";
    width: 100%;
    border-top: 1px solid #eaeef1;
    position: absolute;
    top: 0;
    left: 0;
    transform: scaleY(0.5);
    transform-origin: 0 0;
}

.tui-goods__bar-inner {
    width: 100%;
    height: 100rpx;
    padding: 0 30rpx;
    box-sizing: border-box;
}

.tui--flex {
    display: flex;
    align-items: center;
}

.tui-flex--between {
    justify-content: space-between;
}

.tui-flex--end {
    justify-content: flex-end;
}

.tui-check--all {
    font-size: 26rpx;
    padding-left: 16rpx;
}

.tui-price--box {
    padding-right: 30rpx;
}

.tui-outer__box {
    width: 100%;
    padding-top: 0rpx;
    padding-left: 0rpx;
    padding-right: 0rpx;
    box-sizing: border-box;
}

.tui-bp__tit {
    width: 100%;
    padding: 30rpx;
    box-sizing: border-box;
    position: relative;
    font-weight: 500;
}

.tui-icon--close {
    position: absolute;
    right: 30rpx;
    top: 30rpx;
    // padding: 8rpx;
}

.tui-bp__content {
    width: 100%;
    padding: 24rpx 50rpx 30rpx;
    box-sizing: border-box;
}

.tui-bp--top {
    padding-top: 40rpx;
}

.ti-btn--box {
    width: 100%;
    padding-top: 100rpx;
    box-sizing: border-box;
}

.ti-btn--box button {
    width: 100%;
}

.tui-bp__price-box {
    width: 100%;
    align-items: flex-end;
    // padding-bottom: 80rpx;
}

.tui-bp__price {
    font-size: 70rpx;
    line-height: 70rpx;
}

/* .tui-flex__between{
    width: 100%;
    font-size: 30rpx;
    padding-top:56rpx;
  } */
.tui-bp__name {
    color: #999999;
}

.tui-bottom-popup {
    width: 100%;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    transform: translate3d(0, 100%, 0);
    transform-origin: center;
    transition: all 0.3s ease-in-out;
    min-height: 20rpx;
}

.tui-popup-radius {
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
    padding-bottom: env(safe-area-inset-bottom);
    overflow: hidden;
}

.tui-popup-show {
    /* transform: translate3d(0, 0, 0); */
    opacity: 1;
}

.tui-popup-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    transition: all 0.3s ease-in-out;
    opacity: 0;
    visibility: hidden;
}

.tui-mask-show {
    opacity: 1;
    visibility: visible;
}

.nut-checkbox__label {
    width: auto;
}

// .nut-tabs__content__wrap{
//     height: 0;
// }

.nut-tabpane {
    padding: 0;
}

// =================================================

// .nut-picker-control {
//     padding: '8px 16px';
// }

/************************************************************
** 请将全局样式拷贝到项目的全局 CSS 文件或者当前页面的顶部 **
** 否则页面将无法正常显示                                  **
************************************************************/

page {
    width: 100vw;
    height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Microsoft Yahei",
        sans-serif;
}

view,
image,
text {
    box-sizing: border-box;
    flex-shrink: 0;
}

.flex-row {
    display: flex;
    flex-direction: row;
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.justify-start {
    justify-content: flex-start;
}

.justify-end {
    justify-content: flex-end;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-around {
    justify-content: space-around;
}

.justify-evenly {
    justify-content: space-evenly;
}

.items-start {
    align-items: flex-start;
}

.items-end {
    align-items: flex-end;
}

.items-center {
    align-items: center;
}

.items-baseline {
    align-items: baseline;
}

.items-stretch {
    align-items: stretch;
}

.self-start {
    align-self: flex-start;
}

.self-end {
    align-self: flex-end;
}

.self-center {
    align-self: center;
}

.self-baseline {
    align-self: baseline;
}

.self-stretch {
    align-self: stretch;
}

.flex-1 {
    flex: 1 1 0%;
}

.flex-auto {
    flex: 1 1 auto;
}

.grow {
    flex-grow: 1;
}

.grow-0 {
    flex-grow: 0;
}

.shrink {
    flex-shrink: 1;
}

.shrink-0 {
    flex-shrink: 0;
}

.relative {
    position: relative;
}

.ml-2 {
    margin-left: 4rpx;
}

.mt-2 {
    margin-top: 4rpx;
}

.ml-4 {
    margin-left: 8rpx;
}

.mt-4 {
    margin-top: 8rpx;
}

.ml-6 {
    margin-left: 12rpx;
}

.mt-6 {
    margin-top: 12rpx;
}

.ml-8 {
    margin-left: 16rpx;
}

.mt-8 {
    margin-top: 16rpx;
}

.ml-10 {
    margin-left: 20rpx;
}

.mt-10 {
    margin-top: 20rpx;
}

.ml-12 {
    margin-left: 24rpx;
}

.mt-12 {
    margin-top: 24rpx;
}

.ml-14 {
    margin-left: 28rpx;
}

.mt-14 {
    margin-top: 28rpx;
}

.ml-16 {
    margin-left: 32rpx;
}

.mt-16 {
    margin-top: 32rpx;
}

.ml-18 {
    margin-left: 36rpx;
}

.mt-18 {
    margin-top: 36rpx;
}

.ml-20 {
    margin-left: 40rpx;
}

.mt-20 {
    margin-top: 40rpx;
}

.ml-22 {
    margin-left: 44rpx;
}

.mt-22 {
    margin-top: 44rpx;
}

.ml-24 {
    margin-left: 48rpx;
}

.mt-24 {
    margin-top: 48rpx;
}

.ml-26 {
    margin-left: 52rpx;
}

.mt-26 {
    margin-top: 52rpx;
}

.ml-28 {
    margin-left: 56rpx;
}

.mt-28 {
    margin-top: 56rpx;
}

.ml-30 {
    margin-left: 60rpx;
}

.mt-30 {
    margin-top: 60rpx;
}

.ml-32 {
    margin-left: 64rpx;
}

.mt-32 {
    margin-top: 64rpx;
}

.ml-34 {
    margin-left: 68rpx;
}

.mt-34 {
    margin-top: 68rpx;
}

.ml-36 {
    margin-left: 72rpx;
}

.mt-36 {
    margin-top: 72rpx;
}

.ml-38 {
    margin-left: 76rpx;
}

.mt-38 {
    margin-top: 76rpx;
}

.ml-40 {
    margin-left: 80rpx;
}

.mt-40 {
    margin-top: 80rpx;
}

.ml-42 {
    margin-left: 84rpx;
}

.mt-42 {
    margin-top: 84rpx;
}

.ml-44 {
    margin-left: 88rpx;
}

.mt-44 {
    margin-top: 88rpx;
}

.ml-46 {
    margin-left: 92rpx;
}

.mt-46 {
    margin-top: 92rpx;
}

.ml-48 {
    margin-left: 96rpx;
}

.mt-48 {
    margin-top: 96rpx;
}

.ml-50 {
    margin-left: 100rpx;
}

.mt-50 {
    margin-top: 100rpx;
}

.ml-52 {
    margin-left: 104rpx;
}

.mt-52 {
    margin-top: 104rpx;
}

.ml-54 {
    margin-left: 108rpx;
}

.mt-54 {
    margin-top: 108rpx;
}

.ml-56 {
    margin-left: 112rpx;
}

.mt-56 {
    margin-top: 112rpx;
}

.ml-58 {
    margin-left: 116rpx;
}

.mt-58 {
    margin-top: 116rpx;
}

.ml-60 {
    margin-left: 120rpx;
}

.mt-60 {
    margin-top: 120rpx;
}

.ml-62 {
    margin-left: 124rpx;
}

.mt-62 {
    margin-top: 124rpx;
}

.ml-64 {
    margin-left: 128rpx;
}

.mt-64 {
    margin-top: 128rpx;
}

.ml-66 {
    margin-left: 132rpx;
}

.mt-66 {
    margin-top: 132rpx;
}

.ml-68 {
    margin-left: 136rpx;
}

.mt-68 {
    margin-top: 136rpx;
}

.ml-70 {
    margin-left: 140rpx;
}

.mt-70 {
    margin-top: 140rpx;
}

.ml-72 {
    margin-left: 144rpx;
}

.mt-72 {
    margin-top: 144rpx;
}

.ml-74 {
    margin-left: 148rpx;
}

.mt-74 {
    margin-top: 148rpx;
}

.ml-76 {
    margin-left: 152rpx;
}

.mt-76 {
    margin-top: 152rpx;
}

.ml-78 {
    margin-left: 156rpx;
}

.mt-78 {
    margin-top: 156rpx;
}

.ml-80 {
    margin-left: 160rpx;
}

.mt-80 {
    margin-top: 160rpx;
}

.ml-82 {
    margin-left: 164rpx;
}

.mt-82 {
    margin-top: 164rpx;
}

.ml-84 {
    margin-left: 168rpx;
}

.mt-84 {
    margin-top: 168rpx;
}

.ml-86 {
    margin-left: 172rpx;
}

.mt-86 {
    margin-top: 172rpx;
}

.ml-88 {
    margin-left: 176rpx;
}

.mt-88 {
    margin-top: 176rpx;
}

.ml-90 {
    margin-left: 180rpx;
}

.mt-90 {
    margin-top: 180rpx;
}

.ml-92 {
    margin-left: 184rpx;
}

.mt-92 {
    margin-top: 184rpx;
}

.ml-94 {
    margin-left: 188rpx;
}

.mt-94 {
    margin-top: 188rpx;
}

.ml-96 {
    margin-left: 192rpx;
}

.mt-96 {
    margin-top: 192rpx;
}

.ml-98 {
    margin-left: 196rpx;
}

.mt-98 {
    margin-top: 196rpx;
}

.ml-100 {
    margin-left: 200rpx;
}

.mt-100 {
    margin-top: 200rpx;
}

.mb-9 {
    margin-bottom: 9px;
}

.mb-15 {
    margin-bottom: 15px;
}

button {
    line-height: normal; /* 重点：设置内容行高为 normal */
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
}

.nut-button {
    line-height: normal; /* 重点：设置内容行高为 normal */
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
}
.nut-picker-title {
    font-size: 28rpx;
}
