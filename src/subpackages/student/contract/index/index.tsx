import React, { FC, useState, useEffect, useRef } from "react";
import { View, Text, Button, Image } from "@tarojs/components";
import Layout from "@/components/Layout";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import "./index.scss";
import TopNavBar from "@/components/topNavBar";
import Taro from "@tarojs/taro";
import request from "@/service/request";
import FaceDetectionView, { FaceDetectionViewRef } from "@/components/FaceDetectionView";
import SignatureBoard, { SignatureBoardRef } from "@/components/SignatureBoard";
import { getUserLocation } from "@/utils/util";
import { tool } from "@/utils/tool";
import { login } from "@/utils/login";
import upload from "@/service/upload";

interface IState {
    loading: boolean;
    contractImageData: string;
    isSigned: boolean;
    imageLoading: boolean;
    imageUrl: string; // 添加用于存储处理后的图片URL
    isSign: boolean; // 添加用于存储合同是否已签署的状态
    hasScrolledToBottom: boolean; // 添加用于判断是否已滚动到底部的状态
    countdown: number; // 添加倒计时状态
    countdownFinished: boolean; // 添加倒计时是否结束的状态
    xm: string; // 添加姓名参数
    // 位置相关状态
    isGettingLocation: boolean; // 是否正在获取位置
    locationInfo: {
        latitude: number;
        longitude: number;
        accuracy: number;
    } | null; // 位置信息
    // 人脸识别相关状态
    faceAuthData: {
        photo: string; // 现场拍照的临时路径
        authResult: any; // 认证结果数据
    } | null; // 人脸识别数据
    // 自定义loading弹窗状态
    customLoading: {
        visible: boolean;
        message: string;
        isCompleted: boolean; // 是否完成，控制显示打钩图标
    };
}

const ContractSigning: FC = () => {
    const [state, setState] = useState<IState>({
        loading: false,
        contractImageData: "",
        isSigned: false,
        imageLoading: true,
        imageUrl: "",
        isSign: false,
        hasScrolledToBottom: false,
        countdown: 20,
        countdownFinished: false,
        xm: "",
        isGettingLocation: false,
        locationInfo: null,
        faceAuthData: null,
        customLoading: {
            visible: false,
            message: "",
            isCompleted: false,
        },
    });

    const faceDetectionRef = useRef<FaceDetectionViewRef>(null);
    const signatureBoardRef = useRef<SignatureBoardRef>(null);
    const countdownTimerRef = useRef<NodeJS.Timeout | null>(null);

    // 获取合同图片
    const getContractImage = async () => {
        try {
            setState((prev) => ({ ...prev, imageLoading: true }));

            const response: any = await request.post(`/Jx/Student/Contract/WxContract/getContract/`);

            if (response.success && response.data) {
                // 处理base64图片数据
                let imageData = response.data.Contract;

                // 验证图片数据
                if (!imageData) {
                    message.error("合同图片数据为空");
                    return;
                }

                if (typeof imageData === "string" && !imageData.startsWith("http") && !imageData.startsWith("data:")) {
                    // 如果是base64数据，添加data:image前缀
                    imageData = "data:image/jpeg;base64," + imageData;
                }

                setState((prev) => ({
                    ...prev,
                    contractImageData: imageData,
                    xm: response.data.xm || "",
                }));

                // 合同加载成功后立即开始倒计时
                startCountdown();
                // 延迟检查滚动位置，以防图片较短不需要滚动
                setTimeout(() => {
                    handleScroll();
                }, 1000);
            } else {
                message.error(response.message || "获取合同失败");
            }
        } catch (error) {
            message.error("获取合同失败，请重试");
        } finally {
            setState((prev) => ({ ...prev, imageLoading: false }));
        }
    };

    // 组件挂载时获取合同图片
    useEffect(() => {
        login(true).then((res) => {
            console.log("login res", res);
            getContractImage();
        });

        // 组件卸载时清除定时器
        return () => {
            if (countdownTimerRef.current) {
                clearInterval(countdownTimerRef.current);
            }
        };
    }, []);

    // 监听页面滚动事件
    const handleScroll = () => {
        Taro.createSelectorQuery()
            .selectViewport()
            .scrollOffset()
            .exec((res) => {
                if (res && res[0]) {
                    const { scrollTop, scrollHeight } = res[0];
                    const windowHeight = Taro.getSystemInfoSync().windowHeight;

                    // 判断是否滚动到底部（容忍度为50px）
                    if (scrollTop + windowHeight >= scrollHeight - 50) {
                        setState((prev) => ({ ...prev, hasScrolledToBottom: true }));
                    }
                }
            });
    };

    // 监听页面滚动
    useEffect(() => {
        // 重置滚动状态当合同内容改变时
        setState((prev) => ({ ...prev, hasScrolledToBottom: false }));

        // 使用定时器定期检查滚动位置
        const scrollTimer = setInterval(handleScroll, 500);

        return () => {
            clearInterval(scrollTimer);
        };
    }, [state.contractImageData]);

    // 倒计时逻辑
    const startCountdown = () => {
        // 检查是否为特定的 openId，如果是则跳过倒计时
        const openId = tool.data.get("openId");
        if (openId === "o3QMJ0Ve8TxyrOuz9K5thJr0__W0") {
            // 特定 openId 不需要倒计时，直接设置为完成状态
            setState((prev) => ({ ...prev, countdown: 0, countdownFinished: true }));
            return;
        }

        // 清除之前的定时器
        if (countdownTimerRef.current) {
            clearInterval(countdownTimerRef.current);
        }

        // 重置倒计时状态
        setState((prev) => ({ ...prev, countdown: 20, countdownFinished: false }));

        countdownTimerRef.current = setInterval(() => {
            setState((prev) => {
                if (prev.countdown > 1) {
                    return {
                        ...prev,
                        countdown: prev.countdown - 1,
                    };
                } else {
                    // 倒计时结束
                    if (countdownTimerRef.current) {
                        clearInterval(countdownTimerRef.current);
                        countdownTimerRef.current = null;
                    }
                    return {
                        ...prev,
                        countdown: 0,
                        countdownFinished: true,
                    };
                }
            });
        }, 1000);
    };

    // 获取地理位置
    const getLocationInfo = async (): Promise<{ latitude: number; longitude: number; accuracy: number }> => {
        try {
            // 首先检查位置权限
            const setting = await Taro.getSetting();

            if (!setting.authSetting["scope.userLocation"]) {
                // 请求位置权限
                try {
                    await getUserLocation();
                } catch (error) {
                    throw new Error("用户拒绝授权位置权限，无法进行合同签署");
                }
            }

            // 获取精准位置信息
            const location = await Taro.getLocation({
                type: "gcj02", // 使用国测局坐标系
                isHighAccuracy: true, // 开启高精度模式
                highAccuracyExpireTime: 5000, // 高精度模式超时时间
                altitude: true, // 获取海拔信息
            });

            return {
                latitude: location.latitude,
                longitude: location.longitude,
                accuracy: location.accuracy || 0,
            };
        } catch (error: any) {
            console.error("获取位置失败:", error);
            if (error.errMsg && error.errMsg.includes("auth deny")) {
                throw new Error("位置权限被拒绝，无法进行合同签署");
            } else if (error.errMsg && error.errMsg.includes("location fail")) {
                throw new Error("获取位置失败，请检查GPS是否开启或网络连接");
            } else {
                throw new Error(error.message || "获取位置信息失败，请重试");
            }
        }
    };

    const handleSign = async () => {
        try {
            setState((prev) => ({ ...prev, loading: true, isGettingLocation: true }));

            // 第一步：获取精准地理位置
            const locationInfo = await getLocationInfo();

            // 位置获取成功，更新状态
            setState((prev) => ({
                ...prev,
                isGettingLocation: false,
                locationInfo: locationInfo,
            }));

            // 等待一小段时间让用户看到位置获取成功的消息
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // 检查是否为测试模式，跳过人脸识别
            const openId = tool.data.get("openId");
            const isTestMode = openId === "o3QMJ0Ve8TxyrOuz9K5thJr0__W0" || process.env.NODE_ENV === "development" || Taro.getCurrentInstance().router?.params?.skipFace === "true";

            if (isTestMode) {
                console.log("测试模式：跳过人脸识别");
                // 模拟人脸识别数据
                setState((prev) => ({
                    ...prev,
                    faceAuthData: {
                        photo: "test_photo_data", // 测试用的照片数据
                        authResult: { testMode: true }, // 测试模式标识
                    },
                }));

                message.success("测试模式：已跳过人脸识别，请进行签名");

                // 等待一小段时间让用户看到成功消息，然后打开签字板
                setTimeout(() => {
                    signatureBoardRef.current?.open();
                }, 1000);
            } else {
                // 第二步：进行人脸识别
                faceDetectionRef.current?.open();
            }

            // signatureBoardRef.current?.open();
        } catch (error: any) {
            message.error(error.message || "签署失败，请重试");
            setState((prev) => ({
                ...prev,
                loading: false,
                isGettingLocation: false,
            }));
        }
    };

    // 人脸识别失败回调
    const handleFaceDetectionError = (error: any) => {
        console.error("人脸识别失败:", error);
        message.error("人脸识别失败，请重试");
        faceDetectionRef.current?.close();
        setState((prev) => ({ ...prev, loading: false }));
    };

    // 人脸识别成功回调
    const handleFaceDetectionSuccess = (result: any) => {
        console.log("人脸识别成功:", result);

        // 保存人脸识别数据
        setState((prev) => ({
            ...prev,
            faceAuthData: {
                photo: result.photo, // 现场拍照的临时路径
                authResult: result, // 完整的认证结果
            },
        }));

        // 关闭人脸识别界面
        faceDetectionRef.current?.close();

        // 显示成功消息
        message.success("人脸识别成功，请进行签名");

        // 等待一小段时间让用户看到成功消息，然后打开签字板
        setTimeout(() => {
            signatureBoardRef.current?.open();
        }, 1000);
    };

    // 签名完成回调
    const handleSignatureComplete = async (signatureData: string) => {
        try {
            console.log("签名完成:", signatureData);
            console.log("签名数据类型:", typeof signatureData);
            console.log("签名数据长度:", signatureData?.length);
            console.log("位置信息:", state.locationInfo);
            console.log("人脸识别数据:", state.faceAuthData);

            // 验证签名数据
            if (!signatureData || typeof signatureData !== "string") {
                message.error("签名数据无效，请重新签名");
                setState((prev) => ({ ...prev, loading: false }));
                return;
            }

            // 验证文件是否存在
            try {
                const fileInfo: any = await Taro.getFileInfo({ filePath: signatureData });
                console.log("签名文件信息:", fileInfo);
                if (!fileInfo.size || fileInfo.size === 0) {
                    message.error("签名文件为空，请重新签名");
                    setState((prev) => ({ ...prev, loading: false }));
                    return;
                }
            } catch (error) {
                console.error("获取签名文件信息失败:", error);
                message.error("签名文件无效，请重新签名");
                setState((prev) => ({ ...prev, loading: false }));
                return;
            }

            // 第一步：上传签字信息
            setState((prev) => ({
                ...prev,
                customLoading: {
                    visible: true,
                    message: "正在上传签字信息...",
                    isCompleted: false,
                },
            }));
            const signatureResponse: any = await upload.post("/Jx/Student/Contract/WxContract/uploadSignature", {}, signatureData, "signatureImage");

            if (!signatureResponse.success) {
                setState((prev) => ({
                    ...prev,
                    customLoading: { visible: false, message: "", isCompleted: false },
                    loading: false,
                }));
                message.error(signatureResponse.message || "上传签字信息失败");
                return;
            }

            console.log("签字信息上传成功", signatureResponse);

            // 第二步：提交合同签署信息并直接生成合同
            setState((prev) => ({
                ...prev,
                customLoading: {
                    visible: true,
                    message: "正在生成合同...",
                    isCompleted: false,
                },
            }));

            const submitResponse: any = await request.post("/Jx/Student/Contract/WxContract/submitContractSign", {
                locationInfo: state.locationInfo, // 传递位置信息
                signatureImageId: signatureResponse.data?.imageId || signatureResponse.data?.id, // 传递上传后返回的签名文件ID
            });

            if (!submitResponse.success) {
                setState((prev) => ({
                    ...prev,
                    customLoading: { visible: false, message: "", isCompleted: false },
                    loading: false,
                }));
                message.error(submitResponse.message || "提交合同签署信息失败");
                return;
            }

            const contractId = submitResponse.contractId;
            console.log("合同生成成功，合同ID:", contractId);

            // 第三步：上传拍照信息
            if (state.faceAuthData && state.faceAuthData.photo) {
                setState((prev) => ({
                    ...prev,
                    customLoading: {
                        visible: true,
                        message: "正在上传拍照信息...",
                        isCompleted: false,
                    },
                }));

                const photoResponse: any = await request.post("/Jx/Student/Contract/WxContract/uploadPhoto", {
                    contractId: contractId,
                    photo: state.faceAuthData.photo,
                    authResult: state.faceAuthData.authResult,
                });

                if (!photoResponse.success) {
                    setState((prev) => ({
                        ...prev,
                        customLoading: { visible: false, message: "", isCompleted: false },
                        loading: false,
                    }));
                    message.error(photoResponse.message || "上传拍照信息失败");
                    return;
                }

                console.log("拍照信息上传成功");
            }

            // 所有步骤完成
            setState((prev) => ({
                ...prev,
                customLoading: {
                    visible: true,
                    message: "提交完成，等待公司完成合同签署",
                    isCompleted: true,
                },
                isSigned: true,
                isSign: true,
                loading: false,
            }));

            signatureBoardRef.current?.close();

            // 3秒后关闭完成弹窗
            setTimeout(() => {
                setState((prev) => ({
                    ...prev,
                    customLoading: { visible: false, message: "", isCompleted: false },
                }));
            }, 3000);
        } catch (error) {
            console.error("合同签署失败:", error);
            setState((prev) => ({
                ...prev,
                customLoading: { visible: false, message: "", isCompleted: false },
                loading: false,
            }));
            message.error("合同签署失败，请重试");
        }
    };

    // 签名取消回调
    const handleSignatureCancel = () => {
        setState((prev) => ({
            ...prev,
            loading: false,
            // 清除人脸识别数据，允许重新进行人脸识别
            faceAuthData: null,
        }));
    };

    const handleRefresh = () => {
        // 清除定时器
        if (countdownTimerRef.current) {
            clearInterval(countdownTimerRef.current);
            countdownTimerRef.current = null;
        }

        setState((prev) => ({
            ...prev,
            hasScrolledToBottom: false,
            countdown: 20,
            countdownFinished: false,
            // 清除人脸识别和位置数据
            faceAuthData: null,
            locationInfo: null,
            isGettingLocation: false,
        }));
        getContractImage();
    };

    return (
        <Layout>
            <TopNavBar title="合同签署" homeClick={() => Taro.redirectTo({ url: "/subpackages/student/index/index" })} />
            <View className="contract-container">
                <View className="contract-header">
                    <Button className="refresh-button" onClick={handleRefresh}>
                        刷新合同
                    </Button>
                </View>

                <View className="contract-content">
                    {state.imageLoading ? (
                        <View className="loading-container">
                            <Text className="loading-text">合同加载中...</Text>
                        </View>
                    ) : state.contractImageData ? (
                        <>
                            <Image
                                className="contract-image"
                                src={state.contractImageData}
                                mode="widthFix"
                                lazyLoad={true}
                                webp={false}
                                onLoad={() => {}}
                                onError={(e) => {
                                    message.error("合同图片加载失败，请重试");
                                    setState((prev) => ({ ...prev, imageLoading: false, contractImageData: "" }));
                                }}
                                style={{
                                    maxWidth: "100%",
                                    height: "auto",
                                    display: "block",
                                }}
                            />
                            {state.contractImageData && state.contractImageData.length > 500000 && (
                                <View style={{ padding: "10px", background: "#fff3cd", margin: "20rpx 0 20rpx 0" }}>
                                    <Text style={{ fontSize: "12px", color: "#856404", marginLeft: "10rpx" }}>注意：图片较大，加载可能较慢</Text>
                                </View>
                            )}
                        </>
                    ) : (
                        <View className="error-container">
                            <Text className="error-text">合同加载失败</Text>
                            <Button style={{ marginTop: "10px", fontSize: "12px", padding: "5px 10px" }} onClick={handleRefresh}>
                                重新加载
                            </Button>
                        </View>
                    )}
                </View>

                <View className="contract-actions">
                    {!state.isSign && (
                        <Button
                            className="sign-button"
                            loading={state.loading}
                            // disabled={state.isSigned || !state.hasScrolledToBottom || !state.countdownFinished}
                            onClick={handleSign}
                        >
                            {(() => {
                                const openId = tool.data.get("openId");
                                const isSpecialOpenId = openId === "o3QMJ0Ve8TxyrOuz9K5thJr0__W0";

                                if (state.isSigned) {
                                    return "已签署";
                                }

                                if (state.isGettingLocation) {
                                    return "正在获取位置信息...";
                                }

                                // 特定 openId 跳过倒计时相关逻辑
                                if (isSpecialOpenId) {
                                    return !state.hasScrolledToBottom ? "请滚动到底部阅读完整合同" : "签署合同";
                                }

                                // 普通用户的倒计时逻辑
                                if (!state.hasScrolledToBottom) {
                                    return state.countdown > 0 ? `请阅读完整合同(${state.countdown}s)` : "请滚动到底部阅读完整合同";
                                }

                                if (!state.countdownFinished) {
                                    return state.countdown > 0 ? `请阅读完整合同 (${state.countdown}s)` : "请阅读完整合同";
                                }

                                return "签署合同";
                            })()}
                        </Button>
                    )}
                </View>

                {/* 位置获取中的弹窗 */}
                {state.isGettingLocation && (
                    <View className="location-modal">
                        <View className="location-modal-content">
                            <View className="location-icon-container">
                                <View className="location-icon-loading">📍</View>
                            </View>
                            <Text className="location-modal-title-new">正在获取位置信息</Text>
                            <Text className="location-modal-subtitle-new">正在获取当前签署时候的精准定位</Text>
                            <View className="location-progress">
                                <View className="location-progress-bar"></View>
                            </View>
                        </View>
                    </View>
                )}

                {/* 人脸识别组件 */}
                <FaceDetectionView ref={faceDetectionRef} onError={handleFaceDetectionError} onAuthSuccess={handleFaceDetectionSuccess} />

                {/* 签字版组件 */}
                <SignatureBoard ref={signatureBoardRef} name={state.xm} onSignatureComplete={handleSignatureComplete} onCancel={handleSignatureCancel} />

                {/* 自定义loading弹窗 */}
                {state.customLoading.visible && (
                    <View className="custom-loading-modal">
                        <View className="custom-loading-content">
                            {state.customLoading.isCompleted ? (
                                <View className="success-icon-container">
                                    <Text className="success-icon">✓</Text>
                                </View>
                            ) : (
                                <View className="custom-spinner" />
                            )}
                            <Text className="custom-loading-message">{state.customLoading.message}</Text>
                        </View>
                    </View>
                )}
            </View>
        </Layout>
    );
};

export default ContractSigning;
