.contract-container {
    padding: 40rpx;
    min-height: 100vh;
    background-color: #f5f5f5;
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular, "Microsoft Yahei", sans-serif;
    padding-bottom: 160rpx;

    .contract-header {
        margin-bottom: 40rpx;
        text-align: center;

        .contract-title {
            font-size: 40rpx;
            font-weight: bold;
            color: #333;
        }

        .refresh-button {
            width: 200rpx;
            height: 60rpx;
            line-height: 60rpx;
            background-color: #fff;
            color: #333;
            border: 2rpx solid #ddd;
            border-radius: 30rpx;
            font-size: 24rpx;
            margin: 0 auto;
        }
    }

    .contract-content {
        // background-color: #fff;
        // padding: 40rpx;
        border-radius: 16rpx;
        margin-bottom: 40rpx;
        min-height: 800rpx;

        .contract-text {
            font-size: 28rpx;
            color: #666;
            line-height: 1.6;
        }

        .contract-image {
            width: 100%;
            border-radius: 8rpx;
            box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
        }

        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 400rpx;

            .loading-text {
                font-size: 28rpx;
                color: #999;
            }
        }

        .error-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 400rpx;

            .error-text {
                font-size: 28rpx;
                color: #ff4d4f;
                margin-bottom: 20rpx;
            }

            .retry-button {
                width: 200rpx;
                height: 60rpx;
                line-height: 60rpx;
                background-color: #1890ff;
                color: #fff;
                border-radius: 30rpx;
                font-size: 24rpx;
            }
        }
    }
}

.pagination-controls {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 20rpx;

    .page-button {
        width: 160rpx;
        height: 60rpx;
        line-height: 60rpx;
        font-size: 24rpx;
        margin: 0;
        background-color: #fff;
        color: #333;
        border: 2rpx solid #ddd;

        &[disabled] {
            background-color: #f5f5f5;
            color: #ccc;
        }
    }

    .page-info {
        font-size: 28rpx;
        color: #666;
    }
}

.contract-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 28rpx;
    box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
    z-index: 100;

    .sign-button {
        width: 100%;
        height: 88rpx;
        line-height: 88rpx;
        background-color: #07c160;
        color: #fff;
        border-radius: 44rpx;
        font-size: 32rpx;

        &[disabled] {
            background-color: #9be0b8;
            color: #fff;
        }
    }
}

/* 位置获取弹窗样式 */
.location-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;

    .location-modal-content {
        width: 600rpx;
        background-color: #fff;
        border-radius: 24rpx;
        padding: 60rpx 40rpx;
        text-align: center;
        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);

        .location-icon-container {
            margin-bottom: 40rpx;

            .location-icon-loading {
                font-size: 80rpx;
                color: #1890ff;
                animation: pulse 1.5s ease-in-out infinite;
            }
        }

        .location-modal-title {
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 20rpx;
        }

        .location-modal-subtitle {
            font-size: 28rpx;
            color: #666;
            line-height: 1.5;
            margin-bottom: 40rpx;
        }

        // 新的独立样式，避免被其他样式影响
        .location-modal-title-new {
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 30rpx;
            display: block;
        }

        .location-modal-subtitle-new {
            font-size: 28rpx;
            color: #666;
            line-height: 1.5;
            margin-top: 20rpx;
            margin-bottom: 40rpx;
            display: block;
        }

        .location-progress {
            width: 100%;
            height: 8rpx;
            background-color: #f0f0f0;
            border-radius: 4rpx;
            overflow: hidden;

            .location-progress-bar {
                height: 100%;
                background: linear-gradient(90deg, #1890ff, #52c41a);
                border-radius: 4rpx;
                animation: progressMove 2s linear infinite;
            }
        }
    }
}

/* 脉冲动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 进度条动画 */
@keyframes progressMove {
    0% {
        width: 0%;
        opacity: 0.8;
    }
    50% {
        width: 70%;
        opacity: 1;
    }
    100% {
        width: 100%;
        opacity: 0.8;
    }
}

/* 自定义loading弹窗样式 */
.custom-loading-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;

    .custom-loading-content {
        width: 600rpx;
        background-color: #fff;
        border-radius: 24rpx;
        padding: 80rpx 60rpx 60rpx 60rpx;
        text-align: center;
        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .custom-spinner {
            width: 80rpx;
            height: 80rpx;
            border: 6rpx solid #f3f3f3;
            border-top: 6rpx solid #2f54eb;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 60rpx;
        }

        .success-icon-container {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            background-color: #52c41a;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 60rpx;
            animation: successBounce 0.6s ease-out;

            .success-icon {
                font-size: 48rpx;
                color: #fff;
                font-weight: bold;
            }
        }

        .custom-loading-message {
            font-size: 32rpx;
            color: #333;
            line-height: 1.4;
            text-align: center;
        }
    }
}

/* spinner旋转动画 */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 成功图标弹跳动画 */
@keyframes successBounce {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}
