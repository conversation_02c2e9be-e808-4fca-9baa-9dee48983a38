import Taro, { useReady } from "@tarojs/taro";
import { useState, useCallback } from "react";
import { View, Text } from "@tarojs/components";
import { Button, Empty } from "@nutui/nutui-react-taro";
import Layout from "@components/Layout";
import { message } from "@components/MessageApi/MessageApiSingleton";
import request from "@service/request";
import { login } from "@utils/login";
import "./index.scss";

interface PaymentItem {
    Id: string;
    PayMoney: number;
    CostTypeName: string;
    CreateTime: string;
    Remark: string;
    Tuition: boolean;
    DiscountMoney: number;
    NoPay: number;
    JxPayCreateTime: string;
    JxPayCreateUserName: string | null;
    PayTypeName: string | null;
    JxPayRemark: string | null;
}

const PaymentQueryPage = () => {
    const [unpaidList, setUnpaidList] = useState<PaymentItem[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [page, setPage] = useState(1);
    const pageSize = 10;

    // 查询欠费信息
    const fetchPaymentData = async (isLoadMore = false) => {
        if (isLoading) return;
        setIsLoading(true);

        if (!isLoadMore) {
            message.openLoading("正在查询信息");
        }

        try {
            const res = await request.post<
                API.Result<{
                    current: number;
                    size: number;
                    total: number;
                    pages: number;
                    data: PaymentItem[];
                }>
            >("/Jx/Pay/WxPay/myNoPayList", {
                page,
                pageSize,
            });

            setIsLoading(false);
            if (!isLoadMore) {
                message.closeLoading();
            }

            if (res.success) {
                const newList = res.data.data || [];
                if (isLoadMore) {
                    setUnpaidList((prev) => [...prev, ...newList]);
                } else {
                    setUnpaidList(newList);
                }
                setHasMore(page < res.data.pages);
            } else {
                message.error("查询失败，请稍后再试");
            }
        } catch (error) {
            setIsLoading(false);
            if (!isLoadMore) {
                message.closeLoading();
            }
            message.error("查询出错，请稍后再试");
            console.error("Query error:", error);
        }
    };

    // 处理下拉刷新
    const onRefresh = useCallback(async () => {
        setPage(1);
        await fetchPaymentData();
        Taro.stopPullDownRefresh();
    }, []);

    // 处理加载更多
    const onLoadMore = useCallback(async () => {
        if (!hasMore || isLoading) return;
        setPage((prev) => prev + 1);
        await fetchPaymentData(true);
    }, [hasMore, isLoading]);

    // 跳转到支付页面
    const handlePay = (item: PaymentItem) => {
        Taro.navigateTo({
            url: `/subpackages/student/pay/payment/index?id=${item.Id}&amount=${item.PayMoney}&name=${encodeURIComponent(item.CostTypeName)}`,
        });
    };

    // 渲染支付项目卡片
    const renderPaymentCard = (item: PaymentItem) => (
        <View key={item.Id} className="payment-card">
            <View className="payment-header">
                <Text className="payment-name">{item.CostTypeName}</Text>
                <Text className="payment-amount">¥{item.NoPay.toFixed(2)}</Text>
            </View>
            <View className="payment-body">
                <View className="payment-info">
                    <Text className="info-text">{item.CreateTime}</Text>
                </View>
                <View className="payment-info">
                    <Text className="info-text">
                        应缴金额: ¥{item.PayMoney.toFixed(2)} | 未缴金额: ¥{item.NoPay.toFixed(2)}
                    </Text>
                </View>
                {item.DiscountMoney > 0 && (
                    <View className="payment-info">
                        <Text className="info-text">优惠金额: ¥{item.DiscountMoney.toFixed(2)}</Text>
                    </View>
                )}
            </View>
            <View className="payment-footer">
                <Button className="pay-button" type="primary" onClick={() => handlePay(item)}>
                    立即支付
                </Button>
            </View>
        </View>
    );

    useReady(async () => {
        try {
            await login();
            await fetchPaymentData();
        } catch (error) {
            console.error("Failed to get OpenId:", error);
            message.error("获取用户信息失败，请稍后再试");
        }
    });

    return (
        <Layout>
            <View className="payment-container">
                {unpaidList.length > 0 ? (
                    <View className="payment-list">
                        {unpaidList.map((item) => renderPaymentCard(item))}
                        <View className="bottom-line">
                            {hasMore ? (
                                <View className="loading-more" onClick={onLoadMore}>
                                    {isLoading ? "加载中..." : "点击加载更多数据"}
                                </View>
                            ) : (
                                <View className="bottom-text">我是有底线的</View>
                            )}
                        </View>
                    </View>
                ) : (
                    <Empty description="暂无待缴费项目" />
                )}
            </View>
        </Layout>
    );
};

export default PaymentQueryPage;
