.payment-container {
    font-family: "PingFang SC", "Hiragino Sans GB", "Helvetica Rounded", "Arial Rounded MT Bold", "Microsoft YaHei", sans-serif;
    padding: 32rpx;
    min-height: 100vh;
    background: #f5f5f5;

    .search-section {
        background: #fff;
        border-radius: 16rpx;
        padding: 32rpx;
        margin-bottom: 32rpx;

        .search-form {
            .form-item {
                margin-bottom: 32rpx;

                .label {
                    display: block;
                    font-size: 28rpx;
                    color: #333;
                    margin-bottom: 16rpx;
                }

                .input-wrapper {
                    display: flex;
                    align-items: center;
                    background: #f5f5f5;
                    padding: 16rpx 24rpx;
                    border-radius: 16rpx;
                    height: 80rpx;
                    box-sizing: border-box;

                    .input {
                        flex: 1;
                        margin: 0 16rpx;
                        font-size: 28rpx;
                        background: transparent;
                    }

                    .clear-icon {
                        padding: 8rpx;
                    }
                }
            }
        }

        .search-button {
            width: 100%;
            height: 80rpx;
            border-radius: 40rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32rpx;

            .nutui-iconfont {
                margin-right: 16rpx;
            }
        }
    }

    .tabs-section {
        background: #fff;
        border-radius: 16rpx;
        padding: 32rpx;

        .payment-list {
            margin-top: 32rpx;
        }
    }

    .payment-list {
        .payment-card {
            background: #fff;
            border-radius: 24rpx;
            padding: 24rpx;
            margin-bottom: 48rpx;
            box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

            .payment-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 40rpx;
                padding-bottom: 32rpx;
                border-bottom: 2rpx solid #eee;

                .payment-name {
                    font-size: 32rpx;
                    font-weight: 500;
                    color: #333;
                }

                .payment-amount {
                    font-size: 36rpx;
                    font-weight: 600;
                    color: #ff4d4f;
                }
            }

            .payment-body {
                .payment-info {
                    margin-bottom: 24rpx;
                    display: flex;
                    align-items: center;

                    .info-text {
                        font-size: 24rpx;
                        color: #666;
                        line-height: 1.5;
                    }
                }
            }

            .payment-footer {
                margin-top: 40rpx;
                padding-top: 32rpx;
                border-top: 2rpx solid #eee;

                .pay-button {
                    width: 100%;
                    height: 72rpx;
                    font-size: 28rpx;
                    border-radius: 44rpx;
                }
            }
        }

        .bottom-line {
            position: relative;
            text-align: center;
            margin: 48rpx 0;

            &::before {
                content: "";
                position: absolute;
                left: 0;
                top: 50%;
                width: 100%;
                height: 2rpx;
                background: #eee;
            }

            .loading-more {
                position: relative;
                display: inline-block;
                padding: 0 32rpx;
                background: #f5f5f5;
                color: #999;
                font-size: 24rpx;
                cursor: pointer;
            }

            .bottom-text {
                position: relative;
                display: inline-block;
                padding: 0 32rpx;
                background: #f5f5f5;
                color: #999;
                font-size: 24rpx;
            }
        }
    }
}

// 自定义 Tabs 样式
:global {
    .nut-tabs {
        .nut-tabs__titles {
            background: #f5f5f5;
            border-radius: 16rpx;
            padding: 8rpx;

            .nut-tabs__titles-item {
                font-size: 28rpx;
                padding: 16rpx 48rpx;

                &.active {
                    background: #fff;
                    border-radius: 12rpx;
                    font-weight: 500;
                }
            }
        }

        .nut-tabs__content {
            padding: 32rpx 0;
        }
    }
}

.button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}
