import { useReady } from "@tarojs/taro";
import React from "react";
import TopNavBar from "@components/topNavBar";
import "./index.scss";
import "@utils/app.scss";
import { login } from "@utils/login";
import MessageApi from "@components/MessageApi/MessageApi";
import { Button, Cell, CellGroup, Checkbox } from "@nutui/nutui-react-taro";
import { CheckNormal } from "@nutui/icons-react-taro";

import { Input } from "@nutui/nutui-react-taro";
import { QValue } from "@utils/util";
import request from "@service/request";

const App = () => {
    const message: any = React.useRef();
    const [userInfo, setUserInfo] = React.useState<API.UserInfo>(Object);

    const [carNumber, setCarNumber] = React.useState("");
    const [jxFieldName, setJxFieldName] = React.useState("");
    const [tenantName, setTenantName] = React.useState("");
    const [tenantId, setTenantId] = React.useState("");

    const [xm, setXm] = React.useState("");
    const [sfzmhm, setSfzmhm] = React.useState("");

    const [jxDeviceId, setJxDeviceId] = React.useState("");

    const [faceType, setFaceType] = React.useState("");

    useReady(() => {
        let id = QValue("Id");
        if (!id || id == "") {
            message.current.openDialog("请从设备上二维码扫码进来本页面,当前页面参数错误,请重新扫码错误!", "页面参数错误");
        } else {
            setJxDeviceId(id);
        }

        login().then((info: API.UserInfo) => {
            setUserInfo(info);
            console.log(userInfo);
        });
    });

    React.useEffect(() => {
        if (jxDeviceId && jxDeviceId !== "") getJxDeviceInfo();
    }, [jxDeviceId]);

    const getJxDeviceInfo = () => {
        request
            .post<
                API.Result<{
                    CarNumber: string;
                    TenantName: string;
                    TenantId: string;
                }>
            >("/JiaXiao/JxDevice/getJxDeviceInfo2", {
                Id: jxDeviceId,
            })
            .then((json) => {
                if (json && json.success) {
                    setCarNumber(json.data.CarNumber);
                    setTenantName(json.data.TenantName);
                    setTenantId(json.data.TenantId);
                } else {
                    message.current.openDialog("获取信息失败", json.message, () => {});
                }
            });
    };

    return (
        <>
            <MessageApi ref={message}></MessageApi>
            <TopNavBar title="信息注册"></TopNavBar>

            <view className="content-page">
                <Cell.Group title="">
                    <Cell title="所属单位" extra={tenantName} />
                    {carNumber != "" && <Cell title="车牌号码" extra={carNumber} />}
                    {jxFieldName != "" && <Cell title="训练场地" extra={jxFieldName} />}
                </Cell.Group>
                <Cell.Group title="请输入您的身份信息提交校验">
                    <Cell>
                        <Checkbox
                            value={"student"}
                            style={{ marginRight: "8px" }}
                            shape="button"
                            label={
                                <div
                                    style={{
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                    }}
                                >
                                    <div>我是学员</div>
                                    <div style={{ color: "gray" }}>驾校在培学员</div>
                                </div>
                            }
                            activeIcon={<CheckNormal className="nut-checkbox-button-icon-checked" />}
                            defaultChecked={false}
                            checked={faceType == "student"}
                            onChange={(e) => {
                                if (e) {
                                    setFaceType("student");
                                } else {
                                    setFaceType("");
                                }
                            }}
                        />
                        <Checkbox
                            value={"staff"}
                            style={{ marginRight: "8px" }}
                            shape="button"
                            label={
                                <div
                                    style={{
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                    }}
                                >
                                    <div>我是员工</div>
                                    <div style={{ color: "gray" }}>公司在职员工</div>
                                </div>
                            }
                            activeIcon={<CheckNormal className="nut-checkbox-button-icon-checked" />}
                            defaultChecked={false}
                            checked={faceType == "staff"}
                            onChange={(e) => {
                                if (e) {
                                    setFaceType("staff");
                                } else {
                                    setFaceType("");
                                }
                            }}
                        />
                    </Cell>
                    <Cell>
                        <Input
                            placeholder="请输入您的真实姓名"
                            type="text"
                            value={xm}
                            onChange={(val) => {
                                setXm(val);
                            }}
                        />
                    </Cell>
                    <Cell
                        extra={
                            <Input
                                placeholder="请输入您的证件号码"
                                type="text"
                                value={sfzmhm}
                                onChange={(val) => {
                                    setSfzmhm(val);
                                }}
                            />
                        }
                    ></Cell>
                </Cell.Group>
            </view>
            <view className="footer">
                <view style={{ padding: "10px" }}>
                    <Button
                        type="primary"
                        size="large"
                        style={{
                            width: "100%",
                        }}
                        onClick={() => {
                            if (xm.length < 2) {
                                message.current.openToast("请输入姓名", "fail");
                                return;
                            }
                            if (sfzmhm.length < 8) {
                                message.current.openToast("请输入证件号码", "fail");
                                return;
                            }
                        }}
                    >
                        信息校验并注册
                    </Button>
                </view>
            </view>
        </>
    );
};
export default App;
