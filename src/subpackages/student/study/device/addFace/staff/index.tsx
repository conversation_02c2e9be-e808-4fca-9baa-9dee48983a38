import { useReady } from "@tarojs/taro";
import React from "react";
import TopNavBar from "@components/topNavBar";
import "./index.scss";
import "@utils/app.scss";
import { login } from "@utils/login";
import MessageApi from "@components/MessageApi/MessageApi";
import { Button, Cell, Input } from "@nutui/nutui-react-taro";
import { QValue } from "@utils/util";
import { Current } from "@tarojs/runtime";
import request from "@service/request";
import { getJtDomain } from "@service/base";

const App = () => {
    const message: any = React.useRef();
    const [userInfo, setUserInfo] = React.useState<API.UserInfo>(Object);

    const [carNumber, setCarNumber] = React.useState("");
    const [tenantName, setTenantName] = React.useState("");
    const [tenantId, setTenantId] = React.useState("");

    const [xm, setXm] = React.useState("");
    const [sfzmhm, setSfzmhm] = React.useState("");

    const [jxDeviceId, setJxDeviceId] = React.useState("");

    useReady(() => {
        let id = QValue("Id");
        if (!id || id == "") {
            message.current.openDialog("请从设备上二维码扫码进来本页面,当前页面参数错误,请重新扫码错误!", "页面参数错误");
        } else {
            setJxDeviceId(id);
        }

        login().then((info: API.UserInfo) => {
            setUserInfo(info);
            console.log(userInfo);
            console.log("Id:" + jxDeviceId);
        });
    });

    React.useEffect(() => {
        if (jxDeviceId && jxDeviceId !== "") getJxDeviceInfo();
    }, [jxDeviceId]);

    const getJxDeviceInfo = () => {
        request
            .post<
                API.Result<{
                    CarNumber: string;
                    TenantName: string;
                    TenantId: string;
                }>
            >("/JiaXiao/JxDevice/getJxDeviceInfo2", {
                Id: jxDeviceId,
            })
            .then((json) => {
                if (json && json.success) {
                    setCarNumber(json.data.CarNumber);
                    setTenantName(json.data.TenantName);
                    setTenantId(json.data.TenantId);
                } else {
                    message.current.openDialog("获取信息失败", json.message, () => {});
                }
            });
    };

    return (
        <>
            <MessageApi ref={message}></MessageApi>
            <TopNavBar title="教练设备信息注册"></TopNavBar>

            <view
                style={{
                    padding: "10px 15px",
                    //fontSize: '20px'
                }}
            >
                <Cell.Group title="">
                    <Cell title="所属单位" extra={tenantName} />
                    <Cell title="车牌号码" extra={carNumber} />
                </Cell.Group>

                <Cell.Group title="请输入报名登记的信息并提交" className={"input-cell"}>
                    <Cell title={""}>
                        <Input
                            className="nut-input-text"
                            placeholder="请输入您的真实姓名"
                            type="text"
                            value={xm}
                            onChange={(val) => {
                                setXm(val);
                            }}
                        />
                    </Cell>
                    <Cell title={""}>
                        <Input
                            className="nut-input-text"
                            placeholder="请输入您的证件号码"
                            type="text"
                            value={sfzmhm}
                            onChange={(val) => {
                                setSfzmhm(val);
                            }}
                        />
                    </Cell>
                </Cell.Group>
            </view>
            <view className="footer">
                <view style={{ padding: "10px" }}>
                    <Button
                        disabled={!jxDeviceId || jxDeviceId == ""}
                        type="primary"
                        size="large"
                        style={{
                            width: "100%",
                        }}
                        onClick={() => {
                            if (xm.length < 2) {
                                message.current.openToast("请输入姓名", "fail");
                                return;
                            }
                            if (sfzmhm.length < 8) {
                                message.current.openToast("请输入证件号码", "fail");
                                return;
                            }
                            message.current.openConfirm(
                                "是否确认提交信息到当前车载设备注册,完成注册以后,车载设备才可以进行培训需要的人脸识别,过长时间不学或者换车都需要重新注册!",
                                "确认操作",
                                () => {
                                    message.current.openLoading("正在生成数据");
                                    request
                                        .post<
                                            API.Result<{
                                                StaffId: string;
                                                TenantId: string;
                                            }>
                                        >("/JiaXiao/JxDevice/pushStaffFaceFeature", {
                                            Id: jxDeviceId,
                                            xm: xm,
                                            sfzmhm: sfzmhm,
                                            TenantId: tenantId,
                                        })
                                        .then((json) => {
                                            if (json && json.success) {
                                                message.current.openLoading("等待设备注册");
                                                request
                                                    .post<
                                                        API.Result<{
                                                            data: string;
                                                        }>
                                                    >(getJtDomain() + "/jt808webapi/pushCoach", {
                                                        CoachId: json.data.StaffId,
                                                        TenantId: json.data.TenantId,
                                                        JxDeviceId: jxDeviceId,
                                                    })
                                                    .then((json) => {
                                                        if (json && json.success) {
                                                            message.current.closeLoading();
                                                            if (json.data.data) {
                                                                message.current.openDialog("提交操作完成", json.data.data, () => {});
                                                            } else {
                                                                message.current.openDialog("提交操作完成", json.message, () => {});
                                                            }
                                                        } else {
                                                            message.current.closeLoading();
                                                            message.current.openDialog("注册操作失败", json.message, () => {});
                                                        }
                                                    })
                                                    .catch(() => {
                                                        message.current.closeLoading();
                                                    });
                                            } else {
                                                message.current.closeLoading();
                                                message.current.openDialog("获取信息失败", json.message, () => {});
                                            }
                                        })
                                        .catch(() => {
                                            message.current.closeLoading();
                                        });
                                }
                            );
                        }}
                    >
                        提交信息注册
                    </Button>
                </view>
            </view>
        </>
    );
};
export default App;
