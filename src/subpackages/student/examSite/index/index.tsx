import { useReady } from "@tarojs/taro";
import { useState } from "react";
import TopNavBar from "@components/topNavBar";
import { Dialog, Toast } from "@nutui/nutui-react-taro";
import "./index.scss";
import "@utils/app.scss";

import { login } from "@utils/login";

/**
 * 6. 签署完成 数据上传页面
 * @returns
 */
const App = () => {
    /**
     * Toast 需要的 State
     */
    const [toastShow, setToastShow] = useState(false);
    const [toast, setToast] = useState<API.pdToast>(Object);

    /**
     * Dialog 需要的 State
     */
    const [dialogShow, setDialogShow] = useState(false);
    const [dialog, setDialog] = useState<API.pdDialog>(Object);

    /**
     * 用户登录的 信息
     */
    const [userInfo, setUserInfo] = useState<API.UserInfo>(Object);

    /**
     * 页面 ready 方法
     */
    useReady(() => {
        login().then((userInfo: API.UserInfo) => {
            setUserInfo(userInfo);
        });
    });

    return (
        <>
            <TopNavBar title="计时学车"></TopNavBar>

            {/* Toast 和 Dialog 的通用相关代码 开始 */}
            {toastShow && !dialogShow && (
                <Toast
                    msg={toast.msg}
                    visible={toastShow}
                    type={toast.type}
                    onClose={() => {
                        setToastShow(false);
                        toast.fn();
                    }}
                    // cover={toast.cover}
                    // coverColor="rgba(6, 6, 6, 0.8)"
                    duration={toast.duration}
                    icon=<toast.icon />
                    iconSize="20"
                />
            )}
            {dialogShow && (
                <Dialog
                    closeOnOverlayClick={false}
                    title={dialog.title}
                    confirmText={dialog.okText}
                    hideCancelButton={dialog.noCancelBtn}
                    cancelText={dialog.cancelText}
                    // textAlign={dialog.textAlign}
                    visible={dialogShow}
                    lockScroll
                    footerDirection="vertical"
                    onConfirm={() => {
                        dialog.ok();
                        setDialogShow(false);
                    }}
                    onCancel={() => {
                        dialog.cancel();
                        setDialogShow(false);
                    }}
                >
                    <view
                        style={{
                            lineHeight: "40rpx",
                        }}
                    >
                        {dialog.msg}
                    </view>
                </Dialog>
            )}
            {/* Toast 和 Dialog 的通用相关代码 结束 */}
        </>
    );
};
export default App;
