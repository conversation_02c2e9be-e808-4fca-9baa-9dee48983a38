import { useReady } from "@tarojs/taro";
import React, { useState, useEffect, useRef } from "react";
import { View, Text, Form, Button, Input, Textarea, Image } from "@tarojs/components";
import { Picker, Cell, ConfigProvider, Popup } from "@nutui/nutui-react-taro";
import { login } from "@utils/login";
import request from "@service/request";
import { message } from "@components/MessageApi/MessageApiSingleton";
import Taro from "@tarojs/taro";
import { tool } from "@utils/tool";
import "./index.scss";
import "@utils/app.scss";
import Layout from "@/components/Layout";

type TabType = "points" | "cashback";
const TAB_STORAGE_KEY = "longtime-coupon-tab-selection";

const App = () => {
    const [userInfo, setUserInfo] = React.useState<API.UserInfo>(Object);
    const [activeTab, setActiveTab] = useState<TabType>(() => {
        // Initialize with saved tab selection or default to "points"
        return tool.data.get(TAB_STORAGE_KEY) || "points";
    });
    const [totalBalance, setTotalBalance] = useState<number>(0);
    const [totalAmount, setTotalAmount] = useState<number>(0);
    const [transactions, setTransactions] = useState<any[]>([]);
    const [showTypePicker, setShowTypePicker] = useState(false);
    const [loading, setLoading] = useState(false);
    const [tenantId, setTenantId] = useState<string>("");
    const [currentPage, setCurrentPage] = useState(1);
    const [hasNextPage, setHasNextPage] = useState(false);
    const [totalPages, setTotalPages] = useState(1);
    const [isSlideUp, setIsSlideUp] = useState(false);
    const [qrCodeUrl, setQrCodeUrl] = useState<string>("");
    const [qrCodeLoading, setQrCodeLoading] = useState(false);
    const [hasUsePointPermission, setHasUsePointPermission] = useState(false);
    const [showUsePointsModal, setShowUsePointsModal] = useState(false);
    const [pointsAmount, setPointsAmount] = useState("");
    const [pointsRemark, setPointsRemark] = useState("");
    const [scanLoading, setScanLoading] = useState(false);
    const [isLoadingMore, setIsLoadingMore] = useState(false);

    const typeOptions = [
        { text: "我的积分", value: "points" },
        { text: "现金红包", value: "cashback" },
    ];

    useReady(() => {
        // 获取URL参数中的TenantId
        const params = Taro.getCurrentInstance().router?.params;
        if (params?.TenantId) {
            setTenantId(params.TenantId as string);
        }

        login().then((info: API.UserInfo) => {
            setUserInfo(info);
        });
    });

    useEffect(() => {
        if (tenantId) {
            // handleSearch(1);
            checkUsePointPermission();
        }
    }, [tenantId]);

    useEffect(() => {
        if (hasNextPage && !loading && !isLoadingMore) {
            setIsLoadingMore(true);
            handleSearch(currentPage + 1).finally(() => {
                setIsLoadingMore(false);
            });
        }
    }, [hasNextPage, loading, currentPage]);

    const handleSearch = async (page = 1) => {
        if (!tenantId) {
            message.error("缺少必要参数");
            return;
        }

        setLoading(true);
        try {
            const endpoint = activeTab === "points" ? "/Jx/ExamSite/Wx/PointCoupon/getMyPointCouponList" : "/Jx/ExamSite/Wx/CashCoupon/getMyCouponList";

            const response = await request.post<any>(endpoint, {
                current: page,
                pageSize: 10,
                type: activeTab,
                tenantId: tenantId,
            });

            if (response.success) {
                const { data: responseData, summary, totalPoints } = response.data;
                const { data, pages, hasNextPages } = responseData;
                // 如果是第一页，直接设置数据；如果是加载更多，则追加数据
                setTransactions((prev) => (page === 1 ? data || [] : [...prev, ...(data || [])]));

                console.log("totalPoints", totalPoints);
                setTotalBalance(activeTab === "points" ? totalPoints || 0 : 0); // Use totalPoints for points tab
                setTotalAmount(Math.abs(summary?.TotalAmount || 0));
                setCurrentPage(page);
                setHasNextPage(hasNextPages);
                setTotalPages(pages);
            } else {
                message.error(response.message || "查询失败");
            }
        } catch (error) {
            console.error("Search failed:", error);
            message.error("查询失败，请重试");
        } finally {
            setLoading(false);
        }
    };

    const fetchQRCode = async () => {
        if (!tenantId) {
            message.error("缺少必要参数");
            return;
        }
        setQrCodeLoading(true);
        try {
            const response = await request.post<any>(`/Jx/ExamSite/Wx/PointCoupon/getUserQrCode/${tenantId}`);
            if (response.success) {
                let qrCodeData = response.data;
                // Check if the response is base64 and add prefix if needed
                if (qrCodeData && !qrCodeData.startsWith("data:image")) {
                    qrCodeData = `data:image/png;base64,${qrCodeData}`;
                }
                setQrCodeUrl(qrCodeData);
            } else {
                message.error(response.message || "获取二维码失败");
            }
        } catch (error) {
            console.error("Fetch QR code failed:", error);
            message.error("获取二维码失败，请重试");
        } finally {
            setQrCodeLoading(false);
        }
    };

    const handleBalanceClick = () => {
        setIsSlideUp(!isSlideUp);
        if (!isSlideUp && !qrCodeUrl) {
            fetchQRCode();
        }
    };

    const checkUsePointPermission = async () => {
        try {
            const response = await request.post<any>("/Jx/ExamSite/Wx/PointCoupon/haveUsePointCouponRole", {
                tenantId: tenantId,
            });
            if (response.success) {
                setHasUsePointPermission(response.data);
            }
        } catch (error) {
            console.error("Check permission failed:", error);
        }
    };

    const handleUsePoints = () => {
        setShowUsePointsModal(true);
    };

    const handleScanCode = () => {
        if (!pointsAmount || !pointsRemark.trim()) {
            message.error("请填写完整信息");
            return;
        }

        Taro.scanCode({
            success: async (res) => {
                console.log("Scan result:", res);
                const scannedContent = res.result;
                // 验证二维码内容格式
                if (scannedContent.startsWith("UserId#")) {
                    const userId = scannedContent.substring(7); // 移除 'UserId#' 前缀
                    // 验证是否为有效的 GUID 格式
                    const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
                    if (guidRegex.test(userId)) {
                        try {
                            setScanLoading(true);
                            const response = await request.post<any>("/Jx/ExamSite/Wx/PointCoupon/consumePoints", {
                                FromUserId: userId,
                                Point: parseInt(pointsAmount),
                                Remark: pointsRemark.trim(),
                                TenantId: tenantId,
                            });

                            if (response.success) {
                                message.success("消费积分成功");
                                setShowUsePointsModal(false);
                                // 重置表单
                                setPointsAmount("");
                                setPointsRemark("");
                                // 重新查询数据
                                handleSearch(1);
                            } else {
                                message.error(response.message || "消费积分失败");
                            }
                        } catch (error) {
                            console.error("Consume points failed:", error);
                            message.error("消费积分失败，请重试");
                        } finally {
                            setScanLoading(false);
                        }
                    } else {
                        message.error("无效的会员码格式");
                    }
                } else {
                    message.error("无效的会员码");
                }
            },
            fail: (err) => {
                console.error("Scan failed:", err);
                message.error("扫码失败，请重试");
            },
        });
    };

    const handleTabChange = (value: TabType) => {
        setActiveTab(value);
        tool.data.set(TAB_STORAGE_KEY, value);
    };

    return (
        <Layout>
            <ConfigProvider
                theme={{
                    nutuiPickerListHeight: "40vh",
                    nutuiCalendarHeight: "80vh",
                }}
            >
                <View className="coupon-container">
                    {/* 查询表单 */}
                    <View className="search-card">
                        <Form onSubmit={() => handleSearch(1)}>
                            <View className="form-row">
                                <View className="form-item">
                                    <View className="form-label-wrapper">
                                        <Text className="form-label">返利类型</Text>
                                    </View>
                                    <View className="form-cell-wrapper">
                                        <Cell
                                            title={typeOptions.find((opt) => opt.value === activeTab)?.text || "请选择类型"}
                                            onClick={() => setShowTypePicker(true)}
                                            className="picker-cell"
                                        />
                                    </View>
                                </View>
                                {hasUsePointPermission && activeTab === "points" && (
                                    <View className="form-actions">
                                        <Button className="use-points-btn" onClick={handleUsePoints}>
                                            消费积分
                                        </Button>
                                    </View>
                                )}
                                {activeTab === "points" && (
                                    <View className="form-actions">
                                        <Button className="use-points-btn" onClick={handleBalanceClick}>
                                            我的二维码
                                        </Button>
                                    </View>
                                )}
                                <View className="form-actions">
                                    <Button type="primary" loading={loading} formType="submit">
                                        查询
                                    </Button>
                                </View>
                                {/* 现金返现提示，仅在现金返现tab下显示 */}
                                {activeTab === "cashback" && (
                                    <View style={{ textAlign: "center", fontSize: "22rpx", color: "#999", marginTop: "8rpx", marginBottom: "16rpx" }}>
                                        系统只会展示过去30天在当前考场领取的奖励
                                    </View>
                                )}
                            </View>
                        </Form>
                    </View>

                    {/* 交易列表 */}
                    <View className="transaction-list">
                        {transactions.length === 0 ? (
                            <View className="no-data">
                                <View className="custom-divider">
                                    <View className="divider-line left"></View>
                                    <Text className="divider-text">暂无数据</Text>
                                    <View className="divider-line right"></View>
                                </View>
                            </View>
                        ) : (
                            <>
                                {activeTab === "cashback"
                                    ? transactions.map((transaction, index) => {
                                          // 付款时间处理
                                          const isPaid = transaction.PayTime && transaction.PayTime !== "1900-01-01 00:00:00";
                                          return (
                                              <View key={transaction.Id} className="transaction-item">
                                                  <View className="transaction-info">
                                                      <View className="transaction-main">
                                                          <Text className="transaction-index">#{transaction.RowIndex}</Text>
                                                          <Text className="transaction-title">{transaction.CreateTime}</Text>
                                                          <View className={`status-box ${isPaid ? "success" : "failed"}`}>{isPaid ? "付款成功" : "付款失败"}</View>
                                                      </View>
                                                      <Text className="transaction-date">备注：{transaction.Remark}</Text>
                                                      <Text className="transaction-date">姓名：{transaction.xm}</Text>
                                                      <Text className="transaction-date">证书：{Math.abs(transaction.PayMoney)}</Text>
                                                      <Text className="transaction-date">付款时间：{isPaid ? transaction.PayTime : ""}</Text>
                                                  </View>
                                              </View>
                                          );
                                      })
                                    : transactions.map((transaction, index) => (
                                          <View key={transaction.Id} className="transaction-item">
                                              <View className="transaction-info">
                                                  <View className="transaction-main">
                                                      <Text className="transaction-index">#{transaction.RowIndex}</Text>
                                                      <Text className={`transaction-status ${transaction.Point > 0 ? "positive" : "negative"}`}>
                                                          {transaction.Point > 0 ? "收入" : "支出"}
                                                      </Text>
                                                      <Text className="transaction-title">{transaction.CreateTime}</Text>
                                                  </View>
                                                  <Text className="transaction-date">{transaction.Remark}</Text>
                                              </View>
                                              <Text className={`transaction-amount ${transaction.Point > 0 ? "positive" : "negative"}`}>
                                                  {transaction.Point > 0 ? "+" : ""}
                                                  {transaction.Point}
                                              </Text>
                                          </View>
                                      ))}

                                {/* 加载更多 */}
                                {hasNextPage && (
                                    <View className="load-more">
                                        <View className="custom-divider">
                                            <View className="divider-line left"></View>
                                            <Text className="divider-text">加载中...</Text>
                                            <View className="divider-line right"></View>
                                        </View>
                                    </View>
                                )}

                                {/* 底部提示 */}
                                {!hasNextPage && transactions.length > 0 && (
                                    <View className="no-more">
                                        <View className="custom-divider">
                                            <View className="divider-line left"></View>
                                            <Text className="divider-text">我是有底线的</Text>
                                            <View className="divider-line right"></View>
                                        </View>
                                    </View>
                                )}
                            </>
                        )}
                    </View>

                    {/* 类型选择器 */}
                    <Picker
                        visible={showTypePicker}
                        options={[typeOptions]}
                        onConfirm={(value) => {
                            handleTabChange(value[0].value as TabType);
                            setShowTypePicker(false);
                        }}
                        onClose={() => setShowTypePicker(false)}
                    />

                    {/* Use Points Modal */}
                    <Popup visible={showUsePointsModal} position="bottom" onClose={() => setShowUsePointsModal(false)} style={{ height: "750rpx" }}>
                        <View className="use-points-modal">
                            <View className="modal-header">
                                <Text className="modal-title">消费积分</Text>
                            </View>
                            <View className="modal-content">
                                <View className="input-group">
                                    <Text className="input-label" style={{ fontSize: "28rpx", marginBottom: "16rpx" }}>
                                        数额
                                    </Text>
                                    <Input
                                        type="number"
                                        value={pointsAmount}
                                        onInput={(e) => setPointsAmount(e.detail.value)}
                                        placeholder="请输入正整数"
                                        className="points-input"
                                        style={{
                                            height: "88rpx",
                                            fontSize: "28rpx",
                                            padding: "0 24rpx",
                                            borderRadius: "8rpx",
                                            border: "2rpx solid #E5E5E5",
                                            width: "auto",
                                        }}
                                    />
                                </View>
                                <View className="input-group">
                                    <Text className="input-label" style={{ fontSize: "28rpx", marginBottom: "16rpx" }}>
                                        备注
                                    </Text>
                                    <Textarea
                                        value={pointsRemark}
                                        onInput={(e) => setPointsRemark(e.detail.value)}
                                        placeholder="请输入备注信息"
                                        className="remark-input"
                                        style={{
                                            height: "160rpx",
                                            fontSize: "28rpx",
                                            padding: "24rpx",
                                            borderRadius: "8rpx",
                                            border: "2rpx solid #E5E5E5",
                                            width: "auto",
                                        }}
                                    />
                                </View>
                            </View>
                            <View className="modal-footer">
                                <Button
                                    className="scan-button"
                                    onClick={handleScanCode}
                                    disabled={!pointsAmount || !pointsRemark.trim() || scanLoading}
                                    loading={scanLoading}
                                    style={{
                                        width: "90%",
                                        margin: "0 auto",
                                        height: "88rpx",
                                        lineHeight: "88rpx",
                                        fontSize: "32rpx",
                                        borderRadius: "44rpx",
                                        backgroundColor: !pointsAmount || !pointsRemark.trim() ? "#CCCCCC" : "#1677FF",
                                        color: "#FFFFFF",
                                    }}
                                >
                                    扫会员码
                                </Button>
                            </View>
                        </View>
                    </Popup>
                </View>
                {qrCodeUrl}

                {/* 底部余额和二维码 */}
                <View className={`footer-balance safe-area-bottom no-slide-up`}>
                    <View className="balance-content">
                        <View className="balance-left">
                            <Text className="balance-label">{activeTab === "points" ? "我的积分余额" : "总返利金额"}</Text>
                            <Text className="balance-amount">{activeTab === "points" ? totalBalance : totalAmount}</Text>
                        </View>
                    </View>
                </View>

                {/* QR Code Modal */}
                <Popup visible={isSlideUp} position="center" onClose={() => setIsSlideUp(false)}>
                    <View className="qr-code-modal">
                        <View className="qr-code-container">
                            {qrCodeLoading ? (
                                <View className="qr-code-loading">
                                    <View className="loading-spinner"></View>
                                    <Text className="loading-text">加载中...</Text>
                                </View>
                            ) : (
                                <Image src={qrCodeUrl} mode="aspectFit" style={{ width: "75vw", height: "75vw" }} />
                            )}
                            <Text className="qr-code-tip">点击任意位置关闭</Text>
                        </View>
                    </View>
                </Popup>
            </ConfigProvider>
        </Layout>
    );
};

export default App;
