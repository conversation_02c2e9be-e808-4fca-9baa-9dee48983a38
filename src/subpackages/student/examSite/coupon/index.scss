/************************************************************
** 请将全局样式拷贝到项目的全局 CSS 文件或者当前页面的顶部 **
** 否则页面将无法正常显示                                  **
************************************************************/

page {
    background: #f7f8fa;
    min-height: 100vh;
    font-family: Yuanti SC, STYuanti-SC-Regular;
}

.coupon-container {
    padding-bottom: 120rpx;
    min-height: 100vh;
    position: relative;
    background-color: #f7f8fa;
    padding: 24rpx 16rpx;

    .use-points-btn {
        width: 100%;
        margin-bottom: 16px;
        background-color: #fff;
        color: #333;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 28px;
        padding: 16px 0;

        &:active {
            background-color: #f5f5f5;
        }
    }
}

.search-card {
    background-color: #fff;
    padding: 24rpx 20rpx;
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.form-row {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.form-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;

    .form-label-wrapper {
        width: 100%;
    }

    .form-cell-wrapper {
        width: 100%;
    }
}

.form-label {
    font-size: 26rpx;
    color: #374151;
    font-weight: 500;
    // min-width: 140rpx;
}

.tab-container {
    display: flex;
    background-color: #f3f4f6;
    border-radius: 12rpx;
    padding: 4rpx;
    gap: 4rpx;

    .tab-item {
        flex: 1;
        text-align: center;
        padding: 16rpx 0;
        font-size: 28rpx;
        color: #6b7280;
        position: relative;
        transition: all 0.3s;
        border-radius: 8rpx;

        &.active {
            background-color: #fff;
            color: #3b82f6;
            font-weight: 500;
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
        }
    }
}

.picker-cell {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 20rpx;
    background-color: #fff;
    border: 1rpx solid #e5e7eb;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #1f2937;
    position: relative;
    transition: all 0.3s ease;

    &:active {
        border-color: #3b82f6;
    }

    &::after {
        content: "";
        position: absolute;
        right: 20rpx;
        top: 50%;
        transform: translateY(-50%) rotate(45deg);
        width: 12rpx;
        height: 12rpx;
        border-right: 2rpx solid #6b7280;
        border-bottom: 2rpx solid #6b7280;
    }
}

.form-actions {
    display: flex;
    gap: 20rpx;
    // margin-top: 20rpx;

    button {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 28rpx;
        border-radius: 40rpx;
        transition: all 0.3s ease;

        &[type="primary"] {
            background-color: #3b82f6;
            color: #ffffff;
            border: none;
            box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);

            &:active {
                background-color: #2563eb;
                transform: scale(0.98);
                box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);
            }
        }
    }
}

.transaction-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    margin-bottom: calc(100rpx + env(safe-area-inset-bottom));
}

.transaction-item {
    background-color: #fff;
    border: 1rpx solid #e5e7eb;
    border-radius: 12rpx;
    padding: 20rpx;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;

    &:hover {
        border-color: #3b82f6;
        box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.1);
    }
}

.transaction-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.transaction-main {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 8rpx;
}

.transaction-index {
    font-size: 24rpx;
    color: #6b7280;
    // min-width: 60rpx;
}

.transaction-title {
    font-size: 22rpx;
    color: #1f2937;
    font-weight: 500;
    line-height: 1.4;
}

.transaction-status {
    font-size: 24rpx;
    margin: 0 24rpx;
    padding: 6rpx 16rpx;
    border-radius: 6rpx;
    font-weight: 500;
    display: inline-block;
    white-space: nowrap;

    &.positive {
        background-color: #f0fdf4;
        color: #16a34a;
        border: 1rpx solid #86efac;
    }

    &.negative {
        background-color: #fef2f2;
        color: #ef4444;
        border: 1rpx solid #fca5a5;
    }
}

.transaction-date {
    font-size: 24rpx;
    color: #6b7280;
}

.transaction-amount {
    font-size: 32rpx;
    font-weight: 500;

    &.positive {
        color: #16a34a;
    }

    &.negative {
        color: #ef4444;
    }
}

.load-more {
    text-align: center;
    color: #6b7280;
    font-size: 28rpx;
    margin-top: 24rpx;
    margin-bottom: 24rpx;
    transition: all 0.3s ease;

    .custom-divider {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 16rpx;
        padding: 0 32rpx;

        .divider-line {
            flex: 1;
            height: 1px;
            background: #e5e7eb;
            position: relative;

            &.left {
                &::after {
                    content: "";
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 30%;
                    height: 100%;
                    background: linear-gradient(to right, #e5e7eb, #d1d5db);
                }
            }

            &.right {
                height: 2px;
                &::before {
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 30%;
                    height: 100%;
                    background: linear-gradient(to left, #e5e7eb, #d1d5db);
                }
            }
        }

        .divider-text {
            color: #9ca3af;
            font-size: 24rpx;
            white-space: nowrap;
        }
    }

    &:active {
        background-color: #f9fafb;
    }
}

.no-more {
    text-align: center;
    color: #9ca3af;
    font-size: 28rpx;
    margin-top: 24rpx;
    margin-bottom: 24rpx;

    .custom-divider {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 16rpx;
        padding: 0 32rpx;

        .divider-line {
            flex: 1;
            height: 1px;
            background: #e5e7eb;
            position: relative;

            &.left {
                &::after {
                    content: "";
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 30%;
                    height: 100%;
                    background: linear-gradient(to right, #e5e7eb, #d1d5db);
                }
            }

            &.right {
                height: 2px;
                &::before {
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 30%;
                    height: 100%;
                    background: linear-gradient(to left, #e5e7eb, #d1d5db);
                }
            }
        }

        .divider-text {
            color: #9ca3af;
            font-size: 24rpx;
            white-space: nowrap;
        }
    }
}

.drawer-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    transform: translateY(calc(100% - 120rpx));
    transition: transform 0.3s ease-out;
    z-index: 1000;

    &.drawer-open {
        transform: translateY(0);
    }
}

.drawer-content {
    padding: 32rpx;
    padding-bottom: 120rpx;
}

.qr-code-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 32rpx 0;
}

.footer-balance {
    position: fixed;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 24rpx 12rpx;
    box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    z-index: 100;
    padding-bottom: env(safe-area-inset-bottom);

    &.slide-up {
        // height: 1000rpx;
        height: auto;
        transform: translateY(calc(-100% + 120rpx));
        padding-bottom: 120rpx;
    }

    &.no-slide-up {
        bottom: 0;
    }

    .balance-handle {
        position: absolute;
        top: -32rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 120rpx;
        height: 32rpx;
        background: #fff;
        border-radius: 16rpx 16rpx 0 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 4rpx;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);

        .handle-line {
            width: 40rpx;
            height: 3rpx;
            background: #ddd;
            border-radius: 2rpx;
        }
    }

    .balance-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 0 20px;
        margin-bottom: 40rpx;
    }

    .balance-left {
        flex: 1;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .balance-label {
        font-size: 28px;
        color: #333;
    }

    .balance-amount {
        font-size: 32px;
        font-weight: bold;
        color: #ff4d4f;
    }

    .balance-right {
        flex: 1;
        display: flex;
        justify-content: flex-end;
    }

    .qr-code-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 50rpx 0;
        margin-bottom: 50rpx;
    }

    .qr-code-title {
        font-size: 36rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 40rpx;
    }

    .qr-code-image {
        width: 400rpx;
        height: 400rpx;
        margin-bottom: 32rpx;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 8rpx;
    }

    @keyframes loading {
        0% {
            background-position: 200% 0;
        }
        100% {
            background-position: -200% 0;
        }
    }

    .qr-code-tip {
        font-size: 28rpx;
        color: #999;
    }
}

.no-data {
    padding: 80rpx 0;
    text-align: center;

    .custom-divider {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 32rpx;

        .divider-line {
            flex: 1;
            height: 2rpx;
            background-color: #e5e7eb;

            &.left {
                margin-right: 32rpx;
            }

            &.right {
                margin-left: 32rpx;
            }
        }

        .divider-text {
            color: #999;
            font-size: 28rpx;
        }
    }
}

.qr-code-modal {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80vw;
    margin: 0 auto;

    .qr-code-container {
        background-color: #fff;
        padding: 40rpx 0rpx 40rpx 0rpx;
        border-radius: 20rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 30rpx;
        width: 100%;

        .qr-code-image {
            width: 100%;
            height: auto;
            aspect-ratio: 1;
        }

        .qr-code-tip {
            color: #999;
            font-size: 28rpx;
        }
    }
}

.use-points-modal {
    padding: 20px;
    background: #fff;
    border-radius: 16px 16px 0 0;

    .modal-header {
        text-align: center;
        margin-bottom: 20px;

        .modal-title {
            font-size: 32 rpx;
            font-weight: 500;
            color: #333;
        }
    }

    .modal-content {
        .input-group {
            margin-bottom: 20px;

            .input-label {
                display: block;
                margin-bottom: 8px;
                font-size: 14px;
                color: #666;
            }

            .points-input,
            .remark-input {
                width: 100%;
                height: 40px;
                padding: 0 12px;
                border: 1px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                background: #f8f8f8;
            }
        }
    }

    .modal-footer {
        display: flex;
        gap: 12px;
        margin-top: 24px;

        .scan-button {
            flex: 1;
            background: #f5f5f5;
            color: #333;
            border: none;
        }

        button {
            flex: 1;
            height: 44px;
            border-radius: 8px;
            font-size: 16px;
        }
    }
}

.mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99;
}

.qr-code-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 400rpx;

    .loading-spinner {
        width: 60rpx;
        height: 60rpx;
        border: 4rpx solid #f3f3f3;
        border-top: 4rpx solid #1677ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    .loading-text {
        margin-top: 20rpx;
        font-size: 28rpx;
        color: #666;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.status-box {
    position: absolute;
    right: 0;
    top: 0;
    padding: 4rpx 12rpx;
    border-radius: 4rpx;
    border: 2rpx solid;
    font-size: 24rpx;
    color: #fff;
    white-space: nowrap;

    &.success {
        background-color: #16a34a;
        border-color: #16a34a;
    }

    &.failed {
        background-color: #ef4444;
        border-color: #ef4444;
    }
}
