import React, { useState } from "react";
import { View, Text, Form, Button } from "@tarojs/components";
import { <PERSON><PERSON>, Cell, ConfigProvider } from "@nutui/nutui-react-taro";
import Taro from "@tarojs/taro";
import request from "@service/request";
import "./index.scss";
import "@utils/app.scss";
import Layout from "@/components/Layout";
import { message } from "@/components/MessageApi/MessageApiSingleton";

type StatusType = 0 | 1 | 2 | 3;

const App = () => {
    const [status, setStatus] = useState<StatusType>(0);
    const [loading, setLoading] = useState(false);
    const [rechargeList, setRechargeList] = useState<any[]>([]);
    const [showStatusPicker, setShowStatusPicker] = useState(false);
    const [pageIndex, setPageIndex] = useState(1);
    const [pageSize] = useState(10);
    const [hasNextPage, setHasNextPage] = useState(true);

    const statusOptions = [
        { text: "等待审核", value: 0 },
        { text: "审核通过", value: 1 },
        { text: "审核失败", value: 2 },
        { text: "初始状态", value: 3 },
    ];

    const handleItemClick = (item: any) => {
        Taro.showActionSheet({
            itemList: ["审核通过", "审核驳回"],
            success: (res) => {
                handleAudit(res.tapIndex === 0, item);
            },
        });
    };

    const handleAudit = async (isApprove: boolean, item: any) => {
        if (!item) {
            message.error("请选择要审核的记录");
            return;
        }

        if (loading) return;
        setLoading(true);
        message.openLoading("审核中...");

        try {
            const endpoint = isApprove ? "/Jx/ExamSite/Wx/RechargeApplication/approve" : "/Jx/ExamSite/Wx/RechargeApplication/reject";

            const response = await request.post<any>(endpoint, {
                id: item.Id,
            });

            message.closeLoading();
            if (response.success) {
                message.success(response.message);
                // 刷新列表
                fetchRechargeList();
            } else {
                message.error(response.message);
            }
        } catch (error) {
            console.error("Audit failed:", error);
        } finally {
            setLoading(false);
        }
    };

    const fetchRechargeList = async (isLoadMore = false) => {
        if (loading) return;
        setLoading(true);
        try {
            const response = await request.post<any>("/Jx/ExamSite/Wx/RechargeApplication/page", {
                auditStatus: status,
                pageIndex: isLoadMore ? pageIndex + 1 : 1,
                pageSize,
            });

            if (response.code === 200) {
                const newData = Array.isArray(response.data.data) ? response.data.data : [];
                setHasNextPage(newData.length === pageSize);
                if (isLoadMore) {
                    setRechargeList([...rechargeList, ...newData]);
                    setPageIndex(pageIndex + 1);
                } else {
                    setRechargeList(newData);
                    setPageIndex(1);
                }
            }
        } catch (error) {
            console.error("Fetch recharge list failed:", error);
        } finally {
            setLoading(false);
        }
    };

    const loadMore = () => {
        fetchRechargeList(true);
    };

    const getStatusClass = (status: number) => {
        switch (status) {
            case 0:
                return "waiting";
            case 1:
                return "approved";
            case 2:
                return "rejected";
            case 3:
                return "initial";
            default:
                return "initial";
        }
    };

    return (
        <Layout>
            <ConfigProvider
                theme={{
                    nutuiPickerListHeight: "40vh",
                    nutuiCalendarHeight: "80vh",
                }}
            >
                <View className="recharge-review-container">
                    {/* 查询表单 */}
                    <View className="search-card">
                        <Form onSubmit={() => fetchRechargeList()}>
                            <View className="form-row">
                                <View className="form-item">
                                    <View className="form-label-wrapper">
                                        <Text className="form-label">审核状态</Text>
                                    </View>
                                    <View className="form-cell-wrapper">
                                        <Cell
                                            title={statusOptions.find((opt) => opt.value === status)?.text || "请选择状态"}
                                            onClick={() => setShowStatusPicker(true)}
                                            className="picker-cell"
                                        />
                                    </View>
                                </View>
                                <View className="form-actions">
                                    <Button type="primary" loading={loading} formType="submit">
                                        查询
                                    </Button>
                                </View>
                            </View>
                        </Form>
                    </View>

                    {/* 充值列表 */}
                    <View className="recharge-list">
                        {loading && rechargeList.length === 0 ? (
                            <View className="loading">加载中...</View>
                        ) : rechargeList.length === 0 ? (
                            <View className="no-data">
                                <Text>暂无数据</Text>
                            </View>
                        ) : (
                            <>
                                {rechargeList.map((item) => (
                                    <View key={item.Id} className="recharge-item" onClick={() => handleItemClick(item)}>
                                        <View className="recharge-info">
                                            <View className="recharge-header">
                                                <Text className={`recharge-status status-${getStatusClass(item.AuditStatus)}`}>{item.AuditStatusText}</Text>
                                            </View>
                                            <View className="recharge-details">
                                                <View className="detail-row">
                                                    <Text className="detail-label">转账账户：</Text>
                                                    <Text className="detail-value">{item.AccountName}</Text>
                                                </View>
                                                <Text className="tenant-name">所属单位：{item.TenantName}</Text>
                                                <Text className="create-time">申请时间：{item.ApplyTime}</Text>
                                            </View>
                                            {item.AuditTime !== "1900-01-01 00:00:00" && <Text className="audit-time">审核时间：{item.AuditTime}</Text>}
                                            {item.AuditRemark && <Text className="audit-remark">审核备注：{item.AuditRemark}</Text>}
                                        </View>
                                        <View className="recharge-amount">
                                            <Text className="amount">¥ {item.Amount.toFixed(2)}</Text>
                                        </View>
                                    </View>
                                ))}

                                {/* 加载更多 */}
                                {hasNextPage && (
                                    <View className="load-more" onClick={loadMore}>
                                        <View className="custom-divider">
                                            <View className="divider-line left"></View>
                                            <Text className="divider-text">点击加载更多</Text>
                                            <View className="divider-line right"></View>
                                        </View>
                                    </View>
                                )}

                                {/* 底部提示 */}
                                {!hasNextPage && rechargeList.length > 0 && (
                                    <View className="no-more">
                                        <View className="custom-divider">
                                            <View className="divider-line left"></View>
                                            <Text className="divider-text">我是有底线的</Text>
                                            <View className="divider-line right"></View>
                                        </View>
                                    </View>
                                )}
                            </>
                        )}
                    </View>

                    {/* 状态选择器 */}
                    <Picker
                        visible={showStatusPicker}
                        options={[statusOptions]}
                        onConfirm={(value) => {
                            setStatus(value[0].value as StatusType);
                            setShowStatusPicker(false);
                        }}
                        onClose={() => setShowStatusPicker(false)}
                    />
                </View>
            </ConfigProvider>
        </Layout>
    );
};

export default App;
