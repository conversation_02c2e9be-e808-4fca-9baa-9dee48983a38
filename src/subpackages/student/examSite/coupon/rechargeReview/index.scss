/************************************************************
** 请将全局样式拷贝到项目的全局 CSS 文件或者当前页面的顶部 **
** 否则页面将无法正常显示                                  **
************************************************************/

page {
    background: #f7f8fa;
    min-height: 100vh;
    font-family: Yuanti SC, STYuanti-SC-Regular;
}

.recharge-review-container {
    padding-bottom: 120rpx;
    min-height: 100vh;
    position: relative;
    background-color: #f7f8fa;
    padding: 24rpx 16rpx;
}

.search-card {
    background-color: #fff;
    padding: 24rpx 20rpx;
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.form-row {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.form-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;

    .form-label-wrapper {
        width: 100%;
    }

    .form-cell-wrapper {
        width: 100%;
    }
}

.form-label {
    font-size: 26rpx;
    color: #374151;
    font-weight: 500;
}

.picker-cell {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 20rpx;
    background-color: #fff;
    border: 1rpx solid #e5e7eb;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #1f2937;
    position: relative;
    transition: all 0.3s ease;

    &:active {
        border-color: #3b82f6;
    }

    &::after {
        content: "";
        position: absolute;
        right: 20rpx;
        top: 50%;
        transform: translateY(-50%) rotate(45deg);
        width: 12rpx;
        height: 12rpx;
        border-right: 2rpx solid #6b7280;
        border-bottom: 2rpx solid #6b7280;
    }
}

.form-actions {
    display: flex;
    gap: 20rpx;
    // padding-top: 40rpx;

    button {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 28rpx;
        border-radius: 40rpx;
        transition: all 0.3s ease;

        &[type="primary"] {
            background-color: #3b82f6;
            color: #ffffff;
            border: none;
            box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);

            &:active {
                background-color: #2563eb;
                transform: scale(0.98);
                box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);
            }
        }
    }
}

.recharge-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    // margin-bottom: calc(100rpx + env(safe-area-inset-bottom));
}

.recharge-item {
    background-color: #fff;
    border: 1rpx solid #e5e7eb;
    border-radius: 12rpx;
    padding: 20rpx;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;

    &:hover {
        border-color: #3b82f6;
        box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.1);
    }
}

.recharge-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.recharge-header {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.recharge-title {
    font-size: 28rpx;
    color: #1f2937;
    font-weight: 500;
    line-height: 1.4;
}

.recharge-status {
    font-size: 24rpx;
    padding: 6rpx 16rpx;
    border-radius: 6rpx;
    font-weight: 500;
    display: inline-block;
    white-space: nowrap;

    // 等待审核
    &.status-waiting {
        background-color: #fef3c7;
        color: #d97706;
        border: 1rpx solid #fcd34d;
    }

    // 审核通过
    &.status-approved {
        background-color: #f0fdf4;
        color: #16a34a;
        border: 1rpx solid #86efac;
    }

    // 审核失败
    &.status-rejected {
        background-color: #fee2e2;
        color: #dc2626;
        border: 1rpx solid #fca5a5;
    }

    // 初始状态
    &.status-initial {
        background-color: #f3f4f6;
        color: #6b7280;
        border: 1rpx solid #d1d5db;
    }
}

.recharge-details {
    display: flex;
    flex-direction: column;
    gap: 4rpx;
}

.detail-row {
    display: flex;
    align-items: center;
    gap: 8rpx;
    margin-bottom: 4rpx;
}

.detail-label {
    font-size: 26rpx;
    color: #6b7280;
    white-space: nowrap;
}

.detail-value {
    font-size: 26rpx;
    color: #1f2937;
    font-weight: 500;
}

.tenant-name {
    font-size: 26rpx;
    color: #6b7280;
}

.create-time,
.audit-time,
.audit-remark {
    font-size: 24rpx;
    color: #6b7280;
}

.recharge-amount {
    font-size: 32rpx;
    font-weight: 500;
    color: #16a34a;
}

.loading,
.no-data {
    text-align: center;
    padding: 40rpx;
    color: #6b7280;
    font-size: 28rpx;
}

.load-more,
.no-more {
    text-align: center;
    color: #6b7280;
    font-size: 28rpx;
    margin-top: 24rpx;
    margin-bottom: 24rpx;
    transition: all 0.3s ease;

    &:active {
        opacity: 0.7;
    }
}

.custom-divider {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;
    padding: 0 20rpx;

    .divider-line {
        flex: 1;
        height: 1rpx;
        background-color: #e5e7eb;
    }

    .divider-text {
        color: #6b7280;
        font-size: 24rpx;
        white-space: nowrap;
    }
}
