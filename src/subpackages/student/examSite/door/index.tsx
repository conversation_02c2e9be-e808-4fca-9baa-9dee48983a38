import { View, Text, Form, Button } from "@tarojs/components";
import { useState, useEffect } from "react";
import request from "@service/request";
import { message, MessageApiType } from "@components/MessageApi/MessageApiSingleton";
import { Picker, Cell, ConfigProvider } from "@nutui/nutui-react-taro";
import "./index.scss";
import Layout from "@/components/Layout";

interface DoorControlProps {
    message: MessageApiType;
}

interface Field {
    label: string;
    value: string;
}

interface DoorStatus {
    StartTime: string;
    NickName: string;
    CarModelName: string;
    CarBrandName: string;
    CarType: string;
    PeopleCount: number;
    PlanDetailId: string;
    ControlDate: string;
    IsOpen: boolean;
    Id: string;
}

const DoorControl: React.FC = () => {
    const [loading, setLoading] = useState(false);
    const [fields, setFields] = useState<Field[]>([]);
    const [doorData, setDoorData] = useState<DoorStatus[]>([]);
    const [selectedField, setSelectedField] = useState("");
    const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split("T")[0]);
    const [showFieldPicker, setShowFieldPicker] = useState(false);
    const [showDatePicker, setShowDatePicker] = useState(false);

    // 生成未来7天的日期选项
    const dateOptions = Array.from({ length: 7 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() + i);
        return {
            text: date.toISOString().split("T")[0],
            value: date.toISOString().split("T")[0],
        };
    });

    // 初始化加载
    useEffect(() => {
        initializeFields();
    }, []);

    // 获取考场列表
    const initializeFields = async () => {
        try {
            const response = await request.post<API.Result<Field[]>>("/Jx/ExamSite/Field/getFieldSelectList");
            if (response?.success && Array.isArray(response.data)) {
                setFields(response.data);
                if (response.data.length > 0) {
                    const defaultField = response.data[0].value;
                    setSelectedField(defaultField);
                    fetchDoorStatus(defaultField);
                }
            }
        } catch (error) {
            message.error("获取考场列表失败");
            console.error("Failed to fetch fields:", error);
        }
    };

    // 获取闸门状态
    const fetchDoorStatus = async (fieldId: string) => {
        setLoading(true);
        try {
            const controlDate = selectedDate ? new Date(selectedDate) : new Date();
            const response = await request.post<API.Result<DoorStatus[]>>("/Jx/ExamSite/PlanDetailOpen/getOpenList", {
                fieldId: fieldId,
                controlDate: controlDate.toISOString().split("T")[0],
            });

            if (response?.success && Array.isArray(response.data)) {
                setDoorData(response.data);
            } else {
                message.openToast("未获取到闸门数据");
                setDoorData([]);
            }
        } catch (error) {
            message.openToast("获取闸门数据失败");
            console.error("Failed to fetch door status:", error);
            setDoorData([]);
        } finally {
            setLoading(false);
        }
    };

    // 控制闸门
    const handleDoorControl = async (doorId: string, action: "open" | "close") => {
        if (loading) return;

        console.log(doorId, action, selectedDate);
        setLoading(true);
        try {
            const response = await request.post<API.Result<any>>("/Jx/ExamSite/PlanDetailOpen/togglePlanDetail", {
                PlanDetailId: doorId,
                ControlDate: selectedDate,
                IsOpen: action === "open",
            });

            if (response?.success) {
                message.showNotification(action === "open" ? "开闸成功" : "关闸成功", "success");
                // 重新获取闸门状态
                await fetchDoorStatus(selectedField);
            } else {
                message.showNotification(response?.message || `${action === "open" ? "开闸" : "关闸"}失败`, "error");
            }
        } catch (error) {
            console.error(`${action === "open" ? "开闸" : "关闸"}失败:`, error);
            message.error(`${action === "open" ? "开闸" : "关闸"}失败，请重试`);
        } finally {
            setLoading(false);
        }
    };

    // 分组数据处理
    const groupedData = doorData.reduce((acc: Record<string, DoorStatus[]>, item) => {
        const key = item.NickName; // 使用NickName作为分组key
        if (!acc[key]) {
            acc[key] = [];
        }
        acc[key].push(item);
        return acc;
    }, {});

    return (
        <Layout>
            <ConfigProvider
                theme={{
                    nutuiPickerListHeight: "40vh",
                }}
            >
                <View className="door-control-container">
                    {/* 查询表单 */}
                    <View className="search-card">
                        <Form
                            onSubmit={async (e) => {
                                e.preventDefault();
                                await fetchDoorStatus(selectedField);
                            }}
                        >
                            <View className="form-row">
                                <View className="form-item">
                                    <Text className="form-label">所属考场</Text>
                                    <Cell
                                        title={fields.find((f) => f.value === selectedField)?.label || "请选择考场"}
                                        onClick={() => setShowFieldPicker(true)}
                                        className="picker-cell"
                                    />
                                </View>
                                <View className="form-item">
                                    <Text className="form-label">控制日期</Text>
                                    <Cell title={selectedDate} onClick={() => setShowDatePicker(true)} className="picker-cell" />
                                </View>
                                <View className="form-actions">
                                    <Button type="primary" loading={loading} formType="submit">
                                        查询
                                    </Button>
                                </View>
                            </View>
                        </Form>
                    </View>

                    {/* 闸门控制区域 */}
                    <View className="door-control-content">
                        {Object.entries(groupedData).map(([area, doors]) => (
                            <View key={area} className="area-card">
                                <View className="area-title">
                                    <Text className="title">{area}</Text>
                                </View>
                                <View className="door-list">
                                    {doors.map((door) => (
                                        <View key={door.Id} className="door-item">
                                            <View className="door-info">
                                                <View className="door-main">
                                                    <Text className={`door-status ${door.IsOpen ? "open" : "closed"}`}>{door.IsOpen ? "已开启" : "已关闭"}</Text>
                                                    <Text className="door-name">{`${door.CarBrandName} ${door.CarModelName} (${door.CarType})`}</Text>
                                                </View>
                                                <Text className="door-time">开始时间: {door.StartTime}</Text>
                                            </View>
                                            <View className="door-controls">
                                                {!door.IsOpen && (
                                                    <View
                                                        className={`control-button open ${loading ? "loading" : ""}`}
                                                        onClick={() => handleDoorControl(door.PlanDetailId, "open")}
                                                    >
                                                        <Text>开闸</Text>
                                                    </View>
                                                )}
                                                {door.IsOpen && (
                                                    <View
                                                        className={`control-button close ${loading ? "loading" : ""}`}
                                                        onClick={() => handleDoorControl(door.PlanDetailId, "close")}
                                                    >
                                                        <Text>关闸</Text>
                                                    </View>
                                                )}
                                            </View>
                                        </View>
                                    ))}
                                </View>
                            </View>
                        ))}
                    </View>

                    {/* 考场选择器 */}
                    <Picker
                        visible={showFieldPicker}
                        options={fields.map((field) => ({ text: field.label, value: field.value }))}
                        onConfirm={(value) => {
                            setSelectedField(String(value[0].value));
                            setShowFieldPicker(false);
                        }}
                        onClose={() => setShowFieldPicker(false)}
                    />

                    {/* 日期选择器 */}
                    <Picker
                        visible={showDatePicker}
                        options={[dateOptions]}
                        onConfirm={(value) => {
                            setSelectedDate(String(value[0].value));
                            setShowDatePicker(false);
                        }}
                        onClose={() => setShowDatePicker(false)}
                    />
                </View>
            </ConfigProvider>
        </Layout>
    );
};

export default DoorControl;
