page {
    background: #f7f8fa;
    min-height: 100vh;
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
}

.door-control-container {
    min-height: 100vh;
    background-color: #f7f8fa;
    padding: 20rpx 12rpx;
}

.search-card {
    background-color: #fff;
    padding: 20rpx 16rpx;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.form-row {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
}

.form-item {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.form-label {
    font-size: 24rpx;
    color: #545d69;
    font-weight: 500;
}

.picker {
    height: 76rpx;
    line-height: 76rpx;
    padding: 0 16rpx;
    background-color: #fff;
    border: 1rpx solid #e5e7eb;
    border-radius: 8rpx;
    font-size: 26rpx;
    color: #1f2937;
    position: relative;

    &::after {
        content: "";
        position: absolute;
        right: 16rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 12rpx;
        height: 12rpx;
        border-right: 2rpx solid #999;
        border-bottom: 2rpx solid #999;
        transform: translateY(-50%) rotate(45deg);
    }
}

.form-actions {
    display: flex;
    gap: 16rpx;
    margin-top: 16rpx;
    
    button {
        flex: 1;
        height: 76rpx;
        line-height: 76rpx;
        font-size: 28rpx;
        border-radius: 8rpx;
        
        &:first-child {
            background-color: #f3f4f6;
            color: #4b5563;
            border: none;
            
            &:active {
                background-color: #e5e7eb;
                transform: scale(0.98);
            }
        }
        
        &[type="primary"] {
            background-color: #3b82f6;
            color: #ffffff;
            border: none;
            
            &:active {
                background-color: #2563eb;
                transform: scale(0.98);
            }
        }
    }
}

.door-control-content {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
}

.area-card {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 20rpx 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.area-title {
    margin-bottom: 16rpx;
    padding-bottom: 12rpx;
    border-bottom: 1rpx solid rgba(230, 235, 245, 0.8);

    .title {
        font-size: 28rpx;
        font-weight: 600;
        color: #1f2937;
        position: relative;
        padding-left: 16rpx;

        &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4rpx;
            height: 24rpx;
            background-color: #3b82f6;
            border-radius: 2rpx;
        }
    }
}

.door-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
}

.door-item {
    background-color: #fff;
    border: 1rpx solid #e5e7eb;
    border-radius: 8rpx;
    padding: 16rpx;
    transition: all 0.3s;

    &:active {
        transform: scale(0.98);
    }
}

.door-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    padding-bottom: 12rpx;
    border-bottom: 1rpx solid rgba(230, 235, 245, 0.8);
}

.door-name {
    font-size: 26rpx;
    color: #1f2937;
    font-weight: 500;
}

.door-status {
    font-size: 22rpx;
    padding: 4rpx 12rpx;
    border-radius: 4rpx;
    font-weight: 500;

    &.open {
        background-color: #f0fdf4;
        color: #16a34a;
        border: 1rpx solid #86efac;
    }

    &.closed {
        background-color: #fef2f2;
        color: #ef4444;
        border: 1rpx solid #fca5a5;
    }
}

.door-controls {
    display: flex;
    gap: 16rpx;
}

.control-button {
    flex: 1;
    height: 76rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8rpx;
    font-size: 26rpx;
    font-weight: 500;
    border: none;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;

    &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.2);
        transform: translate(-50%, -50%) scale(0);
        border-radius: 50%;
        transition: transform 0.3s;
    }

    &:active::after {
        transform: translate(-50%, -50%) scale(2);
    }

    &.open {
        background-color: #3b82f6;
        color: #fff;

        &:active {
            background-color: #2563eb;
        }
    }

    &.close {
        background-color: #ef4444;
        color: #fff;

        &:active {
            background-color: #dc2626;
        }
    }

    &.loading {
        opacity: 0.6;
        pointer-events: none;
    }
}
