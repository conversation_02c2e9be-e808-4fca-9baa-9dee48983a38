page {
    background: #f7f8fa;
    min-height: 100vh;
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
}

.door-control-container {
    min-height: 100vh;
    background-color: #f7f8fa;
    padding: 24rpx 16rpx;
}

.search-card {
    background-color: #fff;
    padding: 24rpx 20rpx;
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.form-row {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.form-item {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
}

.form-label {
    font-size: 26rpx;
    color: #374151;
    font-weight: 500;
}

.picker-cell {
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 20rpx;
    background-color: #fff;
    border: 1rpx solid #e5e7eb;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #1f2937;
    position: relative;
    transition: all 0.3s ease;

    &:active {
        border-color: #3b82f6;
    }

    &::after {
        content: "";
        position: absolute;
        right: 20rpx;
        top: 50%;
        transform: translateY(-50%) rotate(45deg);
        width: 12rpx;
        height: 12rpx;
        border-right: 2rpx solid #6b7280;
        border-bottom: 2rpx solid #6b7280;
    }
}

.form-actions {
    display: flex;
    gap: 20rpx;
    margin-top: 20rpx;

    button {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 28rpx;
        border-radius: 40rpx;
        transition: all 0.3s ease;

        &:first-child {
            background-color: #f3f4f6;
            color: #4b5563;
            border: none;

            &:active {
                background-color: #e5e7eb;
                transform: scale(0.98);
            }
        }

        &[type="primary"] {
            background-color: #3b82f6;
            color: #ffffff;
            border: none;
            box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);

            &:active {
                background-color: #2563eb;
                transform: scale(0.98);
                box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);
            }
        }
    }
}

.door-control-content {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
}

.area-card {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 24rpx 20rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-2rpx);
        box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
    }
}

.area-title {
    margin-bottom: 20rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid rgba(230, 235, 245, 0.8);

    .title {
        font-size: 30rpx;
        font-weight: 600;
        color: #1f2937;
        position: relative;
        padding-left: 20rpx;

        &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 6rpx;
            height: 28rpx;
            background-color: #3b82f6;
            border-radius: 3rpx;
        }
    }
}

.door-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
}

.door-item {
    background-color: #fff;
    border: 1rpx solid #e5e7eb;
    border-radius: 12rpx;
    padding: 20rpx;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;

    &:hover {
        border-color: #3b82f6;
        box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.1);
    }
}

.door-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.door-main {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.door-name {
    font-size: 28rpx;
    color: #1f2937;
    font-weight: 500;
    line-height: 1.4;
}

.door-status {
    font-size: 24rpx;
    padding: 6rpx 16rpx;
    border-radius: 6rpx;
    font-weight: 500;
    display: inline-block;
    white-space: nowrap;

    &.open {
        background-color: #f0fdf4;
        color: #16a34a;
        border: 1rpx solid #86efac;
    }

    &.closed {
        background-color: #fef2f2;
        color: #ef4444;
        border: 1rpx solid #fca5a5;
    }
}

.door-time {
    font-size: 24rpx;
    color: #6b7280;
}

.door-controls {
    display: flex;
    gap: 16rpx;
    flex-shrink: 0;
}

.control-button {
    width: 140rpx;
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 32rpx;
    font-size: 26rpx;
    font-weight: 500;
    border: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.2);
        transform: translate(-50%, -50%) scale(0);
        border-radius: 50%;
        transition: transform 0.3s;
    }

    &:active::after {
        transform: translate(-50%, -50%) scale(2);
    }

    &.open {
        background-color: #3b82f6;
        color: #fff;
        box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);

        &:active {
            background-color: #2563eb;
            box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);
        }
    }

    &.close {
        background-color: #ef4444;
        color: #fff;
        box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.3);

        &:active {
            background-color: #dc2626;
            box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.2);
        }
    }

    &.loading {
        opacity: 0.6;
        pointer-events: none;
    }
}

.popup-content,
.popup-header,
.popup-title,
.popup-close,
.popup-body,
.popup-option {
    display: none;
}
