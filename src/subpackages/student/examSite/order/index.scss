page {
    font-family: "AlibabaPuHuiTi";
    // font-family: <PERSON><PERSON>, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
}

.my-orders {
    padding: 32rpx;

    .search-section {
        background: #fff;
        border-radius: 24rpx;
        padding: 32rpx;
        margin-bottom: 32rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
        position: sticky;
        top: 0;
        z-index: 100;

        .date-range {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 32rpx;

            .date-picker-wrapper {
                flex: 1;

                .label {
                    font-size: 24rpx;
                    color: #666;
                    margin-bottom: 16rpx;
                    display: block;
                }

                .date-picker {
                    background: #f5f7fa;
                    padding: 24rpx;
                    border-radius: 16rpx;
                    font-size: 28rpx;
                    color: #333;
                    transition: all 0.2s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 24rpx;

                    &:active {
                        opacity: 0.8;
                        transform: scale(0.98);
                    }
                }
            }

            .separator {
                margin: 0 24rpx;
                margin-top: 48rpx;
                color: #999;
                font-size: 28rpx;
                display: flex;
                align-items: center;
                align-self: center;
                height: 100%;
            }
        }

        .search-btn {
            width: 100%;
            border-radius: 16rpx;
            height: 88rpx;
            font-size: 30rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12rpx;
            background: linear-gradient(135deg, #2080f0 0%, #1677ff 100%);
            box-shadow: 0 8rpx 16rpx rgba(32, 128, 240, 0.2);
            transition: all 0.2s ease;

            &:active {
                transform: translateY(2rpx);
                box-shadow: 0 4rpx 8rpx rgba(32, 128, 240, 0.2);
            }
        }
    }

    .orders-container {
        // padding: 32rpx;
        .orders-list {
            height: calc(100vh - 400rpx);
        }
    }

    .order-item {
        background: #fff;
        border-radius: 24rpx;
        padding: 32rpx;
        margin-bottom: 32rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            // margin-bottom: 24rpx;

            .order-no {
                font-size: 22rpx;
                color: #666;
            }

            .order-status {
                font-size: 22rpx;
                padding: 8rpx 16rpx;
                border-radius: 8rpx;

                &.paid {
                    color: #07c160;
                    background: rgba(7, 193, 96, 0.1);
                }

                &.refunded {
                    color: #ff4d4f;
                    background: rgba(255, 77, 79, 0.1);
                }
            }
        }

        .order-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24rpx;

            .product-name {
                font-size: 26rpx;
                color: #333;
                font-weight: 500;
            }

            .order-amount {
                font-size: 26rpx;
                color: #ff4d4f;
                font-weight: 500;
            }
        }

        .order-info {
            background: #f7f8fa;
            padding: 24rpx;
            border-radius: 16rpx;
            margin-bottom: 24rpx;

            .student-name,
            .car-type,
            .field-name,
            .school-name {
                display: block;
                font-size: 22rpx;
                color: #666;
                margin-bottom: 8rpx;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        .order-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16rpx;

            .order-time {
                font-size: 20rpx;
                color: #999;
            }

            .order-amount {
                font-size: 26rpx;
                color: #ff4d4f;
                font-weight: 500;
            }
        }
    }

    .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40rpx 0;
        width: 100%;
    }

    .bottom-tip {
        text-align: center;
        padding: 40rpx 0;
        color: #999;
        font-size: 28rpx;
    }
}

// 自定义 Tabs 样式
:global {
    .nut-tabs {
        &__titles {
            background: #f7f8fa;
            padding: 8rpx;
            border-radius: 16rpx;
            margin: 0 32rpx 32rpx;
            position: sticky;
            top: 250rpx;
            z-index: 99;

            &-item {
                flex: 1;
                text-align: center;
                font-size: 28rpx;
                padding: 20rpx 0;
                border-radius: 12rpx;
                margin: 0 8rpx;
                transition: all 0.2s ease;

                &.active {
                    background: #fff;
                    color: #2080f7;
                    font-weight: 500;
                    box-shadow: 0 4rpx 12rpx rgba(32, 128, 240, 0.1);
                }
            }
        }

        &__content {
            overflow-y: auto;
            max-height: calc(100vh - 350rpx);
        }
    }
}

// 修改 NutUI Tabs 样式
:root {
    --nutui-tabs-titles-background-color: transparent;
    --nutui-tabs-horizontal-tab-line-color: transparent;
    --nutui-tabs-horizontal-titles-height: 100rpx;
    --nutui-tabs-titles-item-font-size: 30rpx;
    --nutui-tabs-titles-item-color: #666;
    --nutui-tabs-titles-item-active-color: #2080f7;
    --nutui-tabs-horizontal-titles-item-margin: 48rpx;
    --nutui-tabs-tab-line-width: 40rpx;
    --nutui-tabs-tab-line-height: 4rpx;
}

// 修改 Empty 组件样式
:root {
    --nutui-empty-description-font-size: 28rpx;
    --nutui-empty-description-color: #999;
    --nutui-empty-image-size: 240rpx;
}

// 修改 DatePicker 样式
:root {
    --nutui-datepicker-title-height: 100rpx;
    --nutui-datepicker-title-font-size: 32rpx;
    --nutui-datepicker-height: 560rpx;
    --nutui-datepicker-item-height: 80rpx;
    --nutui-datepicker-item-font-size: 28rpx;
    --nutui-datepicker-confirm-btn-bg: #2080f0;
    --nutui-datepicker-confirm-btn-text: #fff;
    --nutui-datepicker-title-bg: #fff;
    --nutui-datepicker-pannel-bg: #fff;
    --nutui-datepicker-choose-item-color: #2080f7;
    --nutui-datepicker-row-height: 80rpx;
    --nutui-datepicker-title-font-weight: 500;
}
