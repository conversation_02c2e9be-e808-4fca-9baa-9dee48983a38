import React, { useState, useCallback, useEffect } from "react";
import { View, Text, ScrollView } from "@tarojs/components";
import { DatePicker, Button, Tabs, Empty, Loading } from "@nutui/nutui-react-taro";
import { Search } from "@nutui/icons-react-taro";
import Taro, { usePullDownRefresh, useReachBottom, getCurrentInstance } from "@tarojs/taro";
import "./index.scss";
import Layout from "@/components/Layout";
import IconFont from "@/components/iconfont";
import request from "@/service/request";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import { tool } from "@/utils/tool";
import { login } from "@utils/login";
import moment from "moment";
import TopNavBar from "@/components/topNavBar";

interface OrderItem {
    Id: string;
    OrderId: string;
    SysId: number;
    ItemName: string;
    PayMoney: number;
    CreateTime: string;
    SaleTime: string;
    xm: string;
    sfzmhm: string;
    CarType: string;
    BrandName: string;
    FieldNickName: string;
    Jxmc: string;
    IsRefund: boolean;
    RefundAmount: number;
}

const MyOrders: React.FC = () => {
    const [startDate, setStartDate] = useState<Date>(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)); // 默认近30天
    const [endDate, setEndDate] = useState<Date>(new Date());
    const [loading, setLoading] = useState<boolean>(false);
    const [isBottom, setIsBottom] = useState<boolean>(false);
    const [page, setPage] = useState<number>(1);
    const [orders, setOrders] = useState<OrderItem[]>([]);
    const [showStartPicker, setShowStartPicker] = useState<boolean>(false);
    const [showEndPicker, setShowEndPicker] = useState<boolean>(false);
    const [tenantId, setTenantId] = useState<string>("");
    const [fieldId, setFieldId] = useState<string>("");

    useEffect(() => {
        login().then((res) => {
            const instance = getCurrentInstance();
            const { TenantId, FieldId } = instance.router?.params || {};

            if (!TenantId || TenantId == "") {
                Taro.showToast({
                    title: "参数错误",
                    icon: "none",
                });
                setTimeout(() => {
                    Taro.switchTab({
                        url: "/pages/index/index",
                    });
                }, 1500);
                return;
            }
            setTenantId(TenantId);
            setFieldId(FieldId || "");

            console.log("TenantId", TenantId);
            console.log("FieldId", FieldId);
        });
    }, []);

    // 监听 tenantId 变化，执行查询
    useEffect(() => {
        if (tenantId) {
            fetchOrders(true);
        }
    }, [tenantId]);

    // 获取订单数据
    const fetchOrders = useCallback(
        async (isRefresh: boolean = false) => {
            setLoading(true);
            try {
                // const openId = "o0My-4mFF5BBNlKYHp1uFCMwWppw";

                const openId = tool.data.get("openId") == null ? undefined : tool.data.get("openId");
                if (!openId || openId == "") {
                    throw new Error("获取用户信息失败，请重新打开小程序");
                }

                // 构建API请求参数
                const params = {
                    current: page,
                    pageSize: 10,
                    SaleTimes: [moment(startDate).format("YYYY-MM-DD"), moment(endDate).format("YYYY-MM-DD")],
                    BuyOpenId: openId,
                    TenantId: tenantId,
                };

                // 发起API请求
                const response: any = await request.post("/Jx/ExamSite/Sale/getMySaleList", params);

                if (response.success) {
                    const { data, hasNextPages } = response.data;
                    if (isRefresh) {
                        setOrders(data);
                    } else {
                        setOrders((prev) => [...prev, ...data]);
                    }
                    setIsBottom(!hasNextPages);
                } else {
                    throw new Error(response.message || "获取订单失败");
                }
            } catch (error) {
                console.error("获取订单列表失败:", error);
                message.error(error.message || "获取订单失败");
            } finally {
                setLoading(false);
                Taro.stopPullDownRefresh();
            }
        },
        [page, startDate, endDate, tenantId]
    );

    // 下拉刷新
    usePullDownRefresh(() => {
        setPage(1);
        setIsBottom(false);
        fetchOrders(true);
    });

    // 上拉加载更多
    useReachBottom(() => {
        if (!loading && !isBottom) {
            setPage((prev) => prev + 1);
            fetchOrders();
        }
    });

    // 查询按钮点击事件
    const handleSearch = () => {
        setPage(1);
        setIsBottom(false);
        fetchOrders(true);
    };

    const homeClick = () => {
        const instance = getCurrentInstance();
        const { TenantId, FieldId } = instance.router?.params || {};
        Taro.redirectTo({ url: "/subpackages/student/examSite/selfLogin/index?TenantId=" + TenantId + "&FieldId=" + FieldId });
    };

    return (
        <View style={{ backgroundColor: "#f7f8fa", minHeight: "100vh" }}>
            <Layout>
                <TopNavBar title="我的订单" homeClick={() => homeClick()} />
                <View className="my-orders">
                    <View className="search-section">
                        <View className="date-range">
                            <View className="date-picker-wrapper">
                                <Text className="label">开始日期</Text>
                                <View className="date-picker" onClick={() => setShowStartPicker(true)}>
                                    <IconFont name="line-schedule" size={32} color="#666" />
                                    <Text>{moment(startDate).format("YYYY-MM-DD")}</Text>
                                </View>
                            </View>
                            <View className="separator">至</View>
                            <View className="date-picker-wrapper">
                                <Text className="label">结束日期</Text>
                                <View className="date-picker" onClick={() => setShowEndPicker(true)}>
                                    <IconFont name="line-schedule" size={32} color="#666" />
                                    <Text>{moment(endDate).format("YYYY-MM-DD")}</Text>
                                </View>
                            </View>
                        </View>
                        <Button className="search-btn" type="primary" icon={<Search size={16} />} onClick={handleSearch}>
                            查询订单
                        </Button>
                    </View>

                    <View className="orders-container">
                        <ScrollView className="orders-list" scrollY>
                            {orders.length > 0 ? (
                                orders.map((order) => (
                                    <View
                                        key={order.Id}
                                        className="order-item"
                                        onClick={() => {
                                            Taro.navigateTo({
                                                url: `/subpackages/student/examSite/selfLogin/success/index?SaleId=${order.Id}&Id=${order.OrderId}&TenantId=${tenantId}`,
                                            });
                                        }}
                                    >
                                        <View className="order-header">
                                            <Text className="order-no">订单号：{order.SysId}</Text>
                                            <Text className={`order-status ${order.IsRefund ? "refunded" : "paid"}`}>{order.IsRefund ? "已退款" : "付款成功"}</Text>
                                        </View>
                                        <View className="order-content">
                                            <Text className="product-name">{order.ItemName}</Text>
                                        </View>
                                        <View className="order-info">
                                            <Text className="student-name">
                                                学员：{order.xm} {order.sfzmhm}
                                            </Text>
                                            <Text className="car-type">
                                                车型：{order.CarType} - {order.BrandName}
                                            </Text>
                                            {/* <Text className="field-name">场地：{order.FieldNickName}</Text> */}
                                            {/* <Text className="school-name">驾校：{order.Jxmc}</Text> */}
                                        </View>
                                        <View className="order-footer">
                                            <Text className="order-time">{order.SaleTime}</Text>
                                            <Text className="order-amount">￥{order.PayMoney.toFixed(2)}</Text>
                                        </View>
                                    </View>
                                ))
                            ) : (
                                <Empty description="暂无订单数据" />
                            )}
                            {loading && <Loading className="loading">加载中...</Loading>}
                            {isBottom && orders.length > 0 && <View className="bottom-tip">已经到底啦~</View>}
                        </ScrollView>
                    </View>

                    <DatePicker
                        visible={showStartPicker}
                        value={startDate}
                        type="date"
                        onClose={() => setShowStartPicker(false)}
                        onConfirm={(_, value: any) => {
                            setStartDate(value);
                            setShowStartPicker(false);
                        }}
                    />

                    <DatePicker
                        visible={showEndPicker}
                        value={endDate}
                        type="date"
                        onClose={() => setShowEndPicker(false)}
                        onConfirm={(_, value: any) => {
                            setEndDate(value);
                            setShowEndPicker(false);
                        }}
                    />
                </View>
            </Layout>
        </View>
    );
};

export default MyOrders;
