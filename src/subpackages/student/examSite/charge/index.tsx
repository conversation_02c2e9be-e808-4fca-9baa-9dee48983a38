import { View, Text } from "@tarojs/components";
import { useState, useEffect } from "react";
import { Input, Button, Cell, Popup } from "@nutui/nutui-react-taro";
import request from "@service/request";
import "./index.scss";
import Layout from "@components/Layout";
import { message } from "@components/MessageApi/MessageApiSingleton";
import { useDidShow, useReady } from "@tarojs/taro";
import { login } from "@utils/login";

interface Company {
    Id: string;
    TenantName: string;
    AccountRule: string;
}

interface ApiResponse<T> {
    code: number;
    success: boolean;
    message: string;
    data: T;
    extras: any;
    time: string;
}

export default function ChargePage() {
    const [companies, setCompanies] = useState<Company[]>([]);
    const [selectedCompany, setSelectedCompany] = useState<string>("");
    const [selectedCompanyName, setSelectedCompanyName] = useState<string>("");
    const [amount, setAmount] = useState<string>("");
    const [balance, setBalance] = useState<number>(0);
    const [loading, setLoading] = useState<boolean>(false);
    const [visible, setVisible] = useState(false);

    useDidShow(() => {
        login().then(async () => {
            fetchCompanies();
        });
    });

    // 获取公司列表
    const fetchCompanies = async () => {
        try {
            const response = await request.post<{ success: boolean; data: Company[] }>("/Jx/ExamSite/CouponCharge/getTenantList");
            if (response.success) {
                setCompanies(response.data);
            }
        } catch (error) {
            console.error("获取公司列表失败:", error);
        }
    };

    // 获取公司余额
    const fetchCompanyBalance = async (companyId: string) => {
        try {
            const response = await request.post<ApiResponse<number>>(`/Jx/ExamSite/CouponCharge/${companyId}`);
            if (response.success) {
                setBalance(Number(response.data));
            }
        } catch (error) {
            console.error("获取公司余额失败:", error);
        }
    };

    // 处理充值
    const handleCharge = async () => {
        if (!selectedCompany || !amount) {
            return;
        }

        setLoading(true);
        try {
            const response = await request.put<{ success: boolean; message: string }>(`/Jx/ExamSite/CouponCharge/${selectedCompany}`, {
                PayMoney: parseFloat(amount),
            });

            if (response.success) {
                message.openToast(response.message);
                // 充值成功后刷新余额
                fetchCompanyBalance(selectedCompany);
                setAmount("");
            } else {
                message.openToast(response.message);
            }
        } catch (error) {
            console.error("充值失败:", error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (selectedCompany) {
            fetchCompanyBalance(selectedCompany);
        }
    }, [selectedCompany]);

    const handleCompanySelect = (companyId: string) => {
        const company = companies.find((c) => c.Id === companyId);
        if (company) {
            // 切换公司时清空充值金额
            setAmount("");
            setSelectedCompany(company.Id);
            setSelectedCompanyName(company.TenantName);
        }
        setVisible(false);
    };

    return (
        <Layout>
            <View className="charge-page">
                <View className="decoration-dots" />
                {/* 选择公司卡片 */}
                <View className="company-card">
                    <Cell title="选择公司" description={selectedCompanyName || "请选择公司"} onClick={() => setVisible(true)} className="company-selector" />

                    <View className="balance-info">
                        <Text className="label">当前余额</Text>
                        <View className="balance-wrapper">
                            <Text className="balance" style={{ color: balance < 0 ? "#ef4444" : "#0c4a6e" }}>
                                ¥ {(Number(balance) || 0).toFixed(2)}
                            </Text>
                            <Text className="refresh-link" onClick={() => selectedCompany && fetchCompanyBalance(selectedCompany)}>
                                刷新余额
                            </Text>
                        </View>
                    </View>
                </View>

                {/* 充值卡片，只在选择公司后显示 */}
                <View className="charge-card">
                    <View className="form-item">
                        <Text className="label">充值金额</Text>
                        <Input className="amount-input" type="digit" placeholder="请输入充值金额" value={amount} onChange={(value) => setAmount(value)} />
                    </View>

                    <Button block type="primary" loading={loading} disabled={!amount || loading} onClick={handleCharge}>
                        确认充值
                    </Button>
                </View>

                {/* 弹窗部分保持不变 */}
                <Popup visible={visible} position="bottom" onClose={() => setVisible(false)}>
                    <view className="user-list-popup">
                        <view className="popup-title">请选择公司</view>
                        {companies.map((company) => (
                            <Cell key={company.Id} title={company.TenantName} onClick={() => handleCompanySelect(company.Id)} clickable />
                        ))}
                    </view>
                </Popup>
            </View>
        </Layout>
    );
}
