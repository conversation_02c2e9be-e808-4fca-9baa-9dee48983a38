// Enhanced styling for the charge page
.charge-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f6f9fe, #f0f4f8);
    padding: 40rpx 30rpx;
    box-sizing: border-box;

    // Common card styles with enhanced design
    .card {
        background-color: #fff;
        border-radius: 24rpx;
        padding: 40rpx 30rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);
        position: relative;
        overflow: hidden;

        &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6rpx;
            background: linear-gradient(90deg, #3b82f6, #2563eb);
            opacity: 0.8;
        }
    }

    // Company selection card with enhanced styling
    .company-card {
        @extend .card;

        .company-selector.nut-cell {
            background: #fff;
            border: 2rpx solid #e5e7eb;
            border-radius: 16rpx;
            padding: 32rpx 28rpx;
            position: relative;
            transition: all 0.3s ease;

            &:active {
                background-color: #f9fafb;
                transform: translateY(2rpx);
            }

            &::after {
                display: none;
            }

            .nut-cell__title {
                font-size: 28rpx;
                color: #6b7280;
                font-weight: 500;
            }

            .nut-cell__description {
                margin-top: 16rpx;
                font-size: 34rpx;
                color: #1f2937;
                font-weight: 600;

                &:empty::before {
                    content: "请选择公司";
                    color: #9ca3af;
                    font-weight: normal;
                }
            }

            .nut-cell__extra {
                color: #6b7280;
                font-size: 40rpx;
            }
        }

        // Enhanced balance info section
        .balance-info {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border-radius: 20rpx;
            padding: 36rpx 30rpx;
            margin-top: 30rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            animation: slideIn 0.4s ease-out;
            box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
            border: 1rpx solid rgba(186, 230, 253, 0.5);

            .label {
                font-size: 30rpx;
                color: #0369a1;
                font-weight: 500;
            }

            .balance {
                font-size: 48rpx;
                font-weight: bold;
                font-family: -apple-system-font, Helvetica Neue, Arial, sans-serif;
                transition: color 0.3s ease;
                color: #0c4a6e;

                // Negative balance styling
                &.negative {
                    color: #ef4444;
                }
            }

            .balance-wrapper {
                display: flex;
                align-items: center;
                gap: 16rpx;

                .refresh-link {
                    color: #0284c7;
                    font-size: 26rpx;
                    font-weight: 500;
                    background-color: rgba(255, 255, 255, 0.6);
                    padding: 8rpx 20rpx;
                    border-radius: 30rpx;
                    transition: all 0.3s ease;

                    &:active {
                        background-color: rgba(255, 255, 255, 0.8);
                        transform: scale(0.96);
                    }
                }
            }
        }
    }

    // Enhanced charge card styling
    .charge-card {
        @extend .card;

        &::before {
            background: linear-gradient(90deg, #3b82f6, #10b981);
        }

        // Enhanced form item styling
        .form-item {
            margin-bottom: 40rpx;

            .label {
                display: block;
                font-size: 30rpx;
                color: #4b5563;
                margin-bottom: 16rpx;
                font-weight: 500;
                position: relative;
                padding-left: 16rpx;

                &::before {
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 8rpx;
                    height: 24rpx;
                    width: 4rpx;
                    background-color: #3b82f6;
                    border-radius: 2rpx;
                }
            }

            .amount-input {
                width: 100%;
                height: 96rpx;
                background: #f9fafb;
                border: 2rpx solid #e5e7eb;
                border-radius: 16rpx;
                padding: 0 30rpx;
                font-size: 36rpx;
                color: #1f2937;
                transition: all 0.3s;
                box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.05);

                &::placeholder {
                    color: #9ca3af;
                    font-size: 30rpx;
                }

                &:focus {
                    border-color: #3b82f6;
                    background-color: #fff;
                    box-shadow: 0 0 0 3rpx rgba(59, 130, 246, 0.1);
                }
            }
        }

        // Enhanced button styling
        .nut-button {
            margin-top: 60rpx;
            height: 96rpx;
            font-size: 34rpx;
            font-weight: 600;
            border-radius: 48rpx;
            box-shadow: 0 8rpx 20rpx rgba(59, 130, 246, 0.2);
            transition: all 0.3s ease;

            &--primary {
                background: linear-gradient(135deg, #3b82f6, #2563eb);
                border: none;

                &:active {
                    background: linear-gradient(135deg, #2563eb, #1d4ed8);
                    transform: translateY(2rpx);
                    box-shadow: 0 4rpx 10rpx rgba(59, 130, 246, 0.15);
                }
            }

            &--disabled {
                background: #f1f5f9;
                color: #94a3b8;
                border: 2rpx solid #e2e8f0;
                box-shadow: none;
            }
        }
    }
}

// Enhanced popup styling
.user-list-popup {
    padding: 0 30rpx 50rpx;

    .popup-title {
        font-size: 36rpx;
        color: #1f2937;
        text-align: center;
        padding: 50rpx 0 40rpx;
        font-weight: 600;
        position: relative;
        margin-bottom: 30rpx;

        &::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60rpx;
            height: 6rpx;
            background: linear-gradient(90deg, #3b82f6, #2563eb);
            border-radius: 3rpx;
        }
    }

    .nut-cell {
        margin-bottom: 24rpx;
        border-radius: 16rpx;
        background-color: #f8fafc;
        padding: 32rpx 28rpx;
        transition: all 0.3s ease;
        border: 1rpx solid #e2e8f0;

        &:last-child {
            margin-bottom: 0;
        }

        .nut-cell__title {
            font-size: 32rpx;
            color: #334155;
            font-weight: 500;
        }

        &:active {
            background-color: #f1f5f9;
            transform: translateY(2rpx);
        }
    }
}

// Enhanced animation
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// Global style overrides
:global {
    .nut-popup {
        &-bottom {
            border-radius: 32rpx 32rpx 0 0 !important;
            overflow: hidden;
            box-shadow: 0 -8rpx 30rpx rgba(0, 0, 0, 0.1);
        }
    }

    .nut-picker {
        .nut-picker__bar {
            background: #f8fafc;
            border-bottom: 1rpx solid #e2e8f0;
        }

        .nut-picker__confirm {
            color: #3b82f6;
            font-weight: 600;
            font-size: 32rpx;
        }

        .nut-picker__cancel {
            color: #64748b;
            font-size: 32rpx;
        }

        .nut-picker-roller-item {
            color: #334155;
            font-size: 34rpx;

            &_active {
                font-weight: 600;
                color: #1e40af;
            }
        }
    }
}
