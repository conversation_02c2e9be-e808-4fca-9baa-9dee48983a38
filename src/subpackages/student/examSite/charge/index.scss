.charge-page {
    min-height: 100vh;
    background: #f9fafb;
    padding: 20rpx 12rpx;
    box-sizing: border-box;

    // 公共卡片样式
    .card {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 20rpx 16rpx;
        margin-bottom: 16rpx;
        box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.02);
        position: relative;
        overflow: hidden;
    }

    // 选择公司卡片
    .company-card {
        @extend .card;

        .company-selector.nut-cell {
            background: #fff;
            border: none;
            border-radius: 8rpx;
            padding: 16rpx 12rpx;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.02);

            &:active {
                background-color: #f9fafb;
                transform: translateY(2rpx);
            }

            &::after {
                display: none;
            }

            .nut-cell__title {
                font-size: 28rpx;
                color: #6b7280;
                font-weight: 500;
            }

            .nut-cell__description {
                margin-top: 12rpx;
                font-size: 32rpx;
                color: #1f2937;
                font-weight: 600;

                &:empty::before {
                    content: "请选择公司";
                    color: #9ca3af;
                    font-weight: normal;
                }
            }

            .nut-cell__extra {
                color: #6b7280;
                font-size: 36rpx;
            }
        }

        // 余额信息
        .balance-info {
            background: #ffffff;
            border-radius: 8rpx;
            padding: 16rpx 12rpx;
            margin-top: 16rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            animation: slideIn 0.3s ease-out;
            box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.02);
            border: 1rpx solid #f1f5f9;

            .label {
                font-size: 24rpx;
                color: #6b7280;
                font-weight: 500;
            }

            .balance {
                font-size: 36rpx;
                font-weight: bold;
                font-family: -apple-system-font, Helvetica Neue, Arial, sans-serif;
                transition: color 0.3s ease;
                color: #1f2937;

                // 负数金额显示为红色
                &.negative {
                    color: #ef4444;
                }
            }

            .balance-wrapper {
                display: flex;
                align-items: center;
                gap: 16rpx;

                .refresh-link {
                    color: #3b82f6;
                    font-size: 22rpx;
                    font-weight: 500;
                    padding: 4rpx 10rpx;
                    border-radius: 6rpx;
                    transition: all 0.3s ease;
                    border: 1rpx solid #e5e7eb;

                    &:active {
                        background-color: #f1f5f9;
                        transform: scale(0.96);
                    }
                }
            }
        }
    }

    // 充值卡片
    .charge-card {
        @extend .card;

        // 充值金额输入区域
        .form-item {
            margin-bottom: 24rpx;

            .label {
                display: block;
                font-size: 24rpx;
                color: #6b7280;
                margin-bottom: 10rpx;
                font-weight: 500;
            }

            .amount-input {
                width: 100%;
                height: 76rpx;
                background: #ffffff;
                border: 1rpx solid #e5e7eb;
                border-radius: 8rpx;
                padding: 0 16rpx;
                font-size: 28rpx;
                color: #1f2937;
                transition: all 0.3s;

                &::placeholder {
                    color: #9ca3af;
                    font-size: 24rpx;
                }

                &:focus {
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 2rpx rgba(59, 130, 246, 0.1);
                }
            }
        }

        // 确认按钮
        .nut-button {
            margin-top: 24rpx;
            height: 76rpx;
            font-size: 28rpx;
            font-weight: 600;
            border-radius: 8rpx;
            box-shadow: 0 2rpx 6rpx rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;

            &--primary {
                background: #3b82f6;
                border: none;

                &:active {
                    background: #2563eb;
                    transform: translateY(2rpx);
                    box-shadow: 0 2rpx 6rpx rgba(59, 130, 246, 0.1);
                }
            }

            &--disabled {
                background: #f1f5f9;
                color: #94a3b8;
                border: none;
                box-shadow: none;
            }
        }
    }
}

// Picker 样式覆盖
:global {
    .nut-picker {
        .nut-picker__bar {
            background: #f7f8fa;
        }

        .nut-picker__confirm {
            color: #1989fa;
        }

        .nut-picker__cancel {
            color: #969799;
        }

        .nut-picker-roller-item {
            color: #323233;
            font-size: 32rpx;

            &_active {
                font-weight: 500;
            }
        }
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// 弹窗样式优化
.user-list-popup {
    padding: 16rpx 12rpx 30rpx;

    .popup-title {
        font-size: 28rpx;
        color: #1f2937;
        text-align: center;
        padding: 20rpx 0;
        font-weight: 600;
        position: relative;
        margin-bottom: 16rpx;
        border-bottom: 1rpx solid #f1f5f9;
    }

    .nut-cell {
        margin-bottom: 16rpx;
        border-radius: 12rpx;
        background-color: #ffffff;
        padding: 24rpx;
        transition: all 0.3s ease;
        border: 1rpx solid #f1f5f9;
        position: relative;
        overflow: hidden;

        &:last-child {
            margin-bottom: 0;
        }

        .nut-cell__title {
            font-size: 30rpx;
            color: #334155;
            font-weight: 500;
        }

        &:active {
            background-color: #f8fafc;
            transform: translateY(2rpx);
        }
    }
}

// 修改 Popup 组件的样式
:global {
    .nut-popup {
        &-bottom {
            border-radius: 24rpx 24rpx 0 0 !important;
            overflow: hidden;
            box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
            background-color: #f9fafb;
        }

        .nut-overlay {
            backdrop-filter: blur(5px);
            background-color: rgba(15, 23, 42, 0.3);
        }
    }

    .nut-picker {
        .nut-picker__bar {
            background: #ffffff;
            border-bottom: 1rpx solid #e5e7eb;
            height: 80rpx;
            display: flex;
            align-items: center;
            padding: 0 16rpx;
        }

        .nut-picker__title {
            font-size: 28rpx;
            color: #1f2937;
            font-weight: 600;
        }

        .nut-picker__confirm {
            color: #3b82f6;
            font-weight: 600;
            font-size: 28rpx;
            background: rgba(59, 130, 246, 0.1);
            padding: 6rpx 16rpx;
            border-radius: 6rpx;
            transition: all 0.3s ease;

            &:active {
                background: rgba(59, 130, 246, 0.2);
                transform: scale(0.96);
            }
        }

        .nut-picker__cancel {
            color: #64748b;
            font-size: 28rpx;
            padding: 6rpx 16rpx;
            border-radius: 6rpx;
            transition: all 0.3s ease;

            &:active {
                background: rgba(100, 116, 139, 0.1);
                transform: scale(0.96);
            }
        }

        .nut-picker-roller-item {
            color: #6b7280;
            font-size: 28rpx;
            transition: all 0.2s ease;

            &_active {
                font-weight: 600;
                color: #3b82f6;
                font-size: 30rpx;
                transform: scale(1.02);
            }
        }

        .nut-picker-roller-mask {
            background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6)),
                linear-gradient(to top, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6));
        }
    }
}
