.face-registration-modal {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #fff;
    // border-radius: 16rpx;
    overflow: hidden;

    .modal-header {
        padding: 32rpx 32rpx 24rpx;
        text-align: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;

        .modal-title {
            font-size: 36rpx;
            font-weight: bold;
            margin-bottom: 8rpx;
        }

        .modal-subtitle {
            font-size: 24rpx;
            opacity: 0.9;
        }
    }

    .steps-container {
        padding: 24rpx 32rpx;
        background: #f8f9fa;

        .steps-indicator {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .step-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                flex: 1;
                position: relative;

                &:not(:last-child)::after {
                    content: "";
                    position: absolute;
                    top: 30rpx;
                    left: 60%;
                    width: 80rpx;
                    height: 2rpx;
                    background: #dee2e6;
                }

                &.completed:not(:last-child)::after {
                    background: #667eea;
                }

                .step-number {
                    width: 60rpx;
                    height: 60rpx;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 24rpx;
                    font-weight: bold;
                    background: #e9ecef;
                    color: #6c757d;
                    margin-bottom: 12rpx;
                }

                &.active .step-number {
                    background: #667eea;
                    color: white;
                }

                &.completed .step-number {
                    background: #28a745;
                    color: white;
                }

                .step-info {
                    text-align: center;

                    .step-title {
                        font-size: 24rpx;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 4rpx;
                    }

                    .step-desc {
                        font-size: 20rpx;
                        color: #666;
                        line-height: 1.2;
                    }
                }

                &.active .step-info .step-title {
                    color: #667eea;
                }

                &.completed .step-info .step-title {
                    color: #28a745;
                }
            }
        }
    }

    .modal-content {
        flex: 1;
        padding: 32rpx;
        overflow-y: auto;

        .step-content {
            .upload-section {
                .section-title {
                    font-size: 32rpx;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 16rpx;
                }

                .section-desc {
                    font-size: 26rpx;
                    color: #666;
                    margin-bottom: 32rpx;
                    line-height: 1.4;
                }

                .image-preview {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 24rpx;

                    .preview-image {
                        width: 300rpx;
                        height: 200rpx;
                        border-radius: 12rpx;
                        border: 2rpx solid #e9ecef;
                    }

                    .re-upload-btn {
                        background: #6c757d;
                        color: white;
                        border: none;
                        border-radius: 8rpx;
                        padding: 12rpx 24rpx;
                        font-size: 24rpx;
                    }
                }

                .upload-area {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding: 60rpx 40rpx;
                    border: 2rpx dashed #dee2e6;
                    border-radius: 12rpx;
                    background: #f8f9fa;
                    cursor: pointer;
                    transition: all 0.3s ease;

                    &:active {
                        background: #e9ecef;
                        border-color: #adb5bd;
                    }

                    .upload-icon {
                        font-size: 80rpx;
                        margin-bottom: 16rpx;
                    }

                    .upload-text {
                        font-size: 28rpx;
                        color: #6c757d;
                        text-align: center;
                        line-height: 1.4;
                    }
                }
            }

            .comparing-section {
                .section-title {
                    font-size: 32rpx;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 32rpx;
                    text-align: center;
                }

                .comparing-content {
                    .loading-container {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        padding: 60rpx 40rpx;

                        .loading-spinner {
                            width: 60rpx;
                            height: 60rpx;
                            border: 4rpx solid #f3f3f3;
                            border-top: 4rpx solid #667eea;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            margin-bottom: 24rpx;
                        }

                        .loading-text {
                            font-size: 28rpx;
                            color: #666;
                        }
                    }

                    .compare-images {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 32rpx 0;

                        .image-item {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            flex: 1;

                            .image-label {
                                font-size: 24rpx;
                                color: #666;
                                margin-bottom: 16rpx;
                            }

                            .compare-image {
                                width: 180rpx;
                                height: 120rpx;
                                border-radius: 8rpx;
                                border: 2rpx solid #e9ecef;
                            }
                        }

                        .vs-text {
                            font-size: 32rpx;
                            font-weight: bold;
                            color: #667eea;
                            margin: 0 24rpx;
                        }
                    }
                }
            }
        }
    }

    .modal-footer {
        padding: 24rpx 32rpx 32rpx;
        background: #f8f9fa;
        border-top: 1rpx solid #e9ecef;

        .button-group {
            display: flex;
            gap: 16rpx;
            justify-content: center;

            .prev-btn,
            .next-btn,
            .cancel-btn {
                flex: 1;
                height: 80rpx;
                line-height: 80rpx;
                border-radius: 8rpx;
                font-size: 28rpx;
                border: none;

                &[disabled] {
                    opacity: 0.5;
                }
            }

            .prev-btn {
                background: #6c757d;
                color: white;
            }

            .next-btn {
                background: #667eea;
                color: white;
            }

            .cancel-btn {
                background: #dc3545;
                color: white;
            }
        }
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
