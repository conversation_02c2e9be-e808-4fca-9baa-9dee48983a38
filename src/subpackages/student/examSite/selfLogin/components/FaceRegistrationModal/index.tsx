import React, { useState } from "react";
import { View, Text, Image } from "@tarojs/components";
import { Button, Popup } from "@nutui/nutui-react-taro";
import Taro from "@tarojs/taro";
import request from "@service/request";
import { getDomain } from "@service/base";
import "./index.scss";

interface FaceRegistrationModalProps {
    visible: boolean;
    onClose: () => void;
    onSuccess: () => void;
    userInfo: {
        xm: string;
        sfzmhm: string;
        fieldId: string;
    };
    message: any;
}

export const FaceRegistrationModal: React.FC<FaceRegistrationModalProps> = ({ visible, onClose, onSuccess, userInfo, message }) => {
    const [currentStep, setCurrentStep] = useState(0);
    const [idCardImage, setIdCardImage] = useState("");
    const [faceImage, setFaceImage] = useState("");
    const [uploading, setUploading] = useState(false);
    const [comparing, setComparing] = useState(false);

    // 步骤配置
    const steps = [
        { title: "上传身份证", description: "请上传身份证人像面照片" },
        { title: "人脸拍照", description: "请拍摄正面人脸照片" },
        { title: "比对验证", description: "正在进行人脸比对验证" },
    ];

    // 重置组件状态
    const resetState = () => {
        setCurrentStep(0);
        setIdCardImage("");
        setFaceImage("");
        setUploading(false);
        setComparing(false);
    };

    // 上传身份证照片
    const uploadIdCard = () => {
        Taro.chooseImage({
            count: 1,
            sizeType: ["compressed"],
            sourceType: ["album", "camera"],
            success: (res) => {
                if (res.tempFilePaths && res.tempFilePaths.length > 0) {
                    setUploading(true);
                    const filePath = res.tempFilePaths[0];

                    // 上传到服务器
                    Taro.uploadFile({
                        url: getDomain() + "/Jx/Image/Image/uploadTempImage",
                        filePath: filePath,
                        name: "file",
                        header: {
                            Authorization: `Bearer ${Taro.getStorageSync("token") || ""}`,
                        },
                        success: (uploadRes) => {
                            try {
                                const result = JSON.parse(uploadRes.data) as any;
                                if (result.success) {
                                    setIdCardImage(result.data.FilePath);
                                    message.openToast("身份证照片上传成功");
                                } else {
                                    message.openToast("上传失败：" + result.message);
                                }
                            } catch (error) {
                                message.openToast("上传失败，请重试");
                            }
                        },
                        fail: () => {
                            message.openToast("上传失败，请重试");
                        },
                        complete: () => {
                            setUploading(false);
                        },
                    });
                }
            },
            fail: () => {
                message.openToast("选择照片失败");
            },
        });
    };

    // 拍摄人脸照片
    const takeFacePhoto = () => {
        Taro.chooseImage({
            count: 1,
            sizeType: ["compressed"],
            sourceType: ["camera"], // 只允许拍照
            success: (res) => {
                if (res.tempFilePaths && res.tempFilePaths.length > 0) {
                    setUploading(true);
                    const filePath = res.tempFilePaths[0];

                    // 上传到服务器
                    Taro.uploadFile({
                        url: getDomain() + "/Jx/Image/uploadTempImage",
                        filePath: filePath,
                        name: "file",
                        header: {
                            Authorization: `Bearer ${Taro.getStorageSync("token") || ""}`,
                        },
                        success: (uploadRes) => {
                            try {
                                const result = JSON.parse(uploadRes.data) as any;
                                if (result.success) {
                                    setFaceImage(result.data.FilePath);
                                    message.openToast("人脸照片上传成功");
                                } else {
                                    message.openToast("上传失败：" + result.message);
                                }
                            } catch (error) {
                                message.openToast("上传失败，请重试");
                            }
                        },
                        fail: () => {
                            message.openToast("上传失败，请重试");
                        },
                        complete: () => {
                            setUploading(false);
                        },
                    });
                }
            },
            fail: () => {
                message.openToast("拍照失败");
            },
        });
    };

    // 进行人脸比对
    const compareFaces = async () => {
        if (!idCardImage || !faceImage) {
            message.openToast("请先完成照片上传");
            return;
        }

        setComparing(true);
        setCurrentStep(2);

        try {
            const result = await request.post<any>("/Jx/ExamSite/Wx/Face/compareFaces", {
                FieldId: userInfo.fieldId,
                xm: userInfo.xm,
                sfzmhm: userInfo.sfzmhm,
                idCardImage: idCardImage,
                faceImage: faceImage,
            });

            if (result.success) {
                message.openDialog(
                    "人脸验证成功",
                    `验证通过！相似度：${result.data?.similarity || "高"}`,
                    () => {
                        resetState();
                        onSuccess();
                        onClose();
                    },
                    "确定"
                );
            } else {
                message.openDialog(
                    "人脸验证失败",
                    result.message || "人脸比对未通过，请重新尝试",
                    () => {
                        setCurrentStep(0);
                        setIdCardImage("");
                        setFaceImage("");
                    },
                    "重新验证"
                );
            }
        } catch (error) {
            message.openDialog("验证失败", "网络错误，请稍后重试");
        } finally {
            setComparing(false);
        }
    };

    // 下一步处理
    const handleNext = () => {
        if (currentStep === 0) {
            if (!idCardImage) {
                message.openToast("请先上传身份证照片");
                return;
            }
            setCurrentStep(1);
        } else if (currentStep === 1) {
            if (!faceImage) {
                message.openToast("请先拍摄人脸照片");
                return;
            }
            compareFaces();
        }
    };

    // 上一步处理
    const handlePrev = () => {
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        }
    };

    // 关闭弹窗
    const handleClose = () => {
        resetState();
        onClose();
    };

    return (
        <Popup visible={visible} position="center" style={{ width: "90%", height: "70%" }} onClose={handleClose}>
            <View className="face-registration-modal">
                {/* 标题 */}
                <View className="modal-header">
                    <Text className="modal-title">人脸登记</Text>
                    <Text className="modal-subtitle">请按步骤完成人脸身份验证</Text>
                </View>

                {/* 步骤指示器 */}
                <View className="steps-container">
                    <View className="steps-indicator">
                        {steps.map((step, index) => (
                            <View key={index} className={`step-item ${index === currentStep ? "active" : ""} ${index < currentStep ? "completed" : ""}`}>
                                <View className="step-number">{index + 1}</View>
                                <View className="step-info">
                                    <Text className="step-title">{step.title}</Text>
                                    <Text className="step-desc">{step.description}</Text>
                                </View>
                            </View>
                        ))}
                    </View>
                </View>

                {/* 内容区域 */}
                <View className="modal-content">
                    {currentStep === 0 && (
                        <View className="step-content">
                            <View className="upload-section">
                                <Text className="section-title">第一步：上传身份证照片</Text>
                                <Text className="section-desc">请上传身份证人像面的清晰照片</Text>

                                {idCardImage ? (
                                    <View className="image-preview">
                                        <Image src={getDomain() + idCardImage} className="preview-image" mode="aspectFit" />
                                        <Button className="re-upload-btn" size="small" onClick={uploadIdCard} loading={uploading}>
                                            重新上传
                                        </Button>
                                    </View>
                                ) : (
                                    <View className="upload-area" onClick={uploadIdCard}>
                                        <Text className="upload-icon">📷</Text>
                                        <Text className="upload-text">{uploading ? "上传中..." : "点击上传身份证照片"}</Text>
                                    </View>
                                )}
                            </View>
                        </View>
                    )}

                    {currentStep === 1 && (
                        <View className="step-content">
                            <View className="upload-section">
                                <Text className="section-title">第二步：拍摄人脸照片</Text>
                                <Text className="section-desc">请正面拍摄清晰的人脸照片</Text>

                                {faceImage ? (
                                    <View className="image-preview">
                                        <Image src={getDomain() + faceImage} className="preview-image" mode="aspectFit" />
                                        <Button className="re-upload-btn" size="small" onClick={takeFacePhoto} loading={uploading}>
                                            重新拍摄
                                        </Button>
                                    </View>
                                ) : (
                                    <View className="upload-area" onClick={takeFacePhoto}>
                                        <Text className="upload-icon">🤳</Text>
                                        <Text className="upload-text">{uploading ? "上传中..." : "点击拍摄人脸照片"}</Text>
                                    </View>
                                )}
                            </View>
                        </View>
                    )}

                    {currentStep === 2 && (
                        <View className="step-content">
                            <View className="comparing-section">
                                <Text className="section-title">第三步：人脸比对验证</Text>
                                <View className="comparing-content">
                                    {comparing ? (
                                        <View className="loading-container">
                                            <View className="loading-spinner"></View>
                                            <Text className="loading-text">正在进行人脸比对...</Text>
                                        </View>
                                    ) : (
                                        <View className="compare-images">
                                            <View className="image-item">
                                                <Text className="image-label">身份证照片</Text>
                                                <Image src={getDomain() + idCardImage} className="compare-image" mode="aspectFit" />
                                            </View>
                                            <Text className="vs-text">VS</Text>
                                            <View className="image-item">
                                                <Text className="image-label">人脸照片</Text>
                                                <Image src={getDomain() + faceImage} className="compare-image" mode="aspectFit" />
                                            </View>
                                        </View>
                                    )}
                                </View>
                            </View>
                        </View>
                    )}
                </View>

                {/* 底部操作按钮 */}
                <View className="modal-footer">
                    <View className="button-group">
                        {currentStep > 0 && currentStep < 2 && (
                            <Button className="prev-btn" onClick={handlePrev} disabled={comparing}>
                                上一步
                            </Button>
                        )}

                        {currentStep < 2 && (
                            <Button
                                className="next-btn"
                                type="primary"
                                onClick={handleNext}
                                loading={uploading}
                                disabled={(currentStep === 0 && !idCardImage) || (currentStep === 1 && !faceImage)}
                            >
                                {currentStep === 1 ? "开始验证" : "下一步"}
                            </Button>
                        )}

                        <Button className="cancel-btn" onClick={handleClose} disabled={comparing}>
                            {currentStep === 2 ? "关闭" : "取消"}
                        </Button>
                    </View>
                </View>
            </View>
        </Popup>
    );
};
