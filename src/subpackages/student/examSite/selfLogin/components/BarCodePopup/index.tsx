import React from "react";
import { View, Image } from "@tarojs/components";
import { Popup } from "@nutui/nutui-react-taro";
import moment from "moment";
import "./index.scss";

interface BarCodePopupProps {
    selfLoginData: any;
    barCodeImage: string;
    showBarCode: boolean;
    brightness: number;
    onClose: () => void;
}

export const BarCodePopup: React.FC<BarCodePopupProps> = ({
    selfLoginData,
    barCodeImage,
    showBarCode,
    onClose,
}) => {
    return (
        <Popup
            title=""
            visible={showBarCode}
            style={{
                paddingLeft: "20px",
                paddingRight: "20px",
            }}
            onClose={onClose}
        >
            <View className="barCode">
                <View className="flex-col section_2">
                    <View className="title">
                        {selfLoginData.xm}
                        {selfLoginData.zjcx != ""
                            ? " - " + selfLoginData.zjcx
                            : ""}
                    </View>

                    {selfLoginData.ksrq != "" &&
                        moment(selfLoginData.ksrq) > moment("2000-01-01") && (
                            <View className="sub-title">
                                考试日期{" "}
                                {moment(selfLoginData.ksrq).format(
                                    "YYYY-MM-DD"
                                )}
                            </View>
                        )}

                    <View className="sub-title">
                        {selfLoginData.FieldNickName}
                    </View>

                    <View className="flex-col items-center self-stretch group_2">
                        <Image
                            className="image_5"
                            id="qrcode"
                            src={barCodeImage}
                        />
                    </View>

                    <View className="self-stretch divider" />

                    <View className="flex-col self-center group_3">
                        <text className="self-stretch font text_4">
                            请直接将二维码放置扫码盒上
                        </text>
                    </View>
                </View>
            </View>
        </Popup>
    );
};
