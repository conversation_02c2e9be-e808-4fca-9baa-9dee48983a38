import React from "react";
import { View, Text, Button } from "@tarojs/components";
import "./index.scss";

interface TenantWarningModalProps {
    visible: boolean;
    onClose: () => void;
    onScan: () => void;
}

export const TenantWarningModal: React.FC<TenantWarningModalProps> = ({ visible, onClose, onScan }) => {
    // 处理遮罩层点击
    const handleOverlayClick = () => {};

    // 防止点击对话框内部时关闭
    const handleDialogClick = (e) => {
        e.stopPropagation();
    };

    if (!visible) return null;

    return (
        <View className="custom-modal-overlay" onClick={handleOverlayClick} style={{ zIndex: 999999 }}>
            <View className="custom-modal-container" onClick={handleDialogClick}>
                {/* Dialog Header */}
                <View className="custom-modal-header" style={{ justifyContent: "flex-start" }}>
                    <Text className="custom-modal-title" style={{ textAlign: "left" }}>
                        定位失败
                    </Text>
                    {/* <View className="custom-modal-close" onClick={onClose}>
                        <Close />
                    </View> */}
                </View>

                {/* Dialog Content */}
                <View className="dialog-content-container">
                    <View className="dialog-content" style={{ textAlign: "center" }}>
                        无法定位当前考场，请直接扫考场的二维码
                    </View>
                </View>

                {/* Dialog Footer */}
                <View className="dialog-popup-buttons vertical">
                    <Button
                        onClick={onScan}
                        className="confirm-button"
                        style={{
                            width: "100%",
                            height: "30px",
                            lineHeight: "30px",
                            borderRadius: "40px",
                            // background: "linear-gradient(120deg, #2c5ecc, #4c37c3)",
                            color: "#fff",
                            border: "none",
                            boxShadow: "0 4px 8px rgba(44, 94, 204, 0.2)",
                        }}
                    >
                        点击扫码
                    </Button>
                </View>
            </View>
        </View>
    );
};
