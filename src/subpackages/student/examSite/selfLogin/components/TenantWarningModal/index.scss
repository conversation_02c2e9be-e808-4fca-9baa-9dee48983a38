// 遮罩层
.nut-overlay.nut-dialog-overlay {
    background: rgba(0, 0, 0, 0.6) !important;
}

// 对话框容器
.nut-dialog-outer {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    z-index: 2000;
}

// 主对话框
.nut-dialog {
    border-radius: 12px !important;
    background: #ffffff !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1) !important;
    overflow: hidden;
}

// 标题和标题下的线条
.nut-dialog-title {
    color: #333 !important;
    font-size: 30px !important;
    font-weight: normal !important;
    text-align: center;
    padding: 20px 16px 8px !important;
    position: relative;

    &::after {
        content: "";
        display: block;
        width: 30px;
        height: 2px;
        background: linear-gradient(135deg, rgb(22, 93, 255) 0%, rgb(22, 93, 255) 100%);
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 8px;
    }
}

// 对话框内容
.nut-dialog-content {
    padding: 8px 8px 24px 8px !important;
    min-height: 40px;
    color: #666 !important;
    text-align: center;
    margin: 0;
    view {
        margin-top: 0;
    }
}

// 底部按钮区域
.dialog-footer {
    padding: 0 0 20rpx 0 !important;
    text-align: center;

    .nut-button {
        width: 100% !important;
        line-height: normal;
        height: 40px;
        color: #fff !important;
        border: none !important;
        box-shadow: 0 4px 8px rgba(44, 94, 204, 0.2);
        background: linear-gradient(120deg, #2c5ecc, #4c37c3) !important;

        &:active {
            opacity: 0.9;
        }
    }
}

// 确保弹窗垂直居中
.h5-div.nut-dialog-outer {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    max-width: 90% !important;
}
