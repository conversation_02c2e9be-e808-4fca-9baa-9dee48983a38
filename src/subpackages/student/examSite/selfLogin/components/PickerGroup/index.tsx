import React from "react";
import { Picker, DatePicker } from "@nutui/nutui-react-taro";
import moment from "moment";
import "./index.scss";

interface PickerGroupProps {
    state: any;
    handlers: any;
    fieldList: any[];
    sfzmmcList: any[];
    carTypeLsit: any[];
}

export const PickerGroup: React.FC<PickerGroupProps> = ({ state, handlers, fieldList, sfzmmcList, carTypeLsit }) => {
    return (
        <>
            <Picker
                title="选择考试场地"
                visible={state.fieldOpen}
                options={fieldList}
                value={[state.fieldId]}
                onConfirm={(list, values) => {
                    if (list?.[0]?.value) {
                        handlers.setFieldId(list[0].value);
                        handlers.clearData();
                    }
                    handlers.setFieldOpen(false);
                }}
                onClose={() => {
                    handlers.setFieldOpen(false);
                }}
            />

            <Picker
                title="选择证件类型"
                visible={state.sfzmmcOpen}
                options={sfzmmcList}
                value={[state.sfzmmc]}
                onConfirm={(list, values) => {
                    console.log("ID Type Picker onConfirm:", { list, values, currentValue: state.sfzmmc });
                    if (list?.[0]?.value) {
                        handlers.setSfzmmc(list[0].value);
                        handlers.clearData();
                    }
                    handlers.setSfzmmcOpen(false);
                }}
                onClose={() => {
                    handlers.setSfzmmcOpen(false);
                }}
            />

            <Picker
                title="选择考试车型"
                visible={state.carTypeOpen}
                options={carTypeLsit}
                value={[state.carType]}
                onConfirm={(list, values) => {
                    console.log("Car Type Picker onConfirm:", { list, values, currentValue: state.carType });
                    if (list?.[0]?.value) {
                        handlers.setCarType(list[0].value);
                    }
                    handlers.setCarTypeOpen(false);
                }}
                onClose={() => {
                    handlers.setCarTypeOpen(false);
                }}
            />

            {state.ksrqOpen && (
                <DatePicker
                    title="选择学员考试日期"
                    visible={state.ksrqOpen}
                    value={new Date(state.ksrq)}
                    showChinese
                    onClose={() => handlers.setKsrqOpen(false)}
                    threeDimensional={true}
                    onConfirm={(options, values) => {
                        handlers.setKsrq(moment(`${values[0]}-${values[1]}-${values[2]}`).format("YYYY-MM-DD"));
                    }}
                    startDate={new Date(moment().subtract(1, "years").format("YYYY-01-01"))}
                    endDate={new Date(moment().add(1, "years").format("YYYY-12-31"))}
                />
            )}
        </>
    );
};
