// PaymentPopup/index.scss
.payment-popup {
    .payment-header {
        // padding: 24px 32px 16px;
        padding: 20px;
        // display: flex;
        // justify-content: space-between;
        // align-items: center;
        // margin-bottom: 32px;
        border-bottom: 1px solid #f5f5f5;
        padding: 16px;
        text-align: center;

        .title {
            font-size: 32rpx;
            font-weight: 500;
            text-align: center;
        }

        .close-icon {
            padding: 8rpx;
            color: #999;
        }
    }

    .payment-groups {
        flex: 1;
        overflow-y: auto;
        padding: 16px 24px;
    }
    .payment-amount {
        text-align: center;
        margin-bottom: 40px;

        .label {
            font-size: 28rpx;
            color: #666;
            margin-bottom: 12px;
            display: block;
            line-height: 100rpx;
        }

        .amount {
            font-size: 60rpx;
            font-weight: bold;
            color: #333;
            line-height: 140rpx;
        }
    }

    .payment-method {
        margin-bottom: 40px;

        .method-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24rpx 0;
            border-bottom: 1px solid #f5f5f5;

            .method-left {
                display: flex;
                align-items: center;
                gap: 12px;

                .method-name {
                    font-size: 28rpx;
                    color: #333;
                }
            }
        }
    }
    .footer {
        position: inherit;
        .pay-button {
            margin: 0 16px;
        }
    }

    .payment-footer {
        padding-bottom: env(safe-area-inset-bottom); // 适配iPhone底部

        .pay-button {
            height: 88rpx;
            border-radius: 44rpx;
            font-size: 32rpx;
            font-weight: 500;
            background: #7539dd;
            border: none;

            &:active {
                opacity: 0.9;
            }
        }
    }
}

// 动画效果
.nut-popup {
    &-slide-bottom-enter-active {
        transition: all 0.3s ease-out;
    }

    &-slide-bottom-leave-active {
        transition: all 0.2s ease-in;
    }

    &-slide-bottom-enter-from,
    &-slide-bottom-leave-to {
        transform: translateY(100%);
    }
}
