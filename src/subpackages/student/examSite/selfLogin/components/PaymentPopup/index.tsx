// PaymentPopup.tsx
import React from "react";
import { View, Text } from "@tarojs/components";
import { Popup, Button } from "@nutui/nutui-react-taro";
import "./index.scss";

interface PaymentPopupProps {
    visible: boolean;
    amount: number;
    loadingText: string;
    scanPayLoading: boolean;
    confirmLoading: boolean;
    onClose: () => void;
    onConfirm: () => void;
    onScanPay: () => void;
}

export const PaymentPopup: React.FC<PaymentPopupProps> = ({ visible, amount, loadingText, scanPayLoading, confirmLoading, onClose, onConfirm, onScanPay }) => {
    return (
        <Popup visible={visible} position="bottom" round onClose={onClose}>
            <View className="payment-popup">
                <View className="payment-header">
                    <Text className="title">收银台</Text>
                </View>
                <View className="payment-groups" style={{ marginBottom: "8vh" }}>
                    <View className="payment-amount">
                        <Text className="label" style={{ marginTop: "8vh" }}>
                            支付金额
                        </Text>
                        <Text className="amount">¥{amount.toFixed(2)}</Text>
                    </View>
                </View>

                <View className="footer" style={{ paddingBottom: "calc(env(safe-area-inset-bottom) + 32rpx)" }}>
                    <Button type="primary" block fill="outline" onClick={onScanPay} className="pay-button" loading={scanPayLoading} style={{ margin: "0 16px" }}>
                        {scanPayLoading && loadingText != "" ? loadingText : "扫对方支付码"}
                    </Button>
                    <Button type="primary" block onClick={onConfirm} className="pay-button" loading={confirmLoading} style={{ margin: "16px 16px 0 16px" }}>
                        {confirmLoading && loadingText != "" ? loadingText : "确认支付"}
                    </Button>
                </View>
            </View>
        </Popup>
    );
};
