.plan-details-popup {
    display: flex;
    flex-direction: column;
    height: 100%;

    .footer {
        position: inherit;
    }

    .popup-header {
        padding: 20px;
        border-bottom: 1px solid #f5f5f5;
        text-align: center;

        .title {
            font-size: 32rpx;
            font-weight: 500;
            // color: #333;
            text-align: center;
        }
    }

    .plan-groups {
        flex: 1;
        overflow-y: auto;
        padding: 16px 24px;

        .plan-group {
            margin-bottom: 24px;

            .group-info {
                margin-bottom: 16px;
                padding: 0 8px;

                .field-name {
                    font-size: 28px;
                    font-weight: 500;
                    color: #333;
                    margin-right: 16px;
                }

                .subject-name {
                    font-size: 26px;
                    color: #666;
                }
            }

            .time-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 16px;

                .time-slot {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding: 16px;
                    border-radius: 8px;
                    background: #f8f8f8;
                    border: 2px solid transparent;

                    &.selected {
                        background: #e6f7ff;
                        border-color: #1890ff;
                    }

                    &.disabled {
                        opacity: 0.5;
                        background: #f5f5f5;
                    }

                    &:active:not(.disabled) {
                        opacity: 0.8;
                    }

                    .time {
                        font-size: 28px;
                        color: #333;
                        margin-bottom: 8px;
                    }

                    .capacity {
                        font-size: 24px;
                        color: #666;
                    }
                }
            }
        }
    }

    // .popup-footer {
    //     padding: 24px 32px;
    //     border-top: 1px solid #f5f5f5;

    //     .submit-button {
    //         height: 80px;
    //         font-size: 32px;
    //         border-radius: 8px;
    //     }
    // }
}
