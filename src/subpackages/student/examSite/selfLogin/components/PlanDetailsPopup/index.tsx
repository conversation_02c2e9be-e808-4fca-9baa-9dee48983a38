import React, { useState, useEffect } from "react";
import { View, Text } from "@tarojs/components";
import { Button, Popup } from "@nutui/nutui-react-taro";
import "./index.scss";
import Taro from "@tarojs/taro";

export interface PlanDetail {
    StartTime: string;
    EndTime: string;
    NickName: string;
    PeopleCount: number;
    MaxPeople: number;
    Id: string;
    CarModelId: string;
    ModelName: string;
    KeMuIdvalue: string;
}

interface PlanDetailsPopupProps {
    planDetailList: PlanDetail[][]; // 修改为二维数组
    planDetailsOpen: boolean;
    onClose: () => void;
    onSelect?: (planDetail: PlanDetail) => void;
}

export const PlanDetailsPopup: React.FC<PlanDetailsPopupProps> = ({ planDetailList, planDetailsOpen, onClose, onSelect }) => {
    const [selectedPlan, setSelectedPlan] = useState<PlanDetail | undefined>(undefined);

    // 重置状态
    useEffect(() => {
        if (!planDetailsOpen) {
            setSelectedPlan(undefined);
        }
    }, [planDetailsOpen]);

    // 处理选择
    const handleTimeSelect = (plan: PlanDetail) => {
        setSelectedPlan(plan);

        // 添加触觉反馈
        if (Taro.vibrateShort) {
            Taro.vibrateShort({ type: "light" });
        }
    };

    // 处理提交
    const handleSubmit = () => {
        console.log(selectedPlan);
        if (!selectedPlan) return;
        onSelect?.(selectedPlan);
        onClose();
    };

    return (
        <Popup visible={planDetailsOpen && planDetailList.length > 0} position="bottom" round>
            <View className="plan-details-popup">
                <View className="popup-header">
                    <Text className="title">场次选择</Text>
                </View>

                <View className="plan-groups" style={{ minHeight: "30vh" }}>
                    {planDetailList.map((group, groupIndex) => (
                        <View key={groupIndex} className="plan-group">
                            {group.length > 0 && (
                                <View className="group-info">
                                    <Text className="field-name">{group[0].ModelName}</Text>
                                    <Text className="subject-name">{group[0].KeMuIdvalue}</Text>
                                </View>
                            )}

                            <View className="time-grid">
                                {group.map(
                                    (plan) =>
                                        plan.PeopleCount < plan.MaxPeople && (
                                            <View
                                                key={plan.Id}
                                                className={`time-slot ${selectedPlan?.Id === plan.Id ? "selected" : ""} 
                                                 ${plan.PeopleCount >= plan.MaxPeople ? "disabled" : ""}`}
                                                onClick={() => {
                                                    if (plan.PeopleCount < plan.MaxPeople) {
                                                        handleTimeSelect(plan);
                                                    }
                                                }}
                                            >
                                                <Text className="time">{plan.NickName || plan.StartTime}</Text>
                                                {/* <Text className="capacity">
                                                    {plan.PeopleCount}/{plan.MaxPeople < 0 ? 0 : plan.MaxPeople}
                                                </Text> */}
                                            </View>
                                        )
                                )}
                            </View>
                        </View>
                    ))}
                </View>
                <View className="footer" style={{ paddingBottom: "env(safe-area-inset-bottom)" }}>
                    <Button type="primary" block disabled={!selectedPlan} onClick={handleSubmit} className="submit-button" style={{ margin: "0 16px" }}>
                        确认选择
                    </Button>
                </View>
            </View>
        </Popup>
    );
};
