// components/ExamSelection/index.scss
.select-exam-item {
    width: 90vw;
    padding: 20rpx;
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
    background-color: rgba(0, 0, 0, 0.7);

    .cell-group {
        margin-bottom: 20rpx;
    }

    .exam-title {
        display: inline-flex;
        align-items: center;
        margin-left: 5px;
    }

    .exam-description {
        span {
            margin-right: 10rpx;
        }
    }
}
