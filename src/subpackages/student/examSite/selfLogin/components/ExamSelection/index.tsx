import React from "react";
import { View } from "@tarojs/components";
import { Cell, Button, Popup } from "@nutui/nutui-react-taro";
import moment from "moment";
import "./index.scss";

interface ExamSelectionProps {
    examList: any[];
    onExamSelect: (item: any) => void;
    onClose: () => void;
}

export const ExamSelection: React.FC<ExamSelectionProps> = ({ examList, onExamSelect, onClose }) => {
    return (
        <Popup visible={examList.length > 0} onClose={onClose}>
            <View className="select-exam-item">
                {examList.map((item: any, index) => (
                    <View
                        style={{
                            marginBottom: "20rpx",
                        }}
                        key={index}
                    >
                        <Cell.Group>
                            <Cell
                                title={
                                    <div
                                        style={{
                                            display: "inline-flex",
                                            alignItems: "center",
                                        }}
                                    >
                                        <span style={{ marginLeft: "5px" }}>{item.xm}</span>
                                    </div>
                                }
                                description={
                                    <>
                                        <span>{item.sfzmhm}</span>
                                        <span>
                                            考试日期:
                                            {moment(item.ksrq).format("MM月DD日")}
                                        </span>
                                        <span>考试车型:{item.zjcx}</span>
                                    </>
                                }
                                extra={item.jxmc}
                            />
                            <Cell
                                align="center"
                                style={{
                                    paddingTop: "10px",
                                    paddingBottom: "10px",
                                }}
                                extra={
                                    <Button type="primary" size="small" onClick={() => onExamSelect(item)}>
                                        选择该记录
                                    </Button>
                                }
                            />
                        </Cell.Group>
                    </View>
                ))}
            </View>
        </Popup>
    );
};
