/* components/RegistrationForm/index.scss */
page {
    font-family: "AlibabaPuHuiTi";
    // font-family: <PERSON><PERSON> SC, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
}
.modern-registration {
    min-height: 100vh;
    background: #f5f7fa;

    // 头部样式优化 - 更加紧凑
    .registration-header {
        // background: linear-gradient(135deg, #4776e6, #8e54e9);
        background: var(--nutui-color-primary);
        padding: 62rpx 24rpx 60rpx;
        position: relative;

        &::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40rpx; // 减小高度
            background: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.05));
            backdrop-filter: blur(10rpx);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16rpx; // 减小间距

            .icon-wrap {
                width: 56rpx; // 减小图标尺寸
                height: 56rpx;
                background: rgba(255, 255, 255, 0.2);
                backdrop-filter: blur(10rpx);
                border: 1rpx solid rgba(255, 255, 255, 0.3);
                border-radius: 16rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 16rpx;
                color: #ffffff;
                box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
                transform: rotate(-5deg);
            }

            .title {
                font-size: 64rpx; // 减小字号
                color: #ffffff;
                font-weight: 600;
                letter-spacing: 1rpx;
                text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
            }
        }

        .subtitle {
            font-size: 24rpx; // 减小字号
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            display: block;
            text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
        }
    }

    // 表单容器 - 上移更多
    .form-wrapper {
        margin: -40rpx 16rpx 0; // 减小边距，增加上移
        position: relative;
        z-index: 1;
    }

    // 表单卡片 - 更加紧凑
    .form-card {
        background: #ffffff;
        border-radius: 20rpx;
        padding: 20rpx; // 减小内边距
        margin-bottom: 16rpx; // 减小卡片间距
        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
        border: 1rpx solid rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20rpx);
        transition: transform 0.3s, box-shadow 0.3s;

        &:hover {
            transform: translateY(-2rpx);
            box-shadow: 0 10rpx 32rpx rgba(0, 0, 0, 0.1);
        }
    }

    // 表单字段 - 左标签右输入框布局
    .form-field {
        padding: 14rpx 0; // 垂直内边距
        border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
        display: flex;
        align-items: center;
        justify-content: space-between;

        &:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        &:first-child {
            padding-top: 0;
        }

        &.clickable {
            cursor: pointer;
            transition: all 0.3s;

            &:active {
                background: rgba(0, 0, 0, 0.02);
                border-radius: 10rpx;
            }
        }

        .field-label {
            font-size: 28rpx;
            color: #1a1a1a;
            // font-weight: 200;
            min-width: 140rpx;
            padding-right: 20rpx;
        }

        .field-input-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            min-height: 64rpx;
            position: relative;

            .field-value {
                // font-family: "AlibabaPuHuiTi";
                // font-family: Yuanti SC, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
                font-size: 28rpx;
                color: #666;
                flex: 1;
                text-align: right;
                padding-right: 10rpx;
            }

            .iconfont {
                color: #999;
                font-size: 28rpx;
            }
        }

        .field-input {
            // font-family: "AlibabaPuHuiTi";
            // font-family: Yuanti SC, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
            font-size: 28rpx;
            color: #666;
            flex: 1;
            text-align: right;
            padding-right: 10rpx;

            &::placeholder {
                color: #999;
                text-align: right;
            }

            &:focus {
                border-color: var(--nutui-color-primary);
            }

            &:disabled {
                color: #999;
                background: transparent;
            }
        }
    }

    // 按钮区域 - 更加紧凑
    .action-buttons {
        padding: 20rpx 16rpx; // 减小内边距

        .btn-primary,
        .btn-secondary {
            width: 100%;
            height: 88rpx; // 减小按钮高度
            border-radius: 44rpx;
            font-size: 30rpx; // 减小字号
            font-weight: 500;
            margin-bottom: 12rpx; // 减小间距
            position: relative;
            overflow: hidden;
            border: none;
            transition: all 0.3s;

            &:active {
                transform: translateY(2rpx);
            }
        }

        .btn-primary {
            // background: linear-gradient(135deg, #4776e6, #8e54e9);
            background: var(--nutui-color-primary);
            color: #ffffff;
            box-shadow: 0 6rpx 16rpx rgba(142, 84, 233, 0.25);

            &:active {
                box-shadow: 0 3rpx 8rpx rgba(142, 84, 233, 0.2);
            }
        }

        .btn-secondary {
            background: #fff;
            color: #8e54e9;
            border: 1.5rpx solid #8e54e9; // 减小边框宽度

            &:active {
                background: rgba(142, 84, 233, 0.05);
            }
        }

        .btn-plain {
            width: 100%;
            // border: 1px solid rgba(142, 84, 233, 0.3);
            border: 1rpx solid var(--nutui-color-primary);
            background: transparent;
            // color: #8e54e9;
            color: var(--nutui-color-primary);
            padding: 10rpx 20rpx; // 减小内边距
            border-radius: 44rpx;
            font-size: 30rpx; // 减小字号
            transition: all 0.3s;

            &:active {
                background: rgba(142, 84, 233, 0.05);
                opacity: 0.9;
            }
        }

        .btn-order {
            width: 100%;
            border: 1rpx solid var(--nutui-color-primary);
            background: #ffffff;
            color: var(--nutui-color-primary);
            padding: 16rpx 24rpx;
            border-radius: 48rpx;
            font-size: 30rpx;
            transition: all 0.3s ease;
            box-shadow: 0 4rpx 12rpx rgba(142, 84, 233, 0.15);
            backdrop-filter: blur(10rpx);

            &:active {
                background: rgba(71, 118, 230, 0.05);
                transform: translateY(1rpx);
                box-shadow: 0 2rpx 8rpx rgba(142, 84, 233, 0.2);
            }

            .btn-order-content {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12rpx;

                .btn-order-text {
                    // font-weight: 500;
                    font-size: 30rpx;
                    color: var(--nutui-color-primary);
                    letter-spacing: 0.5rpx;
                }

                .iconfont {
                    color: var(--nutui-color-primary);
                    transition: transform 0.3s ease;
                }
            }

            &:hover .btn-order-content .iconfont {
                transform: scale(1.1);
            }
        }

        // 按钮行容器 - 支持两个并排按钮
        .btn-row {
            display: flex;
            gap: 0; // 移除间距，让按钮紧贴
            width: 100%;

            .btn-half {
                flex: 1;
                width: auto !important;

                // 左边按钮：右上角和右下角是直角，右边框减半
                // &.btn-order {
                //     border-radius: 48rpx 0 0 48rpx !important;
                //     border-right-width: 0rpx !important;
                // }

                // // 右边按钮：左上角和左下角是直角，左边框减半
                // &.btn-face {
                //     border-radius: 0 48rpx 48rpx 0 !important;
                //     border-left-width: 0.5rpx !important;
                // }
            }
        }

        // 人脸登记按钮样式
        .btn-face {
            border: 1rpx solid var(--nutui-color-primary);
            background: #ffffff;
            color: var(--nutui-color-primary);
            padding: 16rpx 24rpx;
            border-radius: 48rpx;
            font-size: 30rpx;
            transition: all 0.3s ease;
            box-shadow: 0 4rpx 12rpx rgba(142, 84, 233, 0.15);
            backdrop-filter: blur(10rpx);

            &:active {
                background: rgba(71, 118, 230, 0.05);
                transform: translateY(1rpx);
                box-shadow: 0 2rpx 8rpx rgba(142, 84, 233, 0.2);
            }

            .btn-face-content {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12rpx;

                .btn-face-text {
                    font-size: 30rpx;
                    color: var(--nutui-color-primary);
                    letter-spacing: 0.5rpx;
                }
            }
        }
    }

    // 优化动画效果
    @keyframes slideUp {
        from {
            transform: translateY(20rpx); // 减小动画距离
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .form-card {
        animation: slideUp 0.3s ease-out; // 加快动画速度
    }

    // 空数据状态样式
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 20px;
        min-height: 60vh;

        // 使用CSS绘制空状态图标
        .empty-icon {
            width: 120px;
            height: 120px;
            margin-bottom: 20px;
            position: relative;

            &::before {
                content: "";
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 80px;
                height: 100px;
                border: 4px solid #e6e8f1;
                border-radius: 8px;
            }

            &::after {
                content: "";
                position: absolute;
                top: 30%;
                left: 50%;
                transform: translateX(-50%);
                width: 40px;
                height: 4px;
                background: #e6e8f1;
                box-shadow: 0 16px 0 #e6e8f1, 0 32px 0 #e6e8f1;
            }
        }

        .empty-text {
            font-size: 28px;
            color: #999;
            text-align: center;
            line-height: 1.5;
        }
    }
}
