// components/RegistrationForm/index.tsx
import React, { useState } from "react";
import { View, Text, Input } from "@tarojs/components";
import { Button } from "@nutui/nutui-react-taro";
import request from "@service/request";
import IconFont from "@components/iconfont";
import "./index.scss";
import Taro from "@tarojs/taro";
import { FaceRegistrationModal } from "../FaceRegistrationModal";

interface RegistrationFormProps {
    state: any;
    handlers: any;
    message: any;
    statusBarHeight: number;
    onShowProducts: any;
}

export const RegistrationForm: React.FC<RegistrationFormProps> = ({ state, handlers, message, statusBarHeight, onShowProducts }) => {
    // 人脸登记弹窗状态
    const [showFaceRegistration, setShowFaceRegistration] = useState(false);
    // 下一步处理函数
    const handleNextStep = () => {
        if (!state.fieldId || state.fieldId == "") {
            message.openToast("请先选择考试场地");
            return;
        }
        if (state.xm.length < 2) {
            message.error("请输入正确的名字!", "输入错误");
            return;
        }
        if (state.sfzmhm.length < 6) {
            message.error("请输入正确的证件号码!", "输入错误");
            return;
        }
        message.openLoading("正在读取相关数据");
        request
            .post<any>("/Jx/ExamSite/Wx/Exam/getExamList", {
                FieldId: state.fieldId,
                xm: state.xm,
                sfzmmc: state.sfzmmc,
                sfzmhm: state.sfzmhm,
                yddh: state.yddh,
            })
            .then((json) => {
                message.closeLoading();
                if (json && json.success) {
                    if (json.data.data.length == 0) {
                        message.openDialog(
                            "检索失败",
                            "没有找到约考信息，请核对名字或者身份证是否正确?",
                            () => {
                                handlers.setCarType("");
                                handlers.setKsrq("");
                                handlers.setCc("");
                                handlers.setJxmc("");
                                handlers.setStepIndex(2);
                            },
                            "确认继续",
                            "重新输入"
                        );
                    } else if (json.data.data.length == 1) {
                        handlers.handleExamSelect(json.data.data[0]);
                    } else {
                        handlers.setExamList(json.data.data);
                    }
                } else {
                    message.openDialog("读取失败", json.message);
                }
            })
            .catch(() => {
                message.openDialog("操作失败", "系统故障，稍后重试!");
            });
    };

    // 继续购买处理函数
    const handleContinueShopping = () => {
        if (!state.carType || state.carType == "") {
            message.error("请先选择考试车型");
        } else {
            message.openLoading("正在获取商品信息");
            request
                .post<any>("/Jx/ExamSite/Wx/Shop/getSaleItemList", {
                    FieldId: state.fieldId,
                    xm: state.xm,
                    sfzmmc: state.sfzmmc,
                    sfzmhm: state.sfzmhm,
                    yddh: state.yddh,
                    CarType: state.carType,
                })
                .then((json) => {
                    message.closeLoading();
                    if (json && json.success) {
                        if (json.data.length > 0) {
                            if (onShowProducts) onShowProducts(json.data);
                        } else {
                            message.openDialog("读取失败", "获取商品列表失败");
                        }
                    } else {
                        message.openToast(json.message);
                    }
                });
        }
    };

    return (
        <View className="modern-registration">
            {/* 头部区域 */}
            <View className="registration-header" style={{ paddingTop: `${statusBarHeight}px` }}>
                <View className="header-content">
                    <View className="icon-wrap">
                        <IconFont name="gantanhao" size={38} />
                    </View>
                    <View className="title">实名登记</View>
                </View>
                <Text className="subtitle">请完成信息登记，以匹配考试信息</Text>
            </View>

            {/* 表单内容区域 */}
            <View className="form-wrapper">
                {/* 基础信息表单 */}
                <View className="form-card">
                    {/* 考试场地 */}
                    <View className="form-field clickable" onClick={() => handlers.setFieldOpen(true)}>
                        <Text className="field-label">考试场地</Text>
                        <View className="field-input-container">
                            {state.fieldList && state.fieldList.length > 0 ? (
                                <>
                                    <Text className="field-value">{state.fieldList?.find((m: any) => m.value == state.fieldId)?.text || "选择考试场地"}</Text>
                                    {/* <IconFont
                                        name="Arrow-Right2"
                                        size={36}
                                        style={{
                                            marginTop: "6rpx",
                                        }}
                                    /> */}
                                </>
                            ) : (
                                <>
                                    <Text className="field-value">扫码选择考场</Text>
                                    <View
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            Taro.scanCode({
                                                success: (res) => {
                                                    // 处理扫码结果，假设扫码结果包含场地ID
                                                    try {
                                                        const scanData = JSON.parse(res.result);
                                                        if (scanData && scanData.fieldId) {
                                                            handlers.setFieldId(scanData.fieldId);
                                                        } else {
                                                            message.openToast("无效的考场二维码");
                                                        }
                                                    } catch (err) {
                                                        message.openToast("无法解析二维码");
                                                    }
                                                },
                                                fail: () => {
                                                    message.openToast("扫码失败");
                                                },
                                            });
                                        }}
                                    >
                                        <IconFont
                                            name="line-scan"
                                            size={36}
                                            style={{
                                                marginTop: "6rpx",
                                            }}
                                        />
                                    </View>
                                </>
                            )}
                        </View>
                    </View>

                    {/* 学员姓名 */}
                    <View className="form-field">
                        <Text className="field-label">学员姓名</Text>
                        <View className="field-input-container">
                            <Input
                                className="field-input"
                                placeholder="请输入姓名"
                                value={state.xm}
                                onInput={(e) => {
                                    handlers.setXm(e.detail.value);
                                    handlers.clearData();
                                }}
                            />
                        </View>
                    </View>

                    {/* 证件类型 */}
                    <View className="form-field clickable" onClick={() => handlers.setSfzmmcOpen(true)}>
                        <Text className="field-label">证件类型</Text>
                        <View className="field-input-container">
                            <Text className="field-value">{state.sfzmmcList?.find((m: any) => m.value == state.sfzmmc)?.text || "请选择证件类型"}</Text>
                            {/* <IconFont
                                name="Arrow-Right2"
                                size={36}
                                style={{
                                    marginTop: "6rpx",
                                }}
                            /> */}
                        </View>
                    </View>

                    {/* 证件号码 */}
                    <View className="form-field">
                        <Text className="field-label">证件号码</Text>
                        <View className="field-input-container">
                            <Input
                                className="field-input"
                                placeholder="请输入证件号码"
                                type={state.sfzmmc == "A" || state.sfzmmc == "Q" ? "idcard" : "text"}
                                value={state.sfzmhm}
                                onInput={(e) => {
                                    handlers.setSfzmhm(e.detail.value);
                                    handlers.clearData();
                                }}
                            />
                        </View>
                    </View>

                    {/* 手机号码 */}
                    <View className="form-field">
                        <Text className="field-label">手机号码</Text>
                        <View className="field-input-container">
                            <Input
                                className="field-input"
                                placeholder="请输入手机号码"
                                type="number"
                                maxlength={11}
                                value={state.yddh}
                                onInput={(e) => handlers.setYddh(e.detail.value)}
                            />
                        </View>
                    </View>
                </View>

                {/* 考试信息表单 */}
                {state.stepIndex == 2 && (
                    <View className="form-card">
                        {/* 考试车型 */}
                        <View className="form-field clickable" onClick={() => handlers.setCarTypeOpen(true)}>
                            <Text className="field-label">考试车型</Text>
                            <View className="field-input-container">
                                <Text className="field-value">{state.carTypeLsit?.find((m: any) => m.value == state.carType)?.text || "请选择考试车型"}</Text>
                                <IconFont name="Arrow-Right2" size={36} />
                            </View>
                        </View>

                        {/* 考试日期 */}
                        {state.ksrq && (
                            <View className="form-field clickable" onClick={() => handlers.setKsrqOpen(true)}>
                                <Text className="field-label">考试日期</Text>
                                <View className="field-input-container">
                                    <Text className="field-value">{state.ksrq}</Text>
                                    <IconFont name="Arrow-Right2" size={36} />
                                </View>
                            </View>
                        )}

                        {/* 考试场次 */}
                        {state.cc && (
                            <View className="form-field">
                                <Text className="field-label">考试场次</Text>
                                <View className="field-input-container">
                                    <Input className="field-input" value={state.cc} disabled />
                                </View>
                            </View>
                        )}
                    </View>
                )}
            </View>

            {/* 底部按钮区域 */}
            <View className="action-buttons">
                {state.stepIndex == 1 ? (
                    <>
                        <Button className="btn-primary" onClick={handleNextStep}>
                            下一步
                        </Button>
                        <View className="btn-row">
                            <Button
                                className="btn-order btn-half"
                                type="default"
                                onClick={() => {
                                    Taro.navigateTo({
                                        url: "/subpackages/student/examSite/order/index?TenantId=" + state.tenantId + "&FieldId=" + state.fieldId,
                                    });
                                }}
                            >
                                <View className="btn-order-content">
                                    <Text className="btn-order-text">我的订单</Text>
                                </View>
                            </Button>
                        </View>
                    </>
                ) : (
                    <>
                        <Button className="btn-primary" onClick={handleContinueShopping}>
                            继续购买商品
                        </Button>
                        {/* 只有在有考试信息时才显示人脸登记按钮 */}
                        {(state.examInfo || (state.carType && state.ksrq && state.cc)) && (
                            <View className="btn-row">
                                <Button
                                    className="btn-face btn-half"
                                    type="default"
                                    onClick={() => {
                                        setShowFaceRegistration(true);
                                    }}
                                >
                                    <View className="btn-face-content">
                                        <Text className="btn-face-text">人脸登记</Text>
                                    </View>
                                </Button>
                            </View>
                        )}
                    </>
                )}
            </View>

            {/* 人脸登记弹窗 */}
            <FaceRegistrationModal
                visible={showFaceRegistration}
                onClose={() => setShowFaceRegistration(false)}
                onSuccess={() => {
                    message.openToast("人脸登记成功");
                }}
                userInfo={{
                    xm: state.xm,
                    sfzmhm: state.sfzmhm,
                    fieldId: state.fieldId,
                }}
                message={message}
            />
        </View>
    );
};
