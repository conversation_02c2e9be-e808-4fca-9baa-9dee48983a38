import Taro from "@tarojs/taro";
import { message } from "@components/MessageApi/MessageApiSingleton";

interface CoachActionSheetProps {
    visible: boolean;
    onClose: () => void;
    TenantId?: string;
}

export const CoachActionSheet = ({ visible, onClose, TenantId }: CoachActionSheetProps) => {
    const showActionSheet = () => {
        if (!TenantId) {
            message.error("缺少公司 Id 参数，请重新扫码重试!");
            onClose();
            return;
        }

        Taro.showActionSheet({
            itemList: ["已注册身份绑定", "新教练注册申请"],
            success: (res) => {
                if (res.tapIndex === 0) {
                    Taro.navigateTo({
                        url: `/pages/user/findMainUser/index?TenantId=${TenantId}`,
                    });
                    onClose();
                } else if (res.tapIndex === 1) {
                    Taro.navigateTo({
                        url: `/pages/user/create/index?TenantId=${TenantId}`,
                    });
                    onClose();
                }
            },
            fail: () => {
                onClose();
            },
        });
    };

    if (visible) {
        showActionSheet();
    }

    return null;
};
