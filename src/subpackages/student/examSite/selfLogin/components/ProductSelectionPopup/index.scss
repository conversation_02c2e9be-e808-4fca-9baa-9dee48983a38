.product-selection {
    // height: 80vh;
    display: flex;
    flex-direction: column;
    // padding: 0 20rpx;

    .header {
        padding: 16px;
        margin-bottom: 10rpx;
        border-bottom: 1px solid #eee;
        position: sticky;
        top: 0;
        background: #fff;
        z-index: 1;

        .title {
            font-size: 32rpx;
            font-weight: 500;
            text-align: center;
        }
    }

    .product-list {
        padding-bottom: 10rpx;
        max-height: 60vh;
        overflow-y: auto;
        height: 100%;

        .nut-cell {
            padding: 0 10rpx;
            margin: 0;
        }

        .product-item-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding: 16px;

            .product-info-left {
                flex: 1;
                margin-right: 16px;

                .product-details {
                    .product-name {
                        font-size: 24rpx;
                        color: #333;
                    }

                    .product-price {
                        font-size: 26rpx;
                        color: #ff4d4f;
                        margin-top: 8rpx;
                    }
                }
            }
        }
    }

    .footer {
        position: sticky;
        bottom: 0;
        background: #fff;
        padding: 20rpx 0;
        padding-bottom: calc(16px + constant(safe-area-inset-bottom)); /* iOS 11.0 */
        padding-bottom: calc(16px + env(safe-area-inset-bottom)); /* iOS 11.2+ */
        z-index: 100;

        .total-amount {
            font-size: 28rpx;
            margin-bottom: 20rpx;
            text-align: right;
            padding-right: 20rpx;

            .amount {
                color: #ff4d4f;
                font-weight: 500;
            }
        }

        .nutui-button {
            margin-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
            margin-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
        }
    }
}

.product-item-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 16px;
}

.product-info-left {
    flex: 1;
    margin-right: 16px;

    .product-name {
        font-size: 28px;
        color: #333;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .product-price {
        font-size: 32px;
        color: #ff4949;
        font-weight: bold;
    }
}

.quantity-control-right {
    flex-shrink: 0;
    display: flex;
    align-items: center;

    :global(.nut-input-number) {
        width: 200px;
    }
}

.product-selection-popup {
    .popup-content {
        .product-list {
            height: calc(100vh - 300rpx);
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }
    }

    .product-item {
        .product-name {
            font-size: 28rpx;
        }

        .product-desc {
            font-size: 24rpx;
            color: #666;
        }

        .price {
            font-size: 26rpx;

            .amount {
                font-size: 32rpx;
            }
        }
    }
}
