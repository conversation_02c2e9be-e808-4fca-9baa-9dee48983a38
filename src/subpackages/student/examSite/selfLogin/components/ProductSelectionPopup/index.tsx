import React, { useState, useEffect } from "react";
import { View, Image, Text } from "@tarojs/components";
import { Popup, InputNumber, Button, Cell, ConfigProvider, Checkbox, Toast } from "@nutui/nutui-react-taro";
import "./index.scss";
import { message } from "@components/MessageApi/MessageApiSingleton";

export interface Product {
    Id: string;
    ItemName: string;
    Price: number;
    AccountId: string; // 添加 AccountId 字段
    image?: string;
    selected?: boolean;
    Quantity?: number;
    OutTable: number;
}

// 新增 PayDetailInPut 接口
export interface PayDetailInPut {
    OutId: string; // Guid in C# maps to string in TypeScript
    Count: number;
    Price: number;
    OutTable: number;
}

interface ProductSelectionPopupProps {
    visible: boolean;
    products: Product[];
    onClose: () => void;
    onConfirm: (payDetails: PayDetailInPut[]) => void; // 修改返回类型
}

export const ProductSelectionPopup: React.FC<ProductSelectionPopupProps> = ({ visible, products, onClose, onConfirm }) => {
    const [productList, setProductList] = useState<Product[]>([]);

    useEffect(() => {
        setProductList(products.map((p) => ({ ...p, selected: false, Quantity: 1 })));
    }, [products]);

    // 获取当前已选商品的 AccountId
    const getSelectedAccountId = (): string | null => {
        const selectedProducts = productList.filter((p) => p.selected);
        if (selectedProducts.length === 0) return null;
        return selectedProducts[0].AccountId;
    };

    const handleQuantityChange = (id: string, quantity: number) => {
        setProductList((prev) => prev.map((p) => (p.Id === id ? { ...p, Quantity: quantity } : p)));
    };

    const getTotalAmount = () => {
        return productList.filter((p) => p.selected).reduce((sum, p) => sum + p.Price * (p.Quantity || 1), 0);
    };

    const customTheme = {
        nutuiInputnumberButtonWidth: "60rpx",
        nutuiInputnumberButtonHeight: "60rpx",
        nutuiInputnumberButtonBorderRadius: "8rpx",
        nutuiInputnumberButtonBackgroundColor: "#f4f4f4",
        nutuiInputnumberInputHeight: "60rpx",
        nutuiInputnumberInputMargin: "0 4rpx",
        nutuiCheckboxLabelFontSize: "28rpx",
        nutuiCheckboxButtonBorderRadius: "8rpx",
        nutuiCheckboxLabelLineHeight: "28rpx",
    };

    const toggleSelection = (id: string) => {
        const targetProduct = productList.find((p) => p.Id === id);
        if (!targetProduct) return;

        const currentSelectedAccountId = getSelectedAccountId();

        // 如果是取消选中，直接允许
        if (targetProduct.selected) {
            setProductList((prev) => prev.map((p) => (p.Id === id ? { ...p, selected: false } : p)));
            return;
        }

        console.log(currentSelectedAccountId);
        console.log(targetProduct.AccountId);
        // 检查 AccountId 是否一致
        if (currentSelectedAccountId && currentSelectedAccountId !== targetProduct.AccountId) {
            message.error("不能选择不同账户的商品");
            return;
        }

        setProductList((prev) => prev.map((p) => (p.Id === id ? { ...p, selected: true } : p)));
    };

    // 转换选中的商品为 PayDetailInPut 格式
    const convertToPayDetails = () =>
        productList
            .filter((p) => p.selected)
            .map(
                (product) =>
                    ({
                        OutId: product.Id,
                        OutTable: product.OutTable,
                        Count: product.Quantity || 1,
                        Price: product.Price,
                    } as PayDetailInPut)
            );

    // 添加控制页面滚动的副作用
    // useEffect(() => {
    //     if (visible) {
    //         // 禁用主页面滚动
    //         document.body.style.overflow = "hidden";
    //         document.body.style.position = "fixed";
    //         document.body.style.width = "100%";
    //     } else {
    //         // 恢复主页面滚动
    //         document.body.style.overflow = "";
    //         document.body.style.position = "";
    //         document.body.style.width = "";
    //     }

    //     // 组件卸载时恢复滚动
    //     return () => {
    //         document.body.style.overflow = "";
    //         document.body.style.position = "";
    //         document.body.style.width = "";
    //     };
    // }, [visible]);

    return (
        <Popup visible={visible} position="bottom" round onClose={onClose}>
            <View className="product-selection">
                <View className="header">
                    <View className="title">选择商品</View>
                </View>

                <View
                    className="product-list"
                    style={{
                        minHeight: "30vh",
                    }}
                >
                    {productList.map((product) => (
                        <Cell
                            key={product.Id}
                            onClick={(e) => {
                                e.stopPropagation();
                                toggleSelection(product.Id);
                            }}
                            className="product-cell"
                        >
                            <View className="product-item-container">
                                <View className="product-info-left">
                                    <ConfigProvider theme={customTheme}>
                                        <Checkbox checked={product.selected}>
                                            <View className="product-details" style={{ marginLeft: "10px" }}>
                                                <View className="product-name">{product.ItemName}</View>
                                                <View className="product-price">¥{product.Price}</View>
                                            </View>
                                        </Checkbox>
                                    </ConfigProvider>
                                </View>

                                <View className="quantity-control-right">
                                    <ConfigProvider theme={customTheme}>
                                        <InputNumber
                                            min={1}
                                            max={99}
                                            disabled={!product.selected}
                                            value={product.Quantity || 1}
                                            onChange={(val) => handleQuantityChange(product.Id, Number(val))}
                                            onClick={(e) => e.stopPropagation()}
                                        />
                                    </ConfigProvider>
                                </View>
                            </View>
                        </Cell>
                    ))}
                </View>

                <View className="footer">
                    <View className="total-amount">
                        合计：<Text className="amount">¥{getTotalAmount().toFixed(2)}</Text>
                    </View>
                    <Button
                        type="primary"
                        block
                        style={{
                            margin: "0 20rpx",
                        }}
                        disabled={!productList.some((p) => p.selected)}
                        onClick={() => {
                            const result = convertToPayDetails();
                            onConfirm(result);
                        }}
                    >
                        确认选择
                    </Button>
                </View>
            </View>
        </Popup>
    );
};
