import { useState, useEffect } from "react";
import { Current, useDidShow } from "@tarojs/taro";
import Taro from "@tarojs/taro";
import moment from "moment";
import request from "@service/request";
import { tool } from "@/utils/tool";

export const useExamState = (message: any) => {
    const [isInitialized, setIsInitialized] = useState(false);
    const [fieldOpen, setFieldOpen] = useState(false);
    const [fieldList, setFieldList] = useState<any[]>([]);
    const [fieldId, setFieldId] = useState<any>("");
    const [tenantId, setTenantId] = useState<any>("");
    const [showTenantModal, setShowTenantModal] = useState(false);
    const [mainUserInfo, setMainUserInfo] = useState<any>(null);

    const [sfzmmc, setSfzmmc] = useState<any>("A");
    const [sfzmmcList, setSfzmmcList] = useState<any[]>([]);
    const [sfzmmcOpen, setSfzmmcOpen] = useState(false);
    const [xm, setXm] = useState("");
    const [sfzmhm, setSfzmhm] = useState("");
    const [yddh, setYddh] = useState("");
    const [carType, setCarType] = useState<any>("");
    const [carTypeLsit, setCarTypeList] = useState<any[]>([]);
    const [carTypeOpen, setCarTypeOpen] = useState(false);
    const [ksrq, setKsrq] = useState(moment().format("YYYY-MM-DD"));
    const [ksrqOpen, setKsrqOpen] = useState(false);
    const [cc, setCc] = useState("");
    const [jxmc, setJxmc] = useState("");
    const [stepIndex, setStepIndex] = useState(1);
    const [statusBarHeight, setStatusBarHeight] = useState<any>(0);
    const [examList, setExamList] = useState<any[]>([]);
    const [planDetailList, setPlanDetailList] = useState<any[]>([]);
    const [planDetailsOpen, setPlanDetailsOpen] = useState(false);
    const [examInfo, setExamInfo] = useState<any>();
    const [barCodeImage, setBarCodeImage] = useState("");
    const [showBarCode, setShowBarCode] = useState(false);
    const [selfLoginData, setSelfLoginData] = useState<any>();
    const [brightness, setBrightness] = useState(1);

    // Separate initialization function
    const initialize = async () => {
        try {
            console.log("useExamState initialization started");
            await Taro.getScreenBrightness({
                success(res) {
                    console.log("Screen brightness:", res.value);
                    setBrightness(res.value);
                },
            });

            if (tool.data.get("openId") == "o3QMJ0Ve8TxyrOuz9K5thJr0__W0") {
                console.log("Setting test user data");
                setXm("覃萍君");
                setSfzmhm("43012419690611472X");
            }

            // 使用异步方式获取系统信息
            try {
                console.log("Getting system info");
                const systemInfo = await Taro.getSystemInfo();
                console.log("System info:", systemInfo);
                if (systemInfo && systemInfo.statusBarHeight) {
                    setStatusBarHeight(systemInfo.statusBarHeight);
                } else {
                    setStatusBarHeight(20);
                }
            } catch (error) {
                console.error("获取系统信息失败:", error);
                setStatusBarHeight(20);
            }

            console.log("Getting select lists");
            await Promise.all([getSfzmmcSelectList(), getCarTypeSelectList()]);

            console.log("Getting current params");
            const currentParams = Current.router?.params;
            console.log("Current params:", currentParams);

            if (currentParams?.TenantId) {
                console.log("Setting tenant ID:", currentParams.TenantId);
                setTenantId(currentParams.TenantId);
                if (currentParams) {
                    console.log("Getting field list");
                    await getFieldList(currentParams);
                }
            } else {
                console.log("No tenant ID found, showing modal");
                showTenantModalWithAlert();
            }

            try {
                console.log("Getting main user info");
                // 获取主账号绑定信息
                const json = await request.post<any>("/Auth/WxBindUser/getMainUserInfo", {
                    TenantId: currentParams?.TenantId,
                });
                console.log("Main user info response:", json);
                if (json && json.success) {
                    setMainUserInfo(json.data);
                }
            } catch (error) {
                console.error("获取主账号绑定信息失败:", error);
            }

            setIsInitialized(true);
            return true;
        } catch (error) {
            console.error("初始化失败:", error);
            return false;
        }
    };

    // API Calls
    const getSfzmmcSelectList = () => {
        request
            .post<API.SelectResult>("/Base/Select/getSfzmmcSelectList", {})
            .then((json) => {
                if (json && json.success) {
                    setSfzmmcList(json.data);
                } else {
                    message.openDialog("操作失败", "系统故障，稍后重试!");
                }
            })
            .catch(() => {
                message.openDialog("操作失败", "系统故障，稍后重试!");
            });
    };

    const getCarTypeSelectList = () => {
        request
            .post<API.SelectResult>("/Base/Select/getCarTypeSelectList", {})
            .then((json) => {
                if (json && json.success) {
                    setCarTypeList(json.data);
                } else {
                    message.openDialog("操作失败", "系统故障，稍后重试!");
                }
            })
            .catch(() => {
                message.openDialog("操作失败", "系统故障，稍后重试!");
            });
    };

    const getFieldList = (options: any) => {
        getFieldSelectList(options.FieldId, options.TenantId);
    };

    const getFieldSelectList = (fieldId: string, tenantId: string) => {
        request
            .post<API.SelectResult>("/Jx/ExamSite/Wx/Field/getFieldSelectList", {
                FieldId: fieldId,
                TenantId: tenantId,
            })
            .then((json) => {
                if (json.success) {
                    console.log(json.data);
                    setFieldList(json.data);
                    setFieldOpen(true);
                    if (json.data.length == 1) {
                        setTimeout(() => {
                            setFieldOpen(false);
                            setFieldId(json.data[0].value);
                        }, 1000);
                    }
                } else {
                    message.openDialog("操作失败", "请重新扫码重试!");
                }
            });
    };

    // Handlers
    const makeBarCode = () => {
        if (xm.length < 2) {
            message.openDialog("填写错误", "请填写正确的学员姓名!");
        } else if (sfzmhm.length < 5) {
            message.openDialog("填写错误", "请填写正确的证件号码!");
        } else if (yddh.length != 11) {
            message.openDialog("填写错误", "请填写正确的手机号码!");
        } else {
            message.openLoading("正在生成二维码");
            request
                .post<any>("/Jx/ExamSite/Wx/Exam/makeBarCode", {
                    FieldId: fieldId,
                    xm,
                    sfzmmc,
                    sfzmhm,
                    yddh,
                    ksrq: examInfo?.ksrq && moment(examInfo.ksrq) > moment("2000-01-01") ? moment(examInfo.ksrq).format("YYYY-MM-DD") : "1900-01-01",
                    zjcx: carType,
                    cc,
                    jxmc,
                    JxPoliceExamId: examInfo?.Id ? examInfo.Id : undefined,
                })
                .then((json) => {
                    message.closeLoading();
                    if (json && json.success) {
                        Taro.setScreenBrightness({
                            value: 1,
                        });
                        setBarCodeImage("data:image/png;base64," + json.data.image);
                        setShowBarCode(true);
                        setSelfLoginData({
                            ...json.data.data,
                            FieldNickName: json.data.FieldNickName,
                        });
                    } else {
                        message.openDialog("操作失败", json.message);
                    }
                });
        }
    };

    const clearData = () => {
        setExamList([]);
        setExamInfo(undefined);
        setCarType("");
        setKsrq("");
        setCc("");
        setJxmc("");
        setStepIndex(1);
    };

    const handleBarCodeClose = () => {
        Taro.setScreenBrightness({
            value: brightness,
        });
        setShowBarCode(false);
    };

    const handleExamSelect = (item: any) => {
        setExamList([]);
        setExamInfo(item);
        setStepIndex(2);
        setCarType(item.zjcx);
        setKsrq(moment(item.ksrq).format("YYYY-MM-DD"));
        setCc(item.cc);
        setJxmc(item.Jxmc);
    };

    const showTenantModalWithAlert = () => {
        message.Alert("请扫描二维码", "扫码提示", () => {
            setShowTenantModal(true);
        });
    };

    const handleScanCode = () => {
        setShowTenantModal(false);
        Taro.scanCode({
            success: (res) => {
                if (!res.result) {
                    message.error("请扫描正确的二维码!", "扫码错误", () => {
                        showTenantModalWithAlert();
                    });
                } else if (res.scanType == "QR_CODE" && res.result.startsWith("http://weixin.qq.com/q/")) {
                    const url = `Jx/ExamSite/Wx/FiledTicket/${encodeURIComponent(res.result)}`;
                    message.openLoading("正在解析二维码信息");

                    request
                        .post<any>(url)
                        .then((json: any) => {
                            message.closeLoading();
                            if (!json) {
                                message.error("请重新扫码!", "操作失败", () => {
                                    showTenantModalWithAlert();
                                });
                            } else if (json && json.success) {
                                if (json.data.TenantId) {
                                    setTenantId(json.data.TenantId);
                                }
                                if (json.data.FieldId) {
                                    setFieldId(json.data.FieldId);
                                }
                                getFieldSelectList(json.data.FieldId, json.data.TenantId);
                            } else {
                                message.error(json.message || "请重新扫码!", "操作失败", () => {
                                    showTenantModalWithAlert();
                                });
                            }
                        })
                        .catch((err: any) => {
                            console.log("err", err);
                            message.closeLoading();
                            message.error(`请重新扫码:${err.message}!`, "操作失败", () => {
                                showTenantModalWithAlert();
                            });
                        });
                } else {
                    message.error("请扫描正确的二维码!", "扫码错误", () => {
                        showTenantModalWithAlert();
                    });
                }
            },
            fail: () => {
                message.error("请重新扫码!", "扫码失败", () => {
                    showTenantModalWithAlert();
                });
            },
        });
    };

    return {
        state: {
            isInitialized,
            fieldOpen,
            tenantId,
            fieldId,
            sfzmmc,
            sfzmmcOpen,
            xm,
            sfzmhm,
            yddh,
            carType,
            carTypeOpen,
            ksrq,
            ksrqOpen,
            cc,
            jxmc,
            stepIndex,
            statusBarHeight,
            examList,
            planDetailList,
            planDetailsOpen,
            examInfo,
            barCodeImage,
            showBarCode,
            selfLoginData,
            brightness,
            fieldList,
            sfzmmcList,
            carTypeLsit,
            showTenantModal,
            mainUserInfo,
        },
        handlers: {
            initialize,
            setFieldOpen,
            setFieldId,
            setSfzmmc,
            setSfzmmcOpen,
            setXm,
            setSfzmhm,
            setYddh,
            setCarType,
            setCarTypeOpen,
            setKsrq,
            setKsrqOpen,
            setCc,
            setJxmc,
            setStepIndex,
            setExamList,
            setPlanDetailList,
            setPlanDetailsOpen,
            setExamInfo,
            makeBarCode,
            clearData,
            handleBarCodeClose,
            handleExamSelect,
            setShowTenantModal,
            showTenantModalWithAlert,
            handleScanCode,
        },
    };
};
