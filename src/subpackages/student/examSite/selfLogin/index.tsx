import "./styles/common.scss";
import "@utils/app.scss";
import { ConfigProvider } from "@nutui/nutui-react-taro";
import Layout from "@components/Layout";
import { RegistrationForm } from "./components/RegistrationForm";
import { ExamSelection } from "./components/ExamSelection";
import { BarCodePopup } from "./components/BarCodePopup";
import { PlanDetailsPopup } from "./components/PlanDetailsPopup";
import { PickerGroup } from "./components/PickerGroup";
import { useExamState } from "./hooks/useExamState";
import { message } from "@components/MessageApi/MessageApiSingleton";
import { PaymentPopup } from "./components/PaymentPopup";
import { useEffect, useState } from "react";
import { PayDetailInPut, ProductSelectionPopup } from "./components/ProductSelectionPopup";
import request from "@service/request";
import OfficialAccount from "@components/official-account";
import { TenantWarningModal } from "./components/TenantWarningModal";
import { PlanDetail } from "./components/PlanDetailsPopup";
import Taro, { useReady } from "@tarojs/taro";
import { login } from "@utils/login";
import { CoachActionSheet } from "./components/CoachActionSheet";
import { tool } from "@/utils/tool";
import { Close } from "@nutui/icons-react-taro";

// Loading Screen Component
const LoadingScreen = () => (
    <view
        style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            background: "#fff",
            zIndex: 9999,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
        }}
    >
        <view className="custom-spinner" />
        <view style={{ fontSize: "32rpx", color: "#333", marginTop: "30rpx" }}>正在登录...</view>
    </view>
);

const App = () => {
    // Add loading state
    const [isInitializing, setIsInitializing] = useState(true);

    // 1. State Management
    const { state, handlers } = useExamState(message);
    console.log("Initial state:", state);

    // UI State
    const [showProductSelect, setShowProductSelect] = useState(false);
    const [showTimeSelect, setShowTimeSelect] = useState(false);
    const [showPayment, setShowPayment] = useState(false);
    const [showCoachActionSheet, setShowCoachActionSheet] = useState(false);
    const [tenantName, setTenantName] = useState("");
    const [showPaymentWaiting, setShowPaymentWaiting] = useState(false);
    const [syncLoading, setSyncLoading] = useState(false);
    const [orderId, setOrderId] = useState<string>("");
    const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

    // Loading States
    const [paymentLoading, setPaymentLoading] = useState(false);
    const [paymentLoadingText, setPaymentLoadingText] = useState("");
    const [scanPayLoading, setScanPayLoading] = useState(false);
    const [confirmLoading, setConfirmLoading] = useState(false);

    // Data States
    const [selectedProducts, setSelectedProducts] = useState<PayDetailInPut[]>([]);
    const [totalAmount, setTotalAmount] = useState(0);
    const [planDetailList, setPlanDetailList] = useState<any[]>([]);
    const [products, setProducts] = useState<any[]>([]);
    const [carModelId, setCarModelId] = useState("");
    const [planDetailId, setPlanDetailId] = useState("");
    const [latitude, setLatitude] = useState("");
    const [longitude, setLongitude] = useState("");

    // 监听 tenantId 变化
    useEffect(() => {
        console.log("tenantId changed:", state.tenantId);
        if (state.tenantId) {
            GetTenantName(state.tenantId);
        }
    }, [state.tenantId]);

    // 2. Initial Data Loading
    useReady(async () => {
        try {
            setIsInitializing(true);
            console.log("useReady started, current state:", state);

            // First get OpenId
            await login();

            // Then initialize exam state
            const initSuccess = await handlers.initialize();
            if (!initSuccess) {
                message.error("初始化失败，请重试");
                return;
            }

            debugger; // 断点1: 进入useReady初始化
            console.error("       ====================       >>>>>>>>>>>>            useReady");
            const pages = Taro.getCurrentPages();
            debugger; // 断点2: 获取当前页面信息
            const currentPage = pages[pages.length - 1];
            console.log("Current page:", currentPage);
            const currentPath = `/subpackages/student/examSite/selfLogin/index`;
            let fullPath = currentPath;

            if (currentPage && currentPage.options) {
                debugger; // 断点3: 处理页面参数
                const options = currentPage.options;
                console.log("Page options:", options);
                const queryParams = Object.keys(options)
                    .map((key) => `${key}=${options[key]}`)
                    .join("&");
                if (queryParams) {
                    fullPath = `${currentPath}?${queryParams}`;
                }
            }

            debugger; // 断点4: 存储页面信息
            // Store data in tool.data.set
            tool.data.set("longtime-lastScan-pageAddress", fullPath);
            tool.data.set("longtime-lastScan-pageParams", currentPage?.options || {});

            console.log("Current state in useReady:", state);
            console.log("state.tenantId:", state.tenantId);
        } catch (error) {
            debugger; // 断点7: 错误处理
            console.error("       ====================       >>>>>>>>>>>>            初始化失败:", error);
            message.error("初始化失败，请重试");
        } finally {
            // Add a small delay to ensure smooth transition
            setTimeout(() => {
                setIsInitializing(false);
            }, 1000);
        }
    });

    // 3. Location Services
    useEffect(() => {
        console.error("       ====================       >>>>>>>>>>>>            useEffect");
        const getLocation = async () => {
            try {
                const setting = await Taro.getSetting();
                if (!setting.authSetting["scope.userLocation"]) {
                    const auth = await Taro.authorize({
                        scope: "scope.userLocation",
                    });
                    if (!auth) {
                        message.error("需要位置权限才能继续");
                        return;
                    }
                }

                const location = await Taro.getLocation({
                    type: "gcj02",
                    isHighAccuracy: true,
                    highAccuracyExpireTime: 3000,
                });

                setLatitude(location.latitude.toString());
                setLongitude(location.longitude.toString());
            } catch (error) {
                console.error("获取位置失败:", error);
                message.error("获取位置信息失败");
            }
        };

        getLocation();
    }, []);

    // 4.2 Product Selection Related
    const showProducts = (data: any[]) => {
        setProducts(data);
        setShowProductSelect(true);
    };

    const handleProductConfirm = (payDetails: PayDetailInPut[]) => {
        if (!Array.isArray(payDetails)) {
            console.error("payDetails is not an array:", payDetails);
            message.error("数据格式错误");
            return;
        }

        setSelectedProducts(payDetails);
        setShowProductSelect(false);

        const total = payDetails.reduce((sum, item) => {
            const price = Number(item.Price) || 0;
            const count = Number(item.Count) || 0;
            return sum + price * count;
        }, 0);

        setTotalAmount(total);

        if (!state.ksrq || state.ksrq == "") {
            setPaymentLoading(false);
            setShowPayment(true);
        } else {
            fetchPlanDetails();
        }
    };

    // 4.3 Plan Details Related
    const fetchPlanDetails = async () => {
        try {
            message.openLoading("正在获取预约场次");
            setPlanDetailList([]);
            const json = await request.post<any>("/Jx/ExamSite/Wx/Plan/getPlanDetailList", {
                FieldId: state.fieldId,
                xm: state.xm,
                sfzmmc: state.sfzmmc,
                sfzmhm: state.sfzmhm,
                ksrq: state.ksrq,
                yddh: state.yddh,
                CarType: state.carType,
                cc: state.cc,
            });

            message.closeLoading();
            if (!json.success) {
                message.error(json.message);
            } else if (json.data.length == 0 || json.data[0]?.length == 0) {
                setPaymentLoading(false);
                setShowPayment(true);
            } else {
                setShowTimeSelect(true);
                setPlanDetailList(json.data);
                console.log("获取到预约场次:", json.data);
            }
        } catch (error) {
            message.error("获取场次信息失败，请重试");
        }
    };

    const handleTimeConfirm = (plan: PlanDetail) => {
        setPlanDetailId(plan.Id);
        setCarModelId(plan.CarModelId);
        setPaymentLoading(false);
        setShowPayment(true);
    };

    // 4.4 Payment Related
    const handlePayment = async () => {
        setConfirmLoading(true);
        setPaymentLoadingText("正在提交订单");

        try {
            const json = await request.put<any>("/Jx/ExamSite/Wx/Pay/createOrder", {
                FieldId: state.fieldId,
                Jxmc: state.jxmc,
                xm: state.xm,
                sfzmmc: state.sfzmmc,
                sfzmhm: state.sfzmhm,
                ksrq: state.ksrq,
                yddh: state.yddh,
                CarType: state.carType,
                cc: state.cc,
                PayDetails: selectedProducts,
                CarModelId: carModelId || undefined,
                PlanDetailId: planDetailId || undefined,
                PayMoney: totalAmount,
                ExamId: state.examInfo?.Id,
                latitude: latitude || undefined,
                longitude: longitude || undefined,
            });

            message.closeLoading();
            if (json.success) {
                setConfirmLoading(false);
                if (json.data.SignData) {
                    handleWechatPay(json.data.SignData, json.data.ReturnUrl);
                } else if (json.data.JlPayAppId && json.data.CashierUrl) {
                    handleMiniProgramPay(json.data.JlPayAppId, json.data.CashierUrl);
                }
            } else {
                message.error(json.message);
                setConfirmLoading(false);
            }
        } catch (error) {
            message.error("操作失败", "系统故障，稍后重试!");
            setConfirmLoading(false);
        }
    };

    const handleWechatPay = (signData: any, returnUrl: string) => {
        const { nonceStr, package: prepayId, paySign, signType, timeStamp } = signData;
        Taro.requestPayment({
            timeStamp,
            nonceStr,
            package: prepayId,
            signType,
            paySign,
            success: () => {
                message.success("支付成功");
                Taro.navigateTo({ url: returnUrl });
            },
            fail: () => {
                message.error("支付失败，请重试");
                setConfirmLoading(false);
            },
        });
    };

    const handleMiniProgramPay = (appId: string, cashierUrl: string) => {
        Taro.navigateToMiniProgram({
            appId,
            path: cashierUrl,
            success: () => console.log("跳转支付小程序成功"),
            fail: () => {
                message.error("跳转支付页面失败，请重试");
                setConfirmLoading(false);
            },
        });
    };

    const handleScanPay = () => {
        setScanPayLoading(true);
        Taro.scanCode({
            scanType: ["barCode", "qrCode"],
            success: async (res) => {
                console.log("扫码成功", res);
                console.log("二维码", res.result);

                // Validate QR code format
                const qrCode = res.result;

                // qrCode = "130676229114092997";

                // 验证支付码是否为纯数字
                const isValidPaymentCode = /^\d+$/.test(qrCode);

                if (!isValidPaymentCode) {
                    Taro.showToast({
                        title: "请扫描正确的微信或支付宝支付码",
                        icon: "none",
                    });
                    return;
                }

                try {
                    message.openLoading("正在提交订单");

                    console.log("qrCode:" + qrCode);
                    const json = await request.put<any>("/Jx/ExamSite/Wx/Pay/createOrder", {
                        FieldId: state.fieldId,
                        Jxmc: state.jxmc,
                        xm: state.xm,
                        sfzmmc: state.sfzmmc,
                        sfzmhm: state.sfzmhm,
                        ksrq: state.ksrq,
                        yddh: state.yddh,
                        CarType: state.carType,
                        cc: state.cc,
                        PayDetails: selectedProducts,
                        CarModelId: carModelId || undefined,
                        PlanDetailId: planDetailId || undefined,
                        PayMoney: totalAmount,
                        ExamId: state.examInfo?.Id,
                        latitude: latitude || undefined,
                        longitude: longitude || undefined,
                        barCode: qrCode,
                    });

                    message.closeLoading();

                    if (json.success) {
                        setShowPaymentWaiting(true);
                        setShowPayment(false);
                        setOrderId(json.data?.OrderId || "");
                    } else {
                        message.error(json.message || "扫码支付失败");
                    }
                } catch (error) {
                    message.error("扫码支付失败，请重试");
                }
            },
            complete: () => setScanPayLoading(false),
        });
    };

    const handleSyncOrderStatus = async () => {
        if (!orderId) {
            message.error("订单ID不存在");
            return;
        }
        setSyncLoading(true);
        try {
            const statusResponse = await request.post<any>(`/Jx/ExamSite/Wx/PayResult/getResult`, {
                Id: orderId,
            });
            if (statusResponse.success && statusResponse.data) {
                message.success("支付成功");
                setShowPaymentWaiting(false);

                Taro.navigateTo({
                    url: `/subpackages/student/examSite/selfLogin/success/index?Id=${statusResponse.data.Id}&TenantId=${statusResponse.data.TenantId}`,
                });
            }
        } catch (error) {
            console.error("检查支付状态失败:", error);
            message.error("同步订单状态失败");
        } finally {
            setSyncLoading(false);
        }
    };

    // 4.5 Tenant Related
    const GetTenantName = (tenantId: string) => {
        request
            .post<any>("/Wx/Sys/getTenantName", {
                Id: tenantId,
            })
            .then((json) => {
                if (json && json.success) {
                    const tenantName = json.data.data;
                    setTenantName(tenantName);
                    // Store tenant name in tool.data.set
                    tool.data.set("longtime-lastScan-tenantName", tenantName);
                } else {
                    message.error("获取驾校名称失败");
                }
            })
            .catch(() => {
                message.error("获取驾校名称失败");
            });
    };

    // Add useEffect for polling
    useEffect(() => {
        if (showPaymentWaiting && orderId) {
            // Start polling when payment waiting is shown
            const interval = setInterval(() => {
                handleSyncOrderStatus();
            }, 5000); // Poll every 5 seconds

            setPollingInterval(interval);

            // Cleanup interval when component unmounts or payment waiting is closed
            return () => {
                if (interval) {
                    clearInterval(interval);
                }
            };
        }
    }, [showPaymentWaiting, orderId]);

    // Modify the close handler to clear interval
    const handleClosePaymentWaiting = () => {
        if (pollingInterval) {
            clearInterval(pollingInterval);
            setPollingInterval(null);
        }
        setShowPaymentWaiting(false);
    };

    // 5. Render UI
    return (
        <Layout>
            <ConfigProvider>
                {isInitializing && <LoadingScreen />}

                {!isInitializing && state.isInitialized && (
                    <>
                        <RegistrationForm state={state} handlers={handlers} onShowProducts={showProducts} message={message} statusBarHeight={state.statusBarHeight} />

                        <ExamSelection examList={state.examList} onExamSelect={handlers.handleExamSelect} onClose={() => handlers.setExamList([])} />

                        {state.selfLoginData && state.showBarCode && (
                            <BarCodePopup
                                selfLoginData={state.selfLoginData}
                                barCodeImage={state.barCodeImage}
                                showBarCode={state.showBarCode}
                                brightness={state.brightness}
                                onClose={handlers.handleBarCodeClose}
                            />
                        )}

                        <PickerGroup state={state} handlers={handlers} fieldList={state.fieldList} sfzmmcList={state.sfzmmcList} carTypeLsit={state.carTypeLsit} />

                        <ProductSelectionPopup visible={showProductSelect} products={products} onClose={() => setShowProductSelect(false)} onConfirm={handleProductConfirm} />

                        <PlanDetailsPopup
                            planDetailList={planDetailList}
                            planDetailsOpen={showTimeSelect}
                            onClose={() => {
                                setShowTimeSelect(false);
                                setPlanDetailList([]);
                            }}
                            onSelect={handleTimeConfirm}
                        />

                        <PaymentPopup
                            visible={showPayment}
                            amount={totalAmount}
                            scanPayLoading={scanPayLoading}
                            confirmLoading={confirmLoading}
                            loadingText={paymentLoadingText}
                            onClose={() => setShowPayment(false)}
                            onConfirm={handlePayment}
                            onScanPay={handleScanPay}
                        />

                        <TenantWarningModal visible={state.showTenantModal} onClose={() => handlers.setShowTenantModal(false)} onScan={handlers.handleScanCode} />

                        <view
                            className="footer"
                            style={{
                                fontSize: "24rpx",
                                backgroundColor: "transparent",
                                paddingBottom: "env(safe-area-inset-bottom)",
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                                gap: "10rpx",
                            }}
                        >
                            {state.mainUserInfo?.Id && (
                                <a
                                    href="javascript:void(0)"
                                    onClick={() => Taro.navigateTo({ url: "/subpackages/student/examSite/coupon/index?TenantId=" + state.tenantId })}
                                    style={{
                                        color: "#1989fa",
                                        textDecoration: "none",
                                    }}
                                >
                                    我的返利 / 积分
                                </a>
                            )}
                            {!state.mainUserInfo?.Id && (
                                <a
                                    href="javascript:void(0)"
                                    onClick={() => setShowCoachActionSheet(true)}
                                    style={{
                                        color: "#1989fa",
                                        textDecoration: "none",
                                    }}
                                >
                                    教练身份证注册 / 绑定
                                </a>
                            )}
                            {/* {<text style={{ color: "#666", marginTop: "10rpx" }}>{tenantName}</text>} */}
                        </view>

                        <CoachActionSheet visible={showCoachActionSheet} onClose={() => setShowCoachActionSheet(false)} TenantId={state.tenantId} />

                        {showPaymentWaiting && (
                            <view
                                style={{
                                    position: "fixed",
                                    top: 0,
                                    left: 0,
                                    width: "100vw",
                                    height: "100vh",
                                    background: "rgba(0,0,0,0.4)",
                                    zIndex: 9999,
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}
                            >
                                <view
                                    style={{
                                        position: "relative",
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        minWidth: "400rpx",
                                        background: "#fff",
                                        borderRadius: "16rpx",
                                        padding: "90rpx 64rpx 50rpx 64rpx",
                                    }}
                                >
                                    <view
                                        style={{
                                            position: "absolute",
                                            top: "30rpx",
                                            right: "10rpx",
                                            width: "40rpx",
                                            height: "40rpx",
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "center",
                                            cursor: "pointer",
                                            color: "#999",
                                        }}
                                        onClick={handleClosePaymentWaiting}
                                    >
                                        <view className="custom-modal-close" onClick={handleClosePaymentWaiting}>
                                            <Close />
                                        </view>
                                    </view>
                                    <view className="custom-spinner" />
                                    <view style={{ fontSize: "32rpx", color: "#333", marginTop: "62rpx" }}>正在等待学员完成支付...</view>
                                    <view style={{ fontSize: "24rpx", color: "#999", marginTop: "20rpx", textAlign: "center" }}>请勿关闭或离开当前页面，以免影响支付结果</view>
                                </view>
                            </view>
                        )}
                    </>
                )}
            </ConfigProvider>

            <OfficialAccount
                onLoad={() => console.log("OfficialAccount loaded")}
                onError={(err: any) => {
                    if (err?.detail?.errMsg === "创建多次" || err?.detail?.status === 6) {
                        console.log("OfficialAccount already exists, ignoring duplicate creation error");
                        return;
                    }
                    console.log("OfficialAccount error", err);
                }}
            />
        </Layout>
    );
};

export default App;
