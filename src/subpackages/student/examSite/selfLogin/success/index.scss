page {
    background: #f7f8fa;
    font-family: <PERSON>, <PERSON><PERSON>, STYuanti-SC-Regular;
    height: auto;
}

// Loading 动画样式
.loading-page {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    // background: #fff;
}

.loading-content {
    text-align: center;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.loading-wrapper {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.loading-circle {
    width: 12px;
    height: 12px;
    margin: 0 6px;
    background: #07c160;
    border-radius: 50%;
    animation: bounce 0.2s infinite alternate;

    &:nth-child(2) {
        animation-delay: 0.2s;
    }

    &:nth-child(3) {
        animation-delay: 0.4s;
    }
}

@keyframes bounce {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-10px);
    }
}

.loading-text {
    font-size: 32px;
    color: #333;
    margin-bottom: 12px;
}

.loading-tips {
    font-size: 24px;
    color: #999;
}

// 支付结果页样式
.payment-result {
    margin-top: 1vh;
    padding: 32px;
    padding-top: 0;
    opacity: 0;
    transform: translateY(100%); // 改为从底部开始
    transition: all 0s ease-out;

    &.animate-in {
        opacity: 1;
        transform: translateY(0); // 移动到原始位置
    }
}

.status-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 70px;

    .status-iconfont-wrapper {
        margin-bottom: 16px;
    }

    .status-text {
        font-size: 36px;
        font-weight: 500;
        color: #333;
        margin-bottom: 16px;
    }

    .amount {
        margin-top: 10px;
        font-size: 48px;
        font-weight: bold;
        color: #333;
    }
}

.course-card {
    background: #fff;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .course-info {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;
        min-height: 40px;

        .label {
            color: #666;
            font-size: 28px;
            line-height: 40px;
        }

        .value {
            color: #333;
            font-size: 28px;
            font-weight: 500;
            line-height: 40px;
            text-align: right;
        }
    }

    .divider {
        height: 1px;
        background: #eee;
        margin: 16px 0;
    }

    .order-info {
        .pay-time {
            color: #999;
            font-size: 24px;
        }
    }
}

.vouchers-section {
    background: #fff;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 24px;

        .title {
            margin-left: 8px;
            font-size: 32px;
            font-weight: 500;
            color: #333;
        }
    }
}

.voucher-card {
    border: 1px solid #eee;
    border-radius: 12px;
    margin-bottom: 16px;
    overflow: hidden;

    .voucher-header {
        padding: 20px;
        background: #f8f8f8;

        .voucher-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .voucher-name {
                font-size: 32px;
                font-weight: 500;
                color: #333;
            }
        }

        .voucher-code {
            font-size: 24px;
            color: #666;
            margin-bottom: 8px;
            display: block;
        }

        .voucher-validity {
            font-size: 24px;
            color: #999;
            display: block;
        }
    }

    .voucher-qrcode {
        padding: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #fff;
        cursor: pointer;

        .qrcode-tip {
            color: #07c160;
            font-size: 28px;
        }
    }
}

// 二维码弹窗样式
.qr-modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.qr-modal-content {
    width: 90vw;
    // min-height: 80vh;
    background: #fff;
    border-radius: 16px;
    overflow: hidden;
    position: relative;
    // padding-bottom: 40px; // 添加底部间距

    &::before {
        content: "";
        position: absolute;
        left: -20px;
        top: 50%;
        width: 40px;
        height: 40px;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 50%;
        transform: translateY(-50%);
    }

    &::after {
        content: "";
        position: absolute;
        right: -20px;
        top: 50%;
        width: 40px;
        height: 40px;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 50%;
        transform: translateY(-50%);
    }
}
.qr-header {
    padding: 24px;
    text-align: center;
    background: #f8f8f8;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .qr-title {
        font-size: 32px;
        font-weight: 500;
        color: #333;
    }

    .invoice-button {
        position: absolute;
        right: 24px;
        top: 50%;
        transform: translateY(-50%);
        background: #07c160;
        color: #fff;
        font-size: 24px;
        padding: 12px 20px;
        border-radius: 20px;
        border: none;
        height: auto;
        line-height: normal;
        min-height: auto;

        &::after {
            border: none;
        }
    }
}

.qr-body {
    padding: 40px;
    display: flex;
    flex-direction: column;
    // min-height: calc(80vh - 81px);

    .qr-code-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-bottom: 32px;
        flex: 1;

        .qr-id {
            margin-top: 24px;
            font-size: 20rpx;
            color: #cccccc;
            font-family: monospace;
        }
    }

    .qr-info {
        margin-bottom: 40px;

        .info-item {
            display: block;
            text-align: center;
            font-size: 28px;
            color: #666;
            margin-bottom: 12px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }

    .qr-buttons {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 24px;
        gap: 16px;

        .print-button,
        .close-button,
        .share-button {
            width: 100%;
            height: 88px;
            line-height: normal;
            text-align: center;
            border-radius: 44px;
            font-size: 32px;
            font-weight: 500;
        }

        .print-button {
            background: #07c160;
            color: #fff;
        }

        .close-button {
            background: #f5f5f5;
            color: #666;
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;

            &::after {
                border: none;
            }

            &:active {
                opacity: 0.8;
                transform: translateY(1px);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            }

            &:hover {
                border-color: #d0d0d0;
                background: #f0f0f0;
            }
        }

        .share-button {
            background: #07c160;
            color: #fff;
        }

        .share-tip {
            font-size: 24px;
            color: #999;
            text-align: center;
            margin-top: 8px;
        }
    }

    .ad-container {
        width: 100%;
        margin-top: 24px;
        padding: 0 24px;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
        background: #f8f8f8;
        border-radius: 12px;
    }
}

button[openType="share"] {
    background: none;
    padding: 0;
    margin: 0;
    line-height: normal;
    border: none;
    outline: none;
}
.qr-number {
    // font-family: monospace;
    font-size: 40px;
    font-weight: bold;
    margin-bottom: 5px; /* 调整与车型之间的间距 */
}

.qr-cartype {
    font-size: 30rpx;
    margin-bottom: 10px; /* 调整与二维码之间的间距 */
}

/* 如果需要让序号和车型水平居中 */
.qr-code-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

// 测试蓝牙按钮
.bluetooth-test-button {
    width: 100%;
    height: 88px;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: #fff;
    font-size: 32px;
    border: none;
    border-radius: 44px;
    margin: 24px 0;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);

    &::after {
        border: none;
    }

    &:active {
        opacity: 0.8;
        transform: translateY(2px);
        box-shadow: 0 2px 6px rgba(37, 99, 235, 0.2);
    }
}

// 蓝牙设备列表模态框样式
.bluetooth-modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.bluetooth-modal-content {
    width: 90vw;
    max-height: 85vh;
    background: #f7f8fa;
    border-radius: 16px;
    overflow: hidden;
    position: relative;
}

.bluetooth-header {
    padding: 24px;
    text-align: center;
    background: #f8f8f8;
    border-bottom: 1px solid #eee;

    .bluetooth-title {
        font-size: 32px;
        font-weight: 500;
        color: #333;
    }
}

.bluetooth-body {
    padding: 24px;
    display: flex;
    flex-direction: column;
    max-height: calc(85vh - 81px);
    overflow-y: auto;
    background-color: #f7f8fa;

    .bluetooth-scanning {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 0;

        .loading-wrapper {
            margin-bottom: 20px;
        }

        .scanning-text {
            font-size: 28px;
            color: #666;
        }
    }

    .bluetooth-devices-list {
        margin-bottom: 24px;
        max-height: 60vh;
        overflow-y: auto;
        padding: 0 8px;

        .bluetooth-device-item {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background-color: #fff;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

            &:last-child {
                border-bottom: none;
            }

            .device-info {
                display: flex;
                flex-direction: column;
                margin-bottom: 16px;

                .device-name {
                    font-size: 28px;
                    font-weight: 500;
                    color: #333;
                    margin-bottom: 8px;
                }

                .device-id {
                    font-size: 24px;
                    color: #666;
                    font-family: monospace;
                    margin-bottom: 8px;
                }

                .device-attr {
                    font-size: 24px;
                    color: #666;
                    margin-bottom: 4px;
                    word-break: break-all;
                    line-height: 1.4;
                }
            }

            .device-raw {
                margin-top: 16px;
                padding-top: 16px;
                border-top: 1px dashed #eee;

                .device-raw-title {
                    font-size: 24px;
                    font-weight: 500;
                    color: #333;
                    margin-bottom: 8px;
                    display: flex;
                    align-items: center;

                    &::after {
                        content: "";
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23666"><path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/></svg>');
                        background-size: contain;
                        margin-left: 8px;
                    }
                }

                .device-raw-data {
                    font-size: 22px;
                    color: #666;
                    font-family: monospace;
                    white-space: pre-wrap;
                    word-break: break-all;
                    background-color: #f5f5f5;
                    padding: 12px;
                    border-radius: 8px;
                    display: block;
                    line-height: 1.4;
                    position: relative;
                    user-select: text;

                    &::after {
                        content: "";
                        position: absolute;
                        top: 0;
                        right: 0;
                        bottom: 0;
                        left: 0;
                        background: rgba(0, 0, 0, 0.03);
                        pointer-events: none;
                        opacity: 0;
                        transition: opacity 0.2s;
                    }

                    &:active::after {
                        opacity: 1;
                    }
                }
            }
        }

        .no-devices {
            padding: 40px 0;
            text-align: center;
            color: #999;
            font-size: 28px;
        }
    }

    .bluetooth-buttons {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-top: auto;
        padding-top: 16px;
        border-top: 1px solid #eee;

        .scan-button {
            width: 100%;
            height: 88px;
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: #fff;
            font-size: 32px;
            border: none;
            border-radius: 44px;

            &::after {
                border: none;
            }

            &:active {
                opacity: 0.8;
            }

            &[disabled] {
                background: #ccc;
                opacity: 0.7;
            }
        }

        .close-button {
            width: 100%;
            height: 88px;
            background: #f5f5f5;
            color: #666;
            font-size: 32px;
            border: 2rpx;
            border-radius: 44px;

            &::after {
                border: none;
            }

            &:active {
                opacity: 0.8;
            }
        }
    }
}
