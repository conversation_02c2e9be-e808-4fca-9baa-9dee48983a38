// index.tsx
import { Component } from "react";
import Taro, { getCurrentInstance } from "@tarojs/taro";
import { Text, Button, Video, Ad } from "@tarojs/components";
// import { AtIcon } from "taro-ui"; // Removing since module not found
import { QRCode } from "taro-code";

import "./index.scss";
import IconFont from "@components/iconfont";
import request from "@service/request";
import Layout from "@components/Layout";
import { message } from "@components/MessageApi/MessageApiSingleton";
import moment from "moment";
import BluetoothPrinter from "@/utils/bluetoothPrinter";
import TopNavBar from "@/components/topNavBar";

interface SaleInfo {
    Id: string;
    TenantId: string;
    ItemName: string;
    CanUseEndTime: string;
    UseTime: string;
    SysId: string;
    CarType?: string;
    BrandName?: string;
    ModelName?: string;
    SaleOrderNum: number;
}

interface IState {
    orderInfo: {
        Id: any;
        PayMoney: number;
        FieldName: string;
        xm: string;
        sfzmhm: string;
        PayTime: string;
        Sales: SaleInfo[];
        ksrq: string;
        PlanDetailNickName: string;
        StartTime: string;
        CarType: string;
        BrandName: string;
        ModelName: string;
        TenantId: string;
        FieldId: string;
        CanInvoice: boolean;
    };
    loading: boolean;
    animate: boolean;
    id: any;
    tenantId: any;
    saleId: any;
    isQRCodeModalVisible: boolean;
    currentSale?: SaleInfo;
    isBluetoothModalVisible: boolean;
    bluetoothDevices: any[];
    scanningBluetooth: boolean;
}

export default class PaymentResult extends Component<{}, IState> {
    constructor(props: any) {
        super(props);
        this.state = {
            orderInfo: {
                Id: undefined,
                PayMoney: 0,
                FieldName: "",
                xm: "",
                sfzmhm: "",
                PayTime: "",
                ksrq: "",
                Sales: [],
                PlanDetailNickName: "",
                StartTime: "",
                CarType: "",
                BrandName: "",
                ModelName: "",
                TenantId: "",
                FieldId: "",
                CanInvoice: false,
            },
            loading: true,
            animate: false,
            id: undefined,
            tenantId: undefined,
            saleId: undefined,
            isQRCodeModalVisible: false,
            isBluetoothModalVisible: false,
            bluetoothDevices: [],
            scanningBluetooth: false,
        };
    }

    componentDidMount() {
        const router = getCurrentInstance().router;
        const SaleId = router?.params?.SaleId;
        const Id = router?.params?.Id;
        const TenantId = router?.params?.TenantId;

        if (Id) {
            this.setState({
                id: Id,
                saleId: SaleId,
            });
            if (TenantId != undefined) {
                this.setState({
                    tenantId: TenantId,
                });
            }
            this.fetchOrderInfo(Id, TenantId);
            setTimeout(() => {
                this.setState({ animate: true });
            }, 100);
        }
    }

    fetchOrderInfo = async (id: string, tenantId: any) => {
        try {
            const response = await request.post<any>("/Jx/ExamSite/Wx/Pay/getOrder", {
                Id: id,
                TenantId: tenantId,
            });

            if (response.success) {
                const { saleId } = this.state;
                const sales = response.data.Sales || [];
                let currentSale: SaleInfo | undefined = undefined;

                if (saleId) {
                    // If saleId exists, find the matching sale
                    currentSale = sales.find((sale: SaleInfo) => sale.Id === saleId);
                } else if (sales.length > 0) {
                    // If no saleId, use the first sale
                    currentSale = sales[0];
                }

                this.setState({
                    tenantId: response.data.TenantId,
                    orderInfo: response.data,
                    loading: false,
                    isQRCodeModalVisible: !!currentSale,
                    currentSale: currentSale,
                });
            } else {
                message.error(response.message, "参数错误", () => {
                    Taro.navigateBack({
                        delta: 1,
                    });
                });
            }
        } catch (error) {
            message.error("获取订单信息失败");
        }
    };

    handleShowQRCode = (sale: SaleInfo) => {
        this.setState({
            isQRCodeModalVisible: true,
            currentSale: sale,
        });
    };

    handleCloseQRCode = () => {
        this.setState({
            isQRCodeModalVisible: false,
            currentSale: undefined,
        });
    };

    handleInvoiceApplication = () => {
        const { orderInfo } = this.state;

        // 先选择发票抬头
        Taro.chooseInvoiceTitle({
            success: (res) => {
                console.log("选择的发票抬头信息:", res);
                // 这里可以根据需要添加开票申请的逻辑
                // 比如跳转到开票申请页面或显示开票申请表单
                console.log("开票申请", orderInfo);
                message.success("发票抬头选择成功，开票申请功能开发中");
            },
            fail: (err) => {
                console.error("选择发票抬头失败:", err);
                message.error("选择发票抬头失败，请重试");
            },
        });
    };

    handleShareToCoach = () => {
        const { currentSale } = this.state;
        if (currentSale) {
            Taro.showShareMenu({
                withShareTicket: true,
                success: () => {
                    message.success("分享功能已开启");
                },
                fail: (err) => {
                    console.error("开启分享失败:", err);
                    message.error("开启分享失败，请重试");
                },
            });
        } else {
            message.error("没有可分享的电子券信息");
        }
    };

    handlePrintQRCode = () => {
        const { currentSale } = this.state;
        if (currentSale) {
            // 准备打印数据
            const printData = {
                qrCodeUrl: `https://51jx.cc/Self/ExamSite/Sale?Id=${currentSale.Id}`,
                saleId: currentSale.Id,
                sysId: currentSale.SysId,
                itemName: currentSale.ItemName,
                orderNum: currentSale.SaleOrderNum,
            };

            // 调用蓝牙打印工具类打印二维码
            BluetoothPrinter.printQRCode(
                printData,
                () => {
                    console.log("打印成功");
                },
                (error) => {
                    console.error("打印失败:", error);
                    // 错误已由BluetoothPrinter内部处理，这里只需要记录日志
                }
            );
        } else {
            message.error("没有可打印的电子券信息");
        }
    };

    // 显示蓝牙设备列表模态框
    handleShowBluetoothModal = () => {
        this.setState({
            isBluetoothModalVisible: true,
            bluetoothDevices: [],
            scanningBluetooth: false,
        });
    };

    // 关闭蓝牙设备列表模态框
    handleCloseBluetoothModal = () => {
        this.setState({
            isBluetoothModalVisible: false,
        });
        // 停止搜索蓝牙设备
        Taro.stopBluetoothDevicesDiscovery();
    };

    onShareAppMessage() {
        const { orderInfo, currentSale } = this.state;
        if (currentSale) {
            return {
                title: `${orderInfo.xm}-${currentSale.SysId}`,
                path: `/subpackages/student/examSite/selfLogin/share/index?Id=${currentSale.Id}&TenantId=${currentSale.TenantId}`,
                imageUrl: "https://cdn.51panda.com/wx/share-sale-info.png",
            };
        } else {
            message.error("没有可分享的电子券信息");
        }
    }

    render() {
        const { orderInfo, loading, animate, id, isQRCodeModalVisible, currentSale } = this.state;

        if (loading) {
            return (
                <Layout>
                    <view className="loading-page">
                        <view className="loading-content">
                            <view className="loading-container">
                                <view className="loading-wrapper">
                                    <view className="loading-circle"></view>
                                    <view className="loading-circle"></view>
                                    <view className="loading-circle"></view>
                                </view>
                                <Text className="loading-text">正在加载订单信息...</Text>
                                <Text className="loading-tips">请稍候，马上就好</Text>
                            </view>
                        </view>
                    </view>
                    <view className="footer" style={{ fontSize: "18rpx", backgroundColor: "transparent" }}>
                        {id?.toUpperCase()}
                    </view>
                </Layout>
            );
        }

        return (
            <Layout>
                <TopNavBar
                    title=""
                    homeClick={() => {
                        Taro.redirectTo({
                            url: `/subpackages/student/examSite/selfLogin/index?TenantId=${orderInfo?.TenantId}&FieldId=${orderInfo?.FieldId}`,
                        });
                    }}
                    BgColor="transparent"
                    FontColor="#666"
                    leftClick={undefined}
                />

                <view className={`payment-result ${animate ? "animate-in" : ""}`}>
                    <view className="status-header">
                        <view className="status-iconfont-wrapper">
                            <IconFont name={moment(orderInfo.PayTime) > moment("2000-01-01") ? "pay-successful" : "gantanhao"} size={100}></IconFont>
                        </view>
                        <Text className="statfus-text">{moment(orderInfo.PayTime) > moment("2000-01-01") ? "支付成功" : "未收到支付成功信息"}</Text>
                        <Text className="amount" style={{ marginTop: "20rpx", textDecoration: moment(orderInfo.PayTime) > moment("2000-01-01") ? "none" : "line-through" }}>
                            ¥{orderInfo.PayMoney.toFixed(2)}
                        </Text>
                    </view>

                    <view className="course-card">
                        <view className="course-info">
                            <Text className="label">场地名称</Text>
                            <Text className="value">{orderInfo.FieldName}</Text>
                        </view>
                        <view className="course-info">
                            <Text className="label">学员姓名</Text>
                            <Text className="value">{orderInfo.xm}</Text>
                        </view>
                        <view className="course-info">
                            <Text className="label">证件号码</Text>
                            <Text className="value">{orderInfo.sfzmhm}</Text>
                        </view>
                        <view className="course-info">
                            <Text className="label">预约车型</Text>
                            <Text className="value">
                                {orderInfo.CarType} {orderInfo.BrandName}
                                {orderInfo.ModelName}
                            </Text>
                        </view>
                        {moment(orderInfo.PayTime) > moment("2000-01-01") && (
                            <view className="course-info">
                                <Text className="label">考试日期</Text>
                                <Text className="value">{moment(orderInfo.ksrq).format("YYYY-MM-DD")}</Text>
                            </view>
                        )}
                        {orderInfo.PlanDetailNickName && orderInfo.PlanDetailNickName != "" && (
                            <view className="course-info">
                                <Text className="label">预约场次</Text>
                                <Text className="value">{orderInfo.PlanDetailNickName}</Text>
                            </view>
                        )}
                        <view className="course-info">
                            <Text className="label">开始时间</Text>
                            <Text className="value">{orderInfo.StartTime}</Text>
                        </view>
                        {moment(orderInfo.PayTime) > moment("2000-01-01") && (
                            <>
                                <view className="divider"></view>
                                <view className="order-info">
                                    <Text className="pay-time">支付时间：{orderInfo.PayTime}</Text>
                                </view>
                            </>
                        )}
                    </view>

                    {orderInfo.Sales.length > 0 && (
                        <view className="vouchers-section">
                            <view className="section-header">
                                <Text className="title">电子券（{orderInfo.Sales.length}张）</Text>
                            </view>
                            {orderInfo.Sales.map((sale) => (
                                <view key={sale.Id} className="voucher-card">
                                    <view className="voucher-header">
                                        <view className="voucher-info">
                                            <Text className="voucher-name">{sale.ItemName}</Text>
                                        </view>
                                        <Text className="voucher-code">编号: {sale.SysId}</Text>
                                        <Text className="voucher-validity">有效期至: {sale.CanUseEndTime}</Text>
                                    </view>
                                    <view className="voucher-qrcode" onClick={() => this.handleShowQRCode(sale)}>
                                        <Text className="qrcode-tip">扫码验证使用</Text>
                                    </view>
                                </view>
                            ))}
                        </view>
                    )}
                </view>

                {isQRCodeModalVisible && (
                    <view className="qr-modal-mask">
                        <view className="qr-modal-content">
                            <view className="qr-header">
                                <Text className="qr-title">扫码验证使用</Text>
                                {orderInfo.CanInvoice && (
                                    <Button className="invoice-button" onClick={this.handleInvoiceApplication}>
                                        开票申请
                                    </Button>
                                )}
                            </view>
                            <view className="ad-container">
                                <Ad unitId="adunit-64e8a82733ec1ea4" />
                            </view>
                            <view className="qr-body">
                                <view className="qr-code-container">
                                    <Text className="qr-number">{String(currentSale?.SaleOrderNum).padStart(4, "0")}</Text>
                                    <Text className="qr-cartype">
                                        {currentSale?.CarType || "暂无车型"} {currentSale?.BrandName} {currentSale?.ModelName}
                                    </Text>

                                    <QRCode text={"https://51jx.cc/Self/ExamSite/Sale?Id=" + (currentSale?.Id || "")} size={200} scale={4} errorCorrectLevel="M" typeNumber={2} />
                                    <Text className="qr-id">{currentSale?.Id?.toUpperCase()}</Text>
                                    <Text className="qr-id">{currentSale?.SysId}</Text>
                                </view>

                                <view className="qr-buttons">
                                    {/* <Button className="print-button" onClick={this.handlePrintQRCode}>
                                        打印电子券
                                    </Button> */}
                                    <Button className="close-button" onClick={this.handleCloseQRCode}>
                                        关闭
                                    </Button>
                                    <Button className="share-button" openType="share" onClick={this.handleShareToCoach}>
                                        分享给我的教练
                                    </Button>
                                    <Text className="share-tip">为确保训练信息同步，请及时点击分享按钮分享给教练</Text>
                                </view>
                            </view>
                        </view>
                    </view>
                )}
            </Layout>
        );
    }
}
