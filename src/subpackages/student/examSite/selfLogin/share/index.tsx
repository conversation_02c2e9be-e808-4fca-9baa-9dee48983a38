import { useState } from "react";
import { Text, Button, Input } from "@tarojs/components";
import { ConfigProvider, Rate } from "@nutui/nutui-react-taro";
import Layout from "@components/Layout";
import "./index.scss";
import request from "@service/request";
import Taro, { useReady, useRouter } from "@tarojs/taro";
import { message } from "@components/MessageApi/MessageApiSingleton";
import moment from "moment";
import { login } from "@utils/login";
import { QValue } from "@utils/util";

interface TrainingInfo {
    xm: string;
    sfzmhm: string;
    ItemName: string;
    ExamDate: string;
    PayMoney: number;
    trainingStatus: string;
    securityOfficer: string;
    UseTime: string;
    trainingMileage: string;
    trainingDuration: string;
    Id: string;
    SysId: number;
    Coupon: number;
    CouponPoint: number;
    CarType: string;
    BrandName: string;
    ModelName: string;
    PlanDetailNickName: string;
    PlanDetailStartTime: string;
    FieldNickName: string;
    TenantId: string;
    FieldId: string;
    MainUserId: string;
}

// 全局广告实例，避免重复创建
let globalVideoAdInstance: any = null;
let isGlobalAdInitialized = false;

const CoachVoucherDetail = () => {
    const [loading, setLoading] = useState(true);
    const [saleInfo, setSaleInfo] = useState<TrainingInfo>();
    const [activeTab, setActiveTab] = useState("purchase");
    const [comment, setComment] = useState("");
    const [rating, setRating] = useState(5);
    const [adCompleted, setAdCompleted] = useState(false);
    const [isAdPlaying, setIsAdPlaying] = useState(false);
    const [hasWatchedInitialAd, setHasWatchedInitialAd] = useState(false);
    const [userSkippedAd, setUserSkippedAd] = useState(false);

    const router = useRouter();

    // 初始化广告实例（只执行一次）
    const initVideoAdInstance = () => {
        console.log("初始化广告实例");
        if (isGlobalAdInitialized || !Taro.createRewardedVideoAd) {
            console.log("广告实例已初始化或不支持广告");
            return;
        }

        console.log("开始创建全局广告实例");
        globalVideoAdInstance = Taro.createRewardedVideoAd({
            adUnitId: "adunit-93ac4f905c3a8052",
        });

        globalVideoAdInstance.onLoad(() => {
            console.log("✅ 激励视频广告加载成功");
        });

        globalVideoAdInstance.onError((err) => {
            console.error("❌ 激励视频广告加载失败", err);
        });

        globalVideoAdInstance.onClose((res) => {
            console.log("🎯 广告关闭", res);
            setIsAdPlaying(false);

            // 检查用户是否看完了广告
            if (res && res.isEnded) {
                console.log("✅ 用户看完了广告");
                setAdCompleted(true);
                setHasWatchedInitialAd(true);
            } else {
                console.log("❌ 用户放弃了广告");
                setUserSkippedAd(true);
                setHasWatchedInitialAd(true);
            }
        });

        isGlobalAdInitialized = true;
        console.log("广告实例初始化完成");
    };

    // 播放首次广告
    const playInitialAd = async () => {
        return new Promise<void>((resolve) => {
            if (!globalVideoAdInstance) {
                console.log("广告实例不存在，跳过广告");
                setHasWatchedInitialAd(true);
                resolve();
                return;
            }

            if (isAdPlaying) {
                console.log("广告正在播放中，跳过");
                resolve();
                return;
            }

            console.log("开始播放首次广告");
            setIsAdPlaying(true);

            globalVideoAdInstance
                .show()
                .then(() => {
                    console.log("广告显示成功，等待用户观看");
                })
                .catch((err) => {
                    console.error("首次广告显示失败", err);
                    setIsAdPlaying(false);
                    setHasWatchedInitialAd(true);
                    // 记录到本地存储
                    Taro.setStorageSync("hasPlayedInitialAd", true);
                    resolve();
                });

            // 监听广告关闭
            const handleAdClose = () => {
                setIsAdPlaying(false);
                setHasWatchedInitialAd(true);
                // 记录到本地存储
                Taro.setStorageSync("hasPlayedInitialAd", true);
                resolve();
                // 移除监听器避免重复绑定
                globalVideoAdInstance.offClose && globalVideoAdInstance.offClose(handleAdClose);
            };

            globalVideoAdInstance.onClose(handleAdClose);
        });
    };

    // 页面初始化
    useReady(async () => {
        try {
            // 只初始化一次广告实例
            initVideoAdInstance();

            await login();
            console.log("用户登录成功");

            // 播放首次广告
            await playInitialAd();
            console.log("广告流程完成，开始获取订单信息");

            // 加载数据
            if (QValue("Id")) {
                await fetchSaleInfo(QValue("Id"), QValue("TenantId"));
            } else {
                const { Id, TenantId } = router.params;
                await fetchSaleInfo(Id, TenantId);
            }
        } catch (error) {
            console.error("初始化过程中发生错误:", error);
            // 即使出错也要尝试加载数据
            if (QValue("Id")) {
                await fetchSaleInfo(QValue("Id"), QValue("TenantId"));
            } else {
                const { Id, TenantId } = router.params;
                await fetchSaleInfo(Id, TenantId);
            }
        }
    });

    const fetchSaleInfo = async (id: any, tenantId: any) => {
        try {
            if (!id || !tenantId) {
                message.error("请重新获取链接，当前页面链接失效!", "参数错误", () => {});
                return;
            }

            const response = await request.post<any>("/Jx/ExamSite/Wx/Shop/getSaleInfo", {
                Id: id,
                TenantId: tenantId,
            });

            if (response.success) {
                setSaleInfo(response.data);
                setLoading(false);
            } else {
                message.error(response.message, "参数错误", () => {});
            }
        } catch (error) {
            message.error("获取订单信息失败");
        }
    };

    const handleCouponClaim = async () => {
        message.openLoading("正在读取配置");
        const response = await request.post<any>("/Jx/ExamSite/Wx/Coupon/isVideoAd", {});

        // 如果已经观看过初始广告、用户放弃了广告，或者配置不需要看广告，直接领取
        if (hasWatchedInitialAd || userSkippedAd || !response.success || !response.data) {
            console.log(userSkippedAd ? "用户已放弃广告，直接领取返利" : "跳过广告，直接领取返利");
            handleCouponClaimSubmit();
            return;
        }

        // 检查是否正在播放广告
        if (isAdPlaying) {
            message.closeLoading();
            message.error("广告正在播放中，请稍后重试");
            return;
        }

        if (!globalVideoAdInstance) {
            message.closeLoading();
            message.error("广告组件初始化失败");
            return;
        }

        try {
            setIsAdPlaying(true);
            await globalVideoAdInstance.show().catch(() => {
                globalVideoAdInstance
                    .load()
                    .then(() => globalVideoAdInstance.show())
                    .catch((err) => {
                        message.closeLoading();
                        setIsAdPlaying(false);
                        console.error("激励视频广告显示失败", err);
                        message.error("广告显示失败，请稍后重试");
                    });
            });
        } catch (error) {
            message.closeLoading();
            setIsAdPlaying(false);
            console.error("显示广告失败:", error);
            message.error("显示广告失败，请稍后重试");
        }
    };

    const handlePointsClaim = async () => {
        message.openLoading("正在读取配置");
        const response = await request.post<any>("/Jx/ExamSite/Wx/Coupon/isVideoAd", {});

        // 如果已经观看过初始广告、用户放弃了广告，或者配置不需要看广告，直接领取
        if (hasWatchedInitialAd || userSkippedAd || !response.success || !response.data) {
            console.log(userSkippedAd ? "用户已放弃广告，直接领取积分" : "跳过广告，直接领取积分");
            handlePointsClaimSubmit();
            return;
        }

        // 检查是否正在播放广告
        if (isAdPlaying) {
            message.closeLoading();
            message.error("广告正在播放中，请稍后重试");
            return;
        }

        if (!globalVideoAdInstance) {
            message.closeLoading();
            message.error("广告组件初始化失败");
            return;
        }

        try {
            setIsAdPlaying(true);
            await globalVideoAdInstance.show().catch(() => {
                globalVideoAdInstance
                    .load()
                    .then(() => globalVideoAdInstance.show())
                    .catch((err) => {
                        message.closeLoading();
                        setIsAdPlaying(false);
                        console.error("激励视频广告显示失败", err);
                        message.error("广告显示失败，请稍后重试");
                    });
            });
        } catch (error) {
            message.closeLoading();
            setIsAdPlaying(false);
            console.error("显示广告失败:", error);
            message.error("显示广告失败，请稍后重试");
        }
    };

    const handleCouponClaimSubmit = async () => {
        try {
            if (saleInfo) {
                message.openLoading("正在调取接口");
                Taro.login({
                    success: async (res) => {
                        const response: any = await request.put("/Jx/ExamSite/Wx/CashCoupon/sendCashCoupon", {
                            Id: saleInfo?.Id,
                            TenantId: saleInfo?.TenantId,
                            code: res.code,
                        });

                        if (response.success) {
                            message.openToast("返利领取成功");
                            await fetchSaleInfo(saleInfo?.Id!, saleInfo?.TenantId!); // 刷新数据
                        } else {
                            message.closeLoading();
                            message.error(response.message, "发送失败");
                        }
                    },
                });
            } else {
                message.error("请尝试重新点击按钮", "参数错误");
            }
        } catch (error) {
            message.closeLoading();
            message.error("领取失败，请重试", "发送失败");
        }
    };

    const handlePointsClaimSubmit = async () => {
        try {
            if (saleInfo) {
                message.openLoading("正在调取接口");
                const response: any = await request.put("/Jx/ExamSite/Wx/PointCoupon/sendPointCoupon", {
                    Id: saleInfo?.Id,
                    TenantId: saleInfo?.TenantId,
                });

                if (response.success) {
                    message.closeLoading();
                    message.openToast("积分领取成功");
                    await fetchSaleInfo(saleInfo?.Id!, saleInfo?.TenantId!); // 刷新数据
                } else {
                    message.closeLoading();
                    message.error(response.message, "发送失败");
                }
            } else {
                message.error("请尝试重新点击按钮", "参数错误");
            }
        } catch (error) {
            message.closeLoading();
            message.error("领取失败，请重试", "发送失败");
        }
    };

    if (loading) {
        return (
            <Layout>
                <view className="loading-page">
                    <view className="loading-content">
                        <view className="loading-container">
                            <view className="loading-wrapper">
                                <view className="loading-circle"></view>
                                <view className="loading-circle"></view>
                                <view className="loading-circle"></view>
                            </view>
                            <Text className="loading-text">正在加载订单信息...</Text>
                            <Text className="loading-tips">请稍候，马上就好</Text>
                        </view>
                    </view>
                </view>
                <view className="footer">{saleInfo?.SysId}</view>
            </Layout>
        );
    }

    // 判断是否显示返利区域以及显示内容
    const renderCouponSection = () => {
        if (!saleInfo) return null;

        // 如果既没有返利也没有积分，则显示找回绑定账号按钮
        if (saleInfo.MainUserId == "00000000-0000-0000-0000-000000000000" || !saleInfo.MainUserId) {
            return (
                <Button
                    style={{
                        width: "90%",
                        height: "44px",
                        margin: "0 auto 20px",
                        backgroundColor: "#fff",
                        color: "#333",
                        fontSize: "16px",
                        border: "none",
                        borderRadius: "8px",
                        display: "block",
                        boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
                        textAlign: "center",
                        lineHeight: "44px",
                        fontWeight: "normal",
                    }}
                    onClick={() => Taro.navigateTo({ url: `/pages/user/findMainUser/index?TenantId=${saleInfo?.TenantId}` })}
                >
                    找回绑定账号
                </Button>
            );
        }

        if (saleInfo.Coupon > 0 || saleInfo.CouponPoint > 0) {
            return (
                <view className="coupon-section">
                    {/* 返利部分 */}
                    {saleInfo.Coupon > 0 && (
                        <view className="coupon-content" style={{ marginBottom: saleInfo.CouponPoint > 0 ? "20px" : "0" }}>
                            <view className="coupon-info">
                                <Text className="coupon-label">可获得返利</Text>
                                <Text className="coupon-amount">¥{saleInfo.Coupon.toFixed(2)}</Text>
                                <Text className="coupon-desc">完成培训即可领取</Text>
                            </view>
                            <Button className="coupon-btn" onClick={handleCouponClaim}>
                                立即领取
                            </Button>
                        </view>
                    )}

                    {/* 积分部分 */}
                    {saleInfo.CouponPoint > 0 && (
                        <view className="coupon-content points">
                            <view className="coupon-info">
                                <Text className="coupon-label">可获得积分</Text>
                                <Text className="coupon-amount">
                                    {saleInfo.CouponPoint} <Text className="text">点数</Text>
                                </Text>
                                <Text className="coupon-desc">完成培训即可领取</Text>
                            </view>
                            <Button className="coupon-btn" onClick={handlePointsClaim}>
                                立即领取
                            </Button>
                        </view>
                    )}
                </view>
            );
        }
    };
    //.nut-cell {

    return (
        <ConfigProvider
            theme={{
                nutuiCellMargin: "0",
            }}
        >
            <Layout>
                <view className="voucher-page">
                    <view
                        className="voucher-header"
                        onClick={() =>
                            Taro.navigateTo({
                                url: `/subpackages/student/examSite/selfLogin/index?TenantId=${saleInfo?.TenantId}&FieldId=${saleInfo?.FieldId}`,
                            })
                        }
                    >
                        <Text className="voucher-label">{saleInfo?.FieldNickName}</Text>
                        <Text className="amount-desc">{saleInfo?.ItemName}</Text>
                    </view>

                    <view className="tab-container">
                        <view className={`tab-item ${activeTab === "purchase" ? "active" : ""}`} onClick={() => setActiveTab("purchase")}>
                            <Text>购买信息</Text>
                        </view>
                        <view className={`tab-item ${activeTab === "training" ? "active" : ""}`} onClick={() => setActiveTab("training")}>
                            <Text>训练信息</Text>
                        </view>
                    </view>

                    <view className="content-section" style={{ padding: "10px 0" }}>
                        {activeTab === "purchase" ? (
                            <view className="purchase-info" style={{ padding: "0 15px" }}>
                                <view className="info-item" style={{ marginBottom: "8px", display: "flex", alignItems: "center" }}>
                                    <Text className="label" style={{ fontSize: "14px", minWidth: "70px" }}>
                                        学员姓名：
                                    </Text>
                                    <Text className="value" style={{ fontSize: "14px" }}>
                                        {saleInfo?.xm}
                                    </Text>
                                </view>
                                <view className="info-item" style={{ marginBottom: "8px", display: "flex", alignItems: "center" }}>
                                    <Text className="label" style={{ fontSize: "14px", minWidth: "70px" }}>
                                        系统编号：
                                    </Text>
                                    <Text className="value" style={{ fontSize: "14px" }}>
                                        {saleInfo?.SysId}
                                    </Text>
                                </view>
                                <view className="info-item" style={{ marginBottom: "8px", display: "flex", alignItems: "center" }}>
                                    <Text className="label" style={{ fontSize: "14px", minWidth: "70px" }}>
                                        考试车型：
                                    </Text>
                                    <Text className="value" style={{ fontSize: "14px" }}>
                                        {saleInfo?.CarType} {saleInfo?.BrandName} {saleInfo?.ModelName}
                                    </Text>
                                </view>
                                <view className="info-item" style={{ marginBottom: "8px", display: "flex", alignItems: "center" }}>
                                    <Text className="label" style={{ fontSize: "14px", minWidth: "70px" }}>
                                        考试日期：
                                    </Text>
                                    <Text className="value" style={{ fontSize: "14px" }}>
                                        {moment(saleInfo?.ExamDate) < moment("2000-01-01") ? "" : moment(saleInfo?.ExamDate).format("YYYY-MM-DD")}
                                    </Text>
                                </view>
                            </view>
                        ) : (
                            <view className="training-info" style={{ padding: "0 15px" }}>
                                <view className="info-item" style={{ marginBottom: "8px", display: "flex", alignItems: "center" }}>
                                    <Text className="label" style={{ fontSize: "14px", minWidth: "70px" }}>
                                        安全员名：
                                    </Text>
                                    <Text className="value" style={{ fontSize: "14px" }}>
                                        {saleInfo?.securityOfficer}
                                    </Text>
                                </view>
                                <view className="info-item" style={{ marginBottom: "8px", display: "flex", alignItems: "center" }}>
                                    <Text className="label" style={{ fontSize: "14px", minWidth: "70px" }}>
                                        使用时间
                                    </Text>
                                    <Text className="value" style={{ fontSize: "14px" }}>
                                        {moment(saleInfo?.UseTime) < moment("2000-01-01") ? "" : saleInfo?.UseTime}
                                    </Text>
                                </view>
                                <view className="info-item" style={{ marginBottom: "8px", display: "flex", alignItems: "center" }}>
                                    <Text className="label" style={{ fontSize: "14px", minWidth: "70px" }}>
                                        训练里程：
                                    </Text>
                                    <Text className="value" style={{ fontSize: "14px" }}>
                                        {saleInfo?.trainingMileage}公里
                                    </Text>
                                </view>
                                <view className="info-item" style={{ marginBottom: "8px", display: "flex", alignItems: "center" }}>
                                    <Text className="label" style={{ fontSize: "14px", minWidth: "70px" }}>
                                        训练时长：
                                    </Text>
                                    <Text className="value" style={{ fontSize: "14px" }}>
                                        {saleInfo?.trainingDuration}分钟
                                    </Text>
                                </view>
                            </view>
                        )}
                    </view>

                    {/* 返利区域 */}
                    {renderCouponSection()}

                    {/* 评价部分 */}
                    <view className="rating-section">
                        <Text className="section-title">教练评价</Text>
                        <view className="rate-wrapper">
                            <Rate value={rating} onChange={setRating} />
                        </view>
                        <Input
                            className="comment-input"
                            placeholder="请输入您的评价（选填）"
                            value={comment}
                            onInput={(e) => setComment(e.detail.value)}
                            adjustPosition
                            placeholderStyle="vertical-align: top;"
                        />
                        <Button className="submit-btn">提交评价</Button>
                    </view>
                    <view className="footer">{saleInfo?.Id.toUpperCase()}</view>
                </view>
            </Layout>
        </ConfigProvider>
    );
};

export default CoachVoucherDetail;
