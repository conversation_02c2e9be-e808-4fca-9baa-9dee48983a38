.footer {
    font-size: 20rpx;
    background-color: #fff;
}

:root {
    --nutui-cell-group-wrap-margin: 0;
}
// 定义主色调变量
$primary-purple: #1a1a2e;
$primary-light: #8e7cdc;
$bg-white: #fff;
$bg-gray: #f7f8fa;

page {
    background: $bg-gray;
    font-family: "AlibabaPuHuiTi";
    // font-family: Marion, Yuanti SC, STYuanti-SC-Regular;
}

.voucher-page {
    min-height: 100vh;
    background: $bg-gray;
    padding-bottom: 40px;
}

// 电子券头部
.voucher-header {
    background: linear-gradient(135deg, $primary-purple, $primary-light);
    padding: 10vh 32px 40px 32px;
    color: #fff;
    position: relative;
    box-shadow: 0 4px 20px rgba($primary-purple, 0.2);

    &::after {
        content: "";
        position: absolute;
        bottom: -16px;
        left: 32px;
        right: 32px;
        height: 32px;
        background: $bg-white;
        border-radius: 16px 16px 0 0;
    }

    .voucher-label {
        font-size: 40px;
        font-weight: 1000;
        opacity: 0.9;
        // margin-bottom: 24px;
        display: block;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .amount-container {
        display: flex;
        align-items: baseline;
        margin: 20px 0;

        .currency {
            font-size: 40px;
            margin-right: 8px;
            font-weight: 300;
        }

        .amount {
            font-size: 88px;
            font-weight: 600;
            line-height: 1;
            letter-spacing: -2px;
        }
    }

    .amount-desc {
        font-size: 24px;
        opacity: 0.9;
    }
}

// Tab 切换
.tab-container {
    display: flex;
    background: $bg-white;
    margin: 0 32px;
    border-radius: 16px;
    padding: 8px;
    position: relative;
    margin-top: -16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

    .tab-item {
        flex: 1;
        text-align: center;
        padding: 12rpx 0;
        font-size: 28rpx;
        color: #666;
        position: relative;
        border-radius: 12px;
        transition: all 0.3s ease;

        &.active {
            color: $primary-purple;
            font-weight: 500;
            background: rgba($primary-light, 0.1);

            &::after {
                content: "";
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 32px;
                height: 3px;
                background: $primary-purple;
                border-radius: 2px;
            }
        }
    }
}

// 内容区域
.content-section {
    background: $bg-white;
    padding: 32px;
    margin: 20px 32px;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

    .info-item {
        display: flex;
        margin-bottom: 24px;
        font-size: 28px;
        line-height: 1.6;

        &:last-child {
            margin-bottom: 0;
        }

        .label {
            color: #666;
            min-width: 160px;
        }

        .value {
            color: #333;
            flex: 1;

            &.status {
                color: $primary-purple;
                font-weight: 500;
            }
        }
    }
}

// Loading 样式
.loading-page {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: $bg-white;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: fadeIn 0.5s ease;
}

.loading-wrapper {
    display: flex;
    justify-content: center;
    margin-bottom: 32px;
}

.loading-circle {
    width: 12px;
    height: 12px;
    margin: 0 6px;
    background: $primary-purple;
    border-radius: 50%;
    animation: bounce 0.5s infinite alternate;

    &:nth-child(2) {
        animation-delay: 0.15s;
    }

    &:nth-child(3) {
        animation-delay: 0.3s;
    }
}

@keyframes bounce {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-10px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.loading-text {
    font-size: 32px;
    color: #333;
    margin-bottom: 12px;
    font-weight: 500;
}

.loading-tips {
    font-size: 24px;
    color: #999;
}

// 红包区域完整样式
.redpacket-section {
    margin: 20px 32px;
    background: #fff;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

    .redpacket-content {
        background: linear-gradient(135deg, #ff4d4d, #ff6b6b);
        border-radius: 12px;
        padding: 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #fff;
        position: relative;
        overflow: hidden;

        &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSJub25lIj48cGF0aCBkPSJNNjAgMGMyNi41MSAwIDQ4IDIxLjQ5IDQ4IDQ4Uzg2LjUxIDk2IDYwIDk2IDEyIDc0LjUxIDEyIDQ4IDMzLjQ5IDAgNjAgMHoiIGZpbGw9InJnYmEoMjU1LDI1NSwyNTUsMC4xKSIvPjwvc3ZnPg==");
            background-repeat: no-repeat;
            background-position: right -20px top -20px;
            opacity: 0.1;
        }
    }

    .redpacket-info {
        flex: 1;

        .redpacket-label {
            font-size: 28px;
            opacity: 0.9;
            display: block;
            margin-bottom: 12px;
        }

        .redpacket-amount {
            font-size: 48px;
            font-weight: 600;
            display: block;
            margin-bottom: 8px;
        }

        .redpacket-desc {
            font-size: 24px;
            opacity: 0.8;
        }
    }

    .redpacket-btn {
        background: #fff;
        color: #ff4d4d;
        height: 72px;
        padding: 0 36px;
        border-radius: 36px;
        font-size: 28px;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.95);
            opacity: 0.9;
        }
    }
}

// 评价部分样式优化
.rating-section {
    background: #fff;
    padding: 32px;
    margin: 20px 32px 80rpx 20rpx;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

    .section-title {
        font-size: 32px;
        font-weight: 500;
        color: #333;
        margin-bottom: 16px; // 减小标题底部间距
        display: block;
    }

    .rate-wrapper {
        padding: 24px 0; // 增加评分区域的上下间距
        border-bottom: 1px solid #f5f5f5; // 添加分隔线

        // 修改星星样式
        .nut-rate {
            display: flex;
            align-items: center;

            .nut-rate-item {
                margin-right: 24px; // 增加星星间距
                font-size: 36px; // 调整星星大小
                color: #ddd; // 未选中颜色
                transition: transform 0.2s ease;

                &--active {
                    color: $primary-purple;
                    transform: scale(1.1);
                }
            }
        }
    }

    .comment-input {
        width: 100%;
        min-height: 80px;
        background: #f8f9fa;
        border: none;
        border-radius: 12px;
        padding: 20px 24px;
        font-size: 28px;
        line-height: 1.4;
        box-sizing: border-box;
        margin: 24px 0;
        display: block;
        vertical-align: top;

        &::placeholder {
            color: #999;
            vertical-align: top;
        }

        &:focus {
            outline: none;
            background: #fff;
            box-shadow: 0 0 0 2px rgba($primary-purple, 0.1);
        }
    }
    .submit-btn {
        width: 100%;
        height: 88px;
        background: linear-gradient(135deg, $primary-purple, $primary-light);
        color: #fff;
        font-size: 32px;
        border-radius: 44px;
        border: none;
        margin-top: 24px;
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.98);
            opacity: 0.9;
        }
    }
}
.coupon-section {
    margin: 20px 32px;
    background: transparent;
    border-radius: 16px;

    .coupon-content {
        background: linear-gradient(135deg, #ff4d4d, #ff6b6b); // 返现时的红色渐变
        border-radius: 12px;
        padding: 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #fff;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 16px rgba(255, 77, 77, 0.2);

        // 积分版本使用较浅的红色
        &.points {
            background: linear-gradient(135deg, #ff7070, #ff9292);
            box-shadow: 0 4px 16px rgba(255, 112, 112, 0.2);
        }

        &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSJub25lIj48cGF0aCBkPSJNNjAgMGMyNi41MSAwIDQ4IDIxLjQ5IDQ4IDQ4Uzg2LjUxIDk2IDYwIDk2IDEyIDc0LjUxIDEyIDQ4IDMzLjQ5IDAgNjAgMHoiIGZpbGw9InJnYmEoMjU1LDI1NSwyNTUsMC4xKSIvPjwvc3ZnPg==");
            background-repeat: no-repeat;
            background-position: right -20px top -20px;
            opacity: 0.1;
        }
    }

    .coupon-info {
        flex: 1;

        .coupon-label {
            font-size: 28px;
            opacity: 0.9;
            display: block;
            margin-bottom: 12px;
        }

        .coupon-amount {
            font-size: 48px;
            font-weight: 600;
            display: block;
            margin-bottom: 8px;

            .text {
                font-size: 20px;
            }
        }

        .coupon-desc {
            font-size: 24px;
            opacity: 0.8;
        }
    }

    .coupon-btn {
        background: #fff;
        color: #ff4d4d; // 返现按钮文字颜色
        height: 72px;
        padding: 0 36px;
        border-radius: 36px;
        font-size: 28px;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;

        .points & {
            color: #ff7070; // 积分按钮文字颜色
        }

        &:active {
            transform: scale(0.95);
            opacity: 0.9;
        }
    }
}

.recover-account-btn {
    width: 90% !important;
    margin: 0 auto 20px !important;
    background-color: white !important;
    color: #333 !important;
    border: 1px solid #e5e5e5 !important;
    border-radius: 8px !important;
}
