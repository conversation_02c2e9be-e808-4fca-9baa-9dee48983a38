// styles/common.scss
page {
    background: #ffffff;
    --nutui-popup-border-radius: 18rpx;
    font-family: "AlibabaPuHuiTi"; //, -apple-system, "PingFang SC", "Helvetica Neue", STHeiti, "Microsoft Yahei", <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
    color: #333333;
    line-height: 1.6;
    --primary-color: #1677ff;
    --primary-hover: #4096ff;
    --primary-active: #0958d9;
    font-size: 32rpx;
}
.nut-picker-title {
    font-size: 28rpx;
}
// Fix for CSSTransition issues in mini-programs
:global(.nut-popup) {
    border-radius: 24rpx;
}

:global(.nut-css-transition-appear),
:global(.nut-css-transition-enter),
:global(.nut-css-transition-leave) {
    transition: all 0.3s;
}

.font {
    font-size: 34rpx;
    font-family: -apple-system, "PingFang SC", "Helvetica Neue", STHeiti, "Microsoft Yahei", <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: #1a1a1a;
    font-weight: 500;
}

.title-text {
    font-size: 36rpx;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 24rpx;
}

.text-primary {
    font-size: 34rpx;
    color: var(--primary-color);
    font-weight: 500;
}

.text-secondary {
    font-size: 32rpx;
    color: #666666;
}

.input-text {
    font-size: 34rpx;
    color: #333333;
    line-height: 1.6;
    padding: 20rpx 24rpx;
    background: #f8f9fa;
    border: 2rpx solid #e8e8e8;
    border-radius: 12rpx;
}

.label-text {
    font-size: 32rpx;
    color: #666666;
    margin-bottom: 12rpx;
    display: block;
}

.section-spacing {
    margin: 32rpx 0;
    padding: 0 24rpx;
    background: #ffffff;
}

.card {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    border: 2rpx solid #f0f0f0;
    margin: 24rpx;
}

.button-text {
    font-size: 34rpx;
    font-weight: 500;
    color: #ffffff;
    padding: 24rpx 48rpx;
    background: var(--primary-color);
    border-radius: 12rpx;
    text-align: center;
}

.footer {
    font-size: 28rpx;
    color: #999999;
    text-align: center;
    padding: 24rpx 0;
    background: #ffffff;
}

.primary-bg {
    background-color: var(--primary-color);
}

.primary-border {
    border-color: var(--primary-color);
}

.primary-text {
    color: var(--primary-color);
    font-weight: 500;
}

.primary-hover:hover {
    color: var(--primary-hover);
}

.primary-active:active {
    color: var(--primary-active);
}

.primary-gradient {
    background: var(--primary-color);
}

.primary-shadow {
    box-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.1);
}

.list-item {
    padding: 24rpx;
    border-bottom: 2rpx solid #f0f0f0;
    font-size: 32rpx;
    line-height: 1.6;
    background: #ffffff;
}

.popup-content {
    padding: 32rpx;
    font-size: 32rpx;
    line-height: 1.6;
    background: #ffffff;
}

.form-item {
    margin-bottom: 32rpx;
    background: #ffffff;
}

.form-label {
    font-size: 32rpx;
    color: #333333;
    margin-bottom: 12rpx;
    display: block;
}

.form-input {
    font-size: 34rpx;
    padding: 24rpx;
    border: 2rpx solid #e8e8e8;
    border-radius: 12rpx;
    background: #f8f9fa;
}

// 添加新的背景色类
.bg-white {
    background: #ffffff;
}

.bg-light {
    background: #f8f9fa;
}

.bg-primary {
    background: var(--primary-color);
}

// 添加新的文字颜色类
.text-dark {
    color: #1a1a1a;
}

.text-gray {
    color: #666666;
}

.text-light {
    color: #999999;
}

@media screen and (max-width: 375px) {
    .font {
        font-size: 32rpx;
    }

    .title-text {
        font-size: 34rpx;
    }

    .input-text {
        font-size: 32rpx;
        padding: 16rpx 20rpx;
    }

    .button-text {
        font-size: 32rpx;
        padding: 20rpx 40rpx;
    }

    .card {
        padding: 24rpx;
        margin: 16rpx;
    }
}

.custom-bounce-dot {
    width: 24rpx;
    height: 24rpx;
    border-radius: 50%;
    background: #1989fa;
    display: inline-block;
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%,
    100% {
        transform: translateY(0);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20rpx);
        opacity: 1;
    }
}

.custom-spinner {
    width: 80rpx;
    height: 80rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #1989fa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
