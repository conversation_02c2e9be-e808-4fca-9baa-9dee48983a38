page {
    background: #f7f8fa;
    // min-height: 100vh;
    --nutui-popup-border-radius: 18rpx;
    font-family: "AlibabaPuHuiTi";
    // font-family: Marion, Yuanti SC, STYuanti-SC-Regular;
}

.redirect-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(150deg, #f8f9fa, #e9ecef);
    padding: 20px;

    .content-container {
        width: 90%;
        max-width: 600px;
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        position: relative;
    }

    .main-content {
        padding: 40px 30px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
    }

    .icon-wrapper {
        margin-bottom: 10px;
    }

    .icon-circle {
        width: 160px;
        height: 160px;
        background: linear-gradient(135deg, #4caf50, #45a049);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8px 30px rgba(76, 175, 80, 0.3);

        .icon {
            color: white;
            font-size: 72px;
            font-weight: bold;
        }
    }

    .title {
        font-size: 36px;
        font-weight: bold;
        color: #2c3e50;
        margin: 10px 0;
        letter-spacing: 2px;
    }

    .divider {
        width: 40px;
        height: 4px;
        background: linear-gradient(90deg, #4caf50, #45a049);
        border-radius: 2px;
        margin: 5px 0 15px;
    }

    .subtitle {
        font-size: 28px;
        color: #666;
        margin-bottom: 25px;
    }

    .start-button {
        background: linear-gradient(135deg, #4caf50, #45a049);
        color: white;
        font-size: 32px;
        font-weight: 500;
        height: 90px;
        line-height: normal;
        width: 85%;
        border-radius: 45px;
        border: none;
        box-shadow: 0 6px 16px rgba(76, 175, 80, 0.25);
        transition: all 0.3s ease;
        margin-top: 10px;

        &:active {
            transform: translateY(2px);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
        }

        &.rescan-button {
            background: linear-gradient(135deg, #2196f3, #1976d2);
            margin-top: 20px;
            box-shadow: 0 6px 16px rgba(33, 150, 243, 0.25);

            &:active {
                box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
            }
        }
    }

    .loading-container {
        text-align: center;
        padding: 40px;
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);

        .loading-animation {
            margin-bottom: 25px;

            .dot {
                display: inline-block;
                width: 14px;
                height: 14px;
                margin: 0 6px;
                background-color: #4caf50;
                border-radius: 50%;
                animation: bounce 1.4s infinite ease-in-out;

                &.dot-1 {
                    animation-delay: 0s;
                }
                &.dot-2 {
                    animation-delay: 0.16s;
                }
                &.dot-3 {
                    animation-delay: 0.32s;
                }
            }
        }

        .loading-text {
            font-size: 32px;
            color: #2c3e50;
            margin-bottom: 12px;
            font-weight: 500;
        }

        .loading-subtext {
            font-size: 26px;
            color: #666;
        }
    }
}

@keyframes bounce {
    0%,
    80%,
    100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}
.bottom-link {
    position: fixed;
    bottom: calc(env(safe-area-inset-bottom) / 2 + 90px); /* 30px + footer height */
    left: 50%;
    transform: translateX(-50%);
    color: #1677ff;
    font-size: 28px;
    text-decoration: underline;
    padding: 20px;
    cursor: pointer;
    z-index: 100;
}
// @supports (-webkit-touch-callout: none) {
//     .bottom-link {
//         padding-bottom: calc(20px + constant(safe-area-inset-bottom));
//         padding-bottom: calc(20px + env(safe-area-inset-bottom));
//     }
// }
