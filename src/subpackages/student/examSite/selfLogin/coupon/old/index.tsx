import { useState, useEffect } from "react";
import { Button } from "@tarojs/components";
import Taro, { useReady, useDidShow } from "@tarojs/taro";
import { message } from "@components/MessageApi/MessageApiSingleton";
import Layout from "@components/Layout";
import "./index.scss";
import { login } from "@utils/login";
import request, { ApiResponse } from "@service/request";
import { QValue } from "@/utils/util";

interface SaleInfo {
    Id: string;
    TenantId: string;
    // 其他可能的字段
}
const CouponRedirect = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [adCompleted, setAdCompleted] = useState(false);
    const [videoAd, setVideoAd] = useState<Taro.RewardedVideoAd | null>(null);
    const [currentSaleId, setCurrentSaleId] = useState<string | null>(null);

    const [saleId, setSaleId] = useState("");

    useDidShow(() => {
        login().then(async () => {
            // try {
            //     // 重置广告状态
            //     setAdCompleted(false);
            //     setIsLoading(false);
            //     // 获取最新的 SaleID
            //     const newSaleId = await updateCurrentSaleId();
            //     console.log("页面显示，更新 SaleID:", newSaleId);
            //     if (!newSaleId) {
            //         console.warn("未能获取到有效的 SaleID");
            //         // 可以选择是否显示错误提示
            //         message.error("未能获取到有效参数");
            //     } else {
            //         setSaleId(newSaleId);
            //     }
            // } catch (error) {
            //     console.error("页面显示时更新参数失败:", error);
            // }
        });
    });

    // 更新 SaleID 的函数
    const updateCurrentSaleId = async () => {
        const launchOptions = Taro.getLaunchOptionsSync();
        const query = launchOptions.query || {};
        let saleId = query.SaleID;

        // 如果直接获取 SaleID 失败，尝试从 q 参数中提取
        if (!saleId && query.q) {
            try {
                // 解码 URL
                const decodedUrl = decodeURIComponent(decodeURIComponent(query.q));

                // 尝试使用 URL 对象解析
                try {
                    const url = new URL(decodedUrl);
                    const searchParams = new URLSearchParams(url.hash.split("?")[1]);
                    saleId = searchParams.get("SaleID");
                } catch (e) {
                    console.log("URL parsing failed:", e);
                }

                // 如果上面的方式失败，使用正则表达式提取
                if (!saleId) {
                    const saleIdMatch = decodedUrl.match(/SaleID=(\d+)/);
                    if (saleIdMatch) {
                        saleId = saleIdMatch[1];
                    }
                }
            } catch (parseErr) {
                console.error("解析 URL 失败:", parseErr);
                const saleIdMatch = decodeURIComponent(query.q).match(/SaleID=(\d+)/);
                if (saleIdMatch) {
                    saleId = saleIdMatch[1];
                }
            }
        }

        setCurrentSaleId(saleId);
        return saleId;
    };

    // 初始化视频广告
    useEffect(() => {
        if (Taro.createRewardedVideoAd) {
            const videoAdInstance = Taro.createRewardedVideoAd({
                adUnitId: "adunit-b8a95b5c4ffb9383",
            });

            videoAdInstance.onLoad(() => {
                console.log("激励视频广告加载成功");
            });

            videoAdInstance.onError((err) => {
                console.error("广告加载失败", err);
                message.error("视频播放失败，错误信息如下:" + err.errMsg);
                setAdCompleted(true);
            });

            videoAdInstance.onClose((result) => {
                if ((result && result.isEnded) || result === undefined) {
                    setAdCompleted(true);
                } else {
                    message.error("根据微信官方的要求，必须要看完整个视频才能继续操作");
                    setIsLoading(false);
                }
            });

            setVideoAd(videoAdInstance);
        }
    }, []);

    const showAd = async (onComplete: () => Promise<void>) => {
        try {
            const response = await request.post<any>("/Jx/ExamSite/Wx/Coupon/isVideoAd", {});

            if (response.success && response.data) {
                // 在展示广告前，先更新 SaleID
                const latestSaleId = await updateCurrentSaleId();

                if (!latestSaleId) {
                    message.error("缺少必要参数");
                    return;
                }

                if (adCompleted) {
                    setIsLoading(true);
                    await onComplete();
                } else if (videoAd) {
                    await videoAd.show();
                }
            } else {
                await onComplete();
            }
        } catch (err) {
            console.error("激励视频播放失败", err);
            message.error("播放视频失败，错误信息如下:" + err.errMsg);
            setAdCompleted(true);
        }
    };

    const fetchSaleInfo = async (): Promise<SaleInfo | null> => {
        // if (!saleId) {
        //     console.error("SaleId is required");
        //     return null;
        // }

        let saleId = QValue("SaleID");
        let SSGUID = QValue("SSGUID");

        try {
            const response = await request.post<ApiResponse<SaleInfo>>("/Jx/ExamSite/Wx/Shop/getSaleInfo", {
                SysId: saleId ? saleId : 0,
                Id: SSGUID ? SSGUID : undefined,
            });

            console.log("response");
            console.log(response);

            if (response.success && response.data) {
                return response.data;
            }

            return null;
        } catch (error) {
            console.error("获取销售信息失败:", error);
            throw error;
        }
    };

    const handleRescanCoupon = async () => {
        try {
            const result = await Taro.scanCode({
                scanType: ["qrCode"],
                onlyFromCamera: false,
            });

            console.log("扫码结果:", result);

            if (result && result.result) {
                let scannedSaleId: string | null = null;
                const scanResult = result.result;

                // 尝试从扫描结果中提取 SaleID
                try {
                    // 解码 URL
                    const decodedUrl = decodeURIComponent(scanResult);

                    // 尝试使用 URL 对象解析
                    try {
                        const url = new URL(decodedUrl);
                        const searchParams = new URLSearchParams(url.search);
                        scannedSaleId = searchParams.get("SaleID");

                        // 如果在查询参数中找不到，尝试在hash部分查找
                        if (!scannedSaleId && url.hash) {
                            const hashParams = new URLSearchParams(url.hash.split("?")[1]);
                            scannedSaleId = hashParams.get("SaleID");
                        }
                    } catch (e) {
                        console.log("URL parsing failed:", e);
                    }

                    // 如果上述方法失败，使用正则表达式提取
                    if (!scannedSaleId) {
                        const saleIdMatch = decodedUrl.match(/SaleID=(\d+)/);
                        if (saleIdMatch) {
                            scannedSaleId = saleIdMatch[1];
                        }
                    }
                } catch (parseErr) {
                    console.error("解析 URL 失败:", parseErr);
                    // 尝试直接使用正则表达式匹配
                    const saleIdMatch = scanResult.match(/SaleID=(\d+)/);
                    if (saleIdMatch) {
                        scannedSaleId = saleIdMatch[1];
                    }
                }

                if (scannedSaleId) {
                    console.log("成功提取 SaleID:", scannedSaleId);
                    setCurrentSaleId(scannedSaleId);
                    setSaleId(scannedSaleId);
                    message.success("扫码成功，已更新电子券信息");
                } else {
                    message.error("未能从二维码中提取有效信息");
                }
            } else {
                message.error("扫码失败，请重试");
            }
        } catch (error: any) {
            console.error("扫描二维码失败:", error);
            message.error("扫描失败: " + error.errMsg);
        }
    };

    const handleExamSystemClick = async () => {
        try {
            setIsLoading(true);
            const saleInfo = await fetchSaleInfo();

            if (!saleInfo?.Id || !saleInfo?.TenantId) {
                message.error("获取电子券信息失败");
                setIsLoading(false);
                return;
            }

            // 构造目标URL
            const targetUrl = "https://51jx.cc/Self/ExamSite/Coupon";

            // 构造查询参数
            const params = new URLSearchParams({
                Id: saleInfo.Id,
                TenantId: saleInfo.TenantId,
            });

            // 完整的目标URL
            const fullUrl = `${targetUrl}?${params.toString()}`;

            console.log("fullUrl");
            console.log(fullUrl);

            // 构造scene参数
            const scene = `q=${encodeURIComponent(fullUrl)}&scancode_time=${Math.floor(Date.now() / 1000)}`;

            console.log("scene");
            console.log(scene);

            // 使用Taro进行跳转
            Taro.navigateTo({
                url: `/subpackages/student/examSite/selfLogin/share/index?${scene}`,
            });
        } catch (error) {
            console.error("获取考场信息失败:", error);
            message.error("获取考场信息失败，请稍后重试");
            setIsLoading(false);
        }
    };
    return (
        <Layout>
            <view className="redirect-page">
                {!isLoading ? (
                    <view className="content-container">
                        <view className="main-content">
                            <view className="icon-wrapper">
                                <view className="icon-circle">
                                    <view className="icon">督</view>
                                </view>
                            </view>
                            <view className="title">训练监督</view>
                            <view className="divider"></view>
                            <view className="subtitle">观看短视频即可开始</view>

                            <Button
                                className="start-button"
                                onClick={() => {
                                    // showAd(async () => {
                                    //     // 执行其他操作
                                    //     await handleNavigateAfterAd();
                                    // });
                                    showAd(async () => {
                                        // 执行其他操作
                                        await handleExamSystemClick();
                                    });
                                }}
                            >
                                开始提交申请
                            </Button>

                            <Button className="start-button rescan-button" onClick={handleRescanCoupon}>
                                重新扫电子券
                            </Button>
                        </view>
                    </view>
                ) : (
                    <view className="loading-container">
                        <view className="loading-animation">
                            <view className="dot dot-1"></view>
                            <view className="dot dot-2"></view>
                            <view className="dot dot-3"></view>
                        </view>
                        <view className="loading-text">正在为您跳转到小程序</view>
                        <view className="loading-subtext">请稍等片刻...</view>
                    </view>
                )}
            </view>
            {/* <view className="bottom-link-content">
                <view
                    className="bottom-link"
                    onClick={() => {
                        showAd(async () => {
                            // 执行其他操作
                            await handleExamSystemClick();
                        });
                    }}
                >
                    新系统考场点此进入
                </view>
            </view> */}
            <view className="footer">{currentSaleId}</view>
        </Layout>
    );
};

export default CouponRedirect;
