page {
    font-family: "AlibabaPuHuiTi";
    // font-family: <PERSON><PERSON>, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
}

.training-records-container {
    min-height: 100vh;
    background-color: #f5f7fa;
    position: relative;
    // 使容器充满整个屏幕高度
    height: 100vh;
    // 使用 flex 布局
    display: flex;
    flex-direction: column;

    .records-header {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        padding: 10vh 24rpx 32rpx 24rpx;
        color: white;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

        .header-content {
            max-width: 1200rpx;
            margin: 0 auto;
        }

        .date-selector {
            display: flex;
            align-items: center;
            gap: 12rpx;
            margin-bottom: 16rpx;

            .calendar-icon {
                color: rgba(255, 255, 255, 0.9);
            }

            .selected-date {
                margin-left: 10px;
                font-size: 32rpx;
                font-weight: 500;
            }
        }

        .header-summary {
            .summary-text {
                font-size: 24rpx;
                opacity: 0.9;
            }
        }
    }

    .records-list {
        flex: 1; // 占据剩余空间
        overflow-y: auto; // 允许垂直滚动
        padding: 16px;
        // 添加弹性滚动效果
        -webkit-overflow-scrolling: touch;

        padding: 24rpx;
        max-width: 1200rpx;
        margin: 0 auto;
        width: 100%;

        // flex: 1;
        // overflow-y: auto;
        // padding: 16px;
        // padding-bottom: 80px; // 添加底部间距，确保最后一条记录不被按钮遮挡
        // -webkit-overflow-scrolling: touch;

        .loading-state,
        .empty-state {
            text-align: center;
            padding: 80rpx 0;
            color: #666;
        }

        .empty-state {
            .empty-icon {
                color: #999;
                margin-bottom: 16rpx;
            }

            .empty-text {
                font-size: 28rpx;
            }
        }

        .record-card {
            background: white;
            border-radius: 16rpx;
            padding: 24rpx;
            margin-bottom: 16rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;

            &:active {
                transform: scale(0.98);
            }

            &.active {
                border-left: 8rpx solid #4caf50;
            }

            &.completed {
                border-left: 8rpx solid #9e9e9e;
            }

            .record-main {
                flex: 1;
            }

            // .record-primary {
            //     display: flex;
            //     align-items: center;
            //     gap: 16rpx;
            //     margin-bottom: 8rpx;

            //     .student-name {
            //         font-size: 32rpx;
            //         font-weight: 500;
            //         color: #333;
            //     }

            //     .training-field {
            //         font-size: 24rpx;
            //         color: #666;
            //         background: #f5f5f5;
            //         padding: 4rpx 12rpx;
            //         border-radius: 24rpx;
            //     }
            // }
            .record-primary {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;

                .student-name {
                    flex-shrink: 0;
                    max-width: 60%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .training-field {
                    flex-shrink: 0;
                    text-align: right;
                    max-width: 35%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-size: 24rpx;
                    color: #666;
                    background: #f5f5f5;
                    padding: 4rpx 12rpx;
                    border-radius: 24rpx;
                }
            }
            .record-secondary {
                font-size: 24rpx;
                color: #666;

                .time-label {
                    margin-right: 8rpx;
                }

                .start-time {
                    color: #1a237e;
                }
            }

            .chevron-icon {
                color: #999;
            }
        }
    }

    .detail-popup {
        .popup-header {
            padding: 24rpx;
            text-align: center;
            border-bottom: 2rpx solid #f5f5f5;
            position: relative;

            .popup-title {
                font-size: 32rpx;
                font-weight: 500;
                color: #333;
            }

            .close-icon {
                position: absolute;
                right: 24rpx;
                top: 50%;
                transform: translateY(-50%);
                color: #999;
            }
        }

        .detail-content {
            padding: 24rpx;
            max-height: 70vh;
            overflow-y: auto;
        }

        .detail-section {
            margin-bottom: 32rpx;

            .section-title {
                font-size: 28rpx;
                font-weight: 500;
                color: #666;
                margin-bottom: 16rpx;
                padding-left: 16rpx;
                border-left: 4rpx solid #1a237e;
            }

            .info-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 16rpx;
            }

            .info-item {
                background: #f8f9fa;
                padding: 16rpx;
                border-radius: 12rpx;

                .item-label {
                    font-size: 24rpx;
                    color: #666;
                    margin-bottom: 4rpx;
                    display: block;
                }

                .item-value {
                    font-size: 28rpx;
                    color: #333;
                    font-weight: 500;
                }
            }

            .time-stats {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 16rpx;

                .stat-item {
                    background: #1a237e;
                    color: white;
                    padding: 24rpx;
                    border-radius: 12rpx;
                    text-align: center;

                    .stat-value {
                        font-size: 36rpx;
                        font-weight: 600;
                        margin-bottom: 8rpx;
                        display: block;
                    }

                    .stat-label {
                        font-size: 24rpx;
                        opacity: 0.9;
                    }
                }
            }
        }
    }
    .scan-button {
        position: fixed;
        bottom: 48rpx;
        right: 48rpx;
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        padding: 24rpx 32rpx;
        border-radius: 40rpx;
        display: inline-flex; // 改为 inline-flex
        align-items: center; // 垂直居中对齐
        justify-content: center; // 水平居中对齐
        gap: 12rpx;
        box-shadow: 0 4rpx 12rpx rgba(26, 35, 126, 0.3);
        transition: all 0.3s ease;
        line-height: normal;
        font-size: 28rpx;

        // position: fixed;
        // bottom: 20px;
        // left: 50%;
        // transform: translateX(-50%);
        // z-index: 100;

        &:active {
            transform: scale(0.95);
        }

        // 移除额外的 camera-icon 容器
        :global(.nutui-iconfont) {
            // 针对 IconFont 的样式
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
        }

        .scan-text {
            font-size: 28rpx;
            line-height: 1;
            // 移除 margin-left，因为已经使用了 gap
        }
    }
}
