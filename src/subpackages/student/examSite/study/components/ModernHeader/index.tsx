import { View, Text } from "@tarojs/components";
import { useState, useEffect, useRef } from "react";
import { Calendar } from "@nutui/nutui-react-taro";
import dayjs from "dayjs";
import "./index.scss";
import IconFont from "@components/iconfont";

interface HeaderProps {
    todayCount?: number;
    onDateChange?: (date: string) => void;
    userInfo?: API.UserInfo;
    studentCount?: number;
}

const ModernHeader = ({ todayCount = 0, onDateChange, userInfo, studentCount }: HeaderProps) => {
    const [selectedDate, setSelectedDate] = useState(dayjs().format("YYYY-MM-DD"));
    const [showCalendar, setShowCalendar] = useState(false);
    const [isToday, setIsToday] = useState(true);
    const calendarRef = useRef(null);

    // 计算日期范围
    const today = new Date();
    const fiveDaysAgo = new Date(today);
    fiveDaysAgo.setDate(today.getDate() - 5);

    useEffect(() => {
        checkIfToday(selectedDate);
    }, [selectedDate]);

    const checkIfToday = (date: string) => {
        setIsToday(dayjs(date).isSame(dayjs(), "day"));
    };

    const handleDateSelect = (date: string) => {
        console.log("setShowCalendar");
        setShowCalendar(false);
        setSelectedDate(dayjs(date[3]).format("YYYY-MM-DD"));
        onDateChange?.(dayjs(date[3]).format("YYYY-MM-DD"));
    };

    const handleDateClick = () => {
        console.log("点击事件触发");
        setShowCalendar(true);
        console.log("showCalendar 值已更新为:", true);
    };

    const getDateDisplay = () => {
        if (isToday) {
            return "今天";
        }
        return dayjs(selectedDate).format("YYYY年MM月DD日");
    };

    const getDateString = (date: Date) => {
        return dayjs(date).format("YYYY-MM-DD");
    };

    return (
        <View className="modern-header">
            {/* 状态栏背景 */}
            {/* <View className="status-bar-bg" /> */}

            <View className="header-content">
                {/* 左侧日期区域 */}
                <View className="date-container" onClick={handleDateClick} onTap={handleDateClick}>
                    <View className="icon-wrapper">
                        <IconFont name="line-schedule" color={"#fff"} size={40} />
                    </View>
                    <View className="date-info">
                        <Text className="date-text">{getDateDisplay()}</Text>
                        <View className="stat-container">
                            <Text className="stat-text">训练</Text>
                            <Text className="stat-number">{studentCount}</Text>
                            <Text className="stat-text">人</Text>
                        </View>
                    </View>
                </View>

                {/* 右侧功能区 */}
                {/* 右侧用户信息和功能区 */}
                <View className="right-section">
                    {/* 用户信息 */}
                    {userInfo && (
                        <View className="user-info">
                            <View className="user-avatar">
                                <IconFont name="line-me" color="#fff" size={28} />
                            </View>
                            <View className="user-details">
                                <Text className="user-name">{userInfo.profile.nickname}</Text>
                                {/* <Text className="user-role">{userInfo.role}</Text> */}
                            </View>
                        </View>
                    )}

                    {/* 功能按钮区 */}
                    {/* <View className="action-buttons">
                        <View className="action-button">
                            <IconFont name="line-scan" color="#fff" size={24} />
                        </View>
                        <View className="action-button">
                            <IconFont name="line-control" color="#fff" size={24} />
                        </View>
                    </View> */}
                </View>
            </View>

            {/* 装饰性背景元素 */}
            <View className="header-decoration">
                <View className="decoration-circle" />
                <View className="decoration-line" />
            </View>

            {/* 日历选择器 */}
            <Calendar
                ref={calendarRef}
                visible={showCalendar}
                defaultValue={selectedDate}
                startDate={getDateString(fiveDaysAgo)} // 使用 startDate 替代 minDate
                endDate={getDateString(today)} // 使用 endDate 替代 maxDate
                onClose={() => setShowCalendar(false)}
                onConfirm={handleDateSelect}
                type="single"
            />
        </View>
    );
};

export default ModernHeader;
