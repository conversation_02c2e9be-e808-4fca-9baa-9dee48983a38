.modern-header {
    padding-top: 8vh;
    position: relative;
    background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
    padding-bottom: 20rpx;
    overflow: hidden;

    .status-bar-bg {
        height: var(--status-bar-height, 44px);
        background: rgba(0, 0, 0, 0.1);
    }

    .header-content {
        position: relative;
        z-index: 2;
        padding: 20rpx 20rpx 20rpx 32rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    // 左侧日期区域
    .date-container {
        // 修改这些属性
        padding: 20rpx 24rpx;
        height: 120rpx; // 新增固定高度
        box-sizing: border-box;

        display: flex;
        align-items: center;
        gap: 20rpx;
        padding: 16rpx 24rpx;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 16rpx;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.98);
            background: rgba(255, 255, 255, 0.15);
        }

        .icon-wrapper {
            width: 64rpx;
            height: 64rpx;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .calendar-icon {
                font-size: 32rpx;
                color: #fff;
            }
        }

        .date-info {
            display: flex;
            flex-direction: column;
            gap: 8rpx;

            .date-text {
                color: #fff;
                font-size: 36rpx;
                font-weight: 600;
                letter-spacing: 1rpx;
            }

            .stat-container {
                display: flex;
                align-items: center;
                gap: 8rpx;

                .stat-text {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 24rpx;
                }

                .stat-number {
                    color: #fff;
                    font-size: 28rpx;
                    font-weight: 600;
                    margin: 0 4rpx;
                }
            }
        }
    }
    // 右侧区域新样式
    .right-section {
        display: flex;
        align-items: center;
        gap: 24rpx;
        height: 120rpx;
        padding: 40rpx 0 0 0;
        box-sizing: border-box;

        // 用户信息
        .user-info {
            display: flex;
            align-items: center;
            gap: 12rpx;
            padding: 12rpx 20rpx;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16rpx;
            transition: all 0.3s ease;

            padding: 20rpx 24rpx;
            height: 100%;
            box-sizing: border-box;

            &:active {
                background: rgba(255, 255, 255, 0.15);
            }

            .user-avatar {
                width: 56rpx;
                height: 56rpx;
                background: rgba(255, 255, 255, 0.15);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .user-details {
                margin-left: 10rpx;
                display: flex;
                flex-direction: column;
                gap: 4rpx;

                .user-name {
                    color: #fff;
                    font-size: 26rpx;
                    font-weight: 500;
                }

                .user-role {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 22rpx;
                }
            }
        }

        // 功能按钮
        .action-buttons {
            display: flex;
            gap: 12rpx;

            width: 80rpx;
            height: 80rpx;
            margin-top: 20rpx;

            .action-button {
                width: 64rpx;
                height: 64rpx;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;

                &:active {
                    transform: scale(0.9);
                    background: rgba(255, 255, 255, 0.2);
                }
            }
        }
    }
    // 右侧功能按钮
    .action-container {
        display: flex;
        gap: 16rpx;

        .action-button {
            width: 72rpx;
            height: 72rpx;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;

            &:active {
                transform: scale(0.9);
                background: rgba(255, 255, 255, 0.2);
            }

            .nut-icon {
                font-size: 36rpx;
                color: #fff;
            }
        }
    }

    // 装饰性背景
    .header-decoration {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow: hidden;
        pointer-events: none;

        .decoration-circle {
            position: absolute;
            width: 400rpx;
            height: 400rpx;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            top: -200rpx;
            right: -100rpx;
            backdrop-filter: blur(10px);
        }

        .decoration-line {
            position: absolute;
            width: 200rpx;
            height: 2rpx;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transform: rotate(-45deg);
            bottom: 100rpx;
            left: 50rpx;
        }
    }
}

// Calendar 样式优化
:global {
    .nut-calendar {
        .nut-calendar-header {
            background: #1a237e;

            .calendar-title {
                color: #fff;
                font-size: 32rpx;
                font-weight: 500;
            }

            .close-icon {
                color: rgba(255, 255, 255, 0.9);
            }
        }

        .nut-calendar-week-day {
            color: #666;
            font-size: 28rpx;
        }

        .nut-calendar-day {
            font-size: 32rpx;

            &.current {
                background: #1a237e;
                color: #fff;
                font-weight: 500;
            }
        }
    }
}
