import "./styles/TrainingRecords.scss";
import { useState, useEffect } from "react";
import { Button, Calendar, Popup } from "@nutui/nutui-react-taro";
import Taro, { useReady } from "@tarojs/taro";
import request from "@service/request";
import { message } from "@components/MessageApi/MessageApiSingleton";
import dayjs from "dayjs";
import classNames from "classnames";
import ModernHeader from "./components/ModernHeader";
import IconFont from "@components/iconfont";
import Layout from "@components/Layout";
import { login } from "@utils/login";

interface TrainingRecord {
    Id: string;
    xm: string;
    sfzmhm: string;
    UpTime: string;
    FieldNickName: string;
    ItemName: string;
    SaleTime: string;
    KeMuText: string;
    duration?: number; // Training duration in minutes
    distance?: number; // Training distance in kilometers
    status?: string; // Status for styling
}

const TrainingRecordsPage = () => {
    const [selectedDate, setSelectedDate] = useState(dayjs().format("YYYY-MM-DD"));
    const [records, setRecords] = useState<TrainingRecord[]>([]);
    const [showRecordDetail, setShowRecordDetail] = useState(false);
    const [selectedRecord, setSelectedRecord] = useState<TrainingRecord | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    const fetchRecords = async () => {
        try {
            setIsLoading(true);
            const response = await request.post<{ data: { data: TrainingRecord[] } }>("/Jx/ExamSite/Wx/Study/getMyStudyList", {
                StudyDate: selectedDate,
                pageSize: 9999,
            });
            setRecords(response.data.data);
        } catch (error) {
            message.error("获取训练记录失败");
        } finally {
            setIsLoading(false);
        }
    };

    const [userInfo, setUserInfo] = useState<API.UserInfo>();
    // 获取用户信息
    useReady(async () => {
        try {
            const userInfo = await login();
            console.log("获取到用户信息:", userInfo);
            setUserInfo(userInfo);
            // 使用 userInfo 做后续操作
        } catch (error) {
            console.error("获取用户信息失败:", error);
        }
    });

    // Second useEffect for fetchRecords - runs when selectedDate changes
    useEffect(() => {
        if (!userInfo) return;

        fetchRecords();
        const interval = setInterval(fetchRecords, 20000);

        return () => clearInterval(interval);
    }, [selectedDate, userInfo]);

    const handleScan = async () => {
        try {
            const res = await Taro.scanCode({
                onlyFromCamera: true,
            });

            message.openLoading("验证中...");
            const verifyResponse: any = await request.post("/Jx/ExamSite/Wx/Study/getMyStudyList", {
                voucherCode: res.result,
            });

            if (verifyResponse.success) {
                message.success("验证成功");
                setRecords((prev) => [verifyResponse.data, ...prev]);
            } else {
                message.error("无效的电子券");
            }
        } catch (error) {
            message.error("扫码失败");
        } finally {
            message.closeLoading();
        }
    };

    const handleDateChange = (date: string) => {
        console.log(date);
        console.log("selected date:", date);
        setSelectedDate(date);
    };

    return (
        <Layout>
            <view className="training-records-container">
                {/* Elegant Header */}
                <ModernHeader todayCount={5} onDateChange={handleDateChange} userInfo={userInfo} studentCount={records.length} />

                {/* Styled Records List */}
                <view className="records-list">
                    {isLoading ? (
                        <view className="loading-state">加载中...</view>
                    ) : records.length === 0 ? (
                        <view className="empty-state">
                            {/* <Icon name="notice" size="40" className="empty-icon" /> */}
                            <text className="empty-text">暂无训练记录</text>
                        </view>
                    ) : (
                        records.map((record) => (
                            <view
                                key={record.Id}
                                className={classNames("record-card", record.status)}
                                onClick={() => {
                                    setSelectedRecord(record);
                                    setShowRecordDetail(true);
                                }}
                            >
                                <view className="record-main">
                                    <view className="record-primary">
                                        <text className="student-name">{record.xm == "" ? "NULL" : record.xm}</text>
                                        <text className="training-field">{record.KeMuText}</text>
                                    </view>
                                    <view className="record-secondary">
                                        <text className="time-label">上车时间:</text>
                                        <text className="start-time">{record.UpTime}</text>
                                    </view>
                                </view>
                                {/* <Icon name="right" size="20" className="chevron-icon" /> */}
                            </view>
                        ))
                    )}
                </view>

                {/* Enhanced Detail Popup */}
                <Popup visible={showRecordDetail} position="bottom" round onClose={() => setShowRecordDetail(false)}>
                    {selectedRecord && (
                        <view className="detail-popup">
                            <view className="popup-header">
                                <text className="popup-title">训练详情</text>
                                {/* <Icon name="close" size="20" className="close-icon" onClick={() => setShowRecordDetail(false)} /> */}
                            </view>
                            <view className="detail-content">
                                <view className="detail-section">
                                    <view className="section-title">基本信息</view>
                                    <view className="info-grid">
                                        <view className="info-item">
                                            <text className="item-label">学员姓名</text>
                                            <text className="item-value">{selectedRecord.xm}</text>
                                        </view>
                                        <view className="info-item">
                                            <text className="item-label">证件号码</text>
                                            <text className="item-value">{selectedRecord.sfzmhm}</text>
                                        </view>
                                    </view>
                                </view>

                                <view className="detail-section">
                                    <view className="section-title">训练信息</view>
                                    <view className="info-grid">
                                        <view className="info-item">
                                            <text className="item-label">所属场地</text>
                                            <text className="item-value">{selectedRecord.FieldNickName}</text>
                                        </view>
                                        <view className="info-item">
                                            <text className="item-label">电券名称</text>
                                            <text className="item-value">{selectedRecord.ItemName}</text>
                                        </view>
                                        <view className="info-item">
                                            <text className="item-label">购买时间</text>
                                            <text className="item-value">{selectedRecord.SaleTime}</text>
                                        </view>
                                    </view>
                                </view>

                                <view className="detail-section">
                                    <view className="section-title">训练统计</view>
                                    <view className="time-stats">
                                        <view className="stat-item">
                                            <text className="stat-value">{selectedRecord.duration}分钟</text>
                                            <text className="stat-label">训练时长</text>
                                        </view>
                                        <view className="stat-item">
                                            <text className="stat-value">{selectedRecord.distance}公里</text>
                                            <text className="stat-label">训练距离</text>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    )}
                </Popup>

                {/* Floating Scan Button */}
                {/* <view className="scan-button" onClick={handleScan}>
                    <IconFont name="line-scan" color={"#fff"} size={30} />
                    <text className="scan-text">扫描电子券</text>
                </view> */}

                <Button className="scan-button" icon={<IconFont name="line-scan" color={"#fff"} size={30} />} onClick={handleScan}>
                    扫描电子券
                </Button>
            </view>
        </Layout>
    );
};

export default TrainingRecordsPage;
