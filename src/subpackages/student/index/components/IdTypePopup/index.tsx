import { View } from "@tarojs/components";
import { Close } from "@nutui/icons-react-taro";
import "./index.scss";

interface IdTypePopupProps {
    visible: boolean;
    idTypeList: Array<{ label: string; value: string }>;
    selectedIdType: { label: string; value: string } | null;
    handleClose: () => void;
    handleSelect: (item: { label: string; value: string }) => void;
}

const IdTypePopup: React.FC<IdTypePopupProps> = ({ visible, idTypeList, selectedIdType, handleClose, handleSelect }) => {
    if (!visible) return null;

    return (
        <view className="popup-overlay" onClick={handleClose}>
            <view className="popup-container" onClick={(e) => e.stopPropagation()}>
                <view className="popup-header">
                    <text className="popup-title">选择证件类型</text>
                    <view className="popup-close" onClick={handleClose}>
                        <Close />
                    </view>
                </view>
                <view className="popup-content">
                    {idTypeList.map((item, index) => (
                        <view
                            key={index}
                            className={`popup-option ${selectedIdType?.value === item.value ? "selected" : ""}`}
                            onClick={() => {
                                handleSelect(item);
                                handleClose();
                            }}
                        >
                            {item.label}
                        </view>
                    ))}
                </view>
            </view>
        </view>
    );
};

export default IdTypePopup;
