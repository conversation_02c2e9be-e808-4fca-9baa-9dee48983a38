.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: flex-end;
}

.popup-container {
    width: 100%;
    background-color: #fff;
    border-radius: 32rpx 32rpx 0 0;
    padding: 32rpx;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 32rpx;
    border-bottom: 1rpx solid #eee;
}

.popup-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
}

.popup-close {
    padding: 8rpx;
    color: #999;
}

.popup-content {
    max-height: 60vh;
    overflow-y: auto;
}

.popup-option {
    padding: 32rpx;
    font-size: 28rpx;
    color: #333;
    border-bottom: 1rpx solid #f5f5f5;

    &.selected {
        color: #1890ff;
        background-color: #f0f9ff;
    }

    &:active {
        background-color: #f5f5f5;
    }
}

.id-type-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
}
