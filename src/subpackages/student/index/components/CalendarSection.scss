/* 自定义打卡日历样式 */
.custom-calendar-wrapper {
    background: #fff;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 24rpx rgba(99, 69, 237, 0.06);
    padding: 32rpx 0 32rpx 0;
    // margin: 16rpx 24rpx;
    // margin-bottom: 16rpx;
    // position: sticky;
    top: 32rpx;
    z-index: 10;
    overflow: hidden;

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 8rpx;
        background: linear-gradient(90deg, #6345ed, #4568dc);
    }
}

.calendar-header-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx 16rpx 32rpx;
    position: relative;
}

.calendar-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;

    .svg-calendar-icon {
        display: inline-block;
        width: 50rpx;
        height: 50rpx;
        margin-right: 20rpx;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236345ed' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3Ccircle cx='12' cy='15' r='2'%3E%3C/circle%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: center;
        background-size: 60%;
        border-radius: 16rpx;
        background-color: rgba(99, 69, 237, 0.1);
        position: relative;
        overflow: hidden;

        &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
            border-radius: 16rpx;
        }
    }
}

.calendar-month-switch {
    display: flex;
    align-items: center;
    gap: 16rpx;
    background: rgba(99, 69, 237, 0.05);
    padding: 8rpx 16rpx;
    border-radius: 12rpx;
}

.calendar-arrow {
    font-size: 32rpx;
    color: #6345ed;
    padding: 0 12rpx;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;

    &:active {
        opacity: 0.7;
    }
}

.calendar-month-label {
    font-size: 28rpx;
    color: #6345ed;
    margin: 0 8rpx;
    font-weight: 500;
}

.calendar-weekdays {
    display: flex;
    justify-content: space-between;
    padding: 0 16rpx;
    margin-bottom: 8rpx;
}

.calendar-weekday {
    flex: 1;
    text-align: center;
    color: #b0b0b0;
    font-size: 26rpx;
    font-weight: 500;
}

.calendar-days-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 12rpx;
    padding: 0 16rpx;
}

.calendar-day {
    width: 100%;
    aspect-ratio: 1/1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #bbb;
    background: #f5f6fa;
    transition: background 0.2s, color 0.2s;

    &.punch {
        background: #5fd37d;
        color: #fff;
        font-weight: 600;
    }

    &.less {
        background: #d0e6d7;
        color: #3a7c4a;
        font-weight: 500;
    }

    &.today {
        border: 2rpx solid #5677fc;
        color: #5677fc;
        background: #eaf1ff;
        font-weight: 600;
    }

    &.empty {
        background: transparent;
        color: transparent;
        pointer-events: none;
    }
}

.calendar-footer {
    text-align: right;
    padding: 12rpx 32rpx 0 0;
    color: #b0b0b0;
    font-size: 24rpx;
}
