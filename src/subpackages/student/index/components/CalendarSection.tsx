import { FC, useState, useEffect } from "react";
import dayjs from "dayjs";
import "./CalendarSection.scss";

interface PracticeStats {
    totalPunchCount: number;
    days: Array<{ date: string; minutes: number }>;
}

interface CalendarDay {
    day: number;
    status: "normal" | "less" | "punch";
    isToday: boolean;
}

interface CalendarSectionProps {
    userInfo: API.UserInfo | null;
}

const CalendarSection: FC<CalendarSectionProps> = ({ userInfo }) => {
    const today = dayjs();
    const [calendarYear, setCalendarYear] = useState(today.year());
    const [calendarMonth, setCalendarMonth] = useState(today.month()); // 0-based
    const [calendarGrid, setCalendarGrid] = useState<CalendarDay[]>([]);

    const getPracticeStats = (userInfo: API.UserInfo | null): PracticeStats => {
        return (userInfo as any)?.practiceStats || { totalPunchCount: 0, days: [] };
    };

    // 生成日历格子
    const generateCalendarGrid = (year: number, month: number, stats: PracticeStats): CalendarDay[] => {
        const firstDay = dayjs(`${year}-${month + 1}-01`);
        const daysInMonth = firstDay.daysInMonth();
        const startWeek = firstDay.day() === 0 ? 6 : firstDay.day() - 1; // 以周一为第一天
        const grid: CalendarDay[] = [];
        // 统计练车天数
        const punchMap: Record<string, number> = {};
        stats.days.forEach((d) => {
            punchMap[d.date] = d.minutes;
        });

        // 添加前一个月的空白天数
        for (let i = 0; i < startWeek; i++) {
            grid.push(null as any);
        }

        // 添加当前月的天数
        for (let d = 1; d <= daysInMonth; d++) {
            const dateStr = dayjs(`${year}-${month + 1}-${d}`).format("YYYY-MM-DD");
            let status: "normal" | "less" | "punch" = "normal";
            if (punchMap[dateStr] > 0) {
                status = punchMap[dateStr] < 30 ? "less" : "punch";
            }
            grid.push({
                day: d,
                status,
                isToday: today.year() === year && today.month() === month && today.date() === d,
            });
        }

        // 计算需要补充的空白天数，确保总格子数是7的倍数
        const remainingDays = 7 - (grid.length % 7);
        if (remainingDays < 7) {
            for (let i = 0; i < remainingDays; i++) {
                grid.push(null as any);
            }
        }

        return grid;
    };

    const refreshCalendar = (year: number, month: number, userInfo: API.UserInfo | null) => {
        const stats = getPracticeStats(userInfo);
        setCalendarGrid(generateCalendarGrid(year, month, stats));
    };

    // 切换月份
    const handleMonthChange = (delta: number) => {
        let y = calendarYear;
        let m = calendarMonth + delta;
        if (m < 0) {
            y -= 1;
            m = 11;
        } else if (m > 11) {
            y += 1;
            m = 0;
        }
        setCalendarYear(y);
        setCalendarMonth(m);
        refreshCalendar(y, m, userInfo);
    };

    // userInfo 或年月变化时刷新日历
    useEffect(() => {
        if (userInfo) {
            refreshCalendar(calendarYear, calendarMonth, userInfo);
        }
    }, [userInfo, calendarYear, calendarMonth]);

    if (!userInfo) return null;

    return (
        <view className="custom-calendar-wrapper">
            <view className="calendar-header-row">
                <text className="calendar-title">
                    <text className="svg-calendar-icon" />
                </text>
                <view className="calendar-month-switch">
                    <text className="calendar-arrow" onClick={() => handleMonthChange(-1)}>
                        {"<"}
                    </text>
                    <text className="calendar-month-label">
                        {calendarYear}年{calendarMonth + 1}月
                    </text>
                    <text className="calendar-arrow" onClick={() => handleMonthChange(1)}>
                        {">"}
                    </text>
                </view>
            </view>
            <view className="calendar-weekdays">
                {["一", "二", "三", "四", "五", "六", "日"].map((d) => (
                    <text className="calendar-weekday" key={d}>
                        {d}
                    </text>
                ))}
            </view>
            <view className="calendar-days-grid">
                {calendarGrid.map((day, idx) => {
                    if (!day) {
                        return <view className="calendar-day empty" key={idx}></view>;
                    }
                    let className = "calendar-day";
                    let label: string | number = day.day;
                    if (day.isToday) {
                        className += " today";
                        label = "今";
                    }
                    if (day.status === "normal") {
                        // 未练车
                    } else if (day.status === "less") {
                        className += " less";
                    } else if (day.status === "punch") {
                        className += " punch";
                    }
                    return (
                        <view className={className} key={idx}>
                            <text>{label}</text>
                        </view>
                    );
                })}
            </view>
            <view className="calendar-footer">
                <text>累计打卡{getPracticeStats(userInfo).totalPunchCount || 0}次</text>
            </view>
        </view>
    );
};

export default CalendarSection;
