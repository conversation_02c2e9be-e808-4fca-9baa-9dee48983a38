import Taro from "@tarojs/taro";
import "./index.scss";
import request from "@/service/request";
import { GlobalData } from "@/utils/globalData";
import Layout from "@/components/Layout";
import { message } from "@/components/MessageApi/MessageApiSingleton";

interface MenuSectionProps {
    expandedMenus: Record<string, boolean>;
    toggleMenu: (menuKey: string) => void;
    userInfo: API.UserInfo | null;
    handleLogout: () => void;
    loading?: boolean;
}

const MenuSection: React.FC<MenuSectionProps> = ({ expandedMenus, toggleMenu, userInfo, handleLogout, loading = false }) => {
    const navigateTo = (url: string) => {
        Taro.navigateTo({ url });
    };

    return (
        <Layout>
            <view className="menu-content">
                {loading && (
                    <view className="menu-loading-mask">
                        <view className="menu-loading-spinner"></view>
                    </view>
                )}
                {/* 考试练车菜单组 */}
                <view className="menu-card">
                    <view className="card-highlight"></view>
                    <view className="menu-header" onClick={() => toggleMenu("appointment")}>
                        <view className="menu-title">
                            <view className="icon-bubble">
                                <view className="svg-icon svg-calendar-icon"></view>
                            </view>
                            <text className="menu-title-text">考试培训</text>
                        </view>
                        <view className={`menu-arrow ${expandedMenus.appointment ? "expanded" : ""}`}>
                            <view className={`svg-icon ${expandedMenus.appointment ? "svg-arrow-up-icon" : "svg-arrow-down-icon"}`}></view>
                        </view>
                    </view>

                    {expandedMenus.appointment && (
                        <view className="menu-items">
                            <view className="menu-item" onClick={() => navigateTo("/subpackages/student/appointment/booking")}>
                                <text>我要约车</text>
                                <view className="item-action">
                                    <view className="svg-icon svg-arrow-right-icon"></view>
                                </view>
                            </view>
                            <view className="menu-item" onClick={() => navigateTo("/subpackages/student/appointment/records")}>
                                <text>约车记录</text>
                                <view className="item-action">
                                    <view className="svg-icon svg-arrow-right-icon"></view>
                                </view>
                            </view>
                            <view className="menu-item" onClick={() => navigateTo("/subpackages/student/exam/index/index")}>
                                <text>考试记录</text>
                                <view className="item-action">
                                    <view className="svg-icon svg-arrow-right-icon"></view>
                                </view>
                            </view>
                        </view>
                    )}
                </view>

                {userInfo && (
                    <>
                        {/* 驾校缴费菜单组 */}
                        <view className="menu-card">
                            <view className="card-highlight"></view>
                            <view className="menu-header" onClick={() => toggleMenu("payment")}>
                                <view className="menu-title">
                                    <view className="icon-bubble">
                                        <view className="svg-icon svg-payment-icon"></view>
                                    </view>
                                    <text className="menu-title-text">驾校业务</text>
                                </view>
                                <view className={`menu-arrow ${expandedMenus.payment ? "expanded" : ""}`}>
                                    <view className={`svg-icon ${expandedMenus.payment ? "svg-arrow-up-icon" : "svg-arrow-down-icon"}`}></view>
                                </view>
                            </view>

                            {expandedMenus.payment && (
                                <view className="menu-items">
                                    <view className="menu-item" onClick={() => navigateTo("/subpackages/student/contract/index/index")}>
                                        <text>我的合同</text>
                                        <view className="item-action">
                                            <view className="svg-icon svg-arrow-right-icon"></view>
                                        </view>
                                    </view>
                                    <view className="menu-item" onClick={() => navigateTo("/subpackages/student/pay/index/index")}>
                                        <text>我的欠费</text>
                                        <view className="item-action">
                                            <view className="svg-icon svg-arrow-right-icon"></view>
                                        </view>
                                    </view>
                                    <view className="menu-item" onClick={() => navigateTo("/subpackages/student/hnRefund/list/index")}>
                                        <text>我的退费</text>
                                        <view className="item-action">
                                            <view className="svg-icon svg-arrow-right-icon"></view>
                                        </view>
                                    </view>
                                </view>
                            )}
                        </view>
                    </>
                )}

                {/* 考场排队菜单组 */}
                <view className="menu-card">
                    <view className="card-highlight"></view>
                    <view className="menu-header" onClick={() => toggleMenu("examQueue")}>
                        <view className="menu-title">
                            <view className="icon-bubble">
                                <view className="svg-icon svg-queue-icon"></view>
                            </view>
                            <text className="menu-title-text">考场排队</text>
                        </view>
                        <view className={`menu-arrow ${expandedMenus.examQueue ? "expanded" : ""}`}>
                            <view className={`svg-icon ${expandedMenus.examQueue ? "svg-arrow-up-icon" : "svg-arrow-down-icon"}`}></view>
                        </view>
                    </view>

                    {expandedMenus.examQueue && (
                        <view className="menu-items">
                            <view className="menu-item" onClick={() => navigateTo("/subpackages/student/examSite/myQueue/index")}>
                                <text>我的排队</text>
                                <view className="item-action">
                                    <view className="svg-icon svg-arrow-right-icon"></view>
                                </view>
                            </view>
                            <view className="menu-item" onClick={() => navigateTo("/subpackages/student/examSite/mySale/index")}>
                                <text>我的电券</text>
                                <view className="item-action">
                                    <view className="svg-icon svg-arrow-right-icon"></view>
                                </view>
                            </view>
                        </view>
                    )}
                </view>

                {/* 退出系统按钮 */}
                {userInfo && (
                    <view
                        className="logout-button"
                        onClick={() => {
                            console.log("message", message);
                            message.openConfirm("是否确认退出系统?", "确认操作", () => {
                                message.openLoading("正在退出系统");

                                Taro.login().then((loginResult) => {
                                    request.post<any>("/Jx/Student/WxLogin/logOut/" + loginResult.code, {}).then(() => {
                                        message.closeLoading();
                                        handleLogout();
                                        const entryPage = GlobalData("entryPagePath", undefined) || "/pages/role-selection/index";
                                        Taro.reLaunch({
                                            url: entryPage,
                                        });
                                    });
                                });
                            });
                        }}
                    >
                        <view className="logout-icon-wrap">
                            <view className="svg-icon svg-logout-icon"></view>
                        </view>
                        <text className="logout-text">退出系统</text>
                        <view className="arrow-spacer"></view>
                    </view>
                )}
            </view>
        </Layout>
    );
};

export default MenuSection;
