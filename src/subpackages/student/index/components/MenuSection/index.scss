.menu-content {
    padding: 0 24rpx;
    position: relative;
    z-index: 20;
}

.menu-loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    border-radius: 16rpx;
}

.menu-loading-spinner {
    width: 48rpx;
    height: 48rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #5a60f5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.menu-card {
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    border-radius: 16rpx;
    margin-bottom: 16rpx;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04), 0 1rpx 3rpx rgba(0, 0, 0, 0.03);
    transition: all 0.2s ease;
    border: 1rpx solid rgba(230, 236, 240, 0.8);

    // 顶部装饰条
    .card-highlight {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4rpx;
        background: linear-gradient(90deg, #5a60f5, #2f63ed);
        opacity: 0.8;
    }

    // 右上角装饰点
    &::after {
        content: "";
        position: absolute;
        top: 12rpx;
        right: 12rpx;
        width: 6rpx;
        height: 6rpx;
        border-radius: 50%;
        background: #5a60f5;
        opacity: 0.4;
    }

    &:active {
        transform: translateY(1rpx);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
    }
}

.menu-header {
    padding: 20rpx 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;

    &:active {
        background-color: rgba(249, 250, 251, 0.8);
    }
}

.menu-title {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.icon-bubble {
    width: 48rpx;
    height: 48rpx;
    border-radius: 12rpx;
    background: linear-gradient(145deg, #eef2ff, #f0f4ff);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border: 1rpx solid rgba(229, 231, 235, 0.5);
    box-shadow: 0 2rpx 6rpx rgba(86, 97, 179, 0.06);

    .svg-icon {
        width: 28rpx;
        height: 28rpx;
        color: #5a60f5;
    }
}

.menu-title-text {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    text-shadow: 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.menu-arrow {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
    position: relative;

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(249, 250, 251, 0.6);
        border-radius: 50%;
        transform: scale(0);
        transition: transform 0.2s ease;
    }

    &:active::before {
        transform: scale(1);
    }

    &.expanded {
        transform: rotate(-180deg);
    }

    .svg-icon {
        width: 24rpx;
        height: 24rpx;
        opacity: 0.4;
    }
}

.menu-items {
    border-top: 1rpx solid rgba(229, 231, 235, 0.6);
    background: linear-gradient(180deg, rgba(250, 251, 252, 0.8), rgba(255, 255, 255, 0.95));
}

.menu-item {
    padding: 20rpx 24rpx;
    padding-left: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    transition: background-color 0.2s ease;

    // 左侧装饰线
    &::before {
        content: "";
        position: absolute;
        left: 48rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 4rpx;
        height: 4rpx;
        border-radius: 50%;
        background-color: rgba(90, 96, 245, 0.4);
    }

    &:active {
        background-color: rgba(249, 250, 251, 0.9);
    }

    text {
        font-size: 26rpx;
        color: #5e5f66;
        position: relative;
    }

    .item-action {
        display: flex;
        align-items: center;

        .svg-icon {
            width: 24rpx;
            height: 24rpx;
            opacity: 0.3;
            transition: transform 0.2s ease, opacity 0.2s ease;
        }
    }

    &:active .item-action .svg-icon {
        transform: translateX(2rpx);
        opacity: 0.5;
    }
}

// 退出按钮样式
.logout-button {
    background: linear-gradient(145deg, #ffffff, #fff9f9);
    border-radius: 16rpx;
    padding: 20rpx 24rpx;
    display: flex;
    align-items: center;
    margin: 16rpx 0;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04), 0 1rpx 3rpx rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
    border: 1rpx solid rgba(255, 229, 229, 0.6);

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4rpx;
        background: linear-gradient(90deg, #ff4d4f, #ff7875);
        opacity: 0.8;
    }

    &:active {
        transform: translateY(1rpx);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
    }

    .logout-icon-wrap {
        width: 48rpx;
        height: 48rpx;
        border-radius: 12rpx;
        background: linear-gradient(145deg, #fff0f0, #fff5f5);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16rpx;
        border: 1rpx solid rgba(255, 229, 229, 0.8);
        box-shadow: 0 2rpx 6rpx rgba(255, 77, 79, 0.08);

        .svg-icon {
            width: 28rpx;
            height: 28rpx;
            color: #ff4d4f;
        }
    }

    .logout-text {
        font-size: 28rpx;
        font-weight: 500;
        color: #ff4d4f;
        text-shadow: 0 1rpx 0 rgba(255, 255, 255, 0.8);
    }

    // 右侧装饰
    &::after {
        content: "";
        position: absolute;
        top: 12rpx;
        right: 12rpx;
        width: 6rpx;
        height: 6rpx;
        border-radius: 50%;
        background: #ff4d4f;
        opacity: 0.4;
    }
}

// 添加分隔线的样式
.menu-item:not(:last-child) {
    position: relative;

    &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 88rpx;
        right: 24rpx;
        height: 1rpx;
        background: linear-gradient(to right, transparent, rgba(229, 231, 235, 0.6), transparent);
    }
}

// 为每个菜单组添加不同的强调色
.menu-card:nth-child(1) {
    .card-highlight {
        background: linear-gradient(90deg, #5a60f5, #2f63ed);
    }
    .icon-bubble {
        background: linear-gradient(145deg, #eef2ff, #f0f4ff);
        .svg-icon {
            color: #5a60f5;
        }
    }
}

.menu-card:nth-child(2) {
    .card-highlight {
        background: linear-gradient(90deg, #6366f1, #4f46e5);
    }
    .icon-bubble {
        background: linear-gradient(145deg, #eef2ff, #ede9fe);
        .svg-icon {
            color: #6366f1;
        }
    }
}

.menu-card:nth-child(3) {
    .card-highlight {
        background: linear-gradient(90deg, #3b82f6, #2563eb);
    }
    .icon-bubble {
        background: linear-gradient(145deg, #eff6ff, #dbeafe);
        .svg-icon {
            color: #3b82f6;
        }
    }
}
