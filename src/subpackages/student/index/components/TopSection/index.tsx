import { View, Text, Image } from "@tarojs/components";
import "./index.scss";

interface TopSectionProps {
    statusBarHeight: number;
    handleStudentLogin: () => void;
    loading?: boolean;
    userInfo: API.UserInfo | null;
}

const TopSection: React.FC<TopSectionProps> = ({ statusBarHeight, handleStudentLogin, loading = false, userInfo }) => {
    return (
        <View className="top-section">
            {/* 状态栏占位 */}
            <View className="top-section__status-bar" style={{ height: `${statusBarHeight + 100}rpx` }} />

            {/* 主要内容区域 */}
            <View className="top-section__content">
                <View className="top-section__left">
                    <Image className="top-section__logo" src="https://cdn.51panda.com/student.png" mode="aspectFit" />
                    <View className="top-section__text-container">
                        <Text className="top-section__title">全能考场</Text>
                        <Text className="top-section__subtitle">让学车更简单</Text>
                    </View>
                </View>
                <View className="top-section__right">
                    {userInfo?.RealName ? (
                        <View className="top-section__user-info">
                            <Text className="top-section__user-name">{userInfo.RealName}</Text>
                            {userInfo.TenantName && <Text className="top-section__tenant-name">{userInfo.TenantName}</Text>}
                        </View>
                    ) : (
                        <View className={`top-section__login-btn ${loading ? "loading" : ""}`} onClick={handleStudentLogin}>
                            <Text className="top-section__login-text">{loading ? "正在验证" : "学员登录"}</Text>
                        </View>
                    )}
                </View>
            </View>
        </View>
    );
};

export default TopSection;
