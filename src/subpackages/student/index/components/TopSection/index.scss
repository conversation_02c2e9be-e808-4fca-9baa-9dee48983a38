// 从主样式文件中提取相关样式

.top-section {
    // z-index: 100;
    background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
    width: 100%;
    position: relative;
    overflow: hidden;
    padding-top: 20rpx;
    // padding-bottom: 40rpx;

    // Add subtle background pattern
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%), linear-gradient(225deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%),
            linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%), linear-gradient(315deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%);
        background-size: 40rpx 40rpx;
        opacity: 0.6;
        pointer-events: none;
    }

    // Add decorative elements
    &::after {
        content: "";
        position: absolute;
        bottom: -60rpx;
        right: -60rpx;
        width: 200rpx;
        height: 200rpx;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
        border-radius: 50%;
        pointer-events: none;
        animation: pulse 4s ease-in-out infinite;
    }

    &__status-bar {
        width: 100%;
        background: transparent;
        height: var(--status-bar-height);
    }

    &__content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx 40rpx;
        position: relative;
        min-height: 160rpx;
    }

    &__left {
        display: flex;
        align-items: center;
        gap: 32rpx;
    }

    &__logo {
        width: 96rpx;
        height: 96rpx;
        border-radius: 28rpx;
        background: rgba(255, 255, 255, 0.95);
        padding: 12rpx;
        box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.2);
        border: 2rpx solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
            border-radius: 24rpx 24rpx 0 0;
        }

        &:active {
            transform: scale(0.95) translateY(2rpx);
            box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
        }
    }

    &__text-container {
        display: flex;
        flex-direction: column;
        gap: 8rpx;
    }

    &__title {
        color: #ffffff;
        font-size: 44rpx;
        font-weight: 600;
        letter-spacing: 1rpx;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
        background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0.9));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        position: relative;

        &::after {
            content: "";
            position: absolute;
            bottom: -8rpx;
            left: 0;
            width: 48rpx;
            height: 4rpx;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 4rpx;
        }
    }

    &__subtitle {
        color: rgba(255, 255, 255, 0.85);
        font-size: 28rpx;
        font-weight: 400;
        letter-spacing: 2rpx;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    }

    &__right {
        flex-shrink: 0;
        position: relative;
    }

    &__user-info {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 4rpx;
    }

    &__user-name {
        color: #ffffff;
        font-size: 32rpx;
        font-weight: 500;
        letter-spacing: 2rpx;
    }

    &__tenant-name {
        color: rgba(255, 255, 255, 0.8);
        font-size: 24rpx;
        font-weight: 400;
        letter-spacing: 1rpx;
    }

    &__login-btn {
        background: rgba(255, 255, 255, 0.15);
        border: 1.5rpx solid rgba(255, 255, 255, 0.3);
        border-radius: 56rpx;
        padding: 20rpx 44rpx;
        backdrop-filter: blur(8px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15), 0 0 24rpx rgba(255, 255, 255, 0.1), inset 0 1rpx 1rpx rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;

        &.loading {
            background: transparent;
            border: none;
            box-shadow: none;
            padding: 20rpx 0;
            opacity: 0.8;
            cursor: not-allowed;
            pointer-events: none;
        }

        &::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 50%;
            height: 100%;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
            transform: skewX(-25deg);
            animation: shimmer 3s infinite;
        }

        &:active {
            transform: translateY(2rpx) scale(0.98);
            box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12), 0 0 16rpx rgba(255, 255, 255, 0.08);
        }
    }

    &__login-text {
        color: #ffffff;
        font-size: 32rpx;
        font-weight: 500;
        letter-spacing: 2rpx;
        position: relative;
    }
}

.user-info {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
}

.avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 60rpx;
    border: 4rpx solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;

    &:active {
        transform: scale(0.95);
    }
}

.info-content {
    margin-left: 24rpx;
    flex: 1;
}

.welcome-text {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 8rpx;
}

.login-button {
    display: inline-flex;
    align-items: center;
    padding: 16rpx 32rpx;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 32rpx;
    font-size: 28rpx;
    color: #fff;
    transition: all 0.3s ease;

    &:active {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(2rpx);
    }

    .icon {
        margin-left: 8rpx;
        font-size: 24rpx;
    }
}

.stats-row {
    display: flex;
    justify-content: space-between;
    padding: 24rpx 32rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 24rpx;
    backdrop-filter: blur(10px);
}

.stat-item {
    text-align: center;
    padding: 0 24rpx;
    position: relative;

    &:not(:last-child)::after {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        height: 40rpx;
        width: 2rpx;
        background: rgba(255, 255, 255, 0.2);
    }
}

.stat-value {
    font-size: 36rpx;
    font-weight: 600;
    color: #fff;
    margin-bottom: 8rpx;
}

.stat-label {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
}

// 修改波浪动画相关样式
.wave-animation {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: calc(120rpx + env(safe-area-inset-bottom));
    padding-bottom: env(safe-area-inset-bottom);
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.02));
    backdrop-filter: blur(4px);
    z-index: 97;

    &::before {
        content: "";
        position: absolute;
        top: -2rpx;
        left: 0;
        right: 0;
        height: 2rpx;
        background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2) 20%, rgba(255, 255, 255, 0.2) 80%, transparent);
    }

    &::after {
        content: "";
        position: absolute;
        bottom: env(safe-area-inset-bottom);
        left: 50%;
        transform: translateX(-50%);
        width: 200rpx;
        height: 4rpx;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 4rpx;
    }
}

// 修改波浪动画
@keyframes wave {
    0% {
        transform: translateX(0) translateZ(0);
    }
    50% {
        transform: translateX(-25%) translateZ(0);
    }
    100% {
        transform: translateX(-50%) translateZ(0);
    }
}

// 添加渐变动画
@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

// Add animation for status bar transition
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-in {
    animation: fadeIn 0.3s ease-out forwards;
}

// 添加呼吸光效动画
@keyframes pulse {
    0% {
        opacity: 0.5;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.2);
    }
    100% {
        opacity: 0.5;
        transform: scale(1);
    }
}

// 添加光泽动画
@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 200%;
    }
}
