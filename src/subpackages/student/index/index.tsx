import { FC, useState, useEffect, useRef } from "react";
import { View, Text } from "@tarojs/components";
import Taro, { useLoad } from "@tarojs/taro";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import { login, LOGIN_LOADING_STATUS } from "@/utils/login";
import MenuSection from "./components/MenuSection";
import CalendarSection from "./components/CalendarSection";
import StudentLoginPopup from "@/components/StudentLoginPopup";
import TopSection from "./components/TopSection";
import { Calendar, Close } from "@nutui/icons-react-taro";
import "./index.scss";

const StudentIndex: FC = () => {
    const [userInfo, setUserInfo] = useState<API.UserInfo | null>(null);
    const [showCalendarModal, setShowCalendarModal] = useState(false);
    const [showCalendarText, setShowCalendarText] = useState(true);
    const [loading, setLoading] = useState(false);
    const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({
        appointment: true,
        examQueue: true,
        payment: true,
    });
    const [statusBarHeight, setStatusBarHeight] = useState(0);
    const studentLoginRef = useRef<any>(null);

    useLoad(() => {
        fetchUserInfo();
        Taro.getSystemInfo({}).then((res) => {
            setStatusBarHeight(res.statusBarHeight || 0);
        });
        // Hide calendar text after 3 seconds
        setTimeout(() => {
            setShowCalendarText(false);
        }, 3000);

        // 监听登录状态事件
        const handleLoginStart = () => setLoading(true);
        const handleLoginEnd = () => setLoading(false);

        Taro.eventCenter.on(LOGIN_LOADING_STATUS.START, handleLoginStart);
        Taro.eventCenter.on(LOGIN_LOADING_STATUS.END, handleLoginEnd);

        // 清理事件监听
        return () => {
            Taro.eventCenter.off(LOGIN_LOADING_STATUS.START, handleLoginStart);
            Taro.eventCenter.off(LOGIN_LOADING_STATUS.END, handleLoginEnd);
        };
    });

    useEffect(() => {
        if (userInfo && !userInfo.RealName) {
            studentLoginRef.current?.open();
        }
    }, [userInfo]);

    const fetchUserInfo = async () => {
        try {
            const userData = await login(true); // 使用学生登录
            setUserInfo(userData);
        } catch (error) {
            console.error("获取学员信息失败:", error);
            // message.error("获取学员信息失败");
        }
    };

    const handleLoginSuccess = () => {
        fetchUserInfo();
    };

    const handleLogout = () => {
        setUserInfo(null);
    };

    const toggleMenu = (menuKey: string) => {
        setExpandedMenus((prev) => ({
            ...prev,
            [menuKey]: !prev[menuKey],
        }));
    };

    return (
        <View className="app-container">
            <TopSection
                statusBarHeight={statusBarHeight}
                handleStudentLogin={() => {
                    studentLoginRef.current?.open();
                }}
                loading={loading}
                userInfo={userInfo}
            />
            <View className="content-wrapper">
                <MenuSection expandedMenus={expandedMenus} toggleMenu={toggleMenu} userInfo={userInfo} handleLogout={handleLogout} loading={loading} />
                <View className="footer safe-area-bottom">
                    <Text className="copyright">盼达软件</Text>
                </View>
            </View>

            {/* Floating Calendar Button */}
            <View className="floating-calendar-btn" onClick={() => setShowCalendarModal(true)}>
                {showCalendarText && <Text className="calendar-text">练车日历</Text>}
                <Calendar size={24} color="#fff" />
            </View>

            {/* Calendar Modal */}
            {showCalendarModal && (
                <View className="custom-modal-overlay" onClick={() => setShowCalendarModal(false)}>
                    <View className="custom-modal-container" onClick={(e) => e.stopPropagation()}>
                        <View className="custom-modal-header">
                            <Text className="custom-modal-title">练车日历</Text>
                            <View className="custom-modal-close" onClick={() => setShowCalendarModal(false)}>
                                <Close size={20} />
                            </View>
                        </View>
                        <View className="modal-content">
                            <CalendarSection userInfo={userInfo} />
                        </View>
                    </View>
                </View>
            )}

            <StudentLoginPopup ref={studentLoginRef} onLoginSuccess={handleLoginSuccess} />
        </View>
    );
};

export default StudentIndex;
