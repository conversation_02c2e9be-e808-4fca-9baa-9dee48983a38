/* pages/my/my.wxss */

/* 全局字体和样式设置 */

page {
    background: #f0f4f9;
    color: #333;

    font-family: <PERSON><PERSON>, STYuanti-SC-Regular, "Microsoft Yahei", sans-serif;
    // font-family: BlinkMacSystemFont, "Segoe UI";
    // font-family: "PingFang SC", "Hiragino Sans GB", "Helvetica Rounded", "Arial Rounded MT Bold", "Microsoft YaHei", sans-serif;
}

.container {
    position: relative;
    background-color: #f5f7fa;
    min-height: 100vh;
}

.top-container {
    height: 400rpx;
    position: relative;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #5677fc, #4a67d6);
    padding-bottom: 0;
    box-shadow: 0 4rpx 20rpx rgba(74, 103, 214, 0.2);
}

.bg-img {
    position: absolute;
    width: 100%;
    height: 440rpx;
    z-index: 0;
    opacity: 0.15;
    mix-blend-mode: overlay;
}

// 登录区域
.login-section {
    margin-top: 80rpx;
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.left-section {
    display: flex;
    align-items: center;
}

// 头像区域样式
.avatar-wrapper {
    position: relative;
    width: 150rpx;
    height: 150rpx;
}

.avatar-decoration {
    position: absolute;
    width: 180rpx;
    height: 180rpx;
    border-radius: 50%;
    border: 4rpx solid rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.1);
    top: -16rpx;
    left: -16rpx;
    animation: rotate 25s linear infinite;
    z-index: 1;
}

.avatar {
    border-radius: 50%;
    width: 150rpx;
    height: 150rpx;
    border: 6rpx solid #ffffff;
    box-shadow: 0 10rpx 25rpx rgba(86, 119, 252, 0.3);
    position: relative;
    z-index: 2;
}

.avatar-shine {
    position: absolute;
    width: 40rpx;
    height: 150rpx;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0) 100%);
    top: 0;
    left: -40rpx;
    z-index: 3;
    animation: shine 3s ease-in-out infinite;
}

// 登录按钮样式
.student-login-button {
    margin-left: 30rpx;
    background: linear-gradient(135deg, #5677fc, #4a67d6);
    border-radius: 16rpx;
    box-shadow: 0 10rpx 20rpx rgba(86, 119, 252, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15rpx 30rpx;
    position: relative;
    overflow: hidden;
    width: 220rpx;
    height: 80rpx;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:active {
        transform: translateY(2rpx);
        box-shadow: 0 6rpx 15rpx rgba(86, 119, 252, 0.15);
    }
}

.button-glow {
    position: absolute;
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    top: -60rpx;
    right: -30rpx;
}

.login-text {
    color: #ffffff;
    font-size: 26rpx;
    // font-weight: 600;
    position: relative;
    z-index: 1;
    // font-family: Yuanti SC, STYuanti-SC-Regular, "Microsoft Yahei", sans-serif;
}

// 公司名称样式
.tenant-name {
    margin-left: auto;
    margin-right: 20rpx;
    text {
        color: #ffffff;
        font-size: 28rpx;
        opacity: 0.9;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    }
}

// 菜单容器样式
.menu-container {
    position: relative;
    margin-top: -40rpx;
    border-radius: 30rpx 30rpx 0 0;
    background-color: #f5f7fa;
    padding: 40rpx 30rpx;
    z-index: 10;
}

// 菜单组样式
.menu-group {
    margin-bottom: 20rpx;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:active {
        opacity: 0.9;
    }
}

// 菜单头部样式
.menu-header {
    padding: 22rpx 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s ease;

    &:active {
        background-color: #f9f9f9;
    }
}

.menu-title {
    display: flex;
    align-items: center;
}

.menu-icon {
    margin-right: 16rpx;
    color: #5677fc;
    font-size: 38rpx;
}

.menu-title-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    letter-spacing: 0.5rpx;
    // font-family: Yuanti SC, STYuanti-SC-Regular, "Microsoft Yahei", sans-serif;
}

// 菜单项样式
.menu-items {
    border-top: 1px solid #f0f0f0;
}

.menu-item {
    padding: 22rpx 24rpx;
    padding-left: 70rpx;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:last-child {
        border-bottom: none;
    }

    &:active {
        background-color: #f5f7fa;
    }

    text {
        color: #666;
        font-size: 26rpx;
        font-weight: 500;
        // font-family: Yuanti SC, STYuanti-SC-Regular, "Microsoft Yahei", sans-serif;
    }
}

// 退出按钮样式
.logout-button {
    background: white;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 30rpx rgba(50, 50, 93, 0.08);
    position: relative;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
    display: flex;
    align-items: center;
    padding: 22rpx 24rpx;

    /* 添加顶部高亮，类似菜单卡片 */
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 8rpx;
        background: linear-gradient(90deg, #ff4d4f, #ff7875);
    }

    &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 15rpx rgba(50, 50, 93, 0.05);
    }
}

.logout-glow {
    display: none; /* 移除旧的发光效果 */
}

.logout-icon-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50rpx;
    height: 50rpx;
    border-radius: 16rpx;
    background: linear-gradient(135deg, rgba(255, 77, 79, 0.1), rgba(255, 119, 117, 0.1));
    margin-right: 20rpx;
    position: relative;
    overflow: hidden;

    /* 添加与菜单图标一致的高亮效果 */
    &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
        border-radius: 20rpx;
    }
}

.logout-text {
    color: #ff4d4f;
    font-size: 28rpx;
    font-weight: 600;
    flex: 1;
    // font-family: Yuanti SC, STYuanti-SC-Regular, "Microsoft Yahei", sans-serif;
}

/* 添加箭头效果，与菜单保持一致 */
.logout-button::after {
    content: "";
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff4d4f' stroke-width='2' stroke-opacity='0.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M9 18l6-6-6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    opacity: 0.5;
}

// 动画效果
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes shine {
    0% {
        left: -100rpx;
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    70% {
        opacity: 1;
    }
    100% {
        left: 150rpx;
        opacity: 0;
    }
}

/* 时尚现代的学员登录页面样式 */

.app-container {
    min-height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
    background: #f0f4f9;
    height: 100vh;
    overflow: hidden;
}

.content-wrapper {
    flex: 1;
    width: 100%;
    position: relative;
    z-index: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    display: flex;
    flex-direction: column;
}

/* 顶部背景区域样式 */
.top-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 360rpx;
    background: linear-gradient(135deg, #6345ed, #4568dc);
    overflow: hidden;
    z-index: 1;
}

/* 棱镜背景效果 */
.prisma-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

.prisma {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    transform-origin: center;

    &.prisma1 {
        width: 280rpx;
        height: 280rpx;
        top: -80rpx;
        right: 50rpx;
        border-radius: 40rpx;
        transform: rotate(25deg);
        animation: float-rotate 15s ease-in-out infinite alternate;
    }

    &.prisma2 {
        width: 180rpx;
        height: 180rpx;
        bottom: 120rpx;
        left: 40rpx;
        border-radius: 30rpx;
        transform: rotate(-15deg);
        animation: float-rotate 12s ease-in-out infinite alternate-reverse;
    }

    &.prisma3 {
        width: 130rpx;
        height: 130rpx;
        top: 160rpx;
        left: 50%;
        border-radius: 25rpx;
        transform: translateX(-50%) rotate(10deg);
        animation: float-rotate 20s ease-in-out infinite alternate;
    }

    &.prisma4 {
        width: 100rpx;
        height: 100rpx;
        top: 30rpx;
        left: 80rpx;
        border-radius: 18rpx;
        transform: rotate(-30deg);
        animation: float-rotate 18s ease-in-out infinite alternate-reverse;
    }
}

@keyframes float-rotate {
    0% {
        transform: translateY(0) rotate(0deg);
    }
    100% {
        transform: translateY(30rpx) rotate(30deg);
    }
}

/* 玻璃圆圈效果 */
.glass-circles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

.glass-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);

    &.circle1 {
        width: 300rpx;
        height: 300rpx;
        top: -100rpx;
        right: -80rpx;
        animation: pulse1 8s ease-in-out infinite alternate;
    }

    &.circle2 {
        width: 200rpx;
        height: 200rpx;
        bottom: 100rpx;
        left: -60rpx;
        animation: pulse2 12s ease-in-out infinite alternate;
    }

    &.circle3 {
        width: 150rpx;
        height: 150rpx;
        top: 50%;
        right: 100rpx;
        transform: translateY(-50%);
        animation: pulse3 10s ease-in-out infinite alternate;
    }
}

@keyframes pulse1 {
    0% {
        transform: scale(1) translate(0, 0);
    }
    100% {
        transform: scale(1.1) translate(-20rpx, 20rpx);
    }
}

@keyframes pulse2 {
    0% {
        transform: scale(1) translate(0, 0);
    }
    100% {
        transform: scale(1.2) translate(20rpx, -20rpx);
    }
}

@keyframes pulse3 {
    0% {
        transform: translateY(-50%) scale(1);
    }
    100% {
        transform: translateY(-40%) scale(1.15);
    }
}

/* 波浪效果 */
.wave-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 120rpx;
    overflow: hidden;
}

.wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 100%;
    background-repeat: repeat-x;
    transform-origin: center bottom;
}

.wave1 {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="white" fill-opacity="0.4" d="M0,192L60,186.7C120,181,240,171,360,186.7C480,203,600,245,720,240C840,235,960,181,1080,170.7C1200,160,1320,192,1380,208L1440,224L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z"></path></svg>')
        repeat-x;
    animation: wave 12s linear infinite;
    z-index: 1;
}

.wave2 {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="white" fill-opacity="0.8" d="M0,256L60,245.3C120,235,240,213,360,213.3C480,213,600,235,720,229.3C840,224,960,192,1080,197.3C1200,203,1320,245,1380,266.7L1440,288L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z"></path></svg>')
        repeat-x;
    animation: wave 8s linear infinite;
    z-index: 2;
}

@keyframes wave {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* 顶部内容区域样式 */
.top-content {
    position: relative;
    z-index: 10;
    padding: 0 30rpx;
    padding-bottom: 0rpx;
    margin-bottom: 0rpx;
    // font-family: Yuanti SC, STYuanti-SC-Regular, "Microsoft Yahei", sans-serif;
}

/* 个人资料卡片 */
.profile-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 30rpx;
    padding: 30rpx 40rpx;
    box-shadow: 0 20rpx 40rpx rgba(23, 24, 85, 0.15);
    position: relative;
    overflow: hidden;
    margin-bottom: 24rpx;
}

.glass-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.2) 100%);
    z-index: -1;
}

.card-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;
}

/* 头像容器 */
.avatar-container {
    position: relative;
    width: 120rpx;
    height: 120rpx;
    margin: 0;
}

.avatar-backdrop {
    position: absolute;
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    top: 0;
    left: 0;
    z-index: 1;
}

.avatar-ring {
    position: absolute;
    border-radius: 50%;

    &.outer-ring {
        width: 116rpx;
        height: 116rpx;
        border: 2rpx dashed rgba(255, 255, 255, 0.3);
        top: 2rpx;
        left: 2rpx;
        animation: rotate 15s linear infinite;
    }

    &.inner-ring {
        width: 108rpx;
        height: 108rpx;
        border: 2rpx dashed rgba(255, 255, 255, 0.5);
        top: 6rpx;
        left: 6rpx;
        animation: rotate 10s linear infinite reverse;
    }
}

/* 头像区域样式 */
.avatar-area {
    position: relative;
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    overflow: hidden;
    z-index: 3;
    margin: 10rpx auto;
}

.avatar-img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 100%);
    transform: skewX(-25deg);
    animation: shine 3s infinite;
}

/* 登录按钮容器 */
.login-btn-container {
    width: auto;
    display: flex;
    justify-content: flex-end;
    margin-top: 0;
    position: relative;
    perspective: 800rpx;
}

/* 登录按钮样式 - 旗帜风格 */
.login-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #5d89ff, #4568dc 60%, #375bd0);
    padding: 18rpx 46rpx;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8rpx 20rpx rgba(70, 100, 220, 0.3), 0 4rpx 8rpx rgba(70, 100, 220, 0.2);
    border-radius: 10rpx;
    transform-origin: left center;
    animation: flag-wave 6s ease-in-out infinite;
    transition: all 0.3s ease;
    min-width: 160rpx;

    &:active {
        transform: translateY(2rpx) skewX(-2deg);
        box-shadow: 0 4rpx 10rpx rgba(70, 100, 220, 0.2);
    }

    /* 基本形状 - 移除三角形 */
    &::before {
        display: none;
    }

    /* 左侧圆角增强 */
    &::after {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        width: 12rpx;
        height: 100%;
        background: linear-gradient(to right, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
        border-radius: 10rpx 0 0 10rpx;
    }

    /* 添加折叠纹路效果 - 移除白线条 */
    .flag-texture {
        display: none; /* 移除白色线条 */
    }

    /* 旗帜上的花纹 */
    .flag-pattern {
        position: absolute;
        top: 6rpx;
        right: 20rpx;
        width: 30rpx;
        height: 30rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.15);
        box-shadow: -50rpx 10rpx 0 -10rpx rgba(255, 255, 255, 0.1), -25rpx -15rpx 0 -8rpx rgba(255, 255, 255, 0.08);
        opacity: 0.7;
    }
}

.btn-glow {
    position: absolute;
    top: -40%;
    left: -30%;
    width: 140%;
    height: 180%;
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 70%);
    animation: flag-glow 4s infinite alternate;
    opacity: 0.7;
    mix-blend-mode: overlay;
}

@keyframes flag-glow {
    0% {
        opacity: 0.4;
        transform: translateX(-8%) translateY(3%) scale(0.95);
    }
    100% {
        opacity: 0.8;
        transform: translateX(5%) translateY(-8%) scale(1.05);
    }
}

@keyframes flag-wave {
    0% {
        transform: perspective(500rpx) rotateY(1deg) skewX(0deg) scaleY(1);
    }
    25% {
        transform: perspective(500rpx) rotateY(0deg) skewX(-1deg) scaleY(0.98);
    }
    50% {
        transform: perspective(500rpx) rotateY(-1deg) skewX(0deg) scaleY(1);
    }
    75% {
        transform: perspective(500rpx) rotateY(0deg) skewX(1deg) scaleY(0.98);
    }
    100% {
        transform: perspective(500rpx) rotateY(1deg) skewX(0deg) scaleY(1);
    }
}

.login-icon {
    margin-left: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42rpx;
    height: 42rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.25);
    position: relative;
    z-index: 2;
    backdrop-filter: blur(2px);
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.15);

    &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 50%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0));
        z-index: -1;
    }
}

/* 菜单内容区域 */
.menu-content {
    background: #f0f4f9;
    position: relative;
    z-index: 5;
    padding: 0 24rpx 40rpx;
    margin-top: 20rpx;
    flex: 1;
}

/* 菜单卡片样式 */
.menu-card {
    background: white;
    border-radius: 20rpx;
    overflow: hidden;
    margin-bottom: 24rpx;
    box-shadow: 0 10rpx 30rpx rgba(50, 50, 93, 0.08);
    position: relative;
}

.card-highlight {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 8rpx;
    background: linear-gradient(90deg, #6345ed, #4568dc);
}

/* 菜单头部样式 */
.menu-header {
    padding: 22rpx 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s;
    position: relative;
    z-index: 2;

    &:active {
        background-color: #f8f9fe;
    }
}

.menu-title {
    display: flex;
    align-items: center;
}

.icon-bubble {
    width: 50rpx;
    height: 50rpx;
    border-radius: 16rpx;
    background: linear-gradient(135deg, rgba(99, 69, 237, 0.1), rgba(69, 104, 220, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    position: relative;
    overflow: hidden;
}

.menu-icon {
    color: #6345ed;
    font-size: 34rpx;
    position: relative;
    z-index: 1;
}

.menu-title-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    letter-spacing: 0.5rpx;
    // font-family: Yuanti SC, STYuanti-SC-Regular, "Microsoft Yahei", sans-serif;
}

.menu-arrow {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    background: rgba(99, 69, 237, 0.05);
    transition: all 0.3s;

    &.expanded {
        transform: rotate(180deg);
        background: rgba(99, 69, 237, 0.1);
    }
}

/* 菜单项样式 */
.menu-items {
    border-top: 1px solid #f0f3fa;
}

.menu-item {
    padding: 22rpx 24rpx;
    padding-left: 70rpx;
    border-bottom: 1px solid #f0f3fa;
    transition: all 0.2s;
    position: relative;

    &:last-child {
        border-bottom: none;
    }

    &:active {
        background-color: #f8f9fe;
    }

    text {
        color: #666;
        font-size: 26rpx;
        font-weight: 500;
        // font-family: Yuanti SC, STYuanti-SC-Regular, "Microsoft Yahei", sans-serif;
    }
}

.item-action {
    display: flex;
    align-items: center;

    &::after {
        content: "";
        display: block;
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        background-color: rgba(99, 69, 237, 0.05);
        position: absolute;
        right: 30rpx;
        opacity: 0;
        transform: scale(0.5);
        transition: all 0.3s;
    }
}

.menu-item:active .item-action::after {
    opacity: 1;
    transform: scale(1);
}

/* 添加 SVG 图标样式 */

.svg-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 80%;
}

/* 箭头向上图标 */
.svg-arrow-up-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%235677fc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M18 15l-6-6-6 6'/%3E%3C/svg%3E");
    width: 40rpx;
    height: 40rpx;
}

/* 箭头向下图标 */
.svg-arrow-down-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%235677fc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    width: 40rpx;
    height: 40rpx;
}

/* 箭头向右图标 */
.svg-arrow-right-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23c0c4cc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M9 18l6-6-6-6'/%3E%3C/svg%3E");
    width: 32rpx;
    height: 32rpx;
}

/* 基本信息图标 */
.svg-info-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236345ed' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpath d='M12 16v-4'%3E%3C/path%3E%3Cpath d='M12 8h.01'%3E%3C/path%3E%3C/svg%3E");
    width: 50rpx;
    height: 50rpx;
}

/* 订单管理图标 */
.svg-order-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236345ed' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='2' y='3' width='20' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='8' y1='10' x2='16' y2='10'%3E%3C/line%3E%3Cline x1='8' y1='14' x2='16' y2='14'%3E%3C/line%3E%3Cline x1='8' y1='6' x2='16' y2='6'%3E%3C/line%3E%3C/svg%3E");
    width: 50rpx;
    height: 50rpx;
}

/* 照片管理图标 */
.svg-photo-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236345ed' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2'%3E%3C/rect%3E%3Ccircle cx='9' cy='9' r='2'%3E%3C/circle%3E%3Cpath d='M15.82 15.82L21 21'%3E%3C/path%3E%3Cpath d='M21 15.82L15.82 21'%3E%3C/path%3E%3Cpath d='M3 15l4.75-4.75 4.5 4.5 3.5-3.5'%3E%3C/path%3E%3C/svg%3E");
    width: 50rpx;
    height: 50rpx;
}

/* 用户图标 (登录按钮) */
.svg-user-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='12' cy='7' r='4'%3E%3C/circle%3E%3C/svg%3E");
    width: 38rpx;
    height: 38rpx;
    margin-left: 12rpx;
}

/* 退出系统图标 */
.svg-logout-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff4d4f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4'%3E%3C/path%3E%3Cpolyline points='16 17 21 12 16 7'%3E%3C/polyline%3E%3Cline x1='21' y1='12' x2='9' y2='12'%3E%3C/line%3E%3C/svg%3E");
    width: 38rpx;
    height: 38rpx;
}

/* 添加新图标 */

/* 日历/预约图标 */
.svg-calendar-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236345ed' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3Ccircle cx='12' cy='15' r='2'%3E%3C/circle%3E%3C/svg%3E");
    width: 50rpx;
    height: 50rpx;
}

/* 排队图标 */
.svg-queue-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236345ed' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75'%3E%3C/path%3E%3C/svg%3E");
    width: 50rpx;
    height: 50rpx;
}

/* 支付图标 */
.svg-payment-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236345ed' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='5' width='18' height='14' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3Cline x1='12' y1='15' x2='16' y2='15'%3E%3C/line%3E%3C/svg%3E");
    width: 50rpx;
    height: 50rpx;
}

/* 确保退出按钮样式看起来像菜单项 */
.arrow-spacer {
    width: 16px;
    height: 16px;
}

/* 添加底部版权信息 */
.footer {
    position: relative;
    padding: 20rpx 0;
    margin-top: 40rpx;
    background-color: transparent;
    width: 100%;

    &.safe-area-bottom {
        padding-bottom: calc(20rpx + constant(safe-area-inset-bottom)); /* iOS 11.2以下 */
        padding-bottom: calc(20rpx + env(safe-area-inset-bottom)); /* iOS 11.2+ */
    }

    .copyright {
        position: relative;
        color: #999;
        font-size: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12rpx;

        &::before,
        &::after {
            content: "";
            width: 48rpx;
            height: 2rpx;
            background: #e8e8e8;
        }
    }
}

/* 学员登录弹窗样式 */
.student-login-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.student-login-container {
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(245, 247, 250, 0.9));
    border-radius: 16px;
    width: 85%;
    max-width: 650rpx;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    overflow: visible;
    box-shadow: 0 15px 35px rgba(50, 50, 93, 0.2), 0 5px 15px rgba(0, 0, 0, 0.15);
    position: relative;
    animation: slideUp 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    transform: translateY(0);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Decorative elements */
.student-login-container::before {
    content: "";
    position: absolute;
    top: -80rpx;
    right: -80rpx;
    width: 200rpx;
    height: 200rpx;
    background: rgba(99, 69, 237, 0.1);
    border-radius: 50%;
    filter: blur(30px);
    z-index: -1;
}

.student-login-container::after {
    content: "";
    position: absolute;
    bottom: -60rpx;
    left: -60rpx;
    width: 180rpx;
    height: 180rpx;
    background: rgba(86, 119, 252, 0.1);
    border-radius: 40rpx;
    filter: blur(25px);
    z-index: -1;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.select-option {
    padding: 22rpx 24rpx;
    border-bottom: 1px solid rgba(240, 242, 245, 0.8);
    font-size: 28rpx;
    color: #333;
    transition: all 0.15s ease;

    &:last-child {
        border-bottom: none;
    }

    &:active {
        background-color: rgba(245, 247, 250, 0.9);
    }

    &.selected {
        background-color: rgba(47, 84, 235, 0.08);
        color: #2f54eb;
        font-weight: 500;
    }
}

.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: flex-end;
}

.popup-container {
    width: 100%;
    background-color: #fff;
    border-radius: 32rpx 32rpx 0 0;
    padding: 32rpx;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 32rpx;
    border-bottom: 1rpx solid #eee;
}

.popup-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
}

.popup-close {
    padding: 8rpx;
    color: #999;
}

.popup-content {
    max-height: 60vh;
    overflow-y: auto;
}

.popup-option {
    padding: 32rpx;
    font-size: 28rpx;
    color: #333;
    border-bottom: 1rpx solid #f5f5f5;

    &.selected {
        color: #1890ff;
        background-color: #f0f9ff;
    }

    &:active {
        background-color: #f5f5f5;
    }
}

.calendar-header-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx 16rpx 32rpx;
    position: relative;
}

.calendar-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;

    .svg-calendar-icon {
        display: inline-block;
        width: 50rpx;
        height: 50rpx;
        margin-right: 20rpx;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236345ed' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3Ccircle cx='12' cy='15' r='2'%3E%3C/circle%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: center;
        background-size: 60%;
        border-radius: 16rpx;
        background-color: rgba(99, 69, 237, 0.1);
        position: relative;
        overflow: hidden;

        &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
            border-radius: 16rpx;
        }
    }
}

.calendar-month-switch {
    display: flex;
    align-items: center;
    gap: 16rpx;
    background: rgba(99, 69, 237, 0.05);
    padding: 8rpx 16rpx;
    border-radius: 12rpx;
}

.calendar-arrow {
    font-size: 32rpx;
    color: #6345ed;
    padding: 0 12rpx;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;

    &:active {
        opacity: 0.7;
    }
}

.calendar-month-label {
    font-size: 28rpx;
    color: #6345ed;
    margin: 0 8rpx;
    font-weight: 500;
}

.calendar-weekdays {
    display: flex;
    justify-content: space-between;
    padding: 0 16rpx;
    margin-bottom: 8rpx;
}

.calendar-weekday {
    flex: 1;
    text-align: center;
    color: #b0b0b0;
    font-size: 26rpx;
    font-weight: 500;
}

.calendar-days-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 12rpx;
    padding: 0 16rpx;
}

.calendar-day {
    width: 100%;
    aspect-ratio: 1/1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #bbb;
    background: #f5f6fa;
    transition: background 0.2s, color 0.2s;
}

.calendar-day.punch {
    background: #5fd37d;
    color: #fff;
    font-weight: 600;
}

.calendar-day.less {
    background: #d0e6d7;
    color: #3a7c4a;
    font-weight: 500;
}

.calendar-day.today {
    border: 2rpx solid #5677fc;
    color: #5677fc;
    background: #eaf1ff;
    font-weight: 600;
}

.calendar-day.empty {
    background: transparent;
    color: transparent;
    pointer-events: none;
}

.calendar-footer {
    text-align: right;
    padding: 12rpx 32rpx 0 0;
    color: #b0b0b0;
    font-size: 24rpx;
}

.floating-calendar-btn {
    position: fixed;
    right: 32rpx;
    bottom: 120rpx;
    width: 96rpx;
    height: 96rpx;
    background: linear-gradient(135deg, #2c5ecc, #4c37c3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(44, 94, 204, 0.3);
    z-index: 100;
    transition: transform 0.2s ease;

    &:active {
        transform: scale(0.95);
    }

    .calendar-text {
        position: absolute;
        right: 120rpx;
        background-color: #fff;
        padding: 8rpx 16rpx;
        border-radius: 8rpx;
        font-size: 28rpx;
        color: #333;
        white-space: nowrap;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    }
}

.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.custom-modal-container {
    background-color: #fff;
    border-radius: 24rpx;
    width: 90%;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.custom-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 30rpx;
    border-bottom: 1px solid #eee;
}

.custom-modal-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
}

.custom-modal-close {
    font-size: 40rpx;
    color: #999;
    cursor: pointer;
    padding: 10rpx;
    line-height: 1;
}

.modal-content {
    // padding: 30rpx 0;
    overflow-y: auto;
    max-height: calc(80vh - 100rpx);
}
