page {
    background: #f7f8fa;

    font-family: "AlibabaPuHuiTi";
    // font-family: Yuanti SC, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
    height: 100%;
    overflow: hidden; // 防止页面滚动
}

.scan-page {
    padding: 0;
    background: #f7f8fa;
    height: 100vh;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    width: 100%;
    overflow: hidden; // 完全防止滚动
    position: relative;

    .subject-selection {
        padding: 40rpx 30rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 1;
        flex: 1;
        height: 100vh;
        box-sizing: border-box;
        overflow: hidden; // 防止滚动

        .title {
            font-size: 36rpx;
            font-weight: 600;
            color: #333333;
            margin-bottom: 40rpx;
            text-align: center;
            position: relative;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 180rpx;
            width: 100%;
            background: #ffffff;
            border-radius: 16rpx;
            font-size: 30rpx;
            color: #999999;
            box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
            margin-top: 20rpx;
        }

        .subject-list {
            display: flex;
            flex-direction: column;
            gap: 24rpx;
            width: 100%;
            overflow-y: auto; // 允许垂直滚动
            overflow-x: hidden; // 防止水平滚动
            -webkit-overflow-scrolling: touch; // 在iOS上提供惯性滚动
            max-height: calc(100vh - 120rpx); // 设置最大高度，减去标题的高度

            .subject-item {
                background: #ffffff;
                padding: 32rpx 30rpx;
                border-radius: 16rpx;
                box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
                transition: all 0.3s ease;
                position: relative;
                display: flex;
                align-items: center;
                border-left: 8rpx solid #1677ff;

                &:active {
                    transform: translateY(2rpx);
                    background-color: #f8f9fc;
                }

                .circle-icon {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 64rpx;
                    height: 64rpx;
                    background: rgba(22, 119, 255, 0.1);
                    color: #1677ff;
                    border-radius: 32rpx;
                    font-size: 30rpx;
                    font-weight: 600;
                    margin-right: 24rpx;
                    line-height: 1;
                    text-align: center;
                    padding: 0;
                }

                text {
                    font-size: 32rpx;
                    color: #333333;
                    font-weight: 500;
                    flex: 1;
                }
            }

            .list-record-item {
                border-left: 8rpx solid #ff6b00;

                .circle-icon {
                    background: rgba(255, 107, 0, 0.1);
                    color: #ff6b00;
                }

                text {
                    color: #ff6b00;
                }
            }
        }
    }

    .training-info-container {
        display: flex;
        flex-direction: column;
        height: 100vh;
        position: relative;
        width: 100%;
        box-sizing: border-box;
        overflow: hidden; // 防止整个容器滚动
        z-index: 1;

        .tab-container {
            display: flex;
            background: #ffffff;
            padding: 0 30rpx;
            position: sticky;
            top: 0;
            z-index: 2;
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

            .tab-item {
                flex: 1;
                text-align: center;
                padding: 24rpx 0;
                font-size: 30rpx;
                color: #666666;
                position: relative;
                transition: all 0.3s ease;

                &.active {
                    color: #1677ff;
                    font-weight: 500;

                    &::after {
                        content: "";
                        position: absolute;
                        bottom: 0;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 40rpx;
                        height: 4rpx;
                        background: #1677ff;
                        border-radius: 2rpx;
                    }
                }
            }
        }

        .refresh-hint-container {
            padding: 20rpx 0;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f7f8fa;

            .refresh-hint {
                color: #999999;
                font-size: 26rpx;
                text-align: center;
            }
        }

        .scrollable-records {
            flex: 1;
            // padding: 20rpx 30rpx;
            background: #f7f8fa;
            margin-top: 0;
            padding-bottom: 310rpx; // 添加底部填充，防止被固定底部遮挡
            overflow-y: auto; // 只允许垂直滚动
            overflow-x: hidden; // 防止水平滚动
            -webkit-overflow-scrolling: touch; // 在iOS上提供惯性滚动
            height: calc(100vh - 180rpx); // 设置固定高度，减去头部的高度
        }

        .info-section {
            background: transparent;
            padding: 0 0 10rpx 0;
            box-shadow: none;
            margin: 20rpx 20rpx 20rpx 20rpx;

            .section-title {
                font-size: 32rpx;
                font-weight: 600;
                color: #333333;
                margin-bottom: 24rpx;
                padding: 0 10rpx;
            }

            .loading-text,
            .error-text {
                text-align: center;
                padding: 40rpx 0;
                color: #999999;
                font-size: 28rpx;
            }

            .empty-text {
                text-align: center;
                padding: 40rpx 0;
                color: #999999;
                font-size: 28rpx;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                gap: 20rpx;

                .line-decoration {
                    display: flex;
                    align-items: center;
                    height: 20rpx;
                    width: 80rpx;

                    .thin-line {
                        height: 1px;
                        background-color: #cccccc;
                        flex: 1;
                    }

                    .thick-line {
                        height: 2px;
                        background-color: #999999;
                        width: 30rpx;
                        margin: 0 4rpx;
                    }
                }
            }

            .loading-text {
                color: #666666;
            }

            .error-text {
                color: #ff4d4f;
            }

            .record-item {
                padding: 24rpx;
                background: #ffffff;
                border-radius: 16rpx;
                margin-bottom: 20rpx;
                box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
                cursor: pointer;

                // 添加右侧箭头指示可点击
                &::after {
                    content: "";
                    position: absolute;
                    right: 20rpx;
                    top: 50%;
                    width: 16rpx;
                    height: 16rpx;
                    border-top: 2rpx solid #cccccc;
                    border-right: 2rpx solid #cccccc;
                    transform: translateY(-50%) rotate(45deg);
                    opacity: 0.7;
                }

                &:active {
                    transform: scale(0.98);
                    background-color: #f8f9fc;
                }

                .record-content {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                }

                .left-section {
                    display: flex;
                    flex-direction: column;
                    gap: 12rpx;

                    .name-time-container {
                        display: flex;
                        align-items: center;
                        gap: 16rpx;

                        .sequence-number {
                            font-size: 28rpx;
                            color: #ffffff;
                            background: #1677ff;
                            width: 40rpx;
                            height: 40rpx;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: 500;
                            flex-shrink: 0;
                        }

                        .name {
                            font-size: 34rpx;
                            color: #333333;
                            line-height: 1.4;
                        }

                        .boarding-time {
                            font-size: 24rpx;
                            color: #1677ff;
                            background: rgba(22, 119, 255, 0.1);
                            padding: 4rpx 12rpx;
                            border-radius: 20rpx;
                        }
                    }

                    .ticket-name {
                        font-size: 26rpx;
                        color: #666666;
                        padding: 4rpx 0;
                        display: inline-block;
                    }

                    .field-name {
                        font-size: 24rpx;
                        color: #888888;
                        padding: 2rpx 0;
                        display: inline-block;
                    }
                }

                .right-section {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    gap: 4rpx;
                    background: rgba(22, 119, 255, 0.06);
                    padding: 12rpx 0;
                    border-radius: 12rpx;
                    width: 100rpx;
                    text-align: center;

                    .time {
                        font-size: 32rpx;
                        color: #1677ff;
                        line-height: 1.4;
                        font-family: "DIN Alternate", "DIN", -apple-system, sans-serif;
                        letter-spacing: 1rpx;
                        position: relative;

                        &:first-child {
                            &::after {
                                content: "";
                                position: absolute;
                                bottom: -2rpx;
                                left: 0;
                                right: 0;
                                height: 1px;
                                background: rgba(22, 119, 255, 0.2);
                            }
                        }
                    }

                    .elapsed-time {
                        font-size: 32rpx;
                        color: #ff6b00; // 橙色，让实时计时更突出
                        font-weight: 500;
                        animation: pulse 1s infinite;
                    }

                    .duration-time {
                        font-size: 32rpx;
                        color: #1677ff; // 蓝色，表示已完成
                        font-weight: 500;
                    }

                    @keyframes pulse {
                        0% {
                            opacity: 1;
                        }
                        50% {
                            opacity: 0.8;
                        }
                        100% {
                            opacity: 1;
                        }
                    }
                }
            }
        }

        .fixed-footer {
            background: #ffffff;
            padding: 20rpx 30rpx;
            padding-bottom: calc(20rpx + env(safe-area-inset-bottom, 0));
            box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 10;

            .info-row {
                margin-bottom: 8rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .small-text {
                    font-size: 26rpx;
                    color: #666666;
                }
            }

            .info-row {
                display: flex;
                align-items: center;
                justify-content: space-between;
                // margin-bottom: 16rpx;
                background: #ffffff;
                border-radius: 16rpx;
                padding: 8rpx;

                text {
                    font-size: 28rpx;
                    color: #333333;
                }

                &.vehicle-row {
                    .change-btn {
                        font-size: 26rpx;
                        color: #1677ff;
                        padding: 8rpx 24rpx;
                        border: 1px solid #1677ff;
                        border-radius: 32rpx;
                        background-color: rgba(22, 119, 255, 0.06);
                        transition: all 0.3s ease;

                        &:active {
                            opacity: 0.8;
                            transform: scale(0.98);
                        }
                    }
                }
            }

            .button-container {
                display: flex;
                gap: 20rpx;
                margin-top: 20rpx;

                .back-button {
                    width: 80rpx;
                    height: 80rpx;
                    border-radius: 40rpx;
                    background: #f0f0f0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: none;
                    padding: 0;

                    &:active {
                        background: #e0e0e0;
                    }
                }

                .scan-button {
                    flex: 1;
                    height: 80rpx;
                    background: #1677ff;
                    color: #ffffff;
                    border-radius: 40rpx;
                    font-size: 32rpx;
                    font-weight: 500;
                    border: none;
                    transition: all 0.3s ease;

                    &:active {
                        opacity: 0.9;
                        transform: scale(0.98);
                    }

                    &.scan-button-disabled {
                        background: #a0c2f8; /* 浅蓝色背景 */
                        color: #ffffff;
                        opacity: 0.8;
                        cursor: not-allowed;
                        box-shadow: none;

                        &:active {
                            transform: none; /* 禁用点击效果 */
                            opacity: 0.8;
                        }
                    }
                }
            }
        }

        .debug-info {
            padding: 20rpx;
            margin: 20rpx;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 8rpx;
            font-size: 24rpx;
            color: #666;
            display: flex;
            flex-direction: column;
            gap: 8rpx;

            .debug-text {
                font-family: monospace;
            }
        }
    }
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: #ffffff;
    border-radius: 16rpx;
    width: 95vw;
    max-width: 600rpx;
    overflow: hidden;
}

.modal-header {
    padding: 24rpx;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
    }

    .close-btn {
        padding: 8rpx;
        cursor: pointer;
    }
}

.modal-body {
    padding: 24rpx;
    margin-bottom: 20rpx;

    .detail-item {
        display: flex;
        margin-bottom: 20rpx;

        .label {
            font-size: 28rpx;
            color: #666666;
            width: 120rpx;
        }

        .value {
            font-size: 28rpx;
            color: #333333;
            flex: 1;
        }
    }

    .car-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx;
        background: #f7f8fa;
        border-radius: 12rpx;
        margin-bottom: 16rpx;
        transition: all 0.3s ease;

        &:active {
            background: #f0f0f0;
            transform: scale(0.98);
        }

        .car-number {
            font-size: 30rpx;
            color: #333333;
            font-weight: 500;
        }

        .car-nickname {
            font-size: 26rpx;
            color: #666666;
        }
    }
}

.car-modal {
    width: 95vw;
    max-width: 700rpx;
}

.car-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
    padding: 24rpx;
    padding-bottom: 40rpx;
    max-height: 60vh;
    overflow-y: auto;

    .car-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 24rpx;
        background: #f7f8fa;
        border-radius: 12rpx;
        transition: all 0.3s ease;
        min-height: 120rpx;

        &:active {
            background: #f0f0f0;
            transform: scale(0.98);
        }

        &.selected {
            background: rgba(22, 119, 255, 0.1);
            border: 1px solid #1677ff;
        }

        .car-number {
            font-size: 30rpx;
            color: #333333;
            font-weight: 500;
            margin-bottom: 8rpx;
        }

        .car-nickname {
            font-size: 24rpx;
            color: #666666;
        }
    }
}

// 电子券详情模态框样式
.sale-modal {
    width: 95vw;
    max-width: 700rpx;

    .modal-body {
        padding: 30rpx;

        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40rpx 0;

            .loading-text {
                font-size: 28rpx;
                color: #666666;
            }
        }

        .sale-info-container {
            width: 100%;
        }

        .sale-info-item {
            display: flex;
            margin-bottom: 20rpx;
            align-items: flex-start;

            .sale-info-label {
                width: 112rpx;
                font-size: 28rpx;
                color: #666666;
                flex-shrink: 0;
                letter-spacing: 0;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: clip;
                margin-right: 20rpx; // 添加右侧间距
            }

            .sale-info-value {
                flex: 1;
                font-size: 28rpx;
                color: #333333;
                font-weight: 500;
            }
        }
    }

    .modal-footer {
        padding: 20rpx 30rpx 30rpx;
        display: flex;
        justify-content: center;

        .confirm-button {
            width: 80%;
            height: 80rpx;
            background: #1677ff;
            color: #ffffff;
            border-radius: 40rpx;
            font-size: 32rpx;
            font-weight: 500;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            &:active {
                opacity: 0.9;
                transform: scale(0.98);
            }

            &.loading {
                background: #a0c2f8;
                opacity: 0.8;
                cursor: not-allowed;

                &:active {
                    transform: none;
                }
            }

            // 下车按钮样式，使用红色
            &.disembark-button {
                background: #ff4d4f;

                &:active {
                    opacity: 0.9;
                    background: #ff7875;
                }

                &.loading {
                    background: #ffa39e;
                    opacity: 0.8;
                }
            }
        }
    }
}
