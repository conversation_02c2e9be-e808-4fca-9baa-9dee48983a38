import Taro from "@tarojs/taro";
import { useState, useEffect, useRef, useCallback, FC } from "react";
import { View, Text, Button, ScrollView } from "@tarojs/components";
import "./index.scss";
import IconFont from "@components/iconfont";
import { ArrowCornerLeft } from "@nutui/icons-react-taro";
import request from "@/service/request";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import Layout from "@/components/Layout";
import { Close } from "@nutui/icons-react-taro";
import moment from "moment";

// 格式化日期和时间为24小时制
// 格式: YYYY-MM-DD HH:mm:ss
const formatDateTime = (dateString: string) => {
    try {
        if (!dateString || dateString === "undefined" || dateString === "null") {
            console.warn("formatDateTime received invalid date string:", dateString);
            return "--";
        }

        // 使用moment处理日期格式化，更可靠
        return moment(new Date(dateString)).format("YYYY-MM-DD HH:mm:ss");
    } catch (error) {
        console.error("formatDateTime error:", error, "for input:", dateString);
        return "--";
    }
};

// 格式化时间为24小时制
// 格式: HH:mm:ss
const formatTime = (dateString: string) => {
    try {
        if (!dateString || dateString === "undefined" || dateString === "null") {
            console.warn("formatTime received invalid date string:", dateString);
            return "--:--:--";
        }

        // 使用moment处理时间格式化，更可靠
        return moment(new Date(dateString)).format("HH:mm:ss");
    } catch (error) {
        console.error("formatTime error:", error, "for input:", dateString);
        return "--:--:--";
    }
};

// 计算已经过去的时间
// 格式: HH:mm:ss
const calculateElapsedTime = (startTimeStr: string): string => {
    try {
        if (!startTimeStr || startTimeStr === "undefined" || startTimeStr === "null") {
            console.warn("calculateElapsedTime received invalid start time:", startTimeStr);
            return "00:00:00";
        }

        // 尝试使用moment解析时间
        const startMoment = moment(startTimeStr);
        if (!startMoment.isValid()) {
            console.warn("calculateElapsedTime received invalid date format:", startTimeStr);
            return "00:00:00";
        }

        const startTime = startMoment.valueOf();
        const currentTime = moment().valueOf();
        const elapsedMilliseconds = currentTime - startTime;

        if (elapsedMilliseconds <= 0) return "00:00:00";

        // 计算小时、分钟和秒数
        const seconds = Math.floor((elapsedMilliseconds / 1000) % 60);
        const minutes = Math.floor((elapsedMilliseconds / (1000 * 60)) % 60);
        const hours = Math.floor(elapsedMilliseconds / (1000 * 60 * 60));

        // 格式化为两位数字
        const formattedHours = String(hours).padStart(2, "0");
        const formattedMinutes = String(minutes).padStart(2, "0");
        const formattedSeconds = String(seconds).padStart(2, "0");

        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
    } catch (error) {
        console.error("calculateElapsedTime error:", error, "for input:", startTimeStr);
        return "00:00:00";
    }
};

// 计算两个时间之间的时间差
// 格式: HH:mm:ss
const calculateDuration = (startTimeStr: string, endTimeStr: string): string => {
    try {
        if (!startTimeStr || !endTimeStr || startTimeStr === "undefined" || endTimeStr === "undefined" || startTimeStr === "null" || endTimeStr === "null") {
            console.warn("calculateDuration received invalid time strings:", { startTimeStr, endTimeStr });
            return "00:00:00";
        }

        // 尝试使用moment解析时间
        const startMoment = moment(startTimeStr);
        const endMoment = moment(endTimeStr);

        if (!startMoment.isValid() || !endMoment.isValid()) {
            console.warn("calculateDuration received invalid date format:", { startTimeStr, endTimeStr });
            return "00:00:00";
        }

        const startTime = startMoment.valueOf();
        const endTime = endMoment.valueOf();
        const durationMilliseconds = endTime - startTime;

        if (durationMilliseconds <= 0) {
            console.warn("calculateDuration: end time is before or equal to start time", { startTimeStr, endTimeStr });
            return "00:00:00";
        }

        // 计算小时、分钟和秒数
        const seconds = Math.floor((durationMilliseconds / 1000) % 60);
        const minutes = Math.floor((durationMilliseconds / (1000 * 60)) % 60);
        const hours = Math.floor(durationMilliseconds / (1000 * 60 * 60));

        // 格式化为两位数字
        const formattedHours = String(hours).padStart(2, "0");
        const formattedMinutes = String(minutes).padStart(2, "0");
        const formattedSeconds = String(seconds).padStart(2, "0");

        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
    } catch (error) {
        console.error("calculateDuration error:", error, "for inputs:", { startTimeStr, endTimeStr });
        return "00:00:00";
    }
};

// 已经过去的时间显示组件
interface ElapsedTimeDisplayProps {
    startTime: string;
}

const ElapsedTimeDisplay: FC<ElapsedTimeDisplayProps> = ({ startTime }) => {
    const [elapsedTime, setElapsedTime] = useState<string>(calculateElapsedTime(startTime));

    useEffect(() => {
        // 初始计算一次已经过去的时间
        setElapsedTime(calculateElapsedTime(startTime));

        // 设置定时器，每秒更新一次
        const timer = setInterval(() => {
            setElapsedTime(calculateElapsedTime(startTime));
        }, 1000);

        // 清除定时器
        return () => clearInterval(timer);
    }, [startTime]);

    return <Text className="time elapsed-time">{elapsedTime}</Text>;
};

interface FieldInfo {
    Id?: string;
    kc: string;
    KeMuId: string;
}

interface SubjectItem {
    id: string;
    code?: string;
    kc?: string;
    FieldId?: string;
    label?: string;
    KeMuId?: string;
}

interface KeMuItem {
    KeMuId: number;
    kc: string;
    FieldId: string;
}

const ScanPage = () => {
    const [selectedSubject, setSelectedSubject] = useState<SubjectItem | null>(null);
    const [fieldInfo, setFieldInfo] = useState<FieldInfo | null>(null);
    const [subjects, setSubjects] = useState<SubjectItem[]>([]);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState<"training" | "completed">("training");
    const [showCarModal, setShowCarModal] = useState(false);
    const [carList, setCarList] = useState<Array<{ Id: string; CarNumber: string; NickName: string }>>([]);
    const [selectedCar, setSelectedCar] = useState<{ Id: string; CarNumber: string; NickName: string } | null>(null);
    const [scanLoading, setScanLoading] = useState(false); // 添加扫码按钮的loading状态
    const [activeTrainingList, setActiveTrainingList] = useState<any[]>([]); // 存储正在训练的列表
    const [completedTrainingList, setCompletedTrainingList] = useState<any[]>([]); // 存储已完成训练的列表
    const [listLoading, setListLoading] = useState(false); // 列表加载状态
    const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null); // 存储定时器引用

    // 电子券相关状态
    const [showSaleModal, setShowSaleModal] = useState(false); // 显示电子券详情模态框
    const [saleInfo, setSaleInfo] = useState<any>(null); // 电子券详情
    const [saleLoading, setSaleLoading] = useState(false); // 电子券信息加载状态
    const [confirmLoading, setConfirmLoading] = useState(false); // 确认上车按钮加载状态

    // 获取训练列表
    // isAutoRefresh: 是否是自动定时刷新，如果是则不显示loading
    const fetchTrainingList = useCallback(
        async (isAutoRefresh = false) => {
            if (!fieldInfo?.Id) {
                console.log("缺少FieldId，无法获取训练列表");
                return;
            }

            try {
                console.log("开始获取训练列表，FieldId:", fieldInfo.Id, "Status:", activeTab === "training" ? 1 : 2, "自动刷新:", isAutoRefresh);

                // 只有在非自动刷新时才显示loading
                if (!isAutoRefresh) {
                    setListLoading(true);
                    message.openLoading("获取训练列表...");
                }

                // 使用正确的API路径和参数
                const result = await request.post<any>("JiaXiao/Study/StudyExamSite/getMyStudyTodayList", {
                    FieldId: fieldInfo.Id,
                    IsDown: activeTab === "training" ? 0 : 1, // 0 没有下车  1 已经下车
                });

                // 只有在非自动刷新时才关闭loading
                if (!isAutoRefresh) {
                    message.closeLoading();
                }

                console.log("获取训练列表响应:", result);

                if (result.success) {
                    // 处理嵌套的数据结构
                    let listData: any[] = [];

                    if (result.data && result.data.data && Array.isArray(result.data.data)) {
                        // 新的API响应格式: data.data是数组
                        console.log("训练列表数据(嵌套):", result.data.data);
                        listData = result.data.data;
                    } else if (Array.isArray(result.data)) {
                        // 旧的API响应格式: data直接是数组
                        console.log("训练列表数据(直接数组):", result.data);
                        listData = result.data;
                    } else {
                        console.warn("训练列表数据格式不正确，已转换为空数组");
                    }

                    // 根据当前标签选择不同的状态变量来存储数据
                    if (activeTab === "training") {
                        setActiveTrainingList(listData);
                    } else {
                        setCompletedTrainingList(listData);
                    }
                } else {
                    // 只有在非自动刷新时才显示错误提示
                    if (!isAutoRefresh) {
                        console.error("获取训练列表失败:", result.message);
                        message.error(result.message || "获取训练列表失败");
                    } else {
                        console.warn("自动刷新获取训练列表失败:", result.message);
                    }
                }
            } catch (error) {
                // 只有在非自动刷新时才关闭loading和显示错误提示
                if (!isAutoRefresh) {
                    message.closeLoading();
                    console.error("获取训练列表出错:", error);
                    message.error("获取训练列表失败，请重试");
                } else {
                    console.warn("自动刷新获取训练列表出错:", error);
                }
            } finally {
                // 只有在非自动刷新时才设置loading状态
                if (!isAutoRefresh) {
                    setListLoading(false);
                }
            }
        },
        [fieldInfo, activeTab]
    );

    // 初始加载科目列表
    useEffect(() => {
        fetchSubjects();
    }, []);

    // 当选择科目或切换标签时，获取训练列表并设置定时刷新
    useEffect(() => {
        // 清除之前的定时器
        if (refreshIntervalRef.current) {
            clearInterval(refreshIntervalRef.current);
            refreshIntervalRef.current = null;
        }

        // 如果有选择科目且有考场信息，则获取训练列表并设置定时刷新
        if (selectedSubject && fieldInfo?.Id) {
            // 立即获取一次列表
            fetchTrainingList();

            // 设置定时器，每1分钟刷新一次
            refreshIntervalRef.current = setInterval(() => {
                // 传入true表示这是自动刷新，不显示loading
                fetchTrainingList(true);
            }, 60000); // 60秒（1分钟）刷新一次
        }

        // 组件卸载时清除定时器
        return () => {
            if (refreshIntervalRef.current) {
                clearInterval(refreshIntervalRef.current);
                refreshIntervalRef.current = null;
            }
        };
    }, [selectedSubject, fieldInfo, activeTab, fetchTrainingList]);

    const fetchSubjects = async () => {
        try {
            setLoading(true);
            const result = await request.post<API.Result<KeMuItem[]>>("JiaXiao/Study/Coach/getMyKeMuList");

            if (result.success) {
                const availableSubjects: SubjectItem[] = [{ id: "school", label: "驾校训练" }];

                // Process the data which is now an array of objects with keMuId and kc
                if (result.data && Array.isArray(result.data)) {
                    result.data.forEach((item) => {
                        if (item.KeMuId === 20) {
                            availableSubjects.push({
                                id: "20",
                                label: "科二模拟",
                                KeMuId: "20",
                                kc: item.kc,
                                FieldId: item.FieldId,
                            });
                        } else if (item.KeMuId === 30) {
                            availableSubjects.push({
                                id: "30",
                                label: "科三模拟",
                                KeMuId: "30",
                                kc: item.kc,
                                FieldId: item.FieldId,
                            });
                        }
                    });
                }

                // Always add training records option
                availableSubjects.push({ id: "list", label: "训练记录" });

                setSubjects(availableSubjects);
            } else {
                message.error("获取科目列表失败");
            }
        } catch (error) {
            console.error("获取科目列表出错:", error);
            Taro.showToast({
                title: "获取科目列表失败",
                icon: "none",
            });
        } finally {
            setLoading(false);
        }
    };

    // 获取当前绑定的车辆信息
    const fetchCurrentCar = async (fieldId: string) => {
        try {
            message.openLoading("获取车辆信息...");
            const result = await request.post<API.Result<{ Id: string; CarNumber: string; NickName: string }>>("JiaXiao/Study/Car/getMyCar", {
                FieldId: fieldId,
            });
            message.closeLoading();

            if (result.success && result.data) {
                setSelectedCar(result.data);
            } else {
                // 如果没有绑定车辆，不显示错误，只是将selectedCar设为null
                setSelectedCar(null);
            }
        } catch (error) {
            message.closeLoading();
            console.error("获取当前绑定车辆出错:", error);
            setSelectedCar(null);
        }
    };

    const handleSubjectSelect = async (subject: SubjectItem) => {
        if (subject.id === "list") {
            Taro.navigateTo({ url: "/subpackages/student/user/study/list/index" });
        } else {
            setSelectedSubject(subject);

            // Set training info with the correct venue name
            if (subject.KeMuId === "20" || subject.KeMuId === "30") {
                const fieldInfoData = {
                    Id: subject.FieldId,
                    KeMuId: subject.KeMuId,
                    kc: subject?.kc || "未知考场",
                };
                setFieldInfo(fieldInfoData);

                // 获取当前绑定的车辆信息
                await fetchCurrentCar(subject.FieldId || "");
            }
        }
    };

    const handleScan = () => {
        // 如果已经在加载中，不重复执行
        if (scanLoading) {
            return;
        }

        // 检查是否选择了车辆
        if (!selectedCar) {
            message.error("请先绑定车辆信息");
            return;
        }

        // 检查是否有有效的考场信息
        if (!fieldInfo || !fieldInfo.Id) {
            message.error("考场信息不完整");
            return;
        }

        // 设置扫码按钮为加载状态
        setScanLoading(true);

        Taro.scanCode({
            success: async (res) => {
                console.log("扫码结果：", res);

                try {
                    // 解析扫码结果中的数据
                    const scanData = res.result;
                    console.log("解析后的数据：", scanData);
                    message.openLoading("处理中...");

                    // 使用正则表达式检查URL格式并提取Id
                    const urlRegex = /https?:\/\/51jx\.cc\/Self\/ExamSite\/Sale\?Id=([0-9a-f-]+)(?:&|$)/i;
                    const match = scanData.match(urlRegex);

                    if (!match || !match[1]) {
                        message.closeLoading();
                        message.error("二维码格式不正确");
                        // 重置扫码按钮的加载状态
                        setScanLoading(false);
                        return;
                    }

                    // 提取到的Id
                    const extractedSaleId = match[1];
                    console.log("提取到的Id:", extractedSaleId);

                    // 获取电子券详情
                    await fetchSaleInfo(extractedSaleId);

                    // 关闭加载提示
                    message.closeLoading();
                    // 重置扫码按钮的加载状态
                    setScanLoading(false);
                } catch (error) {
                    message.closeLoading();
                    console.error("处理扫码结果出错:", error);
                    message.error("处理扫码结果失败");
                    // 重置扫码按钮的加载状态
                    setScanLoading(false);
                }
            },
            fail: (err) => {
                console.error("扫码失败：", err);
                Taro.showToast({
                    title: "扫码失败",
                    icon: "none",
                });
                // 重置扫码按钮的加载状态
                setScanLoading(false);
            },
        });
    };

    const handleBack = () => {
        if (selectedSubject) {
            setSelectedSubject(null);
            setFieldInfo(null);
        } else {
            Taro.navigateBack();
        }
    };

    const handleCarChange = async () => {
        if (!fieldInfo?.Id) {
            message.error("考场信息不完整");
            return;
        }

        try {
            message.openLoading("加载中...");
            const result = await request.post<API.Result<Array<{ Id: string; CarNumber: string; NickName: string }>>>("JiaXiao/Study/Car/getCarList", {
                FieldId: fieldInfo.Id,
            });
            message.closeLoading();

            if (result.success) {
                setCarList(result.data || []);
                setShowCarModal(true);
            } else {
                message.error("获取车辆列表失败");
            }
        } catch (error) {
            message.closeLoading();
            console.error("获取车辆列表出错:", error);
            Taro.showToast({
                title: "获取车辆列表失败",
                icon: "none",
            });
        }
    };

    // 获取电子券详情
    const fetchSaleInfo = async (id: string) => {
        try {
            setSaleLoading(true);
            message.openLoading("获取电子券详情...");

            console.log("请求电子券详情，Id:", id);
            const result = await request.post<any>("JiaXiao/Study/Sale/getSaleInfo", {
                Id: id,
            });

            message.closeLoading();
            console.log("电子券原始响应:", result);

            // 检查响应是否存在
            if (!result) {
                message.error("服务器响应为空");
                return;
            }

            // 检查响应结构
            if (result.success && (result.code === 200 || result.code === 0 || result.code === undefined)) {
                console.log("电子券详情:", result);

                // 处理新的响应结构，将sale和study合并到data中
                if (result.data && (result.data.sale || result.data.study)) {
                    // 将sale和study对象的属性合并到一个新对象中
                    const combinedData = {
                        sale: { ...result.data.sale },
                        study: { ...result.data.study },
                        // 确保系统编号显示在电子券详情中
                        SysId: result.data.sale?.SysId || result.data.study?.SysId,
                        // 使用sale中的Id作为电子券ID
                        Id: result.data.sale?.Id || result.data.study?.SaleId,
                    };

                    // 创建一个新的对象保存合并后的数据
                    const processedResult = {
                        ...result,
                        data: combinedData,
                    };

                    setSaleInfo(processedResult);
                }
                //  else {
                //     // 兼容旧的数据结构
                //     setSaleInfo(result);
                // }

                // 显示电子券详情模态框
                setShowSaleModal(true);
            } else {
                console.error("获取电子券详情失败，状态码:", result?.code, "消息:", result?.message);
                message.error(result?.message || "获取电子券详情失败");
            }
        } catch (error) {
            message.closeLoading();
            console.error("获取电子券详情出错:", error);
            message.error("获取电子券详情失败");
        } finally {
            setSaleLoading(false);
        }
    };

    // 确认上车
    const handleConfirmBoarding = async () => {
        // 检查是否有有效的电子券ID
        const saleId = saleInfo?.data?.sale?.Id;
        if (!saleId || !selectedCar || !fieldInfo?.Id) {
            message.error("信息不完整，无法上车");
            return;
        }

        try {
            setConfirmLoading(true);
            message.openLoading("处理中...");

            // 使用电子券详情中的 Id
            const saleIdToUse = saleId;
            console.log("使用电子券ID:", saleIdToUse);

            // 调用使用券的接口，并传递车辆ID和考场ID
            const result = await request.put<API.Result<any>>("JiaXiao/Study/StudyExamSite/useSale", {
                SaleId: saleIdToUse,
                CarId: selectedCar.Id,
                FieldId: fieldInfo.Id,
            });

            message.closeLoading();

            if (result.success) {
                message.success(result.message || "上车成功");
                // 关闭模态框
                setShowSaleModal(false);
                // 刷新训练列表
                fetchTrainingList();
            } else {
                message.error(result.message || "上车失败");
            }
        } catch (error) {
            message.closeLoading();
            console.error("上车处理出错:", error);
            message.error("上车失败，请重试");
        } finally {
            setConfirmLoading(false);
        }
    };

    // 确认下车
    const handleConfirmDisembarking = async () => {
        // 检查是否有有效的训练ID
        const studyId = saleInfo?.data?.study?.Id;

        console.log(saleInfo);
        if (!studyId) {
            message.error("信息不完整，无法下车");
            return;
        }

        try {
            setConfirmLoading(true);
            message.openLoading("处理中...");

            // 调用下车的接口
            const result = await request.put<API.Result<any>>("JiaXiao/Study/StudyExamSite/studyDown", {
                Id: studyId,
            });

            message.closeLoading();

            if (result.success) {
                message.success(result.message || "下车成功");
                // 关闭模态框
                setShowSaleModal(false);
                // 刷新训练列表
                fetchTrainingList();
            } else {
                message.error(result.message || "下车失败");
            }
        } catch (error) {
            message.closeLoading();
            console.error("下车处理出错:", error);
            message.error("下车失败，请重试");
        } finally {
            setConfirmLoading(false);
        }
    };

    // 点击训练记录，查看电券详情
    const handleRecordClick = async (record: any) => {
        if (!record || !record.SaleId) {
            message.error("无法获取电券信息，缺少SaleId");
            return;
        }

        // 使用SaleId作为电券ID
        const saleId = record.SaleId;
        console.log("点击记录，获取电券详情，SaleId:", saleId);

        // 获取电子券详情
        await fetchSaleInfo(saleId);
    };

    const handleCarSelect = async (car: { Id: string; CarNumber: string; NickName: string }) => {
        try {
            message.openLoading("设置车辆中...");
            const result = await request.post<API.Result<any>>("JiaXiao/Study/Car/setMyCar", {
                CarId: car.Id,
            });
            message.closeLoading();

            if (result.success) {
                setSelectedCar(car);
                setShowCarModal(false);
                message.success("车辆设置成功");
            } else {
                message.error(result.message || "设置车辆失败");
            }
        } catch (error) {
            message.closeLoading();
            console.error("设置车辆出错:", error);
            message.error("设置车辆失败，请重试");
        }
    };

    return (
        <Layout>
            <View className="scan-page">
                {!selectedSubject ? (
                    <View className="subject-selection">
                        <Text className="title">请选择训练科目</Text>
                        {loading ? (
                            <View className="loading">正在加载科目...</View>
                        ) : (
                            <View className="subject-list">
                                {subjects.map((subject) => (
                                    <View
                                        key={subject.id}
                                        className={`subject-item ${subject.id === "list" ? "list-record-item" : ""}`}
                                        onClick={() => handleSubjectSelect(subject)}
                                    >
                                        <View className="circle-icon">
                                            {subject.id === "school" ? "驾" : subject.id === "subject2" ? "二" : subject.id === "subject3" ? "三" : "记"}
                                        </View>
                                        <Text>{subject.label}</Text>
                                        <IconFont name="Arrow-Right2" size={36} color="#bbbbbb" />
                                    </View>
                                ))}
                            </View>
                        )}
                    </View>
                ) : (
                    <View className="training-info-container">
                        <View className="tab-container">
                            <View
                                className={`tab-item ${activeTab === "training" ? "active" : ""}`}
                                onClick={() => {
                                    setActiveTab("training");
                                    // 切换标签后立即刷新列表
                                    setTimeout(() => fetchTrainingList(), 100);
                                }}
                            >
                                <Text>正在训练</Text>
                            </View>
                            <View
                                className={`tab-item ${activeTab === "completed" ? "active" : ""}`}
                                onClick={() => {
                                    setActiveTab("completed");
                                    // 切换标签后立即刷新列表
                                    setTimeout(() => fetchTrainingList(), 100);
                                }}
                            >
                                <Text>已经下车</Text>
                            </View>
                        </View>
                        {(selectedSubject.KeMuId === "20" || selectedSubject.KeMuId === "30") && fieldInfo && (
                            <>
                                <View className="refresh-hint-container">
                                    <Text className="refresh-hint">下拉刷新列表</Text>
                                </View>
                                <ScrollView
                                    scrollY
                                    className="scrollable-records"
                                    scrollWithAnimation
                                    refresherEnabled={true}
                                    refresherThreshold={50}
                                    refresherDefaultStyle="black"
                                    refresherBackground="#f7f8fa"
                                    refresherTriggered={listLoading}
                                    onRefresherRefresh={() => fetchTrainingList()}
                                >
                                    <View className="info-section">
                                        {/* 根据当前标签选择显示对应的列表 */}
                                        {listLoading &&
                                        ((activeTab === "training" && (!activeTrainingList || activeTrainingList.length === 0)) ||
                                            (activeTab === "completed" && (!completedTrainingList || completedTrainingList.length === 0))) ? (
                                            <View className="loading-text">正在加载训练列表...</View>
                                        ) : (activeTab === "training" && (!activeTrainingList || activeTrainingList.length === 0)) ||
                                          (activeTab === "completed" && (!completedTrainingList || completedTrainingList.length === 0)) ? (
                                            <View className="empty-text">
                                                <View className="line-decoration">
                                                    <View className="thin-line"></View>
                                                    <View className="thick-line"></View>
                                                    <View className="thin-line"></View>
                                                </View>
                                                <Text>暂无{activeTab === "training" ? "正在训练" : "已完成训练"}记录</Text>
                                                <View className="line-decoration">
                                                    <View className="thin-line"></View>
                                                    <View className="thick-line"></View>
                                                    <View className="thin-line"></View>
                                                </View>
                                            </View>
                                        ) : (
                                            // 根据当前标签选择显示对应的列表
                                            (activeTab === "training" ? activeTrainingList : completedTrainingList).map((record, index) => (
                                                <View key={index} className="record-item" onClick={() => handleRecordClick(record)}>
                                                    <View className="record-content">
                                                        <View className="left-section">
                                                            <View className="name-time-container">
                                                                <Text className="sequence-number">{index + 1}</Text>
                                                                <Text className="name">{record.xm || "未知学员"}</Text>
                                                                <Text className="boarding-time">{record.UpTime ? formatTime(record.UpTime) : ""}</Text>
                                                            </View>
                                                            <Text className="ticket-name">{record.ItemName || "未知电券"}</Text>
                                                        </View>
                                                        <View className="right-section">
                                                            {activeTab === "training" && record.UpTime ? (
                                                                <ElapsedTimeDisplay startTime={record.UpTime} />
                                                            ) : (
                                                                <Text className="time duration-time">
                                                                    {record.UpTime && record.DownTime ? calculateDuration(record.UpTime, record.DownTime) : "--:--"}
                                                                </Text>
                                                            )}
                                                        </View>
                                                    </View>
                                                </View>
                                            ))
                                        )}
                                    </View>
                                </ScrollView>

                                <View className="fixed-footer">
                                    <View
                                        className="info-row"
                                        style={{
                                            padding: "0 8rpx",
                                            marginBottom: "0rpx",
                                        }}
                                    >
                                        <Text className="small-text">考场：{fieldInfo.kc}</Text>
                                    </View>
                                    <View className="info-row vehicle-row">
                                        <Text className="small-text">车辆：{selectedCar ? `${selectedCar.CarNumber} (${selectedCar.NickName})` : "未选择"}</Text>
                                        <View className="change-btn" onClick={handleCarChange}>
                                            更换
                                        </View>
                                    </View>

                                    <View className="button-container">
                                        <Button className="back-button" onClick={handleBack}>
                                            <ArrowCornerLeft />
                                        </Button>
                                        <Button className={`scan-button ${scanLoading ? "scan-button-disabled" : ""}`} onClick={handleScan} disabled={scanLoading}>
                                            {scanLoading ? "正在获取数据" : "扫码上车"}
                                        </Button>
                                    </View>
                                </View>
                                {/* 调试信息 */}
                                {/* <View className="debug-info">
                                    <Text className="debug-text">FieldId: {fieldInfo?.Id || "无"}</Text>
                                    <Text className="debug-text">Status: {activeTab === "training" ? "1 (正在训练)" : "2 (已下车)"}</Text>
                                    <Text className="debug-text">当前列表: {activeTab === "training" ? "正在训练" : "已完成训练"}</Text>
                                    <Text className="debug-text">
                                        列表数量:{" "}
                                        {activeTab === "training"
                                            ? Array.isArray(activeTrainingList)
                                                ? activeTrainingList.length
                                                : "非数组"
                                            : Array.isArray(completedTrainingList)
                                            ? completedTrainingList.length
                                            : "非数组"}
                                    </Text>
                                </View> */}
                            </>
                        )}
                    </View>
                )}
            </View>
            {showCarModal && (
                <View className="modal-overlay">
                    <View className="modal-content car-modal">
                        <View className="modal-header">
                            <Text className="modal-title">选择车辆</Text>
                            <Close className="close-btn" onClick={() => setShowCarModal(false)} size={20} color="#666" />
                        </View>
                        <View className="modal-body car-grid">
                            {carList.map((car) => (
                                <View key={car.Id} className={`car-item ${selectedCar?.Id === car.Id ? "selected" : ""}`} onClick={() => handleCarSelect(car)}>
                                    <Text className="car-number">{car.CarNumber}</Text>
                                    <Text className="car-nickname">{car.NickName}</Text>
                                </View>
                            ))}
                        </View>
                    </View>
                </View>
            )}

            {/* 电子券详情模态框 */}
            {showSaleModal && saleInfo && (
                <View className="modal-overlay">
                    <View className="modal-content sale-modal">
                        <View className="modal-header">
                            <Text className="modal-title">电券详情</Text>
                            <Close className="close-btn" onClick={() => setShowSaleModal(false)} size={20} color="#666" />
                        </View>
                        <View className="modal-body">
                            {saleLoading ? (
                                <View className="loading-container">
                                    <Text className="loading-text">加载中...</Text>
                                </View>
                            ) : (
                                <View className="sale-info-container">
                                    <View className="sale-info-item">
                                        <Text className="sale-info-label">电券名称</Text>
                                        <Text className="sale-info-value">{saleInfo.data?.sale?.ItemName || "未知"}</Text>
                                    </View>
                                    <View className="sale-info-item">
                                        <Text className="sale-info-label">系统编号</Text>
                                        <Text className="sale-info-value">{saleInfo.data?.sale?.SysId || "未知"}</Text>
                                    </View>
                                    <View className="sale-info-item">
                                        <Text className="sale-info-label">学员姓名</Text>
                                        <Text className="sale-info-value">{saleInfo.data?.sale?.xm || "未知"}</Text>
                                    </View>
                                    <View className="sale-info-item">
                                        <Text className="sale-info-label">考场名称</Text>
                                        <Text className="sale-info-value">{saleInfo.data?.sale?.Jxmc || "未知"}</Text>
                                    </View>
                                    <View className="sale-info-item">
                                        <Text className="sale-info-label">准驾车型</Text>
                                        <Text className="sale-info-value">{saleInfo.data?.sale?.CarType || "未知"}</Text>
                                    </View>
                                    <View className="sale-info-item">
                                        <Text className="sale-info-label">截止日期</Text>
                                        <Text className="sale-info-value">{saleInfo.data?.sale?.CanUseEndTime ? formatDateTime(saleInfo.data.sale.CanUseEndTime) : "未知"}</Text>
                                    </View>
                                    <View className="sale-info-item">
                                        <Text className="sale-info-label">销售金额</Text>
                                        <Text className="sale-info-value">{saleInfo.data?.sale?.PayMoney ? `￥${saleInfo.data.sale.PayMoney.toFixed(2)}` : "未知"}</Text>
                                    </View>
                                    <View className="sale-info-item">
                                        <Text className="sale-info-label">购买时间</Text>
                                        <Text className="sale-info-value">{saleInfo.data?.sale?.SaleTime ? formatDateTime(saleInfo.data.sale.SaleTime) : "未知"}</Text>
                                    </View>
                                    {saleInfo.data?.sale?.UseTime &&
                                        moment(saleInfo.data?.sale?.UseTime).isValid() &&
                                        moment(saleInfo.data?.sale?.UseTime).isAfter(moment("2000-01-01")) && (
                                            <>
                                                <View className="sale-info-item">
                                                    <Text className="sale-info-label">使用时间</Text>
                                                    <Text className="sale-info-value">{formatDateTime(saleInfo.data.sale?.UseTime)}</Text>
                                                </View>
                                                {/* 显示下车时间，当DownTime存在且大于2000-01-01时 */}
                                                {(saleInfo.data?.study?.DownTime || saleInfo.data?.study?.DownTime) &&
                                                    moment(saleInfo.data?.study?.DownTime || saleInfo.data?.DownTime).isValid() &&
                                                    moment(saleInfo.data?.study?.DownTime || saleInfo.data?.DownTime) < moment() && (
                                                        <View className="sale-info-item">
                                                            <Text className="sale-info-label">下车时间</Text>
                                                            <Text className="sale-info-value">{formatDateTime(saleInfo.data.study?.DownTime || saleInfo.data.DownTime)}</Text>
                                                        </View>
                                                    )}
                                                {saleInfo.data?.sale?.UseUserName && (
                                                    <View className="sale-info-item">
                                                        <Text className="sale-info-label">使用人员</Text>
                                                        <Text className="sale-info-value">{saleInfo.data.sale.UseUserName}</Text>
                                                    </View>
                                                )}
                                            </>
                                        )}
                                </View>
                            )}
                        </View>
                        {/* 判断是否显示上车按钮或下车按钮 */}
                        {(() => {
                            // 获取UpTime和DownTime
                            const upTime = saleInfo?.data?.sale?.UseTime;
                            const downTime = saleInfo?.data?.study?.DownTime;

                            // 判断是否已经上车（UpTime大于2000-01-01）
                            const hasBoarded = upTime && moment(upTime).isValid() && moment(upTime).isAfter(moment("2000-01-01"));

                            // 判断是否需要显示下车按钮（没有DownTime或DownTime大于当前时间）
                            const needDisembark = !downTime || (moment(downTime).isValid() && moment(downTime).isAfter(moment()));

                            if (hasBoarded && needDisembark) {
                                // 如果已经上车且需要下车，显示下车按钮
                                return (
                                    <View className="modal-footer">
                                        <Button
                                            className={`confirm-button disembark-button ${confirmLoading ? "loading" : ""}`}
                                            onClick={handleConfirmDisembarking}
                                            disabled={confirmLoading}
                                        >
                                            {confirmLoading ? "正在提交数据" : "确认下车"}
                                        </Button>
                                    </View>
                                );
                            } else if (!hasBoarded) {
                                // 如果还没有上车，显示上车按钮
                                return (
                                    <View className="modal-footer">
                                        <Button className={`confirm-button ${confirmLoading ? "loading" : ""}`} onClick={handleConfirmBoarding} disabled={confirmLoading}>
                                            {confirmLoading ? "正在提交数据" : "确认上车"}
                                        </Button>
                                    </View>
                                );
                            }

                            // 如果已经上车且已经下车，不显示按钮
                            return null;
                        })()}
                    </View>
                </View>
            )}
        </Layout>
    );
};

export default ScanPage;
