page {
    background: #f7f8fa;
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular, Source Sans Pro;
}

.repair-list {
    // 已有的样式保持不变...
    padding: 20px;

    // 添加新的 Footer 样式
    &__footer {
        margin-top: 50rpx;

        &--loading,
        &--end {
            .flex-row {
                display: flex;
                justify-content: space-evenly;
                align-items: center;
            }
        }

        &-divider {
            height: 2rpx;
            background-color: #eeeeee;
            flex: 1;
        }

        &-text {
            font-size: 28rpx;
            color: #999999;
            padding: 0 20rpx;
        }
    }
}

.search-bar {
    &__popup {
        background-color: #fff;
        width: 100%;
        z-index: 1002;
    }

    &__container {
        width: 100%;
        background-color: #fff;
        padding: 0 20rpx;
    }

    &__content {
        padding-bottom: 20rpx;
        border-radius: 0 24rpx 0;
        color: #1a1a1a;
        font-size: 14px;
    }

    &__title {
        display: block;
        font-size: 24rpx;
        padding-top: 10rpx;
    }

    &__date-wrapper {
        display: flex;
        justify-content: center;
        width: 100%;
        margin-top: 18rpx;
    }

    &__date-picker {
        padding: 20rpx 0 16rpx;
        background-color: #f5f6f7;
        border-radius: 10rpx;
        width: calc(100vw - 100rpx);
        height: 62rpx;
        border: 2rpx solid #dfdfdf;
        text-align: center;

        &-text {
            font-size: 26rpx;
            line-height: 18rpx;
            color: #999999;
        }
    }
}

// 日期选择器样式优化
.nut-picker {
    height: 50vh;

    &-panel {
        height: 98%;
    }

    &-view {
        &-panel {
            height: 98%;
        }
    }
}

// 页面基础样式
.page {
    background: #f7f8fa;
}

// 通用 flex 布局辅助类
.flex {
    &-row {
        display: flex;
        flex-direction: row;
    }

    &-col {
        display: flex;
        flex-direction: column;
    }
}

.justify {
    &-evenly {
        justify-content: space-evenly;
    }

    &-center {
        justify-content: center;
    }
}

.items {
    &-center {
        align-items: center;
    }
}
