import { useReady } from "@tarojs/taro";
import React, { useState, useCallback, useEffect, useRef } from "react";
import { DatePicker, Empty } from "@nutui/nutui-react-taro";
import moment from "moment";
import { login } from "@utils/login";
import "./index.scss";
import "@utils/app.scss";
import Item from "./component/Item";
import TakePhoto from "./component/TakePhoto";
import Layout from "@components/Layout";
import { message } from "@components/MessageApi/MessageApiSingleton";
import request from "@service/request";

interface ListItem {
    id: string;
    repairNumber: string;
    status: string;
    repairPerson: string;
    company: string;
    description: string;
    repairTime: string;
    maintainer: string;
    progress: string;
}

const PAGE_SIZE = 15;
const INITIAL_DATE = moment().format("YYYY-MM-DD");
const DATE_RANGE = {
    start: moment().subtract(5, "years").format("YYYY-01-01"),
    end: moment().format("YYYY-12-31"),
};

// Loading Footer Component
const LoadingFooter: React.FC = () => (
    <view className="repair-list__footer repair-list__footer--loading">
        <view className="flex-row justify-evenly items-center">
            <view className="repair-list__footer-divider" />
            <text className="repair-list__footer-text">正在加载</text>
            <view className="repair-list__footer-divider" />
        </view>
    </view>
);

// End Footer Component
const EndFooter: React.FC = () => (
    <view className="repair-list__footer repair-list__footer--end">
        <view className="flex-row justify-evenly items-center">
            <view className="repair-list__footer-divider" />
            <text className="repair-list__footer-text">已经到底部</text>
            <view className="repair-list__footer-divider" />
        </view>
    </view>
);

// Main Component
const App: React.FC = () => {
    const [pageIndex, setPageIndex] = useState(1);
    const [pageCount, setPageCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const [searchBarOpen, setSearchBarOpen] = useState(false);
    const [data, setData] = useState<ListItem[]>([]);
    const [dataCount, setDataCount] = useState(0);
    const [studyDateOpen, setStudyDateOpen] = useState(false);
    const [studyDate, setStudyDate] = useState(INITIAL_DATE);

    // 添加获取数据的方法
    const fetchData = useCallback(async (page: number, date: string) => {
        try {
            setLoading(true);

            request
                .post<any>("/JiaXiao/Study/WxStudy/studyList", {
                    pageIndex: page,
                    pageSize: PAGE_SIZE,
                    studyDate: date,
                })
                .then((json) => {
                    setLoading(false);
                    const { data, total, pages } = json.data;

                    if (page === 1) {
                        setData(data);
                    } else {
                        setData((prev) => [...prev, ...data]);
                    }
                    setDataCount(total);
                    setPageCount(pages);
                });
        } catch (error) {
            message.error("获取数据失败");
            console.error("获取数据失败:", error);
        } finally {
            setLoading(false);
        }
    }, []);

    // 处理日期确认
    const handleDateConfirm = useCallback(
        (options: any, values: any[]) => {
            const newDate = moment(`${values[0]}-${values[1]}-${values[2]}`).format("YYYY-MM-DD");
            setStudyDate(newDate);
            setPageIndex(1); // 重置页码
            fetchData(1, newDate); // 获取新日期的数据
            setStudyDateOpen(false);
            setSearchBarOpen(false);
        },
        [fetchData]
    );

    // 处理滚动加载
    const handleScroll = useCallback(
        async (e: any) => {
            if (loading || pageIndex >= pageCount) return;

            const { scrollTop, scrollHeight, clientHeight } = e.target;
            if (scrollHeight - scrollTop - clientHeight < 50) {
                const nextPage = pageIndex + 1;
                setPageIndex(nextPage);
                await fetchData(nextPage, studyDate);
            }
        },
        [loading, pageIndex, pageCount, fetchData, studyDate]
    );

    // 初始化加载
    useEffect(() => {
        fetchData(1, studyDate);
    }, [fetchData, studyDate]);

    const [userInfo, setUserInfo] = useState<API.UserInfo>();
    // 获取用户信息
    useReady(async () => {
        try {
            const userInfo = await login();
            console.log("获取到用户信息:", userInfo);
            setUserInfo(userInfo);
            // 使用 userInfo 做后续操作
        } catch (error) {
            console.error("获取用户信息失败:", error);
        }
    });

    return (
        <Layout>
            {/* Search Bar */}
            <view className="search-bar__popup">
                <view className="search-bar__container" id="search-bar-top">
                    <view className="search-bar__content">
                        <text className="search-bar__title">自定义日期筛选</text>
                        <view className="search-bar__date-wrapper">
                            <view
                                className="search-bar__date-picker"
                                onClick={() => {
                                    if (!searchBarOpen) {
                                        setSearchBarOpen(true);
                                        setStudyDateOpen(true);
                                    }
                                }}
                            >
                                <text className="search-bar__date-picker-text">
                                    {studyDate === "" ? "选择查询日期" : moment(studyDate).format("YYYY-MM-DD") === moment().format("YYYY-MM-DD") ? `${studyDate} 今天` : studyDate}
                                </text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            {/* List View */}
            <view className="repair-list" id="primaryScroll" onScroll={handleScroll}>
                {data.length > 0 ? (
                    <>
                        {data.map((item: any) => (
                            <Item key={item.Id} data={item} onCallback={() => {}} />
                        ))}
                        {loading && <LoadingFooter />}
                        {pageIndex >= pageCount && !loading && <EndFooter />}
                    </>
                ) : (
                    !loading && <Empty description="无数据" style={{ margin: "0px" }} />
                )}
            </view>

            {/* Date Picker Popup */}
            {studyDateOpen && (
                <view className="search-bar__popup">
                    <DatePicker
                        title="选择查询日期"
                        visible={studyDateOpen}
                        value={studyDate === "" ? new Date() : new Date(studyDate)}
                        showChinese
                        onClose={() => {
                            setStudyDateOpen(false);
                            setSearchBarOpen(false);
                        }}
                        threeDimensional={true}
                        onConfirm={handleDateConfirm}
                        startDate={new Date(DATE_RANGE.start)}
                        endDate={new Date(DATE_RANGE.end)}
                    />
                </view>
            )}
            <TakePhoto />
        </Layout>
    );
};

export default App;
