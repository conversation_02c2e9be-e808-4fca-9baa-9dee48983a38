.photo-capture {

    // Footer styles
    &__footer {
        border-radius: 24px 24px 0 0 !important; // 增加重要性，确保样式生效
        padding: 20rpx 20rpx 30rpx 20rpx;
        background-color: #fff;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 100;
    }

    // Popup styles
    &__popup {
        z-index: 9999;
        width: 100%;
    }

    // Container styles
    &__container {
        padding: 0 62rpx 42rpx;
        background-color: #ffffff;
    }

    &__content {
        display: flex;
        flex-direction: column;
    }

    // Header section
    &__header {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 30rpx;
    }

    &__title {
        color: #222222;
        font-size: 40rpx;
        font-weight: 700;
        line-height: 48rpx;
        margin-top: 32rpx;
    }

    &__subtitle {
        font-size: 32rpx;
        line-height: 80rpx;
        color: rgba(34, 34, 34, 0.5);
    }

    // Camera section
    &__camera-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 20rpx 0;
    }

    &__camera {
        border-radius: 50%;
        overflow: hidden;
        // 注意：主要样式在内联style中定义
    }

    // Action button section
    &__action {
        margin-top: 50rpx;
        padding: 0 20rpx;
    }
}

// Utility classes
.flex {
    &-col {
        display: flex;
        flex-direction: column;
    }

    &-row {
        display: flex;
        flex-direction: row;
    }
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

// Spacing utilities
.mt-8 {
    margin-top: 16rpx;
}

.photo-capture__popup {
    .nut-popup {
        border-radius: 24px 24px 24px 24px !important; // 增加重要性，确保样式生效
    }
}