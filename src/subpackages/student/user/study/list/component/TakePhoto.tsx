import Layout from "@components/Layout";
import { message } from "@components/MessageApi/MessageApiSingleton";
import { Button, Popup } from "@nutui/nutui-react-taro";
import { Camera } from "@tarojs/components";
import { useReady } from "@tarojs/taro";
import { tool } from "@utils/tool";
import Taro from "@tarojs/taro";
import upload from "@service/upload";
import React, { useCallback, useState, useRef, useEffect } from "react";
import "./take-photo.scss";

interface Props {
    onCallBack?: () => void;
}

interface FaceSearchResponse {
    success: boolean;
    message: string;
    data: {
        xm: string;
        SaleUserName: string;
        [key: string]: any;
    };
}

type method = {};

const DOUBLE_CLICK_THRESHOLD = 300;

const TakePhoto = React.forwardRef<method, Props>((props, ref) => {
    const [takeFaceOpen, setTakeFaceOpen] = useState(false);
    const [scanLoading, setScanLoading] = useState(false);
    const [takePhotoLoading, setTakePhotoLoading] = useState(false);
    const [cameraInitializing, setCameraInitializing] = useState(false); // 新增相机初始化状态
    const [data, setData] = useState<any[]>([]);
    const lastTapTimeRef = useRef(0);
    const tapCountRef = useRef(0);

    const DevicePositionValues = {
        front: "front",
        back: "back",
    } as const;

    const [devicePosition, setDevicePosition] = useState<
        (typeof DevicePositionValues)[keyof typeof DevicePositionValues]
    >(DevicePositionValues.back);

    // 添加相机初始化监听
    useEffect(() => {
        if (takeFaceOpen) {
            setCameraInitializing(true);
            // 模拟相机启动时间，您可以根据实际情况调整或移除这个延时
            setTimeout(() => {
                setCameraInitializing(false);
            }, 1000);
        } else {
            setCameraInitializing(false);
        }
    }, [takeFaceOpen]);

    const handleCameraSwitch = useCallback(() => {
        const currentTime = Date.now();
        const timeDiff = currentTime - lastTapTimeRef.current;

        if (timeDiff < DOUBLE_CLICK_THRESHOLD) {
            const newPosition = devicePosition === "back" ? "front" : "back";
            tool.data.set("longtime-study-device-position", newPosition);
            setDevicePosition(newPosition);
            tapCountRef.current = 0;
            lastTapTimeRef.current = 0;
            message.openToast("已切换摄像头");
        } else {
            lastTapTimeRef.current = currentTime;
            tapCountRef.current += 1;
            setTimeout(() => {
                if (tapCountRef.current === 1) {
                    tapCountRef.current = 0;
                    lastTapTimeRef.current = 0;
                }
            }, DOUBLE_CLICK_THRESHOLD);
        }
    }, [devicePosition]);

    const handleScanFace = useCallback(() => {
        setScanLoading(true);
        setTakeFaceOpen(true);
        setScanLoading(false);
    }, []);

    const handleTakePhoto = useCallback(async () => {
        if (takePhotoLoading) {
            message.openToast("请等待当前操作完成");
            return;
        }

        try {
            const context = Taro.createCameraContext();
            context.takePhoto({
                quality: "high",
                success: (res) => {
                    setTakePhotoLoading(true);
                    upload
                        .post<FaceSearchResponse>(
                            "/JiaXiao/Study/WxStudy/faceLogOn",
                            {},
                            res.tempImagePath,
                            "file"
                        )
                        .then((json) => {
                            setTakePhotoLoading(false);
                            if (json && json.success) {
                                setData([json.data, ...data.slice(0, 9)]);

                                message.openToast(json.message);

                                setTakeFaceOpen(false);
                            } else if (json) {
                                message.openToast(json.message);
                            }
                        })
                        .catch((e) => {
                            setTakePhotoLoading(false);
                            message.openToast(JSON.stringify(e));
                        });
                },
                fail: (error) => {
                    setTakePhotoLoading(false);
                    message.openToast("拍照失败: " + JSON.stringify(error));
                },
            });
        } catch (error) {
            setTakePhotoLoading(false);
            message.openToast("相机操作失败: " + JSON.stringify(error));
        }
    }, [takePhotoLoading, data]);

    useReady(() => {
        const position = tool.data.get("longtime-study-device-position");
        if (position === "back" || position === "front") {
            setDevicePosition(position);
        }
    });

    return (
        <Layout>
            <view className="safe__area">
                <view className="photo-capture__footer">
                    <Button
                        block
                        type="primary"
                        onClick={handleScanFace}
                        disabled={scanLoading}
                    >
                        {scanLoading ? "准备中..." : "扫脸上车"}
                    </Button>
                </view>
            </view>

            {/* Camera Popup */}
            {takeFaceOpen && (
                <view className="photo-capture__popup">
                    <Popup
                        visible={takeFaceOpen}
                        onClose={() =>
                            !takePhotoLoading && setTakeFaceOpen(false)
                        }
                    >
                        <view className="photo-capture__container">
                            <view className="photo-capture__content">
                                <view className="photo-capture__header">
                                    <text className="photo-capture__title">
                                        请保持正脸在取景框中
                                    </text>
                                    <text className="photo-capture__subtitle">
                                        双击取景窗切换镜头
                                    </text>
                                </view>
                                <view
                                    className="photo-capture__camera-wrapper"
                                    onClick={handleCameraSwitch}
                                >
                                    <Camera
                                        mode="normal"
                                        devicePosition={devicePosition}
                                        className="photo-capture__camera"
                                        flash="off"
                                        style={{
                                            height: "75vw",
                                            width: "75vw",
                                            borderRadius: "50%",
                                        }}
                                    />
                                </view>
                                <view className="photo-capture__action">
                                    <Button
                                        block
                                        type="primary"
                                        onClick={handleTakePhoto}
                                        disabled={
                                            takePhotoLoading ||
                                            cameraInitializing
                                        }
                                    >
                                        {cameraInitializing
                                            ? "正在启动相机..."
                                            : takePhotoLoading
                                            ? "识别中..."
                                            : "拍照"}
                                    </Button>
                                </view>
                            </view>
                        </view>
                    </Popup>
                </view>
            )}
        </Layout>
    );
});

export default TakePhoto;
