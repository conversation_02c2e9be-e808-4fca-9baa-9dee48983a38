import { Button } from "@nutui/nutui-react-taro";
import React, { useEffect, useState } from "react";
import "./item.scss";
import moment from "moment";
import { message } from "@components/MessageApi/MessageApiSingleton";
import request from "@service/request";

interface Props {
    data: {
        Id: string;
        RowIndex: number;
        SysId: string;
        UpTime: string;
        KeMuText: string;
        xm: string;
        Ywzt: string;
        DownTime: string;
    };
    onCallback: () => void;
}

interface TimeDisplay {
    minutes: number;
    seconds: number;
    milliseconds: number;
}

type method = {};

const Item = React.forwardRef<method, Props>((props, ref) => {
    const [elapsedTime, setElapsedTime] = useState<TimeDisplay>({
        minutes: 0,
        seconds: 0,
        milliseconds: 0,
    });

    useEffect(() => {
        const calculateElapsed = (): void => {
            const startTime = new Date(props.data.UpTime).getTime();
            const currentTime =
                new Date(props.data.DownTime) < new Date()
                    ? new Date(props.data.DownTime).getTime()
                    : new Date().getTime();

            const diff = currentTime - startTime;

            const minutes = Math.floor(diff / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);
            const milliseconds = Math.floor((diff % 1000) / 10); // 只显示前两位毫秒

            setElapsedTime({
                minutes,
                seconds,
                milliseconds,
            });
        };

        calculateElapsed();
        if (moment(props.data.DownTime) > moment(new Date())) {
            // 更新间隔设为10毫秒以获得流畅的毫秒显示
            const timer = setInterval(calculateElapsed, 10);
            return () => clearInterval(timer);
        }
    }, [props.data.UpTime]);

    // 格式化显示，确保两位数字显示
    const formatNumber = (num: number, digits: number = 2): string => {
        return num.toString().padStart(digits, "0");
    };

    const downCar = () => {
        message.openConfirm(
            `是否确认 ${props.data.xm} 下车的操作，该操作不可逆，如果还需要训练，需要重新上车操作!`,
            "确认操作",
            () => {
                message.openLoading("正在操作");
                request
                    .post<any>("/JiaXiao/Study/WxStudy/studyDown", {
                        Id: props.data.Id,
                    })
                    .then((json) => {
                        message.closeLoading();
                        message.openToast(json.message);

                        if (props.onCallback) {
                            props.onCallback();
                        }
                    });
            }
        );
    };

    return (
        <view className="order-item" onClick={() => {}}>
            {/* 订单头部 */}
            <view className="order-item__header">
                <text className="order-item__number">
                    {props.data.RowIndex} # {props.data.xm}
                </text>
                <text className="order-item__status">{props.data.Ywzt}</text>
            </view>
            <view className="order-item__row">
                <view className="flex items-center">
                    <text className="order-item__label">开始时间：</text>
                    <text className="order-item__value">
                        {moment(props.data.UpTime).format("HH:mm")}
                    </text>
                </view>
                <view className="flex items-center ml-auto">
                    <text className="order-item__label">时长：</text>
                    <text
                        className="order-item__value"
                        style={{ width: "100rpx" }}
                    >
                        {elapsedTime.minutes >= 0
                            ? `${formatNumber(
                                  elapsedTime.minutes
                              )}:${formatNumber(
                                  elapsedTime.seconds
                              )}.${formatNumber(elapsedTime.milliseconds)}`
                            : "计算中..."}
                    </text>
                </view>
            </view>

            {/* 计费规则 */}
            <view className="order-item__row">
                <text className="order-item__label">培训科目：</text>
                <text className="order-item__value">{props.data.KeMuText}</text>
            </view>

            {/* 底部累计租金 */}
            <view className="order-item__footer">
                <text className="order-item__label"></text>
                <Button
                    type={"primary"}
                    onClick={downCar}
                    disabled={new Date(props.data.DownTime) < new Date()}
                >
                    下车
                </Button>
            </view>
        </view>
    );
});

export default Item;
