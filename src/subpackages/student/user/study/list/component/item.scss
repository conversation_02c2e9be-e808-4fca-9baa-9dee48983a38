// SCSS 样式

.order-item {
    background-color: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;

    &:first-child {
        margin-top: 0;
    }

    // 头部样式
    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 36rpx 0rpx 28rpx 0rpx;
        border-bottom: solid 1rpx #eeeeee;
        margin: 0 30rpx;
    }

    &__number {
        font-size: 32rpx;
        font-family: PingFangSC;
        line-height: 28rpx;
        color: #333333;
    }

    &__status {
        font-size: 24rpx;
        font-family: PingFangSC;
        line-height: 28rpx;
        color: #f68e4a;
    }

    // 行样式
    &__row {
        display: flex;
        align-items: center;
        padding: 0 30rpx;
        margin-top: 24rpx;
    }

    &__start-time {
        margin-left: 24rpx;
        margin-top: 32rpx;
        width: 376.46rpx;
    }

    &__label {
        font-size: 24rpx;
        font-family: PingFangSC;
        line-height: 22.3rpx;
        color: #999999;
        flex-shrink: 0;
    }

    &__value {
        font-size: 24rpx;
        font-family: PingFangSC;
        line-height: 28rpx;
        color: #999999;
        flex: 1;
    }

    // 押金行特殊样式
    &__deposit {
        padding: 0 28rpx;
        margin-top: 28rpx;
    }

    // 底部样式
    &__footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20rpx;
        padding: 20rpx 0rpx 20rpx 0rpx;
        border-top: solid 1rpx #eeeeee;
        margin: 20rpx 30rpx 0 30rpx;
        button {
            height: 46rpx;
            font-size: 24rpx;
            border-radius: 4rpx;
        }
    }

    &__total {
        display: flex;
        align-items: center;
    }

    &__price {
        font-size: 32rpx;
        font-family: PingFangSC;
        line-height: 26.84rpx;
        color: #f68e4a;
    }

    &__arrow {
        margin-right: 8rpx;
        width: 18rpx;
        height: 32rpx;
    }
    &__label {
        color: #666;
        font-size: 24rpx;
        margin-right: 8px;
    }

    &__value {
        color: #333;
        font-size: 24rpx;
    }

    .flex {
        display: flex;
    }

    .items-center {
        align-items: center;
    }

    .ml-auto {
        margin-left: auto;
    }
}

// 主题变量
:root {
    --order-primary-color: #f68e4a;
    --order-text-color: #333333;
    --order-text-secondary: #999999;
    --order-border-color: #eeeeee;
    --order-background: #ffffff;
}
