// App.jsx
import { useReady } from "@tarojs/taro";
import React, { useState } from "react";
import "./index.scss";
import "@utils/app.scss";
import Taro from "@tarojs/taro";
import request from "@service/request";
import { message } from "@components/MessageApi/MessageApiSingleton";
import { useStudentForm } from "./hooks/useStudentForm";
import { IdCardUpload } from "./components/IdCardUpload";
import { StudentForm } from "./components/StudentForm";
import Layout from "@components/Layout";
import { UploadStatus } from "./components/UploadStatus";
import { SlideDownLoader } from "./components/SlideDownLoader";
import { login } from "@utils/login";
import TopNavBar from "@/components/topNavBar";

const StudentRegistration = () => {
    const [stepIndex, setStepIndex] = useState(1);
    const [openTakePhoto, setOpenTakePhoto] = useState(0);
    const [uploadStatus, setUploadStatus] = useState<{
        show: boolean;
        isSuccess: boolean;
    }>({ show: false, isSuccess: false });
    const [frontStatus, setFrontStatus] = useState<"uploading" | "success" | "error" | undefined>(undefined);
    const [backStatus, setBackStatus] = useState<"uploading" | "success" | "error" | undefined>(undefined);

    const { formData, setFormData, isLoading, loadingText, handleChange, handleSubmit, setIsLoading, setLoadingText, pickerStates, setPickerStates, pickerLists, setPickerLists } =
        useStudentForm({}, message);

    useReady(() => {
        login().then(() => {
            request.post<any>("/Wx/JxUser/getMyJxUser", {}).then((json) => {
                if (json && json.success) {
                    setFormData((prev) => ({
                        ...prev,
                        jxDeptId: json.data.JxDeptId,
                        jxFieldId: json.data.JxFieldId,
                        userId: json.data.UserId,
                    }));

                    // 获取证件类型列表
                    request
                        .post<any>("/Base/Select/getSfzmmcSelectList", {})
                        .then((json) => {
                            if (json && json.success) {
                                setPickerLists((prev) => ({
                                    ...prev,
                                    sfzmmcList: json.data,
                                }));
                            } else {
                                message.error("系统故障，稍后重试!", "操作失败");
                            }
                        })
                        .catch(() => {
                            message.error("系统故障，稍后重试!", "操作失败");
                        });

                    // 获取培训班型列表
                    request
                        .post<any>("/JiaXiao/JxClass/getJxClassSelectList", {})
                        .then((json) => {
                            if (json && json.success) {
                                setPickerLists((prev) => ({
                                    ...prev,
                                    jxClassList: json.data[0].options,
                                }));
                            } else {
                                message.error("系统故障，稍后重试!", "操作失败");
                            }
                        })
                        .catch(() => {
                            message.error("系统故障，稍后重试!", "操作失败");
                        });
                } else {
                    message.error(json.message, "无法报名", () => {
                        Taro.navigateTo({
                            url: "/subpackages/student/user/index/index",
                        });
                    });
                }
            });
        });
    });

    const handleTakePhoto = async (imageId: number): Promise<{ id: string }> => {
        setOpenTakePhoto(0);
        const res = await Taro.chooseImage({
            count: 1,
            sizeType: ["original"],
            sourceType: ["camera"],
        });

        if (imageId === 1) {
            handleChange("sfzzm", res.tempFilePaths[0]);
        }
        if (imageId === 2) {
            handleChange("sfzfm", res.tempFilePaths[0]);
        }
        return { id: res.tempFilePaths[0] };
    };

    const handleChooseImage = async (imageId: number, sourceType: ("camera" | "album")[]): Promise<{ id: string }> => {
        setOpenTakePhoto(0);
        const res = await Taro.chooseImage({
            count: 1,
            sizeType: ["original"],
            sourceType,
        });

        if (imageId === 1) {
            handleChange("sfzzm", res.tempFilePaths[0]);
        }
        if (imageId === 2) {
            handleChange("sfzfm", res.tempFilePaths[0]);
        }
        return { id: res.tempFilePaths[0] };
    };

    const handleIdCardInfo = (info: any) => {
        // 保存身份证信息和图片数据
        setFormData((prev) => ({
            ...prev,
            ...info,
            cardImages: info.cardImages, // 包含了front和back的base64数据
        }));
    };

    return (
        <Layout>
            <TopNavBar title="学员录入" homeClick={() => Taro.navigateTo({ url: "/subpackages/student/user/index/index" })} />
            <SlideDownLoader isLoading={isLoading} loadingText={loadingText} />

            {stepIndex === 1 && (
                <>
                    <IdCardUpload
                        frontStatus={frontStatus}
                        setFrontStatus={setFrontStatus}
                        backStatus={backStatus}
                        setBackStatus={setBackStatus}
                        sfzzm={formData.sfzzm}
                        sfzfm={formData.sfzfm}
                        isLoading={isLoading}
                        loadingText={loadingText}
                        openTakePhoto={openTakePhoto}
                        setOpenTakePhoto={setOpenTakePhoto}
                        onManualEntry={() => setStepIndex(2)}
                        message={message}
                        onTakePhoto={handleTakePhoto}
                        onChooseImage={handleChooseImage}
                        onIdCardInfo={handleIdCardInfo}
                        onStepChange={(step) => setStepIndex(step)}
                        setIsLoading={setIsLoading}
                        setLoadingText={setLoadingText}
                    />
                    {uploadStatus.show && <UploadStatus isSuccess={uploadStatus.isSuccess} />}
                </>
            )}

            {stepIndex === 2 && (
                <StudentForm
                    formData={formData}
                    onChange={handleChange}
                    onSubmit={handleSubmit}
                    pickerLists={pickerLists}
                    setPickerLists={setPickerLists}
                    pickerStates={pickerStates}
                    setPickerStates={setPickerStates}
                    isLoading={isLoading}
                    loadingText={loadingText}
                    messageRef={message}
                />
            )}
        </Layout>
    );
};

export default StudentRegistration;
