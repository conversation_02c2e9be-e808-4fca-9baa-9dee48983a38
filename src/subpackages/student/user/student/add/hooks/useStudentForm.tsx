// import { message } from "@components/MessageApi/MessageApiSingleton";
import request from "@service/request";
import upload from "@service/upload";
import Taro from "@tarojs/taro";
import { useState } from "react";

export const useStudentForm = (initialData = {}, message) => {
    const [formData, setFormData] = useState({
        jxDeptId: "",
        jxFieldId: "",
        userId: "",
        xm: "",
        sfzmmc: "A",
        sfzmhm: "",
        xb: 1,
        djzsxxdz: "",
        lxzsxxdz: "",
        remark: "",
        sfzzm: "",
        sfzfm: "",
        csrq: "",
        fzrq: "",
        yxqz: "",
        yddh: "",
        carType: "",
        jxClassId: "",
        ...initialData,
    });

    const [isLoading, setIsLoading] = useState(false);
    const [loadingText, setLoadingText] = useState("");

    const [pickerStates, setPickerStates] = useState({
        sfzmmcOpen: false,
        xbOpen: false,
        csrqOpen: false,
        fzrqOpen: false,
        yxqzOpen: false,
        jxClassOpen: false,
        carTypeOpen: false,
    });

    const [pickerLists, setPickerLists] = useState({
        sfzmmcList: [],
        xbList: [
            { text: "男", value: 1 },
            { text: "女", value: 2 },
        ],
        jxClassList: [],
        carTypeList: [],
    });

    const handleChange = (field, value) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    const handleSubmit = async () => {
        // 表单验证
        if (formData.xm === "") {
            message.openToast("请输入学员姓名");
            return;
        }
        if (formData.sfzmhm === "") {
            message.openToast("请输入身份证号码");
            return;
        }
        if (formData.sfzmhm.length < 18) {
            message.openToast("请输入正确的身份证号码");
            return;
        }
        if (formData.yddh.length < 11) {
            message.openToast("请输入正确的手机号码");
            return;
        }
        if (formData.jxClassId === "") {
            message.openToast("请选择培训班型");
            return;
        }
        if (formData.carType === "") {
            message.openToast("请选择培训车型");
            return;
        }

        message.openConfirm("是否确认提交学员信息", "确认操作", async () => {
            try {
                setIsLoading(true);
                setLoadingText("正在获取学费信息");
                message.openLoading("正在获取学费信息");

                // 获取学费信息
                const priceResult = await request.post<any>("/JiaXiao/JxClass/getCarTypePrice", {
                    JxClassId: formData.jxClassId,
                    JxDeptId: formData.jxDeptId,
                    JxFieldId: formData.jxFieldId,
                    CarType: formData.carType,
                });

                setIsLoading(true);
                setLoadingText("正在保存");
                // message.openLoading("正在保存学员信息");

                // 构建提交数据
                const postData = {
                    xm: formData.xm,
                    sfzmmc: formData.sfzmmc,
                    sfzmhm: formData.sfzmhm,
                    xb: formData.xb,
                    yddh: formData.yddh,
                    gj: 156,
                    djzsxxdz: formData.djzsxxdz,
                    lxzsxxdz: formData.lxzsxxdz,
                    csrq: formData.csrq,
                    JxClassId: formData.jxClassId,
                    CarType: formData.carType,
                    SaleUserId: formData.userId,
                    JxDeptId: formData.jxDeptId,
                    JxFieldId: formData.jxFieldId,
                    PayMoney: priceResult.data.PayMoney,
                    JxShouldPayDetails: priceResult.data.ShouldPayDetail,
                };

                // 添加可选字段
                if (formData.fzrq) {
                    postData["sfzyxqs"] = formData.fzrq;
                }
                if (formData.yxqz) {
                    postData["sfzyxqz"] = formData.yxqz;
                }
                // 保存学员信息
                const saveResult = await request.put<API.SelectResult>("/Jx/Student/StudentInfo/00000000-0000-0000-0000-000000000000", postData);

                if (!saveResult.success) {
                    message.openDialog("保存失败", saveResult.message);
                    return;
                }

                const studentId = saveResult.data;

                // 上传身份证图片
                if (formData.sfzzm) {
                    setIsLoading(true);
                    setLoadingText("正在上传身份证图片");
                    // message.openLoading("正在上传身份证正面");

                    await upload.post(
                        "/Jx/Image/StudentImage/uploadStudentImage",
                        {
                            StudentId: studentId,
                            ImageId: 40,
                        },
                        formData.sfzzm,
                        "file"
                    );

                    if (formData.sfzfm) {
                        // message.openLoading("正在上传身份证反面");
                        await upload.post(
                            "/Jx/Image/StudentImage/uploadStudentImage",
                            {
                                StudentId: studentId,
                                ImageId: 41,
                            },
                            formData.sfzfm,
                            "file"
                        );

                        setLoadingText("正在合成正反面");
                        // message.openLoading("正在合成正反面");
                        await request.post("/Jx/Image/StudentImage/makeMyIdCard", {
                            Id: studentId,
                        });
                    }
                }

                message.openDialog("保存成功", "保存学员资料完成，请前往学员详情界面完善其他的资料", () => {
                    Taro.navigateTo({
                        url: `/subpackages/student/user/student/index/index?id=${studentId}`,
                    });
                });
            } catch (error) {
                message.error("系统出现错误，请稍后重试", "保存失败");
            } finally {
                setIsLoading(false);
            }
        });
    };

    return {
        formData,
        setFormData,
        isLoading,
        loadingText,
        handleChange,
        handleSubmit,
        setIsLoading,
        setLoadingText,
        pickerStates,
        setPickerStates,
        pickerLists,
        setPickerLists,
    };
};
