import React from "react";
import "./index.scss";

import { Image } from "@tarojs/components";

interface UploadStatusProps {
    isSuccess: boolean;
}

export const UploadStatus: React.FC<UploadStatusProps> = ({ isSuccess }) => {
    const imageUrl = isSuccess
        ? "https://cdn.51panda.com/wx/idcard2/11925506e02093b15ff12c2ffed8c146.png"
        : "https://cdn.51panda.com/wx/idcard2/330a4d8d8d876be7583d1e2c3e08726a.png";

    const text = isSuccess ? "上传成功" : "无法识别请重新上传";
    const sectionClass = isSuccess ? "section_3" : "section_5";
    const textClass = isSuccess ? "text_5" : "text_7";

    return (
        <view className={`flex-col items-center ${sectionClass} pos_2`}>
            <Image className="image_6" src={imageUrl} />
            <text className={`${isSuccess ? "mt-6 font_2" : "font mt-5"} ${textClass}`}>{text}</text>
        </view>
    );
};
