import { View, Text } from "@tarojs/components";
import React, { useEffect, useState } from "react";
import "./SlideDownLoader.scss";

interface SlideDownLoaderProps {
    isLoading: boolean;
    loadingText: string;
}

export const SlideDownLoader: React.FC<SlideDownLoaderProps> = ({ isLoading, loadingText }) => {
    const [visible, setVisible] = useState(false);

    useEffect(() => {
        if (isLoading) {
            setVisible(true);
        } else {
            // 添加延迟以便动画完成
            const timer = setTimeout(() => {
                setVisible(false);
            }, 300);
            return () => clearTimeout(timer);
        }
    }, [isLoading]);

    if (!visible && !isLoading) return null;

    return (
        <View className={`slide-down-loader ${isLoading ? "show" : "hide"}`}>
            <View className="loader-icon"></View>
            <Text className="loader-text">{loadingText || "加载中..."}</Text>
        </View>
    );
};
