import IconFont from "@components/iconfont";
import request from "@service/request";

export const TrainingSection = ({ formData, pickerLists, pickerStates, setPickerStates, onChange, messageRef }) => {
    const handleJxClassChange = async (value) => {
        if (formData.jxClassId !== value) {
            onChange("jxClassId", value);

            messageRef.current.openLoading("正在获取相关车型");
            try {
                const result = await request.post<any>("/JiaXiao/ClassCarType/getCarTypeSelectList", {
                    JxDeptId: formData.jxDeptId,
                    JxClassId: value,
                });

                messageRef.current.closeLoading();

                if (result && result.success) {
                    if (result.data.length === 0) {
                        messageRef.current.openDialog("操作失败", "该班型暂无相关车型");
                    } else {
                        onChange("carTypeList", result.data);
                        setPickerStates((prev) => ({ ...prev, carTypeOpen: true }));
                        onChange("carType", "");
                    }
                } else {
                    messageRef.current.openDialog("操作失败", result.message);
                }
            } catch (error) {
                messageRef.current.closeLoading();
                messageRef.current.openDialog("操作失败", "系统故障，稍后重试!");
            }
        }
    };

    return (
        <>
            <view className="flex-col mt-8">
                <text className="self-start font_3 text_31">培训设置</text>
                <view className="mt-18 flex-col self-stretch section_3">
                    <view className="flex-row justify-between group_19" onClick={() => setPickerStates((prev) => ({ ...prev, jxClassOpen: true }))}>
                        <view className="flex-row">
                            <text className="font_3">培训班型</text>
                            <text className="ml-8 self-start font_6">*</text>
                        </view>
                        <view className="flex-row items-center">
                            <text className="font text_5">{pickerLists.jxClassList.find((m) => m.value === formData.jxClassId)?.text || "请选择培训班型"}</text>
                            <IconFont name="Arrow-Right2" size={30} style={{ marginTop: "0rpx", marginLeft: "10rpx" }} />
                        </view>
                    </view>

                    <view className="flex-row justify-between group_19" onClick={() => setPickerStates((prev) => ({ ...prev, carTypeOpen: true }))}>
                        <view className="flex-row">
                            <text className="font_3">培训车型</text>
                            <text className="ml-8 self-start font_6">*</text>
                        </view>
                        <view className="flex-row items-center">
                            <text className="font text_5">{pickerLists.carTypeList.find((m) => m.value === formData.carType)?.text || "请选择培训车型"}</text>
                            <IconFont name="Arrow-Right2" size={30} style={{ marginTop: "0rpx", marginLeft: "10rpx" }} />
                        </view>
                    </view>
                </view>
            </view>
        </>
    );
};
