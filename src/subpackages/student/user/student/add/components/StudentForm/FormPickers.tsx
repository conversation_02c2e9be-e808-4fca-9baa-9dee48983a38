import { message } from "@components/MessageApi/MessageApiSingleton";
import { DatePicker, Picker } from "@nutui/nutui-react-taro";
import request from "@service/request";
import moment from "moment";

export const FormPickers = ({ formData, pickerStates, setPickerStates, pickerLists, setPickerLists, onChange }) => {
    const handleJxClassChange = async (value) => {
        if (formData.jxClassId !== value) {
            onChange("jxClassId", value);

            message.openLoading("正在获取相关车型");
            try {
                const result = await request.post<any>("/JiaXiao/ClassCarType/getCarTypeSelectList", {
                    JxDeptId: formData.jxDeptId,
                    JxClassId: value,
                });

                message.closeLoading();

                console.log(result);

                if (result && result.success) {
                    if (result.data.length === 0) {
                        message.openDialog("操作失败", "该班型暂无相关车型");
                    } else {
                        // 同时更新 formData 和 pickerLists
                        onChange("carTypeList", result.data);
                        // 假设 API 返回的数据格式需要转换
                        const formattedData = result.data.map((item) => ({
                            value: item.value || item.id,
                            text: item.text || item.name,
                        }));
                        // 更新 pickerLists
                        setPickerLists((prev) => ({
                            ...prev,
                            carTypeList: formattedData,
                        }));
                        setPickerStates((prev) => ({ ...prev, carTypeOpen: true }));
                        onChange("carType", "");
                    }
                } else {
                    message.openDialog("操作失败", result.message);
                }
            } catch (error) {
                message.closeLoading();
                message.openDialog("操作失败", "系统故障，稍后重试!");
            }
        }
    };
    return (
        <>
            <Picker
                title="选择证件类型"
                visible={pickerStates.sfzmmcOpen}
                options={pickerLists.sfzmmcList}
                defaultValue={[formData.sfzmmc]}
                onConfirm={(_options, values) => {
                    // onChange("sfzmmc", values[0]);
                    // 确保即使用户不选择也能获取默认值
                    const selectedValue = values[0] || pickerLists.sfzmmcList[0]?.value || formData.sfzmmc;
                    onChange("sfzmmc", selectedValue);
                }}
                onClose={() => setPickerStates((prev) => ({ ...prev, sfzmmcOpen: false }))}
            />

            <Picker
                title="选择学员性别"
                visible={pickerStates.xbOpen}
                options={pickerLists.xbList}
                defaultValue={[formData.xb === 1 ? "男" : "女"]}
                onConfirm={(_options, values) => {
                    // onChange("xb", values[0]);
                    // 确保即使用户不选择也能获取默认值
                    const selectedValue = values[0] || pickerLists.xbList[0]?.value || (formData.xb === 1 ? "男" : "女");
                    onChange("xb", selectedValue);
                }}
                onClose={() => setPickerStates((prev) => ({ ...prev, xbOpen: false }))}
            />

            <DatePicker
                title="选择学员出生日期"
                visible={pickerStates.csrqOpen}
                value={new Date(formData.csrq)}
                showChinese
                onClose={() => setPickerStates((prev) => ({ ...prev, csrqOpen: false }))}
                threeDimensional={true}
                onConfirm={(_options, values) => {
                    onChange("csrq", moment(`${values[0]}-${values[1]}-${values[2]}`).format("YYYY-MM-DD"));
                }}
                startDate={new Date(moment().subtract(100, "years").format("YYYY-01-01"))}
                endDate={new Date(moment().subtract(15, "years").format("YYYY-12-31"))}
            />

            <DatePicker
                title="选择证件发证日期"
                visible={pickerStates.fzrqOpen}
                value={new Date(formData.fzrq)}
                showChinese
                onClose={() => setPickerStates((prev) => ({ ...prev, fzrqOpen: false }))}
                threeDimensional={true}
                onConfirm={(_options, values) => {
                    onChange("fzrq", moment(`${values[0]}-${values[1]}-${values[2]}`).format("YYYY-MM-DD"));
                }}
                startDate={new Date(moment().subtract(100, "years").format("YYYY-01-01"))}
                endDate={new Date(moment().add(1, "years").format("YYYY-12-31"))}
            />

            <DatePicker
                title="选择证件有效日期"
                visible={pickerStates.yxqzOpen}
                value={new Date(formData.yxqz)}
                showChinese
                onClose={() => setPickerStates((prev) => ({ ...prev, yxqzOpen: false }))}
                threeDimensional={true}
                onConfirm={(_options, values) => {
                    onChange("yxqz", moment(`${values[0]}-${values[1]}-${values[2]}`).format("YYYY-MM-DD"));
                }}
                startDate={new Date(moment().subtract(1, "years").format("YYYY-01-01"))}
                endDate={new Date(moment().add(200, "years").format("YYYY-12-31"))}
            />

            <Picker
                title="选择报名班型"
                visible={pickerStates.jxClassOpen}
                options={pickerLists.jxClassList}
                defaultValue={[formData.jxClassId]}
                onConfirm={(_options, values) => {
                    // handleJxClassChange(values[0]);
                    // 确保即使用户不选择也能获取默认值
                    const selectedValue = values[0] || pickerLists.jxClassList[0]?.value || formData.jxClassId;
                    handleJxClassChange(selectedValue);
                }}
                onClose={() => setPickerStates((prev) => ({ ...prev, jxClassOpen: false }))}
            />

            <Picker
                title="选择报名车型"
                visible={pickerStates.carTypeOpen}
                options={pickerLists.carTypeList}
                defaultValue={[formData.carType]}
                onConfirm={(_options, values) => {
                    // onChange("carType", values[0]);
                    // 确保即使用户不选择也能获取默认值
                    const selectedValue = values[0] || pickerLists.carTypeList[0]?.value || formData.carType;
                    onChange("carType", selectedValue);
                }}
                onClose={() => setPickerStates((prev) => ({ ...prev, carTypeOpen: false }))}
            />
        </>
    );
};
