import { Button } from "@nutui/nutui-react-taro";

export const FormFooter = ({ isLoading, loadingText, onSubmit }) => {
    return (
        <view className="footer" style={{ paddingLeft: "20rpx", paddingRight: "20rpx", paddingTop: "20rpx" }}>
            <Button block type="primary" disabled={isLoading} loading={isLoading} onClick={onSubmit}>
                {isLoading ? loadingText : "保存"}
            </Button>
        </view>
    );
};
