import IconFont from "@components/iconfont";
import { Input } from "@tarojs/components";

export const BasicInfoSection = ({ formData, pickerStates, setPickerStates, onChange }) => {
    return (
        <view className="flex-col mt-32">
            <text className="self-start font_3 text_31">基本信息</text>
            <view className="flex-col self-stretch section_3">
                <view className="flex-row justify-between group_4">
                    <text className="font_3">学员姓名</text>
                    <Input className="font text_6" placeholder="请输入学员的姓名" maxlength={20} value={formData.xm} onInput={(e) => onChange("xm", e.detail.value)} />
                </view>

                <view className="flex-row justify-between group_5" onClick={() => setPickerStates((prev) => ({ ...prev, csrqOpen: true }))}>
                    <text className="font_3">出生年月</text>
                    <view className="flex-row items-center">
                        <text className="font text_8">{formData.csrq || "请选择出生日期"}</text>
                        <IconFont name="Arrow-Right2" size={30} style={{ marginTop: "0rpx", marginLeft: "10rpx" }} />
                    </view>
                </view>

                <view className="flex-row justify-between group_5" onClick={() => setPickerStates((prev) => ({ ...prev, xbOpen: true }))}>
                    <text className="font_3 text_7">学员性别</text>
                    <view className="flex-row items-center">
                        <text className="font text_8">{formData.xb === 1 ? "男" : "女"}</text>
                        <IconFont name="Arrow-Right2" size={30} style={{ marginTop: "0rpx", marginLeft: "10rpx" }} />
                    </view>
                </view>

                <view className="flex-row justify-between group_5">
                    <text className="font_3">手机号码</text>
                    <Input
                        className="font text_6"
                        placeholder="请输入学员手机号码"
                        maxlength={11}
                        type="number"
                        value={formData.yddh}
                        onInput={(e) => onChange("yddh", e.detail.value)}
                    />
                </view>

                <view className="flex-row justify-between group_5">
                    <text className="font_3">证件地址</text>
                    <Input
                        className="font text_6"
                        placeholder="请输入身份证件登记地址"
                        maxlength={100}
                        value={formData.djzsxxdz}
                        onInput={(e) => onChange("djzsxxdz", e.detail.value)}
                    />
                </view>

                <view className="flex-row justify-between group_33">
                    <text className="font_3">居住地址</text>
                    <Input
                        className="font text_6"
                        placeholder="请输入本市当前居住地址"
                        maxlength={100}
                        value={formData.lxzsxxdz}
                        onInput={(e) => onChange("lxzsxxdz", e.detail.value)}
                    />
                </view>
            </view>
        </view>
    );
};
