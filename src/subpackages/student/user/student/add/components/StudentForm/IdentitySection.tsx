import IconFont from "@components/iconfont";
import { Input } from "@tarojs/components";
import moment from "moment";

export const IdentitySection = ({ formData, pickerLists, pickerStates, setPickerStates, onChange }) => {
    return (
        <view className="flex-col mt-32">
            <text className="self-start font_3 text_31">证件信息</text>
            <view className="flex-col self-stretch section_3">
                <view className="flex-row justify-between group_4" onClick={() => setPickerStates((prev) => ({ ...prev, sfzmmcOpen: true }))}>
                    <text className="font_3">证件类型</text>
                    <view className="flex-row items-center">
                        <text className="font text_8">{pickerLists.sfzmmcList.find((m) => m.value === formData.sfzmmc)?.text || "请选择证件类型"}</text>
                        <IconFont name="Arrow-Right2" size={30} style={{ marginTop: "0rpx", marginLeft: "10rpx" }} />
                    </view>
                </view>

                <view className="flex-row justify-between group_5">
                    <text className="font_3">证件号码</text>
                    <Input
                        className="font text_6"
                        placeholder="请输入学员的证件号码"
                        maxlength={20}
                        type={formData.sfzmmc === "A" || formData.sfzmmc === "Q" ? "idcard" : "text"}
                        value={formData.sfzmhm}
                        onInput={(e) => onChange("sfzmhm", e.detail.value)}
                    />
                </view>

                <view className="flex-row justify-between group_5" onClick={() => setPickerStates((prev) => ({ ...prev, fzrqOpen: true }))}>
                    <text className="font_3">发证日期</text>
                    <view className="flex-row items-center">
                        <text className="font text_8">{formData.fzrq || "证件有效期开始"}</text>
                        <IconFont name="Arrow-Right2" size={30} style={{ marginTop: "0rpx", marginLeft: "10rpx" }} />
                    </view>
                </view>

                <view className="flex-row justify-between group_33" onClick={() => setPickerStates((prev) => ({ ...prev, yxqzOpen: true }))}>
                    <text className="font_3">有效日期</text>
                    <view className="flex-row items-center">
                        <text className="font text_8">{!formData.yxqz ? "证件有效期截止" : moment(formData.yxqz).isSame(moment("2099-01-01")) ? "长期" : formData.yxqz}</text>
                        <IconFont name="Arrow-Right2" size={30} style={{ marginTop: "0rpx", marginLeft: "10rpx" }} />
                    </view>
                </view>
            </view>
        </view>
    );
};
