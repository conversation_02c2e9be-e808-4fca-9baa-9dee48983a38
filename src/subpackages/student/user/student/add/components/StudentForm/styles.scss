// components/StudentForm/styles.scss

.student-form {
    // Training Section styles
    .training-section {
        .section_3 {
            margin-top: 24rpx;
            padding: 0 24rpx;
            background-color: #ffffff;
            border-radius: 10rpx;
        }

        .group_19 {
            padding: 24rpx 8rpx 20rpx;
            border-bottom: solid 1rpx #eeeeee;
        }

        .text_5 {
            color: #1a66ff;
            line-height: 27.8rpx;
        }
    }

    // Identity Section styles
    .identity-section {
        margin-top: 32rpx;

        .group_4,
        .group_5 {
            padding: 24rpx 3.48rpx 20rpx 7.58rpx;
            border-bottom: solid 2rpx #eeeeee;
        }

        .group_33 {
            padding: 20rpx 3.48rpx 20rpx 7.58rpx;
        }
    }

    // Basic Info Section styles
    .basic-info-section {
        margin-top: 32rpx;

        .group_4,
        .group_5 {
            padding: 24rpx 3.48rpx 20rpx 7.58rpx;
            border-bottom: solid 2rpx #eeeeee;
        }

        .text_6 {
            text-align: right;
            width: 98%;
            font-size: 26rpx;
        }
    }

    // Common styles
    .group_2 {
        padding: 20rpx 30rpx;
    }

    .font {
        font-size: 28rpx;
        font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
        line-height: 25.96rpx;
        color: #999999;
    }

    .font_3 {
        font-size: 26rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        font-weight: 700;
        color: #333333;
    }

    .font_6 {
        font-size: 26rpx;
        font-family: PingFang SC;
        line-height: 10.76rpx;
        font-weight: 700;
        color: #ff0000;
    }

    .text_31 {
        margin-left: 28rpx;
        line-height: 27.7rpx;
    }

    // Form controls
    .input-control {
        flex: 1;
        text-align: right;
        font-size: 26rpx;
        margin-left: 20rpx;
    }

    // Picker styles
    .nut-picker-control {
        height: 80rpx;
    }

    .nut-popup-bottom.nut-popup-round {
        border-radius: 24rpx 24rpx 0 0;
    }

    .nut-picker-roller-item-title {
        font-size: 30rpx;
    }

    .nut-picker-view-panel {
        height: 40vh;
    }

    .nut-popup {
        font-family: Yuanti SC, STYuanti-SC-Regular;
    }

    .nut-picker {
        height: 50vh;
    }

    // Footer styles
    .footer {
        padding: 20rpx 32rpx;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #ffffff;
        box-shadow: 0rpx -4rpx 10rpx rgba(0, 0, 0, 0.05);

        .nut-button {
            height: 88rpx;
            border-radius: 44rpx;
        }
    }

    // Remark text area
    .text-wrapper_2 {
        margin-top: 32rpx;

        .nut-textarea {
            background-color: #ffffff;
            border-radius: 10rpx;
            padding: 20rpx;
            min-height: 180rpx;
        }
    }

    // Safe area bottom margin
    .safe__area {
        height: constant(safe-area-inset-bottom);
        height: env(safe-area-inset-bottom);
    }
}
