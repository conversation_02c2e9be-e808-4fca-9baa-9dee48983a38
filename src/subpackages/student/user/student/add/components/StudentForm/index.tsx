import { TextArea } from "@nutui/nutui-react-taro";
import { IdentitySection } from "./IdentitySection";
import "./styles.scss";
import { TrainingSection } from "./TrainingSection";
import { BasicInfoSection } from "./BasicInfoSection";
import { FormFooter } from "./FormFooter";
import { FormPickers } from "./FormPickers";

export const StudentForm = ({ formData, onChange, onSubmit, pickerLists, setPickerLists, pickerStates, setPickerStates, isLoading, loadingText, messageRef }) => {
    return (
        <view className="step-2">
            <view className="flex-col group_2">
                <TrainingSection
                    formData={formData}
                    pickerLists={pickerLists}
                    pickerStates={pickerStates}
                    setPickerStates={setPickerStates}
                    onChange={onChange}
                    messageRef={messageRef}
                />

                <IdentitySection formData={formData} pickerLists={pickerLists} pickerStates={pickerStates} setPickerStates={setPickerStates} onChange={onChange} />

                <BasicInfoSection formData={formData} pickerStates={pickerStates} setPickerStates={setPickerStates} onChange={onChange} />

                <view className="mt-8 flex-col justify-start items-start text-wrapper_2">
                    <TextArea
                        className="font"
                        placeholder="请输入备注，0-200字"
                        value={formData.remark}
                        onInput={(e) => onChange("remark", e.detail.value)}
                        maxLength={200}
                        style={{
                            height: "90px",
                            margin: "0",
                        }}
                    />
                </view>
            </view>

            <view className="safe__area" style={{ marginTop: "100rpx" }} />

            <FormFooter isLoading={isLoading} loadingText={loadingText} onSubmit={onSubmit} />

            <FormPickers
                formData={formData}
                pickerStates={pickerStates}
                setPickerStates={setPickerStates}
                pickerLists={pickerLists}
                setPickerLists={setPickerLists}
                onChange={onChange}
            />
        </view>
    );
};
