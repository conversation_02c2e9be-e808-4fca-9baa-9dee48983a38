// components/IdCardUpload/components/PhotoPopup.scss
.photo-popup {
    padding: 48rpx 32rpx;
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;

    .popup-content {
        padding: 0 32rpx;
    }

    .option-item {
        display: flex;
        align-items: center;
        padding: 32rpx 0;
        margin-bottom: 36rpx;

        &:active {
            opacity: 0.7;
        }
    }

    .circle-icon {
        width: 96rpx;
        height: 96rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 40rpx;
        background: #f5f7fe;
    }

    .option-icon {
        width: 48rpx;
        height: 40rpx;
    }

    .text-container {
        display: flex;
        flex-direction: column;
        gap: 12rpx;
    }

    .option-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
    }

    .option-desc {
        font-size: 28rpx;
        color: #999;
    }

    .cancel-button {
        margin-top: 48rpx;
        height: 96rpx;
        border-radius: 48rpx;
        font-size: 32rpx;
        color: #333;
        background: #f5f5f5;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;

        &:active {
            opacity: 0.8;
        }
    }
}

// 覆盖 NutUI 的默认样式
:global {
    .nut-popup {
        border-radius: 24rpx 24rpx 0 0;

        .nut-popup-bottom {
            padding-bottom: constant(safe-area-inset-bottom);
            padding-bottom: env(safe-area-inset-bottom);
        }
    }
}
