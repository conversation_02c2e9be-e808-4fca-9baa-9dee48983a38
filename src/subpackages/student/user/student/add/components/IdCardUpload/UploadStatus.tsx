import { View, Image, Text } from "@tarojs/components";
import "./UploadStatus.scss";

interface UploadStatusProps {
  status: "success" | "error";
}

export const UploadStatus: React.FC<UploadStatusProps> = ({ status }) => {
  const isSuccess = status === "success";
  
  return (
    <View className={`flex-col items-center upload-status ${isSuccess ? "success" : "error"}`}>
      <Image
        className="status-icon"
        src={`https://cdn.51panda.com/wx/idcard2/${
          isSuccess ? "11925506e02093b15ff12c2ffed8c146.png" : "330a4d8d8d876be7583d1e2c3e08726a.png"
        }`}
      />
      <Text className={`status-text ${isSuccess ? "success-text" : "error-text"}`}>
        {isSuccess ? "上传成功" : "无法识别请重新上传"}
      </Text>
    </View>
  );
};
