import { View, Text } from "@tarojs/components";
import { <PERSON><PERSON>, Popup } from "@nutui/nutui-react-taro";
import { useState, useRef } from "react";
import { IdCardSide } from "./IdCardSide";
import { PhotoPopup } from "./PhotoPopup";
import { IdCardGuidelines } from "./IdCardGuidelines";
import upload from "@service/upload";
import "./index.scss";
import moment from "moment";
import Layout from "@/components/Layout";

// Add this interface before the component props
interface IdCardInfo {
    // Front side properties
    xm?: string; // Name
    sfzmhm?: string; // ID card number
    xb?: number; // Gender (1 for male, 2 for female)
    mz?: string; // Nationality/Ethnicity
    csrq?: string; // Date of birth
    djzsxxdz?: string; // Address

    // Back side properties
    qfjg?: string; // Issuing authority
    yxqx?: string; // Validity period
    fzjg?: string; // Issuing organization
    fzrq?: string; // Issue date
    yxqz?: string; // Expiration date

    // Image URLs
    cardImages?: {
        front?: string;
        back?: string;
    };
}

interface IdCardUploadProps {
    sfzzm: string;
    sfzfm: string;
    isLoading: boolean;
    loadingText: string;
    openTakePhoto: number;
    setOpenTakePhoto: (value: number) => void;
    onTakePhoto: (imageId: number) => void;
    onChooseImage: (imageId: number, sourceType: string[]) => Promise<{ id: string }>;
    onManualEntry: () => void;
    // onUploadAndIdentify: () => void;
    onIdCardInfo: (info: any) => void;
    message?: any;
    onStepChange?: (step: number) => void;
    setIsLoading: (loading: boolean) => void;
    setLoadingText: (text: string) => void;
    frontStatus: "uploading" | "success" | "error" | undefined;
    setFrontStatus: (status: "uploading" | "success" | "error" | undefined) => void;
    backStatus: "uploading" | "success" | "error" | undefined;
    setBackStatus: (status: "uploading" | "success" | "error" | undefined) => void;
}

export const IdCardUpload: React.FC<IdCardUploadProps> = ({
    sfzzm,
    sfzfm,
    isLoading,
    loadingText,
    openTakePhoto,
    setOpenTakePhoto,
    onChooseImage,
    onManualEntry,
    onIdCardInfo,
    message,
    onStepChange,
    setIsLoading,
    setLoadingText,
    frontStatus,
    setFrontStatus,
    backStatus,
    setBackStatus,
}) => {
    console.log("IdCardUpload组件渲染，props:", { sfzzm, sfzfm, isLoading, openTakePhoto });

    const [cardImages, setCardImages] = useState<{ front?: string; back?: string }>({});

    const handleChooseImage = async (imageId: number, sourceType: string[]) => {
        try {
            let front = frontStatus;
            let back = backStatus;

            const result = await onChooseImage(imageId, sourceType);
            if (result) {
                setIsLoading(true);
                setLoadingText("正在识别中...");

                const response = await upload.post<{
                    success: boolean;
                    data: any;
                    message?: string;
                }>("/Jx/Image/IdCard/getIdCardInfo", {}, result.id, "file");

                setIsLoading(false);

                if (response.success) {
                    if (imageId === 1 && (!response.data?.xm || response.data.xm.trim() === "")) {
                        setFrontStatus("error");
                        message.error("请确保上传的是身份证正面照片", "识别失败");
                        return;
                    }
                    if (imageId === 2 && (!response.data?.fzjg || response.data.fzjg.trim() === "")) {
                        setBackStatus("error");
                        message.error("请确保上传的是身份证反面照片", "识别失败");
                        return;
                    }

                    const updatedCardImages = {
                        ...cardImages,
                        [imageId === 1 ? "front" : "back"]: response.data.card_image,
                    };
                    setCardImages(updatedCardImages);

                    let idCardInfo: IdCardInfo = {};
                    if (imageId === 1) {
                        // 仅在API调用成功且数据有效后设置状态为success
                        setFrontStatus("success");
                        front = "success";

                        idCardInfo = {
                            csrq: moment(response.data.csrq).format("YYYY-MM-DD"),
                            djzsxxdz: response.data.djzsxxdz,
                            mz: response.data.mz,
                            sfzmhm: response.data.sfzmhm,
                            xb: response.data.xb == "男" ? 1 : 2,
                            xm: response.data.xm,
                        };
                    } else {
                        // 仅在API调用成功且数据有效后设置状态为success
                        setBackStatus("success");

                        back = "success";

                        idCardInfo = {
                            qfjg: response.data.qfjg,
                            yxqx: response.data.yxqx,
                            fzjg: response.data.fzjg,
                            fzrq: moment(response.data.fzrq).format("YYYY-MM-DD"),
                            yxqz: response.data.yxqz === "长期" ? "2099-01-01" : moment(response.data.yxqz).format("YYYY-MM-DD"),
                        };
                    }

                    onIdCardInfo({
                        ...idCardInfo,
                        cardImages: updatedCardImages,
                    });

                    if (front === "success" && back === "success") {
                        console.log("两面都成功，准备切换到步骤2");
                        onStepChange?.(2);
                    }
                } else {
                    console.log("API返回失败", response.message);
                    imageId === 1 ? setFrontStatus("error") : setBackStatus("error");
                    message.error("识别失败", response.message);
                }
            }
        } catch (error) {
            console.error("身份证识别失败:", error);
            imageId === 1 ? setFrontStatus("error") : setBackStatus("error");
            message.error("请重新上传", "识别失败");
            setIsLoading(false);
        }
        setOpenTakePhoto(0);
    };
    return (
        <Layout>
            <View className="step-1">
                <View className="flex-col page">
                    <View className="flex-col">
                        <View className="flex-col section">
                            <View className="mt-12 flex-col group_3">
                                <Text className="self-start font_2 text_3">请拍摄并上传学员的身份证照片</Text>
                                <View className="mt-20 flex-row equal-division">
                                    <IdCardSide type="front" image={sfzzm} onClick={() => setOpenTakePhoto(1)} uploadStatus={frontStatus} />
                                    <IdCardSide type="back" image={sfzfm} onClick={() => setOpenTakePhoto(2)} uploadStatus={backStatus} />
                                </View>
                            </View>
                        </View>

                        <IdCardGuidelines />

                        <View className="footer">
                            <Button
                                disabled={isLoading}
                                block
                                type="primary"
                                onClick={onManualEntry}
                                color={"#ebeef4"}
                                style={{
                                    color: isLoading ? "#999" : "#000",
                                    marginBottom: "10px",
                                }}
                            >
                                {frontStatus === "success" && backStatus === "success" ? "下一步" : "自助手工填写信息"}
                            </Button>
                        </View>
                    </View>
                </View>

                <PhotoPopup
                    visible={openTakePhoto > 0}
                    onClose={() => setOpenTakePhoto(0)}
                    onTakePhoto={() => handleChooseImage(openTakePhoto, ["camera"])}
                    onChooseFromAlbum={() => handleChooseImage(openTakePhoto, ["album"])}
                />
            </View>
        </Layout>
    );
};
