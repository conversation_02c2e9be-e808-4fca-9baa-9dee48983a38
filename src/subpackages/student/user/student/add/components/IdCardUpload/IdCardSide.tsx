import { Image, View, Text } from "@tarojs/components";
import React, { useEffect } from "react";
import "./IdCardSide.scss";
import { Loading } from "@nutui/nutui-react-taro";
import Layout from "@/components/Layout";

interface Props {
    type: "front" | "back";
    image?: string;
    onClick?: () => void;
    uploadStatus?: "uploading" | "success" | "error" | undefined;
    isLoading?: boolean;
    loadingText?: string;
}

export const IdCardSide: React.FC<Props> = ({ type, image, onClick, uploadStatus, isLoading = false, loadingText = "正在处理..." }) => {
    // 监控 uploadStatus 的变化
    useEffect(() => {
        console.log(`IdCardSide ${type}: uploadStatus 变化为`, uploadStatus);
    }, [uploadStatus, type]);

    return (
        <Layout>
            <View className="card-side" onClick={onClick}>
                <View className={`card-bg ${type === "front" ? "card-bg-front" : "card-bg-back"}`}>
                    {type === "back" && (
                        <>
                            <Image className="emblem-icon" src="https://cdn.51panda.com/wx/idcard2/17365977402253340881.png" />
                            <View className="text-group">
                                <Image className="text-image" src="https://cdn.51panda.com/wx/idcard2//83478398e1e3acfb3388bde04e22284b.png" />
                                <Image className="text-image mt-16" src="https://cdn.51panda.com/wx/idcard2/17365977402254501173.png" />
                            </View>
                        </>
                    )}
                </View>
                <Text className="card-title">{type === "front" ? "头像面" : "国徽面"}</Text>
                <Text className="card-subtitle">{`上传身份证${type === "front" ? "头像" : "国徽"}面`}</Text>
                <View className="card-content">
                    {isLoading && (
                        <View className="upload-status loading flex-col items-center">
                            <Loading type="spinner" color="#2972f6" />
                            <Text className="loading-text">{loadingText}</Text>
                        </View>
                    )}
                    {!isLoading && uploadStatus === "success" && (
                        <View className="upload-status success flex-col items-center">
                            <Image className="success-icon" src="https://cdn.51panda.com/wx/idcard2/11925506e02093b15ff12c2ffed8c146.png" />
                            <Text className="success-text">上传成功</Text>
                        </View>
                    )}
                    {!isLoading && uploadStatus === "error" && (
                        <View className="upload-status error">
                            <Image className="camera-icon" src="https://cdn.51panda.com/wx/idcard2/330a4d8d8d876be7583d1e2c3e08726a.png" />
                            <Text>无法识别请重新上传</Text>
                        </View>
                    )}
                    {!isLoading && uploadStatus === undefined && (
                        <View className="upload-placeholder">
                            <Image className="camera-icon" src="https://cdn.51panda.com/wx/idcard2/camera.png" />
                            <Text className="upload-text">{`拍摄身份证${type === "front" ? "正" : "反"}面`}</Text>
                        </View>
                    )}
                </View>
            </View>
        </Layout>
    );
};
