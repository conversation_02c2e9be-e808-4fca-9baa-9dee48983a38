.step-1 {
    .page {
        min-height: 100vh;
        background: #f7f8fa;
        padding-bottom: 180rpx;
    }

    .section {
        background: #fff;
        padding: 0;

        .group_3 {
            padding: 0 32rpx 32rpx;

            .text_3 {
                font-size: 32rpx;
                color: #333;
                font-weight: 500;
            }

            .equal-division {
                display: flex;
                flex-direction: column;
                gap: 24rpx;
                margin-top: 24rpx;

                .card-side {
                    width: 100%;
                    background: #fff;
                    border-radius: 16rpx;
                    padding: 40rpx 44rpx;
                    position: relative;
                    overflow: hidden;
                    height: 308rpx;

                    .card-bg {
                        position: absolute;
                        right: 44rpx;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 376rpx;
                        height: 224rpx;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 48rpx 38rpx;

                        &.card-bg-front {
                            background-image: url("https://cdn.51panda.com/wx/idcard2/17365977402243752066.png");
                            background-size: 100% 100%;
                            background-repeat: no-repeat;
                            opacity: 0.1;
                        }

                        &.card-bg-back {
                            background-image: url("https://cdn.51panda.com/wx/idcard2/17365977402250933967.png");
                            background-size: 100% 100%;
                            background-repeat: no-repeat;
                            opacity: 1;
                            display: flex;
                            flex-direction: row;
                            justify-content: center;
                            align-items: center;

                            .emblem-icon {
                                width: 112rpx;
                                height: 122rpx;
                            }

                            .text-group {
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                                margin-left: 38rpx;

                                .text-image {
                                    width: 136rpx;
                                    height: 48rpx;

                                    &.mt-16 {
                                        margin-top: 32rpx;
                                        width: 148rpx;
                                    }
                                }
                            }
                        }
                    }

                    .card-title {
                        position: absolute;
                        left: 45.72rpx;
                        top: 53.42rpx;
                        font-size: 36rpx;
                        font-family: SourceHanSansCN;
                        line-height: 33.48rpx;
                        color: #212121;
                    }

                    .card-subtitle {
                        position: absolute;
                        left: 45.22rpx;
                        top: 103.6rpx;
                        font-size: 24rpx;
                        font-family: SourceHanSansCN;
                        line-height: 22.34rpx;
                        color: #a8a8a8;
                    }

                    .card-content {
                        position: absolute;
                        left: 0;
                        right: 0;
                        top: 0;
                        bottom: 0;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        .preview-image {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }

                        .upload-placeholder {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            padding: 80rpx 0 88rpx;
                            background-color: #e3ecfc99;
                            border-radius: 16rpx;
                            border: 2rpx dotted #2972f6;
                            width: 100%;
                            height: 100%;

                            .camera-icon {
                                width: 96rpx;
                                height: 96rpx;
                            }

                            .upload-text {
                                margin-top: 24rpx;
                                font-size: 36rpx;
                                font-family: HarmonyOSSansSC;
                                line-height: 33.48rpx;
                                color: #2972f6;
                            }
                        }
                    }
                }
            }
        }
    }

    .footer {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 24rpx 32rpx;
        background: #fff;
        box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    }
}

.tips-section {
    margin-top: 48rpx;
    padding: 0 32rpx;

    .tips-header {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 32rpx;

        &::before,
        &::after {
            content: "";
            height: 1px;
            flex: 1;
            background: rgba(0, 0, 0, 0.06);
        }

        .tips-title {
            font-size: 28rpx;
            color: #999;
            margin: 0 24rpx;
        }
    }

    .tips-content {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24rpx;

        .tip-item {
            background: #fff;
            border-radius: 16rpx;
            padding: 24rpx;
            display: flex;
            flex-direction: column;
            align-items: center;

            .tip-image {
                width: 200rpx;
                height: 126rpx;
                margin-bottom: 16rpx;
            }

            .tip-status {
                display: flex;
                align-items: center;

                .status-icon {
                    width: 32rpx;
                    height: 32rpx;
                    margin-right: 8rpx;
                }

                .status-text {
                    font-size: 26rpx;
                    color: #999;

                    &.correct {
                        color: #07c160;
                    }

                    &.wrong {
                        color: #ff4d4f;
                    }
                }
            }
        }
    }
}

.guidelines-container {
    padding: 32rpx;

    .guidelines-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333333;
        line-height: 44rpx;
        margin-bottom: 20rpx;
    }

    .guidelines-requirements {
        margin-top: 20rpx;
        margin-bottom: 20rpx;

        .requirement-text {
            font-size: 24rpx;
            color: #999999;
            line-height: 34rpx;
            display: block;
            margin-bottom: 8rpx;

            .highlight {
                color: #ff4d4f;
            }
        }
    }

    .guidelines-content {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 12rpx;

        .example-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .image-wrapper {
                width: 120rpx;
                height: 120rpx;
                background: #f5f6fa;
                border-radius: 12rpx;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 8rpx;

                .id-image {
                    width: 120rpx;
                    height: 120rpx;
                    object-fit: contain;
                }
            }

            .label-text {
                font-size: 24rpx;
                color: #999999;
                text-align: center;
            }
        }
    }
}

.global-loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;

    .global-loading-text {
        margin-top: 20px;
        color: #ffffff;
        font-size: 16px;
    }
}

.debug-panel {
    margin: 10px 0;
    padding: 10px;
    border: 1px dashed #999;
    border-radius: 8px;
    background-color: #f5f5f5;
}

.debug-title {
    font-weight: bold;
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    display: block;
}

.debug-text {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
    display: block;
}
