import { View, Text, Image } from "@tarojs/components";
import React from "react";
import "./index.scss";

interface ExampleProps {
    image: string;
    text: string;
    isSuccess?: boolean;
}

const Example: React.FC<ExampleProps> = ({ image, text, isSuccess }) => (
    <View className="example-item">
        <View className="image-wrapper">
            <Image className="id-image" src={image} mode="aspectFit" />
        </View>
        <Text className="label-text">{text}</Text>
    </View>
);

export const IdCardGuidelines: React.FC = () => {
    const examples: ExampleProps[] = [
        {
            image: "https://cdn.51panda.com/wx/idcard2/54ea0d42191980f2379c6c74082e831c06d0b32d.png",
            text: "标准",
            isSuccess: true,
        },
        {
            image: "https://cdn.51panda.com/wx/idcard2/c8260355a708d8ef6e75763e27b7247ec6e3cdc3.png",
            text: "边框缺失",
            isSuccess: false,
        },
        {
            image: "https://cdn.51panda.com/wx/idcard2/3c9172ff07c80342c48a8ccedd21d956480c0bb7.png",
            text: "照片模糊",
            isSuccess: false,
        },
        {
            image: "https://cdn.51panda.com/wx/idcard2/1f16772bab52e099729ff1938ebafc06376d8f56.png",
            text: "闪光强烈",
            isSuccess: false,
        },
    ];

    return (
        <View className="guidelines-container">
            <Text className="guidelines-title">拍摄身份证要求:</Text>
            <View className="guidelines-requirements">
                <Text className="requirement-text">持有的本人有效二代身份证;</Text>
                <Text className="requirement-text">
                    拍摄时确保身份证
                    <Text className="highlight">边框完整</Text>、<Text className="highlight">字体清晰</Text>、<Text className="highlight">亮度均匀</Text>;
                </Text>
            </View>
            <View className="guidelines-content">
                {examples.map((example, index) => (
                    <Example key={index} {...example} />
                ))}
            </View>
        </View>
    );
};
