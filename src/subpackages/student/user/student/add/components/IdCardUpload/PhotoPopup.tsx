// components/IdCardUpload/components/PhotoPopup.jsx

import { Image } from "@tarojs/components";
import { Button, Popup } from "@nutui/nutui-react-taro";
import "./PhotoPopup.scss";

export const PhotoPopup = ({ visible, onClose, onTakePhoto, onChooseFromAlbum }) => {
    return (
        <Popup
            visible={visible}
            position="bottom"
            onClose={onClose}
            lockScroll
            duration={0}
            style={{
                borderTopLeftRadius: "12px",
                borderTopRightRadius: "12px",
            }}
        >
            <view className="photo-popup">
                <view className="popup-content">
                    <view className="option-item" onClick={onChooseFromAlbum}>
                        <view className="circle-icon album-icon">
                            <Image className="option-icon" src="https://cdn.51panda.com/wx/photo/87b2f9bf65bcda607ce47bdb409b9491.png" />
                        </view>
                        <view className="text-container">
                            <text className="option-title">相册</text>
                            <text className="option-desc">直接从相册上传照片</text>
                        </view>
                    </view>

                    <view className="option-item" onClick={onTakePhoto}>
                        <view className="circle-icon camera-icon">
                            <Image className="option-icon" src="https://cdn.51panda.com/wx/photo/a4fab962dc018dcec846d1befb430aff.png" />
                        </view>
                        <view className="text-container">
                            <text className="option-title">拍照</text>
                            <text className="option-desc">直接用手机相机拍照</text>
                        </view>
                    </view>
                </view>

                <Button className="cancel-button" block onClick={onClose}>
                    取消
                </Button>
            </view>
        </Popup>
    );
};
