// components/IdCardUpload/styles.scss

.id-card-upload {
    // 间距样式
    .ml-9 {
        margin-left: 18rpx;
    }

    .ml-19 {
        margin-left: 38rpx;
    }

    .mt-9 {
        margin-top: 18rpx;
    }

    .mt-11 {
        margin-top: 22rpx;
    }

    .mt-12 {
        margin-top: 24rpx;
    }

    .mt-14 {
        margin-top: 28rpx;
    }

    .mt-20 {
        margin-top: 40rpx;
    }

    // 页面布局
    .page {
        background-color: #f8f8f8;
        width: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        height: 100%;
    }

    // 文字样式
    .font {
        font-size: 28rpx;
        font-family: <PERSON><PERSON> SC, STYuanti-SC-Regular;
        line-height: 25.88rpx;
        color: #ababab;
    }

    .font_2 {
        font-size: 36rpx;
        font-family: <PERSON>ti SC, STYuanti-SC-Regular;
        line-height: 33.76rpx;
        color: #404040;
    }

    .font_3 {
        font-size: 28rpx;
        font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
        line-height: 25.88rpx;
        color: #ffffff;
    }

    // 区域样式
    .section {
        padding: 32rpx 0;
        background-color: #ffffff;
    }

    .section_2 {
        flex: 1 1 324rpx;
    }

    .section_3 {
        padding: 28rpx 20rpx;
        background-color: #ffffff;
        border-radius: 12rpx;
        box-shadow: 0rpx 0rpx 16rpx #4280f21f;
    }

    // 身份证上传区域
    // .equal-division {
    //     align-self: stretch;
    //     display: flex;
    //     justify-content: space-between;
    //     padding: 0 60rpx;
    // }
    .equal-division {
        align-self: stretch;
    }

    .equal-division-item {
        padding-top: 32rpx;
        overflow: hidden;
        border-radius: 16rpx;
        background-color: #f4f8fe;
        height: 284rpx;
    }

    // .equal-division-item {
    //     padding-top: 32rpx;
    //     overflow: hidden;
    //     border-radius: 16rpx;
    //     background-color: #f4f8fe;
    //     height: 284rpx;
    //     flex: 1;

    //     // 除了第一个 item 外都添加 margin-left
    //     &:nth-child(2) {
    //         margin-left: 80rpx;
    //     }
    // }
    .equal-division_2 {
        margin-top: 44rpx;
    }

    .equal-division-item_2 {
        padding: 8rpx;
        border-radius: 20px;
    }

    .group_4 {
        padding: 20rpx 0;
        width: 242rpx;
        height: 160rpx;
        border: solid 2rpx #247fef;
    }

    // 身份证示例区域
    .group_3 {
        padding: 0 32rpx;
    }

    .group_6 {
        width: 16rpx;
    }

    .group_7 {
        margin-top: 28rpx;
        line-height: 28.68rpx;
    }

    // 图片样式
    .image_5 {
        width: 40rpx;
        height: 44rpx;
    }

    .image_6 {
        width: 58rpx;
        height: 64rpx;
    }

    .image_7 {
        width: 78rpx;
        height: 28rpx;
    }

    .image_8 {
        width: 50rpx;
        height: 48rpx;
    }

    // 示例图片装饰
    .section_5 {
        background-color: #8eb3f7;
        border-radius: 4rpx;
        height: 8rpx;
    }

    .section_6 {
        background-color: #c7dbff;
        border-radius: 4rpx;
        height: 8rpx;
    }

    // 底部按钮区域
    .footer {
        padding: 20rpx 32rpx;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #ffffff;
        box-shadow: 0rpx -4rpx 10rpx rgba(0, 0, 0, 0.05);

        .nut-button {
            height: 88rpx;
            border-radius: 44rpx;
            margin-bottom: 20rpx;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }

    // 拍照弹窗样式
    .photo-popup {
        .popup {
            padding: 40rpx 30rpx;
            background-color: #ffffff;
            border-radius: 36rpx 36rpx 0 0;
        }

        .option-item {
            display: flex;
            align-items: center;
            padding: 30rpx 0;

            .icon {
                width: 68rpx;
                height: 54rpx;
                margin-right: 40rpx;
            }

            .content {
                flex: 1;

                .title {
                    font-size: 30rpx;
                    color: #000000;
                    line-height: 1.2;
                    margin-bottom: 10rpx;
                }

                .desc {
                    font-size: 26rpx;
                    color: #999999;
                    line-height: 1.2;
                }
            }
        }

        .cancel-button {
            margin-top: 40rpx;
        }
    }

    // 拍摄要求说明区域
    .requirements {
        .title {
            font-size: 36rpx;
            color: #404040;
            margin-bottom: 20rpx;
        }

        .subtitle {
            font-size: 28rpx;
            color: #666666;
            margin-bottom: 16rpx;
        }

        .tip {
            font-size: 28rpx;
            color: #ff5757;
        }
    }

    // 示例图片区域
    .examples {
        display: flex;
        justify-content: space-between;
        padding: 0 22rpx;
        margin-top: 44rpx;

        .example-item {
            flex: 1;
            padding: 8rpx;
            text-align: center;

            .image-wrapper {
                padding: 16rpx 0;
                background-color: #f2f7fe;
                margin-bottom: 16rpx;
            }

            .label {
                font-size: 28rpx;
                color: #666666;
            }
        }
    }
}
