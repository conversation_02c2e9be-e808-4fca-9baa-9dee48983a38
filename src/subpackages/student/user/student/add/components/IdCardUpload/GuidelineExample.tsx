import { Image } from "@tarojs/components";

export const GuidelineExample = ({ type, image, text, isBlurred }) => {
    return (
        <view className="flex-col group_9 equal-division-item_2">
            <view className="flex-col justify-start self-stretch relative group_10">
                <view className="flex-col justify-start items-center image-wrapper">
                    <Image className={`image_${type === "standard" ? "10" : type === "glare" ? "12" : "11"} ${isBlurred ? "blur" : ""}`} src={image} />
                </view>
                <Image className="image_13 pos_3" src="https://cdn.51panda.com/WxAppImage/pandaWxApp/user/add/17218725204094640155.png" />
            </view>
            <text className="self-center font mt-9">{text}</text>
        </view>
    );
};
