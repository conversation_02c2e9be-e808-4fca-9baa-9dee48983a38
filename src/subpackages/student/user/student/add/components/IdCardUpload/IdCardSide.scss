.id-card-side {
    position: relative;
    width: 100%;
    height: 400px;
    border-radius: 16px;
    overflow: hidden;
    background-color: #f5f7fa;

    .card-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;

        .loading-text {
            margin-top: 8px;
            font-size: 12px;
            color: #666;
        }
    }
}

.card-content {
    position: relative;
    // 其他已有样式...

    .upload-status {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &.error {
            padding: 84rpx 0 88rpx;
            background-color: rgba(227, 236, 252, 0.6);
            border-radius: 16rpx;
            border: dotted 2rpx #2972f6;

            .camera-icon {
                width: 96rpx;
                height: 96rpx;
            }

            Text {
                margin-top: 12rpx;
                color: #f24545;
                font-size: 36rpx;
                line-height: 34rpx;
                font-family: HarmonyOSSansSC;
            }
        }

        &.uploading {
            background-color: rgba(227, 236, 252, 0.6);
            border-radius: 16rpx;
            border: dotted 2rpx #2972f6;

            Text {
                color: #2972f6;
                font-size: 28rpx;
                line-height: 26.32rpx;
                font-family: HarmonyOSSansSC;
            }
        }

        &.success {
            padding: 80rpx 0 88rpx;
            background-color: rgba(227, 236, 252, 0.6);
            border-radius: 16rpx;
            border: 2rpx dotted #2972f6;
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;

            .success-icon {
                width: 96rpx;
                height: 96rpx;
            }

            .success-text {
                margin-top: 12rpx;
                font-size: 36rpx;
                font-family: HarmonyOSSansSC;
                line-height: 34rpx;
                color: #6dd401;
            }
        }

        &.loading {
            padding: 80rpx 0 88rpx;
            background-color: rgba(227, 236, 252, 0.9);
            border-radius: 16rpx;
            border: 2rpx dotted #2972f6;
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            z-index: 10;

            .loading-text {
                margin-top: 20rpx;
                font-size: 36rpx;
                font-family: HarmonyOSSansSC;
                line-height: 34rpx;
                color: #2972f6;
            }
        }
    }
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.items-center {
    align-items: center;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
