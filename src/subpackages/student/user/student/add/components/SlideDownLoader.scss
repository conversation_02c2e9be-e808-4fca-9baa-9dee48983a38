.slide-down-loader {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: #ffffff;
    padding: 24rpx 32rpx;
    display: flex;
    align-items: center;
    transform: translateY(-100%);
    transition: transform 0.3s ease-in-out;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    &.show {
        transform: translateY(0);
    }

    &.hide {
        transform: translateY(-100%);
    }

    .loader-icon {
        width: 40rpx;
        height: 40rpx;
        border: 5rpx solid rgba(22, 119, 255, 0.3);
        border-radius: 50%;
        border-top-color: #1677ff;
        animation: spin 1s ease-in-out infinite;
        margin-right: 24rpx;
    }

    .loader-text {
        color: #333333;
        font-size: 28rpx;
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
