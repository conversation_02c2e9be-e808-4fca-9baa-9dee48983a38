.page {
    background: #f7f8fa;
    min-height: 100vh;
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
}

// 步骤一样式
.step-1 {
    .ml-9 {
        margin-left: 18rpx;
    }

    .ml-19 {
        margin-left: 38rpx;
    }

    .mt-9 {
        margin-top: 18rpx;
    }

    .mt-11 {
        margin-top: 22rpx;
    }

    .page {
        background-color: #f8f8f8;
        width: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        height: 100%;
    }

    .group {
        padding-left: 68rpx;
        padding-right: 28rpx;
    }

    .font {
        font-size: 28rpx;
        font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
        line-height: 25.88rpx;
        color: #ababab;
    }

    .text {
        color: #000000;
        font-size: 30rpx;
        line-height: 22.68rpx;
    }

    .section {
        padding: 32rpx 0;
        background-color: #ffffff;
    }

    .equal-division {
        align-self: stretch;
    }

    .section_2 {
        flex: 1 1 324rpx;
    }

    .equal-division-item {
        padding-top: 32rpx;
        overflow: hidden;
        border-radius: 16rpx;
        background-color: #f4f8fe;
        height: 284rpx;
    }

    .group_4 {
        padding: 20rpx 0;
        width: 242rpx;
        height: 160rpx;
        border: solid 2rpx #247fef;
    }

    .font_2 {
        font-size: 36rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 33.76rpx;
        color: #404040;
    }

    .text_3 {
        line-height: 34.02rpx;
    }

    .group_3 {
        padding: 0 32rpx;
    }

    .section_3 {
        padding: 28rpx 20rpx;
        background-color: #ffffff;
        border-radius: 12rpx;
        box-shadow: 0rpx 0rpx 16rpx #4280f21f;
    }

    .footer {
        padding: 20rpx 32rpx;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #ffffff;
        box-shadow: 0rpx -4rpx 10rpx rgba(0, 0, 0, 0.05);
    }

    // 身份证示例样式
    .group_7 {
        margin-top: 28rpx;
        line-height: 28.68rpx;
    }

    .text_8 {
        line-height: 26.26rpx;
    }

    .text_9 {
        color: #ff5757;
        line-height: 28.68rpx;
    }

    .equal-division_2 {
        margin-top: 44rpx;
    }

    .group_8 {
        padding: 0 22rpx;
    }

    .group_9 {
        flex: 1 1 176rpx;
    }

    .equal-division-item_2 {
        padding: 8rpx;
    }

    .group_10 {
        padding-bottom: 16rpx;
    }

    .image-wrapper {
        padding: 16rpx 0;
        background-color: #f2f7fe;
    }

    .image_10 {
        filter: drop-shadow(0rpx 0rpx 8rpx #4280f21f);
        border-radius: 7.38rpx;
        width: 118rpx;
        height: 73.76rpx;
    }
}

// 步骤二样式
.step-2 {
    font-family: Yuanti SC, STYuanti-SC-Regular;

    .group_2 {
        padding: 20rpx 30rpx;
    }

    .font {
        font-size: 28rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 25.96rpx;
        color: #999999;
    }

    .section_3 {
        margin-top: 24rpx;
        padding: 0 24rpx;
        background-color: #ffffff;
        border-radius: 10rpx;
    }

    .font_3 {
        font-size: 26rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        font-weight: 700;
        color: #333333;
    }

    .text_31 {
        margin-left: 28rpx;
        line-height: 27.7rpx;
    }

    .group_19 {
        padding: 24rpx 8rpx 20rpx;
        border-bottom: solid 1rpx #eeeeee;
    }

    .font_6 {
        font-size: 26rpx;
        font-family: PingFang SC;
        line-height: 10.76rpx;
        font-weight: 700;
        color: #ff0000;
    }

    .text_5 {
        color: #1a66ff;
        line-height: 27.8rpx;
    }

    .group_4 {
        padding: 24rpx 3.48rpx 20rpx 7.58rpx;
        border-bottom: solid 2rpx #eeeeee;
    }

    .group_5 {
        padding: 24rpx 3.48rpx 20rpx 7.58rpx;
        border-bottom: solid 2rpx #eeeeee;
    }

    .text_6 {
        text-align: right;
        width: 98%;
        font-size: 26rpx;
    }

    .text_8 {
        line-height: 24.44rpx;
    }

    .group_33 {
        padding: 20rpx 3.48rpx 20rpx 7.58rpx;
    }

    // 选择器样式
    .nut-picker-control {
        height: 80rpx;
    }

    .nut-popup-bottom.nut-popup-round {
        border-radius: 24rpx 24rpx 0 0;
    }

    .nut-picker-roller-item-title {
        font-size: 30rpx;
    }

    .nut-picker-view-panel {
        height: 40vh;
    }

    .nut-popup {
        font-family: Yuanti SC, STYuanti-SC-Regular;
    }

    .nut-picker {
        height: 50vh;
    }
}

// 拍照弹窗样式
.take-photo {
    .ml-23 {
        margin-left: 41.67rpx;
    }

    .pos {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .popup {
        padding: 101.45rpx 28.99rpx 97.83rpx;
        background-color: #ffffff;
        border-radius: 36.23rpx 36.23rpx 0rpx 0rpx;
        z-index: 2;
        position: fixed;
        bottom: 0;
        width: 100%;
    }

    .group_3 {
        padding: 0 3.62rpx;
    }

    .image_6 {
        border-radius: 7.25rpx;
        width: 68.84rpx;
        height: 54.35rpx;
    }

    .font {
        font-size: 28.99rpx;
        font-family: PingFangSC;
        line-height: 26.81rpx;
        font-weight: 600;
        color: #000000cc;
    }

    .text_3 {
        line-height: 26.85rpx;
    }

    .font_2 {
        font-size: 25.36rpx;
        font-family: PingFangSC;
        line-height: 24.11rpx;
        color: #00000066;
    }

    .view {
        margin-top: 119.57rpx;
    }

    .image_7 {
        width: 68.84rpx;
        height: 57.97rpx;
    }

    .text-wrapper {
        margin-top: 100rpx;
        padding: 28.99rpx 3.62rpx;
    }

    .text_4 {
        line-height: 26.7rpx;
        font-weight: unset;
    }

    .footer {
        padding: 20rpx;
        .nut-button {
            height: 88rpx;
            border-radius: 44rpx;
        }
    }
}

// 公共组件样式
.footer {
    padding: 20rpx 32rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    box-shadow: 0rpx -4rpx 10rpx rgba(0, 0, 0, 0.05);

    .nut-button {
        height: 88rpx;
        border-radius: 44rpx;
    }
}

.safe__area {
    height: constant(safe-area-inset-bottom);
    height: env(safe-area-inset-bottom);
}
