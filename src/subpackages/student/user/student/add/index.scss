page {
    background-color: #f7faff;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 100vh;
    font-family: HarmonyOSSansSC;
}

.step-1 {
    .ml-9 {
        margin-left: 18rpx;
    }

    .ml-19 {
        margin-left: 38rpx;
    }

    .mt-9 {
        margin-top: 18rpx;
    }

    .mt-11 {
        margin-top: 22rpx;
    }

    .page {
        background-color: #f8f8f8;
        width: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        height: 100%;
    }

    .group {
        padding-left: 68rpx;
        padding-right: 28rpx;
    }

    .font {
        font-size: 28rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 25.88rpx;
        color: #ababab;
    }

    .text {
        color: #000000;
        font-size: 30rpx;
        line-height: 22.68rpx;
    }

    .image {
        width: 36rpx;
        height: 24rpx;
    }

    .image_2 {
        width: 32rpx;
        height: 24rpx;
    }

    .image_3 {
        width: 50rpx;
        height: 24rpx;
    }

    .group_2 {
        padding: 0 24rpx;
    }

    .image_4 {
        width: 42.26rpx;
        height: 42.26rpx;
    }

    .pos {
        position: absolute;
        left: 22.88rpx;
        top: 50%;
        transform: translateY(-50%);
    }

    .font_2 {
        // font-size: 36rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 33.76rpx;
        color: #404040;
    }

    .text_2 {
        color: #212121;
    }

    .group_3 {
        padding: 0 32rpx;
    }

    .text_3 {
        line-height: 34.02rpx;
    }

    .section_2 {
        flex: 1 1 324rpx;
    }

    .group_4 {
        padding: 20rpx 0;
        width: 242rpx;
        height: 160rpx;
        border-left: solid 2rpx #247fef;
        border-right: solid 2rpx #247fef;
        border-top: solid 2rpx #247fef;
        border-bottom: solid 2rpx #247fef;
    }

    .section_3 {
        padding: 28rpx 20rpx;
        background-color: #ffffff;
        border-radius: 12rpx;
        box-shadow: 0rpx 0rpx 16rpx #4280f21f;
    }

    .group_6 {
        width: 16rpx;
    }

    .section_5 {
        background-color: #8eb3f7;
        border-radius: 4rpx;
        height: 8rpx;
    }

    .image_8 {
        width: 50rpx;
        height: 48rpx;
    }

    .image_6 {
        width: 58rpx;
        height: 64rpx;
    }

    .text-wrapper {
        padding: 20rpx 0;
        background-color: #4280f2;
    }

    .font_3 {
        // font-size: 28rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 25.88rpx;
        color: #ffffff;
    }

    .text_4 {
        line-height: 25.78rpx;
    }

    .section_4 {
        padding: 0 24rpx 20rpx;
        background-color: #ffffff;
        border-radius: 12rpx;
        box-shadow: 0rpx 0rpx 16rpx #4280f21f;
        width: 192rpx;
    }

    .group_5 {
        padding: 20rpx 0 28rpx;
    }

    .image_5 {
        width: 40rpx;
        height: 44rpx;
    }

    .image_7 {
        width: 78rpx;
        height: 28rpx;
    }

    .section_6 {
        background-color: #c7dbff;
        border-radius: 4rpx;
        height: 8rpx;
    }

    .text_5 {
        line-height: 25.82rpx;
    }

    .section {
        padding: 32rpx 0;
        background-color: #ffffff;
    }

    .text_6 {
        line-height: 33.62rpx;
    }

    .text_7 {
        margin-top: 44rpx;
        line-height: 26.34rpx;
    }

    .group_7 {
        margin-top: 28rpx;
        line-height: 28.68rpx;
    }

    .text_8 {
        line-height: 26.26rpx;
    }

    .text_9 {
        color: #ff5757;
        line-height: 28.68rpx;
    }

    .equal-division_2 {
        margin-top: 44rpx;
    }

    .group_8 {
        padding: 0 22rpx;
    }

    .group_9 {
        flex: 1 1 176rpx;
    }
    .group_10 {
        padding-bottom: 16rpx;
    }

    .image-wrapper {
        padding: 16rpx 0;
        background-color: #f2f7fe;
    }

    .image_10 {
        filter: drop-shadow(0rpx 0rpx 8rpx #4280f21f);
        border-radius: 7.38rpx;
        width: 118rpx;
        height: 73.76rpx;
    }

    .image_13 {
        width: 32rpx;
        height: 32rpx;
    }

    .pos_3 {
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
    }

    .image_9 {
        width: 160rpx;
        height: 108rpx;
    }

    .pos_4 {
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
    }

    .text_10 {
        line-height: 26.06rpx;
    }

    .section_7 {
        background-color: #f2f7fe;
        height: 108rpx;
    }

    .group_11 {
        filter: drop-shadow(0rpx 0rpx 8rpx #4280f21f);
        width: 118rpx;
    }

    .pos_2 {
        position: absolute;
        left: 50%;
        top: 18rpx;
        transform: translateX(-50%);
    }

    .image_11 {
        filter: blur(2rpx);
        width: 118rpx;
        height: 73.76rpx;
    }

    .image_12 {
        filter: drop-shadow(0rpx 0rpx 8rpx #4280f21f);
        width: 118rpx;
        height: 73.76rpx;
    }

    .pos_5 {
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
    }

    .text_11 {
        line-height: 26.3rpx;
    }

    .text-wrapper_2 {
        margin: 332rpx 28rpx 0 36rpx;
        padding: 28rpx 0;
        background-color: #2972f6;
        border-radius: 16rpx;
    }

    .text_12 {
        color: #ffffff;
        font-size: 32rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 30.14rpx;
    }

    .image_14 {
        width: 26rpx;
        height: 26rpx;
    }

    .group_12 {
        line-height: 22.78rpx;
        height: 22.78rpx;
    }

    .font_4 {
        font-size: 24rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 22.78rpx;
    }

    .text_13 {
        color: #a6a6a6;
        line-height: 22.58rpx;
    }

    .text_14 {
        color: #009dff;
    }

    .section_8 {
        background-color: #ffffff;
        height: 68rpx;
    }
}

.step-21 {
    .group_2 {
        padding: 20rpx 30rpx;
    }

    .font {
        font-size: 28rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 25.96rpx;
        color: #999999;
        // margin-left: 40rpx;
    }

    .section_2 {
        padding: 20rpx 24rpx;
        background-color: #ffffff;
        border-radius: 10rpx;
    }

    .font_3 {
        font-size: 28rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        // line-height: 25.96rpx;
        font-weight: 700;
        color: #333333;
    }

    .text_3 {
        margin-top: 20rpx;
        line-height: 26.6rpx;
    }

    .group_3 {
        margin: 0 4rpx;
        overflow: hidden;
        width: 70rpx;
        height: 22rpx;
    }

    .text_4 {
        color: #ffffff;
        font-size: 16rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 14.84rpx;
    }

    .section_4 {
        padding: 24rpx 28rpx 26rpx;
        background-color: #ffffff;
        border-radius: 10rpx;

        input {
            margin-left: 20rpx;
        }
    }

    .section_line {
        // margin-top: 31rpx;
        padding: 0 23rpx;
        // border-top: solid 2rpx #f2f5f5;
        border-bottom: solid 2rpx #f2f5f5;
    }

    .text_5 {
        line-height: 26.02rpx;
    }

    .text_6 {
        // margin-right: 16rpx;
        text-align: right;
        width: 98%;
    }

    .section_5 {
        padding: 20rpx 24rpx 26rpx;
        background-color: #ffffff;
        border-radius: 10rpx;
    }

    .text_7 {
        line-height: 25.98rpx;
    }

    .text_8 {
        line-height: 24.44rpx;
    }

    .image_6 {
        width: 10rpx;
        height: 20rpx;
    }

    .text_9 {
        line-height: 26.06rpx;
    }

    .text-wrapper_2 {
        padding: 32rpx 0;
        background-color: #ffffff;
        border-radius: 10rpx;
    }

    .text_10 {
        margin-left: 32rpx;
        margin-right: 32rpx;
        line-height: 28.34rpx;
    }

    .nut-picker-control {
        height: 80rpx;
    }

    .nut-popup-bottom.nut-popup-round {
        border-radius: 24rpx 24rpx 0 0;
    }

    // .nut-popup {
    //     min-height: 45vh;
    // }

    .nut-picker-roller-item-title {
        font-size: 30rpx;
    }

    .nut-picker-view-panel {
        height: 40vh;
    }

    .nut-popup {
        font-family: Yuanti SC, STYuanti-SC-Regular;
    }
}

.take-photo {
    .ml-23 {
        margin-left: 41.67rpx;
    }

    // .mask {
    //     position: fixed;
    //     /* 固定位置 */
    //     top: 0;
    //     left: 0;
    //     width: 100%;
    //     height: 100%;
    //     background-color: rgba(0, 0, 0, 0.5);
    //     /* 半透明黑色背景 */
    //     // z-index: 9999;
    //     /* 确保遮罩层在最上方 */
    //     background-color: #0000004d;
    // }

    .pos {
        position: absolute;
        left: 0;
        right: 0;
        // top: 0;
        bottom: 0;
    }

    .popup {
        padding: 101.45rpx 28.99rpx 97.83rpx;
        background-color: #ffffff;
        border-radius: 36.23rpx 36.23rpx 0rpx 0rpx;
        z-index: 2;
        position: fixed;
        bottom: 0;
        width: 100%;
    }

    .group_3 {
        padding: 0 3.62rpx;
    }

    .image_6 {
        border-radius: 7.25rpx;
        width: 68.84rpx;
        height: 54.35rpx;
    }

    .font {
        font-size: 28.99rpx;
        font-family: PingFangSC;
        line-height: 26.81rpx;
        font-weight: 600;
        color: #000000cc;
    }

    .text_3 {
        line-height: 26.85rpx;
    }

    .font_2 {
        font-size: 25.36rpx;
        font-family: PingFangSC;
        line-height: 24.11rpx;
        color: #00000066;
    }

    .view {
        margin-top: 119.57rpx;
    }

    .image_7 {
        width: 68.84rpx;
        height: 57.97rpx;
    }

    .text-wrapper {
        margin-top: 100rpx;
        padding: 28.99rpx 3.62rpx;
        // background-color: #ebeef4;
        // border-radius: 43.48rpx;
    }

    .text_4 {
        line-height: 26.7rpx;
        font-weight: unset;
    }
}

.step-2 {
    font-family: Yuanti SC, STYuanti-SC-Regular;

    .group_2 {
        padding: 20rpx 30rpx;
    }

    .mt-37 {
        margin-top: 74rpx;
    }

    .text_5 {
        color: #1a66ff;
        line-height: 27.8rpx;
    }

    .font_3 {
        font-size: 26rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        // line-height: 25.96rpx;
        font-weight: 700;
        color: #333333;
    }

    .image_12 {
        margin-right: 12rpx;
        overflow: hidden;
        width: 76rpx;
        height: 38rpx;
    }

    .font_6 {
        font-size: 26rpx;
        font-family: PingFang SC;
        line-height: 10.76rpx;
        font-weight: 700;
        color: #ff0000;
    }

    .group_16 {
        padding: 28rpx 20rpx 24rpx;
        border-bottom: solid 1rpx #eeeeee;
    }

    .image_13 {
        width: 10rpx;
        height: 20rpx;
    }

    .font_9 {
        font-size: 30rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 27.72rpx;
        font-weight: 700;
        color: #333333;
    }

    .text_31 {
        margin-left: 28rpx;
        line-height: 27.7rpx;
    }

    .section_9 {
        padding: 0 8rpx;
        background-color: #ffffff;
        border-radius: 10rpx;
    }

    .text_32 {
        line-height: 23.92rpx;
    }

    .group_20 {
        margin-right: 12rpx;
    }

    .text_33 {
        line-height: 24.16rpx;
    }

    .group_22 {
        margin-right: 12rpx;
    }

    .text_34 {
        margin-left: 28rpx;
    }

    .text_35 {
        line-height: 24.08rpx;
    }

    .text_36 {
        line-height: 24.08rpx;
    }

    .group_23 {
        padding: 20rpx 20rpx 24rpx;
    }

    .text_37 {
        line-height: 24.08rpx;
    }

    .image_14 {
        margin-right: 0;
    }

    .section_3 {
        margin-top: 24rpx;
        padding: 0 24rpx;
        background-color: #ffffff;
        border-radius: 10rpx;
    }

    .font_4 {
        font-size: 26rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 24.02rpx;
        color: #cccccc;
    }

    .text_7 {
        line-height: 24.76rpx;
    }

    .group_4 {
        // padding: 24rpx 8rpx 20rpx;
        padding: 24rpx 3.48rpx 20rpx 7.58rpx;
        border-bottom: solid 2rpx #eeeeee;
    }

    .group_5 {
        padding: 24rpx 3.48rpx 20rpx 7.58rpx;
        border-bottom: solid 2rpx #eeeeee;
    }

    .group_19 {
        padding: 24rpx 8rpx 20rpx;
        border-bottom: solid 1rpx #eeeeee;
    }

    .group_33 {
        padding: 20rpx 3.48rpx 20rpx 7.58rpx;
    }

    .font {
        font-size: 26rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 25.96rpx;
        color: #999999;
    }

    .text_6 {
        // margin-right: 16rpx;
        text-align: right;
        width: 98%;
        font-size: 26rpx;
    }

    .text_8 {
        line-height: 24.44rpx;
        // font-size: 27rpx;
    }
}

.nut-picker {
    height: 50vh;
}

.section {
    padding: 28rpx 24rpx;
    background-color: #ffffff;
    position: relative;

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        &-title {
            font-size: 36rpx;
            font-family: HarmonyOSSansSC;
            line-height: 33.76rpx;
            color: #212121;
        }
    }
}

.bottom-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 24rpx 32rpx;
    background: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
    
    .btn {
        flex: 1;
        margin: 0 12rpx;
        height: 88rpx;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30rpx;
        
        &-cancel {
            background: #f5f5f5;
            color: #666;
        }
        
        &-confirm {
            background: linear-gradient(135deg, #4971f7, #7b61ff);
            color: #fff;
        }
    }
}
