import { useReady } from "@tarojs/taro";
import React, { useState } from "react";
import "./index.scss";
import "@utils/app.scss";
import MessageApi from "@components/MessageApi/MessageApi";
import { Input } from "@tarojs/components";
import IconFont from "@components/iconfont";
import { <PERSON><PERSON>, DatePicker, Picker, Popup, TextArea } from "@nutui/nutui-react-taro";
import request from "@service/request";
import { Image } from "@tarojs/components";
import Taro from "@tarojs/taro";
import moment from "moment";
import upload from "@service/upload";

const App = () => {
    const message: any = React.useRef();

    const [jxDeptId, setJxDeptId] = useState("");
    const [jxFieldId, setJxFieldId] = useState("");
    const [userId, setUserId] = useState("");

    const [xm, setXm] = useState("");
    const [sfzmmc, setSfzmmc] = useState<any>("A");
    const [sfzmmcList, setSfzmmcList] = useState<any[]>([]);
    const [sfzmmcOpen, setSfzmmcOpen] = useState(false);
    const [sfzmhm, setSfzmhm] = useState("");

    const [xb, setXb] = useState<any>(1);
    const [xbList, setXbList] = useState<any[]>([
        { text: "男", value: 1 },
        { text: "女", value: 2 },
    ]);
    const [xbOpen, setXbOpen] = useState(false);

    const [djzsxxdz, setDjzsxxdz] = useState("");
    const [lxzsxxdz, setLxzsxxdz] = useState("");
    const [remark, setRemark] = useState("");

    const [stepIndex, setStepIndex] = useState(1);
    const [openTakePhoto, setOpenTakePhoto] = useState(0);

    const [sfzzm, setSfzzm] = useState("");
    const [sfzfm, setSfzfm] = useState("");

    const [csrq, setCsrq] = useState("");
    const [csrqOpen, setCsrqOpen] = useState(false);

    const [fzrq, setFzrq] = useState("");
    const [fzrqOpen, setFzrqOpen] = useState(false);
    const [yxqz, setYxqz] = useState("");
    const [yxqzOpen, setYxqzOpen] = useState(false);

    const [yddh, setYddh] = useState("");

    const [carType, setCarType] = useState<any>("");
    const [carTypeLsit, setCarTypeList] = useState<any[]>([]);
    const [carTypeOpen, setCarTypeOpen] = useState(false);

    const [jxClassList, setJxClassList] = useState<any[]>([]);
    const [jxClassId, setJxClassId] = useState<any>();
    const [jxClassOpen, setJxClassOpen] = useState(false);

    const [isLoading, setIsLoading] = useState(false);
    const [loadingText, setLoadingText] = useState("");

    useReady(() => {
        request.post<any>("/Wx/JxUser/getMyJxUser", {}).then((json) => {
            if (json && json.success) {
                setJxDeptId(json.data.JxDeptId);
                setJxFieldId(json.data.JxFieldId);
                setUserId(json.data.UserId);

                request
                    .post<API.SelectResult>("/Base/Select/getSfzmmcSelectList", {})
                    .then((json) => {
                        if (json && json.success) {
                            setSfzmmcList(json.data);
                            console.log(json.data);
                        } else {
                            message.current.openDialog("操作失败", "系统故障，稍后重试!");
                        }
                    })
                    .catch(() => {
                        message.current.openDialog("操作失败", "系统故障，稍后重试!");
                    });

                request
                    .post<any>("/JiaXiao/JxClass/getJxClassSelectList", {})
                    .then((json) => {
                        if (json && json.success) {
                            setJxClassList(json.data[0].options);
                        } else {
                            message.current.openDialog("操作失败", "系统故障，稍后重试!");
                        }
                    })
                    .catch(() => {
                        message.current.openDialog("操作失败", "系统故障，稍后重试!");
                    });
            } else {
                message.current.openDialog("无法报名", json.message, () => {
                    Taro.navigateTo({
                        url: "/subpackages/student/user/index/index",
                    });
                });
            }
        });
    });

    const chooseImage = (imageId, sourceType) => {
        setOpenTakePhoto(0);
        Taro.chooseImage({
            count: 1, // 默认9
            sizeType: ["original"],
            sourceType: sourceType,
            success: (res) => {
                if (imageId == 1) {
                    setSfzzm(res.tempFilePaths[0]);
                }
                if (imageId == 2) {
                    setSfzfm(res.tempFilePaths[0]);
                }
            },
        });
    };

    return (
        <>
            <MessageApi ref={message}></MessageApi>
            {stepIndex == 1 && (
                <view className="step-1">
                    <view className="flex-col page">
                        <view className="flex-col">
                            <view
                                className="flex-col section"
                                style={{
                                    padding: "0",
                                }}
                            >
                                <view className="mt-12 flex-col group_3">
                                    <text className="self-start font_2 text_3">请拍摄并上传学员的身份证照片</text>
                                    <view className="mt-20 flex-row equal-division">
                                        <view
                                            className="flex-col section_2 equal-division-item"
                                            onClick={() => {
                                                setOpenTakePhoto(1);
                                            }}
                                        >
                                            <view className="flex-col justify-start items-center self-center group_4">
                                                {sfzzm == "" && (
                                                    <view className="flex-row items-center section_3">
                                                        <view className="flex-col group_6">
                                                            <view className="section_5"></view>
                                                            <view className="mt-6 section_5"></view>
                                                            <view className="mt-6 section_5"></view>
                                                        </view>
                                                        <view className="ml-4 flex-row items-center">
                                                            <Image
                                                                className="image_8"
                                                                src="https://cdn.51panda.com/WxAppImage/pandaWxApp/user/add/95dcca86c925639cfab1c773ad2ca3df.png"
                                                            />
                                                            <Image className="image_6 ml-9" src="https://cdn.51panda.com/WxAppImage/pandaWxApp/user/add/17218725204098355723.png" />
                                                        </view>
                                                    </view>
                                                )}
                                                {sfzzm != "" && (
                                                    <Image
                                                        src={sfzzm}
                                                        style={{
                                                            padding: "0 20rpx",
                                                            width: "100%",
                                                            height: "100%",
                                                        }}
                                                    />
                                                )}
                                            </view>
                                            <view className="mt-14 flex-col justify-start items-center self-stretch text-wrapper">
                                                <text className="font_3 text_4">拍摄正面</text>
                                            </view>
                                        </view>
                                        <view
                                            className="flex-col section_2 equal-division-item ml-19"
                                            onClick={() => {
                                                setOpenTakePhoto(2);
                                            }}
                                        >
                                            <view className="flex-col justify-start items-center self-center group_4">
                                                {sfzfm == "" && (
                                                    <view className="flex-col section_4">
                                                        <view className="flex-row items-center group_5">
                                                            <Image className="image_5" src="https://cdn.51panda.com/WxAppImage/pandaWxApp/user/add/17218725204096604497.png" />
                                                            <Image
                                                                className="ml-12 image_7"
                                                                src="https://cdn.51panda.com/WxAppImage/pandaWxApp/user/add/324487a491cd97010cb9cf0c08791553.png"
                                                            />
                                                        </view>
                                                        <view className="section_6"></view>
                                                    </view>
                                                )}
                                                {sfzfm != "" && (
                                                    <Image
                                                        src={sfzfm}
                                                        style={{
                                                            padding: "0 20rpx",
                                                            width: "100%",
                                                            height: "100%",
                                                        }}
                                                    />
                                                )}
                                            </view>
                                            <view className="mt-14 flex-col justify-start items-center self-stretch text-wrapper">
                                                <text className="font_3 text_5">拍摄背面</text>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view className="flex-col section">
                                <view className="flex-col">
                                    <view className="flex-col items-start group_3">
                                        <text className="font_2 text_6">拍摄身份证要求:</text>
                                        <text className="font text_7">持有的本人有效二代身份证:</text>
                                        <view className="group_7">
                                            <text className="font text_8">拍摄时确保身份证</text>
                                            <text className="font text_9">边框完整、字体清晰、亮度均匀;</text>
                                        </view>
                                    </view>
                                    <view className="flex-row equal-division_2 group_8">
                                        <view className="flex-col group_9 equal-division-item_2">
                                            <view className="flex-col justify-start self-stretch relative group_10">
                                                <view className="flex-col justify-start items-center image-wrapper">
                                                    <Image className="image_10" src="https://cdn.51panda.com/WxAppImage/pandaWxApp/user/add/0d828421822cd50c082459f80a061a86.png" />
                                                </view>
                                                <Image className="image_13 pos_3" src="https://cdn.51panda.com/WxAppImage/pandaWxApp/user/add/17218725204094640155.png" />
                                            </view>
                                            <text className="self-center font mt-9">标准</text>
                                        </view>
                                        <view className="flex-col group_9 equal-division-item_2">
                                            <view className="flex-col justify-start items-center self-stretch relative group_10">
                                                <Image className="image_9" src="https://cdn.51panda.com/WxAppImage/pandaWxApp/user/add/17218725208547830619.png" />
                                                <Image className="image_13 pos_4" src="https://cdn.51panda.com/WxAppImage/pandaWxApp/user/add/17218725206641968633.png" />
                                            </view>
                                            <text className="self-center font text_10 mt-9">边框缺失</text>
                                        </view>
                                        <view className="flex-col group_9 equal-division-item_2">
                                            <view className="flex-col justify-start self-stretch relative group_10">
                                                <view className="section_7"></view>
                                                <view className="flex-col items-center group_11 pos_2">
                                                    <Image className="image_11" src="https://cdn.51panda.com/WxAppImage/pandaWxApp/user/add/0d828421822cd50c082459f80a061a86.png" />
                                                    <Image className="image_13" src="https://cdn.51panda.com/WxAppImage/pandaWxApp/user/add/17218725206641968633.png" />
                                                </view>
                                            </view>
                                            <text className="self-center font mt-9">照片模糊</text>
                                        </view>
                                        <view className="flex-col group_9 equal-division-item_2">
                                            <view className="flex-col justify-start self-stretch relative group_10">
                                                <view className="flex-col justify-start items-center image-wrapper">
                                                    <Image className="image_12" src="https://cdn.51panda.com/WxAppImage/pandaWxApp/user/add/ce18050368a9bc2d1050ccf88a75513f.png" />
                                                </view>
                                                <Image className="image_13 pos_5" src="https://cdn.51panda.com/WxAppImage/pandaWxApp/user/add/17218725206641968633.png" />
                                            </view>
                                            <text className="self-center font text_11 mt-9">闪光强烈</text>
                                        </view>
                                    </view>

                                    <view className="footer">
                                        <Button
                                            icon={<IconFont name="nfc" size={30} style={{}} />}
                                            disabled={isLoading}
                                            block
                                            type="primary"
                                            onClick={() => {
                                                message.current.openToast("当前驾校还未开通此功能");
                                            }}
                                            color={"#ebeef4"}
                                            style={{
                                                color: isLoading ? "#999" : "#000",
                                                marginBottom: "10px",
                                            }}
                                        >
                                            读取二代身份证
                                        </Button>
                                        <Button
                                            disabled={isLoading}
                                            block
                                            type="primary"
                                            onClick={() => {
                                                setStepIndex(2);
                                            }}
                                            color={"#ebeef4"}
                                            style={{
                                                color: isLoading ? "#999" : "#000",
                                                marginBottom: "10px",
                                            }}
                                        >
                                            自助手工填写信息
                                        </Button>
                                        <Button
                                            disabled={isLoading}
                                            loading={isLoading}
                                            block
                                            type="primary"
                                            onClick={() => {
                                                if (sfzzm == "") {
                                                    message.current.openToast("请先上传身份证正面");
                                                } else {
                                                    setIsLoading(true);
                                                    setLoadingText("正在识别正面");

                                                    upload.post<any>("/Jx/Image/IdCard/getIdCardInfo", {}, sfzzm, "file").then((json) => {
                                                        setIsLoading(false);
                                                        if (json && json.success) {
                                                            if (json.data.ImageStatus == "reversed_side") {
                                                                message.current.openToast("请上传正确的正面图片，您上传的是身份证反面");
                                                            } else if (json.data.ImageStatus == "normal") {
                                                                setXm(json.data.xm);
                                                                setSfzmhm(json.data.sfzmhm);
                                                                setDjzsxxdz(json.data.djzsxxdz);
                                                                setCsrq(moment(json.data.csrq).format("YYYY-MM-DD"));

                                                                setXb(json.data.xb == "男" ? 1 : 2);

                                                                if (sfzfm == "") {
                                                                    setStepIndex(2);
                                                                } else {
                                                                    setIsLoading(true);
                                                                    setLoadingText("正在识别反面");
                                                                    upload.post<any>("/Jx/Image/IdCard/getIdCardInfo", {}, sfzfm, "file").then((json) => {
                                                                        setIsLoading(false);
                                                                        if (json.data.ImageStatus == "reversed_side") {
                                                                            setFzrq(moment(json.data.fzrq).format("YYYY-MM-DD"));
                                                                            if (json.data.yxqz == "长期") {
                                                                                setYxqz("2099-01-01");
                                                                            } else {
                                                                                setYxqz(moment(json.data.yxqz).format("YYYY-MM-DD"));
                                                                            }
                                                                            setStepIndex(2);
                                                                        } else if (json.data.ImageStatus == "normal") {
                                                                            message.current.openToast("请上传正确的反面图片，您上传的是身份证正面");
                                                                        } else {
                                                                            message.current.openToast("您上传的反面图片无法识别");
                                                                        }
                                                                    });
                                                                }
                                                            } else {
                                                                message.current.openToast("您上传的正面图片无法识别");
                                                            }
                                                        } else if (json && !json.success) {
                                                            message.current.openDialog("上传失败", json.message);
                                                        } else {
                                                            message.current.openDialog("上传失败", "上传文件发生了网络错误，请稍后重试!");
                                                        }
                                                    });
                                                }
                                            }}
                                        >
                                            {isLoading ? loadingText : "上传识别"}
                                        </Button>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view className="section_8"></view>
                    </view>
                </view>
            )}

            {stepIndex == 2 && (
                <>
                    <view className="step-2">
                        <view className="flex-col group_2">
                            <view className="flex-col mt-8">
                                <text className="self-start font_3 text_31">培训设置</text>
                                <view className="mt-18 flex-col self-stretch section_3">
                                    <view
                                        className="flex-row justify-between group_19"
                                        onClick={() => {
                                            setJxClassOpen(true);
                                        }}
                                    >
                                        <view className="flex-row">
                                            <text className="font_3">培训班型</text>
                                            <text className="ml-8 self-start font_6">*</text>
                                        </view>
                                        <view className="flex-row items-center">
                                            <text className="font text_5">
                                                {jxClassList.find((m: any) => m.value == jxClassId) ? jxClassList.find((m: any) => m.value == jxClassId).text : "请选择培训班型"}
                                            </text>
                                            <IconFont
                                                name="Arrow-Right2"
                                                size={30}
                                                style={{
                                                    marginTop: "0rpx",
                                                    marginLeft: "10rpx",
                                                }}
                                            />
                                        </view>
                                    </view>
                                    <view
                                        className="flex-row justify-between group_19"
                                        onClick={() => {
                                            setCarTypeOpen(true);
                                        }}
                                    >
                                        <view className="flex-row">
                                            <text className="font_3">培训车型</text>
                                            <text className="ml-8 self-start font_6">*</text>
                                        </view>
                                        <view className="flex-row items-center">
                                            <text className="font text_5">
                                                {carTypeLsit.find((m: any) => m.value == carType) ? carTypeLsit.find((m: any) => m.value == carType).text : "请选择培训车型"}
                                            </text>
                                            <IconFont
                                                name="Arrow-Right2"
                                                size={30}
                                                style={{
                                                    marginTop: "0rpx",
                                                    marginLeft: "10rpx",
                                                }}
                                            />
                                        </view>
                                    </view>
                                </view>
                            </view>

                            <view className="flex-col mt-32">
                                <text className="self-start font_3 text_31">证件信息</text>
                                <view className="flex-col self-stretch section_3">
                                    <view
                                        className="flex-row justify-between group_4"
                                        onClick={() => {
                                            setSfzmmcOpen(true);
                                        }}
                                    >
                                        <text className="font_3">证件类型</text>
                                        <view className="flex-row items-center">
                                            <text className="font text_8">
                                                {sfzmmcList.find((m: any) => m.value == sfzmmc) ? sfzmmcList.find((m: any) => m.value == sfzmmc).text : "请选择证件类型"}
                                            </text>
                                            <IconFont
                                                name="Arrow-Right2"
                                                size={30}
                                                style={{
                                                    marginTop: "0rpx",
                                                    marginLeft: "10rpx",
                                                }}
                                            />
                                        </view>
                                    </view>
                                    <view className="flex-row justify-between group_5">
                                        <text className="font_3">证件号码</text>
                                        <Input
                                            className="font text_6"
                                            placeholder="请输入学员的证件号码"
                                            maxlength={20}
                                            type={sfzmmc == "A" || sfzmmc == "Q" ? "idcard" : "text"}
                                            value={sfzmhm}
                                            onInput={(e) => {
                                                setSfzmhm(e.detail.value);
                                            }}
                                        ></Input>
                                    </view>
                                    <view
                                        className="flex-row justify-between group_5"
                                        onClick={() => {
                                            setFzrqOpen(true);
                                        }}
                                    >
                                        <text className="font_3">发证日期</text>
                                        <view className="flex-row items-center">
                                            <text className="font text_8">{fzrq == "" ? "证件有效期开始" : fzrq}</text>
                                            <IconFont
                                                name="Arrow-Right2"
                                                size={30}
                                                style={{
                                                    marginTop: "0rpx",
                                                    marginLeft: "10rpx",
                                                }}
                                            />
                                        </view>
                                    </view>
                                    <view
                                        className="flex-row justify-between group_33"
                                        onClick={() => {
                                            setYxqzOpen(true);
                                        }}
                                    >
                                        <text className="font_3">有效日期</text>
                                        <view className="flex-row items-center">
                                            <text className="font text_8">{yxqz == "" ? "证件有效期截止" : moment(yxqz) == moment("2099-01-01") ? "长期" : yxqz}</text>
                                            <IconFont
                                                name="Arrow-Right2"
                                                size={30}
                                                style={{
                                                    marginTop: "0rpx",
                                                    marginLeft: "10rpx",
                                                }}
                                            />
                                        </view>
                                    </view>
                                </view>
                            </view>

                            <view className="flex-col mt-32">
                                <text className="self-start font_3 text_31">基本信息</text>
                                <view className="flex-col self-stretch section_3">
                                    <view className="flex-row justify-between group_4">
                                        <text className="font_3">学员姓名</text>
                                        <Input
                                            className="font text_6"
                                            placeholder="请输入学员的姓名"
                                            maxlength={20}
                                            value={xm}
                                            onInput={(e) => {
                                                setXm(e.detail.value);
                                            }}
                                        ></Input>
                                    </view>
                                    <view
                                        className="flex-row justify-between group_5"
                                        onClick={() => {
                                            setCsrqOpen(true);
                                        }}
                                    >
                                        <text className="font_3">出生年月</text>
                                        <view className="flex-row items-center">
                                            <text className="font text_8">{csrq == "" ? "请选择出生日期" : csrq}</text>
                                            <IconFont
                                                name="Arrow-Right2"
                                                size={30}
                                                style={{
                                                    marginTop: "0rpx",
                                                    marginLeft: "10rpx",
                                                }}
                                            />
                                        </view>
                                    </view>
                                    <view
                                        className="flex-row justify-between group_5"
                                        onClick={() => {
                                            setXbOpen(true);
                                        }}
                                    >
                                        <text className="font_3 text_7">学员性别</text>
                                        <view className="flex-row items-center">
                                            <text className="font text_8">{xb == 1 ? "男" : "女"}</text>
                                            <IconFont
                                                name="Arrow-Right2"
                                                size={30}
                                                style={{
                                                    marginTop: "0rpx",
                                                    marginLeft: "10rpx",
                                                }}
                                            />
                                        </view>
                                    </view>
                                    <view className="flex-row justify-between group_5">
                                        <text className="font_3">手机号码</text>
                                        <Input
                                            className="font text_6"
                                            placeholder="请输入学员手机号码"
                                            maxlength={11}
                                            type={"number"}
                                            onInput={(e) => {
                                                setYddh(e.detail.value);
                                            }}
                                        ></Input>
                                    </view>
                                    <view className="flex-row justify-between group_5">
                                        <text className="font_3">证件地址</text>
                                        <Input
                                            className="font text_6"
                                            placeholder="请输入身份证件登记地址"
                                            maxlength={100}
                                            value={djzsxxdz}
                                            onInput={(e) => {
                                                setDjzsxxdz(e.detail.value);
                                            }}
                                        ></Input>
                                    </view>
                                    <view className="flex-row justify-between group_33">
                                        <text className="font_3">居住地址</text>
                                        <Input
                                            className="font text_6"
                                            placeholder="请输入本市当前居住地址"
                                            maxlength={100}
                                            value={lxzsxxdz}
                                            onInput={(e) => {
                                                setLxzsxxdz(e.detail.value);
                                            }}
                                        ></Input>
                                    </view>
                                </view>
                            </view>
                            <view className="mt-8 flex-col justify-start items-start text-wrapper_2">
                                <TextArea
                                    className="font"
                                    placeholder="请输入备注，0-200字"
                                    value={remark}
                                    onInput={(e) => {
                                        setRemark(e.detail.value);
                                    }}
                                    maxLength={200}
                                    style={{
                                        // width: 'calc(100vw - 70px)',
                                        height: "90px",
                                        margin: "0",
                                    }}
                                ></TextArea>
                            </view>
                        </view>
                        <view
                            className={"safe__area"}
                            style={{
                                marginTop: "100rpx",
                            }}
                        ></view>
                        <view className="footer">
                            <Button
                                block
                                type="primary"
                                disabled={isLoading}
                                loading={isLoading}
                                onClick={() => {
                                    if (xm == "") {
                                        message.current.openToast("请输入学员姓名");
                                    } else if (sfzmhm == "") {
                                        message.current.openToast("请输入身份证号码");
                                    } else if (sfzmhm.length < 18) {
                                        message.current.openToast("请输入正确的身份证号码");
                                    } else if (yddh.length < 11) {
                                        message.current.openToast("请输入正确的手机号码");
                                    } else if (jxClassId == "") {
                                        message.current.openToast("请选择培训班型");
                                    } else if (carType == "") {
                                        message.current.openToast("请选择培训车型");
                                    } else {
                                        message.current.openConfirm("是否确认提交学员信息", "确认操作", () => {
                                            setIsLoading(true);
                                            setLoadingText("正在获取学费信息");
                                            message.current.openLoading("正在获取学费信息");

                                            request
                                                .post<any>("/JiaXiao/JxClass/getCarTypePrice", {
                                                    JxClassId: jxClassId,
                                                    JxDeptId: jxDeptId,
                                                    JxFieldId: jxFieldId,
                                                    CarType: carType,
                                                })
                                                .then((json) => {
                                                    setIsLoading(true);
                                                    setLoadingText("正在保存");
                                                    message.current.openLoading("正在保存学员信息");
                                                    let postData: any = {
                                                        xm: xm,
                                                        sfzmmc: sfzmmc,
                                                        sfzmhm: sfzmhm,
                                                        xb: xb,
                                                        yddh: yddh,
                                                        gj: 156,
                                                        djzsxxdz: djzsxxdz,
                                                        lxzsxxdz: lxzsxxdz,
                                                        csrq: csrq,
                                                        JxClassId: jxClassId,
                                                        CarType: carType,
                                                        SaleUserId: userId,
                                                        JxDeptId: jxDeptId,
                                                        JxFieldId: jxFieldId,
                                                        PayMoney: json.data.PayMoney,
                                                        JxShouldPayDetails: json.data.ShouldPayDetail,
                                                    };

                                                    if (fzrq !== "") {
                                                        postData.sfzyxqs = fzrq;
                                                    }
                                                    if (yxqz !== "") {
                                                        postData.sfzyxqz = yxqz;
                                                    }

                                                    request.put<API.SelectResult>("/Jx/Student/StudentInfo/addStudentInfo", postData).then((json) => {
                                                        setIsLoading(false);
                                                        if (json && json.success) {
                                                            let studentId = json.data;
                                                            if (sfzzm != "") {
                                                                setIsLoading(true);
                                                                setLoadingText("正在上传身份证图片");
                                                                message.current.openLoading("正在上传身份证正面");
                                                                upload
                                                                    .post<
                                                                        API.Result<{
                                                                            data: string;
                                                                        }>
                                                                    >(
                                                                        "/Jx/Image/StudentImage/uploadStudentImage",
                                                                        {
                                                                            StudentId: studentId,
                                                                            ImageId: 40,
                                                                        },
                                                                        sfzzm,
                                                                        "file"
                                                                    )
                                                                    .then((json) => {
                                                                        if (sfzfm != "") {
                                                                            setIsLoading(true);
                                                                            setLoadingText("正在上传身份证反面");
                                                                            message.current.openLoading("正在上传身份证反面");
                                                                            upload
                                                                                .post<
                                                                                    API.Result<{
                                                                                        data: string;
                                                                                    }>
                                                                                >(
                                                                                    "/Jx/Image/StudentImage/uploadStudentImage",
                                                                                    {
                                                                                        StudentId: studentId,
                                                                                        ImageId: 41,
                                                                                    },
                                                                                    sfzfm,
                                                                                    "file"
                                                                                )
                                                                                .then((json) => {
                                                                                    setIsLoading(true);
                                                                                    setLoadingText("正在合成正反面");
                                                                                    message.current.openLoading("正在合成正反面");
                                                                                    request
                                                                                        .post<API.Result<string>>("/Jx/Image/StudentImage/makeMyIdCard", {
                                                                                            Id: studentId,
                                                                                        })
                                                                                        .then((json) => {
                                                                                            setIsLoading(false);
                                                                                            message.current.openDialog(
                                                                                                "保存成功",
                                                                                                "保存学员资料完成，请前往学员详情界面完善其他的资料",
                                                                                                function () {
                                                                                                    Taro.navigateTo({
                                                                                                        url: `/subpackages/student/user/student/index/index?id=${studentId}`,
                                                                                                    });
                                                                                                }
                                                                                            );
                                                                                        });
                                                                                });
                                                                        } else {
                                                                            message.current.openDialog(
                                                                                "保存成功",
                                                                                "保存学员资料完成，请前往学员详情界面完善其他的资料",
                                                                                function () {
                                                                                    setIsLoading(false);
                                                                                    Taro.navigateTo({
                                                                                        url: `/subpackages/student/user/student/index/index?id=${studentId}`,
                                                                                    });
                                                                                }
                                                                            );
                                                                        }
                                                                    });
                                                            } else if (sfzfm != "") {
                                                                setIsLoading(true);
                                                                setLoadingText("正在上传身份证反面");
                                                                message.current.openLoading("正在上传身份证反面");
                                                                upload
                                                                    .post<
                                                                        API.Result<{
                                                                            data: string;
                                                                        }>
                                                                    >(
                                                                        "/Jx/Image/StudentImage/uploadStudentImage",
                                                                        {
                                                                            StudentId: studentId,
                                                                            ImageId: 41,
                                                                        },
                                                                        sfzfm,
                                                                        "file"
                                                                    )
                                                                    .then((json) => {
                                                                        setIsLoading(false);
                                                                        message.current.openDialog("保存成功", "保存学员资料完成，请前往学员详情界面完善其他的资料", function () {
                                                                            Taro.navigateTo({
                                                                                url: `/subpackages/student/user/student/index/index?id=${studentId}`,
                                                                            });
                                                                        });
                                                                    });
                                                            } else {
                                                                setIsLoading(false);
                                                                message.current.openDialog("保存成功", "保存学员资料完成，请前往学员详情界面完善其他的资料", function () {
                                                                    Taro.navigateTo({
                                                                        url: `/subpackages/student/user/student/index/index?id=${studentId}`,
                                                                    });
                                                                });
                                                            }
                                                        } else {
                                                            setIsLoading(false);
                                                            message.current.openDialog("保存失败", json.message);
                                                        }
                                                    });
                                                });
                                        });
                                    }
                                }}
                            >
                                {isLoading ? loadingText : "保存"}
                            </Button>
                        </view>
                    </view>
                    <Picker
                        title="选择证件类型"
                        visible={sfzmmcOpen}
                        options={sfzmmcList}
                        defaultValue={[sfzmmc]}
                        onConfirm={(list, values) => {
                            setSfzmmc(values[0]);

                            console.log(values[0]);
                        }}
                        onClose={() => {
                            setSfzmmcOpen(false);
                        }}
                    />
                    <Picker
                        title="选择学员性别"
                        visible={xbOpen}
                        options={xbList}
                        defaultValue={[xb == 1 ? "男" : "女"]}
                        onConfirm={(list, values) => {
                            setXb(values[0]);

                            console.log(values[0]);
                        }}
                        onClose={() => {
                            setXbOpen(false);
                        }}
                    />
                    <DatePicker
                        title="选择学员出生日期"
                        visible={csrqOpen}
                        value={new Date(csrq)}
                        showChinese
                        onClose={() => setCsrqOpen(false)}
                        threeDimensional={true}
                        onChange={(options, values) => {
                            console.log(options);
                            console.log(values);
                        }}
                        onConfirm={(options, values) => {
                            setCsrq(moment(`${values[0]}-${values[1]}-${values[2]}`).format("YYYY-MM-DD"));
                        }}
                        startDate={new Date(moment().subtract(100, "years").format("YYYY-01-01"))}
                        endDate={new Date(moment().subtract(15, "years").format("YYYY-12-31"))}
                    />
                    <DatePicker
                        title="选择证件发证日期"
                        visible={fzrqOpen}
                        value={new Date(fzrq)}
                        showChinese
                        onClose={() => setFzrqOpen(false)}
                        threeDimensional={true}
                        onChange={(options, values) => {
                            console.log(options);
                            console.log(values);
                        }}
                        onConfirm={(options, values) => {
                            setFzrq(moment(`${values[0]}-${values[1]}-${values[2]}`).format("YYYY-MM-DD"));
                        }}
                        startDate={new Date(moment().subtract(100, "years").format("YYYY-01-01"))}
                        endDate={new Date(moment().add(1, "years").format("YYYY-12-31"))}
                    />
                    <DatePicker
                        title="选择证件有效日期"
                        visible={yxqzOpen}
                        value={new Date(yxqz)}
                        showChinese
                        onClose={() => setYxqzOpen(false)}
                        threeDimensional={true}
                        onChange={(options, values) => {
                            console.log(options);
                            console.log(values);
                        }}
                        onConfirm={(options, values) => {
                            setCsrq(moment(`${values[0]}-${values[1]}-${values[2]}`).format("YYYY-MM-DD"));
                        }}
                        startDate={new Date(moment().subtract(1, "years").format("YYYY-01-01"))}
                        endDate={new Date(moment().add(200, "years").format("YYYY-12-31"))}
                    />
                    <Picker
                        title="选择班名班型"
                        visible={jxClassOpen}
                        options={jxClassList}
                        defaultValue={[jxClassId]}
                        onConfirm={(list, values) => {
                            if (jxClassId !== values[0]) {
                                setJxClassId(values[0]);

                                message.current.openLoading("正在获取相关车型");
                                request
                                    .post<any>("/JiaXiao/JxClass/getCarTypeSelectList", {
                                        JxDeptId: jxClassId,
                                        JxClassId: values[0],
                                    })
                                    .then((json) => {
                                        message.current.closeLoading();
                                        if (json && json.success) {
                                            if (json.data.length == 0) {
                                                message.current.openDialog("操作失败", "该班型暂无相关车型");
                                            } else {
                                                setCarTypeList(json.data);
                                                setCarTypeOpen(true);
                                                setCarType("");
                                            }
                                        } else {
                                            message.current.openDialog("操作失败", json.message);
                                        }
                                    })
                                    .catch(() => {
                                        message.current.closeLoading();
                                        message.current.openDialog("操作失败", "系统故障，稍后重试!");
                                    });
                            }
                        }}
                        onClose={() => {
                            setJxClassOpen(false);
                        }}
                    />
                    <Picker
                        title="选择报名车型"
                        visible={carTypeOpen}
                        options={carTypeLsit}
                        defaultValue={[carType]}
                        onConfirm={(list, values) => {
                            setCarType(values[0]);
                        }}
                        onClose={() => {
                            setCarTypeOpen(false);
                        }}
                    />
                </>
            )}
            {
                <Popup
                    visible={openTakePhoto > 0}
                    position="bottom"
                    onClose={() => {
                        setOpenTakePhoto(0);
                    }}
                    lockScroll
                    duration={0}
                >
                    <view className="take-photo">
                        <view className="flex-col justify-start mask pos">
                            <view className="flex-col popup">
                                <view
                                    className="flex-row items-center group_3"
                                    onClick={() => {
                                        chooseImage(openTakePhoto, ["album"]);
                                    }}
                                >
                                    <Image className="image_6" src="https://cdn.51panda.com/WxAppImage/pandaWxApp/user/add/87b2f9bf65bcda607ce47bdb409b9491.png" />
                                    <view className="flex-col items-start flex-1 ml-23">
                                        <text className="font text_3">相册</text>
                                        <text className="mt-10 font_2">直接从相册上传照片</text>
                                    </view>
                                </view>
                                <view
                                    className="flex-row items-center group_3 view"
                                    onClick={() => {
                                        chooseImage(openTakePhoto, ["camera"]);
                                    }}
                                >
                                    <Image className="image_7" src="https://cdn.51panda.com/wx/photo/a4fab962dc018dcec846d1befb430aff.png" />
                                    <view className="flex-col items-start flex-1 ml-23">
                                        <text className="font">拍照</text>
                                        <text className="mt-10 font_2">直接用手机相机拍照</text>
                                    </view>
                                </view>
                                <view className="flex-col justify-start items-center text-wrapper">
                                    <view className="footer">
                                        <Button
                                            block
                                            type="primary"
                                            onClick={() => {
                                                setOpenTakePhoto(0);
                                            }}
                                            color={"#ebeef4"}
                                            style={{
                                                color: "#000",
                                            }}
                                        >
                                            取消
                                        </Button>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </Popup>
            }
        </>
    );
};
export default App;
