page {
    background: #f7f8fa;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", "Microsoft YaHei", sans-serif;
}

.step-1 {
    .ml-9 {
        margin-left: 18rpx;
    }

    .ml-19 {
        margin-left: 38rpx;
    }

    .mt-9 {
        margin-top: 18rpx;
    }

    .mt-11 {
        margin-top: 22rpx;
    }

    .page {
        background-color: #f8f8f8;
        width: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        height: 100%;
    }

    .group {
        padding: 32rpx;
    }

    .font {
        font-size: 28rpx;
        line-height: 1.5;
        color: #666;
    }

    .text {
        color: #333;
        font-size: 32rpx;
        line-height: 1.5;
        font-weight: 500;
    }

    .image {
        width: 36rpx;
        height: 24rpx;
    }

    .image_2 {
        width: 32rpx;
        height: 24rpx;
    }

    .image_3 {
        width: 50rpx;
        height: 24rpx;
    }

    .group_2 {
        padding: 0 24rpx;
    }

    .image_4 {
        width: 42.26rpx;
        height: 42.26rpx;
    }

    .pos {
        position: absolute;
        left: 22.88rpx;
        top: 50%;
        transform: translateY(-50%);
    }

    .font_2 {
        font-size: 36rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 34rpx;
        color: #404040;
    }

    .text_2 {
        color: #212121;
    }

    .group_3 {
        padding: 0 32rpx;
    }

    .text_3 {
        line-height: 34.02rpx;
    }

    .equal-division {
        align-self: stretch;
    }

    .section_2 {
        flex: 1 1 324rpx;
    }

    .equal-division-item {
        padding: 32rpx;
        border-radius: 24rpx;
        background-color: #fff;
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.98);
        }
    }

    .group_4 {
        padding: 20rpx 0;
        width: 242rpx;
        height: 160rpx;
        border-left: solid 2rpx #247fef;
        border-right: solid 2rpx #247fef;
        border-top: solid 2rpx #247fef;
        border-bottom: solid 2rpx #247fef;
    }

    .section_3 {
        padding: 32rpx;
        background-color: #fff;
        border-radius: 24rpx;
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
    }

    .group_6 {
        width: 16rpx;
    }

    .section_5 {
        background-color: #8eb3f7;
        border-radius: 4rpx;
        height: 8rpx;
    }

    .image_8 {
        width: 50rpx;
        height: 48rpx;
    }

    .image_6 {
        width: 58rpx;
        height: 64rpx;
    }

    .text-wrapper {
        padding: 24rpx;
        background-color: #2972fe;
        border-radius: 16rpx;
        margin: 32rpx;
        box-shadow: 0 8rpx 32rpx rgba(41, 114, 254, 0.2);
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.98);
        }
    }

    .font_3 {
        font-size: 28rpx;
        line-height: 1.5;
        color: #fff;
        text-align: center;
    }

    .text_4 {
        line-height: 25.78rpx;
    }

    .section_4 {
        padding: 32rpx;
        background-color: #fff;
        border-radius: 24rpx;
        margin: 24rpx;
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);

        input {
            margin-left: 24rpx;
            font-size: 28rpx;
            color: #333;
        }
    }

    .group_5 {
        padding: 20rpx 0 28rpx;
    }

    .image_5 {
        width: 40rpx;
        height: 44rpx;
    }

    .image_7 {
        width: 78rpx;
        height: 28rpx;
    }

    .section_6 {
        background-color: #c7dbff;
        border-radius: 4rpx;
        height: 8rpx;
    }

    .text_5 {
        line-height: 25.82rpx;
    }

    .section {
        padding: 32rpx 0;
        background-color: #ffffff;
    }

    .text_6 {
        line-height: 33.62rpx;
    }

    .text_7 {
        margin-top: 44rpx;
        line-height: 26.34rpx;
    }

    .group_7 {
        margin-top: 28rpx;
        line-height: 28.68rpx;
    }

    .text_8 {
        line-height: 26.26rpx;
    }

    .text_9 {
        color: #ff5757;
        line-height: 28.68rpx;
    }

    .equal-division_2 {
        margin-top: 44rpx;
    }

    .group_8 {
        padding: 0 22rpx;
    }

    .group_9 {
        flex: 1 1 176rpx;
    }

    .equal-division-item_2 {
        padding: 8rpx;
    }

    .group_10 {
        padding-bottom: 16rpx;
    }

    .image-wrapper {
        padding: 16rpx 0;
        background-color: #f2f7fe;
    }

    .image_10 {
        filter: drop-shadow(0rpx 0rpx 8rpx #4280f21f);
        border-radius: 7.38rpx;
        width: 118rpx;
        height: 73.76rpx;
    }

    .image_13 {
        width: 32rpx;
        height: 32rpx;
    }

    .pos_3 {
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
    }

    .image_9 {
        width: 160rpx;
        height: 108rpx;
    }

    .pos_4 {
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
    }

    .text_10 {
        line-height: 26.06rpx;
    }

    .section_7 {
        background-color: #f2f7fe;
        height: 108rpx;
    }

    .group_11 {
        filter: drop-shadow(0rpx 0rpx 8rpx #4280f21f);
        width: 118rpx;
    }

    .pos_2 {
        position: absolute;
        left: 50%;
        top: 18rpx;
        transform: translateX(-50%);
    }

    .image_11 {
        filter: blur(2rpx);
        width: 118rpx;
        height: 73.76rpx;
    }

    .image_12 {
        filter: drop-shadow(0rpx 0rpx 8rpx #4280f21f);
        width: 118rpx;
        height: 73.76rpx;
    }

    .pos_5 {
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
    }

    .text_11 {
        line-height: 26.3rpx;
    }

    .text-wrapper_2 {
        margin: 332rpx 28rpx 0 36rpx;
        padding: 28rpx 0;
        background-color: #2972f6;
        border-radius: 16rpx;
    }

    .text_12 {
        color: #ffffff;
        font-size: 32rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 30rpx;
    }

    .image_14 {
        width: 26rpx;
        height: 26rpx;
    }

    .group_12 {
        line-height: 22.78rpx;
        height: 22.78rpx;
    }

    .font_4 {
        font-size: 24rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 23rpx;
    }

    .text_13 {
        color: #a6a6a6;
        line-height: 22.58rpx;
    }

    .text_14 {
        color: #009dff;
    }

    .section_8 {
        background-color: #ffffff;
        height: 68rpx;
    }
}

.step-21 {
    .group_2 {
        padding: 20rpx 30rpx;
    }

    .font {
        font-size: 28rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 26rpx;
        color: #999999;
    }

    .section_2 {
        padding: 32rpx;
        background-color: #fff;
        border-radius: 24rpx;
        margin: 24rpx;
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
    }

    .font_3 {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        line-height: 1.5;
    }

    .text_3 {
        margin-top: 20rpx;
        line-height: 26.6rpx;
    }

    .group_3 {
        margin: 0 4rpx;
        overflow: hidden;
        width: 70rpx;
        height: 22rpx;
    }

    .text_4 {
        color: #ffffff;
        font-size: 28rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 15rpx;
    }

    .section_4 {
        padding: 32rpx;
        background-color: #fff;
        border-radius: 24rpx;
        margin: 24rpx;
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);

        input {
            margin-left: 24rpx;
            font-size: 28rpx;
            color: #333;
        }
    }

    .section_line {
        padding: 0 32rpx;
        border-bottom: 2rpx solid #f5f5f5;
    }

    .text_5 {
        line-height: 26.02rpx;
    }

    .text_6 {
        text-align: right;
        width: 98%;
        font-size: 28rpx;
        color: #666;
    }

    .section_5 {
        padding: 20rpx 24rpx 26rpx;
        background-color: #ffffff;
        border-radius: 10rpx;
    }

    .text_7 {
        line-height: 25.98rpx;
    }

    .text_8 {
        line-height: 24.44rpx;
    }

    .image_6 {
        width: 10rpx;
        height: 20rpx;
    }

    .text_9 {
        line-height: 26.06rpx;
    }

    .text-wrapper_2 {
        padding: 32rpx 0;
        background-color: #ffffff;
        border-radius: 10rpx;
    }

    .text_10 {
        margin-left: 32rpx;
        margin-right: 32rpx;
        line-height: 28.34rpx;
    }

    .nut-picker-control {
        height: 80rpx;
    }

    .nut-popup-bottom.nut-popup-round {
        border-radius: 24rpx 24rpx 0 0;
    }

    .nut-picker-roller-item-title {
        font-size: 30rpx;
    }

    .nut-picker-view-panel {
        height: 40vh;
    }

    .nut-popup {
        font-family: Yuanti SC, STYuanti-SC-Regular;
    }
}

.take-photo {
    .ml-23 {
        margin-left: 41.67rpx;
    }

    .pos {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .popup {
        padding: 101.45rpx 28.99rpx 97.83rpx;
        background-color: #ffffff;
        border-radius: 36.23rpx 36.23rpx 0rpx 0rpx;
        z-index: 2;
        position: fixed;
        bottom: 0;
        width: 100%;
    }

    .group_3 {
        padding: 0 3.62rpx;
    }

    .image_6 {
        border-radius: 7.25rpx;
        width: 68.84rpx;
        height: 54.35rpx;
    }

    .font {
        font-size: 28.99rpx;
        font-family: PingFangSC;
        line-height: 26.81rpx;
        font-weight: 600;
        color: #000000cc;
    }

    .text_3 {
        line-height: 26.85rpx;
    }

    .font_2 {
        font-size: 25.36rpx;
        font-family: PingFangSC;
        line-height: 24.11rpx;
        color: #00000066;
    }

    .view {
        margin-top: 119.57rpx;
    }

    .image_7 {
        width: 68.84rpx;
        height: 57.97rpx;
    }

    .text-wrapper {
        margin-top: 100rpx;
        padding: 28.99rpx 3.62rpx;
    }

    .text_4 {
        line-height: 26.7rpx;
        font-weight: unset;
    }
}

.step-2 {
    font-family: Yuanti SC, STYuanti-SC-Regular;

    .group_2 {
        padding: 20rpx 30rpx;
    }

    .mt-37 {
        margin-top: 74rpx;
    }

    .text_5 {
        color: #1a66ff;
        line-height: 27.8rpx;
    }

    .font_3 {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        line-height: 1.5;
    }

    .image_12 {
        margin-right: 12rpx;
        overflow: hidden;
        width: 76rpx;
        height: 38rpx;
    }

    .font_6 {
        font-size: 28rpx;
        line-height: 1.5;
        font-weight: 500;
        color: #ff4d4f;
    }

    .group_16 {
        padding: 28rpx 20rpx 24rpx;
        border-bottom: solid 1rpx #eeeeee;
    }

    .image_13 {
        width: 10rpx;
        height: 20rpx;
    }

    .font_9 {
        font-size: 32rpx;
        line-height: 1.5;
        font-weight: 600;
        color: #333;
    }

    .text_31 {
        margin-left: 28rpx;
        line-height: 27.7rpx;
    }

    .section_9 {
        padding: 0 8rpx;
        background-color: #ffffff;
        border-radius: 10rpx;
    }

    .text_32 {
        line-height: 23.92rpx;
    }

    .group_20 {
        margin-right: 12rpx;
    }

    .text_33 {
        line-height: 24.16rpx;
    }

    .group_22 {
        margin-right: 12rpx;
    }

    .text_34 {
        margin-left: 28rpx;
    }

    .text_35 {
        line-height: 24.08rpx;
    }

    .text_36 {
        line-height: 24.08rpx;
    }

    .group_23 {
        padding: 20rpx 20rpx 24rpx;
    }

    .text_37 {
        line-height: 24.08rpx;
    }

    .image_14 {
        margin-right: 0;
    }

    .section_3 {
        margin: 24rpx;
        padding: 32rpx;
        background-color: #fff;
        border-radius: 24rpx;
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
    }

    .font {
        font-size: 28rpx;
        line-height: 1.5;
        color: #666;
    }

    .text_6 {
        text-align: right;
        width: 98%;
        font-size: 28rpx;
        color: #666;
    }

    .text_8 {
        line-height: 24.44rpx;
    }
}

.nut-picker {
    height: 50vh;

    .nut-picker-roller-item {
        font-size: 32rpx;
        color: #333;
    }
}
