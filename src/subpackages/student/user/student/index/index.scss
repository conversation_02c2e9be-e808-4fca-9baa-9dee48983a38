page {
    background-color: #eff1f7;
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
}

.page {
    // background-color: #eff1f7;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

.student-info {
    .ml-5 {
        margin-left: 9.62rpx;
    }

    .ml-9 {
        margin-left: 17.31rpx;
    }

    .ml-3 {
        margin-left: 5.77rpx;
    }

    .mt-11 {
        margin-top: 21.15rpx;
    }

    .mt-15 {
        margin-top: 28.85rpx;
    }

    .ml-41 {
        margin-left: 78.85rpx;
    }

    .section {
        padding: 33rpx 30.77rpx 263.46rpx;
        background-image: linear-gradient(180deg, #59bdff1a -36.7%, #585ce500 136.7%);
    }

    .group {
        padding-left: 33.56rpx;
        padding-right: 25.63rpx;
    }

    .image {
        width: 53.85rpx;
        height: 21.15rpx;
    }

    .image_2 {
        width: 128.85rpx;
        height: 21.15rpx;
    }

    .image_3 {
        width: 61.54rpx;
        height: 61.54rpx;
    }

    .image_4 {
        width: 46.15rpx;
        height: 46.15rpx;
    }

    .view {
        margin-top: -186.54rpx;
        padding: 0 30.77rpx;
    }

    .group_4 {
        padding-bottom: 15.38rpx;
    }

    .section_2 {
        padding: 0 30.75rpx 30.77rpx;
        background-color: #ffffff;
        border-radius: 15.38rpx;
        // height: 436.54rpx;
    }

    .group_2 {
        margin-top: -46.15rpx;
    }

    .image_5 {
        margin-bottom: 5.77rpx;
        border-radius: 50%;
        width: 160rpx;
        height: 190rpx;
    }

    .section_3 {
        padding: 10.58rpx 12.17rpx 10.58rpx 15.38rpx;
        background-color: #666bff1a;
        border-radius: 23.08rpx;
        height: 51.92rpx;
    }

    .image_6 {
        width: 30.77rpx;
        height: 30.77rpx;
    }

    .font {
        font-size: 26.92rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 23.83rpx;
        color: #585ce5;
    }

    .text {
        line-height: 24.85rpx;
    }

    .group_5 {
        margin-top: 24.06rpx;
        padding: 0 6.04rpx;
    }

    .text_2 {
        color: #15151a;
        font-size: 46.15rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 43.38rpx;
    }

    .font_2 {
        font-size: 26.92rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 23.83rpx;
        color: #585ce5cc;
    }

    .text_3 {
        color: #15151a;
        line-height: 24.77rpx;
    }

    .text_4 {
        line-height: 24.85rpx;
    }

    .group_6 {
        margin-top: 32.56rpx;
    }

    .text-wrapper {
        padding: 9.29rpx 0 6.92rpx;
        background-color: #00b587;
        border-radius: 7.69rpx;
        width: 53.85rpx;
        height: 32.69rpx;
    }

    .font_3 {
        font-size: 19.23rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 16.48rpx;
        color: #ffffff;
    }

    .text_5 {
        color: #000000;
        line-height: 25.23rpx;
    }

    .group_7 {
        margin-top: 31.44rpx;
    }

    .group_8 {
        line-height: 22.15rpx;
        height: 22.15rpx;
    }

    .font_4 {
        font-size: 30.77rpx;
        font-family: YouSheBiaoTiHei;
        line-height: 21.46rpx;
    }

    .text_6 {
        color: #ffc300;
        line-height: 21.63rpx;
    }

    .text_7 {
        color: #00b588;
        line-height: 22.15rpx;
    }

    .font_5 {
        font-size: 26.92rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 23.83rpx;
        color: #15151a99;
    }

    .text_8 {
        line-height: 25.15rpx;
    }

    .group_9 {
        margin-top: 15.52rpx;
    }

    .text_9 {
        line-height: 24.98rpx;
    }

    .group_10 {
        margin-right: 15.4rpx;
        width: 145.19rpx;
    }

    .image_7 {
        margin-left: 106.73rpx;
        width: 38.46rpx;
        height: 38.46rpx;
    }

    .text_10 {
        margin-left: -145.19rpx;
        color: #00b588;
        line-height: 25.02rpx;
    }

    .group_11 {
        margin-top: 33.65rpx;
    }

    .font_6 {
        font-size: 34.62rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        font-weight: 700;
        color: #585ce5;
    }

    .text_11 {
        margin-bottom: 8.46rpx;
        line-height: 26.58rpx;
    }

    .group_12 {
        width: 166.88rpx;
    }

    .font_7 {
        font-size: 26.92rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 23.83rpx;
        color: #15151a4d;
    }

    .text_13 {
        margin-left: 28.42rpx;
        line-height: 24.9rpx;
    }

    .image_8 {
        margin-left: -166.88rpx;
    }

    .text_14 {
        margin-bottom: 8.46rpx;
        line-height: 26.56rpx;
    }

    .group_13 {
        width: 137.98rpx;
    }

    .text_15 {
        margin-left: 26.44rpx;
        line-height: 24.88rpx;
    }

    .image_9 {
        margin-left: -137.98rpx;
    }

    .text_12 {
        margin-right: 28.77rpx;
        margin-bottom: 5.83rpx;
        line-height: 32.67rpx;
    }

    .section_4 {
        padding-left: 26.92rpx;
        padding-right: 26.92rpx;
        background-color: #ffffff;
        border-radius: 15.38rpx;
    }

    .group_14 {
        margin-left: 6.25rpx;
        margin-right: 23.56rpx;
        padding: 40.5rpx 0 22.96rpx;
    }

    .font_8 {
        font-size: 23.08rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 21.46rpx;
        color: #000000;
    }

    .text_16 {
        line-height: 21.27rpx;
    }

    .font_9 {
        font-size: 30.77rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
    }

    .text_17 {
        color: #15151a99;
        line-height: 36.54rpx;
        text-align: center;
        width: 163.46rpx;
    }

    .group_15 {
        padding: 31rpx 2.17rpx 33rpx;
        border-top: solid 1.92rpx #15151a33;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .text_18 {
        line-height: 21.38rpx;
    }

    .text_19 {
        line-height: 25.19rpx;
        margin-left: 20rpx;
    }

    .section_5 {
        padding: 38.46rpx 0 26.92rpx 30.77rpx;
        overflow: hidden;
        background-color: #ffffff;
        border-radius: 15.38rpx;
    }

    .group_16 {
        margin-right: 30.77rpx;
    }

    .text_20 {
        margin-top: 2.15rpx;
        color: #000000;
        line-height: 28.27rpx;
    }

    .text_21 {
        line-height: 23.67rpx;
    }

    .horiz-list {
        overflow-x: auto;
    }

    .horiz-list-item {
        flex-shrink: 0;
    }

    .section_6 {
        padding-bottom: 15.85rpx;
        background-color: #edf3ff;
        border-radius: 15.38rpx;
        width: 100rpx;
        height: 115.38rpx;
    }

    .group_18 {
        padding: 23.96rpx 0 2.21rpx;
    }

    .text-wrapper_2 {
        padding: 6.02rpx 0 26.1rpx;
        // background-image: url('https://ide.code.fun/api/image?token=6695557547b10e0011255241&name=8f7f3f58f41a09516dcf772cf0032779.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 50rpx;
    }

    .pos {
        position: absolute;
        left: 0;
        top: 0;
    }

    .text_22 {
        margin-left: 6.44rpx;
        line-height: 17.88rpx;
    }

    .font_10 {
        font-size: 38.46rpx;
        font-family: AlimamaShuHeiTi;
        line-height: 27.92rpx;
        color: #15151a;
    }

    .section_7 {
        flex-shrink: 0;
        margin-left: 23.08rpx;
    }

    .horiz-list-item_1 {
        padding: 23.96rpx 0 16.35rpx;
        background-color: #edf3ff;
        border-radius: 15.38rpx;
        width: 100rpx;
        height: 115.38rpx;
    }

    .text_24 {
        line-height: 26.92rpx;
    }

    .section_8 {
        padding: 23.96rpx 0 15.85rpx;
        background-color: #585ce5;
        border-radius: 15.38rpx;
        width: 100rpx;
        height: 115.38rpx;
    }

    .text_23 {
        color: #ffffffcc;
    }

    .text_25 {
        color: #ffffff;
        line-height: 27.42rpx;
    }

    .horiz-list-item_2 {
        padding: 23.96rpx 0 15.85rpx;
        background-color: #edf3ff;
        border-radius: 15.38rpx;
        width: 100rpx;
        height: 115.38rpx;
    }

    .text_26 {
        line-height: 26.88rpx;
    }

    .section_9 {
        padding: 23.96rpx 0 15.85rpx;
        background-color: #edf3ff;
        border-radius: 15.38rpx 0 0 15.38rpx;
        width: 103.08rpx;
        height: 115.38rpx;
    }

    .text_27 {
        margin-right: 7.42rpx;
    }

    .section_10 {
        background-color: #edf3ff;
        border-radius: 15.38rpx 0 0 15.38rpx;
        width: 80rpx;
        height: 115.38rpx;
    }

    .pos_2 {
        position: absolute;
        right: -37.69rpx;
        top: 0;
    }

    .grid {
        margin-right: 30.77rpx;
        height: 134.62rpx;
        display: grid;
        grid-template-rows: repeat(2, minmax(0, 1fr));
        grid-template-columns: repeat(2, minmax(0, 1fr));
        row-gap: 25.08rpx;
        column-gap: 28.92rpx;
    }

    .grid-item {
        padding: 15.96rpx 20.58rpx 0 32.12rpx;
        border-radius: 15.38rpx;
        border: solid 3.85rpx #585ce5;
    }

    .text_28 {
        line-height: 32.69rpx;
        text-align: center;
        width: 138.46rpx;
    }

    .font_11 {
        font-size: 23.08rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 21.46rpx;
        color: #15151a99;
    }

    .text_29 {
        color: #585ce5;
    }

    .grid-item_2 {
        padding: 15.96rpx 24.42rpx 3.27rpx 33.13rpx;
        border-radius: 15.38rpx;
        border: solid 1.92rpx #15151a80;
    }

    .font_12 {
        font-size: 26.92rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 32.69rpx;
        color: #15151a99;
    }

    .text_30 {
        text-align: center;
        width: 136.54rpx;
    }

    .text-wrapper_3 {
        margin-right: 30.77rpx;
        padding: 24.42rpx 0 23.12rpx;
        background-color: #00b587;
        border-radius: 15.38rpx;
    }

    .text_31 {
        color: #ffffff;
        font-size: 30.77rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        font-weight: 700;
        line-height: 29.38rpx;
    }

    .group_1 {
        padding-left: 32.15rpx;
        padding-right: 32.15rpx;
    }

    .group_19 {
        padding-bottom: 20.1rpx;
    }

    .text_32 {
        color: #000000;
        font-size: 34.62rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 32.54rpx;
    }

    .text_33 {
        color: #15151a80;
        line-height: 28.5rpx;
    }

    .section_11 {
        margin-left: 6.31rpx;
        background-color: #00b487;
        border-radius: 3.85rpx;
        width: 53.85rpx;
        height: 7.69rpx;
    }

    .section_12 {
        padding: 30.77rpx 30.77rpx 0;
        background-color: #ffffff;
        border-radius: 15.38rpx 15.38rpx 0 0;
    }

    .text-wrapper_4 {
        padding: 14.83rpx 0 9.73rpx;
        background-color: #15151a0d;
        border-radius: 28.85rpx 28.85rpx 0 0;
        width: 92.31rpx;
        height: 46rpx;
    }

    .text_34 {
        line-height: 21.44rpx;
    }

    .text-wrapper_5 {
        padding: 14.96rpx 0 9.77rpx;
        background-color: #15151a0d;
        border-radius: 28.85rpx;
        width: 188.46rpx;
        height: 46.15rpx;
    }

    .text_35 {
        line-height: 21.42rpx;
    }

    .text-wrapper_6 {
        padding: 14.87rpx 0 9.83rpx;
        background-color: #15151a0d;
        border-radius: 28.85rpx;
        width: 151.92rpx;
        height: 46.15rpx;
    }
}

.exam-list {
    .exam-item {
        margin-top: 24rpx;
    }

    .ml-1 {
        margin-left: 2rpx;
    }

    .font {
        font-size: 28rpx;
        font-family: PingFang SC;
        line-height: 25.98rpx;
        color: #b3b3b3;
    }

    .font_2 {
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 700;
        color: #253738;
    }

    .exam-section_3 {
        padding: 38rpx 32rpx 34rpx 38rpx;
        background-color: #ffffff;
        border-radius: 15.38rpx;
    }

    .text_11 {
        line-height: 27.8rpx;
    }

    .divider {
        margin-top: 16rpx;
        background-color: #e0e1e4;
        height: 0.8rpx;
    }

    .group_7 {
        padding: 0 2rpx;
    }

    .view_3 {
        margin-top: 38rpx;
    }

    .view_4 {
        margin-top: 42rpx;
    }

    .text_13 {
        line-height: 25.9rpx;
    }

    .group_8 {
        margin-top: 42rpx;
        padding: 0 4rpx;
    }

    .text_15 {
        line-height: 25.88rpx;
    }

    .font_3 {
        font-size: 28rpx;
        font-family: PingFang SC;
        line-height: 25.98rpx;
        color: #253738;
    }

    .text_16 {
        line-height: 26.8rpx;
    }

    .text_14 {
        line-height: 26.74rpx;
    }

    .text_12 {
        line-height: 26.14rpx;
    }

    .exam-flex-row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        /* 左右对齐 */
        align-items: center;
        /* 垂直居中对齐 */
    }
}

.pay-list {
    .pay-item {
        margin-top: 24rpx;
    }

    .ml-1 {
        margin-left: 2rpx;
    }

    .font {
        font-size: 28rpx;
        font-family: PingFang SC;
        line-height: 25.98rpx;
        color: #b3b3b3;
    }

    .font_2 {
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 700;
        color: #253738;
    }

    .pay-section_3 {
        padding: 38rpx 32rpx 34rpx 38rpx;
        background-color: #ffffff;
        border-radius: 15.38rpx;
    }

    .text_11 {
        line-height: 27.8rpx;
    }

    .divider {
        margin-top: 16rpx;
        background-color: #e0e1e4;
        height: 0.8rpx;
    }

    .group_7 {
        padding: 0 2rpx;
    }

    .view_3 {
        margin-top: 38rpx;
    }

    .view_4 {
        margin-top: 42rpx;
    }

    .text_13 {
        line-height: 25.9rpx;
    }

    .group_8 {
        margin-top: 42rpx;
        padding: 0 4rpx;
    }

    .text_15 {
        line-height: 25.88rpx;
    }

    .font_3 {
        font-size: 28rpx;
        font-family: PingFang SC;
        line-height: 25.98rpx;
        color: #253738;
    }

    .text_16 {
        line-height: 26.8rpx;
    }

    .text_14 {
        line-height: 26.74rpx;
    }

    .text_12 {
        line-height: 26.14rpx;
    }

    .pay-flex-row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        /* 左右对齐 */
        align-items: center;
        /* 垂直居中对齐 */
    }

    .bottom-pay {
        margin-bottom: -20rpx;
    }
}

.image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: space-between;

    .divider {
        margin-top: 16rpx;
        background-color: #e0e1e4;
        height: 0.8rpx;
    }

    .mt-31 {
        margin-top: 62rpx;
    }

    .ml-9 {
        margin-left: 18rpx;
    }

    .ml-17 {
        margin-left: 34rpx;
    }

    .page {
        background-color: #504de5;
        width: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        height: 100%;
    }

    .group {
        padding: 88rpx 28rpx 64rpx;
    }

    .image {
        width: 40rpx;
        height: 28rpx;
    }

    .text {
        color: #ffffff;
        font-size: 32rpx;
        font-family: SF Pro Text;
        font-weight: 700;
        line-height: 23.06rpx;
    }

    .image_2 {
        width: 32rpx;
        height: 30rpx;
    }

    .section {
        background-color: #ffffff;
        border-radius: 50rpx 50rpx 0rpx 0rpx;
    }

    .group_2 {
        padding: 36rpx 0 40rpx 28rpx;
        border-bottom: solid 2rpx #f0f2f6;
    }

    .font {
        font-size: 28rpx;
        font-family: SF Pro Text;
        letter-spacing: 2rpx;
        line-height: 34rpx;
        font-weight: 600;
        color: #3b566e;
    }

    .text_2 {
        width: 206rpx;
    }

    .horiz-list {
        align-self: stretch;
    }

    .group_3 {
        overflow-x: auto;
    }

    .image_3 {
        flex-shrink: 0;
    }

    .horiz-list-item {
        border-radius: 50%;
        width: 92rpx;
        height: 92rpx;
    }

    .horiz-list-item_2 {
        flex-shrink: 0;
        margin-right: -48rpx;
    }

    .image_4 {
        border-radius: 50% 0 0 50%;
        width: 86rpx;
        height: 92rpx;
    }

    .text_3 {
        margin-left: 32rpx;
        width: 216rpx;
    }

    .list {
        padding: 0 28rpx;
    }

    .list-item {
        width: calc(50% - 15rpx);
        padding: 8rpx 28rpx 28rpx;
        background-color: #ffffff;
        border-radius: 24rpx;
        box-shadow: 0rpx 4rpx 96rpx #00000021;
    }

    .image_51 {
        margin-top: 20rpx;
        // width: 200rpx;
        // height: 200rpx;
        width: 100%;
        // height: 100%;
        object-fit: cover;
    }

    .font_2 {
        font-size: 28rpx;
        font-family: SF Pro Text;
        letter-spacing: 2rpx;
        line-height: 34rpx;
        color: #3b566e;
    }

    .text_4 {
        width: 276rpx;
    }

    .font_3 {
        font-size: 24rpx;
        font-family: SF Pro Text;
        letter-spacing: 2rpx;
        line-height: 23.12rpx;
        color: #504de5;
    }

    .text_5 {
        line-height: 22.88rpx;
    }

    .group_4 {
        width: 288.98rpx;
    }

    .image_6 {
        width: 22rpx;
        height: 26rpx;
    }

    .font_4 {
        font-size: 24rpx;
        font-family: SF Pro Text;
        letter-spacing: 2rpx;
        line-height: 28rpx;
        color: #6f8ba4;
    }

    .image_7 {
        width: 30rpx;
        height: 30rpx;
    }

    .text_6 {
        width: 164rpx;
    }

    .text-wrapper {
        padding: 20rpx 0;
        background-color: #504de5;
        border-radius: 30rpx;
        width: 180rpx;
        height: 60rpx;
    }

    .font_5 {
        // font-size: 24rpx;
        font-family: SF Pro Text;
        line-height: 18.24rpx;
        // font-weight: 700;
        // color: #ffffff;
    }

    .section_2 {
        margin: 0 28rpx;
        padding: 8rpx 28rpx 56rpx;
        background-color: #ffffff;
        border-radius: 24rpx 24rpx 0 0;
        box-shadow: 0rpx 4rpx 96rpx #00000021;
    }

    .text_9 {
        width: 272rpx;
    }

    .section_3 {
        padding: 20rpx 50rpx;
        background-color: #ffffff;
        border-radius: 50rpx 50rpx 0rpx 0rpx;
        box-shadow: 0rpx -4rpx 32rpx #00000029;
    }

    .pos {
        position: absolute;
        left: 0;
        right: 0;
        top: 64rpx;
    }

    .image_9 {
        width: 42rpx;
        height: 40rpx;
    }

    .image_8 {
        width: 100rpx;
        height: 100rpx;
    }

    .image_10 {
        width: 38rpx;
        height: 40rpx;
    }

    .image_11 {
        width: 42rpx;
        height: 38rpx;
    }
}

.take-photo {
    .ml-23 {
        margin-left: 41.67rpx;
    }

    // .mask {
    //     position: fixed;
    //     /* 固定位置 */
    //     top: 0;
    //     left: 0;
    //     width: 100%;
    //     height: 100%;
    //     background-color: rgba(0, 0, 0, 0.5);
    //     /* 半透明黑色背景 */
    //     // z-index: 9999;
    //     /* 确保遮罩层在最上方 */
    //     background-color: #0000004d;
    // }

    .pos {
        position: absolute;
        left: 0;
        right: 0;
        // top: 0;
        bottom: 0;
    }

    .popup {
        padding: 101.45rpx 28.99rpx 97.83rpx;
        background-color: #ffffff;
        border-radius: 36.23rpx 36.23rpx 0rpx 0rpx;
        z-index: 2;
        position: fixed;
        bottom: 0;
        width: 100%;
    }

    .group_3 {
        padding: 0 3.62rpx;
    }

    .image_6 {
        border-radius: 7.25rpx;
        width: 68.84rpx;
        height: 54.35rpx;
    }

    .font {
        font-size: 28.99rpx;
        font-family: PingFangSC;
        line-height: 26.81rpx;
        font-weight: 600;
        color: #000000cc;
    }

    .text_3 {
        line-height: 26.85rpx;
    }

    .font_2 {
        font-size: 25.36rpx;
        font-family: PingFangSC;
        line-height: 24.11rpx;
        color: #00000066;
    }

    .view {
        margin-top: 119.57rpx;
    }

    .image_7 {
        width: 68.84rpx;
        height: 57.97rpx;
    }

    .text-wrapper {
        margin-top: 100rpx;
        padding: 28.99rpx 3.62rpx;
        // background-color: #ebeef4;
        // border-radius: 43.48rpx;
    }

    .text_4 {
        line-height: 26.7rpx;
        font-weight: unset;
    }
}
