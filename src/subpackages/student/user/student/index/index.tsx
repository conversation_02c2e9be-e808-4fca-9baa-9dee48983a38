import Taro from "@tarojs/taro";
import React, { useRef, useEffect } from "react";
import StudentDetail, { StudentDetailRef } from "@components/StudentDetail";
import TopNavBar from "@/components/topNavBar";

const App = () => {
    const studentDetailRef = useRef<StudentDetailRef>(null);

    const getId = () => {
        const params = Taro.getCurrentInstance().router?.params || {};

        // 使用参数时，将所有参数名转成小写
        const normalizedParams = Object.keys(params).reduce((acc, key) => {
            acc[key.toLowerCase()] = params[key];
            return acc;
        }, {});

        return normalizedParams["id"];
    };

    const studentId = getId();

    useEffect(() => {
        if (studentId && studentDetailRef.current) {
            studentDetailRef.current.open(studentId);
        }
    }, [studentId]);

    const handleClose = () => {
        // 关闭弹窗后返回上一页
        Taro.navigateBack();
    };

    return (
        <>
            <TopNavBar title="学员详情" homeClick={() => Taro.navigateBack()} BgColor="#eff1f7" />
            <StudentDetail ref={studentDetailRef} onClose={handleClose} fullScreen={true} />
        </>
    );
};

export default App;
