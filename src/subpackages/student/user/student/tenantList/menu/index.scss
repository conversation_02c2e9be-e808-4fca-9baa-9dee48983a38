.menu-container {
    padding: 24rpx;
    background-color: #f5f7fa;
    min-height: 100vh;
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular;

    * {
        font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
    }
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
    padding: 12rpx;
}

.menu-item {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 24rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 1rpx solid #eef2f7;

    &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
    }

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4rpx;
        background: linear-gradient(90deg, #3b82f6, #2563eb);
        opacity: 0.9;
    }
}

.menu-icon {
    width: 72rpx;
    height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    position: relative;
    flex-shrink: 0;
}

.subject-number {
    width: 72rpx;
    height: 72rpx;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 28rpx;
    box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.25);
}

.custom-icon {
    width: 72rpx;
    height: 72rpx;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 28rpx;
    box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.25);
}

.menu-title {
    font-size: 30rpx;
    color: #1f2937;
    font-weight: 500;
    text-align: left;
    margin-bottom: 0;
    flex: 1;
}

.menu-count {
    position: absolute;
    top: 12rpx;
    right: 12rpx;
    background: #3b82f6;
    color: #ffffff;
    font-size: 22rpx;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    min-width: 36rpx;
    text-align: center;
    box-shadow: 0 2rpx 6rpx rgba(59, 130, 246, 0.25);
}

.search-container {
    padding: 24rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #eef2f7;
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 16rpx;
}

.search-input {
    width: 100%;
    height: 80rpx;
    background-color: #f5f7fa;
    border-radius: 40rpx;
    padding: 0 36rpx;
    font-size: 30rpx;
    box-sizing: border-box;
    // margin-bottom: 20rpx;
    border: 1rpx solid #eef2f7;
    color: #1f2937;
    transition: all 0.3s ease;

    &:focus {
        background-color: #ffffff;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3rpx rgba(59, 130, 246, 0.1);
    }
}

.search-button {
    width: 100%;
    height: 80rpx;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: #ffffff;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.25);

    &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.3);
    }
}

.filter-container {
    display: flex;
    justify-content: space-between;
    padding: 0 10rpx;
}

.filter-item {
    display: flex;
    align-items: center;
    padding: 10rpx 20rpx;
    background-color: #f8f9fc;
    border-radius: 8rpx;
    border: 1rpx solid #e8e8e8;
}

.filter-label {
    font-size: 26rpx;
    color: #4a5568;
    margin-right: 10rpx;
}

.filter-value {
    font-size: 26rpx;
    color: #2d3748;
}

.search-bar {
    .section {
        padding: 10rpx 21rpx 10rpx 34rpx;
        background-color: #f4f6f9;
        border-radius: 40.5rpx;
    }

    .ml-13 {
        margin-left: 26rpx;
    }

    .group_5 {
        margin-right: 4rpx;
        width: 106.96rpx;
        height: 56rpx;
    }

    .text-wrapper {
        padding: 8rpx 0;
        background-color: #d64444;
        border-radius: 18rpx;
        width: 52rpx;
        border-left: solid 2rpx #ffffff;
        border-right: solid 2rpx #ffffff;
        border-top: solid 2rpx #ffffff;
        border-bottom: solid 2rpx #ffffff;
    }

    .font {
        font-size: 24rpx;
        font-family: PingFang SC;
        line-height: 17.54rpx;
        font-weight: 700;
        color: #ff0000;
    }

    .text_3 {
        color: #ffffff;
        line-height: 17.3rpx;
    }

    .group_6 {
        margin-top: -10rpx;
    }

    .image_7 {
        width: 36rpx;
        height: 32rpx;
    }

    .font_3 {
        font-size: 24rpx;
        font-family: PingFang SC;
        line-height: 20.32rpx;
        color: #999999;
    }

    .text_5 {
        font-size: 26rpx;
        line-height: 24.06rpx;
    }

    .nut-searchbar-content {
        background: #f4f6f9;
        padding: 0;
    }

    .nut-searchbar-input-box {
        padding: 0 10rpx;
    }
}
