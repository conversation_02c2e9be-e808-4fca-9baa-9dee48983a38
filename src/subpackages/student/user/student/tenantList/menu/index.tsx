import { View, Text, Input } from "@tarojs/components";
import { useState } from "react";
import "./index.scss";
import Layout from "@components/Layout";
import Taro from "@tarojs/taro";
import { SearchBar } from "@nutui/nutui-react-taro";
import TopNavBar from "@/components/topNavBar";

interface MenuItem {
    id: string;
    title: string;
    customIcon?: string;
    count?: number;
}

const StudentMenuPage = () => {
    const [menuItems] = useState<MenuItem[]>([
        { id: "1", title: "在培学员", customIcon: "培" },
        { id: "2", title: "毕业学员", customIcon: "毕" },
        { id: "3", title: "退学学员", customIcon: "退" },
        { id: "-1", title: "未注册学员", customIcon: "未" },
        { id: "101", title: "科一阶段", customIcon: "壹" },
        { id: "102", title: "科二阶段", customIcon: "贰" },
        { id: "103", title: "科三阶段", customIcon: "叁" },
        { id: "104", title: "科四阶段", customIcon: "肆" },
        { id: "203", title: "三个月内过期", customIcon: "过" },
        { id: "206", title: "六个月内过期", customIcon: "过" },
        { id: "200", title: "已经过期", customIcon: "过" },
    ]);

    const [searchValue, setSearchValue] = useState("");

    const handleKeywordSearch = () => {
        if (searchValue.trim()) {
            Taro.navigateTo({
                url: `/subpackages/student/user/student/list/index?SearchKey=${encodeURIComponent(searchValue.trim())}`,
            });
        }
    };

    const handleMenuClick = (item: MenuItem) => {
        Taro.navigateTo({
            url: `/subpackages/student/user/student/list/index?SearchType=${item.id}`,
        });
    };

    return (
        <Layout>
            <TopNavBar title="学员搜索" homeClick={() => Taro.redirectTo({ url: "/subpackages/student/user/student/tenantList/menu/index" })} />

            <view
                id={"search-bar-top"}
                style={{
                    backgroundColor: "#fff",
                    width: "100%",
                    zIndex: 99999,
                }}
            >
                <view
                    className="flex-col search-bar"
                    style={{
                        // position: 'fixed',
                        // top: `0px`,
                        width: "100%",
                        backgroundColor: "#fff",
                        paddingLeft: "20rpx",
                        paddingRight: "20rpx",
                    }}
                >
                    <view
                        className="flex-col flex-1 group_2"
                        style={{
                            paddingBottom: "20rpx",
                            borderRadius: "0 24rpx 0",
                        }}
                    >
                        <view className="flex-row">
                            <view
                                style={{
                                    display: "flex",
                                    width: "100%",
                                    alignItems: "center",
                                    background: "#fff",
                                    marginTop: "10rpx",
                                }}
                            >
                                <view
                                    className="flex-row justify-between items-center section"
                                    style={{
                                        width: "100%",
                                    }}
                                >
                                    <SearchBar
                                        style={{
                                            border: 0,
                                            padding: 0,
                                        }}
                                        placeholder="请输入关键字词"
                                        onSearch={handleKeywordSearch}
                                        onChange={setSearchValue}
                                    ></SearchBar>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <View className="menu-container">
                <View className="menu-grid">
                    {menuItems.map((item) => (
                        <View key={item.id} className="menu-item" onClick={() => handleMenuClick(item)}>
                            <View className="menu-icon">
                                <View className="custom-icon">{item.customIcon}</View>
                            </View>
                            <Text className="menu-title">{item.title}</Text>
                            {item.count !== undefined && (
                                <View className="menu-count">
                                    <Text>{item.count}</Text>
                                </View>
                            )}
                        </View>
                    ))}
                </View>
            </View>
        </Layout>
    );
};

export default StudentMenuPage;
