.search-container {
    padding: 20rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #eee;
}

.search-input {
    width: 100%;
    height: 72rpx;
    background-color: #f5f5f5;
    border-radius: 36rpx;
    padding: 0 20rpx;
    box-sizing: border-box;
    font-size: 28rpx;
}

.list-container {
    padding: 0 20rpx;
}

.table-header {
    display: flex;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #eee;
    font-weight: bold;
    font-size: 26rpx;
}

.table-row {
    display: flex;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #eee;
}

.header-item,
.row-item {
    flex: 1;
    text-align: center;
    font-size: 24rpx;
}

.row-item {
    color: #333;
}

.expiry-item {
    padding: 4rpx 8rpx;
    border-radius: 8rpx;

    &.normal {
        background-color: #f5f5f5;
        color: #333;
    }

    &.warning {
        background-color: #fff7e6;
        color: #fa8c16;
    }

    &.expired {
        background-color: #fff1f0;
        color: #f5222d;
    }
}
