import { View, Text, Input } from "@tarojs/components";
import { useState } from "react";
import "./index.scss";
import Layout from "@components/Layout";

const SelectCarPage = () => {
    const [searchValue, setSearchValue] = useState("");

    const handleSearch = (e) => {
        setSearchValue(e.detail.value);
    };

    const getExpiryStatus = (dateStr) => {
        const today = new Date();
        const expiryDate = new Date(dateStr);
        const diffTime = expiryDate.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays < 0) return "expired";
        if (diffDays <= 30) return "warning";
        return "normal";
    };

    // 模拟数据
    const carList = [
        {
            licensePlate: "粤A12345",
            user: "张三",
            insuranceExpiry: "2024-12-31",
            inspectionExpiry: "2024-12-31",
        },
        {
            licensePlate: "粤B67890",
            user: "李四",
            insuranceExpiry: "2024-11-30",
            inspectionExpiry: "2024-11-30",
        },
    ];

    return (
        <Layout>
            <View className="search-container">
                <Input className="search-input" placeholder="输入车牌号或车辆编号" value={searchValue} onInput={handleSearch} />
            </View>
            <View className="list-container">
                <View className="table-header">
                    <Text className="header-item">车牌</Text>
                    <Text className="header-item">使用人</Text>
                    <Text className="header-item">保险到期</Text>
                    <Text className="header-item">年审到期</Text>
                </View>
                {carList.map((car, index) => (
                    <View key={index} className="table-row">
                        <Text className="row-item">{car.licensePlate}</Text>
                        <Text className="row-item">{car.user}</Text>
                        <Text className={`row-item expiry-item ${getExpiryStatus(car.insuranceExpiry)}`}>{car.insuranceExpiry}</Text>
                        <Text className={`row-item expiry-item ${getExpiryStatus(car.inspectionExpiry)}`}>{car.inspectionExpiry}</Text>
                    </View>
                ))}
            </View>
        </Layout>
    );
};

export default SelectCarPage;
