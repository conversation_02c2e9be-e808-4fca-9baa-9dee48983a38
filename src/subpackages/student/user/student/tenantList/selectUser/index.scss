.search-container {
    padding: 20rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #eee;
}

.search-input {
    width: 100%;
    height: 72rpx;
    background-color: #f5f5f5;
    border-radius: 36rpx;
    padding: 0 30rpx;
    font-size: 28rpx;
    box-sizing: border-box;
}

.list-container {
    padding: 20rpx;
}

.user-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
}

.user-name {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
}

.user-phone,
.user-idcard,
.user-tenant,
.user-account {
    font-size: 28rpx;
    color: #666;
}

.loading,
.empty {
    text-align: center;
    padding: 40rpx;
    color: #999;
    font-size: 28rpx;
}
