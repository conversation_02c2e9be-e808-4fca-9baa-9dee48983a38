import { useReady } from "@tarojs/taro";
import React, { useRef, useState } from "react";
import "./index.scss";
import "@utils/app.scss";
import MessageApi from "@components/MessageApi/MessageApi";
import { Camera } from "@tarojs/components";
import { Badge, Button } from "@nutui/nutui-react-taro";
import request from "@service/request";
import Taro from "@tarojs/taro";
import upload from "@service/upload";
import IconFont from "@components/iconfont";
import { tool } from "@utils/tool";

const App = () => {
    const message: any = React.useRef();

    const [devicePosition, setDevicePosition] = useState("back");

    const DOUBLE_CLICK_THRESHOLD = 500; // 双击时间间隔阈值，单位为毫秒
    const [lastClickTime, setLastClickTime] = useState(0);

    const [loading, setLoading] = useState(false);

    const [data, setData] = useState<any[]>([]);

    useReady(() => {
        getUserInfo();
        let position = tool.data.get("longtime-face-device-position");

        if (position == "back" || position == "front") {
            setDevicePosition(position);
        }
    });

    const getUserInfo = () => {
        message.current.openLoading("正在读取用户信息");
        request.post<any>("/Wx/Login/getUserInfo", {}).then((json) => {
            message.current.closeLoading();
            if (!json || !json.success) {
                Taro.navigateTo({
                    url: "/subpackages/student/user/index/index",
                });
            }
        });
    };

    return (
        <>
            <MessageApi ref={message}></MessageApi>

            <view className="flex-col page">
                <view className="flex-col">
                    <view className="flex-col section_2">
                        <view className="flex-col">
                            <view className="flex-col self-stretch group_2">
                                <text className="text_2 mt-8">请保持正脸在取景框中</text>
                                <text className="font">双击取景窗切换镜头</text>
                            </view>
                            <view
                                className="flex-col items-center"
                                onClick={() => {
                                    const currentTime = Date.now();
                                    if (currentTime - lastClickTime <= DOUBLE_CLICK_THRESHOLD) {
                                        tool.data.set("longtime-face-device-position", devicePosition == "back" ? "front" : "back");
                                        setDevicePosition(devicePosition == "back" ? "front" : "back");
                                    }
                                    setLastClickTime(currentTime);
                                }}
                            >
                                <Camera
                                    mode="normal"
                                    devicePosition={devicePosition == "back" ? "back" : "front"}
                                    className="camera"
                                    flash={"off"}
                                    style={{
                                        height: "75vw",
                                        width: "75vw",
                                        borderRadius: "50%",
                                    }}
                                ></Camera>
                            </view>
                        </view>
                        <view className="flex-col justify-start items-center mt-34-5">
                            <Button
                                block
                                type="primary"
                                loading={loading}
                                disabled={loading}
                                onClick={async () => {
                                    const context = Taro.createCameraContext();
                                    context.takePhoto({
                                        quality: "high",
                                        success: (res) => {
                                            setLoading(true);
                                            upload
                                                .post<{
                                                    success: boolean;
                                                    message: string;
                                                    data: any;
                                                }>("/JiaXiao/Face/Student/faceSearch", {}, res.tempImagePath, "file")
                                                .then((json) => {
                                                    setLoading(false);
                                                    if (json && json.success) {
                                                        setData([json.data, ...data.slice(0, 9)]);
                                                        message.current.openAlert(
                                                            "识别成功",
                                                            `学员姓名:${json.data.xm},推荐人:${json.data.SaleUserName},详细信息请点击下面的结果列表.`
                                                        );
                                                    } else if (json) {
                                                        message.current.openToast(json.message);
                                                    }
                                                })
                                                .catch((e) => {
                                                    setLoading(false);
                                                    message.current.openToast(JSON.stringify(e));
                                                });
                                        },
                                    });
                                }}
                            >
                                学员查询
                            </Button>
                        </view>
                    </view>
                </view>
                <view
                    className="flex-col safe__area student-list"
                    id="primaryScroll"
                    style={{
                        overflowY: "auto",
                    }}
                >
                    <>
                        {data.map((item, index) => {
                            return (
                                <view
                                    className="flex-col flex-1 group_2"
                                    onClick={() => {
                                        Taro.navigateTo({
                                            url: `/subpackages/student/user/student/index/index?id=${item.Id}`,
                                        });
                                    }}
                                >
                                    <view
                                        className={
                                            item.NoPay > 0 ? "flex-row justify-between relative mt-12 list-item qianfei" : "flex-row justify-between relative mt-12 list-item"
                                        }
                                        style={{
                                            paddingRight: "40rpx",
                                        }}
                                    >
                                        <view className="flex-row self-center">
                                            <Badge style={{ marginInlineEnd: "5px" }} value={index + 1}>
                                                {item.xb == 1 && <IconFont name="008-man" size={80} />}
                                                {item.xb == 2 && <IconFont name="014-woman" size={80} />}
                                            </Badge>
                                            <view className="flex-col shrink-0 group_5 ml-13">
                                                <text className="self-start font_3 ">
                                                    {item.xm}
                                                    <text className="ml-8 mt-6 self-stretch font_4">介:{item.SaleUserName}</text>
                                                </text>
                                                <text className="mt-6 self-stretch font_4">{item.Ywzt}</text>
                                            </view>
                                        </view>
                                        <view className="flex-col">
                                            <text className="self-start font text_7">{item.Age} 岁</text>
                                            <text
                                                className="self-end mt-12"
                                                style={{
                                                    fontWeight: "300",
                                                }}
                                            >
                                                {item.CarType}
                                            </text>
                                        </view>
                                    </view>
                                </view>
                            );
                        })}
                    </>
                </view>
            </view>
        </>
    );
};
export default App;
