page {
    background: #f7f8fa;
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
}

.mt-17 {
    margin-top: 34rpx;
}

.mt-5 {
    margin-top: 10rpx;
}

.mt-22-5 {
    margin-top: 45rpx;
}

.mt-25-5 {
    margin-top: 50px;
}

.mt-19 {
    margin-top: 38rpx;
}

.mt-34-5 {
    margin-top: 69rpx;
}

.page {
    // padding-bottom: 100rpx;
    background-color: #f5faff;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    // height: 100%;
}

.section {
    padding: 10rpx 14rpx 24rpx;
    background-color: #ffffff;
}

.image {
    width: 96.2667vw;
    height: 2.6667vw;
}

.group {
    padding: 0 10rpx 6rpx;
}

.image_2 {
    width: 17rpx;
    height: 32.82rpx;
}

.pos {
    position: absolute;
    left: 10.3rpx;
    top: 50%;
    transform: translateY(-50%);
}

.text {
    color: #333333;
    font-size: 34rpx;
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular, Source Sans Pro;
    line-height: 30.96rpx;
}

.section_2 {
    padding: 0rpx 62rpx 42rpx;
    background-color: #ffffff;
}

.group_2 {
    text-align: center;
}

.text_2 {
    color: #222222;
    font-size: 40rpx;
    font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
    font-weight: 700;
    line-height: 48rpx;
}

.font {
    font-size: 32rpx;
    font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
    line-height: 80rpx;
    color: #22222280;
}

.image_3 {
    width: 400rpx;
    height: 400rpx;
}

.equal-division {
    margin: 0 6rpx;
}

.group_3 {
    flex: 1 1 205.06rpx;
}

.equal-division-item {
    padding: 10rpx 0;
}

.image_4 {
    width: 80rpx;
    height: 80rpx;
}

.font_2 {
    font-size: 32rpx;
    font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
    line-height: 29.38rpx;
    color: #222222;
}

.text-wrapper {
    margin: 0 50rpx;
    padding: 34rpx 0 44rpx;
    background-image: linear-gradient(90deg, #00acfc 0%, #0082fc 100%);
    border-radius: 10rpx;
}

.text_3 {
    color: #ffffff;
    font-size: 36rpx;
    font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
    line-height: 32.5rpx;
}


.student-list {
    .qianfei {
        width: 100%;
        background: linear-gradient(to right, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #fa2c19);
    }

    .group_2 {
        padding: 18rpx 20rpx 0rpx;
        // overflow-y: auto;
        // margin-top: 2rpx;
    }

    .section {
        padding: 16rpx 21rpx 16rpx 34rpx;
        background-color: #f4f6f9;
        border-radius: 40.5rpx;
    }

    .font {
        font-size: 26rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
        line-height: 18.98rpx;
        color: #545d69;
    }

    .text_2 {
        line-height: 19.76rpx;
    }

    .image_5 {
        width: 48rpx;
        height: 49rpx;
    }

    .group_3 {
        padding: 20rpx 0 13rpx 0;
    }

    .group_4 {
        padding: 0 3rpx;
    }


    .text-wrapper {
        padding: 12rpx 0;
        border-radius: 20rpx;
        width: 150rpx;
        height: 63rpx;
        border-left: solid 3rpx #2972fe;
        border-right: solid 3rpx #2972fe;
        border-top: solid 3rpx #2972fe;
        border-bottom: solid 3rpx #2972fe;
    }

    .text-wrapper_2 {
        padding: 11rpx 0 20rpx;
        background-color: #2972fe;
        border-radius: 20rpx;
        width: 144rpx;
        height: 63rpx;
        border-left: solid 3rpx #2972fe;
        border-right: solid 3rpx #2972fe;
        border-top: solid 3rpx #2972fe;
        border-bottom: solid 3rpx #2972fe;
    }

    .text-wrapper_3 {
        padding: 14rpx 0 20rpx;
        border-radius: 20rpx;
        width: 162rpx;
        height: 63rpx;
        border-left: solid 3rpx #2972fe;
        border-right: solid 3rpx #2972fe;
        border-top: solid 3rpx #2972fe;
        border-bottom: solid 3rpx #2972fe;
    }

    .text_5 {
        line-height: 34rpx;
    }

    .list-item {
        padding: 20rpx 20rpx 20rpx 20rpx;
        background-color: #ffffff;
        border-radius: 26rpx;
        box-shadow: 0rpx 2rpx 24rpx #76767633;
    }

    .list-item:first-child {
        margin-top: 0;
    }

    .image_6 {
        border-radius: 50%;
        width: 88rpx;
        height: 88rpx;
    }

    .group_5 {
        margin-top: 6rpx;
        width: 60vw;
    }

    .ml-13 {
        margin-left: 26rpx;
    }

    .font_3 {
        font-size: 32rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
        line-height: 38rpx;
        font-weight: 400;
        color: #09101d;
    }

    .text_6 {
        width: 131rpx;
    }

    .font_4 {
        font-size: 26rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
        line-height: 31rpx;
        color: #545d69;
    }

    .text_7 {
        margin-top: 12rpx;
    }

    .section_2 {
        padding: 53rpx 48rpx;
        background-color: #ffffff;
        border-radius: 42rpx;
        box-shadow: 0rpx 2rpx 24rpx #76767633;
    }

    .image_7 {
        border-radius: 14rpx;
        width: 76rpx;
        height: 54rpx;
    }

    .section_3 {
        padding: 8rpx 16rpx 8rpx 22rpx;
        background-color: #eaf2ff;
        border-radius: 14rpx;
        height: 54rpx;
    }

    .image_8 {
        width: 36rpx;
        height: 36rpx;
    }

    .text_14 {
        color: #2972fe;
        font-size: 31.54rpx;
        line-height: 23.96rpx;
    }

    .text_4 {
        color: #ffffff;
        font-size: 26rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
        // font-weight: 400;
        line-height: 40rpx;
    }

    .text_3 {
        line-height: 40rpx;
        font-size: 26rpx;
        text-align: center;
        width: 102rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
    }

    .font_2 {
        font-size: 26rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
        font-weight: 400;
        color: #2972fe;
    }

    .self-stretch {
        -webkit-align-self: stretch;
        -ms-flex-item-align: stretch;
        align-self: stretch;
        text-align: start;
    }
}