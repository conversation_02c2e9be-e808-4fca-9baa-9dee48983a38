import { useReady } from "@tarojs/taro";
import React, { useState, useRef } from "react";
import "./index.scss";
import "@utils/app.scss";
import { login } from "@utils/login";
import { Badge, Empty, SearchBar, Picker, Switch } from "@nutui/nutui-react-taro";
import Taro from "@tarojs/taro";
import request from "@service/request";
import { Image } from "@tarojs/components";
import TopNavBar from "@/components/topNavBar";
import Layout from "@/components/Layout";
import { tool } from "@utils/tool";
import StudentDetail from "@/components/StudentDetail";

interface MenuItem {
    id: string;
    title: string;
    customIcon: string;
}

const App = () => {
    const message: any = React.useRef();
    const studentDetailRef = useRef<any>();
    const [userInfo, setUserInfo] = React.useState<API.UserInfo>(Object);

    const [pageIndex, setPageIndex] = useState(1);
    const [pageSize] = useState(15);
    const [pageCount, setPageCount] = useState(0);

    const [searchKey, setSearchKey] = useState("");
    const [data, setData] = useState<any[]>([]);
    const [dataCount, setDataCount] = useState<any>(0);

    const [loading, setLoading] = useState(false);

    const [mySale, setMySale] = useState(false);
    const [myTeach, setMyTeach] = useState(false);

    // 添加StudentDetail打开状态
    const [isStudentDetailOpen, setIsStudentDetailOpen] = useState(false);

    // 菜单项状态
    const [menuItems] = useState<MenuItem[]>([
        { id: "", title: "全部学员", customIcon: "全" },
        { id: "1", title: "在培学员", customIcon: "培" },
        { id: "2", title: "毕业学员", customIcon: "毕" },
        { id: "3", title: "退学学员", customIcon: "退" },
        { id: "-1", title: "未注册学员", customIcon: "未" },
        { id: "101", title: "科一阶段", customIcon: "壹" },
        { id: "102", title: "科二阶段", customIcon: "贰" },
        { id: "103", title: "科三阶段", customIcon: "叁" },
        { id: "104", title: "科四阶段", customIcon: "肆" },
        { id: "203", title: "三个月内过期", customIcon: "过" },
        { id: "206", title: "六个月内过期", customIcon: "过" },
        { id: "200", title: "已经过期", customIcon: "过" },
    ]);

    const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItem>(menuItems[0]);
    const [pickerVisible, setPickerVisible] = useState(false);
    const [filterVisible, setFilterVisible] = useState(false);

    useReady(() => {
        login().then((info: API.UserInfo) => {
            setUserInfo(info);
            console.log(userInfo);
        });

        // 检查传入的 SearchKey 参数，如果存在就设置到 state
        const searchKeyParam = Taro.getCurrentInstance().router?.params.SearchKey;
        if (searchKeyParam) {
            setSearchKey(searchKeyParam);
        }

        // 检查传入的 SearchType 参数，如果存在就设置对应的菜单项
        const searchTypeParam = Taro.getCurrentInstance().router?.params.SearchType;
        if (searchTypeParam) {
            const targetMenuItem = menuItems.find((item) => item.id === searchTypeParam);
            if (targetMenuItem) {
                setSelectedMenuItem(targetMenuItem);
            }
        }

        // 读取保存的筛选条件
        const savedMySale = tool.data.get("longtime-student-list-my-sale");
        const savedMyTeach = tool.data.get("longtime-student-list-my-teach");

        if (savedMySale !== null) {
            setMySale(savedMySale);
        }
        if (savedMyTeach !== null) {
            setMyTeach(savedMyTeach);
        }

        setData([]);
        search(1, searchTypeParam, savedMySale && savedMyTeach ? "MyAll" : savedMySale ? "MySale" : savedMyTeach ? "MyTeach" : "");

        getViewHeight();
    });

    const [topHeight, setTopHeight] = useState(0);
    const getViewHeight = () => {
        // 创建一个选择器查询实例
        const query = Taro.createSelectorQuery();

        // 选择 id 为 'myView' 的元素
        query
            .select("#search-bar-top")
            .boundingClientRect((rect: any) => {
                if (rect) {
                    // rect.height 是元素的高度
                    console.log("View Height:", rect.height);
                    setTopHeight(rect.height);
                }
            })
            .exec();
    };

    const search = async (index: any, menuItemId?: string, myStudentType?: string) => {
        setPageIndex(index);
        setLoading(true);

        // 根据菜单项ID设置状态
        let statuss = [1, 2, 3];
        const currentMenuId = menuItemId || selectedMenuItem?.id || "";

        var postData: any = {
            SearchKey: searchKey,
            SearchType: currentMenuId,
            current: index,
            pageSize: pageSize,
            MyStudentType: myStudentType ? myStudentType : mySale && myTeach ? "MyAll" : mySale ? "MySale" : myTeach ? "MyTeach" : "",
            Statuss: statuss,
        };
        request.post<any>("/Jx/Student/Student/getStudentList", postData).then((json) => {
            setLoading(false);

            if (json && json.success) {
                if (index == 1) {
                    setData(json.data.data);
                    setDataCount(json.data.total);
                } else {
                    setData(data.concat(json.data.data));
                }
                setPageCount(json.data.pages);
            } else {
                message.current.openDialog("查询失败", json.message);
            }
        });
    };

    // 处理菜单项选择
    const handleMenuItemSelect = (item: MenuItem) => {
        console.log(item);
        if (!item) return;
        setSelectedMenuItem(item);
        setData([]);

        search(1, item.id);
        setPickerVisible(false);
    };

    // 处理StudentDetail打开
    const handleStudentDetailOpen = (studentId: string) => {
        setIsStudentDetailOpen(true);
        studentDetailRef.current?.open(studentId);
    };

    // 处理StudentDetail关闭
    const handleStudentDetailClose = () => {
        setIsStudentDetailOpen(false);
    };

    return (
        <Layout>
            <view className="student-list-page">
                <TopNavBar
                    title="学员搜索"
                    homeClick={() => Taro.redirectTo({ url: "/subpackages/student/user/index/index" })}
                    leftClick={() => Taro.redirectTo({ url: "/subpackages/student/user/student/list/menu/index" })}
                    bottomContent={
                        <view
                            id={"search-bar-top"}
                            style={{
                                position: "relative",
                                left: "0",
                                right: "0",
                                backgroundColor: "#fff",
                                width: "100%",
                                zIndex: 99999,
                                boxShadow: "0 4rpx 8rpx -2rpx rgba(0,0,0,0.1)",
                            }}
                        >
                            <view
                                className="flex-col search-bar"
                                style={{
                                    width: "100%",
                                    backgroundColor: "#fff",
                                    paddingLeft: "20rpx",
                                    paddingRight: "20rpx",
                                }}
                            >
                                <view
                                    className="flex-col flex-1 group_2"
                                    style={{
                                        paddingBottom: "20rpx",
                                        borderRadius: "0 24rpx 0",
                                    }}
                                >
                                    <view className="flex-row">
                                        <view
                                            style={{
                                                display: "flex",
                                                width: "100%",
                                                alignItems: "center",
                                                background: "#fff",
                                                marginTop: "10rpx",
                                            }}
                                        >
                                            <view
                                                className="flex-row justify-between items-center section"
                                                style={{
                                                    width: "100%",
                                                }}
                                            >
                                                <SearchBar
                                                    style={{
                                                        border: 0,
                                                        padding: 0,
                                                    }}
                                                    placeholder="请输入关键字词"
                                                    value={searchKey}
                                                    onSearch={(e) => {
                                                        setData([]);
                                                        setSearchKey(e);
                                                        search(1);
                                                    }}
                                                    onChange={(val: string) => setSearchKey(val)}
                                                ></SearchBar>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    }
                />
                <view
                    className="list flex-col safe__area"
                    id="primaryScroll"
                    style={{
                        overflowY: isStudentDetailOpen ? "hidden" : "auto", // 当StudentDetail打开时禁用滚动
                        width: "100%",
                        paddingTop: `${topHeight}px`, // 为固定的搜索栏留出空间
                        paddingBottom: "160rpx", // 为底部选择器留出空间
                    }}
                >
                    <>
                        {data.map((item, index) => {
                            return (
                                <view
                                    className="flex-col flex-1 group_2"
                                    onClick={() => {
                                        handleStudentDetailOpen(item.Id);
                                    }}
                                >
                                    <view
                                        className={
                                            item.NoPay > 0 ? "flex-row justify-between relative mt-12 list-item qianfei" : "flex-row justify-between relative mt-12 list-item"
                                        }
                                        style={{
                                            paddingRight: "40rpx",
                                        }}
                                    >
                                        <view className="flex-row self-center">
                                            <Badge style={{ marginInlineEnd: "5px" }} value={index + 1} max={999999}>
                                                <Image
                                                    src={`https://api.51panda.com/Jx/Image/StudentImage/getStudentZp?Id=${item.Id}&TenantId=${item.TenantId}`}
                                                    mode="widthFix"
                                                    style={{ width: "32px", height: "44.8px" }}
                                                />
                                            </Badge>
                                            <view className="flex-col shrink-0 group_5 ml-13">
                                                <text className="self-start font_3 ">{item.xm}</text>
                                                <text className="mt-6 self-stretch font_4">{item.Ywzt}</text>
                                            </view>
                                        </view>
                                        <view className="flex-col">
                                            <text className="self-start font text_7">{item.Age} 岁</text>
                                            <text
                                                className="self-end mt-12"
                                                style={{
                                                    fontWeight: "300",
                                                }}
                                            >
                                                {item.CarType}
                                            </text>
                                        </view>
                                    </view>
                                </view>
                            );
                        })}

                        {!loading && pageIndex < pageCount && (
                            <view
                                style={{ padding: "40rpx 0 30rpx", textAlign: "center", width: "100%", fontSize: "24rpx", color: "#2972fe" }}
                                onClick={() => {
                                    search(pageIndex + 1);
                                }}
                            >
                                <view className="flex-row justify-evenly items-center" style={{ color: "#2972fe" }}>
                                    <view className="section_4_bottom"></view>
                                    <view className="flex-col items-center">
                                        <text
                                            className="font_4 text_13_bottom"
                                            style={{
                                                color: "#2972fe",
                                            }}
                                        >
                                            点击查看更多
                                        </text>
                                        <text
                                            style={{
                                                color: "#999",
                                                fontSize: "20rpx",
                                                marginTop: "8rpx",
                                            }}
                                        >
                                            已显示 {data.length} 条，共 {dataCount} 条
                                        </text>
                                    </view>
                                    <view className="section_5_bottom"></view>
                                </view>
                            </view>
                        )}
                        {loading && (
                            <view style={{ padding: "40rpx 0 30rpx", textAlign: "center", width: "100%", fontSize: "24rpx" }}>
                                <view className="flex-row justify-evenly items-center">
                                    <view className="section_4_bottom"></view>
                                    <text className="font_4 text_13_bottom">正在加载</text>
                                    <view className="section_5_bottom"></view>
                                </view>
                            </view>
                        )}

                        {pageIndex >= pageCount && data.length > 0 && !loading && (
                            <view style={{ padding: "40rpx 0 30rpx", textAlign: "center", width: "100%", fontSize: "24rpx" }}>
                                <view className="flex-row justify-evenly items-center">
                                    <view className="section_4_bottom"></view>
                                    <text className="font_4 text_13_bottom">已经到底部</text>
                                    <view className="section_5_bottom"></view>
                                </view>
                            </view>
                        )}
                    </>
                    {!loading && data.length == 0 && <Empty description="无数据" style={{ margin: "0px" }} />}
                </view>

                {/* 底部菜单选择器 */}
                <view
                    className="bottom-menu-selector"
                    style={{
                        position: "fixed",
                        bottom: "0",
                        left: "0",
                        right: "0",
                        backgroundColor: "#fff",
                        borderTop: "1px solid #f0f0f0",
                        padding: " 10rpx 20rpx 40rpx 20rpx",
                        zIndex: 1000,
                        boxShadow: "0 -2px 8px rgba(0,0,0,0.1)",
                    }}
                >
                    <view
                        style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                            padding: "20rpx 30rpx",
                            // backgroundColor: "#f8f9fa",
                            borderRadius: "12rpx",
                            // border: "1px solid #e9ecef",
                        }}
                    >
                        {/* 左侧部分 */}
                        <view style={{ display: "flex", alignItems: "center" }} onClick={() => setPickerVisible(true)}>
                            <text
                                style={{
                                    fontSize: "32rpx",
                                    fontWeight: "bold",
                                    color: "#2972fe",
                                    marginRight: "20rpx",
                                }}
                            >
                                {selectedMenuItem?.customIcon || "全"}
                            </text>
                            <text style={{ fontSize: "32rpx", color: "#333" }}>{selectedMenuItem?.title || "全部学员"}</text>
                        </view>
                        {/* 右侧部分 */}
                        <view style={{ display: "flex", alignItems: "center", cursor: "pointer" }} onClick={() => setFilterVisible(true)}>
                            <text style={{ fontSize: "32rpx", color: "#333" }}>查询区间</text>
                        </view>
                    </view>
                </view>

                {/* 菜单选择器弹窗 */}
                <Picker
                    visible={pickerVisible}
                    onClose={() => setPickerVisible(false)}
                    onConfirm={(options, values) => {
                        const selectedId = values[0];
                        console.log(selectedId);
                        const selectedItem = menuItems.find((item) => item.id === selectedId);
                        if (selectedItem) {
                            handleMenuItemSelect(selectedItem);
                        }
                    }}
                    options={[menuItems.map((item) => ({ text: item.title, value: item.id }))]}
                    value={[
                        Math.max(
                            0,
                            menuItems.findIndex((item) => item.id === selectedMenuItem?.id)
                        ),
                    ]}
                />

                {/* 自定义筛选条件Modal */}
                {filterVisible && (
                    <view className="custom-modal">
                        {/* 遮罩层 */}
                        <view
                            className="custom-modal-mask"
                            onClick={() => setFilterVisible(false)}
                            style={{
                                position: "fixed",
                                top: "0",
                                left: "0",
                                right: "0",
                                bottom: "0",
                                backgroundColor: "rgba(0, 0, 0, 0.5)",
                                zIndex: "9999",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                            }}
                        >
                            {/* 弹窗内容 */}
                            <view
                                className="custom-modal-content"
                                onClick={(e) => e.stopPropagation()}
                                style={{
                                    width: "85%",
                                    maxWidth: "700rpx",
                                    backgroundColor: "#fff",
                                    borderRadius: "24rpx",
                                    padding: "0",
                                    boxShadow: "0 20rpx 60rpx rgba(0, 0, 0, 0.15)",
                                    overflow: "hidden",
                                }}
                            >
                                {/* 标题 */}
                                <view
                                    style={{
                                        padding: "30rpx 40rpx 30rpx 40rpx",
                                        borderBottom: "1px solid #eee",
                                        fontSize: "36rpx",
                                        // fontWeight: "600",
                                        textAlign: "center",
                                        color: "#333",
                                        backgroundColor: "#fafafa",
                                    }}
                                >
                                    筛选条件
                                </view>

                                {/* 内容区域 */}
                                <view
                                    style={{
                                        padding: "40rpx",
                                    }}
                                >
                                    <view style={{ marginBottom: "40rpx", display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                                        <text style={{ fontSize: "32rpx", color: "#333" }}>只查询自己招的人</text>
                                        <Switch
                                            checked={mySale}
                                            onChange={(checked) => {
                                                setMySale(checked);
                                                tool.data.set("longtime-student-list-my-sale", checked);
                                            }}
                                        />
                                    </view>

                                    <view style={{ marginBottom: "10rpx", display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                                        <text style={{ fontSize: "32rpx", color: "#333" }}>只查询自己带的人</text>
                                        <Switch
                                            checked={myTeach}
                                            onChange={(checked) => {
                                                setMyTeach(checked);
                                                tool.data.set("longtime-student-list-my-teach", checked);
                                            }}
                                        />
                                    </view>
                                </view>

                                {/* 按钮区域 */}
                                <view
                                    style={{
                                        display: "flex",
                                        borderTop: "1px solid #eee",
                                        height: "100rpx",
                                    }}
                                >
                                    <view
                                        className="modal-btn-cancel"
                                        style={{
                                            flex: "1",
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "center",
                                            fontSize: "32rpx",
                                            color: "#666",
                                            borderRight: "1px solid #eee",
                                            transition: "background-color 0.2s ease",
                                        }}
                                        onClick={() => setFilterVisible(false)}
                                    >
                                        取消
                                    </view>
                                    <view
                                        className="modal-btn-confirm"
                                        style={{
                                            flex: "1",
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "center",
                                            fontSize: "32rpx",
                                            color: "#2972fe",
                                            fontWeight: "600",
                                            transition: "background-color 0.2s ease",
                                        }}
                                        onClick={() => {
                                            setData([]);
                                            search(1);
                                            setFilterVisible(false);
                                        }}
                                    >
                                        确定
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                )}

                {/* 学员详情弹窗 */}
                <StudentDetail ref={studentDetailRef} onClose={handleStudentDetailClose} />
            </view>
        </Layout>
    );
};
export default App;
