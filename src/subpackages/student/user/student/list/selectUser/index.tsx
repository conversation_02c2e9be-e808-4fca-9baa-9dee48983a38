import { Input, View, Text } from "@tarojs/components";
import { useState, useEffect } from "react";
import "./index.scss";
import Layout from "@components/Layout";
import request from "@service/request";
import { message } from "@components/MessageApi/MessageApiSingleton";

interface UserInfo {
    UserId: string;
    RealName: string;
    Phone: string;
    IdCard: string;
    TenantName: string;
    Account: string;
}

const SelectUserPage = () => {
    const [searchValue, setSearchValue] = useState("");
    const [userList, setUserList] = useState<UserInfo[]>([]);
    const [loading, setLoading] = useState(false);

    const handleSearch = (e) => {
        setSearchValue(e.detail.value);
        searchUsers(e.detail.value);
    };

    const searchUsers = async (keyword: string) => {
        if (!keyword) {
            setUserList([]);
            return;
        }

        setLoading(true);
        try {
            const response = await request.post<API.Result<UserInfo[]>>("/Wx/User/search", {
                keyword: keyword,
            });

            if (response?.success) {
                setUserList(response.data || []);
            } else {
                message.error(response?.message || "搜索失败");
            }
        } catch (error) {
            console.error("搜索用户失败:", error);
            message.error("搜索失败，请稍后重试");
        } finally {
            setLoading(false);
        }
    };

    return (
        <Layout>
            <View className="search-container">
                <Input className="search-input" placeholder="输入推荐人姓名或手机号" value={searchValue} onInput={handleSearch} />
            </View>
            <View className="list-container">
                {loading ? (
                    <View className="loading">加载中...</View>
                ) : userList.length > 0 ? (
                    userList.map((user) => (
                        <View key={user.UserId} className="user-item">
                            <View className="user-info">
                                <Text className="user-name">{user.RealName}</Text>
                                <Text className="user-phone">电话: {user.Phone}</Text>
                                <Text className="user-idcard">证件号: {user.IdCard}</Text>
                                <Text className="user-tenant">所属单位: {user.TenantName}</Text>
                                <Text className="user-account">账号: {user.Account}</Text>
                            </View>
                        </View>
                    ))
                ) : searchValue ? (
                    <View className="empty">未找到相关用户</View>
                ) : null}
            </View>
        </Layout>
    );
};

export default SelectUserPage;
