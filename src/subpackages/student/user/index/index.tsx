import Taro, { useDidShow } from "@tarojs/taro";
import { useState, useEffect, useRef } from "react";
import request from "@service/request";
import upload from "@service/upload";
import { tool } from "@utils/tool";
import BottomNav from "./components/BottomNav";
import AvatarUploadDialog from "./components/AvatarUploadDialog";
import "./index.scss";
import Layout from "@components/Layout";
import { message } from "@components/MessageApi/MessageApiSingleton";
import UserCard from "./components/UserCard";
import StudentSection from "./components/StudentSection";
import ToolMenu from "./components/ToolMenu";
import { login } from "@utils/login";
import IconFont from "@/components/iconfont";
import UserListPopup from "@/components/UserListPopup";
import PhoneSelector, { PhoneSelectorRef } from "./components/PhoneSelector";
import { Close } from "@nutui/icons-react-taro";

import { checkOfficialAccountSubscription, redirectToFollowPage } from "@/utils/util";
const ProfilePage = () => {
    const [userInfo, setUserInfo] = useState<API.UserInfo>();
    const [userInfoLoading, setUserInfoLoading] = useState(true);
    const isFirstMount = useRef(true);
    const [uploadAvatarOpen, setUploadAvatarOpen] = useState(false);
    const [uploadAvatarLoading, setUploadAvatarLoading] = useState(false);
    const [tabIndex, setTabIndex] = useState(3);
    const [statusBarHeight, setStatusBarHeight] = useState(0);
    const phoneLoginRef = useRef<PhoneSelectorRef>(null);
    const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);

    const [menuList, setMenuList] = useState<any[]>([]);

    // Get status bar height when component mounts
    useEffect(() => {
        const systemInfo = Taro.getSystemInfoSync();
        setStatusBarHeight(systemInfo.statusBarHeight || 20);

        setUserInfoLoading(true);
        tool.data.clear();
        login().then(async () => {
            try {
                getMyMenu();
                const res = await request.post<any>("/Wx/Login/getUserInfo", {});
                if (res?.success) {
                    setUserInfo({ ...res.data, Avatar: "https://cdn.51panda.com/coach.png" });
                }
            } finally {
                setUserInfoLoading(false);
            }
        });

        // 标记已经不是第一次挂载
        isFirstMount.current = false;

        // 检查公众号订阅状态
        if (!checkOfficialAccountSubscription()) {
            setShowSubscriptionModal(true);
        }
    }, []);

    useDidShow(() => {
        // 移除这里的检查
    });

    const getMyMenu = async () => {
        const res = await request.post<any>("/Auth/MyMenu/getWxMenu", {});
        if (res?.success) {
            setMenuList(res.data);
        }
    };

    const handleAvatarUpload = async (e) => {
        setUploadAvatarLoading(true);
        try {
            const res = await upload.post<any>("/Wx/Login/uploadAvatar", {}, e.detail.avatarUrl, "file");
            if (res?.success) {
                setUserInfo((prev) => (prev ? { ...prev, Avatar: res.data } : undefined));
                setUploadAvatarOpen(false);
                message.openToast(res.message);
            } else {
                message.error(res.message, "上传失败");
            }
        } finally {
            setUploadAvatarLoading(false);
        }
    };

    const handleLogout = () => {
        message.openConfirm("是否确认退出系统?", "确认操作", async () => {
            message.openLoading("正在退出系统");
            const res = await request.post<any>("/Wx/Login/logOut", {});
            message.closeLoading();

            if (res?.success) {
                tool.data.clear();
                Taro.reLaunch({ url: "/subpackages/student/user/index/index" });
            } else {
                message.openToast(res.message);
            }
        });
    };

    const handleAccountSelect = async (userId: string) => {
        message.openLoading("切换账号中");
        try {
            const response = await request.put<API.Result<any>>("/Wx/Login/switchBinding", {
                userId: userId,
            });
            message.closeLoading();

            if (response?.success) {
                // tool.data.clear();
                // message.openLoading("正在刷新会话");

                Taro.reLaunch({
                    url: "/subpackages/student/user/index/index",
                });
            } else {
                message.openDialog("切换失败", response?.message);
            }
        } catch (error) {
            message.closeLoading();
            message.openDialog("切换失败", "请稍后重试");
        }
    };

    const [userList, setUserList] = useState<
        Array<{
            UserId: string;
            UserName: string;
            Phone: string;
            TenantName: string;
            Account: string;
        }>
    >([]);

    const [showUserListPopup, setShowUserListPopup] = useState(false);
    const handleAccountClick = async () => {
        console.log("userInfo", userInfo);
        message.openLoading("获取相关账号");
        try {
            const response = await request.post<API.Result<any[]>>("/Wx/Login/getUserList");
            if (response?.success) {
                setUserList(response.data);
                setShowUserListPopup(true);
            } else {
                message.error("获取关联账号失败");
            }
            message.closeLoading();
        } catch {
            message.closeLoading();
            message.error("获取关联账号失败");
        }
    };

    const handlePhoneLogin = () => {
        if (phoneLoginRef.current) {
            phoneLoginRef.current.showPhoneLogin();
        }
    };

    const handleCopyAccountName = () => {
        Taro.setClipboardData({
            data: "考场助手",
            success: () => {
                message.openToast("公众号名称已复制");
            },
        });
    };

    const handleGoToSettings = () => {
        console.log("handleGoToSettings");
        // redirectToFollowPage();
        setShowSubscriptionModal(false);
    };

    return (
        <Layout>
            <view
                className="profile-page"
                style={{
                    background: "#f0f2ff",
                    minHeight: "100vh",
                    position: "relative",
                }}
            >
                <AvatarUploadDialog
                    visible={uploadAvatarOpen}
                    loading={uploadAvatarLoading}
                    hasAvatar={!!userInfo?.Avatar}
                    onClose={() => setUploadAvatarOpen(false)}
                    onUpload={handleAvatarUpload}
                />

                {/* Header Background */}
                <view
                    style={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        right: 0,
                        height: "320px",
                        background: "linear-gradient(135deg, #2f54eb, #4377fd)",
                        borderBottomLeftRadius: "35px",
                        borderBottomRightRadius: "35px",
                        zIndex: 1,
                        overflow: "hidden",
                    }}
                >
                    {/* Decorative Elements */}
                    <view
                        style={{
                            position: "absolute",
                            top: "15px",
                            right: "25px",
                            width: "150px",
                            height: "150px",
                            borderRadius: "50%",
                            background: "rgba(255, 255, 255, 0.1)",
                        }}
                    />
                    <view
                        style={{
                            position: "absolute",
                            bottom: "60px",
                            left: "10px",
                            width: "100px",
                            height: "100px",
                            borderRadius: "50%",
                            background: "rgba(255, 255, 255, 0.05)",
                        }}
                    />
                    <view
                        style={{
                            position: "absolute",
                            top: "50%",
                            left: "50%",
                            width: "250px",
                            height: "250px",
                            borderRadius: "50%",
                            background: "radial-gradient(circle, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0) 70%)",
                            transform: "translate(-50%, -50%)",
                        }}
                    />
                    <view
                        style={{
                            position: "absolute",
                            width: "100%",
                            height: "100%",
                            backgroundImage: "radial-gradient(rgba(255, 255, 255, 0.12) 1px, transparent 1px)",
                            backgroundSize: "25px 25px",
                            opacity: 0.4,
                        }}
                    />
                </view>

                <view
                    className="profile-header"
                    style={{
                        paddingTop: `${30 + statusBarHeight}rpx`,
                        position: "relative",
                        zIndex: 2,
                        lineHeight: "50px",
                    }}
                    onClick={handleAccountClick}
                >
                    <view
                        className="tenant-info"
                        style={{
                            display: "flex",
                            alignItems: "center",
                            marginLeft: "40rpx",
                            height: "88rpx",
                            paddingTop: "5rpx",
                        }}
                    >
                        {userInfo?.UserId != undefined ? (
                            <>
                                <text
                                    className="tenant-name"
                                    style={{
                                        fontSize: "32rpx",
                                        // fontWeight: "bold",
                                        lineHeight: "normal",
                                        color: "#ffffff",
                                        marginRight: "10rpx",
                                        textShadow: "0 2px 4px rgba(0, 0, 0, 0.15)",
                                        letterSpacing: "2rpx",
                                    }}
                                >
                                    {userInfo?.TenantName}
                                </text>
                                <IconFont name="Arrow-Right2" size={18} color="#ffffff" style={{ marginTop: "6rpx" }} />
                            </>
                        ) : null}
                    </view>
                </view>
                <view
                    className="profile-content"
                    style={{
                        position: "relative",
                        zIndex: 2,
                        padding: "0 20px",
                    }}
                >
                    {/* User Card with special design to integrate with header */}
                    <view
                        className={`card user-card-wrapper ${isFirstMount.current ? "animated fadeInUp" : ""}`}
                        style={{
                            backgroundColor: "#ffffff",
                            borderRadius: "20px",
                            boxShadow: "0 8px 25px rgba(47, 84, 235, 0.12)",
                            marginTop: "20px",
                            marginBottom: "25px",
                            padding: "15px 15px 0px",
                            border: "1px solid rgba(255, 255, 255, 0.8)",
                        }}
                    >
                        <UserCard
                            userInfo={userInfo}
                            onAvatarClick={() => setUploadAvatarOpen(true)}
                            onAccountSelect={handleAccountSelect}
                            message={message}
                            onPhoneLogin={handlePhoneLogin}
                            loading={userInfoLoading}
                        />
                    </view>

                    <view
                        className={`card student-section-wrapper ${isFirstMount.current ? "animated fadeInUp" : ""}`}
                        style={{
                            backgroundColor: "#ffffff",
                            borderRadius: "20px",
                            boxShadow: "0 5px 20px rgba(47, 84, 235, 0.08)",
                            marginBottom: "25px",
                            padding: "0",
                            border: "1px solid rgba(255, 255, 255, 0.8)",
                        }}
                    >
                        <StudentSection userInfo={userInfo} />
                    </view>

                    <view
                        className={`card tool-menu-wrapper ${isFirstMount.current ? "animated fadeInUp" : ""}`}
                        style={{
                            backgroundColor: "#ffffff",
                            borderRadius: "20px",
                            boxShadow: "0 5px 20px rgba(47, 84, 235, 0.08)",
                            marginBottom: "40px",
                            padding: "0",
                            border: "1px solid rgba(255, 255, 255, 0.8)",
                        }}
                    >
                        <ToolMenu onLogout={handleLogout} menuList={menuList} />
                    </view>
                </view>

                {/* <view className="bottom-padding" /> */}
                <view className="safe-area" />

                {userInfo?.UserId != undefined && <BottomNav activeTab={tabIndex} onTabChange={setTabIndex} />}

                {showSubscriptionModal && (
                    <view className="custom-dialog-overlay">
                        <view className="custom-dialog">
                            <view className="custom-dialog-header">
                                <text className="custom-dialog-title">关注提醒</text>
                                <view className="custom-dialog-close" onClick={() => setShowSubscriptionModal(false)}>
                                    <Close size={16} />
                                </view>
                            </view>
                            <view className="custom-dialog-content">
                                <text className="custom-dialog-message">
                                    系统检测到你还没有关注公众号《考场助手》，或者关注了没有关联您的小程序，如果不关注或者不关联，您将无法收到学员的预约或者成绩信息推送。
                                </text>
                            </view>
                            <view className="custom-dialog-footer">
                                <view className="custom-dialog-btn cancel" onClick={handleCopyAccountName}>
                                    复制公众号名称
                                </view>
                                <view className="custom-dialog-btn confirm" onClick={handleGoToSettings}>
                                    跳转至公众号关联设置
                                </view>
                            </view>
                        </view>
                    </view>
                )}
            </view>
            <UserListPopup visible={showUserListPopup} userList={userList} onClose={() => setShowUserListPopup(false)} onSelect={handleAccountSelect} />

            <PhoneSelector ref={phoneLoginRef} message={message} />
        </Layout>
    );
};

export default ProfilePage;
