@use "sass:color";

// 主色调和辅助色
$primary: #4a6cf7;
$primary-light: rgba(74, 108, 247, 0.1);
$primary-lighter: rgba(74, 108, 247, 0.05);
$primary-dark: color.adjust($primary, $lightness: 10%);
$primary-darker: color.adjust($primary, $lightness: 20%);
$primary-gradient: linear-gradient(135deg, $primary, #6e3ff5);

// 文本颜色
$text-dark: #222;
$text-medium: #555;
$text-light: #888;
$text-white: #fff;

// 背景和边框颜色
$bg-light: #f9faff;
$border-light: #eaefff;
$divider: rgba(0, 0, 0, 0.06);

// 全局变量
$primary-color: #4a6cf7;
$primary-light: rgba(74, 108, 247, 0.1);
$text-primary: #333333;
$text-secondary: #666666;
$background-light: #f8f9fc;
$card-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);

page {
    font-family: BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Microsoft Yahei", sans-serif;
}

// 动画
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translate3d(0, 30px, 0);
        opacity: 0;
    }
    to {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@keyframes breathe {
    0% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.6;
    }
}

@keyframes shimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

// 全局样式
.user-profile {
    position: relative;
    min-height: 100vh;
    background-color: $bg-light;
    color: $text-dark;
    overflow-x: hidden;

    .hidden-component {
        position: absolute;
        left: -9999px;
        height: 0;
        width: 0;
        overflow: hidden;
    }

    // 顶部状态栏
    .status-bar {
        background-color: $primary;
        width: 100%;
    }

    // 头部区域
    .header {
        position: relative;
        padding: 0 0 30px;
        overflow: hidden;

        .header-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: $primary-gradient;
            z-index: 0;
            overflow: hidden;

            .bg-circle {
                position: absolute;
                border-radius: 50%;

                &-1 {
                    width: 220px;
                    height: 220px;
                    background: rgba(255, 255, 255, 0.1);
                    top: -80px;
                    right: -80px;
                }

                &-2 {
                    width: 140px;
                    height: 140px;
                    background: rgba(255, 255, 255, 0.05);
                    bottom: -50px;
                    left: -50px;
                }
            }

            .bg-dots {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-image: radial-gradient(rgba(255, 255, 255, 0.15) 1px, transparent 1px);
                background-size: 20px 20px;
                opacity: 0.5;
            }
        }

        .header-content {
            position: relative;
            z-index: 1;
            padding: 20px;
            display: flex;
            align-items: center;
            color: $text-white;

            .avatar-container {
                position: relative;
                margin-right: 16px;

                .avatar {
                    width: 80px;
                    height: 80px;
                    border-radius: 50%;
                    border: 4px solid rgba(255, 255, 255, 0.25);
                    background-color: rgba(255, 255, 255, 0.1);
                }

                .edit-badge {
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    width: 24px;
                    height: 24px;
                    background: $text-white;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);

                    .edit-icon {
                        font-size: 12px;
                        color: $primary;
                        line-height: 1;
                    }
                }
            }

            .user-info {
                flex: 1;

                .user-name {
                    font-size: 22px;
                    font-weight: 600;
                    margin-bottom: 4px;
                }

                .user-tenant {
                    font-size: 14px;
                    opacity: 0.9;
                }
            }
        }
    }

    // 内容区域
    .main-content {
        position: relative;
        margin-top: -20px;
        border-radius: 20px 20px 0 0;
        background-color: $bg-light;
        padding: 24px 16px;
        z-index: 2;
        box-shadow: 0 -8px 20px rgba(0, 0, 0, 0.05);
        animation: slideInUp 0.5s ease-out;

        // 快捷操作区域
        .quick-actions {
            display: flex;
            justify-content: space-between;
            margin-bottom: 24px;
            padding: 8px;

            .action-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 22%;

                .action-icon {
                    width: 52px;
                    height: 52px;
                    border-radius: 16px;
                    background: $primary-lighter;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-bottom: 8px;
                    position: relative;
                    transition: all 0.3s;

                    &::before {
                        content: "";
                        position: absolute;
                        width: 24px;
                        height: 24px;
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                        filter: brightness(0) saturate(100%) invert(33%) sepia(92%) saturate(1282%) hue-rotate(218deg) brightness(99%) contrast(94%);
                    }

                    &.icon-courses::before {
                        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M12 3L1 9l11 6l9-4.91V17h2V9M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82z'/%3E%3C/svg%3E");
                    }

                    &.icon-exams::before {
                        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M11 18h2v-2h-2v2zm1-16C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8s8 3.59 8 8s-3.59 8-8 8zm0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5c0-2.21-1.79-4-4-4z'/%3E%3C/svg%3E");
                    }

                    &.icon-cert::before {
                        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2.5 13.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5s1.5.67 1.5 1.5s-.67 1.5-1.5 1.5zM12 13.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5s1.5.67 1.5 1.5s-.67 1.5-1.5 1.5zM7.5 13.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5s1.5.67 1.5 1.5s-.67 1.5-1.5 1.5z'/%3E%3C/svg%3E");
                    }

                    &.icon-stats::before {
                        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z'/%3E%3C/svg%3E");
                    }

                    &:active {
                        transform: scale(0.95);
                        background: color.adjust($primary-lighter, $lightness: 5%);
                    }
                }

                .action-name {
                    font-size: 12px;
                    color: $text-medium;
                    text-align: center;
                }
            }
        }

        // 卡片通用样式
        .card {
            background-color: $text-white;
            border-radius: 16px;
            box-shadow: 0 8px 24px rgba(47, 84, 235, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;

            &:hover {
                transform: translateY(-5px);
                box-shadow: 0 12px 30px rgba(47, 84, 235, 0.15);
            }

            .card-header {
                padding: 16px 16px 0;

                .card-title {
                    font-size: 18px;
                    font-weight: 600;
                    color: $primary;
                    display: flex;
                    align-items: center;

                    &:before {
                        content: "";
                        display: inline-block;
                        width: 4px;
                        height: 18px;
                        background-color: $primary;
                        margin-right: 8px;
                        border-radius: 2px;
                    }
                }

                .card-subtitle {
                    font-size: 12px;
                    color: $text-light;
                    margin-top: 4px;
                    margin-left: 12px;
                }
            }
        }

        // 特定卡片样式
        .student-card {
            position: relative;

            &:after {
                content: "";
                position: absolute;
                top: 0;
                right: 0;
                width: 120px;
                height: 120px;
                background: radial-gradient(circle, $primary-lighter 0%, rgba(255, 255, 255, 0) 70%);
                opacity: 0.6;
                border-radius: 50%;
                z-index: 0;
            }
        }

        .tools-card {
            position: relative;

            &:before {
                content: "";
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100px;
                height: 100px;
                background: radial-gradient(circle, $primary-lighter 0%, rgba(255, 255, 255, 0) 70%);
                opacity: 0.4;
                border-radius: 50%;
                z-index: 0;
            }
        }
    }

    // 底部区域
    .bottom-padding {
        height: 76px;
    }

    .safe-area {
        height: constant(safe-area-inset-bottom);
        height: env(safe-area-inset-bottom);
    }
}

// 底部导航样式覆盖
.bottom-nav {
    background-color: white;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    border-top: 1px solid $border-light;
    z-index: 2;
    padding-bottom: 15rpx;

    &-item {
        transition: all 0.3s;

        &.active {
            color: $primary;
            font-weight: 500;
        }

        &:active {
            transform: translateY(-2px);
        }
    }
}

// 确保现有组件样式兼容
.section-title {
    color: $text-medium !important;
    font-weight: 500 !important;
    font-size: 16px !important;
    margin: 16px 0 12px !important;
    z-index: 1;
    position: relative;
}

.menu-item {
    position: relative;
    z-index: 1;
    // border-bottom: 1px solid $divider;
    padding: 14px 16px;
    transition: background-color 0.2s;

    &:last-child {
        border-bottom: none;
    }

    &-icon {
        color: $primary;
    }

    &-title {
        font-size: 15px;
        color: $text-medium;
        margin-left: 12px;
    }

    &:active {
        background-color: $primary-lighter;
    }
}

// 通用工具类
.flex-row {
    display: flex;
    flex-direction: row;
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.justify-between {
    justify-content: space-between;
}

.justify-center {
    justify-content: center;
}

.items-center {
    align-items: center;
}

.self-start {
    align-self: flex-start;
}

.self-end {
    align-self: flex-end;
}

.relative {
    position: relative;
}

// 间距工具类
.mt-4 {
    margin-top: 8rpx;
}

.mt-8 {
    margin-top: 16rpx;
}

.mt-10 {
    margin-top: 20rpx;
}

.mt-12 {
    margin-top: 24rpx;
}

.mt-16 {
    margin-top: 32rpx;
}

.mt-20 {
    margin-top: 40rpx;
}

.mt-24 {
    margin-top: 48rpx;
}

.ml-2 {
    margin-left: 4rpx;
}

.ml-4 {
    margin-left: 8rpx;
}

.ml-8 {
    margin-left: 16rpx;
}

.ml-12 {
    margin-left: 24rpx;
}

// 对话框样式覆盖
.nut-dialog {
    .nut-dialog__content {
        padding: 24rpx 32rpx;
    }

    .nut-dialog__footer {
        padding: 16rpx 32rpx 32rpx;
    }

    .nut-button {
        border-radius: 20rpx;
        height: 60rpx;
        font-size: 25rpx;
        font-weight: 400;
    }
}

// 消息样式覆盖
.nut-toast {
    font-family: sans-serif;
}

.profile-page {
    position: relative;
    min-height: 100vh;
    background-color: #f9faff;
    background-image: radial-gradient(rgba(74, 108, 247, 0.1) 1px, transparent 1px), radial-gradient(rgba(110, 63, 245, 0.1) 1px, transparent 1px);
    background-size: 30rpx 30rpx, 25rpx 25rpx;
    background-position: 0 0, 15rpx 15rpx;
    overflow-x: hidden;
    padding-bottom: 100rpx;

    .profile-header {
        position: relative;
        height: 240rpx;
        overflow: hidden;
        box-shadow: 0 8rpx 20rpx rgba(74, 108, 247, 0.2);
    }

    .profile-content {
        position: relative;
        z-index: 1;
        margin-top: -80rpx;
        padding: 0 30rpx;
        background-color: transparent;
    }

    .card {
        background-color: white;
        border-radius: 20rpx;
        box-shadow: $card-shadow;
        margin-bottom: 30rpx;
        padding: 30rpx;
        transition: transform 0.3s, box-shadow 0.3s;
        position: relative;
        overflow: hidden;

        &:before {
            content: "";
            position: absolute;
            inset: 0;
            border-radius: 20rpx;
            padding: 2rpx; /* 控制边框宽度 */
            background: linear-gradient(135deg, rgba(74, 108, 247, 0.5), rgba(110, 63, 245, 0.5));
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            opacity: 0.5;
        }

        &:after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
            pointer-events: none;
        }

        &:hover {
            transform: translateY(-5rpx);
            box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.1);
        }
    }

    .animated {
        animation-duration: 0.6s;
        animation-fill-mode: both;
    }

    .fadeInUp {
        animation-name: slideInUp;
    }

    .user-card-wrapper {
        animation-delay: 0.1s;
        background-color: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10rpx);
    }

    .student-section-wrapper {
        animation-delay: 0.2s;
    }

    .tool-menu-wrapper {
        animation-delay: 0.3s;
    }

    .decoration {
        position: absolute;
        z-index: 0;
        border-radius: 50%;
        opacity: 0.1;

        &-1 {
            top: 25%;
            left: -100rpx;
            width: 300rpx;
            height: 300rpx;
            background: linear-gradient(135deg, rgba(74, 108, 247, 0.8), rgba(110, 63, 245, 0.8));
            animation: breathe 8s infinite ease-in-out;
            filter: blur(30rpx);
        }

        &-2 {
            top: 45%;
            right: -80rpx;
            width: 250rpx;
            height: 250rpx;
            background: linear-gradient(45deg, rgba(74, 108, 247, 0.8), rgba(110, 63, 245, 0.8));
            animation: breathe 10s infinite ease-in-out;
            filter: blur(25rpx);
        }

        &-3 {
            bottom: 15%;
            left: 50%;
            transform: translateX(-50%);
            width: 200rpx;
            height: 200rpx;
            background: linear-gradient(225deg, rgba(74, 108, 247, 0.8), rgba(110, 63, 245, 0.8));
            animation: breathe 6s infinite ease-in-out;
            filter: blur(20rpx);
        }
    }

    .bottom-padding {
        height: 70rpx;
    }

    .safe-area {
        height: env(safe-area-inset-bottom);
    }
}

// 头像区域优化
.avatar-container .avatar {
    border: 3px solid $primary;
    box-shadow: 0 4px 12px rgba(47, 84, 235, 0.2);
}

// 按钮交互效果优化
.action-item .action-icon {
    background: $primary-gradient;
    color: $text-white;

    &:hover {
        transform: scale(1.1);
    }
}

// 背景装饰优化
.profile-page {
    background: linear-gradient(to bottom, $primary-light, $bg-light);
}

// Custom Dialog styles
.custom-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-dialog {
    background-color: #fff;
    border-radius: 12px;
    width: 85%;
    max-width: 650rpx;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.custom-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 30rpx;
    border-bottom: 1px solid #eee;
    position: relative;
}

.custom-dialog-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
}

.custom-dialog-close {
    position: absolute;
    right: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40rpx;
    height: 40rpx;
    cursor: pointer;
}

.custom-dialog-content {
    padding: 30rpx;
}

.custom-dialog-message {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
}

.custom-dialog-footer {
    padding: 0 30rpx 30rpx;
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.custom-dialog-btn {
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 32rpx;
    font-size: 28rpx;
    font-weight: 500;
    transition: all 0.3s ease;

    &.confirm {
        background-color: #2f54eb;
        color: #fff;
        &:active {
            opacity: 0.9;
        }
    }

    &.cancel {
        background-color: #fff;
        color: #666;
        border: 1px solid #d9d9d9;
        &:active {
            background-color: #f5f5f5;
        }
    }
}
