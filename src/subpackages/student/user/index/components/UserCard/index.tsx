import { Image, Button, View, Text } from "@tarojs/components";
import IconFont from "@components/iconfont";
import { useState, useEffect } from "react";
import "./index.scss";
import request from "@service/request";
import { MessageApiType } from "@components/MessageApi/MessageApiSingleton";
import UserListPopup from "@components/UserListPopup";
import Taro from "@tarojs/taro";

interface UserCardProps {
    userInfo?: API.UserInfo;
    onAvatarClick: () => void;
    onAccountSelect: (accountId: string) => void;
    message: MessageApiType;
    onPhoneLogin: () => void;
    loading?: boolean;
}

interface UserListItem {
    UserId: string;
    UserName: string;
    Phone: string;
    TenantName: string;
    Account: string;
    RealName?: string;
    Avatar?: string;
}

const UserCard = ({ userInfo, onAccountSelect, message, onPhoneLogin, loading = false }: UserCardProps) => {
    const [showUserListPopup, setShowUserListPopup] = useState(false);
    const [userList, setUserList] = useState<UserListItem[]>([]);
    const [isLoading, setLoading] = useState(false);

    // Fetch user list when popup is opened
    useEffect(() => {
        if (showUserListPopup) {
            fetchUserList();
        }
    }, [showUserListPopup]);

    const fetchUserList = async () => {
        try {
            setLoading(true);
            const response = await request.post<API.Result<UserListItem[]>>("/Wx/Login/getUserList");
            if (response && response.success && Array.isArray(response.data)) {
                setUserList(response.data);
            } else {
                // If we can't get real data, at least show current user
                setUserList([
                    {
                        UserId: userInfo?.UserSysId || "",
                        UserName: userInfo?.RealName || "",
                        Phone: "",
                        TenantName: userInfo?.TenantName || "",
                        Account: userInfo?.Account || "",
                        RealName: userInfo?.RealName || "",
                        Avatar: userInfo?.Avatar || "",
                    },
                ]);
            }
        } catch (error) {
            console.error("Failed to fetch user list:", error);
            message.error("获取用户列表失败");
        } finally {
            setLoading(false);
        }
    };

    // 处理账户选择
    const handleAccountSelect = (userId: string) => {
        onAccountSelect(userId);
        setShowUserListPopup(false);
    };

    return (
        <>
            <view className="user-card" style={{ position: "relative" }}>
                <view
                    className="user-card-content flex-row justify-between items-center"
                    style={{
                        position: "relative",
                        paddingTop: "5px",
                        paddingBottom: "0",
                        marginBottom: "-10px",
                    }}
                >
                    {loading ? (
                        // Loading state UI
                        <view className="flex-row items-center">
                            <view
                                style={{
                                    display: "flex",
                                    flexDirection: "row",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    width: "100%",
                                    padding: " 0 0 40rpx 0",
                                    marginTop: "-45rpx",
                                }}
                            >
                                <view
                                    className="loading-spinner"
                                    style={{
                                        width: "80rpx",
                                        height: "80rpx",
                                        borderRadius: "50%",
                                        border: "4rpx solid rgba(47, 84, 235, 0.2)",
                                        borderTopColor: "#2f54eb",
                                        animation: "spin 1s linear infinite",
                                        marginRight: "20rpx",
                                    }}
                                />
                                <text
                                    style={{
                                        fontSize: "28rpx",
                                        color: "#888",
                                    }}
                                >
                                    正在加载数据
                                </text>
                            </view>
                        </view>
                    ) : (
                        <view className="flex-row items-center">
                            {userInfo?.UserId ? (
                                // 已登录状态 - 显示头像
                                <view
                                    className="avatar-wrapper"
                                    style={{
                                        position: "relative",
                                        marginTop: "-35px",
                                    }}
                                >
                                    <view
                                        className="avatar-decoration"
                                        style={{
                                            position: "absolute",
                                            width: "180rpx",
                                            height: "180rpx",
                                            borderRadius: "50%",
                                            border: "4rpx solid rgba(255, 255, 255, 0.25)",
                                            background: "rgba(255, 255, 255, 0.1)",
                                            top: "-20rpx",
                                            left: "-20rpx",
                                            animation: "rotate 25s linear infinite",
                                            zIndex: 1,
                                        }}
                                    >
                                        <view
                                            style={{
                                                position: "absolute",
                                                width: "8rpx",
                                                height: "8rpx",
                                                borderRadius: "50%",
                                                background: "#ffffff",
                                                top: "10rpx",
                                                left: "50%",
                                                transform: "translateX(-50%)",
                                                opacity: 0.8,
                                            }}
                                        />
                                        <view
                                            style={{
                                                position: "absolute",
                                                width: "8rpx",
                                                height: "8rpx",
                                                borderRadius: "50%",
                                                background: "#ffffff",
                                                bottom: "10rpx",
                                                left: "50%",
                                                transform: "translateX(-50%)",
                                                opacity: 0.8,
                                            }}
                                        />
                                    </view>
                                    <Image
                                        className="avatar"
                                        src={userInfo.Avatar || "https://cdn.51panda.com/coach.png"}
                                        onError={(e: any) => {
                                            console.log("e", e);
                                            e.target.src = "https://cdn.51panda.com/coach.png";
                                        }}
                                        style={{
                                            borderRadius: "50%",
                                            width: "140rpx",
                                            height: "140rpx",
                                            border: "6rpx solid #ffffff",
                                            boxShadow: "0 10rpx 25rpx rgba(47, 84, 235, 0.3)",
                                            position: "relative",
                                            zIndex: 2,
                                        }}
                                    />
                                    <view
                                        className="avatar-edit-badge"
                                        style={{
                                            position: "absolute",
                                            right: "-8rpx",
                                            bottom: "5rpx",
                                            width: "50rpx",
                                            height: "50rpx",
                                            background: "#fff",
                                            borderRadius: "50%",
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "center",
                                            color: "white",
                                            boxShadow: "0 4rpx 10rpx rgba(0, 0, 0, 0.2)",
                                            zIndex: 3,
                                            border: "3rpx solid #ffffff",
                                        }}
                                    >
                                        <IconFont name="line-examine" size={18} color={"#2f54eb"} />
                                    </view>
                                </view>
                            ) : null}

                            {!userInfo?.UserId ? (
                                // 未登录状态 - 显示员工登录按钮
                                <view
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                        position: "relative",
                                        top: "-25px",
                                        height: "120rpx",
                                    }}
                                >
                                    {/* 添加未登录状态的头像 */}
                                    <Image
                                        className="avatar"
                                        src="https://cdn.51panda.com/coach.png"
                                        style={{
                                            borderRadius: "50%",
                                            width: "120rpx",
                                            height: "120rpx",
                                            border: "6rpx solid #ffffff",
                                            boxShadow: "0 10rpx 25rpx rgba(47, 84, 235, 0.3)",
                                            marginRight: "20rpx",
                                        }}
                                    />

                                    {/* 登录按钮，点击后打开手机号选择弹窗 */}
                                    <Button
                                        className="staff-login-button"
                                        onClick={onPhoneLogin}
                                        style={{
                                            background: "linear-gradient(135deg, #3366ff, #2f54eb)",
                                            borderRadius: "16rpx",
                                            boxShadow: "0 10rpx 20rpx rgba(47, 84, 235, 0.2)",
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "center",
                                            padding: "15rpx 30rpx",
                                            position: "relative",
                                            overflow: "hidden",
                                            width: "220rpx",
                                            height: "80rpx",
                                            // 重置Button默认样式
                                            border: "none",
                                            outline: "none",
                                            lineHeight: "normal",
                                            fontSize: "inherit",
                                            fontWeight: "normal",
                                        }}
                                    >
                                        <view
                                            style={{
                                                position: "absolute",
                                                width: "120rpx",
                                                height: "120rpx",
                                                borderRadius: "50%",
                                                background: "rgba(255, 255, 255, 0.1)",
                                                top: "-60rpx",
                                                right: "-30rpx",
                                            }}
                                        />
                                        <text
                                            style={{
                                                color: "#ffffff",
                                                fontSize: "32rpx",
                                                fontWeight: "bold",
                                            }}
                                        >
                                            员工登录
                                        </text>
                                    </Button>
                                </view>
                            ) : (
                                // 已登录状态 - 显示用户信息
                                <view
                                    className="info-container"
                                    style={{
                                        marginLeft: "30rpx",
                                        marginTop: "-35px",
                                        display: "flex",
                                        flexDirection: "column",
                                        justifyContent: "center",
                                    }}
                                >
                                    <view className="name-container">
                                        <text
                                            className="name"
                                            style={{
                                                fontSize: "38rpx",
                                                fontWeight: 600,
                                                color: "#333333",
                                                lineHeight: "50rpx",
                                                display: "block",
                                                marginBottom: "8rpx",
                                            }}
                                        >
                                            {userInfo.RealName}
                                        </text>
                                        <text
                                            className="user-id"
                                            style={{
                                                fontSize: "22rpx",
                                                color: "#2f54eb",
                                                fontWeight: "normal",
                                                backgroundColor: "rgba(47, 84, 235, 0.08)",
                                                padding: "4rpx 10rpx",
                                                borderRadius: "10rpx",
                                            }}
                                            onClick={() => {
                                                console.log("userInfo", userInfo);
                                            }}
                                        >
                                            ID: {userInfo.UserSysId}
                                        </text>
                                    </view>
                                </view>
                            )}
                        </view>
                    )}

                    {/* Switch identity button - always show */}
                    {!loading && (
                        <view
                            className="switch-identity-btn"
                            onClick={() => {
                                Taro.navigateTo({
                                    url: "/pages/role-selection/index",
                                });
                            }}
                            style={{
                                position: "absolute",
                                top: "-65rpx",
                                right: "-30rpx",
                                background: "rgba(47, 84, 235, 0.1)",
                                color: "#2f54eb",
                                fontSize: "24rpx",
                                padding: "8rpx 15rpx",
                                borderRadius: "30rpx",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                cursor: "pointer",
                                zIndex: 2,
                            }}
                        >
                            <IconFont name="line-user" size={24} style={{ marginRight: "12rpx", color: "#2f54eb" }} />
                            <text>切换到学员</text>
                        </view>
                    )}

                    {userInfo?.IsTenantAdmin && userInfo?.UserSysId && (
                        <view
                            className="admin-badge"
                            style={{
                                position: "absolute",
                                top: "55rpx",
                                right: "-30rpx",
                                background: "#2f54eb",
                                color: "white",
                                fontSize: "22rpx",
                                padding: "6rpx 15rpx",
                                borderRadius: "20rpx 0 20rpx 0",
                                boxShadow: "0 4rpx 10rpx rgba(47, 84, 235, 0.2)",
                            }}
                        >
                            <text>管理员</text>
                        </view>
                    )}
                </view>
            </view>

            {/* Render the UserListPopup for identity switching */}
            {showUserListPopup && <UserListPopup visible={showUserListPopup} onClose={() => setShowUserListPopup(false)} onSelect={handleAccountSelect} userList={userList} />}
        </>
    );
};

export default UserCard;
