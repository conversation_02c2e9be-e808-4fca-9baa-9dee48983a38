.user-card {
    font-family: BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Microsoft Yahei", sans-serif;
    padding: 30rpx 20rpx 20rpx;
    margin-top: 25rpx;

    .user-card-content {
        position: relative;
    }

    .avatar-wrapper {
        position: relative;
        margin: 10rpx;

        .avatar-decoration {
            position: absolute;
            width: 170rpx;
            height: 170rpx;
            border-radius: 50%;
            border: 2rpx dashed rgba(74, 108, 247, 0.3);
            top: -15rpx;
            left: -15rpx;
            animation: rotate 20s linear infinite;

            &:before,
            &:after {
                content: "";
                position: absolute;
                width: 12rpx;
                height: 12rpx;
                border-radius: 50%;
                background: #4a6cf7;
            }

            &:before {
                top: 10rpx;
                left: 50%;
                transform: translateX(-50%);
            }

            &:after {
                bottom: 10rpx;
                left: 50%;
                transform: translateX(-50%);
            }
        }

        .avatar {
            border-radius: 50%;
            width: 140rpx;
            height: 140rpx;
            border: 4rpx solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 10rpx 20rpx rgba(74, 108, 247, 0.25);
            transition: all 0.3s ease;

            &:active {
                transform: scale(0.96);
            }
        }

        .avatar-edit-badge {
            position: absolute;
            right: -5rpx;
            bottom: -5rpx;
            width: 44rpx;
            height: 44rpx;
            background: linear-gradient(135deg, #4a6cf7, #6e3ff5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 4rpx 10rpx rgba(74, 108, 247, 0.3);
            z-index: 1;

            &:active {
                transform: scale(0.9);
            }
        }
    }

    .info-container {
        width: 270rpx;
        margin-left: 30rpx;
    }

    .name-container {
        margin-bottom: 15rpx;
    }

    .name {
        font-size: 38rpx;
        font-weight: 600;
        color: #333333;
        line-height: 1.2;
        display: block;
        margin-bottom: 8rpx;
    }

    .user-id {
        font-size: 22rpx;
        color: #888888;
        font-weight: normal;
        background-color: #f5f7ff;
        padding: 4rpx 10rpx;
        border-radius: 10rpx;
    }

    .tenant-info {
        display: flex;
        align-items: center;
        background-color: #f8faff;
        padding: 12rpx 20rpx;
        border-radius: 30rpx;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
        transition: all 0.3s ease;

        &:active {
            background-color: #eef1ff;
            transform: translateX(5rpx);
        }
    }

    .tenant-name {
        font-size: 24rpx;
        line-height: 1;
        color: #666666;
        margin-right: 10rpx;
    }

    .admin-badge {
        position: absolute;
        top: 0;
        right: 0;
        background: linear-gradient(135deg, #4a6cf7, #6e3ff5);
        color: white;
        font-size: 22rpx;
        padding: 6rpx 15rpx;
        border-radius: 20rpx 0 20rpx 0;
        box-shadow: 0 4rpx 10rpx rgba(74, 108, 247, 0.2);
    }
}

.user-list-popup {
    .popup-title {
        font-size: 32rpx;
        color: #333;
        text-align: center;
        padding: 30rpx 0;
        font-weight: 500;
        border-bottom: 1rpx solid #f0f0f0;
        margin-bottom: 20rpx;
    }

    .nut-cell {
        margin-bottom: 20rpx;
        border-radius: 12rpx;
        background-color: #f8f8f8;
        transition: all 0.2s ease;

        &:active {
            background-color: #f0f0f0;
            transform: scale(0.98);
        }
    }
}

.company-account {
    font-size: 22rpx;
    color: #999;
    margin-top: 8rpx;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.loading-spinner {
    animation: spin 1s linear infinite;
}
