import Taro from "@tarojs/taro";
import IconFont from "@components/iconfont";
import "./index.scss";

interface ToolMenuProps {
    onLogout: () => void;
    menuList: any[];
}

const ToolMenu = ({ onLogout, menuList }: ToolMenuProps) => {
    const topMenuItems = [
        {
            icon: "purexam",
            text: "招生统计",
            url: "/subpackages/student/user/count/student/index",
        },
        {
            icon: "engexam",
            text: "考试统计",
            url: "/subpackages/student/user/count/exam/index",
        },
        {
            icon: "finexam",
            text: "财务统计",
            url: "/subpackages/student/user/count/pay/index",
        },
        {
            icon: "line-examine",
            text: "审核列表",
            url: "/subpackages/student/user/audit/menu/index",
        },
        {
            icon: "line-order",
            text: "预约训练",
            url: "/subpackages/student/user/study/order/index",
        },
        ...menuList,
        {
            icon: "line-signout",
            text: "退出系统",
            onClick: onLogout,
        },
    ];

    // Calculate placeholder items needed to ensure rows of 4
    const placeholdersNeeded = topMenuItems.length % 4 === 0 ? 0 : 4 - (topMenuItems.length % 4);
    const placeholders = Array(placeholdersNeeded).fill(0);

    const handleNavigate = (url: string) => {
        Taro.navigateTo({ url });
    };

    return (
        <view className="tool-menu">
            <text className="title">常用工具</text>
            <view className="top-menu">
                {topMenuItems.map((item, index) => (
                    <view key={index} className="menu-item" onClick={() => (item.onClick ? item.onClick() : item.url && handleNavigate(item.url))}>
                        <IconFont name={item.icon as any} size={50} />
                        <text className="text">{item.text}</text>
                    </view>
                ))}
                {placeholders.map((_, index) => (
                    <view key={`placeholder-${index}`} className="menu-item placeholder"></view>
                ))}
            </view>
        </view>
    );
};

export default ToolMenu;
