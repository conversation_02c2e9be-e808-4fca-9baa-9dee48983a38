.tool-menu {
    font-family: BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Microsoft Yahei", sans-serif;
    // margin-top: 20rpx;
    padding: 20rpx;

    .title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
        margin-bottom: 30rpx;
        position: relative;
        padding-left: 15rpx;

        &:before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 6rpx;
            height: 28rpx;
            background: linear-gradient(to bottom, #4a6cf7, #6e3ff5);
            border-radius: 3rpx;
        }
    }

    .top-menu {
        display: flex;
        flex-wrap: wrap;

        .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 25%;
            padding: 20rpx 0;
            border-radius: 15rpx;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;

            &:active {
                background-color: #f5f7ff;
                transform: scale(0.95);
            }

            &:before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: radial-gradient(circle at center, rgba(74, 108, 247, 0.05) 0%, transparent 70%);
                z-index: -1;
                opacity: 0;
                transition: opacity 0.3s;
            }

            &:active:before {
                opacity: 1;
            }

            .iconfont {
                color: #4a6cf7;
                background-color: rgba(74, 108, 247, 0.1);
                padding: 15rpx;
                border-radius: 15rpx;
                margin-bottom: 10rpx;
                box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
            }

            .text {
                margin-top: 15rpx;
                font-size: 26rpx;
                color: #555555;
                font-weight: 500;
            }

            &.placeholder {
                width: 25%;
                visibility: hidden;
            }
        }
    }

    .top-menu {
        margin-top: 10rpx;
    }

    .bottom-menu {
        padding-top: 10rpx;
        border-top: 1rpx solid #f0f0f0;

        .menu-item .iconfont {
            color: #ff6b6b;
            background-color: rgba(255, 107, 107, 0.1);
        }
    }
}
