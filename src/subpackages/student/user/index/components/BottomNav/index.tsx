import IconFont from "@components/iconfont";
import "./index.scss";
import Taro from "@tarojs/taro";

interface BottomNavProps {
    activeTab: number;
    onTabChange: (index: number) => void;
}

const BottomNav = ({ activeTab, onTabChange }: BottomNavProps) => {
    return (
        <view className="bottom-nav">
            <view className="nav-content">
                <view
                    className="nav-item"
                    onClick={() =>
                        Taro.navigateTo({
                            url: "/subpackages/student/user/student/add/index",
                        })
                    }
                >
                    <IconFont name="person-circle-plus" size={45} color={[activeTab === 2 ? "#ff4141" : "#999"]} />
                    <text className={activeTab === 2 ? "text active" : "text"}>报名</text>
                </view>
                <view className="nav-item" onClick={() => onTabChange(3)}>
                    <IconFont name="user-gear" size={45} color={[activeTab === 3 ? "#ff4141" : "#999"]} />
                    <text className={activeTab === 3 ? "text active" : "text"}>我的</text>
                </view>
            </view>
            <view className="safe-area" />
        </view>
    );
};

export default BottomNav;
