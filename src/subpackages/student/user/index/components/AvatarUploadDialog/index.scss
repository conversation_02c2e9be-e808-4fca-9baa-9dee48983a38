.dialog-content {
    width: 100%;
    text-align: center;
    padding: 20rpx 0;
    font-family: <PERSON><PERSON>;
}

.upload-button {
    border-radius: 20rpx;
    height: 60rpx;
    font-size: 25rpx;
    font-weight: 400;
}

.avatar-upload-dialog {
    padding: 24px 16px;
    text-align: center;

    .dialog-content {
        margin: 16px 0 24px;
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
    }

    .dialog-actions {
        display: flex;
        gap: 12px;

        .dialog-button {
            flex: 1;
            height: 70rpx;
            font-size: 28rpx;
            border-radius: 20rpx;
            border: none;
            font-weight: normal;

            &.cancel {
                background: #f5f5f5;
                color: #666;
            }

            &.confirm {
                background: #4971f7;
                color: #fff;
            }
        }
    }
}

:global {
    .nut-dialog {
        width: 280px !important;
        border-radius: 12px !important;
        overflow: hidden;

        &__header {
            padding: 20px 16px 0 !important;
            font-size: 30rpx !important;
            font-weight: 500 !important;
            color: #333;
            text-align: center;
            border: none !important;
        }

        &__content {
            margin: 0 !important;
            padding: 0 !important;
        }
    }
}

.custom-dialog-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

.custom-dialog {
    width: 580rpx;
    background: #fff;
    border-radius: 24rpx;
    overflow: hidden;

    &__header {
        padding: 32rpx;
        text-align: center;
        font-size: 26rpx;
        font-weight: normal;
        border-bottom: 1rpx solid #eee;
    }

    &__content {
        padding: 100rpx 32rpx;
        text-align: center;
        font-size: 28rpx;
        color: #333;
    }

    &__footer {
        display: flex;
        border-top: 1rpx solid #eee;

        .btn-cancel,
        .btn-confirm {
            flex: 1;
            padding: 24rpx 0;
            text-align: center;
            font-size: 28rpx;
        }

        .btn-cancel {
            color: #666;
            border-right: 1rpx solid #eee;
        }

        .btn-confirm {
            color: #3370ff;

            &.loading {
                opacity: 0.5;
            }
        }
    }
}

@keyframes dialogSlideIn {
    0% {
        opacity: 0;
        transform: translateY(30px) translateZ(0) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) translateZ(0) scale(1);
    }
}

@keyframes maskFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes ripple {
    to {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0;
    }
}
