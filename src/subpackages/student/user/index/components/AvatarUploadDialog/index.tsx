import { View, Text } from "@tarojs/components";
import Taro from "@tarojs/taro";
import "./index.scss";

interface AvatarUploadDialogProps {
    visible: boolean;
    loading?: boolean;
    hasAvatar?: boolean;
    onClose: () => void;
    onUpload?: (e: any) => Promise<void>;
    onConfirm?: () => void;
}

const AvatarUploadDialog = ({ visible, loading = false, hasAvatar = false, onClose, onUpload, onConfirm }: AvatarUploadDialogProps) => {
    if (!visible) return null;

    const handleConfirm = async () => {
        try {
            const res = await Taro.chooseMedia({
                count: 1,
                mediaType: ["image"],
                sourceType: ["album", "camera"],
                maxDuration: 30,
                camera: "back",
            });
            if (res.tempFiles.length > 0 && onUpload) {
                await onUpload(res);
            }
        } catch (error) {
            console.error("选择头像失败:", error);
        }
    };

    return (
        <View className="custom-dialog-mask">
            <View className="custom-dialog">
                {/* <View className="custom-dialog__header">
                    <Text>上传头像</Text>
                </View> */}
                <View className="custom-dialog__content">{hasAvatar ? <Text>是否重新上传头像？</Text> : <Text>您还未设置头像，建议上传真实头像</Text>}</View>
                <View className="custom-dialog__footer">
                    <View className="btn-cancel" onClick={onClose}>
                        取消
                    </View>
                    <View className={`btn-confirm ${loading ? "loading" : ""}`} onClick={handleConfirm}>
                        确认
                    </View>
                </View>
            </View>
        </View>
    );
};

export default AvatarUploadDialog;
