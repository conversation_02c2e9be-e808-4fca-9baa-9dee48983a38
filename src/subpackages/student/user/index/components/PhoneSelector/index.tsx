import { View, Text, Button, Input } from "@tarojs/components";
import { useState, useEffect, forwardRef, useImperativeHandle, useRef, useCallback, useMemo, memo } from "react";
import request from "@service/request";
import { MessageApiType } from "@components/MessageApi/MessageApiSingleton";
import Taro from "@tarojs/taro";
import "./index.scss";
import { Close } from "@nutui/icons-react-taro";

interface PhoneSelectorProps {
    message: MessageApiType;
}

export interface PhoneSelectorRef {
    showPhoneLogin: () => void;
}

// 优化键盘按键组件
const KeyboardKey = memo(({ value, onClick, className }: { value: string; onClick: () => void; className: string }) => (
    <View className={className} onClick={onClick}>
        {value === "delete" ? "删除" : value}
    </View>
));

const PhoneSelector = forwardRef<PhoneSelectorRef, PhoneSelectorProps>(({ message }, ref) => {
    const [showPhonePopup, setShowPhonePopup] = useState(false);
    const [phoneList, setPhoneList] = useState<string[]>([]);
    const [selectedPhoneId, setSelectedPhoneId] = useState<string | null>(null);
    const [loadingPhones, setLoadingPhones] = useState(false);
    const [bindingPhone, setBindingPhone] = useState(false);
    const [loadingUserList, setLoadingUserList] = useState(false);
    const [loadingConfirm, setLoadingConfirm] = useState(false);
    const [loadingLogin, setLoadingLogin] = useState(false);

    // 添加用户账号列表相关的状态
    const [userList, setUserList] = useState<any[]>([]);
    const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
    const [showUserListPopup, setShowUserListPopup] = useState(false);

    // 添加手机验证相关状态
    const [showBindPhonePopup, setShowBindPhonePopup] = useState(false);
    const [phone, setPhone] = useState("");
    const [verificationCode, setVerificationCode] = useState(["", "", "", ""]);
    const [isGettingCode, setIsGettingCode] = useState(false);
    const [countdown, setCountdown] = useState(0);
    const [isListeningInput, setIsListeningInput] = useState(false);
    const [showPhoneKeyboard, setShowPhoneKeyboard] = useState(false);

    // 使用 useRef 存储键盘数据
    const keyboardNumbers = useRef(["1", "2", "3", "4", "5", "6", "7", "8", "9", "", "0", "delete"]);

    // 验证手机号格式
    const isValidPhoneNumber = (phone: string) => {
        return /^1[3-9]\d{9}$/.test(phone);
    };

    // 绑定手机号
    const handleBindPhone = useCallback(
        async (codeArray = verificationCode) => {
            const fullCode = codeArray.join("");
            if (!phone || phone.length !== 11) {
                message.openToast("请输入正确的手机号");
                return;
            }

            if (fullCode.length !== 4) {
                message.openToast("请输入完整的验证码");
                return;
            }

            // 避免重复提交
            if (bindingPhone) {
                return;
            }

            setBindingPhone(true);
            try {
                const response = await request.put<API.Result<any>>("/Wx/Phone/bindPhoneWithCode", {
                    phone: phone,
                    validCode: fullCode,
                });

                if (response && response.success) {
                    // 获取手机号
                    if (phone) {
                        // 检查手机号是否已存在于列表中
                        const isPhoneExist = phoneList.includes(phone);

                        if (!isPhoneExist) {
                            // 将新手机号添加到列表
                            const updatedPhoneList = [...phoneList, phone];
                            setPhoneList(updatedPhoneList);
                            // 自动选择新添加的手机号
                            setSelectedPhoneId(phone);
                        } else {
                            // 如果手机号已存在，选中它
                            setSelectedPhoneId(phone);
                        }

                        setShowBindPhonePopup(false);
                        setShowPhonePopup(true);
                        message.success("手机号绑定成功");
                        // 重置状态
                        setPhone("");
                        setVerificationCode(["", "", "", ""]);
                    } else {
                        message.openToast("未获取到有效手机号");
                    }
                } else {
                    message.openToast(response?.message || "手机号验证失败");
                    // 验证失败时清空验证码
                    setVerificationCode(["", "", "", ""]);
                }
            } catch (error) {
                console.error("手机号验证失败:", error);
                message.error("手机号验证失败，请重试");
                // 验证失败时清空验证码
                setVerificationCode(["", "", "", ""]);
            } finally {
                setBindingPhone(false);
            }
        },
        [phone, verificationCode, bindingPhone, phoneList, message]
    );

    // 优化手机号输入处理
    const handlePhoneInput = useCallback(
        (value: string) => {
            if (value === "delete") {
                setPhone((prev) => prev.slice(0, -1));
                return;
            }

            // 如果已经输入了11位，不再接受输入
            if (phone.length >= 11) {
                return;
            }

            // 第一位必须是1
            if (phone.length === 0 && value !== "1") {
                return;
            }

            // 第二位必须是3-9
            if (phone.length === 1 && !["3", "4", "5", "6", "7", "8", "9"].includes(value)) {
                return;
            }

            setPhone((prev) => prev + value);
        },
        [phone.length]
    );

    // 优化验证码输入处理
    const handleNumericInput = useCallback(
        (value: string) => {
            if (value === "delete") {
                setVerificationCode((prev) => {
                    const lastFilledIndex = [...prev].reverse().findIndex((code) => code !== "");
                    if (lastFilledIndex === -1) return prev;

                    const actualIndex = 3 - lastFilledIndex;
                    const newCode = [...prev];
                    newCode[actualIndex] = "";
                    return newCode;
                });
                return;
            }

            setVerificationCode((prev) => {
                const currentIndex = prev.findIndex((code) => code === "");
                if (currentIndex === -1) return prev;

                const newCode = [...prev];
                newCode[currentIndex] = value;

                // 如果填满了验证码，自动触发绑定
                if (currentIndex === 3) {
                    setTimeout(() => handleBindPhone(newCode), 0);
                }

                return newCode;
            });
        },
        [handleBindPhone]
    );

    // 获取绑定的手机号列表
    const fetchPhoneList = async () => {
        try {
            setLoadingPhones(true);
            const response = await request.get<API.Result<string[]>>("/Wx/Phone/list");

            if (response && response.success && Array.isArray(response.data)) {
                setPhoneList(response.data);
                setSelectedPhoneId(response.data[0]);
            } else {
                setPhoneList([]);
            }
        } catch (error) {
            console.error("Failed to fetch phone list:", error);
            message.error("获取手机号列表失败");
        } finally {
            setLoadingPhones(false);
        }
    };

    // 获取手机号绑定的用户账号列表
    const fetchUserAccounts = async (phoneId: string) => {
        if (!phoneId) {
            message.error("请先选择一个手机号");
            return;
        }

        setLoadingUserList(true);
        try {
            const response = await request.post<API.Result<any[]>>("/Wx/Login/getUserList", {
                Phone: phoneId,
            });

            setLoadingUserList(false);
            if (response && response.success) {
                if (response.data && response.data.length > 0) {
                    // 设置用户列表并重置选中状态
                    setUserList(response.data);
                    setSelectedUserId(null);
                    setShowUserListPopup(true);
                    setShowPhonePopup(false); // 隐藏手机号选择弹窗
                } else {
                    message.openToast("未找到关联账号");
                }
            } else {
                message.openDialog("获取关联账号失败", response?.message || "请稍后重试");
            }
        } catch (error) {
            setLoadingUserList(false);
            console.error("获取关联账号失败:", error);
            message.openDialog("获取关联账号失败", "请稍后重试");
        }
    };

    // 使用已有手机号获取用户列表
    const handleLoginWithPhone = async () => {
        if (!selectedPhoneId) {
            message.error("请选择一个手机号");
            return;
        }

        // 设置加载状态
        setLoadingConfirm(true);

        try {
            // 获取手机号绑定的用户账号列表
            await fetchUserAccounts(selectedPhoneId);
        } finally {
            // 无论成功失败都重置加载状态
            setLoadingConfirm(false);
        }
    };

    // 确认登录选择的账号
    const handleConfirmUserLogin = async () => {
        if (!selectedUserId) {
            message.error("请选择一个账号");
            return;
        }

        setLoadingLogin(true);

        try {
            const response = await request.put<API.Result<any>>("/Wx/Login/loginByPhone", {
                userId: selectedUserId,
                phone: selectedPhoneId,
            });

            if (response && response.success) {
                message.success("登录成功");
                setShowUserListPopup(false);

                // 登录成功后刷新当前页面
                const currentPage = Taro.getCurrentPages();
                const currentPageRoute = currentPage[currentPage.length - 1].route;
                Taro.reLaunch({
                    url: `/${currentPageRoute}`,
                });
            } else {
                message.error(response?.message || "登录失败");
            }
        } catch (error) {
            console.error("登录失败:", error);
            message.error("登录失败，请重试");
        } finally {
            setLoadingLogin(false);
        }
    };

    // 关闭用户列表弹窗
    const handleCloseUserList = () => {
        setShowUserListPopup(false);
        setSelectedUserId(null);
    };

    // 处理获取手机号事件
    const handleGetPhoneNumber = async () => {
        // 显示手机号绑定弹窗
        setShowBindPhonePopup(true);
        setShowPhonePopup(false);
    };

    // 处理微信手机号快速验证
    const handleWxGetPhoneNumber = async (e: any) => {
        console.log("微信手机号快速验证结果:", e);

        if (e.detail.errMsg === "getPhoneNumber:ok") {
            // 用户同意授权
            const { code } = e.detail;

            if (code) {
                setBindingPhone(true);
                try {
                    const response = await request.put<API.Result<{ phone: string }>>(`/Wx/Phone/bindPhoneWithCode/${code}`, {});

                    if (response && response.success && response.data?.phone) {
                        const phone = response.data.phone;

                        // 检查手机号是否已存在于列表中
                        const isPhoneExist = phoneList.includes(phone);

                        if (!isPhoneExist) {
                            // 将新手机号添加到列表
                            const updatedPhoneList = [...phoneList, phone];
                            setPhoneList(updatedPhoneList);
                            // 自动选择新添加的手机号
                            setSelectedPhoneId(phone);
                        } else {
                            // 如果手机号已存在，选中它
                            setSelectedPhoneId(phone);
                        }

                        message.success("手机号绑定成功");
                    } else {
                        message.error(response?.message || "获取手机号失败");
                    }
                } catch (error) {
                    console.error("微信手机号绑定失败:", error);
                    message.error("手机号绑定失败，请重试");
                } finally {
                    setBindingPhone(false);
                }
            } else {
                message.error("未获取到授权码");
            }
        } else {
            // 用户拒绝授权或其他错误
            message.openToast("用户取消授权");
        }
    };

    // 优化键盘渲染
    const renderPhoneKeyboard = useMemo(
        () => (
            <View className="numeric-keyboard">
                {keyboardNumbers.current.map((num, index) => (
                    <KeyboardKey
                        key={index}
                        value={num}
                        className={`keyboard-key ${num === "" ? "empty" : ""} ${num === "delete" ? "delete-key" : ""}`}
                        onClick={() => num !== "" && handlePhoneInput(num)}
                    />
                ))}
            </View>
        ),
        [handlePhoneInput]
    );

    // 优化验证码键盘渲染
    const renderNumericKeyboard = useMemo(
        () => (
            <View className="numeric-keyboard">
                {keyboardNumbers.current.map((num, index) => (
                    <KeyboardKey
                        key={index}
                        value={num}
                        className={`keyboard-key ${num === "" ? "empty" : ""} ${num === "delete" ? "delete-key" : ""}`}
                        onClick={() => num !== "" && handleNumericInput(num)}
                    />
                ))}
            </View>
        ),
        [handleNumericInput]
    );

    // 优化验证码显示组件
    const VerificationCodeDisplay = memo(({ codes }: { codes: string[] }) => (
        <View className="verification-code-container">
            {codes.map((code, index) => (
                <View key={index} className="code-input">
                    {code}
                </View>
            ))}
        </View>
    ));

    // 发送验证码
    const handleSendVerificationCode = async () => {
        if (!phone || phone.length !== 11) {
            message.error("请输入正确的手机号");
            return;
        }

        Taro.login({
            success: async (res) => {
                if (res.code) {
                    setIsGettingCode(true);
                    try {
                        const response = await request.put<API.Result<any>>(`/Wx/Phone/sendVerificationCode/${res.code}`, {
                            phone: phone,
                        });

                        if (response && response.success) {
                            message.success(response?.message || "验证码已发送");
                            // 设置倒计时60秒
                            setCountdown(60);
                            // 开始监听输入
                            setIsListeningInput(true);
                            // 重置验证码
                            setVerificationCode(["", "", "", ""]);
                            // 隐藏手机号键盘
                            setShowPhoneKeyboard(false);
                            const timer = setInterval(() => {
                                setCountdown((prev) => {
                                    if (prev <= 1) {
                                        clearInterval(timer);
                                        setIsListeningInput(false);
                                        return 0;
                                    }
                                    return prev - 1;
                                });
                            }, 1000);
                        } else {
                            message.error(response?.message || "发送验证码失败");
                        }
                    } catch (error) {
                        console.error("发送验证码失败:", error);
                        message.error("发送验证码失败，请重试");
                    } finally {
                        setIsGettingCode(false);
                    }
                }
            },
        });
    };

    // 关闭手机号绑定弹窗
    const handleCloseBindPhone = () => {
        setShowBindPhonePopup(false);
        setShowPhonePopup(true);
        // 重置状态
        setPhone("");
        setVerificationCode(["", "", "", ""]);
        setIsListeningInput(false);
        setShowPhoneKeyboard(false);
    };

    // 打开手机号选择弹窗
    const openPhonePopup = async () => {
        try {
            setLoadingPhones(true);
            const response = await request.get<API.Result<string[]>>("/Wx/Phone/list");

            if (response && response.success && Array.isArray(response.data)) {
                setPhoneList(response.data);
                if (response.data.length > 0) {
                    // 如果有手机号列表，显示选择手机号弹窗
                    setSelectedPhoneId(response.data[0]);
                    setShowPhonePopup(true);
                } else {
                    // 如果没有手机号列表，直接显示绑定手机号弹窗
                    handleGetPhoneNumber();
                }
            } else {
                // 如果获取失败，直接显示绑定手机号弹窗
                handleGetPhoneNumber();
            }
        } catch (error) {
            console.error("Failed to fetch phone list:", error);
            message.error("获取手机号列表失败");
            // 发生错误时，直接显示绑定手机号弹窗
            handleGetPhoneNumber();
        } finally {
            setLoadingPhones(false);
        }
    };

    // 公开的手机号登录弹窗方法
    const showPhoneLogin = () => {
        openPhonePopup();
    };

    // 通过 ref 暴露方法给父组件
    useImperativeHandle(ref, () => ({
        showPhoneLogin,
    }));

    // Handle overlay click (close modal)
    const handleOverlayClick = () => {
        setShowPhonePopup(false);
        setShowUserListPopup(false);
    };

    // Prevent clicks inside the modal from closing it
    const handleModalClick = (e) => {
        e.stopPropagation();
    };

    return (
        <>
            {/* 手机号验证绑定弹窗 */}
            {showBindPhonePopup && (
                <View className="custom-modal-overlay" onClick={handleOverlayClick}>
                    <View className="custom-modal-container" onClick={handleModalClick}>
                        {/* Modal Header */}
                        <View className="custom-modal-header">
                            <Text className="custom-modal-title">绑定手机号</Text>
                            <View className="custom-modal-close" onClick={handleCloseBindPhone}>
                                <Close />
                            </View>
                        </View>

                        {/* Modal Content */}
                        <View className="phone-bind-container">
                            {/* 手机号输入 */}
                            <View className="phone-input-wrapper">
                                <View className="phone-input-container">
                                    <View className="phone-input" onClick={() => setShowPhoneKeyboard(true)}>
                                        {phone || "请输入手机号码"}
                                    </View>
                                    <Button
                                        className={`send-code-button ${countdown > 0 || !isValidPhoneNumber(phone) || isGettingCode ? "disabled" : ""}`}
                                        onClick={handleSendVerificationCode}
                                        disabled={countdown > 0 || isGettingCode || !isValidPhoneNumber(phone)}
                                    >
                                        {countdown > 0 ? `${countdown}s` : isGettingCode ? "发送中" : "获取验证码"}
                                    </Button>
                                </View>
                            </View>

                            {/* 验证码输入 - 只在倒计时开始后显示 */}
                            {countdown > 0 && (
                                <View className="verification-code-wrapper">
                                    <Text className="input-label">验证码</Text>
                                    <VerificationCodeDisplay codes={verificationCode} />
                                    {renderNumericKeyboard}
                                </View>
                            )}

                            {/* 手机号键盘 */}
                            {showPhoneKeyboard && renderPhoneKeyboard}

                            {/* 绑定状态指示 */}
                            {bindingPhone && (
                                <View className="binding-status">
                                    <Text>验证中...</Text>
                                </View>
                            )}
                        </View>
                    </View>
                </View>
            )}

            {showPhonePopup && (
                <View className="custom-modal-overlay" onClick={handleOverlayClick}>
                    <View className="custom-modal-container" onClick={handleModalClick}>
                        {/* Modal Header */}
                        <View className="custom-modal-header">
                            <Text className="custom-modal-title">选择手机号</Text>
                            <View className="custom-modal-close" onClick={() => setShowPhonePopup(false)}>
                                <Close />
                            </View>
                        </View>

                        {/* Modal Content */}
                        <View className="phone-list-container">
                            <View className="phone-list">
                                {loadingPhones ? (
                                    <View style={{ textAlign: "center", padding: "30rpx" }}>加载中...</View>
                                ) : phoneList.length > 0 ? (
                                    phoneList.map((phone) => (
                                        <View key={phone} className={`phone-item ${selectedPhoneId === phone ? "selected" : ""}`} onClick={() => setSelectedPhoneId(phone)}>
                                            <Text>{phone}</Text>
                                        </View>
                                    ))
                                ) : (
                                    <View style={{ textAlign: "center", padding: "30rpx", color: "#999" }}>没有已绑定的手机号</View>
                                )}
                            </View>

                            {/* Modal Footer */}
                            <View className="phone-popup-buttons">
                                {/* 绑定新号码按钮 - 原始版本（已注释）
                                <Button onClick={handleGetPhoneNumber} className={`bind-button ${bindingPhone ? "loading" : ""}`} disabled={bindingPhone}>
                                    {bindingPhone ? "绑定中..." : "绑定其他号码"}
                                </Button>
                                */}

                                {/* 使用微信手机号快速验证组件 */}
                                <Button
                                    openType="getPhoneNumber"
                                    onGetPhoneNumber={handleWxGetPhoneNumber}
                                    className={`bind-button ${bindingPhone ? "loading" : ""}`}
                                    disabled={bindingPhone}
                                >
                                    {bindingPhone ? "绑定中..." : "绑定其他号码"}
                                </Button>

                                {/* 确认按钮 */}
                                <Button
                                    onClick={handleLoginWithPhone}
                                    disabled={phoneList.length === 0 || !selectedPhoneId || loadingConfirm}
                                    className={`confirm-button ${phoneList.length === 0 || !selectedPhoneId || loadingConfirm ? "disabled" : ""}`}
                                >
                                    {loadingConfirm ? "加载中..." : "确认"}
                                </Button>
                            </View>
                        </View>
                    </View>
                </View>
            )}

            {/* 用户账号列表弹窗 */}
            {showUserListPopup && (
                <View className="custom-modal-overlay" onClick={handleOverlayClick}>
                    <View className="custom-modal-container" onClick={handleModalClick}>
                        {/* Modal Header */}
                        <View className="custom-modal-header">
                            <Text className="custom-modal-title">选择登录账号</Text>
                            <Close className="close-btn" onClick={() => handleCloseUserList()} size={20} color="#666" />
                        </View>

                        {/* Modal Content */}
                        <View className="phone-list-container">
                            <View className="phone-list">
                                <Text className="account-title">手机号 {selectedPhoneId} 关联的账号列表</Text>

                                {loadingUserList ? (
                                    <View style={{ textAlign: "center", padding: "30rpx" }}>加载中...</View>
                                ) : userList.length > 0 ? (
                                    userList.map((user) => (
                                        <View
                                            key={user.UserId}
                                            className={`user-item ${selectedUserId === user.UserId ? "selected" : ""}`}
                                            onClick={() => setSelectedUserId(user.UserId)}
                                        >
                                            <View className="user-info">
                                                <Text className="user-name">{user.RealName || "未命名用户"}</Text>
                                                <Text className="user-account">账号: {user.Account}</Text>
                                            </View>
                                            <View className="tenant-container">
                                                <Text className="user-tenant">{user.TenantName}</Text>
                                            </View>
                                        </View>
                                    ))
                                ) : (
                                    <View style={{ textAlign: "center", padding: "30rpx", color: "#999" }}>没有关联账号</View>
                                )}
                            </View>

                            {/* Modal Footer */}
                            <View className="phone-popup-buttons">
                                {/* 关闭按钮 */}
                                <Button onClick={handleCloseUserList} className="cancel-button">
                                    关闭
                                </Button>

                                {/* 确认按钮 */}
                                <Button
                                    onClick={handleConfirmUserLogin}
                                    disabled={!selectedUserId || loadingLogin}
                                    className={`confirm-button ${!selectedUserId || loadingLogin ? "disabled" : ""}`}
                                >
                                    {loadingLogin ? "登录中..." : "确认登录"}
                                </Button>
                            </View>
                        </View>
                    </View>
                </View>
            )}
        </>
    );
});

export default memo(PhoneSelector);
