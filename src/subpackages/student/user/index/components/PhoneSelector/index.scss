.phone-item {
    transition: all 0.2s ease;

    &.selected {
        transform: scale(1.02);
    }

    &:active {
        opacity: 0.8;
    }
}

// Override NutUI Dialog styling to ensure it appears above everything
:global {
    .nut-dialog {
        z-index: 9999 !important;
    }

    .nut-overlay {
        z-index: 9998 !important;
    }

    .nut-dialog__content {
        // 移除高度限制，避免滚动条
    }
}

// Modal overlay
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

// Modal container
.custom-modal-container {
    background-color: #fff;
    border-radius: 12px;
    width: 85%;
    max-width: 650rpx;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    // z-index: 1;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

// Modal header
.custom-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 30rpx;
    border-bottom: 1px solid #eee;
}

.custom-modal-title {
    font-size: 32rpx;
    // font-weight: 500;
    color: #333;
}

.custom-modal-close {
    font-size: 40rpx;
    color: #999;
    cursor: pointer;
    padding: 10rpx;
}

// Phone list container
.phone-list-container {
    display: flex;
    flex-direction: column;
}

.phone-list {
    padding: 20rpx;
    max-height: 60vh;
    overflow: auto;
}

.phone-item {
    padding: 20rpx;
    border-radius: 8rpx;
    margin-bottom: 16rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f5f5f5;
    border: 1px solid transparent;

    &.selected {
        background-color: rgba(47, 84, 235, 0.1);
        border: 1px solid #2f54eb;
    }
}

// Buttons container
.phone-popup-buttons {
    display: flex;
    justify-content: space-between;
    padding: 20rpx;
    margin-top: 20rpx;
    border-top: 1px solid #eee;
}

.bind-button {
    flex: 1;
    margin-right: 20rpx;
    background-color: #fff !important;
    color: #2f54eb !important;
    border: 1px solid #2f54eb !important;
    border-radius: 8rpx !important;
    padding: 0rpx 0 !important;
    text-align: center !important;
    font-size: 30rpx !important;
    transition: all 0.3s ease;

    &.loading {
        opacity: 0.7;
        background-color: #f0f0f0 !important;
        color: #999 !important;
        border-color: #ccc !important;
    }

    &:disabled {
        cursor: not-allowed;
    }
}

.confirm-button {
    flex: 1;
    background-color: #2f54eb !important;
    color: #fff !important;
    border-radius: 8rpx !important;
    padding: 20rpx 0 !important;
    text-align: center !important;
    font-size: 30rpx !important;

    &.disabled {
        opacity: 0.5;
    }
}

.cancel-button {
    flex: 1;
    margin-right: 20rpx;
    background-color: #f0f0f0 !important;
    color: #666 !important;
    border: 1px solid #ddd !important;
    border-radius: 8rpx !important;
    padding: 20rpx 0 !important;
    text-align: center !important;
    font-size: 30rpx !important;
}

// 用户账号列表相关样式
.account-title {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
    padding: 0 10rpx;
}

.user-item {
    padding: 24rpx;
    border-radius: 8rpx;
    margin-bottom: 16rpx;
    background-color: #f5f5f5;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16rpx;

    &.selected {
        background-color: rgba(47, 84, 235, 0.1);
        border: 1px solid #2f54eb;
        transform: scale(1.02);
    }

    &:active {
        opacity: 0.8;
    }
}

.user-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
}

.tenant-container {
    display: flex;
    align-items: center;
    max-width: 40%;
    justify-content: flex-end;
    padding-left: 10rpx;
    overflow: hidden;
}

.user-name {
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.user-tenant {
    font-size: 26rpx;
    color: #333;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-word;
}

.user-account {
    font-size: 24rpx;
    color: #666;
    margin-top: 4rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.user-detail {
    font-size: 26rpx;
    color: #666;
    margin-top: 6rpx;
}

// 手机号验证绑定相关样式
.phone-bind-container {
    padding: 30rpx;
    display: flex;
    flex-direction: column;
}

.phone-input-wrapper,
.verification-code-wrapper {
    margin-bottom: 40rpx;
}

.input-label {
    display: block;
    font-size: 28rpx;
    color: #333;
    margin-bottom: 20rpx;
    font-weight: 500;
}

.phone-input-container {
    display: flex;
    align-items: center;
    gap: 20rpx;
}

.phone-input {
    flex: 1;
    height: 88rpx;
    background-color: #f5f5f5;
    border-radius: 8rpx;
    padding: 0 20rpx;
    font-size: 30rpx;
    border: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    color: #333;

    &:empty::before {
        content: "请输入手机号码";
        color: #999;
    }
}

.send-code-button {
    width: 200rpx;
    height: 88rpx;
    background-color: #2f54eb !important;
    color: #fff !important;
    border-radius: 8rpx !important;
    padding: 0 !important;
    font-size: 28rpx !important;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    transition: all 0.3s ease;

    &.disabled {
        background-color: #f5f5f5 !important;
        color: #999 !important;
        cursor: not-allowed;
        pointer-events: none;
    }

    &:active {
        opacity: 0.8;
    }
}

@keyframes button-loading-spinner {
    from {
        transform: translate(-50%, -50%) rotate(0turn);
    }
    to {
        transform: translate(-50%, -50%) rotate(1turn);
    }
}

.verification-code-container {
    display: flex;
    gap: 20rpx;
    justify-content: space-between;
}

.code-input {
    flex: 1;
    height: 100rpx;
    width: 100rpx;
    background-color: #f5f5f5;
    border-radius: 8rpx;
    text-align: center;
    font-size: 36rpx;
    font-weight: 600;
    border: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: center;
}

// Add style for binding status
.binding-status {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30rpx;
    padding: 20rpx;

    text {
        color: #2f54eb;
        font-size: 30rpx;
    }
}

.bind-phone-button {
    width: 100%;
    height: 88rpx;
    margin-top: 20rpx;
    background-color: #2f54eb !important;
    color: #fff !important;
    border-radius: 8rpx !important;
    font-size: 30rpx !important;
    display: flex;
    align-items: center;
    justify-content: center;

    &.loading {
        opacity: 0.7;
    }

    &:disabled {
        background-color: #ccc !important;
    }
}

.numeric-keyboard {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rpx;
    background-color: #f5f5f5;
    padding: 20rpx;
    border-radius: 24rpx 24rpx 0 0;
    box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
    z-index: 1000;

    .keyboard-key {
        background-color: #fff;
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 48rpx;
        border-radius: 8rpx;
        user-select: none;
        cursor: pointer;
        transition: background-color 0.2s;
        margin: 4rpx;

        &:active {
            background-color: #e0e0e0;
        }

        &.empty {
            visibility: hidden;
        }

        &.delete-key {
            font-size: 32rpx;
            color: #666;
        }
    }
}
