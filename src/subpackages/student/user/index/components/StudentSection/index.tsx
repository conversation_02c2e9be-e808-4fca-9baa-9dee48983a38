import Taro from "@tarojs/taro";
import { Image, View, Text } from "@tarojs/components";
import "./index.scss";
import { message } from "@components/MessageApi/MessageApiSingleton";

// 添加接口定义
interface StudentSectionProps {
    userInfo?: API.UserInfo;
}

// 修改组件接受UserInfo参数
const StudentSection: React.FC<StudentSectionProps> = ({ userInfo }) => {
    const handleNavigate = (url: string) => {
        console.log(url);
        console.log("userInfo", userInfo);
        // 检查用户是否登录
        if (!userInfo?.UserId) {
            message.error("您需要先登录才能使用此功能", "登录失败");
            return;
        }
        // 用户已登录，正常导航
        Taro.navigateTo({ url });
    };

    const menuItems = [
        {
            text: "学员",
            icon: "https://cdn.51panda.com/wx/tab/d7a1d61419f4d29eb5c21af7c257e7ba.png",
            url: "/subpackages/student/user/student/list/menu/index",
        },
        {
            text: "上车",
            icon: "https://cdn.51panda.com/wx/tab/de53c1c76bdda511d9be823e3aed8e4d.png",
            url: "/subpackages/student/user/study/scan/index",
        },
        {
            text: "考试",
            icon: "https://cdn.51panda.com/wx/tab/c76785689f737d578ac1d374e578f35c.png",
            url: "/subpackages/student/user/exam/list/index",
            hasIndicator: true,
        },
        {
            text: "评价",
            icon: "https://cdn.51panda.com/wx/tab/f46d9ec0278030dcf29c1a0d0879e37e.png",
        },
        {
            text: "查询",
            icon: "https://cdn.51panda.com/wx/tab/d7a1d61419f4d29eb5c21af7c257e7ba.png",
            url: "/subpackages/student/user/student/face/index",
        },
    ];

    // 修改: 直接处理全部学员点击
    const handleViewAllClick = () => {
        console.log("点击了全部学员");
        handleNavigate("/subpackages/student/user/student/list/index");
    };

    return (
        <View className="student-section">
            <View className="header" style={{ cursor: "pointer" }}>
                <Text className="title">我的学员</Text>
                {/* <View
                    className="view-all"
                    onClick={(e) => {
                        e.stopPropagation(); // 阻止事件冒泡
                        handleViewAllClick();
                    }}
                    style={{ position: "relative", zIndex: 2 }}
                >
                    <Text>全部学员</Text>
                    <IconFont name="Arrow-Right2" size={23} style={{ marginTop: "6rpx" }} />
                </View> */}
            </View>

            <View className="menu-grid">
                {menuItems.map((item, index) => (
                    <View key={index} className="menu-item" onClick={() => item.url && handleNavigate(item.url)} style={{ cursor: "pointer" }}>
                        <View className="icon-wrapper">
                            <Image className="icon" src={item.icon} />
                            {item.hasIndicator && <View className="indicator" />}
                        </View>
                        <Text className="text">{item.text}</Text>
                    </View>
                ))}
            </View>
        </View>
    );
};

export default StudentSection;
