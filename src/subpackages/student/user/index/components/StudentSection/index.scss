.student-section {
    font-family: BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Microsoft Yahei", sans-serif;
    // margin-top: 20rpx;
    padding: 20rpx;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30rpx;
        line-height: normal;

        .title {
            font-size: 32rpx;
            font-weight: 600;
            color: #333333;
            position: relative;
            padding-left: 15rpx;

            &:before {
                content: "";
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 6rpx;
                height: 28rpx;
                background: linear-gradient(to bottom, #4a6cf7, #6e3ff5);
                border-radius: 3rpx;
            }
        }

        .view-all {
            display: flex;
            align-items: center;
            background-color: #f8faff;
            padding: 10rpx 15rpx;
            border-radius: 20rpx;
            transition: all 0.3s ease;

            &:active {
                background-color: #eef1ff;
                transform: translateX(5rpx);
            }

            text {
                font-size: 24rpx;
                color: #666666;
                margin-right: 5rpx;
            }
        }
    }

    .menu-grid {
        display: flex;
        justify-content: space-between;
        padding: 10rpx 0;

        .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: all 0.3s ease;
            padding: 15rpx;
            border-radius: 15rpx;

            &:active {
                background-color: #f5f7ff;
                transform: scale(0.95);
            }
        }

        .icon-wrapper {
            position: relative;
            margin-bottom: 12rpx;

            .icon {
                width: 70rpx;
                height: 70rpx;
                border-radius: 15rpx;
                box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
            }

            .indicator {
                position: absolute;
                right: -5rpx;
                top: -5rpx;
                background: linear-gradient(to bottom right, #ff6b6b, #ff4757);
                border-radius: 50%;
                width: 20rpx;
                height: 20rpx;
                border: solid 2rpx #ffffff;
                box-shadow: 0 2rpx 5rpx rgba(255, 71, 87, 0.3);
            }
        }

        .text {
            margin-top: 5rpx;
            font-size: 26rpx;
            color: #555555;
            font-weight: 500;
        }
    }
}
