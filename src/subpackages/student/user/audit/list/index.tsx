import { useReady } from "@tarojs/taro";
import React from "react";
import TopNavBar from "@components/topNavBar";
import "./index.scss";
import "@utils/app.scss";
import { login } from "@utils/login";
import MessageApi from "@components/MessageApi/MessageApi";

const App = () => {
    const message: any = React.useRef();
    const [userInfo, setUserInfo] = React.useState<API.UserInfo>(Object);

    useReady(() => {
        login().then((info: API.UserInfo) => {
            setUserInfo(info);
            console.log(userInfo);
        });
    });

    return (
        <>
            <MessageApi ref={message}></MessageApi>
            <TopNavBar title="身份确认"></TopNavBar>
        </>
    );
};
export default App;
