.qr-code-container {
    .floating-qr-btn {
        position: fixed;
        right: 32rpx;
        bottom: calc(32rpx + constant(safe-area-inset-bottom));
        bottom: calc(32rpx + env(safe-area-inset-bottom));
        width: 100rpx;
        height: 100rpx;
        background: #2f54eb;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4rpx 16rpx rgba(47, 84, 235, 0.3);
        z-index: 100;
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.95);
        }

        .qr-icon {
            width: 48rpx;
            height: 48rpx;
            color: #fff;
        }
    }

    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .modal-content {
        width: 80vw;
        background: #fff;
        border-radius: 12rpx;
        overflow: hidden;
    }

    .modal-header {
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid #f5f7fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .close-btn {
        font-size: 40rpx;
        color: #999;
        padding: 8rpx;
    }

    .modal-body {
        padding: 32rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .qr-image {
        width: 400rpx;
        height: 400rpx;
        margin: 32rpx 0;
    }

    .loading-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12rpx;
        color: #999;
        font-size: 28rpx;
    }

    .loading-icon {
        width: 32rpx;
        height: 32rpx;
        border: 4rpx solid #f3f3f3;
        border-top: 4rpx solid #2f54eb;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    .qr-tip {
        color: #666;
        font-size: 24rpx;
        margin-top: 16rpx;
        margin-left: 24rpx;
        margin-bottom: 24rpx;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
}
