import { View, Image } from "@tarojs/components";
import { Component } from "react";
import "./index.scss";
import { Close, QrCode } from "@nutui/icons-react-taro";
import request from "@/service/request";
import Layout from "@/components/Layout";
import { message } from "@/components/MessageApi/MessageApiSingleton";

interface State {
    showModal: boolean;
    qrCodeImage: string;
}

export default class QrCodePopup extends Component<{}, State> {
    state: State = {
        showModal: false,
        qrCodeImage: "",
    };

    showQRCode = () => {
        this.setState({ showModal: true });
        this.fetchQRCode();
    };

    hideQRCode = () => {
        this.setState({
            showModal: false,
            qrCodeImage: "",
        });
    };

    preventBubble = (e) => {
        e.stopPropagation();
    };

    fetchQRCode = async () => {
        try {
            const response = await request.post<any>("/Auth/CreateUserCode/getAuditRegisterQCode");
            if (response.success) {
                this.setState({
                    qrCodeImage: `data:image/png;base64,${response.data}`,
                });
            } else {
                message.error(response.message);
            }
        } catch (error) {
            this.setState({
                showModal: false,
                qrCodeImage: "",
            });
            console.error("获取二维码失败:", error);
            message.error("获取二维码失败");
        }
    };

    render() {
        const { showModal, qrCodeImage } = this.state;

        return (
            <Layout>
                <View className="qr-code-container">
                    <View className="floating-qr-btn" onClick={this.showQRCode}>
                        <QrCode
                            style={{
                                color: "#fff",
                            }}
                        />
                    </View>

                    {showModal && (
                        <View className="modal-overlay" onClick={this.hideQRCode}>
                            <View className="modal-content qr-modal" onClick={this.preventBubble}>
                                <View className="modal-header">
                                    <View>注册二维码</View>
                                    <Close className="close-btn" onClick={() => this.setState({ showModal: false })} size={20} color="#666" />
                                </View>
                                <View className="modal-body">
                                    {qrCodeImage ? (
                                        <Image
                                            className="qr-image"
                                            src={qrCodeImage}
                                            mode="aspectFit"
                                            style={{
                                                width: "70vw",
                                                height: "70vw",
                                            }}
                                        />
                                    ) : (
                                        <View className="loading-indicator">
                                            <View className="loading-icon"></View>
                                            <View>加载中...</View>
                                        </View>
                                    )}
                                </View>
                                <View className="qr-tip">请使用微信扫描二维码</View>
                            </View>
                        </View>
                    )}
                </View>
            </Layout>
        );
    }
}
