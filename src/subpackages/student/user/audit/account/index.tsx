import React, { useState, useEffect } from "react";
import { View, Text, Input, ScrollView, Image } from "@tarojs/components";
import { Button } from "@nutui/nutui-react-taro";
import Taro from "@tarojs/taro";
import "./index.scss";
import "@utils/app.scss";
import { Close } from "@nutui/icons-react-taro";
import request from "@/service/request";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import Layout from "@/components/Layout";
import QrCodePopup from "./components/qr-code-popup/index";
import { login } from "@utils/login";

interface Account {
    Id: string;
    RowIndex: number;
    UserId: string;
    RealName: string;
    IdCard: string;
    Phone: string;
    AuditStatus: number;
    AuditStatusText: string;
    subjects: string[];
    CompanyName: string;
    Gender: number;
    CreateTime: string;
    AuditTime: string;
    AuditUserName: string;
}

interface AuditListResponse {
    data: Account[];
    current: number;
    pages: number;
    total: number;
    size: number;
    hasPrevPages: boolean;
    hasNextPages: boolean;
}

interface AuditResponse {
    data: boolean;
    message: string;
}

interface SubjectResponse {
    data: boolean;
    message: string;
}

const AccountAudit = () => {
    const [statusValue, setStatusValue] = useState<number>(0);
    const [keyword, setKeyword] = useState("");
    const [accounts, setAccounts] = useState<Account[]>([]);
    const [showModal, setShowModal] = useState(false);
    const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]);
    const [showStatusPopup, setShowStatusPopup] = useState(false);
    const [selectedGender, setSelectedGender] = useState<number>(1);
    const [userEnabled, setUserEnabled] = useState<boolean>(true);
    const [isSubjectLoading, setIsSubjectLoading] = useState(false);

    const statusColumns = [
        { value: 0, text: "等待审核" },
        { value: 1, text: "审核通过" },
        { value: 2, text: "审核失败" },
    ];

    const subjectOptions = [
        { value: "20", label: "科二模拟" },
        { value: "30", label: "科三模拟" },
    ];

    // 初始加载数据
    useEffect(() => {
        login().then(async () => {
            handleSearch();
        });
    }, []);

    const handleSearch = async (searchStatus?: number) => {
        setCurrentPage(1);
        setAccounts([]);
        setIsLoading(true);
        setHasMore(true);

        try {
            const response = await request.post<API.Result<AuditListResponse>>("/SystemManage/UserAudit/getAuditList", {
                current: 1,
                pageSize: 10,
                AuditStatus: searchStatus ?? statusValue,
                SearchKey: keyword,
            });
            if (!response.success) {
                message.error(response.message || "查询失败");
                setHasMore(false);
            } else if (response.data) {
                setAccounts(response.data.data);
                setHasMore(response.data.hasNextPages);
            } else {
                setHasMore(false);
            }
        } catch (error) {
            console.log("error", error);
        } finally {
            setIsLoading(false);
        }
    };

    const loadMoreData = async () => {
        console.log("loadMoreData", isLoading, hasMore);
        if (isLoading || !hasMore) return;

        setIsLoading(true);
        const nextPage = currentPage + 1;

        try {
            const response = await request.post<API.Result<AuditListResponse>>("/SystemManage/UserAudit/getAuditList", {
                current: nextPage,
                pageSize: 10,
                AuditStatus: statusValue,
                SearchKey: keyword,
            });

            if (response.data) {
                setAccounts((prev) => [...prev, ...response.data.data]);
                setCurrentPage(response.data.current);
                setHasMore(response.data.hasNextPages);
            }
        } catch (error) {
            Taro.showToast({
                title: "获取更多数据失败",
                icon: "none",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleAccountClick = (account: Account) => {
        setSelectedAccount(account);
        setShowModal(true);
    };

    const getStatusText = (status: number) => {
        const option = statusColumns.find((opt) => opt.value === status);
        return option ? option.text : "未知状态";
    };

    const getStatusColor = (status: number) => {
        switch (status) {
            case 0:
                return "#1989fa"; // 等待审核 - 蓝色
            case 1:
                return "#07c160"; // 审核通过 - 绿色
            case 2:
                return "#ff4d4f"; // 审核失败 - 红色
            default:
                return "#666666";
        }
    };

    const handleStatusSelect = (status: number) => {
        setStatusValue(status);
        setShowStatusPopup(false);
        handleSearch(status);
    };

    const handleSubjectToggle = async (subject: string) => {
        console.log("handleSubjectToggle", subject);
        console.log("selectedAccount", selectedAccount);
        if (!selectedAccount) return;
        message.openLoading("正在处理...");

        setIsSubjectLoading(true);
        try {
            if (selectedSubjects.includes(subject)) {
                // Remove subject
                const response = await request.delete<API.Result<SubjectResponse>>("/SystemManage/JxUserInfo/deleteKeMuId", {
                    data: {
                        UserId: selectedAccount.UserId,
                        KeMuId: subject,
                    },
                });
                if (response.success) {
                    setSelectedSubjects((prev) => prev.filter((s) => s !== subject));
                    message.openToast("移除科目成功", "success");
                } else {
                    message.error(response.message, "移除科目失败");
                }
            } else {
                // Add subject
                const response = await request.put<API.Result<SubjectResponse>>("/SystemManage/JxUserInfo/addKeMuId", {
                    data: {
                        UserId: selectedAccount.UserId,
                        KeMuId: subject,
                    },
                });
                if (response.success) {
                    setSelectedSubjects((prev) => [...prev, subject]);
                    message.openToast("添加科目成功", "success");
                } else {
                    message.error(response.message, "添加科目失败");
                }
            }
        } catch (error) {
            message.error(error.message, "操作失败");
        } finally {
            setIsSubjectLoading(false);
            message.closeLoading();
        }
    };

    const handleApprove = async (account: Account) => {
        try {
            message.openConfirm("确定要通过该账号的审核吗？", "确认审核", async () => {
                const response = await request.put<API.Result<AuditResponse>>("/SystemManage/UserAudit/approve", {
                    userId: account.UserId,
                    subjects: selectedSubjects,
                    gender: selectedGender,
                    enabled: userEnabled,
                });

                if (response.data) {
                    message.openToast("审核通过成功", "success");
                    setShowModal(false);
                    setSelectedSubjects([]);
                    handleSearch(); // 刷新列表
                }
            });
        } catch (error) {
            message.openToast("操作失败", "error");
        }
    };

    const handleReject = async (account: Account) => {
        try {
            message.openConfirm("确定要拒绝该账号的审核吗？", "确认拒绝", async () => {
                const response = await request.put<API.Result<AuditResponse>>("/SystemManage/UserAudit/reject", {
                    userId: account.UserId,
                });

                if (response.data) {
                    message.openToast("拒绝成功", "success");
                    setShowModal(false);
                    handleSearch(); // 刷新列表
                }
            });
        } catch (error) {
            message.openToast("操作失败", "error");
        }
    };

    const handleGenderSelect = (gender: number) => {
        setSelectedGender(gender);
    };

    return (
        <>
            <Layout>
                <View className="account-audit">
                    <View className="fixed-header">
                        <View className="status-row">
                            <View className="status-item">
                                <Text className="dot">•</Text>
                                <Text className="label">审核状态</Text>
                                <View className="status-value" onClick={() => setShowStatusPopup(true)}>
                                    <Text>{statusColumns.find((opt) => opt.value === statusValue)?.text}</Text>
                                    <View className="arrow" />
                                </View>
                            </View>
                        </View>

                        <View className="search-row">
                            <Input className="search-input" placeholder="请输入姓名/证件号/电话" value={keyword} onInput={(e) => setKeyword(e.detail.value)} />
                            <Button className="search-btn" onClick={() => handleSearch()}>
                                查询
                            </Button>
                        </View>
                    </View>

                    <ScrollView className="result-list" scrollY enhanced showScrollbar={false} scrollWithAnimation>
                        {accounts.map((item) => (
                            <View key={item.Id} className="student-card" onClick={() => handleAccountClick(item)}>
                                <View className="status-tag">
                                    <Text className="status-dot" style={{ color: getStatusColor(item.AuditStatus) }}>
                                        •
                                    </Text>
                                    <Text className="status-text" style={{ color: getStatusColor(item.AuditStatus) }}>
                                        {getStatusText(item.AuditStatus)}
                                    </Text>
                                </View>
                                <View className="info-row name-row">
                                    <Text className="dot">•</Text>
                                    <Text className="label">姓名：</Text>
                                    <Text className="value">{item.RealName}</Text>
                                </View>
                                <View className="info-row">
                                    <Text className="dot">•</Text>
                                    <Text className="label">证件：</Text>
                                    <Text className="value">{item.IdCard}</Text>
                                </View>
                                <View className="info-row bottom-row">
                                    <Text className="dot">•</Text>
                                    <Text className="label">电话：</Text>
                                    <Text className="value">{item.Phone}</Text>
                                    <Text className="index-number">#{item.RowIndex}</Text>
                                </View>
                            </View>
                        ))}

                        {isLoading && (
                            <View className="loading-indicator">
                                <View className="loading-icon">
                                    <Text className="weui-loading" />
                                </View>
                                <Text>加载中...</Text>
                            </View>
                        )}

                        {hasMore && !isLoading && (
                            <View className="load-more-btn" onClick={loadMoreData}>
                                <Text>点击查看更多</Text>
                            </View>
                        )}

                        {!hasMore && accounts.length > 0 && (
                            <View className="no-more-data">
                                <Text>已经到底啦</Text>
                            </View>
                        )}

                        {accounts.length === 0 && !isLoading && (
                            <View className="empty-data">
                                <Text>暂无数据</Text>
                            </View>
                        )}
                    </ScrollView>

                    {/* 状态选择弹窗 */}
                    {showStatusPopup && (
                        <View className="status-popup-overlay" onClick={() => setShowStatusPopup(false)}>
                            <View className="status-popup-content" onClick={(e) => e.stopPropagation()}>
                                <View className="status-popup-header">
                                    <Text>选择审核状态</Text>
                                    <Close className="close-btn" onClick={() => setShowStatusPopup(false)} size={20} color="#666" />
                                </View>
                                <View className="status-popup-body">
                                    {statusColumns.map((option) => (
                                        <View
                                            key={option.value}
                                            className={`status-option ${statusValue === option.value ? "selected" : ""}`}
                                            onClick={() => handleStatusSelect(option.value)}
                                        >
                                            <Text className="status-text">{option.text}</Text>
                                        </View>
                                    ))}
                                </View>
                            </View>
                        </View>
                    )}

                    {showModal && selectedAccount && (
                        <View className="modal-overlay" onClick={() => setShowModal(false)}>
                            <View className="modal-content" onClick={(e) => e.stopPropagation()}>
                                <View className="modal-header">
                                    <Text>账号审核</Text>
                                    <Close className="close-btn" onClick={() => setShowModal(false)} size={20} color="#666" />
                                </View>
                                <View className="modal-body">
                                    <View className="info-item">
                                        <Text className="label">账户姓名</Text>
                                        <Text className="value">{selectedAccount.RealName}</Text>
                                        {selectedAccount.AuditStatus === 1 && (
                                            <View className="user-enabled-selection">
                                                <View className={`enabled-option ${userEnabled ? "selected" : ""}`} onClick={() => setUserEnabled(true)}>
                                                    启用
                                                </View>
                                                <View className={`disabled-option ${!userEnabled ? "selected" : ""}`} onClick={() => setUserEnabled(false)}>
                                                    禁用
                                                </View>
                                            </View>
                                        )}
                                    </View>
                                    <View className="info-item">
                                        <Text className="label">证件号码</Text>
                                        <Text className="value">{selectedAccount.IdCard}</Text>
                                    </View>
                                    <View className="info-item">
                                        <Text className="label">联系电话</Text>
                                        <Text className="value">{selectedAccount.Phone}</Text>
                                    </View>
                                    <View className="info-item">
                                        <Text className="label">登记性别</Text>
                                        <View className="gender-selection">
                                            <View className={`gender-option ${selectedGender === 1 ? "selected" : ""}`} onClick={() => handleGenderSelect(1)}>
                                                男
                                            </View>
                                            <View className={`gender-option ${selectedGender === 2 ? "selected" : ""}`} onClick={() => handleGenderSelect(2)}>
                                                女
                                            </View>
                                        </View>
                                    </View>
                                    <View className="info-item">
                                        <Text className="label">公司名称</Text>
                                        <Text className="value">{selectedAccount.CompanyName}</Text>
                                    </View>
                                    <View className="info-item">
                                        <Text className="label">审核状态</Text>
                                        <View className="status-value-container">
                                            <Text className="status-dot" style={{ color: getStatusColor(selectedAccount.AuditStatus) }}>
                                                •
                                            </Text>
                                            <Text className="value" style={{ color: getStatusColor(selectedAccount.AuditStatus) }}>
                                                {getStatusText(selectedAccount.AuditStatus)}
                                            </Text>
                                        </View>
                                    </View>
                                    {selectedAccount.AuditStatus === 1 && (
                                        <View className="info-item">
                                            <Text className="label">带训科目</Text>
                                            <View className="subjects">
                                                {subjectOptions.map((option) => (
                                                    <Text
                                                        key={option.value}
                                                        className={`subject-tag ${selectedSubjects.includes(option.value) ? "selected" : ""}`}
                                                        onClick={() => handleSubjectToggle(option.value)}
                                                    >
                                                        {option.label}
                                                    </Text>
                                                ))}
                                            </View>
                                        </View>
                                    )}
                                    <View className="info-item">
                                        <Text className="label">提交时间</Text>
                                        <Text className="value">{selectedAccount.CreateTime}</Text>
                                    </View>
                                    {selectedAccount.AuditStatus !== 0 && (
                                        <>
                                            <View className="info-item">
                                                <Text className="label">审核时间</Text>
                                                <Text className="value">{selectedAccount.AuditTime}</Text>
                                            </View>
                                            <View className="info-item">
                                                <Text className="label">审核人员</Text>
                                                <Text className="value">{selectedAccount.AuditUserName}</Text>
                                            </View>
                                        </>
                                    )}
                                </View>

                                <View className="modal-footer">
                                    <View
                                        className={`btn reject ${selectedAccount.AuditStatus !== 0 ? "disabled" : ""}`}
                                        onClick={() => selectedAccount.AuditStatus === 0 && handleReject(selectedAccount)}
                                    >
                                        拒绝
                                    </View>
                                    <View
                                        className={`btn approve ${selectedAccount.AuditStatus !== 0 ? "disabled" : ""}`}
                                        onClick={() => selectedAccount.AuditStatus === 0 && handleApprove(selectedAccount)}
                                    >
                                        通过
                                    </View>
                                </View>
                            </View>
                        </View>
                    )}
                </View>
            </Layout>
            <QrCodePopup />
        </>
    );
};

export default AccountAudit;
