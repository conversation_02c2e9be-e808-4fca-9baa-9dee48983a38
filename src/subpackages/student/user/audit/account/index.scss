page {
    background: #f7f8fa;

    font-family: "AlibabaPuHuiTi";
    // font-family: <PERSON><PERSON> SC, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
    font-size: 28rpx;
}

.account-audit {
    min-height: 100vh;
    padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
    padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
    background: #f5f7fa;
    font-size: 28rpx;
    display: flex;
    flex-direction: column;

    .fixed-header {
        position: sticky;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        background: #fff;
        margin-bottom: 24rpx;
    }

    .status-row {
        background: #fff;
        margin-bottom: 2rpx;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

        .status-item {
            margin: 0 32rpx;
            display: flex;
            align-items: center;
            height: 100rpx;

            .dot {
                color: #2f54eb;
                font-size: 28rpx;
                margin-right: 12rpx;
            }

            .label {
                font-size: 30rpx;
                color: #333;
                margin-right: 32rpx;
                font-weight: 500;
            }

            .status-value {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: space-between;
                color: #333;
                font-size: 30rpx;
                padding: 16rpx 24rpx;
                background: #f8f9fc;
                border-radius: 12rpx;
                transition: all 0.3s ease;

                &:active {
                    background: #f0f2f7;
                }

                .arrow {
                    width: 16rpx;
                    height: 16rpx;
                    border-right: 4rpx solid #999;
                    border-bottom: 4rpx solid #999;
                    transform: rotate(45deg);
                    margin-left: 16rpx;
                }
            }
        }
    }

    .search-row {
        background: #fff;
        // margin-bottom: 24rpx;
        padding: 24rpx 32rpx;
        display: flex;
        gap: 24rpx;
        position: sticky;
        top: 88rpx;
        z-index: 99;

        .search-input {
            flex: 1;
            height: 72rpx;
            background: #f7f7f7;
            border-radius: 8rpx;
            padding: 0 24rpx;
            font-size: 28rpx;
            color: #333;

            &::placeholder {
                color: #999;
            }
        }

        .search-btn {
            min-width: 120rpx;
            height: 72rpx;
            background: #2f54eb;
            border-radius: 8rpx;
            color: #fff;
            font-size: 28rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            padding: 0 32rpx;
            transition: all 0.2s ease;
            box-shadow: none;

            &:active {
                opacity: 0.9;
                transform: translateY(1rpx);
            }
        }
    }

    .result-list {
        flex: 1;
        // margin-top: 200rpx;
        padding-bottom: 24rpx;
        // padding-bottom: calc(24rpx + constant(safe-area-inset-bottom));
        // padding-bottom: calc(24rpx + env(safe-area-inset-bottom));

        .student-card {
            margin: 0 24rpx;
            background: #fff;
            border-radius: 12rpx;
            margin-bottom: 24rpx;
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
            position: relative;
            padding: 24rpx;

            .status-tag {
                position: absolute;
                top: 24rpx;
                right: 24rpx;
                display: flex;
                align-items: center;
                gap: 8rpx;

                .status-dot {
                    font-size: 32rpx;
                    line-height: 1;
                }

                .status-text {
                    font-size: 24rpx;
                }
            }

            .info-row {
                padding: 8rpx 0;
                height: 48rpx;
                display: flex;
                align-items: center;
                border: none;

                &.name-row {
                    margin-bottom: 8rpx;
                    padding-right: 120rpx;

                    .value {
                        font-size: 28rpx;
                        font-weight: 500;
                    }
                }

                .dot {
                    color: #2f54eb;
                    font-size: 24rpx;
                    margin-right: 8rpx;
                }

                .label {
                    color: #666;
                    font-size: 24rpx;
                }

                .value {
                    flex: 1;
                    margin-left: 12rpx;
                    color: #333;
                    font-size: 24rpx;
                }
            }
        }
    }

    .loading-indicator,
    .no-more-data,
    .empty-data,
    .load-more-btn {
        text-align: center;
        padding: 32rpx 0;
        color: #999;
        font-size: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12rpx;

        &::before,
        &::after {
            content: "";
            width: 48rpx;
            height: 2rpx;
            background: #e8e8e8;
        }
    }

    .loading-indicator {
        .loading-icon {
            width: 32rpx;
            height: 32rpx;
            margin-right: 8rpx;
            animation: rotate 1s linear infinite;
        }
    }

    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;

        .modal-content {
            width: 95vw;
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
        }
    }

    .modal-header {
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid #f5f7fa;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .close-btn {
            cursor: pointer;
        }
    }

    .modal-body {
        padding: 24rpx 32rpx;
        max-height: 60vh;
        overflow-y: auto;

        .info-item {
            margin-bottom: 24rpx;
            display: flex;
            align-items: flex-start;
            min-height: 60rpx;
            width: 100%;

            &:last-child {
                margin-bottom: 0;
            }

            .label {
                width: 160rpx;
                color: #999;
                font-size: 28rpx;
                padding-top: 2rpx;
                flex-shrink: 0;
                text-align: left;
            }

            .value {
                flex: 1;
                color: #333;
                font-size: 28rpx;
                line-height: 1.5;
                word-break: break-all;
                display: flex;
                align-items: flex-start;
                text-align: left;
            }

            .status-value-container {
                flex: 1;
                display: flex;
                align-items: flex-start;
                gap: 8rpx;
                min-height: 60rpx;
                text-align: left;

                .status-dot {
                    font-size: 32rpx;
                    line-height: 1;
                }

                .value {
                    color: #333;
                    font-size: 28rpx;
                    line-height: 1.5;
                    word-break: break-all;
                    text-align: left;
                }
            }

            .subjects {
                flex: 1;
                display: flex;
                flex-wrap: wrap;
                gap: 16rpx;
                padding-top: 4rpx;
                justify-content: flex-start;

                .subject-tag {
                    padding: 8rpx 24rpx;
                    background: #f5f5f5;
                    border-radius: 28rpx;
                    font-size: 26rpx;
                    color: #999;
                    line-height: 32rpx;
                    transition: all 0.3s ease;

                    &.selected {
                        background: #2f54eb;
                        color: #ffffff;
                    }
                }
            }
        }
    }

    .modal-footer {
        padding: 24rpx 32rpx;
        border-top: 1rpx solid #f5f7fa;
        display: flex;
        justify-content: flex-end;
        gap: 24rpx;

        .btn {
            min-width: 160rpx;
            height: 72rpx;
            border-radius: 8rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28rpx;
            transition: all 0.2s ease;

            &.reject {
                background: #f5f7fa;
                color: #666;

                &:active {
                    background: #eee;
                }
            }

            &.approve {
                background: #2f54eb;
                color: #fff;

                &:active {
                    background: #2f54eb;
                    opacity: 0.9;
                }
            }

            &.disabled {
                opacity: 0.5;
                pointer-events: none;
            }
        }
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.status-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-popup-content {
    width: 600rpx;
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
}

.status-popup-header {
    padding: 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1rpx solid #eee;

    .close-btn {
        padding: 8rpx;
    }
}

.status-popup-body {
    padding: 0rpx 32rpx 32rpx 32rpx;
}

.status-option {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #eee;

    &:last-child {
        border-bottom: none;
    }

    &.selected {
        .status-text {
            color: #2f54eb;
            font-weight: 500;
        }
    }

    .status-text {
        font-size: 28rpx;
        color: #333;
    }
}

.gender-selection {
    display: flex;
    gap: 20rpx;
    margin-top: 0;
    align-items: center;

    .gender-option {
        padding: 10rpx 30rpx;
        border: 1rpx solid #ddd;
        border-radius: 8rpx;
        font-size: 28rpx;
        color: #666;
        background: #fff;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        &.selected {
            background: #2f54eb;
            color: #fff;
            border-color: #2f54eb;
        }
    }
}

.user-enabled-selection {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-left: 16rpx;

    .enabled-option,
    .disabled-option {
        padding: 8rpx 24rpx;
        border-radius: 8rpx;
        font-size: 24rpx;
        background: #f5f7fa;
        color: #666;
        transition: all 0.2s ease;

        &.selected {
            background: #2f54eb;
            color: #fff;
        }
    }
}

.floating-qr-btn {
    position: fixed;
    right: 32rpx;
    bottom: calc(32rpx + constant(safe-area-inset-bottom));
    bottom: calc(32rpx + env(safe-area-inset-bottom));
    width: 100rpx;
    height: 100rpx;
    background: #2f54eb;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 16rpx rgba(47, 84, 235, 0.3);
    z-index: 100;
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.95);
    }

    .qr-icon {
        width: 48rpx;
        height: 48rpx;
        color: #fff;
    }
}

.qr-modal {
    .modal-content {
        width: 80vw;
        padding: 32rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .qr-image {
        width: 100%;
        height: 100%;
        margin: 32rpx 0;
    }

    .qr-tip {
        color: #666;
        font-size: 24rpx;
        margin-top: 16rpx;
    }
}

.bottom-row {
    display: flex;
    align-items: center;
    position: relative;
    padding-right: 80rpx; // Make space for the index number
}

.index-number {
    position: absolute;
    right: 20rpx;
    color: #999;
    font-size: 24rpx;
}

.load-more-btn {
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
        opacity: 0.8;
    }
}
