page {
    background: #f7f8fa;
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular, Source Sans Pro;
}

.audit-menu {
    min-height: 100vh;
    background-color: #f5f7fa;
    padding: 40rpx;

    .menu-grid {
        display: flex;
        flex-direction: column;
        gap: 30rpx;
        padding: 40rpx 0;
    }

    .menu-item {
        background: #ffffff;
        border-radius: 32rpx;
        padding: 40rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        width: 100%;

        &:active {
            transform: scale(0.98);
        }

        .icon-wrapper {
            width: 96rpx;
            height: 96rpx;
            background: #f0f7ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 32rpx;

            .icon {
                font-size: 48rpx;
            }
        }

        .title {
            font-size: 32rpx;
            color: #333;
            font-weight: 500;
        }
    }
}
