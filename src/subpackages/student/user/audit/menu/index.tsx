import Taro from "@tarojs/taro";
import React, { useEffect, useState } from "react";
import "./index.scss";
import "@utils/app.scss";
import request from "@service/request";

const AuditMenu = () => {
    const [menuItems, setMenuItems] = useState<
        Array<{
            text: string;
            icon: string;
            url: string;
        }>
    >([]);

    useEffect(() => {
        getMenuItems();
    }, []);

    const getMenuItems = async () => {
        try {
            const res = await request.post<any>("/Auth/MyMenu/getReviewMenu", {});
            if (res?.success) {
                // Transform the API response into the required format
                const transformedMenuItems = res.data.map((item: any) => ({
                    text: item.text || item.text,
                    icon: item.icon || "📋", // Default icon if none provided
                    url: item.url || item.url,
                }));
                setMenuItems(transformedMenuItems);
            }
        } catch (error) {
            console.error("Failed to fetch menu items:", error);
            Taro.showToast({
                title: "获取菜单失败",
                icon: "none",
            });
        }
    };

    const handleNavigate = (path: string) => {
        Taro.navigateTo({
            url: path,
        });
    };

    return (
        <div className="audit-menu">
            <div className="menu-grid">
                {menuItems.map((item, index) => (
                    <div key={index} className="menu-item" onClick={() => handleNavigate(item.url)}>
                        <div className="icon-wrapper">
                            <span className="icon">{item.icon}</span>
                        </div>
                        <div className="title">{item.text}</div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default AuditMenu;
