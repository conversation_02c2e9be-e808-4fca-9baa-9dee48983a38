page {
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
    background: #f7f8fa;
    height: auto;
    min-height: auto;
}


.page {
    background-color: #ffffff;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
}

.list {
    .ml-13 {
        margin-left: 26rpx;
    }

    .mt-23 {
        margin-top: 46rpx;
    }

    .ml-9-5 {
        margin-left: 19rpx;
    }

    .group_2 {
        padding: 18rpx 20rpx 0rpx;
        // overflow-y: auto;
        // margin-top: 2rpx;
    }

    .section {
        padding: 16rpx 21rpx 16rpx 34rpx;
        background-color: #f4f6f9;
        border-radius: 40.5rpx;
    }

    .font {
        font-size: 26rpx;
        font-family: Source Sans Pro;
        line-height: 18.98rpx;
        color: #545d69;
    }

    .text_2 {
        line-height: 19.76rpx;
    }

    .image_5 {
        width: 48rpx;
        height: 49rpx;
    }

    .group_3 {
        padding: 20rpx 0 13rpx 0;
    }

    .group_4 {
        padding: 0 3rpx;
    }


    .text-wrapper {
        padding: 12rpx 0;
        border-radius: 20rpx;
        width: 150rpx;
        height: 63rpx;
        border-left: solid 3rpx #2972fe;
        border-right: solid 3rpx #2972fe;
        border-top: solid 3rpx #2972fe;
        border-bottom: solid 3rpx #2972fe;
    }

    .text-wrapper_2 {
        padding: 11rpx 0 20rpx;
        background-color: #2972fe;
        border-radius: 20rpx;
        width: 144rpx;
        height: 63rpx;
        border-left: solid 3rpx #2972fe;
        border-right: solid 3rpx #2972fe;
        border-top: solid 3rpx #2972fe;
        border-bottom: solid 3rpx #2972fe;
    }

    .text-wrapper_3 {
        padding: 14rpx 0 20rpx;
        border-radius: 20rpx;
        width: 162rpx;
        height: 63rpx;
        border-left: solid 3rpx #2972fe;
        border-right: solid 3rpx #2972fe;
        border-top: solid 3rpx #2972fe;
        border-bottom: solid 3rpx #2972fe;
    }

    .text_5 {
        line-height: 34rpx;
    }

    .list-item {
        padding: 20rpx 20rpx 20rpx 20rpx;
        background-color: #ffffff;
        border-radius: 26rpx;
        box-shadow: 0rpx 2rpx 24rpx #76767633;
    }

    .list-item:first-child {
        margin-top: 0;
    }

    .image_6 {
        border-radius: 50%;
        width: 88rpx;
        height: 88rpx;
    }

    .group_5 {
        margin-top: 6rpx;
        width: 290rpx;
    }

    .font_3 {
        font-size: 32rpx;
        font-family: Source Sans Pro;
        line-height: 38rpx;
        font-weight: 600;
        color: #09101d;
    }

    .text_6 {
        width: 131rpx;
    }

    .font_4 {
        font-size: 26rpx;
        font-family: Source Sans Pro;
        line-height: 31rpx;
        color: #545d69;
    }

    .text_7 {
        margin-top: 12rpx;
    }

    .section_2 {
        padding: 53rpx 48rpx;
        background-color: #ffffff;
        border-radius: 42rpx;
        box-shadow: 0rpx 2rpx 24rpx #76767633;
    }

    .image_7 {
        border-radius: 14rpx;
        width: 76rpx;
        height: 54rpx;
    }

    .section_3 {
        padding: 8rpx 16rpx 8rpx 22rpx;
        background-color: #eaf2ff;
        border-radius: 14rpx;
        height: 54rpx;
    }

    .image_8 {
        width: 36rpx;
        height: 36rpx;
    }

    .text_14 {
        color: #2972fe;
        font-size: 31.54rpx;
        line-height: 23.96rpx;
    }


    .search-input {
        margin: 0;
        padding: 0;
        border: none;
        outline: none;
        background: none;
        box-shadow: none;
        // font: inherit;
        color: inherit;
        width: auto;
        height: auto;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    .text_4 {
        color: #ffffff;
        font-size: 26rpx;
        font-family: Source Sans Pro;
        // font-weight: 400;
        line-height: 40rpx;
    }

    .text_3 {
        line-height: 40rpx;
        font-size: 26rpx;
        text-align: center;
        width: 102rpx;
        font-family: Source Sans Pro;
    }

    .font_2 {
        font-size: 26rpx;
        font-family: Source Sans Pro;
        font-weight: 400;
        color: #2972fe;
    }

    .loading-view {
        // text-align: center;
        width: 100%;
        padding: 50rpx;
    }


    .nut-calendar-popup .nut-popup-title-right {
        top: 12px !important;
    }

    .nut-cell-title {
        line-height: 60rpx;
    }

    .nut-collapse-item-content-text {
        padding: 0;
    }

    // .nut-collapse-item-header {
    //     height: 100rpx;
    // }

    // .nut-collapse-item-extra {
    //     display: none;
    // }

    .nut-collapse-item-header {
        padding: 12px 16px;
    }

    .qianfei {
        width: 100%;
        background: linear-gradient(to right, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #fa2c19);
    }


    .section_4_bottom {
        background-image: linear-gradient(90deg, #cccccc00 0%, #cccccc 100%);
        width: 116rpx;
        height: 2rpx;
    }

    .section_5_bottom {
        background-image: linear-gradient(90deg, #cccccc 0%, #cccccc00 100%);
        width: 116rpx;
        height: 2rpx;
    }

}

.exam-list-item {
    .mt-15 {
        margin-top: 30rpx;
    }

    .font_2 {
        font-size: 36rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 33.8rpx;
        color: #000000e6;
    }

    .font_4 {
        font-size: 24rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 22.08rpx;
        color: #00000080;
    }

    .list-item:first-child {
        margin-top: 0;
    }

    .font_7 {
        font-size: 24rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 29.32rpx;
        color: #ffffff;
    }

    .font_9 {
        font-size: 42rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 35rpx;
        color: #ffffff;
    }

    .text-wrapper {
        padding: 24rpx 0 20rpx;
        border-radius: 16rpx;
        width: 198rpx;
        height: 78rpx;
        border-left: solid 2rpx #ffffff;
        border-right: solid 2rpx #ffffff;
        border-top: solid 2rpx #ffffff;
        border-bottom: solid 2rpx #ffffff;
    }

    .font_8 {
        font-size: 34rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        // line-height: 25.78rpx;
        color: #ffffff;
        font-weight: 800;
    }

    .text_13 {
        line-height: 22rpx;
    }

    .section_8 {
        padding: 40rpx 0 16rpx;
        background-color: #ffffff;
        border-radius: 16rpx;
        box-shadow: 0rpx 6rpx 16rpx #36384d0d;
    }

    .group_12 {
        padding: 0 32rpx;
    }

    .image_10 {
        margin-left: 324rpx;
        width: 19.58rpx;
        height: 22.18rpx;
    }

    .group_13 {
        margin-right: 8rpx;
    }

    .font_5 {
        font-size: 24rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 22.08rpx;
        color: #3a88ff;
    }

    .text_15 {
        line-height: 22.12rpx;
    }

    .image_9 {
        width: 10rpx;
        height: 16rpx;
    }

    .group_14 {
        margin-top: 20rpx;
    }

    .equal-division_2 {
        padding: 0 24rpx;
    }

    .group_15 {
        flex: 1 1 212.12rpx;
    }

    .font {
        font-size: 28rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 25.78rpx;
        color: #00000080;
    }

    .image_11 {
        width: 86rpx;
        height: 28rpx;
    }

    .font_6 {
        font-size: 24rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 22.08rpx;
        color: #000000e6;
    }

    .text_16 {
        line-height: 22.2rpx;
    }

    .text_17 {
        line-height: 26.02rpx;
    }

    .view_3 {
        margin: 10rpx 16rpx 0;
    }



    .equal-division-item {
        padding: 12rpx 0;
    }

    .font_2_kc {
        font-size: 22rpx;
    }

    .font_2_cc {
        font-size: 22rpx;
    }

    .group_12_xm {
        flex: 1;
        align-items: left;
    }

    .group_12_kc {
        flex: 2;
        align-items: right;
    }



    .section_7_1 {
        padding: 24rpx 32rpx 20rpx;
        background-color: #3a88ff;
        border-radius: 16rpx;
    }

    .section_7_2 {
        padding: 24rpx 32rpx 20rpx;
        background-color: #ff851a;
        border-radius: 16rpx;
    }

    .section_7_3 {
        padding: 24rpx 32rpx 20rpx;
        background-color: #67c23a;
        border-radius: 16rpx;
    }

    .section_7_4 {
        padding: 34rpx 32rpx 30rpx;
        background-color: #868588;
        border-radius: 16rpx;
    }

    .text-qianfei {
        padding: 1rpx 10rpx;
        background-color: #fff1ee;
        border-radius: 10rpx;
        height: 40rpx;
        border-left: solid 1rpx #ffc8c0;
        border-right: solid 1rpx #ffc8c0;
        border-top: solid 1rpx #ffc8c0;
        border-bottom: solid 1rpx #ffc8c0;
        color: #fa624b;
        font-size: 30rpx;
    }
}



.search-bar {
    .section {
        padding: 10rpx 21rpx 10rpx 34rpx;
        background-color: #f4f6f9;
        border-radius: 40.5rpx;
    }

    .ml-13 {
        margin-left: 26rpx;
    }

    .group_5 {
        margin-right: 4rpx;
        width: 106.96rpx;
        height: 56rpx;
    }

    .text-wrapper {
        padding: 8rpx 0;
        background-color: #d64444;
        border-radius: 18rpx;
        width: 52rpx;
        border-left: solid 2rpx #ffffff;
        border-right: solid 2rpx #ffffff;
        border-top: solid 2rpx #ffffff;
        border-bottom: solid 2rpx #ffffff;
    }

    .font {
        font-size: 24rpx;
        font-family: PingFang SC;
        line-height: 17.54rpx;
        font-weight: 700;
        color: #ff0000;
    }

    .text_3 {
        color: #ffffff;
        line-height: 17.3rpx;
    }

    .group_6 {
        margin-top: -10rpx;
    }

    .image_7 {
        width: 36rpx;
        height: 32rpx;
    }

    .font_3 {
        font-size: 24rpx;
        font-family: PingFang SC;
        line-height: 20.32rpx;
        color: #999999;
    }

    .text_5 {
        font-size: 26rpx;
        line-height: 24.06rpx;
    }

    .nut-searchbar-content {
        background: #f4f6f9;
        padding: 0;
    }

    .nut-searchbar-input-box {
        padding: 0 10rpx;
    }
}


.search-bar-popup {

    .divider {
        margin: 20rpx 0 0 0;
        background-color: #eeeeee;
        height: 2rpx;
    }

    .section_3 {
        width: 100%;
        padding-bottom: 40rpx;
        background-color: #ffffff;
        border-radius: 0rpx 0rpx 40rpx 40rpx;
        padding-left: 30rpx;
        padding-right: 30rpx;
    }

    .group_5 {
        margin-top: 18rpx;
        // padding: 0 24rpx;
    }

    .text-wrapper {
        padding: 16rpx 0;
        flex: 1 1 158rpx;
        background-color: #fdf4f1;
        border-radius: 10rpx;
        height: 60rpx;
    }

    .font_2 {
        // font-size: 28rpx;
        // font-family: 萍方-简;
        line-height: 26.16rpx;
        color: #333333;
        font-weight: 100;
    }

    .font_3 {
        font-size: 28rpx;
        // font-family: 萍方-简;
        line-height: 22.2rpx;
        color: #333333;
        font-weight: 100;
    }

    .text_8 {
        color: #fb634f;
        font-size: 26rpx;
        line-height: 24.3rpx;
        -webkit-text-stroke: 0.4rpx #fb634f;
    }

    .text_9 {
        -webkit-text-stroke: 0.4rpx #333333;
    }

    .text-wrapper_2 {
        flex: 1 1 158rpx;
        padding: 16rpx 0;
        background-color: #f5f6f7;
        border-radius: 10rpx;
        height: 60rpx;
    }

    .text_10 {
        font-size: 26rpx;
        line-height: 26rpx;
    }

    .font_4 {
        font-size: 28rpx;
        line-height: 22.2rpx;
        color: #999999;
    }

    .text_9 {
        -webkit-text-stroke: 0.4rpx #333333;
    }

    .group_6 {
        margin-top: 18rpx;
    }

    .text-wrapper_3 {
        padding: 20rpx 0 16rpx;
        background-color: #f5f6f7;
        border-radius: 10rpx;
        // width: 322rpx;
        width: calc((100vw - 100rpx) / 2);
        height: 62rpx;
        border-left: solid 2rpx #dfdfdf;
        border-right: solid 2rpx #dfdfdf;
        border-top: solid 2rpx #dfdfdf;
        border-bottom: solid 2rpx #dfdfdf;
    }

    .text_14 {
        font-size: 26rpx;
        line-height: 18rpx;
        // -webkit-text-stroke: 0.4rpx #999999;
    }

    .text_15 {
        font-size: 26rpx;
        line-height: 1.92rpx;
    }
}

.nut-picker {
    height: 50vh;
}

.nut-picker-panel {
    height: 98%;
}

.nut-picker-view-panel {
    height: 98%;
}