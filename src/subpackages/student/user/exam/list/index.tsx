import { useReady } from "@tarojs/taro";
import React, { useState } from "react";
import "./index.scss";
import "@utils/app.scss";
import { login } from "@utils/login";
import MessageApi from "@components/MessageApi/MessageApi";
import { Badge, Button, Calendar, Cell, Checkbox, Collapse, DatePicker, Empty, NavBar, Popup, PullToRefresh, SearchBar } from "@nutui/nutui-react-taro";
import Taro from "@tarojs/taro";
import IconFont from "@components/iconfont";
import moment from "moment";
import request from "@service/request";

const App = () => {
    const message: any = React.useRef();

    const [pageIndex, setPageIndex] = useState(1);
    const [pageSize] = useState(15);
    const [pageCount, setPageCount] = useState(0);
    const [dataCount, setDataCount] = useState(0);
    const [searchKey, setSearchKey] = useState("");

    const [data, setData] = useState<any[]>([]);

    const [loading, setLoading] = useState(false);

    const [mySale, setMySale] = useState(true);
    const [myTeach, setMyTeach] = useState(true);

    const [keMuIds, setKeMuIds] = useState([true, true, true]);

    const [resultIds, setResultIds] = useState([true, true, true]);
    const weeks = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];

    const [startTimeOpen, setStartTimeOpen] = useState(false);
    const [startTime, setStartTime] = useState("");
    const [endTimeOpen, setEndTimeOpen] = useState(false);
    const [endTime, setEndTime] = useState("");

    const [searchBarOpen, setSearchBarOpen] = useState(false);

    useReady(() => {
        search(1);
        setData([]);

        getViewHeight();
    });

    const [topHeight, setTopHeight] = useState(0);
    const getViewHeight = () => {
        // 创建一个选择器查询实例
        const query = Taro.createSelectorQuery();

        // 选择 id 为 'myView' 的元素
        query
            .select("#search-bar-top")
            .boundingClientRect((rect: any) => {
                if (rect) {
                    // rect.height 是元素的高度
                    console.log("View Height:", rect.height);
                    setTopHeight(rect.height);
                }
            })
            .exec();
    };

    const search = (index) => {
        setPageIndex(index);
        setLoading(true);
        setSearchBarOpen(false);
        let KeMuIds: string[] = [];

        if (keMuIds[0]) {
            KeMuIds.push("1");
        }
        if (keMuIds[1]) {
            KeMuIds.push("2");
        }
        if (keMuIds[2]) {
            KeMuIds.push("3");
        }

        let ResultIds: string[] = [];

        if (resultIds[0]) {
            ResultIds.push("2");
        }
        if (resultIds[1]) {
            ResultIds.push("3");
        }
        if (resultIds[2]) {
            ResultIds.push("7");
        }

        var postData: any = {
            SearchKey: searchKey,
            current: index,
            pageSize: pageSize,
            // ksrqs: ksrqs,
            KeMuIds: KeMuIds,
            ResultIds: ResultIds,
            MyStudentType: mySale && myTeach ? "MyAll" : mySale ? "MySale" : myTeach ? "MyTeach" : "",
        };
        if (startTime != "" || endTime != "") {
            postData.ksrqs = [startTime == "" ? "2000-01-01" : startTime, endTime == "" ? moment().format("YYYY-MM-01") : endTime];
        }

        request.post<any>("/Jx/Exam/Result/getExamList", postData).then((json) => {
            setLoading(false);
            if (json && json.success) {
                if (index == 1) {
                    setData(json.data.data);
                } else {
                    setData(data.concat(json.data.data));
                }
                setPageCount(json.data.pages);
                setDataCount(json.data.total);
            }
        });
    };

    return (
        <>
            <MessageApi ref={message}></MessageApi>
            <view
                id={"search-bar-top"}
                style={{
                    backgroundColor: "#fff",
                    position: "fixed",
                    top: "0px",
                    width: "100%",
                    zIndex: 99999,
                }}
            >
                <view
                    className="flex-col search-bar"
                    style={{
                        // position: 'fixed',
                        // top: `${statusBarHeight + 40}px`,
                        width: "100%",
                        backgroundColor: "#fff",
                        paddingLeft: "20rpx",
                        paddingRight: "20rpx",
                    }}
                >
                    <view
                        className="flex-col flex-1 group_2"
                        style={{
                            paddingBottom: "20rpx",
                            borderRadius: "0 24rpx 0",
                        }}
                    >
                        <view className="flex-row">
                            <view
                                style={{
                                    display: "flex",
                                    // width: '80%',
                                    width: "calc(100vw - 140rpx)",
                                    alignItems: "center",
                                    background: "#fff",
                                    marginTop: "10rpx",
                                }}
                            >
                                <view
                                    className="flex-row justify-between items-center section"
                                    style={{
                                        width: "100%",
                                    }}
                                >
                                    <SearchBar
                                        style={{
                                            border: 0,
                                            padding: 0,
                                        }}
                                        placeholder="请输入关键字词"
                                        onSearch={(e) => {
                                            setData([]);
                                        }}
                                        onFocus={() => {
                                            setSearchBarOpen(true);
                                        }}
                                        onChange={(val: string) => setSearchKey(val)}
                                    ></SearchBar>
                                </view>
                            </view>
                            <view
                                onClick={() => {
                                    setSearchBarOpen(!searchBarOpen);
                                }}
                                style={{
                                    display: "flex",
                                    alignItems: "center",
                                    // padding: '24rpx 30rpx',
                                    // width: '20%',
                                    marginLeft: "10px",
                                    marginTop: "14rpx",
                                }}
                            >
                                <div>
                                    <view className="flex-row items-center self-stretch relative group_6">
                                        <Badge style={{ marginRight: "5px" }} value={dataCount} max={999999}>
                                            <IconFont name="Setting" size={50} />
                                            {/* <text className="ml-4 font_3 text_5">筛选</text> */}
                                        </Badge>
                                    </view>
                                </div>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <Popup
                className="search-bar-popup"
                visible={searchBarOpen}
                position="top"
                style={{
                    width: "100%",
                    position: "fixed",
                    top: `${topHeight}px`,
                    // top: `50px`,
                }}
                round
                onClose={() => {
                    setSearchBarOpen(false);
                }}
            >
                <>
                    <view
                        className="flex-row justify-between items-center mt-6"
                        style={{
                            width: "100%",
                        }}
                    >
                        <view className="flex-col section_3">
                            <text className="self-start font text_7">考试时间范围</text>
                            <view className="flex-row self-stretch group_5">
                                <view
                                    className={
                                        startTime == moment().format("YYYY-MM-DD") && endTime == moment().add(7, "days").format("YYYY-MM-DD")
                                            ? "flex-col justify-start items-center text-wrapper"
                                            : "flex-col justify-start items-center text-wrapper_2"
                                    }
                                    onClick={() => {
                                        setStartTime(moment().format("YYYY-MM-DD"));
                                        setEndTime(moment().add(7, "days").format("YYYY-MM-DD"));
                                    }}
                                >
                                    <text
                                        className={
                                            startTime == moment().format("YYYY-MM-DD") && endTime == moment().add(7, "days").format("YYYY-MM-DD")
                                                ? "font_2 text_8"
                                                : "font_3 text_9 text_10"
                                        }
                                    >
                                        后七日
                                    </text>
                                </view>

                                <view
                                    className={
                                        startTime == moment().add(1, "days").format("YYYY-MM-DD") && endTime == moment().add(1, "days").format("YYYY-MM-DD")
                                            ? "ml-12 flex-col justify-start items-center text-wrapper"
                                            : "ml-12 flex-col justify-start items-center text-wrapper_2"
                                    }
                                    onClick={() => {
                                        setStartTime(moment().add(1, "days").format("YYYY-MM-DD"));
                                        setEndTime(moment().add(1, "days").format("YYYY-MM-DD"));
                                    }}
                                >
                                    <text
                                        className={
                                            startTime == moment().add(1, "days").format("YYYY-MM-DD") && endTime == moment().add(1, "days").format("YYYY-MM-DD")
                                                ? "font_2 text_8"
                                                : "font_3 text_9 text_10"
                                        }
                                    >
                                        明日
                                    </text>
                                </view>

                                <view
                                    className={
                                        startTime == moment().format("YYYY-MM-DD") && endTime == moment().format("YYYY-MM-DD")
                                            ? "ml-12 flex-col justify-start items-center text-wrapper"
                                            : "ml-12 flex-col justify-start items-center text-wrapper_2"
                                    }
                                    onClick={() => {
                                        setStartTime(moment().format("YYYY-MM-DD"));
                                        setEndTime(moment().format("YYYY-MM-DD"));
                                    }}
                                >
                                    <text
                                        className={
                                            startTime == moment().format("YYYY-MM-DD") && endTime == moment().format("YYYY-MM-DD") ? "font_2 text_8" : "font_3 text_9 text_10"
                                        }
                                    >
                                        今日
                                    </text>
                                </view>

                                <view
                                    className={
                                        startTime == moment().subtract(1, "days").format("YYYY-MM-DD") && endTime == moment().subtract(1, "days").format("YYYY-MM-DD")
                                            ? "ml-12 flex-col justify-start items-center text-wrapper"
                                            : "ml-12 flex-col justify-start items-center text-wrapper_2"
                                    }
                                    onClick={() => {
                                        setStartTime(moment().subtract(1, "days").format("YYYY-MM-DD"));
                                        setEndTime(moment().subtract(1, "days").format("YYYY-MM-DD"));
                                    }}
                                >
                                    <text
                                        className={
                                            startTime == moment().subtract(1, "days").format("YYYY-MM-DD") && endTime == moment().subtract(1, "days").format("YYYY-MM-DD")
                                                ? "font_2 text_8"
                                                : "font_3 text_9 text_10"
                                        }
                                    >
                                        昨日
                                    </text>
                                </view>

                                <view
                                    className={
                                        startTime == moment().subtract(7, "days").format("YYYY-MM-DD") && endTime == moment().format("YYYY-MM-DD")
                                            ? "ml-12 flex-col justify-start items-center text-wrapper"
                                            : "ml-12 flex-col justify-start items-center text-wrapper_2"
                                    }
                                    onClick={() => {
                                        setStartTime(moment().subtract(7, "days").format("YYYY-MM-DD"));
                                        setEndTime(moment().format("YYYY-MM-DD"));
                                    }}
                                >
                                    <text
                                        className={
                                            startTime == moment().subtract(7, "days").format("YYYY-MM-DD") && endTime == moment().format("YYYY-MM-DD")
                                                ? "font_2 text_8"
                                                : "font_3 text_9 text_10"
                                        }
                                    >
                                        近七日
                                    </text>
                                </view>
                                {moment().date() > 10 && (
                                    <view
                                        className={
                                            startTime == moment().format("YYYY-MM-01") && endTime == moment().format("YYYY-MM-DD")
                                                ? "ml-12 flex-col justify-start items-center text-wrapper"
                                                : "ml-12 flex-col justify-start items-center text-wrapper_2"
                                        }
                                        onClick={() => {
                                            setStartTime(moment().format("YYYY-MM-01"));
                                            setEndTime(moment().format("YYYY-MM-DD"));
                                        }}
                                    >
                                        <text
                                            className={
                                                startTime == moment().format("YYYY-MM-01") && endTime == moment().format("YYYY-MM-DD") ? "font_2 text_8" : "font_3 text_9 text_10"
                                            }
                                        >
                                            本月
                                        </text>
                                    </view>
                                )}
                                {moment().date() <= 10 && (
                                    <view
                                        className={
                                            startTime == moment().subtract(1, "months").format("YYYY-MM-01") &&
                                            endTime == moment().subtract(1, "months").endOf("month").format("YYYY-MM-DD")
                                                ? "ml-12 flex-col justify-start items-center text-wrapper"
                                                : "ml-12 flex-col justify-start items-center text-wrapper_2"
                                        }
                                        onClick={() => {
                                            setStartTime(moment().subtract(1, "months").format("YYYY-MM-01"));
                                            setEndTime(moment().subtract(1, "months").endOf("month").format("YYYY-MM-DD"));
                                        }}
                                    >
                                        <text
                                            className={
                                                startTime == moment().subtract(1, "months").format("YYYY-MM-01") &&
                                                endTime == moment().subtract(1, "months").endOf("month").format("YYYY-MM-DD")
                                                    ? "font_2 text_8"
                                                    : "font_3 text_9 text_10"
                                            }
                                        >
                                            上月
                                        </text>
                                    </view>
                                )}
                            </view>
                            <view className="self-stretch divider"></view>
                            <text className="self-start font text_13 mt-10">自定义日期筛选</text>

                            <view className="flex-row justify-center self-stretch group_6">
                                <view
                                    className="flex-col justify-start items-center text-wrapper_3"
                                    onClick={() => {
                                        setStartTimeOpen(true);
                                    }}
                                >
                                    <text className="font_4 text_14">{startTime == "" ? "选择开始日期" : startTime}</text>
                                </view>
                                <text
                                    className="self-center font_2 text_9 text_15"
                                    style={{
                                        width: "50rpx",
                                        textAlign: "center",
                                    }}
                                >
                                    -
                                </text>
                                <view
                                    className="flex-col justify-start items-center text-wrapper_3"
                                    onClick={() => {
                                        setEndTimeOpen(true);
                                    }}
                                >
                                    <text className="font_4 text_14">{endTime == "" ? "选择结束日期" : endTime}</text>
                                </view>
                            </view>

                            <view className="self-stretch divider"></view>
                            <text className="self-start font text_13 mt-10">考试科目</text>
                            <view className="flex-row self-stretch group_5">
                                <view
                                    className={keMuIds[0] ? "flex-col justify-start items-center text-wrapper" : "flex-col justify-start items-center text-wrapper_2"}
                                    onClick={() => {
                                        setKeMuIds([!keMuIds[0], keMuIds[1], keMuIds[2]]);
                                    }}
                                >
                                    <text className={keMuIds[0] ? "font_2 text_8" : "font_3 text_9 text_10"}>科一</text>
                                </view>
                                <view
                                    className={keMuIds[1] ? "ml-12 flex-col justify-start items-center text-wrapper" : "ml-12 flex-col justify-start items-center text-wrapper_2"}
                                    onClick={() => {
                                        setKeMuIds([keMuIds[0], !keMuIds[1], keMuIds[2]]);
                                    }}
                                >
                                    <text className={keMuIds[1] ? "font_2 text_8" : "font_3 text_9 text_10"}>科二</text>
                                </view>
                                <view
                                    className={keMuIds[2] ? "ml-12 flex-col justify-start items-center text-wrapper" : "ml-12 flex-col justify-start items-center text-wrapper_2"}
                                    onClick={() => {
                                        setKeMuIds([keMuIds[0], keMuIds[1], !keMuIds[2]]);
                                    }}
                                >
                                    <text className={keMuIds[2] ? "font_2 text_8" : "font_3 text_9 text_10"}>科三</text>
                                </view>
                            </view>

                            <view className="self-stretch divider"></view>
                            <text className="self-start font text_13 mt-10">考试成绩</text>
                            <view className="flex-row self-stretch group_5">
                                <view
                                    className={resultIds[0] ? "flex-col justify-start items-center text-wrapper" : "flex-col justify-start items-center text-wrapper_2"}
                                    onClick={() => {
                                        setResultIds([!resultIds[0], resultIds[1], resultIds[2]]);
                                    }}
                                >
                                    <text className={resultIds[0] ? "font_2 text_8" : "font_3 text_9 text_10"}>合格</text>
                                </view>
                                <view
                                    className={resultIds[1] ? "ml-12 flex-col justify-start items-center text-wrapper" : "ml-12 flex-col justify-start items-center text-wrapper_2"}
                                    onClick={() => {
                                        setResultIds([resultIds[0], !resultIds[1], resultIds[2]]);
                                    }}
                                >
                                    <text className={resultIds[1] ? "font_2 text_8" : "font_3 text_9 text_10"}>不合格</text>
                                </view>
                                <view
                                    className={resultIds[2] ? "ml-12 flex-col justify-start items-center text-wrapper" : "ml-12 flex-col justify-start items-center text-wrapper_2"}
                                    onClick={() => {
                                        setResultIds([resultIds[0], resultIds[1], !resultIds[2]]);
                                    }}
                                >
                                    <text className={resultIds[2] ? "font_2 text_8" : "font_3 text_9 text_10"}>待考</text>
                                </view>
                            </view>

                            <view className="self-stretch divider"></view>
                            <text className="self-start font text_13 mt-10">查询区间</text>
                            <view className="flex-row self-stretch group_5">
                                <view
                                    className={mySale ? "flex-col justify-start items-center text-wrapper" : "flex-col justify-start items-center text-wrapper_2"}
                                    onClick={() => {
                                        // if (!mySale) {
                                        //     setMyTeach(false);
                                        // }
                                        setMySale(!mySale);
                                    }}
                                >
                                    <text className={mySale ? "font_2 text_8" : "font_3 text_9 text_10"}>我招的人</text>
                                </view>
                                <view
                                    className={myTeach ? "ml-12 flex-col justify-start items-center text-wrapper" : "ml-12 flex-col justify-start items-center text-wrapper_2"}
                                    onClick={() => {
                                        // if (!myTeach) {
                                        //     setMySale(false);
                                        // }
                                        setMyTeach(!myTeach);
                                    }}
                                >
                                    <text className={myTeach ? "font_2 text_8" : "font_3 text_9 text_10"}>我带的人</text>
                                </view>
                            </view>
                            <view
                                className="flex-row justify-between items-center mt-20"
                                style={{
                                    width: "100%",
                                }}
                            >
                                <Button
                                    block
                                    type="primary"
                                    onClick={() => {
                                        setData([]);
                                        search(1);
                                    }}
                                >
                                    确认
                                </Button>
                            </view>
                        </view>
                    </view>
                </>
            </Popup>

            <view
                className="flex-col safe__area list"
                id="primaryScroll"
                style={{
                    overflowY: "auto",
                    width: "100%",
                    // position: 'fixed',
                    marginTop: `${topHeight}px`,
                }}
            >
                {data.map((item, index) => {
                    return (
                        <view className="exam-list-item">
                            <view className="flex-col flex-1 group_2">
                                <view className="mt-8 flex-col list-item section_8">
                                    <view className="flex-row justify-between group_12">
                                        <view className="flex-row">
                                            <Badge style={{ marginInlineEnd: "5px" }} value={index + 1}></Badge>
                                            <text className="font_4 text_14 ml-14">
                                                {item?.sfzmhm
                                                    ? item?.sfzmhm.length == 18
                                                        ? item?.sfzmhm.substring(0, 4) + "*".repeat(item?.sfzmhm.length - 8) + item?.sfzmhm.substring(item?.sfzmhm.length - 4)
                                                        : item?.sfzmhm
                                                    : ""}{" "}
                                                招: {item.SaleUserName}
                                            </text>
                                        </view>
                                        <view
                                            className="flex-row items-center"
                                            onClick={() => {
                                                Taro.navigateTo({
                                                    url: `/subpackages/student/user/student/index/index?id=${item.StudentId}`,
                                                });
                                            }}
                                        >
                                            <text className="font_5 text_15">查看</text>
                                            <IconFont
                                                name="Arrow-Right"
                                                size={28}
                                                color={"#3a88ff"}
                                                style={{
                                                    marginLeft: "10rpx",
                                                }}
                                            ></IconFont>
                                        </view>
                                    </view>
                                    <view className="flex-row group_14 equal-division_2 ">
                                        <view
                                            className="flex-col items-center group_15 equal-division-item group_12_xm"
                                            style={{ display: "flex", flexDirection: "column", alignItems: "flex-start" }}
                                        >
                                            <text className="font_2">
                                                <text className={`${item.NoPay > 0 ? "text-qianfei" : ""}`}>{item.xm}</text>
                                                <text className="font_2_cc ml-4">{item.StatusText}</text>
                                            </text>
                                            <text className="mt-10 font">
                                                {item.KeMuText}
                                                <text className="font_2_cc ml-4">{item.Times} 次</text>
                                                <text className="font_2_cc ml-4">{item.TeachUserName}</text>
                                            </text>
                                        </view>
                                        <view
                                            className="flex-col items-center group_15 equal-division-item group_12_kc"
                                            style={{ display: "flex", flexDirection: "column", alignItems: "flex-end" }}
                                        >
                                            <text className="font_2 font_2_kc">{item.kc}</text>
                                            <text className="mt-10 font text_12 font_2_cc">{item.cc}</text>
                                        </view>
                                    </view>
                                    <view
                                        className={`flex-row justify-between items-center view_3 ${
                                            item.ResultText == "取消"
                                                ? "section_7_4"
                                                : item.ResultText == "待考"
                                                ? "section_7_3"
                                                : item.ResultText == "合格"
                                                ? "section_7_1"
                                                : "section_7_2"
                                        }`}
                                    >
                                        <view className="flex-col items-start">
                                            <text className="font_7">{moment(item.ksrq).format("YYYY")}</text>
                                            <text className="font_9">
                                                {moment(item.ksrq).format("MM-DD")} <text className="font_7">{weeks[moment(item.ksrq).format("d")]}</text>
                                            </text>
                                        </view>
                                        <view className="flex-col justify-start items-center text-wrapper">
                                            <text className="font_8 text_13">{item.ResultText}</text>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    );
                })}
                {!loading && pageIndex < pageCount && (
                    <view
                        style={{
                            padding: "40rpx 0 30rpx",
                            textAlign: "center",
                            width: "100%",
                            fontSize: "24rpx",
                            color: "#2972fe",
                        }}
                        onClick={() => {
                            search(pageIndex + 1);
                        }}
                    >
                        <view
                            className="flex-row justify-evenly items-center"
                            style={{
                                color: "#2972fe",
                            }}
                        >
                            <view className="section_4_bottom"></view>
                            <text
                                className="font_4 text_13_bottom"
                                style={{
                                    color: "#2972fe",
                                }}
                            >
                                点击查看更多
                            </text>
                            <view className="section_5_bottom"></view>
                        </view>
                    </view>
                )}
                {loading && (
                    <view
                        style={{
                            padding: "40rpx 0 30rpx",
                            textAlign: "center",
                            width: "100%",
                            fontSize: "24rpx",
                        }}
                    >
                        <view className="flex-row justify-evenly items-center">
                            <view className="section_4_bottom"></view>
                            <text className="font_4 text_13_bottom">正在加载</text>
                            <view className="section_5_bottom"></view>
                        </view>
                    </view>
                )}

                {pageIndex >= pageCount && data.length > 0 && !loading && (
                    <view
                        style={{
                            padding: "40rpx 0 30rpx",
                            textAlign: "center",
                            width: "100%",
                            fontSize: "24rpx",
                        }}
                    >
                        <view className="flex-row justify-evenly items-center">
                            <view className="section_4_bottom"></view>
                            <text className="font_4 text_13_bottom">已经到底部</text>
                            <view className="section_5_bottom"></view>
                        </view>
                    </view>
                )}
                {!loading && data.length == 0 && (
                    <>
                        <Empty description="无数据" style={{ margin: "0px" }} />
                    </>
                )}
            </view>
            <view className="search-bar-popup">
                <DatePicker
                    title="选择查询的报名日期"
                    visible={startTimeOpen}
                    value={startTime == "" ? new Date() : new Date(startTime)}
                    showChinese
                    onClose={() => setStartTimeOpen(false)}
                    threeDimensional={true}
                    onChange={(options, values) => {
                        console.log(options);
                        console.log(values);
                    }}
                    onConfirm={(options, values) => {
                        setStartTime(moment(`${values[0]}-${values[1]}-${values[2]}`).format("YYYY-MM-DD"));
                    }}
                    startDate={new Date(moment().subtract(5, "years").format("YYYY-01-01"))}
                    // endDate={new Date()}
                    endDate={new Date(moment().format("YYYY-12-31"))}
                />
                <DatePicker
                    title="选择查询的报名日期"
                    visible={endTimeOpen}
                    value={endTime == "" ? new Date() : new Date(endTime)}
                    showChinese
                    onClose={() => setEndTimeOpen(false)}
                    threeDimensional={true}
                    onChange={(options, values) => {
                        console.log(options);
                        console.log(values);
                    }}
                    onConfirm={(options, values) => {
                        setEndTime(moment(`${values[0]}-${values[1]}-${values[2]}`).format("YYYY-MM-DD"));
                    }}
                    startDate={new Date(moment().subtract(5, "years").format("YYYY-01-01"))}
                    // endDate={new Date()}
                    endDate={new Date(moment().format("YYYY-12-31"))}
                />
            </view>
        </>
    );
};
export default App;
