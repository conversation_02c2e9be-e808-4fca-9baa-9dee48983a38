page {
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
    background: #f7f8fa;
    height: auto;
    min-height: auto;
}

.page {
    background-color: #ffffff;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
}

.list {
    .ml-13 {
        margin-left: 26rpx;
    }

    .mt-23 {
        margin-top: 46rpx;
    }

    .ml-9-5 {
        margin-left: 19rpx;
    }

    .group_2 {
        padding: 20rpx 20rpx 0rpx;
        // overflow-y: auto;
        // margin-top: 2rpx;
    }

    .font {
        font-size: 26rpx;
        font-family: <PERSON><PERSON>, STYuanti-SC-Regular, Source Sans Pro;
        line-height: 18.98rpx;
        color: #545d69;
    }

    .text_2 {
        line-height: 19.76rpx;
    }

    .image_5 {
        width: 48rpx;
        height: 49rpx;
    }

    .group_3 {
        padding: 20rpx 0 13rpx 0;
    }

    .group_4 {
        padding: 0 3rpx;
    }

    .text-wrapper {
        padding: 12rpx 0;
        border-radius: 20rpx;
        width: 150rpx;
        height: 63rpx;
        border-left: solid 3rpx #2972fe;
        border-right: solid 3rpx #2972fe;
        border-top: solid 3rpx #2972fe;
        border-bottom: solid 3rpx #2972fe;
    }

    .text-wrapper_2 {
        padding: 11rpx 0 20rpx;
        background-color: #2972fe;
        border-radius: 20rpx;
        width: 144rpx;
        height: 63rpx;
        border-left: solid 3rpx #2972fe;
        border-right: solid 3rpx #2972fe;
        border-top: solid 3rpx #2972fe;
        border-bottom: solid 3rpx #2972fe;
    }

    .text-wrapper_3 {
        padding: 14rpx 0 20rpx;
        border-radius: 20rpx;
        width: 162rpx;
        height: 63rpx;
        border-left: solid 3rpx #2972fe;
        border-right: solid 3rpx #2972fe;
        border-top: solid 3rpx #2972fe;
        border-bottom: solid 3rpx #2972fe;
    }

    .text_5 {
        line-height: 34rpx;
    }

    .list-item {
        padding: 40rpx;
        background-color: #ffffff;
        border-radius: 52rpx;
        box-shadow: 0rpx 4rpx 48rpx #76767633;
    }

    .list-item:first-child {
        margin-top: 0;
    }

    .image_6 {
        border-radius: 50%;
        width: 176rpx;
        height: 176rpx;
    }

    .group_5 {
        margin-top: 12rpx;
        width: 50vw;
    }

    .font_3 {
        font-size: 64rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
        line-height: 76rpx;
        font-weight: 400;
        color: #09101d;
    }

    .text_6 {
        width: 262rpx;
    }

    .font_4 {
        font-size: 52rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
        line-height: 62rpx;
        color: #545d69;
    }

    .text_7 {
        margin-top: 24rpx;
    }

    .section_2 {
        padding: 106rpx 96rpx;
        background-color: #ffffff;
        border-radius: 84rpx;
        box-shadow: 0rpx 4rpx 48rpx #76767633;
    }

    .image_7 {
        border-radius: 28rpx;
        width: 152rpx;
        height: 108rpx;
    }

    .section_3 {
        padding: 16rpx 32rpx 16rpx 44rpx;
        background-color: #eaf2ff;
        border-radius: 28rpx;
        height: 108rpx;
    }

    .image_8 {
        width: 72rpx;
        height: 72rpx;
    }

    .text_14 {
        color: #2972fe;
        font-size: 63.08rpx;
        line-height: 47.92rpx;
    }

    .search-input {
        margin: 0;
        padding: 0;
        border: none;
        outline: none;
        background: none;
        box-shadow: none;
        // font: inherit;
        color: inherit;
        width: auto;
        height: auto;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    .text_4 {
        color: #ffffff;
        font-size: 26rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
        // font-weight: 400;
        line-height: 40rpx;
    }

    .text_3 {
        line-height: 40rpx;
        font-size: 26rpx;
        text-align: center;
        width: 102rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
    }

    .font_2 {
        font-size: 26rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
        font-weight: 400;
        color: #2972fe;
    }

    .loading-view {
        // text-align: center;
        width: 100%;
        padding: 50rpx;
    }

    .nut-calendar-popup .nut-popup-title-right {
        top: 12px !important;
    }

    .nut-cell-title {
        line-height: 60rpx;
    }

    .nut-collapse-item-content-text {
        padding: 0;
    }

    // .nut-collapse-item-header {
    //     height: 100rpx;
    // }

    // .nut-collapse-item-extra {
    //     display: none;
    // }

    .nut-collapse-item-header {
        padding: 12px 16px;
    }

    .qianfei {
        width: 100%;
        background: linear-gradient(to right, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #fa2c19);
    }

    .section_4_bottom {
        background-image: linear-gradient(90deg, #cccccc00 0%, #cccccc 100%);
        width: 116rpx;
        height: 2rpx;
    }

    .section_5_bottom {
        background-image: linear-gradient(90deg, #cccccc 0%, #cccccc00 100%);
        width: 116rpx;
        height: 2rpx;
    }
}

.search-bar {
    .section {
        padding: 10rpx 21rpx 10rpx 34rpx;
        background-color: #f4f6f9;
        border-radius: 40.5rpx;
    }

    .ml-13 {
        margin-left: 26rpx;
    }

    .group_5 {
        margin-right: 4rpx;
        width: 106.96rpx;
        height: 56rpx;
    }

    .text-wrapper {
        padding: 8rpx 0;
        background-color: #d64444;
        border-radius: 18rpx;
        width: 52rpx;
        border-left: solid 2rpx #ffffff;
        border-right: solid 2rpx #ffffff;
        border-top: solid 2rpx #ffffff;
        border-bottom: solid 2rpx #ffffff;
    }

    // .font {
    //     font-size: 24rpx;
    //     font-family: PingFang SC;
    //     line-height: 17.54rpx;
    //     font-weight: 700;
    //     color: #ff0000;
    // }

    .text_3 {
        color: #ffffff;
        line-height: 17.3rpx;
    }

    .group_6 {
        margin-top: -10rpx;
    }

    .image_7 {
        width: 36rpx;
        height: 32rpx;
    }

    .font_3 {
        font-size: 24rpx;
        font-family: PingFang SC;
        line-height: 20.32rpx;
        color: #999999;
    }

    .text_5 {
        font-size: 26rpx;
        line-height: 24.06rpx;
    }

    .nut-searchbar-content {
        background: #f4f6f9;
        padding: 0;
    }

    .nut-searchbar-input-box {
        padding: 0 10rpx;
    }
}

.search-bar-popup {
    .divider {
        margin: 20rpx 0 0 0;
        background-color: #eeeeee;
        height: 2rpx;
    }

    .section_3 {
        width: 100%;
        padding-bottom: 40rpx;
        background-color: #ffffff;
        border-radius: 0rpx 0rpx 40rpx 40rpx;
        padding-left: 30rpx;
        padding-right: 30rpx;
    }

    .group_5 {
        margin-top: 18rpx;
        // padding: 0 24rpx;
    }

    .text-wrapper {
        padding: 16rpx 0;
        flex: 1 1 158rpx;
        background-color: #fdf4f1;
        border-radius: 10rpx;
        height: 60rpx;
    }

    .font_2 {
        font-size: 28rpx;
        // font-family: 萍方-简;
        line-height: 26.16rpx;
        color: #333333;
        font-weight: 100;
    }

    .font_3 {
        font-size: 28rpx;
        // font-family: 萍方-简;
        line-height: 22.2rpx;
        color: #333333;
        font-weight: 100;
    }

    .text_8 {
        color: #fb634f;
        font-size: 26rpx;
        line-height: 24.3rpx;
        -webkit-text-stroke: 0.4rpx #fb634f;
    }

    .text_9 {
        -webkit-text-stroke: 0.4rpx #333333;
    }

    .text-wrapper_2 {
        flex: 1 1 158rpx;
        padding: 16rpx 0;
        background-color: #f5f6f7;
        border-radius: 10rpx;
        height: 60rpx;
    }

    .text_10 {
        font-size: 26rpx;
        line-height: 26rpx;
    }

    .font_4 {
        font-size: 28rpx;
        line-height: 22.2rpx;
        color: #999999;
    }

    .font_2 {
        // font-size: 28rpx;
        line-height: 26.16rpx;
        color: #333333;
    }

    .text_9 {
        -webkit-text-stroke: 0.4rpx #333333;
    }

    .group_6 {
        margin-top: 18rpx;
    }

    .text-wrapper_3 {
        padding: 20rpx 0 16rpx;
        background-color: #f5f6f7;
        border-radius: 10rpx;
        // width: 322rpx;
        width: calc((100vw - 100rpx) / 2);
        height: 62rpx;
        border-left: solid 2rpx #dfdfdf;
        border-right: solid 2rpx #dfdfdf;
        border-top: solid 2rpx #dfdfdf;
        border-bottom: solid 2rpx #dfdfdf;
    }

    .text_14 {
        font-size: 26rpx;
        line-height: 18rpx;
        // -webkit-text-stroke: 0.4rpx #999999;
    }

    .text_15 {
        font-size: 26rpx;
        line-height: 1.92rpx;
    }

    // .ml-9 {
    //     margin-left: 0.56rem;
    // }

    // .font {
    //     font-size: 0.88rem;
    //     font-family: 苹方;
    //     line-height: 0.82rem;
    //     color: #333333;
    // }

    // .section_4 {
    //     padding: 0.88rem 0.63rem;
    //     background-color: #f5f6f7;
    // }

    .section_5 {
        padding: 24rpx 36rpx 24rpx 48rpx;
        background-color: #ffffff;
        border-radius: 10rpx;
        width: 100%;
        height: 74rpx;
        border-left: solid 1rpx #d8d8d8;
        border-right: solid 1rpx #d8d8d8;
        border-top: solid 1rpx #d8d8d8;
        border-bottom: solid 1rpx #d8d8d8;
    }

    .text_5 {
        line-height: 0.82rem;
    }

    .image_9 {
        border-radius: 0.031rem;
        width: 0.56rem;
        height: 0.31rem;
    }

    .section_6 {
        padding: 0.75rem 0;
        background-color: #ffffff;
        border-radius: 1.13rem;
        height: 2.31rem;
    }

    .image_8 {
        width: 0.75rem;
        height: 0.75rem;
    }

    // .font_2 {
    //     font-size: 0.88rem;
    //     font-family: 萍方-简;
    //     line-height: 0.82rem;
    //     color: #333333;
    // }

    // .text_6 {
    //     color: #999999;
    //     line-height: 0.81rem;
    // }
}

.nut-picker {
    height: 50vh;
}

.nut-picker-panel {
    height: 98%;
}

.nut-picker-view-panel {
    height: 98%;
}

.count-list {
    padding: 20rpx 20rpx 0rpx;
    width: 100%;
    border-radius: 4px;
    overflow: hidden;

    .count-list-table {
        border-radius: 10rpx;
    }

    .table-header {
        padding: 18px 18px 18px 18px;
        background-color: #e0e0e0;
        display: flex;
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .header-cell {
        flex: 1;
        padding: 10px;
        text-align: center;
        line-height: 21.65rpx;
        font-size: 26rpx;
        line-height: 17.63rpx;
        color: #1b1d21;
    }

    .table-row {
        display: flex;
    }

    .row-cell {
        flex: 1;
        padding: 10px;
        text-align: center;
        // border-bottom: 1px solid #ccc;
        font-size: 23.55rpx;
        line-height: 28.99rpx;
        color: #212121;
        display: flex;
        flex-direction: column;
        justify-content: center;
        line-height: 60rpx;

        .cell-top {
            font-size: 23.55rpx;
            line-height: 28.99rpx;
            color: #212121;
            margin-bottom: 5px;
        }

        .cell-bottom {
            font-size: 16rpx;
            line-height: 25.36rpx;
            color: #8f8f8f;
        }
    }

    .table-total {
        padding: 37rpx 4rpx 29rpx 8rpx;

        display: flex;
        margin-top: 5px;
        padding-top: 10px;
        // border-top: 1px solid #000;
    }

    .total-row-cell {
        flex: 1;
        padding: 10px;
        text-align: center;
        font-weight: bolder;
    }

    /* 奇数行背景颜色 */
    .table-row:nth-child(odd) {
        background-color: #f9f9f9;
    }

    /* 偶数行背景颜色 */
    .table-row:nth-child(even) {
        background-color: #f0f0f0;
    }

    .table-row:last-child .row-cell {
        border-bottom: none;
    }
}
