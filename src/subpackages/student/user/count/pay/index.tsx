import React, { useEffect, useState } from "react";
import "./index.scss";
import "@utils/app.scss";
import MessageApi from "@components/MessageApi/MessageApi";
import IconFont from "@components/iconfont";
import { Badge, Button, DatePicker, Empty, NavBar, Picker, Popup, SearchBar } from "@nutui/nutui-react-taro";
import Taro, { nextTick, useDidShow, useLoad, useReady } from "@tarojs/taro";
import moment from "moment";
import { tool } from "@utils/tool";
import request from "@service/request";

const App = () => {
    const message: any = React.useRef();
    const [pageIndex, setPageIndex] = useState(1);
    const [pageSize] = useState(15);
    const [pageCount, setPageCount] = useState(0);

    const [data, setData] = useState<any[]>([]);
    const [dataCount, setDataCount] = useState<any>(0);

    const [searchBarOpen, setSearchBarOpen] = useState(false);
    const [loading, setLoading] = useState(false);

    const [startTimeOpen, setStartTimeOpen] = useState(false);
    const [startTime, setStartTime] = useState("");
    const [endTimeOpen, setEndTimeOpen] = useState(false);
    const [endTime, setEndTime] = useState("");

    const [timeType, setTimeType] = useState("CreateTime");

    const [countType, setCountType] = useState<string>();

    const [countTypeList] = useState([
        { text: "招生推荐人", value: "SaleUserId" },
        { text: "学员报名点", value: "JxDeptId" },
        { text: "学员训练场", value: "JxFieldId" },
        { text: "收款报名点", value: "CreateJxDeptId" },
        { text: "推荐人报名点", value: "SaleJxDeptId" },
        { text: "推荐人训练场", value: "SaleJxFieldId" },
        { text: "费用类型", value: "CostTypeId" },
        { text: "支付方式", value: "PayTypeId" },
        { text: "收款账户", value: "AccountId" },
    ]);

    const [countTypeOpen, setCountTypeOpen] = useState(false);

    const [isTuition, setIsTuition] = useState("1");
    const [isStandardFee, setIsStandardFee] = useState("0");
    const [isCost, setIsCost] = useState("0");

    useDidShow(() => {
        if (tool.data.get("user-count-pay-timeType")) {
            setTimeType(tool.data.get("user-count-pay-timeType"));
        }
        if (tool.data.get("user-count-pay-countType")) {
            setCountType(tool.data.get("user-count-pay-countType"));
        } else {
            setCountType("SaleUserId");
        }
        if (tool.data.get("user-count-pay-startTime")) {
            setStartTime(tool.data.get("user-count-pay-startTime"));
        } else {
            setStartTime(moment().format("YYYY-MM-DD"));
        }
        if (tool.data.get("user-count-pay-endTime")) {
            setEndTime(tool.data.get("user-count-pay-endTime"));
        } else {
            setEndTime(moment().format("YYYY-MM-DD"));
        }

        if (tool.data.get("user-count-pay-isTuition")) {
            setIsTuition(tool.data.get("user-count-pay-isTuition"));
        }
        if (tool.data.get("user-count-pay-isStandardFee")) {
            setIsStandardFee(tool.data.get("user-count-pay-isStandardFee"));
        }
        if (tool.data.get("user-count-pay-isCost")) {
            setIsCost(tool.data.get("user-count-pay-isCost"));
        }

        setSearchBarOpen(true);
    });

    const search = async () => {
        tool.data.set("user-count-pay-timeType", timeType);
        tool.data.set("user-count-pay-countType", countType);
        tool.data.set("user-count-pay-startTime", startTime);
        tool.data.set("user-count-pay-endTime", endTime);

        tool.data.set("user-count-pay-isTuition", isTuition);
        tool.data.set("user-count-pay-isStandardFee", isStandardFee);
        tool.data.set("user-count-pay-isCost", isCost);

        if (!startTime || startTime == "") {
            message.current.openToast("选择时间");
        } else if (!endTime || endTime == "") {
            message.current.openToast("选择时间");
        } else {
            setSearchBarOpen(false);

            message.current.openLoading("正在加载数据");

            let post: any = {
                TimeType: timeType,
                Times: [startTime, endTime],
                CountTypes: [countType],
                isTuition: isTuition,
                isStandardFee: isStandardFee,
                isCost: isCost,
                pageSize: 99999,
            };
            if (timeType == "CreateTime") {
                post.CreateTimes = [startTime, endTime];
            }
            if (timeType == "PayTime") {
                post.PayTimes = [startTime, endTime];
            }
            request.post<any>("/Jx/Pay/Count/getPayCount", post).then((json) => {
                message.current.closeLoading();

                if (json && json.success) {
                    setData(json.data.data);
                } else {
                    message.current.openDialog("统计失败", json.message);
                }
            });
        }
    };

    useReady(() => {
        getViewHeight();
    });

    const [topHeight, setTopHeight] = useState(0);
    const getViewHeight = () => {
        // 创建一个选择器查询实例
        const query = Taro.createSelectorQuery();

        query
            .select("#search-bar-top")
            .boundingClientRect((rect: any) => {
                if (rect) {
                    // rect.height 是元素的高度
                    console.log("View Height:", rect.height);
                    setTopHeight(rect.height);
                }
            })
            .exec();
    };

    return (
        <>
            <MessageApi ref={message}></MessageApi>
            <view
                className="search-bar-popup"
                id="search-bar-popup"
                style={{
                    backgroundColor: "#fff",
                    position: "fixed",
                    top: "0px",
                    width: "100%",
                    zIndex: 1002,
                }}
            >
                <view
                    className="flex-col search-bar"
                    id={"search-bar-top"}
                    style={{
                        position: "fixed",
                        top: `0px`,
                        width: "100%",
                        backgroundColor: "#fff",
                        paddingLeft: "20rpx",
                        paddingRight: "20rpx",
                    }}
                >
                    <view
                        className="flex-col flex-1 group_2"
                        style={{
                            paddingBottom: "20rpx",
                            borderRadius: "0 24rpx 0",
                            color: "#1a1a1a",
                            fontSize: "14px",
                        }}
                    >
                        <text className="self-start font text_13 mt-10">自定义日期筛选</text>
                        <view className="flex-row justify-center self-stretch group_6">
                            <view
                                className="flex-col justify-start items-center text-wrapper_3"
                                onClick={() => {
                                    if (!searchBarOpen) {
                                        setSearchBarOpen(true);
                                    } else {
                                        setEndTimeOpen(false);
                                        setStartTimeOpen(true);
                                    }
                                }}
                            >
                                <text className="font_4 text_14">{startTime == "" ? "选择开始日期" : startTime}</text>
                            </view>
                            <text
                                className="self-center font_2 text_9 text_15"
                                style={{
                                    width: "50rpx",
                                    textAlign: "center",
                                }}
                            >
                                -
                            </text>
                            <view
                                className="flex-col justify-start items-center text-wrapper_3"
                                onClick={() => {
                                    if (!searchBarOpen) {
                                        setSearchBarOpen(true);
                                    } else {
                                        setStartTimeOpen(false);
                                        setEndTimeOpen(true);
                                    }
                                }}
                            >
                                <text className="font_4 text_14">{endTime == "" ? "选择结束日期" : endTime}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view
                className="list flex-col safe__area"
                id="primaryScroll"
                style={{
                    overflowY: "auto",
                    marginTop: `${topHeight}px`,
                }}
            >
                {!loading && data.length > 0 && (
                    <>
                        <view className="count-list">
                            <view className="count-list-table">
                                <view className="table-header">
                                    <view className="header-cell">
                                        {countType == "SaleUserId"
                                            ? "推荐人"
                                            : countType == "JxDeptId"
                                            ? "报名点"
                                            : countType == "JxFieldId"
                                            ? "训练场"
                                            : countType == "CreateJxDeptId"
                                            ? "报名点"
                                            : countType == "SaleJxDeptId"
                                            ? "店面"
                                            : countType == "SaleJxFieldId"
                                            ? "训练场"
                                            : countType == "CostTypeId"
                                            ? "类型"
                                            : countType == "PayTypeId"
                                            ? "方式"
                                            : countType == "AccountId"
                                            ? "账户"
                                            : ""}
                                    </view>
                                    <view className="header-cell">数量</view>
                                </view>
                                {data.map((item) => {
                                    return (
                                        <view key={item.id} className="table-row">
                                            <view className="row-cell">
                                                {countType == "SaleUserId" ? (
                                                    <>
                                                        <view className="cell-top">{item.SaleUserName}</view>
                                                        <view className="cell-bottom">{item.SaleJxDeptName}</view>
                                                    </>
                                                ) : countType == "JxDeptId" ? (
                                                    item.JxDeptName
                                                ) : countType == "JxFieldId" ? (
                                                    item.JxFieldName
                                                ) : countType == "CreateJxDeptId" ? (
                                                    item.JxDeptName
                                                ) : countType == "SaleJxDeptId" ? (
                                                    item.JxDeptName
                                                ) : countType == "SaleJxFieldId" ? (
                                                    item.JxFieldName
                                                ) : countType == "CostTypeId" ? (
                                                    item.CostTypeName
                                                ) : countType == "PayTypeId" ? (
                                                    item.PayTypeName
                                                ) : countType == "AccountId" ? (
                                                    item.AccountName
                                                ) : (
                                                    ""
                                                )}
                                            </view>
                                            <view className="row-cell">{item.SumPayMoney.toFixed(2)}</view>
                                        </view>
                                    );
                                })}
                                <view className="table-total">
                                    <view className="total-row-cell">合计</view>
                                    <view className="total-row-cell">{data.reduce((sum, item) => sum + item.SumPayMoney, 0).toFixed(2)}</view>
                                </view>
                            </view>
                        </view>
                    </>
                )}
                {!loading && data.length == 0 && <Empty description="无数据" style={{ margin: "0px" }} />}
                {loading && (
                    <view
                        style={{
                            padding: "40rpx 0 30rpx",
                            textAlign: "center",
                            width: "100%",
                            fontSize: "24rpx",
                        }}
                    >
                        <view className="flex-row justify-evenly items-center">
                            <view className="section_4_bottom"></view>
                            <text className="font_4 text_13_bottom">正在加载</text>
                            <view className="section_5_bottom"></view>
                        </view>
                    </view>
                )}
            </view>

            <Popup
                className="search-bar-popup"
                visible={searchBarOpen}
                position="top"
                style={{
                    width: "100%",
                    position: "fixed",
                    top: `${topHeight}px`,
                    // top: `${topHeight}px`,
                }}
                round
                onClose={() => {
                    setSearchBarOpen(false);
                }}
            >
                <>
                    <view
                        className="flex-row justify-between items-center mt-6"
                        style={{
                            width: "100%",
                        }}
                    >
                        <view className="flex-col section_3">
                            {/* <text className="self-start font text_7">时间范围</text> */}
                            <view className="flex-row self-stretch">
                                <view
                                    className={
                                        startTime == moment().format("YYYY-MM-DD") && endTime == moment().format("YYYY-MM-DD")
                                            ? "flex-col justify-start items-center text-wrapper"
                                            : "flex-col justify-start items-center text-wrapper_2"
                                    }
                                    onClick={() => {
                                        setStartTime(moment().format("YYYY-MM-DD"));
                                        setEndTime(moment().format("YYYY-MM-DD"));
                                    }}
                                >
                                    <text
                                        className={
                                            startTime == moment().format("YYYY-MM-DD") && endTime == moment().format("YYYY-MM-DD") ? "font_2 text_8" : "font_3 text_9 text_10"
                                        }
                                    >
                                        今日
                                    </text>
                                </view>

                                <view
                                    className={
                                        startTime == moment().subtract(1, "days").format("YYYY-MM-DD") && endTime == moment().subtract(1, "days").format("YYYY-MM-DD")
                                            ? "ml-12 flex-col justify-start items-center text-wrapper"
                                            : "ml-12 flex-col justify-start items-center text-wrapper_2"
                                    }
                                    onClick={() => {
                                        setStartTime(moment().subtract(1, "days").format("YYYY-MM-DD"));
                                        setEndTime(moment().subtract(1, "days").format("YYYY-MM-DD"));
                                    }}
                                >
                                    <text
                                        className={
                                            startTime == moment().subtract(1, "days").format("YYYY-MM-DD") && endTime == moment().subtract(1, "days").format("YYYY-MM-DD")
                                                ? "font_2 text_8"
                                                : "font_3 text_9 text_10"
                                        }
                                    >
                                        昨日
                                    </text>
                                </view>

                                <view
                                    className={
                                        startTime == moment().subtract(7, "days").format("YYYY-MM-DD") && endTime == moment().format("YYYY-MM-DD")
                                            ? "ml-12 flex-col justify-start items-center text-wrapper"
                                            : "ml-12 flex-col justify-start items-center text-wrapper_2"
                                    }
                                    onClick={() => {
                                        setStartTime(moment().subtract(7, "days").format("YYYY-MM-DD"));
                                        setEndTime(moment().format("YYYY-MM-DD"));
                                    }}
                                >
                                    <text
                                        className={
                                            startTime == moment().subtract(7, "days").format("YYYY-MM-DD") && endTime == moment().format("YYYY-MM-DD")
                                                ? "font_2 text_8"
                                                : "font_3 text_9 text_10"
                                        }
                                    >
                                        近七日
                                    </text>
                                </view>
                                {moment().date() > 10 && (
                                    <view
                                        className={
                                            startTime == moment().format("YYYY-MM-01") && endTime == moment().format("YYYY-MM-DD")
                                                ? "ml-12 flex-col justify-start items-center text-wrapper"
                                                : "ml-12 flex-col justify-start items-center text-wrapper_2"
                                        }
                                        onClick={() => {
                                            setStartTime(moment().format("YYYY-MM-01"));
                                            setEndTime(moment().format("YYYY-MM-DD"));
                                        }}
                                    >
                                        <text
                                            className={
                                                startTime == moment().format("YYYY-MM-01") && endTime == moment().format("YYYY-MM-DD") ? "font_2 text_8" : "font_3 text_9 text_10"
                                            }
                                        >
                                            本月
                                        </text>
                                    </view>
                                )}
                                {moment().date() <= 10 && (
                                    <view
                                        className={
                                            startTime == moment().subtract(1, "months").format("YYYY-MM-01") &&
                                            endTime == moment().subtract(1, "months").endOf("month").format("YYYY-MM-DD")
                                                ? "ml-12 flex-col justify-start items-center text-wrapper"
                                                : "ml-12 flex-col justify-start items-center text-wrapper_2"
                                        }
                                        onClick={() => {
                                            setStartTime(moment().subtract(1, "months").format("YYYY-MM-01"));
                                            setEndTime(moment().subtract(1, "months").endOf("month").format("YYYY-MM-DD"));
                                        }}
                                    >
                                        <text
                                            className={
                                                startTime == moment().subtract(1, "months").format("YYYY-MM-01") &&
                                                endTime == moment().subtract(1, "months").endOf("month").format("YYYY-MM-DD")
                                                    ? "font_2 text_8"
                                                    : "font_3 text_9 text_10"
                                            }
                                        >
                                            上月
                                        </text>
                                    </view>
                                )}
                            </view>
                            <view className="self-stretch divider"></view>
                            <text className="self-start font text_13 mt-10">时间类型</text>
                            <view className="flex-row self-stretch group_5">
                                <view
                                    className={timeType == "CreateTime" ? "flex-col justify-start items-center text-wrapper" : "flex-col justify-start items-center text-wrapper_2"}
                                    onClick={() => {
                                        setTimeType("CreateTime");
                                    }}
                                >
                                    <text className={timeType == "CreateTime" ? "font_2 text_8" : "font_3 text_9 text_10"}>录入时间</text>
                                </view>
                                <view
                                    className={
                                        timeType == "PayTime"
                                            ? "ml-12 flex-col justify-start items-center text-wrapper"
                                            : "ml-12 flex-col justify-start items-center text-wrapper_2"
                                    }
                                    onClick={() => {
                                        setTimeType("PayTime");
                                    }}
                                >
                                    <text className={timeType == "PayTime" ? "font_2 text_8" : "font_3 text_9 text_10"}>缴费时间</text>
                                </view>
                            </view>
                            <view className="self-stretch divider"></view>
                            <text className="self-start font text_13 mt-10">费用类型</text>
                            <view className="flex-row self-stretch group_5">
                                <view
                                    className={isTuition == "1" ? "flex-col justify-start items-center text-wrapper" : "flex-col justify-start items-center text-wrapper_2"}
                                    onClick={() => {
                                        setIsTuition(isTuition == "1" ? "" : "1");
                                    }}
                                >
                                    <text className={isTuition == "1" ? "font_2 text_8" : "font_3 text_9 text_10"}>报名费用</text>
                                </view>
                                <view
                                    className={
                                        isTuition == "0" ? "ml-12 flex-col justify-start items-center text-wrapper" : "ml-12 flex-col justify-start items-center text-wrapper_2"
                                    }
                                    onClick={() => {
                                        setIsTuition(isTuition == "0" ? "" : "0");
                                    }}
                                >
                                    <text className={isTuition == "0" ? "font_2 text_8" : "font_3 text_9 text_10"}>其他费用</text>
                                </view>
                            </view>
                            <view className="self-stretch divider"></view>
                            <text className="self-start font text_13 mt-10">收费类型</text>
                            <view className="flex-row self-stretch group_5">
                                <view
                                    className={isStandardFee == "0" ? "flex-col justify-start items-center text-wrapper" : "flex-col justify-start items-center text-wrapper_2"}
                                    onClick={() => {
                                        setIsStandardFee(isStandardFee == "0" ? "" : "0");
                                    }}
                                >
                                    <text className={isStandardFee == "0" ? "font_2 text_8" : "font_3 text_9 text_10"}>学员收费</text>
                                </view>
                                <view
                                    className={
                                        isStandardFee == "1" ? "ml-12 flex-col justify-start items-center text-wrapper" : "ml-12 flex-col justify-start items-center text-wrapper_2"
                                    }
                                    onClick={() => {
                                        setIsStandardFee(isStandardFee == "1" ? "" : "1");
                                    }}
                                >
                                    <text className={isStandardFee == "1" ? "font_2 text_8" : "font_3 text_9 text_10"}>一般收费</text>
                                </view>
                            </view>
                            <view className="self-stretch divider"></view>
                            <text className="self-start font text_13 mt-10">收入支出</text>
                            <view className="flex-row self-stretch group_5">
                                <view
                                    className={isCost == "0" ? "flex-col justify-start items-center text-wrapper" : "flex-col justify-start items-center text-wrapper_2"}
                                    onClick={() => {
                                        setIsCost(isCost == "0" ? "" : "0");
                                    }}
                                >
                                    <text className={isCost == "0" ? "font_2 text_8" : "font_3 text_9 text_10"}>收入</text>
                                </view>
                                <view
                                    className={
                                        isCost == "1" ? "ml-12 flex-col justify-start items-center text-wrapper" : "ml-12 flex-col justify-start items-center text-wrapper_2"
                                    }
                                    onClick={() => {
                                        setIsCost(isCost == "1" ? "" : "1");
                                    }}
                                >
                                    <text className={isCost == "1" ? "font_2 text_8" : "font_3 text_9 text_10"}>支出</text>
                                </view>
                            </view>
                            <view className="self-stretch divider"></view>
                            <text className="self-start font text_13 mt-10">统计方式</text>
                            <view
                                className="flex-row self-stretch group_5"
                                onClick={() => {
                                    setCountTypeOpen(true);
                                }}
                            >
                                <view
                                    className="flex-row items-center shrink-0 section_5"
                                    style={{
                                        textAlign: "right",
                                    }}
                                >
                                    <span className="font text_5">{countTypeList.find((item) => item.value === countType)?.text}</span>
                                    <IconFont name="Arrow-Right2" size={30} style={{}} />
                                </view>
                            </view>

                            <view className="self-stretch divider"></view>

                            <view
                                className="flex-row justify-between items-center mt-20"
                                style={{
                                    width: "100%",
                                }}
                            >
                                <Button
                                    block
                                    type="primary"
                                    onClick={() => {
                                        setData([]);
                                        search();
                                    }}
                                >
                                    确认
                                </Button>
                            </view>
                        </view>
                    </view>
                </>
            </Popup>
            <view className="search-bar-popup">
                {countType && (
                    <Picker
                        title="选择统计类型"
                        visible={countTypeOpen}
                        options={countTypeList}
                        defaultValue={[countType]}
                        onConfirm={(list, values: string[]) => {
                            setCountType(values[0]);
                        }}
                        onClose={() => setCountTypeOpen(false)}
                    />
                )}
                {!countType && (
                    <Picker
                        title="选择统计类型"
                        visible={countTypeOpen}
                        options={countTypeList}
                        onConfirm={(list, values: string[]) => {
                            setCountType(values[0]);
                        }}
                        onClose={() => setCountTypeOpen(false)}
                    />
                )}
                <DatePicker
                    title="选择查询日期"
                    visible={startTimeOpen}
                    value={startTime == "" ? new Date() : new Date(startTime)}
                    showChinese
                    onClose={() => setStartTimeOpen(false)}
                    threeDimensional={true}
                    onChange={(options, values) => {}}
                    onConfirm={(options, values) => {
                        setStartTime(moment(`${values[0]}-${values[1]}-${values[2]}`).format("YYYY-MM-DD"));
                    }}
                    startDate={new Date(moment().subtract(5, "years").format("YYYY-01-01"))}
                    // endDate={new Date()}
                    endDate={new Date(moment().format("YYYY-12-31"))}
                />
                <DatePicker
                    title="选择查询日期"
                    visible={endTimeOpen}
                    value={endTime == "" ? new Date() : new Date(endTime)}
                    showChinese
                    onClose={() => setEndTimeOpen(false)}
                    threeDimensional={true}
                    onChange={(options, values) => {
                        console.log(options);
                        console.log(values);
                    }}
                    onConfirm={(options, values) => {
                        setEndTime(moment(`${values[0]}-${values[1]}-${values[2]}`).format("YYYY-MM-DD"));
                    }}
                    startDate={new Date(moment().subtract(5, "years").format("YYYY-01-01"))}
                    // endDate={new Date()}
                    endDate={new Date(moment().format("YYYY-12-31"))}
                />
            </view>
        </>
    );
};
export default App;
