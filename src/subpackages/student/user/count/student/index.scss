page {
    background: #f7f8fa;
    min-height: 100vh;
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
}

.page {
    background-color: #ffffff;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
}

.list {
    .ml-13 {
        margin-left: 26rpx;
    }

    .mt-23 {
        margin-top: 46rpx;
    }

    .ml-9-5 {
        margin-left: 19rpx;
    }

    .group_2 {
        padding: 20rpx 20rpx 0rpx;
        // overflow-y: auto;
        // margin-top: 2rpx;
    }

    .font {
        font-size: 26rpx;
        font-family: <PERSON><PERSON>, STYuanti-SC-Regular, Source Sans Pro;
        line-height: 18.98rpx;
        color: #545d69;
    }

    .text_2 {
        line-height: 19.76rpx;
    }

    .image_5 {
        width: 48rpx;
        height: 49rpx;
    }

    .group_3 {
        padding: 20rpx 0 13rpx 0;
    }

    .group_4 {
        padding: 0 3rpx;
    }

    .text-wrapper {
        padding: 12rpx 0;
        border-radius: 20rpx;
        width: 150rpx;
        height: 63rpx;
        border-left: solid 3rpx #2972fe;
        border-right: solid 3rpx #2972fe;
        border-top: solid 3rpx #2972fe;
        border-bottom: solid 3rpx #2972fe;
    }

    .text-wrapper_2 {
        padding: 11rpx 0 20rpx;
        background-color: #2972fe;
        border-radius: 20rpx;
        width: 144rpx;
        height: 63rpx;
        border-left: solid 3rpx #2972fe;
        border-right: solid 3rpx #2972fe;
        border-top: solid 3rpx #2972fe;
        border-bottom: solid 3rpx #2972fe;
    }

    .text-wrapper_3 {
        padding: 14rpx 0 20rpx;
        border-radius: 20rpx;
        width: 162rpx;
        height: 63rpx;
        border-left: solid 3rpx #2972fe;
        border-right: solid 3rpx #2972fe;
        border-top: solid 3rpx #2972fe;
        border-bottom: solid 3rpx #2972fe;
    }

    .text_5 {
        line-height: 34rpx;
    }

    .list-item {
        padding: 20rpx 20rpx 20rpx 20rpx;
        background-color: #ffffff;
        border-radius: 26rpx;
        box-shadow: 0rpx 2rpx 24rpx #76767633;
    }

    .list-item:first-child {
        margin-top: 0;
    }

    .image_6 {
        border-radius: 50%;
        width: 88rpx;
        height: 88rpx;
    }

    .group_5 {
        margin-top: 6rpx;
        // width: 290rpx;
        width: 50vw;
    }

    .font_3 {
        font-size: 32rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
        line-height: 38rpx;
        font-weight: 400;
        color: #09101d;
    }

    .text_6 {
        width: 131rpx;
    }

    .font_4 {
        font-size: 26rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
        line-height: 31rpx;
        color: #545d69;
    }

    .text_7 {
        margin-top: 12rpx;
    }

    .section_2 {
        padding: 53rpx 48rpx;
        background-color: #ffffff;
        border-radius: 42rpx;
        box-shadow: 0rpx 2rpx 24rpx #76767633;
    }

    .image_7 {
        border-radius: 14rpx;
        width: 76rpx;
        height: 54rpx;
    }

    .section_3 {
        padding: 8rpx 16rpx 8rpx 22rpx;
        background-color: #eaf2ff;
        border-radius: 14rpx;
        height: 54rpx;
    }

    .image_8 {
        width: 36rpx;
        height: 36rpx;
    }

    .text_14 {
        color: #2972fe;
        font-size: 31.54rpx;
        line-height: 23.96rpx;
    }

    .search-input {
        margin: 0;
        padding: 0;
        border: none;
        outline: none;
        background: none;
        box-shadow: none;
        // font: inherit;
        color: inherit;
        width: auto;
        height: auto;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    .text_4 {
        color: #ffffff;
        font-size: 26rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
        // font-weight: 400;
        line-height: 40rpx;
    }

    .text_3 {
        line-height: 40rpx;
        font-size: 26rpx;
        text-align: center;
        width: 102rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
    }

    .font_2 {
        font-size: 26rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular, Source Sans Pro;
        font-weight: 400;
        color: #2972fe;
    }

    .loading-view {
        // text-align: center;
        width: 100%;
        padding: 50rpx;
    }

    .nut-calendar-popup .nut-popup-title-right {
        top: 12px !important;
    }

    .nut-cell-title {
        line-height: 60rpx;
    }

    .nut-collapse-item-content-text {
        padding: 0;
    }

    // .nut-collapse-item-header {
    //     height: 100rpx;
    // }

    // .nut-collapse-item-extra {
    //     display: none;
    // }

    .nut-collapse-item-header {
        padding: 12px 16px;
    }

    .qianfei {
        width: 100%;
        background: linear-gradient(to right, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #fa2c19);
    }

    .section_4_bottom {
        background-image: linear-gradient(90deg, #cccccc00 0%, #cccccc 100%);
        width: 116rpx;
        height: 2rpx;
    }

    .section_5_bottom {
        background-image: linear-gradient(90deg, #cccccc 0%, #cccccc00 100%);
        width: 116rpx;
        height: 2rpx;
    }
}

.search-bar {
    position: fixed;
    top: 0;
    width: 100%;
    background-color: #fff;
    padding: 20rpx;
    border-bottom: 1rpx solid rgba(230, 235, 245, 0.8);

    .section {
        padding: 10rpx 21rpx 10rpx 34rpx;
        background-color: #f4f6f9;
        border-radius: 40.5rpx;
    }

    .ml-13 {
        margin-left: 26rpx;
    }

    .group_5 {
        margin-right: 4rpx;
        width: 106.96rpx;
        height: 56rpx;
    }

    .text-wrapper {
        padding: 8rpx 0;
        background-color: #d64444;
        border-radius: 18rpx;
        width: 52rpx;
        border-left: solid 2rpx #ffffff;
        border-right: solid 2rpx #ffffff;
        border-top: solid 2rpx #ffffff;
        border-bottom: solid 2rpx #ffffff;
    }

    // .font {
    //     font-size: 24rpx;
    //     font-family: PingFang SC;
    //     line-height: 17.54rpx;
    //     font-weight: 700;
    //     color: #ff0000;
    // }

    .text_3 {
        color: #ffffff;
        line-height: 17.3rpx;
    }

    .group_6 {
        margin-top: -10rpx;
    }

    .image_7 {
        width: 36rpx;
        height: 32rpx;
    }

    .font_3 {
        font-size: 24rpx;
        font-family: PingFang SC;
        line-height: 20.32rpx;
        color: #999999;
    }

    .text_5 {
        font-size: 26rpx;
        line-height: 24.06rpx;
    }

    .nut-searchbar-content {
        background: #f4f6f9;
        padding: 0;
    }

    .nut-searchbar-input-box {
        padding: 0 10rpx;
    }
}

.search-bar-popup {
    background-color: #fff;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1002;
    box-shadow: 0 2rpx 24rpx rgba(118, 118, 118, 0.2);

    .divider {
        height: 1rpx;
        background-color: rgba(230, 235, 245, 0.8);
        margin: 24rpx 0;
    }

    .section_3 {
        width: 100%;
        padding-bottom: 40rpx;
        background-color: #ffffff;
        border-radius: 0rpx 0rpx 40rpx 40rpx;
        padding-left: 30rpx;
        padding-right: 30rpx;
    }

    .group_5 {
        margin-top: 18rpx;
        // padding: 0 24rpx;
    }

    .text-wrapper {
        padding: 16rpx 0;
        flex: 1 1 158rpx;
        background-color: #fdf4f1;
        border-radius: 10rpx;
        height: 60rpx;
    }

    .font_2 {
        font-size: 28rpx;
        // font-family: 萍方-简;
        line-height: 26.16rpx;
        color: #333333;
        font-weight: 100;
    }

    .font_3 {
        font-size: 28rpx;
        // font-family: 萍方-简;
        line-height: 22.2rpx;
        color: #333333;
        font-weight: 100;
    }

    .text_8 {
        color: #fb634f;
        font-size: 26rpx;
        line-height: 24.3rpx;
        -webkit-text-stroke: 0.4rpx #fb634f;
    }

    .text_9 {
        -webkit-text-stroke: 0.4rpx #333333;
    }

    .text-wrapper_2 {
        flex: 1 1 158rpx;
        padding: 16rpx 0;
        background-color: #f5f6f7;
        border-radius: 10rpx;
        height: 60rpx;
    }

    .text_10 {
        font-size: 26rpx;
        line-height: 26rpx;
    }

    .font_4 {
        font-size: 28rpx;
        line-height: 22.2rpx;
        color: #999999;
    }

    .font_2 {
        // font-size: 28rpx;
        line-height: 26.16rpx;
        color: #333333;
    }

    .text_9 {
        -webkit-text-stroke: 0.4rpx #333333;
    }

    .group_6 {
        margin-top: 18rpx;
    }

    .text-wrapper_3 {
        padding: 20rpx 0 16rpx;
        background-color: #f5f6f7;
        border-radius: 10rpx;
        // width: 322rpx;
        width: calc((100vw - 100rpx) / 2);
        height: 62rpx;
        border-left: solid 2rpx #dfdfdf;
        border-right: solid 2rpx #dfdfdf;
        border-top: solid 2rpx #dfdfdf;
        border-bottom: solid 2rpx #dfdfdf;
    }

    .text_14 {
        font-size: 26rpx;
        line-height: 18rpx;
        // -webkit-text-stroke: 0.4rpx #999999;
    }

    .text_15 {
        font-size: 26rpx;
        line-height: 1.92rpx;
    }

    // .ml-9 {
    //     margin-left: 0.56rem;
    // }

    // .font {
    //     font-size: 0.88rem;
    //     font-family: 苹方;
    //     line-height: 0.82rem;
    //     color: #333333;
    // }

    // .section_4 {
    //     padding: 0.88rem 0.63rem;
    //     background-color: #f5f6f7;
    // }

    .section_5 {
        padding: 24rpx 36rpx 24rpx 48rpx;
        background-color: #ffffff;
        border-radius: 10rpx;
        width: 100%;
        height: 74rpx;
        border-left: solid 1rpx #d8d8d8;
        border-right: solid 1rpx #d8d8d8;
        border-top: solid 1rpx #d8d8d8;
        border-bottom: solid 1rpx #d8d8d8;
    }

    .text_5 {
        line-height: 0.82rem;
    }

    .image_9 {
        border-radius: 0.031rem;
        width: 0.56rem;
        height: 0.31rem;
    }

    .section_6 {
        padding: 0.75rem 0;
        background-color: #ffffff;
        border-radius: 1.13rem;
        height: 2.31rem;
    }

    .image_8 {
        width: 0.75rem;
        height: 0.75rem;
    }

    // .font_2 {
    //     font-size: 0.88rem;
    //     font-family: 萍方-简;
    //     line-height: 0.82rem;
    //     color: #333333;
    // }

    // .text_6 {
    //     color: #999999;
    //     line-height: 0.81rem;
    // }
}

.nut-picker {
    height: 50vh;
}

.nut-picker-panel {
    height: 98%;
}

.nut-picker-view-panel {
    height: 98%;
}

.count-list {
    padding: 20rpx;

    .count-list-table {
        background-color: #ffffff;
        border-radius: 16rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
        overflow: hidden;

        .table-header {
            display: flex;
            background-color: #f8faff;
            padding: 24rpx;
            border-bottom: 1rpx solid rgba(230, 235, 245, 0.8);

            .header-cell {
                flex: 1;
                font-size: 28rpx;
                font-weight: 500;
                color: #1f2937;
            }
        }

        .table-row {
            display: flex;
            padding: 24rpx;
            border-bottom: 1rpx solid rgba(230, 235, 245, 0.8);

            &:last-child {
                border-bottom: none;
            }

            .row-cell {
                flex: 1;
                font-size: 26rpx;
                color: #4b5563;

                .cell-top {
                    font-weight: 500;
                    color: #1f2937;
                    margin-bottom: 8rpx;
                }

                .cell-bottom {
                    font-size: 24rpx;
                    color: #6b7280;
                }
            }
        }

        .table-total {
            display: flex;
            padding: 24rpx;
            background-color: #f8faff;
            border-top: 1rpx solid rgba(230, 235, 245, 0.8);

            .total-row-cell {
                flex: 1;
                font-size: 28rpx;
                font-weight: 600;
                color: #1f2937;
            }
        }
    }
}

.text-wrapper {
    padding: 12rpx 24rpx;
    background-color: #3b82f6;
    border-radius: 8rpx;
    transition: all 0.3s ease;

    &:active {
        background-color: #2563eb;
        transform: scale(0.98);
    }

    .font_2 {
        color: #ffffff;
        font-size: 26rpx;
        font-weight: 500;
    }
}

.text-wrapper_2 {
    padding: 12rpx 24rpx;
    background-color: #f3f4f6;
    border-radius: 8rpx;
    transition: all 0.3s ease;

    &:active {
        background-color: #e5e7eb;
        transform: scale(0.98);
    }

    .font_3 {
        color: #4b5563;
        font-size: 26rpx;
    }
}

.divider {
    height: 1rpx;
    background-color: rgba(230, 235, 245, 0.8);
    margin: 24rpx 0;
}
