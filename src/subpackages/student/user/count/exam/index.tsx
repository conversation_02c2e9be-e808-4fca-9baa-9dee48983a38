
import React, { useEffect, useState } from 'react'
import './index.scss'
import '@utils/app.scss'
import MessageApi from "@components/MessageApi/MessageApi";
import IconFont from '@components/iconfont';
import { Button, DatePicker, Empty, Picker, Popup } from '@nutui/nutui-react-taro';
import { nextTick, useDidShow, useReady } from '@tarojs/taro';
import moment from 'moment'
import { tool } from '@utils/tool';
import request from '@service/request';
import Taro from '@tarojs/taro';


const App = () => {
    const message: any = React.useRef();
    const [pageIndex, setPageIndex] = useState(1);
    const [pageSize] = useState(15);
    const [pageCount, setPageCount] = useState(0);

    const [data, setData] = useState<any[]>([]);
    const [dataCount, setDataCount] = useState<any>(0);

    const [searchBarOpen, setSearchBarOpen] = useState(false);
    const [loading, setLoading] = useState(false);

    const [startTimeOpen, setStartTimeOpen] = useState(false);
    const [startTime, setStartTime] = useState('');
    const [endTimeOpen, setEndTimeOpen] = useState(false);
    const [endTime, setEndTime] = useState('');

    const [keMuIds, setKeMuIds] = useState([true, true, true]);


    const [countType, setCountType] = useState<string>();
    const [countTypeList] = useState([
        { text: '考试教练', value: 'TeachUserId' },
        { text: '招生推荐人', value: 'SaleUserId' },
        { text: '考试教练或无教练则推荐人', value: 'TeachdOrSaleUserId' },
        // { text: '学员报名点', value: 'JxDeptId' },
        // { text: '学员训练场', value: 'JxFieldId' },
        // { text: '推荐人报名点', value: 'SaleJxDeptId' },
        // { text: '推荐人训练场', value: 'SaleJxFieldId' },
        // { text: '教练报名点', value: 'TeachUserJxDeptId' },
        // { text: '教练训练场', value: 'TeachUserJxFieldId' },
        // { text: '考试教练或推荐人报名点', value: 'TeachOrSaleJxDeptId' },
        // { text: '考试教练或推荐人训练场', value: 'TeachOrSaleJxFieldId' },
    ]);

    const [countTypeOpen, setCountTypeOpen] = useState(false);


    useDidShow(() => {

        if (tool.data.get('user-count-exam-countType')) {
            setCountType(tool.data.get('user-count-exam-countType'));
        }
        else {
            setCountType('TeachUserId');
        }
        if (tool.data.get('user-count-exam-startTime')) {
            setStartTime(tool.data.get('user-count-exam-startTime'));
        }
        else {
            setStartTime(moment().format('YYYY-MM-DD'));
        }
        if (tool.data.get('user-count-exam-endTime')) {
            setEndTime(tool.data.get('user-count-exam-endTime'));
        }
        else {
            setEndTime(moment().format('YYYY-MM-DD'));
        }
        if (tool.data.get('user-count-exam-kemuids-kemu1') && tool.data.get('user-count-exam-kemuids-kemu2') && tool.data.get('user-count-exam-kemuids-kemu3')) {
            setKeMuIds([tool.data.get('user-count-exam-kemuids-kemu1'), tool.data.get('user-count-exam-kemuids-kemu2'), tool.data.get('user-count-exam-kemuids-kemu3')]);
        }

        setSearchBarOpen(true);
    });

    useReady(() => {
        getViewHeight();
    });


    const [topHeight, setTopHeight] = useState(0);
    const getViewHeight = () => {
        // 创建一个选择器查询实例
        const query = Taro.createSelectorQuery();

        query.select('#search-bar-top').boundingClientRect((rect: any) => {
            if (rect) {
                // rect.height 是元素的高度
                console.log('View Height:', rect.height);
                setTopHeight(rect.height);
            }
        }).exec();
    }

    const search = async () => {
        tool.data.set('user-count-exam-countType', countType);
        tool.data.set('user-count-exam-startTime', startTime);
        tool.data.set('user-count-exam-endTime', endTime);

        tool.data.set('user-count-exam-kemuids-kemu1', keMuIds[0]);
        tool.data.set('user-count-exam-kemuids-kemu2', keMuIds[1]);
        tool.data.set('user-count-exam-kemuids-kemu3', keMuIds[2]);

        setSearchBarOpen(false);

        let KeMuIds: number[] = [];
        if (keMuIds[0] == true) {
            KeMuIds.push(1);
        }
        if (keMuIds[1] == true) {
            KeMuIds.push(2);
        }
        if (keMuIds[2] == true) {
            KeMuIds.push(3);
        }

        if (KeMuIds.length == 0) {
            message.current.openToast('选择一个科目');
        } else {
            message.current.openLoading('正在加载数据');
            request.post<any>('/Jx/Exam/Count/getResultCount', {
                ksrqs: [startTime, endTime],
                KeMuIds: KeMuIds,
                CountType: countType,
                pageSize: 99999
            }).then(json => {
                message.current.closeLoading();

                if (json && json.success) {
                    setData(json.data.data);
                }
                else {
                    message.current.openDialog('统计失败', json.message);
                }
            });
        }
    }
    return (
        <>
            <MessageApi ref={message}></MessageApi>
            <view
                className='search-bar-popup'
                style={{
                    backgroundColor: '#fff',
                    position: 'fixed',
                    top: '0px',
                    width: '100%',
                    zIndex: 1002
                }}>
                <view className="flex-col search-bar"
                    id={'search-bar-top'}
                    style={{
                        position: 'fixed',
                        top: `0px`,
                        width: '100%',
                        backgroundColor: '#fff',
                        paddingLeft: '20rpx',
                        paddingRight: '20rpx'
                    }}>
                    <view className="flex-col flex-1 group_2" style={{
                        paddingBottom: '20rpx',
                        borderRadius: '0 24rpx 0',
                        color: '#1a1a1a',
                        fontSize: '14px'
                    }}>
                        <text className="self-start font text_13 mt-10">自定义日期筛选</text>
                        <view className="flex-row justify-center self-stretch group_6">
                            <view className="flex-col justify-start items-center text-wrapper_3" onClick={() => {
                                if (!searchBarOpen) {
                                    setSearchBarOpen(true);
                                } else {
                                    setEndTimeOpen(false);
                                    setStartTimeOpen(true);
                                }
                            }}>
                                <text className="font_4 text_14">{startTime == '' ? '选择开始日期' : startTime}</text>
                            </view>
                            <text className="self-center font_2 text_9 text_15" style={{
                                width: '50rpx',
                                textAlign: 'center'
                            }}>-</text>
                            <view className="flex-col justify-start items-center text-wrapper_3" onClick={() => {
                                if (!searchBarOpen) {
                                    setSearchBarOpen(true);
                                } else {
                                    setStartTimeOpen(false);
                                    setEndTimeOpen(true);
                                }
                            }}>
                                <text className="font_4 text_14">{endTime == '' ? '选择结束日期' : endTime}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view className="list flex-col safe__area" id='primaryScroll' style={{
                overflowY: 'auto',
                marginTop: `${topHeight}px`,
            }}>
                {
                    !loading && data.length > 0 &&
                    <>
                        <view className='count-list'>
                            <view className='count-list-table'>
                                <view className="table-header">
                                    <view className="header-cell">
                                        {
                                            countType == 'TeachUserId' ? '教练' :
                                                countType == 'SaleUserId' ? '推荐人' :
                                                    countType == 'JxDeptId' ? '报名点' :
                                                        countType == 'JxFieldId' ? '训练场' :
                                                            countType == 'SaleJxDeptId' ? '推荐人店面' :
                                                                countType == 'SaleJxFieldId' ? '推荐人训练场' : ''
                                        }</view>
                                    <view className="header-cell">合格</view>
                                    <view className="header-cell">总数</view>
                                    <view className="header-cell">比例</view>
                                    <view className="header-cell">待考</view>
                                </view>
                                {
                                    data.map((item) => {
                                        return (
                                            <view key={item.id} className="table-row">
                                                <view className="row-cell">
                                                    {
                                                        (countType == 'TeachUserId' || countType == 'SaleUserId' || countType == 'TeachdOrSaleUserId') ?
                                                            <>
                                                                <view className="cell-top">{item.TeachUserName}</view>
                                                                <view className="cell-bottom">{item.TeachUserJxDeptName}</view>
                                                            </> :
                                                            countType == 'JxDeptId' ? item.JxDeptName :
                                                                countType == 'JxFieldId' ? item.JxFieldName :
                                                                    countType == 'SaleJxDeptId' ? item.SaleUserJxDeptName :
                                                                        countType == 'SaleJxFieldId' ? item.SaleUserJxFieldName : ''
                                                    }
                                                </view>
                                                <view className="row-cell">{item.PassCount}</view>
                                                <view className="row-cell">{item.Count}</view>
                                                <view className="row-cell">{((item.PassCount / item.Count) * 100.00).toFixed(2)} %</view>
                                                <view className="row-cell">{item.WaitCount}</view>
                                            </view>
                                        )
                                    })
                                }
                                <view className="table-total">
                                    <view className="total-row-cell">合计</view>
                                    <view className="total-row-cell">{data.reduce((sum, item) => sum + item.PassCount, 0)}</view>
                                    <view className="total-row-cell">{data.reduce((sum, item) => sum + item.Count, 0)}</view>
                                    <view className="total-row-cell">{(((parseFloat(data.reduce((sum, item) => sum + item.PassCount, 0)) / parseFloat(data.reduce((sum, item) => sum + item.Count, 0)))) * 100.00).toFixed(2)} %</view>
                                    <view className="total-row-cell">{data.reduce((sum, item) => sum + item.WaitCount, 0)}</view>
                                </view>
                            </view>
                        </view>
                    </>
                }
                {
                    !loading && data.length == 0 &&
                    <Empty description="无数据" style={{ margin: '0px' }} />
                }
                {
                    loading &&
                    <view style={{
                        padding: '40rpx 0 30rpx',
                        textAlign: 'center',
                        width: '100%',
                        fontSize: '24rpx'
                    }}>
                        <view className="flex-row justify-evenly items-center">
                            <view className="section_4_bottom"></view>
                            <text className="font_4 text_13_bottom">正在加载</text>
                            <view className="section_5_bottom"></view>
                        </view>
                    </view>
                }
            </view >

            <Popup
                className='search-bar-popup'
                visible={searchBarOpen}
                position="top"
                style={{
                    width: '100%',
                    position: 'fixed', top: `${topHeight}px`,
                    // top: `${topHeight}px`,
                }}
                round
                onClose={() => {
                    setSearchBarOpen(false)
                }}
            >
                <>
                    <view className="flex-row justify-between items-center mt-6" style={{
                        width: '100%'
                    }}>
                        <view className="flex-col section_3">
                            {/* <text className="self-start font text_7">时间范围</text> */}
                            <view className="flex-row self-stretch">
                                <view className={(startTime == moment().format('YYYY-MM-DD') && endTime == moment().format('YYYY-MM-DD')) ? 'flex-col justify-start items-center text-wrapper' : 'flex-col justify-start items-center text-wrapper_2'} onClick={() => {
                                    setStartTime(moment().format('YYYY-MM-DD'));
                                    setEndTime(moment().format('YYYY-MM-DD'));
                                }}>
                                    <text className={(startTime == moment().format('YYYY-MM-DD') && endTime == moment().format('YYYY-MM-DD')) ? 'font_2 text_8' : 'font_3 text_9 text_10'}>今日</text>
                                </view>

                                <view className={(startTime == moment().subtract(1, 'days').format('YYYY-MM-DD') && endTime == moment().subtract(1, 'days').format('YYYY-MM-DD')) ? 'ml-12 flex-col justify-start items-center text-wrapper' : 'ml-12 flex-col justify-start items-center text-wrapper_2'} onClick={() => {
                                    setStartTime(moment().subtract(1, 'days').format('YYYY-MM-DD'));
                                    setEndTime(moment().subtract(1, 'days').format('YYYY-MM-DD'));
                                }}>
                                    <text className={(startTime == moment().subtract(1, 'days').format('YYYY-MM-DD') && endTime == moment().subtract(1, 'days').format('YYYY-MM-DD')) ? 'font_2 text_8' : 'font_3 text_9 text_10'}>昨日</text>
                                </view>

                                <view className={(startTime == moment().subtract(7, 'days').format('YYYY-MM-DD') && endTime == moment().format('YYYY-MM-DD')) ? 'ml-12 flex-col justify-start items-center text-wrapper' : 'ml-12 flex-col justify-start items-center text-wrapper_2'} onClick={() => {
                                    setStartTime(moment().subtract(7, 'days').format('YYYY-MM-DD'));
                                    setEndTime(moment().format('YYYY-MM-DD'));
                                }}>
                                    <text className={(startTime == moment().subtract(7, 'days').format('YYYY-MM-DD') && endTime == moment().format('YYYY-MM-DD')) ? 'font_2 text_8' : 'font_3 text_9 text_10'}>近七日</text>
                                </view>
                                {
                                    moment().date() > 10 &&
                                    <view className={(startTime == moment().format('YYYY-MM-01') && endTime == moment().format('YYYY-MM-DD')) ? 'ml-12 flex-col justify-start items-center text-wrapper' : 'ml-12 flex-col justify-start items-center text-wrapper_2'} onClick={() => {
                                        setStartTime(moment().format('YYYY-MM-01'));
                                        setEndTime(moment().format('YYYY-MM-DD'));
                                    }}>
                                        <text className={(startTime == moment().format('YYYY-MM-01') && endTime == moment().format('YYYY-MM-DD')) ? 'font_2 text_8' : 'font_3 text_9 text_10'}>本月</text>
                                    </view>
                                }
                                {
                                    moment().date() <= 10 &&
                                    <view className={(startTime == moment().subtract(1, 'months').format('YYYY-MM-01') && endTime == moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD')) ? 'ml-12 flex-col justify-start items-center text-wrapper' : 'ml-12 flex-col justify-start items-center text-wrapper_2'} onClick={() => {
                                        setStartTime(moment().subtract(1, 'months').format('YYYY-MM-01'));
                                        setEndTime(moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD'));
                                    }}>
                                        <text className={(startTime == moment().subtract(1, 'months').format('YYYY-MM-01') && endTime == moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD')) ? 'font_2 text_8' : 'font_3 text_9 text_10'}>上月</text>
                                    </view>
                                }
                            </view>
                            <view className="self-stretch divider"></view>
                            <text className="self-start font text_13 mt-10">考试科目</text>
                            <view className="flex-row self-stretch group_5">
                                <view className={keMuIds[0] ? 'flex-col justify-start items-center text-wrapper' : 'flex-col justify-start items-center text-wrapper_2'} onClick={() => {
                                    setKeMuIds([!keMuIds[0], keMuIds[1], keMuIds[2]]);
                                }}>
                                    <text className={keMuIds[0] ? 'font_2 text_8' : 'font_3 text_9 text_10'}>科一</text>
                                </view>
                                <view className={keMuIds[1] ? 'ml-12 flex-col justify-start items-center text-wrapper' : 'ml-12 flex-col justify-start items-center text-wrapper_2'} onClick={() => {
                                    setKeMuIds([keMuIds[0], !keMuIds[1], keMuIds[2]]);
                                }}>
                                    <text className={keMuIds[1] ? 'font_2 text_8' : 'font_3 text_9 text_10'}>科二</text>
                                </view>
                                <view className={keMuIds[2] ? 'ml-12 flex-col justify-start items-center text-wrapper' : 'ml-12 flex-col justify-start items-center text-wrapper_2'} onClick={() => {
                                    setKeMuIds([keMuIds[0], keMuIds[1], !keMuIds[2]]);
                                }}>
                                    <text className={keMuIds[2] ? 'font_2 text_8' : 'font_3 text_9 text_10'}>科三</text>
                                </view>
                            </view>


                            <view className="self-stretch divider"></view>
                            <text className="self-start font text_13 mt-10">统计方式</text>
                            <view className="flex-row self-stretch group_5"
                                onClick={() => {
                                    setCountTypeOpen(true);
                                }}>
                                <view className="flex-row items-center shrink-0 section_5"
                                    style={{
                                        textAlign: 'right'
                                    }}>
                                    <span className="font text_5">{countTypeList.find(item => item.value === countType)?.text}</span>
                                    <IconFont name='Arrow-Right2' size={30} style={{}} />
                                </view>
                            </view>

                            <view className="self-stretch divider"></view>

                            <view className="flex-row justify-between items-center mt-20" style={{
                                width: '100%',
                            }}>
                                <Button block type="primary" onClick={() => {
                                    setData([]);
                                    search();
                                }}>
                                    确认
                                </Button>
                            </view>
                        </view>
                    </view>
                </>
            </Popup>
            <view className='search-bar-popup'>
                {
                    countType &&
                    <Picker
                        title="选择统计类型"
                        visible={countTypeOpen}
                        options={countTypeList}
                        defaultValue={[countType]}
                        onConfirm={(list, values: string[]) => {
                            setCountType(values[0])
                        }}
                        onClose={() => setCountTypeOpen(false)}
                    />
                }
                {
                    !countType &&
                    <Picker
                        title="选择统计类型"
                        visible={countTypeOpen}
                        options={countTypeList}
                        onConfirm={(list, values: string[]) => {
                            setCountType(values[0])
                        }}
                        onClose={() => setCountTypeOpen(false)}
                    />
                }
                <DatePicker
                    title="选择查询日期"
                    visible={startTimeOpen}
                    value={startTime == '' ? new Date() : new Date(startTime)}
                    showChinese
                    onClose={() => setStartTimeOpen(false)}
                    threeDimensional={true}
                    onChange={() => {
                    }}
                    onConfirm={(options, values) => {
                        setStartTime(moment(`${values[0]}-${values[1]}-${values[2]}`).format('YYYY-MM-DD'));
                    }}
                    startDate={new Date(moment().subtract(5, 'years').format('YYYY-01-01'))}
                    // endDate={new Date()}
                    endDate={new Date(moment().format('YYYY-12-31'))}
                />
                <DatePicker
                    title="选择查询日期"
                    visible={endTimeOpen}
                    value={endTime == '' ? new Date() : new Date(endTime)}
                    showChinese
                    onClose={() => setEndTimeOpen(false)}
                    threeDimensional={true}
                    onChange={(options, values) => {
                        console.log(options)
                        console.log(values)
                    }}
                    onConfirm={(options, values) => {
                        setEndTime(moment(`${values[0]}-${values[1]}-${values[2]}`).format('YYYY-MM-DD'));
                    }}
                    startDate={new Date(moment().subtract(5, 'years').format('YYYY-01-01'))}
                    // endDate={new Date()}
                    endDate={new Date(moment().format('YYYY-12-31'))}
                />
            </view>
        </>
    )
};
export default App;
