page {
    background: #f8f8f8;
}

.tui-box {
    width: 100%;
    background: #fafafa;
    // background: #fff;
    box-shadow: 0 3rpx 20rpx rgba(183, 183, 183, 0.1);
    border-radius: 10rpx;
    overflow: hidden;
}

.tui-cell-header {
    width: 100%;
    height: 80rpx;
    padding: 0 26rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
}

.tui-cell-title {
    font-size: 30rpx;
    line-height: 30rpx;
    font-weight: 600;
    color: #333;
}

.tui-flex-wrap {
    flex-wrap: wrap;
    height: auto;
    padding-bottom: 30rpx;
}

.tui-order-list {
    width: 100%;
    height: 134rpx;
    padding: 0 30rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40rpx 54rpx 40rpx 54rpx;
}

.tui-list-cell {
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

.tui-list-cell::after {
    content: "";
    position: absolute;
    border-bottom: 1px solid #eaeef1;
    -webkit-transform: scaleY(0.5) translateZ(0);
    transform: scaleY(0.5) translateZ(0);
    transform-origin: 0 100%;
    bottom: 0;
    right: 0;
    left: 0;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: rgb(234, 238, 241);
}

.tui-list-cell::before {
    content: "";
    position: absolute;
    border-bottom: 1px solid #eaeef1;
    -webkit-transform: scaleY(0.5) translateZ(0);
    transform: scaleY(0.5) translateZ(0);
    transform-origin: 0 100%;
    bottom: 0;
    right: 0;
    left: 0;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: rgb(234, 238, 241);
}

.ul-row {
    display: flex;
    flex: 2;
}

.ul-cell {
    flex: 1;
    margin: 10px;
}

.ul-item {
    height: 500rpx;
}

.thorui-panel {
    // margin-bottom: 30rpx;
    background-color: #fff;
    position: relative;
    overflow: hidden;
}

.thorui-panel__hd {
    // width: 100%;
    padding: 26rpx 30rpx;
    color: var(--thorui-text-color);
    font-size: 32rpx;
    font-weight: 700;
    position: relative;
    display: flex;
    align-items: center;
    text-align: center;
}

.thorui-media-box {
    padding: 32rpx;
    position: relative;
    display: flex;
    align-items: center;
}

.thorui-media-box__hd {
    // margin-right: 32rpx;
    // width: var(--thorui-img-size-lg);
    // height: var(--thorui-img-size-lg);
    width: 100%;
    height: 40vw;

    // background-color: var(--thorui-bg-color);
    image {
        width: 100%;
        // height: auto;
    }
}

.thorui-panel__hd:after {
    content: " ";
    position: absolute;
    bottom: 0;
    right: 0;
    height: 1px;
    border-bottom: 1px solid var(--thorui-line-color);
    transform-origin: 0 100%;
    transform: scaleY(0.5);
    left: 30rpx;
}

.thorui-media-box::after {
    content: " ";
    position: absolute;
    bottom: 0;
    right: 0;
    height: 1px;
    border-bottom: 1px solid var(--thorui-line-color);
    color: var(--thorui-text-color);
    transform-origin: 0 100%;
    transform: scaleY(0.5);
    left: 30rpx;
}

.tui-flex__item {
    margin: 10rpx;
    padding: 0 20rpx;
    text-align: center;
    height: 46rpx;
    line-height: 46rpx;
    background-color: #fff;
    color: #999;
    font-size: 28rpx;
}

.thorui-media-button {
    text-align: center;
    background: #f8f8f8;
    padding: 20rpx 0 0 0;
}
