import Taro, { useReady } from "@tarojs/taro";
import { useState } from "react";
import TopNavBar from "@components/topNavBar";
import { Button, Dialog, Toast } from "@nutui/nutui-react-taro";
import "./index.scss";
import "@utils/app.scss";
import upload from "@service/upload";

import { getLoading, getMsg } from "@utils/util";
import { login } from "@utils/login";
import request from "@service/request";

import { Camera, CoverImage, Image } from "@tarojs/components";

type StudentImageItem = {
    ImageId: string;
    ImagePath: string;
};

const App = () => {
    /**
     * Toast 需要的 State
     */
    const [toastShow, setToastShow] = useState(false);
    const [toast, setToast] = useState<API.pdToast>(Object);

    /**
     * Dialog 需要的 State
     */
    const [dialogShow, setDialogShow] = useState(false);
    const [dialog, setDialog] = useState<API.pdDialog>(Object);

    /**
     * 用户登录的 信息
     */
    const [userInfo, setUserInfo] = useState<API.UserInfo>(Object);

    const [studentImages, setStudentImages] = useState<StudentImageItem[]>([]);

    const [showCamera, setShowCamera] = useState(false);
    const [tempFilePaths, setTempFilePaths] = useState("");
    const [devicePosition, setDevicePosition] = useState("back");

    const [CameraImageId, setCameraImageId] = useState(0);

    const [image2, setImage2] = useState("");
    const [image4, setImage4] = useState("");
    const [image6, setImage6] = useState("");
    const [image7, setImage7] = useState("");

    /**
     * 页面 ready 方法
     */
    useReady(() => {
        login().then((userInfo: API.UserInfo) => {
            setUserInfo(userInfo);
            // 需要登录的时候 再用  这个页面是全局的  会和部分模块冲突 比如 湖南驾协 长沙驾协
            GetStudentImages();
        });
    });

    /**
     * 获取 学员的 图片
     */
    const GetStudentImages = () => {
        request.post<API.Result<StudentImageItem[]>>("/Jx/Image/StudentImage/GetStudentImages", {}).then((json) => {
            setStudentImages(json.data);
            var image2 = json.data.find((m) => m.ImageId == "2");
            setImage2(image2 ? image2.ImagePath : "");
            var image4 = json.data.find((m) => m.ImageId == "4");
            setImage4(image4 ? image4.ImagePath : "");
            var image6 = json.data.find((m) => m.ImageId == "6");
            setImage6(image6 ? image6.ImagePath : "");
            var image7 = json.data.find((m) => m.ImageId == "7");
            setImage7(image7 ? image7.ImagePath : "");
        });
    };

    /**
     *
     * @param ImageId
     */
    const TakePhoto = (ImageId: number) => {
        // MakeIdCard();
        // return;
        // setShowCamera(true);
        // setTempFilePaths('');

        if (ImageId == 4) {
            ImageId = 40;
        }

        // if (ImageId == 4) {
        //     setCameraImageId(40);
        // } else {
        //     setCameraImageId(ImageId);
        // }
        // Taro.showActionSheet({
        //     itemList: ['拍照上传', '相册选择'],
        //     success(res) {
        //         if (res.tapIndex == 0) {
        //             setShowCamera(true);

        //             if (ImageId == 4) {
        //                 setCameraImageId(40);
        //             } else {
        //                 setCameraImageId(ImageId);
        //             }
        //         }
        //         else {
        //             if (ImageId == 4) {
        //                 setDialog(getMsg("请先上传身份证的正面（人像面）", '上传提示', function () {
        //                     Taro.chooseImage({
        //                         count: 1,
        //                         sizeType: ['compressed'],//['original', 'compressed'],
        //                         sourceType: ['album'], //['album', 'camera'],
        //                         success(res) {
        //                             const tempFilePaths = res.tempFilePaths[0]

        //                         }
        //                     })
        //                 }));
        //                 setDialogShow(true);
        //             } else {
        Taro.chooseImage({
            count: 1,
            sizeType: ["compressed"], //['original', 'compressed'],
            sourceType: ["album"], //['album', 'camera'],
            success(res) {
                Upload(res.tempFilePaths[0], ImageId).then((json) => {
                    GetStudentImages();
                });
            },
        });
        //             }
        //         }
        //     }
        // })
    };

    /**
     * 上传
     * @param filePath
     */
    const Upload = (filePath: any, ImageId: number) => {
        return new Promise(function (resolve, reject) {
            setToast(getLoading("正在上传", () => {}));
            setToastShow(true);
            upload
                .post<
                    API.Result<{
                        xm: string;
                        sfzmhm: string;
                        Photo: string;
                    }>
                >(
                    "/Jx/Image/StudentImage/uploadStudentImage",
                    {
                        ImageId: ImageId,
                    },
                    filePath,
                    "file"
                )
                .then((json) => {
                    setToastShow(false);
                    if (json.success) {
                        if (ImageId == 40) {
                            setTempFilePaths("");
                            setShowCamera(true);
                            setCameraImageId(41);
                        } else if (ImageId == 41) {
                            MakeIdCard();
                        } else {
                            json && json.message && setDialog(getMsg(json.message, "上传完成"));
                            json && json.message && setDialogShow(true);
                            GetStudentImages();
                        }
                        resolve(json);
                    } else {
                        setDialog(getMsg(json.message));
                        setDialogShow(true);
                        reject(json);
                    }
                });
        });
    };

    /**
     * 合成 身份证的 正反面
     */
    const MakeIdCard = () => {
        setToast(getLoading("正在合成图片", () => {}));
        setToastShow(true);

        request.post<API.Result<string>>("/Jx/Image/StudentImage/makeMyIdCard", {}).then((json) => {
            setToastShow(false);
            if (json.success) {
                json && json.message && setDialog(getMsg(json.message, "生成完成"));
                json && json.message && setDialogShow(true);

                GetStudentImages();
            } else {
                json && setDialog(getMsg(json.message, "生成失败"));
                json && setDialogShow(true);
            }
        });
    };
    return (
        <view
            style={{
                paddingBottom: "calc(env(safe-area-inset-bottom))",
            }}
        >
            {showCamera && (
                <>
                    {tempFilePaths == "" && (
                        <view
                            style={{
                                width: "100vw",
                                height: "100vh",
                                backgroundColor: "#000",
                            }}
                        >
                            <Camera
                                mode="normal"
                                devicePosition={devicePosition == "back" ? "back" : "front"}
                                className="camera"
                                flash={"off"}
                                style={{
                                    position: "absolute",
                                    height: "75vw",
                                    width: "100vw",
                                    top: "15vh",
                                    zIndex: 9999,
                                }}
                            >
                                {CameraImageId == 2 && (
                                    <view
                                        style={{
                                            width: "100%",
                                            textAlign: "center",
                                        }}
                                    >
                                        <CoverImage
                                            src="https://cdn.51panda.com/WxAppImage/takePhoto/outline.png"
                                            style={{
                                                margin: "0 auto",
                                                width: "50%",
                                                height: "auto",
                                                paddingTop: "10vw",
                                            }}
                                        ></CoverImage>
                                    </view>
                                )}
                                {(CameraImageId == 6 || CameraImageId == 7) && (
                                    <CoverImage
                                        src="https://cdn.51panda.com/WxAppImage/takePhoto/camera_1.png"
                                        style={{
                                            marginLeft: "5vw",
                                            width: "98%",
                                            height: "auto",
                                            paddingTop: "1vw",
                                        }}
                                    ></CoverImage>
                                )}
                                {CameraImageId == 40 && (
                                    <CoverImage
                                        src="https://cdn.51panda.com/WxAppImage/takePhoto/camera_idcard_front.png"
                                        style={{
                                            marginLeft: "5vw",
                                            width: "90%",
                                            height: "auto",
                                            paddingTop: "10vw",
                                        }}
                                    ></CoverImage>
                                )}
                                {CameraImageId == 41 && (
                                    <CoverImage
                                        src="https://cdn.51panda.com/WxAppImage/takePhoto/camera_idcard_back.png"
                                        style={{
                                            marginLeft: "5vw",
                                            width: "90%",
                                            height: "auto",
                                            paddingTop: "10vw",
                                        }}
                                    ></CoverImage>
                                )}
                            </Camera>
                            <view
                                style={{
                                    position: "absolute",
                                    width: "100%",
                                    height: "150rpx",
                                    bottom: "calc(100rpx + env(safe-area-inset-bottom))",
                                    zIndex: 999,
                                }}
                            >
                                <view
                                    style={{
                                        display: "flex",
                                    }}
                                >
                                    <view
                                        onClick={() => {
                                            setShowCamera(false);
                                        }}
                                        style={{
                                            flex: "1",
                                            textAlign: "center",
                                            padding: "60rpx 60rpx 120rpx 60rpx",
                                        }}
                                    >
                                        <CoverImage src="https://cdn.51panda.com/WxAppImage/takePhoto/cancel.png"></CoverImage>
                                    </view>
                                    <view
                                        onClick={() => {
                                            const ctx = Taro.createCameraContext();
                                            ctx.takePhoto({
                                                quality: "high",
                                                success: (res) => {
                                                    setTempFilePaths(res.tempImagePath);
                                                },
                                            });
                                        }}
                                        style={{
                                            flex: "1.5",
                                            textAlign: "center",
                                            padding: "30rpx 60rpx 90rpx 60rpx",
                                        }}
                                    >
                                        <CoverImage src="https://cdn.51panda.com/WxAppImage/takePhoto/bnt.png"></CoverImage>
                                    </view>
                                    <view
                                        onClick={() => {
                                            if (devicePosition == "back") {
                                                setDevicePosition("front");
                                            } else {
                                                setDevicePosition("back");
                                            }
                                        }}
                                        style={{
                                            flex: "1",
                                            textAlign: "center",
                                            padding: "60rpx 60rpx 120rpx 60rpx",
                                        }}
                                    >
                                        <CoverImage src="https://cdn.51panda.com/WxAppImage/takePhoto/switch.png"></CoverImage>
                                    </view>
                                </view>
                            </view>
                        </view>
                    )}
                    {tempFilePaths != "" && (
                        <view
                            style={{
                                height: "100vh",
                                width: "100vw",
                                backgroundColor: "#000",
                                paddingTop: "15vh",
                            }}
                        >
                            <Image
                                src={tempFilePaths}
                                mode="widthFix"
                                style={{
                                    width: "100vw",
                                }}
                            ></Image>
                            <view
                                style={{
                                    position: "absolute",
                                    width: "100%",
                                    height: "150rpx",
                                    bottom: "calc(100rpx + env(safe-area-inset-bottom))",
                                    zIndex: 999,
                                }}
                            >
                                <view
                                    style={{
                                        display: "flex",
                                    }}
                                >
                                    <view
                                        onClick={() => {
                                            setTempFilePaths("");
                                        }}
                                        style={{
                                            flex: "1",
                                            textAlign: "center",
                                            padding: "30rpx 120rpx 90rpx 120rpx",
                                        }}
                                    >
                                        <CoverImage src="https://cdn.51panda.com/WxAppImage/takePhoto/cancel.png"></CoverImage>
                                    </view>
                                    <view
                                        onClick={() => {
                                            setShowCamera(false);
                                            Upload(tempFilePaths, CameraImageId);
                                        }}
                                        style={{
                                            flex: "1",
                                            textAlign: "center",
                                            padding: "30rpx 120rpx 90rpx 120rpx",
                                        }}
                                    >
                                        <CoverImage src="https://cdn.51panda.com/WxAppImage/takePhoto/confirm.png"></CoverImage>
                                    </view>
                                </view>
                            </view>
                        </view>
                    )}
                </>
            )}
            {!showCamera && (
                <>
                    <TopNavBar title="受理照片"></TopNavBar>
                    <view className="container">
                        <view className="ul-row thorui-flex">
                            <view className="thorui-panel ul-cell thorui-flex__item">
                                <view className="thorui-panel__hd">现场照片</view>
                                <view className="thorui-media-box">
                                    <view
                                        className="thorui-media-box__hd"
                                        style={{
                                            backgroundImage: `url('${image2}')`,
                                            backgroundSize: "100%,auto",
                                            backgroundRepeat: "no-repeat",
                                        }}
                                    ></view>
                                </view>
                                <view className="thorui-media-button">
                                    <Button
                                        type="info"
                                        shape={"square"}
                                        onClick={() => {
                                            TakePhoto(2);
                                        }}
                                        style={{
                                            width: "100%",
                                        }}
                                    >
                                        拍照上传
                                    </Button>
                                </view>
                            </view>
                            <view className="thorui-panel ul-cell thorui-flex__item">
                                <view className="thorui-panel__hd ">身份证正反面</view>
                                <view className="thorui-media-box">
                                    <view
                                        className="thorui-media-box__hd"
                                        style={{
                                            backgroundImage: `url('${image4}')`,
                                            backgroundSize: "100%,auto",
                                            backgroundRepeat: "no-repeat",
                                        }}
                                    ></view>
                                </view>
                                <view className="thorui-media-button">
                                    <Button
                                        type="info"
                                        shape={"square"}
                                        onClick={() => {
                                            TakePhoto(4);
                                        }}
                                        style={{
                                            width: "100%",
                                        }}
                                    >
                                        拍照上传
                                    </Button>
                                </view>
                            </view>
                        </view>
                        <view className="ul-row thorui-flex">
                            <view className="thorui-panel ul-cell thorui-flex__item">
                                <view className="thorui-panel__hd">申请表照片</view>
                                <view className="thorui-media-box">
                                    <view
                                        className="thorui-media-box__hd"
                                        style={{
                                            backgroundImage: `url('${image6}')`,
                                            backgroundSize: "100%,auto",
                                            backgroundRepeat: "no-repeat",
                                        }}
                                    ></view>
                                </view>
                                <view className="thorui-media-button">
                                    <Button
                                        type="info"
                                        shape={"square"}
                                        onClick={() => {
                                            TakePhoto(6);
                                        }}
                                        style={{
                                            width: "100%",
                                        }}
                                    >
                                        拍照上传
                                    </Button>
                                </view>
                            </view>
                            <view className="thorui-panel ul-cell thorui-flex__item">
                                <view className="thorui-panel__hd">体检表照片</view>
                                <view className="thorui-media-box">
                                    <view
                                        className="thorui-media-box__hd"
                                        style={{
                                            backgroundImage: `url('${image7}')`,
                                            backgroundSize: "100%,auto",
                                            backgroundRepeat: "no-repeat",
                                        }}
                                    ></view>
                                </view>
                                <view className="thorui-media-button">
                                    <Button
                                        type="info"
                                        shape={"square"}
                                        onClick={() => {
                                            TakePhoto(7);
                                        }}
                                        style={{
                                            width: "100%",
                                        }}
                                    >
                                        拍照上传
                                    </Button>
                                </view>
                            </view>
                        </view>
                    </view>
                </>
            )}
            {/* Toast 和 Dialog 的通用相关代码 开始 */}
            {toastShow && !dialogShow && (
                <Toast
                    msg={toast.msg}
                    visible={toastShow}
                    type={toast.type}
                    onClose={() => {
                        setToastShow(false);
                        toast.fn();
                    }}
                    // cover={toast.cover}
                    // coverColor="rgba(6, 6, 6, 0.8)"
                    duration={toast.duration}
                    icon=<toast.icon />
                    iconSize="20"
                />
            )}
            {dialogShow && (
                <Dialog
                    closeOnOverlayClick={false}
                    title={dialog.title}
                    confirmText={dialog.okText}
                    hideCancelButton={dialog.noCancelBtn}
                    cancelText={dialog.cancelText}
                    // textAlign={dialog.textAlign}
                    visible={dialogShow}
                    lockScroll
                    footerDirection="vertical"
                    onConfirm={() => {
                        dialog.ok();
                        setDialogShow(false);
                    }}
                    onCancel={() => {
                        dialog.cancel();
                        setDialogShow(false);
                    }}
                >
                    <view
                        style={{
                            lineHeight: "40rpx",
                        }}
                    >
                        {dialog.msg}
                    </view>
                </Dialog>
            )}
            {/* Toast 和 Dialog 的通用相关代码 结束 */}
        </view>
    );
};
export default App;
