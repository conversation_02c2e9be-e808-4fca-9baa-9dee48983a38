import Taro, { useDidShow } from "@tarojs/taro";
import { useRef, useState } from "react";
import TopNavBar from "@components/topNavBar";
import { Button, Cell, CellGroup, Dialog, Input, Toast } from "@nutui/nutui-react-taro";
import "./index.scss";
import "@utils/app.scss";

import { getDialog, getLoading, getMsg, QValue } from "@utils/util";
import { login } from "@utils/login";
import request from "@service/request";
import { Image, Picker } from "@tarojs/components";
import PdAgreement from "@components/pdAgreemen";

import moment from "moment";

const App = () => {
    /**
     * Toast 需要的 State
     */
    const [toastShow, setToastShow] = useState(false);
    const [toast, setToast] = useState<API.pdToast>(Object);

    /**
     * Dialog 需要的 State
     */
    const [dialogShow, setDialogShow] = useState(false);
    const [dialog, setDialog] = useState<API.pdDialog>(Object);

    /**
     * 用户登录的 信息
     */
    const [userInfo, setUserInfo] = useState<API.UserInfo>(Object);

    /**
     * 当前步骤 Index
     */
    const [StepIndex, setStepIndex] = useState(0);

    const pdAgreemen = useRef<any>(null);

    /**
     * 当前扫码进来的用户
     */
    const [UserId, setUserId] = useState("");

    const [JxDeptName, setJxDeptName] = useState("");
    const [JxDeptId, setJxDeptId] = useState("");

    /**
     * 培训场地
     */
    const [JxFieldList, setJxFieldList] = useState<any[]>([]);
    const [JxFieldNameList, setJxFieldNameList] = useState<any[]>([]);
    const [JxFieldIndex, setJxFieldIndex] = useState(-1);
    // const [JxFieldOpen, setJxFieldOpen] = useState(false);

    /**
     * 车型
     */
    const [CarTypeList, setCarTypeList] = useState<any[]>([]);
    const [CarTypeNameList, setCarTypeNameList] = useState<any[]>([]);
    const [CarTypeIndex, setCarTypeIndex] = useState(-1);
    // const [CarTypeOpen, setCarTypeOpen] = useState(false);

    /**
     * 班型
     */
    const [JxClassList, setJxClassList] = useState<any[]>([]);
    const [JxClassNameList, setJxClassNameList] = useState<any[]>([]);
    const [JxClassIndex, setJxClassIndex] = useState(-1);
    // const [JxClassOpen, setJxClassOpen] = useState(false);

    const [xm, setxm] = useState("");

    /**
     * 证件类型
     */
    const [sfzmmcList, setsfzmmcList] = useState<any[]>([]);
    const [sfzmmcNameList, setsfzmmcNameList] = useState<any[]>([]);
    const [sfzmmcIndex, setsfzmmcIndex] = useState(0);
    // const [sfzmmcOpen, setsfzmmcOpen] = useState(false);

    const [sfzmhm, setsfzmhm] = useState("");

    /**
     * 性别
     */
    const [xbList] = useState<any[]>([
        { value: 1, label: "男" },
        { value: 2, label: "女" },
    ]);
    const [xbNameList] = useState<any[]>(["男", "女"]);
    const [xbIndex, setxbIndex] = useState(-1);
    // const [xbOpen, setxbOpen] = useState(false);

    const [yddh, setyddh] = useState("");

    /**
     * 出生日期
     */
    const [csrq, setcsrq] = useState("");

    /**
     * 页面 ready 方法
     */
    useDidShow(() => {
        let UserId = "";
        if (QValue("UserId")) {
            UserId = QValue("UserId");
            setUserId(UserId);
        }

        login().then((_userInfo: API.UserInfo) => {
            setUserInfo(_userInfo);
            if (UserId !== "" && UserId) {
                GetJxDeptInfo(UserId);
            } else {
                setDialog(
                    getMsg("请重新扫码进入!", "扫码错误", () => {
                        // Taro.navigateTo({
                        //     url: '/subpackages/csjpxh/student/contract/complete/index'
                        // })
                    })
                );
                setDialogShow(true);
            }

            console.log(userInfo);
        });

        ///测试时候用的
        // UserId = '490FE538-1DC1-4040-A549-DA82C74D1F30';
        // setUserId(UserId);
        // setxm('雷达');
        // setsfzmhm('43010319830814351X');
        // setyddh('18973163677');

        // setStepIndex(1);
        // getSfzmmcSelectList();

        // setStepIndex(2);
    });

    /**
     * 获取 当前的 扫码进入的账户 锁绑定的报名点的信息
     */
    const GetJxDeptInfo = (UserId: string) => {
        request
            .post<
                API.Result<{
                    Name: string;
                    Id: string;
                }>
            >("/JiaXiao/WxStudent/GetJxDeptInfo", {
                UserId: UserId,
            })
            .then((json) => {
                if (json && json.success) {
                    setJxDeptName(json.data.Name);
                    setJxDeptId(json.data.Id);
                    GetJxFieldSelectList(UserId, json.data.Id);
                } else {
                    setDialog(
                        getMsg(json.message, "扫码错误", () => {
                            // Taro.navigateTo({
                            //     url: '/subpackages/csjpxh/student/contract/complete/index'
                            // })
                        })
                    );
                    setDialogShow(true);
                }
            })
            .catch((error) => {
                console.log(error);
                setDialog(
                    getMsg("请重新扫码进入!", "扫码错误", () => {
                        // Taro.navigateTo({
                        //     url: '/subpackages/csjpxh/student/contract/complete/index'
                        // })
                    })
                );
                setDialogShow(true);
            });
    };

    /**
     * 获取 培训场地的 下拉
     */
    const GetJxFieldSelectList = (UserId: string, JxDeptId: string) => {
        request
            .post<API.SelectResult>("/JiaXiao/WxStudent/GetJxFieldSelectList", {
                UserId: UserId,
            })
            .then((json) => {
                if (json && json.success) {
                    setJxFieldList(json.data);

                    var jxFieldNameList: string[] = [];
                    json.data.forEach((item: any) => {
                        jxFieldNameList.push(item.label);
                    });

                    setJxFieldNameList(jxFieldNameList);

                    if (json.data.length == 0) {
                        getCarTypeSelectList(JxDeptId, undefined);
                    }
                } else {
                    setDialog(
                        getMsg(json.message, "扫码错误", () => {
                            // Taro.navigateTo({
                            //     url: '/subpackages/csjpxh/student/contract/complete/index'
                            // })
                        })
                    );
                    setDialogShow(true);
                }
            })
            .catch((error) => {
                console.log(error);
                setDialog(
                    getMsg("请重新扫码进入!", "扫码错误", () => {
                        // Taro.navigateTo({
                        //     url: '/subpackages/csjpxh/student/contract/complete/index'
                        // })
                    })
                );
                setDialogShow(true);
            });
    };

    /**
     * 通过相关参数 获取 可报名的 车型的信息
     * @param JxDeptId
     * @param JxFieldId
     */
    const getCarTypeSelectList = (JxDeptId: string, JxFieldId: any) => {
        setCarTypeList([]);
        setCarTypeNameList([]);
        setCarTypeIndex(-1);
        request
            .post<API.SelectResult>("/JiaXiao/WxStudent/GetCarTypeSelectList", {
                JxDeptId: JxDeptId,
                JxFieldId: JxFieldId,
            })
            .then((json) => {
                if (json && json.success) {
                    setCarTypeList(json.data);

                    var carTypeNameList: string[] = [];
                    json.data.forEach((item: any) => {
                        carTypeNameList.push(item.label);
                    });

                    setCarTypeNameList(carTypeNameList);
                } else {
                    setDialog(
                        getMsg(json.message, "扫码错误", () => {
                            // Taro.navigateTo({
                            //     url: '/subpackages/csjpxh/student/contract/complete/index'
                            // })
                        })
                    );
                    setDialogShow(true);
                }
            })
            .catch((error) => {
                console.log(error);
                setDialog(
                    getMsg("请重新扫码进入!", "扫码错误", () => {
                        // Taro.navigateTo({
                        //     url: '/subpackages/csjpxh/student/contract/complete/index'
                        // })
                    })
                );
                setDialogShow(true);
            });
    };

    /**
     * 通过相关参数 获取 可报名的 班型的信息
     * @param JxDeptId
     * @param JxFieldId
     * @param CarType
     */
    const GetJxClassList = (JxDeptId: string, JxFieldId: any, CarType: any) => {
        setJxClassList([]);
        setJxClassNameList([]);
        setJxClassIndex(-1);

        request
            .post<
                API.Result<
                    {
                        value: string;
                        label: string;
                        PayMoney: number;
                        MinPayMoney: number;
                    }[]
                >
            >("/JiaXiao/WxStudent/GetJxClassSelectList", {
                JxDeptId: JxDeptId,
                JxFieldId: JxFieldId,
                CarType: CarType,
            })
            .then((json) => {
                if (json && json.success) {
                    setJxClassList(json.data);
                    if (json.data.length == 1) {
                        setJxClassIndex(0);
                    }

                    var jxClassNameList: string[] = [];
                    json.data.forEach((item: any) => {
                        jxClassNameList.push(item.label);
                    });

                    setJxClassNameList(jxClassNameList);

                    if (json.data.length == 0) {
                        getCarTypeSelectList(JxDeptId, undefined);
                    }
                } else {
                    setDialog(
                        getMsg(json.message, "扫码错误", () => {
                            // Taro.navigateTo({
                            //     url: '/subpackages/csjpxh/student/contract/complete/index'
                            // })
                        })
                    );
                    setDialogShow(true);
                }
            })
            .catch((error) => {
                console.log(error);
                setDialog(
                    getMsg("请重新扫码进入!", "扫码错误", () => {
                        // Taro.navigateTo({
                        //     url: '/subpackages/csjpxh/student/contract/complete/index'
                        // })
                    })
                );
                setDialogShow(true);
            });
    };

    /**
     * 报名的下一步
     */
    const NextStep = () => {
        if (!pdAgreemen.current.state.agreeChecked) {
            setDialog(
                getMsg("请先阅读并确认同意《用户服务协议与隐私政策》", "协议确认", () => {
                    pdAgreemen.current.state.showDetail = true;
                })
            );
            setDialogShow(true);
        } else {
            setStepIndex(1);
            getSfzmmcSelectList();
        }
    };

    /**
     * 获取 证件类型的 列表
     */
    const getSfzmmcSelectList = () => {
        setsfzmmcList([]);
        setsfzmmcNameList([]);
        request
            .post<API.SelectResult>("/Base/Select/getSfzmmcSelectList", {})
            .then((json) => {
                if (json && json.success) {
                    setsfzmmcList(json.data);

                    var sfzmmcNameList: string[] = [];
                    json.data.forEach((item: any) => {
                        sfzmmcNameList.push(item.label);
                    });

                    setsfzmmcNameList(sfzmmcNameList);
                } else {
                    setDialog(getMsg("系统故障，稍后重试!", "操作失败", () => {}));
                    setDialogShow(true);
                }
            })
            .catch(() => {
                setDialog(getMsg("系统故障，稍后重试!", "操作失败", () => {}));
                setDialogShow(true);
            });
    };

    /**
     * 报名并支付
     */
    const addAndPay = () => {
        setDialog(
            getDialog("确认操作", "确认是否提交资料并完成支付?", "确认", "取消", () => {
                setToast(getLoading("正在提交数据", () => {}));
                setToastShow(true);
                request
                    .post<
                        API.Result<{
                            OutTable: number;
                            OutId: string;
                            AccountId: string;
                            PayMoney: number;
                        }>
                    >("/JiaXiao/WxStudent/createOrder", {
                        UserId: UserId,
                        JxFieldId: JxFieldList.length > 0 ? JxFieldList[JxFieldIndex].value : undefined,
                        JxClassId: JxClassList[JxClassIndex].value,
                        CarType: CarTypeList[CarTypeIndex].value,
                        xm: xm,
                        sfzmmc: sfzmmcList[sfzmmcIndex].value,
                        sfzmhm: sfzmhm,
                        xb: xbList[xbIndex].value,
                        yddh: yddh,
                        csrq: csrq,
                    })
                    .then((json) => {
                        setToastShow(false);
                        if (json && json.success) {
                            setToast(getLoading("正在生成订单", () => {}));
                            setToastShow(true);

                            request
                                .put<
                                    API.Result<{
                                        SignData: {
                                            timeStamp: string;
                                            package: string;
                                            paySign: string;
                                            appId: string;
                                            signType: string;
                                            nonceStr: string;
                                        };
                                        OrderId: string;
                                    }>
                                >("/Pay/WxPay/createOrder", json.data)
                                .then((json) => {
                                    setToastShow(false);

                                    if (json && json.success) {
                                        var SignData = json.data.SignData;
                                        Taro.requestPayment({
                                            timeStamp: SignData.timeStamp,
                                            nonceStr: SignData.nonceStr,
                                            package: SignData.package,
                                            signType: "MD5",
                                            paySign: SignData.paySign,
                                            success: function () {
                                                GetResult(json.data.OrderId);
                                            },
                                            fail: function (res) {
                                                if (res.errMsg === "requestPayment:fail cancel") {
                                                    setDialog(getMsg("当前交易被取消,请重新发起支付", "交易取消"));
                                                    setDialogShow(true);
                                                } else {
                                                    setDialog(getMsg(res.errMsg));
                                                    setDialogShow(true);
                                                }
                                            },
                                        });
                                    } else {
                                        json && setDialog(getMsg(json.message, "支付配置出错"));
                                        json && setDialogShow(true);
                                    }
                                });
                        } else {
                            json && setDialog(getMsg(json.message, "操作失败", () => {}));
                            json && setDialogShow(true);
                        }
                    })
                    .catch(() => {
                        setToastShow(false);
                        setDialog(getMsg("系统故障，稍后重试!", "操作失败", () => {}));
                        setDialogShow(true);
                    });
            })
        );
        setDialogShow(true);
    };

    const GetResult = (OrderId: string) => {
        setToast(getLoading("等待支付结果", () => {}));
        setToastShow(true);

        request
            .post<API.Result<{}>>("/Pay/WxPay/GetStatus", {
                Id: OrderId,
            })
            .then((json) => {
                if (json && json.success) {
                    setToastShow(false);
                    setStepIndex(2);
                } else {
                    setTimeout(GetResult, 1000, OrderId);
                }
            });
    };

    return (
        <>
            {(StepIndex == 0 || StepIndex == 1) && (
                <>
                    <view className="tui-top__circle"></view>
                    <view className="tui-bottom__circle"></view>
                    <view className="tui-header__box" style={{ margin: "180rpx 0 0 80rpx" }}>
                        <text className="tui-header__title">在线报名</text>
                        <text className="tui-header__descr">在线报名缴费一气呵成~~</text>
                    </view>
                </>
            )}
            {StepIndex == 0 && (
                <>
                    <view
                        style={{
                            overflow: "auto",
                            margin: "80rpx 30rpx 30rpx 30rpx",
                        }}
                    >
                        <Cell title="报名门店" description={JxDeptName} />
                        {JxFieldList && JxFieldList?.length > 0 && (
                            <Cell
                                title="培训场地"
                                extra={
                                    <>
                                        <Picker
                                            mode="selector"
                                            range={JxFieldNameList}
                                            onChange={(e: any) => {
                                                setJxFieldIndex(parseInt(e.detail.value));
                                                getCarTypeSelectList(JxDeptId, JxFieldList[parseInt(e.detail.value)].value);
                                            }}
                                        >
                                            {JxFieldNameList[JxFieldIndex] ? JxFieldNameList[JxFieldIndex] : "请选择培训场地"}
                                        </Picker>
                                    </>
                                }
                            />
                        )}
                        {
                            CarTypeList && CarTypeList.length > 0 && (
                                <Cell
                                    title="报名车型"
                                    extra={
                                        <>
                                            <Picker
                                                mode="selector"
                                                range={CarTypeNameList}
                                                onChange={(e: any) => {
                                                    setCarTypeIndex(parseInt(e.detail.value));
                                                    GetJxClassList(
                                                        JxDeptId,
                                                        JxFieldList[JxFieldIndex] ? JxFieldList[JxFieldIndex].value : undefined,
                                                        CarTypeList[parseInt(e.detail.value)].value
                                                    );
                                                }}
                                            >
                                                {CarTypeNameList[CarTypeIndex] ? CarTypeNameList[CarTypeIndex] : "请选择培训车型"}
                                            </Picker>
                                        </>
                                    }
                                />
                            )
                            // <Cell title="报名车型" desc={CarTypeList.length == 0 ? '请先选择其他项目' : CarTypeList[CarTypeIndex] ? CarTypeList[CarTypeIndex].text : '请选择培训车型'} onClick={() => {
                            //     setCarTypeOpen(true);
                            // }} />
                        }
                        {
                            CarTypeList[CarTypeIndex] && (
                                <Cell
                                    title="培训班型"
                                    extra={
                                        <>
                                            <Picker
                                                mode="selector"
                                                range={JxClassNameList}
                                                onChange={(e: any) => {
                                                    setJxClassIndex(parseInt(e.detail.value));
                                                    // getCarTypeSelectList(JxDeptId, JxFieldList[parseInt(e.detail.value)].value);
                                                }}
                                            >
                                                {JxClassNameList[JxClassIndex] ? JxClassNameList[JxClassIndex] : "请选择培训班型"}
                                            </Picker>
                                        </>
                                    }
                                />
                            )
                            // <Cell title="培训班型" desc={JxClassList[JxClassIndex] ? JxClassList[JxClassIndex].text : '请选择培训班型'} onClick={() => {
                            //     setJxClassOpen(true);
                            // }} />
                        }

                        {CarTypeList[CarTypeIndex] && JxClassList[JxClassIndex] && (
                            <CellGroup title="报名费用">
                                <Cell title="学费金额" description={JxClassList[JxClassIndex].PayMoney} />
                            </CellGroup>
                        )}

                        <view style={{ padding: "20rpx 0 0 20rpx" }}>
                            <PdAgreement ref={pdAgreemen}></PdAgreement>
                        </view>
                        <view
                            className="footer-placeholder"
                            style={{
                                height: "196rpx",
                            }}
                        ></view>
                    </view>
                    <view className="footer">
                        <view style={{ padding: "10px" }} className={"pandaButton"}>
                            {JxClassList[JxClassIndex] && (
                                <Button block type="primary" size={"large"} onClick={NextStep}>
                                    下一步
                                </Button>
                            )}
                            {!JxClassList[JxClassIndex] && (
                                <Button block type="primary" size={"large"} disabled={true}>
                                    下一步
                                </Button>
                            )}
                        </view>
                    </view>
                </>
            )}
            {StepIndex == 1 && (
                <>
                    <view
                        style={{
                            overflow: "auto",
                            margin: "80rpx 30rpx 30rpx 30rpx",
                        }}
                    >
                        <Cell
                            style={{ padding: 0 }}
                            extra={
                                <Input
                                    placeholder="请输入证件上的姓名"
                                    defaultValue={xm}
                                    onChange={(val) => {
                                        setxm(val);
                                        console.log(val);
                                    }}
                                />
                            }
                        ></Cell>
                        <view className="leftcell">
                            {/* <Cell
                                title="证件类型"
                                descTextAlign='left'
                                desc={sfzmmcList[sfzmmcIndex] ? sfzmmcList[sfzmmcIndex].text : ''}
                                onClick={() => {
                                    setsfzmmcOpen(true);
                                }}
                            /> */}
                            <Cell
                                title="证件类型"
                                extra={
                                    <>
                                        <Picker
                                            style={{
                                                flex: "1",
                                            }}
                                            mode="selector"
                                            range={sfzmmcNameList}
                                            onChange={(e: any) => {
                                                setsfzmmcIndex(parseInt(e.detail.value));
                                            }}
                                        >
                                            {sfzmmcNameList[sfzmmcIndex] ? sfzmmcNameList[sfzmmcIndex] : "请选择证件类型"}
                                        </Picker>
                                    </>
                                }
                            />
                        </view>
                        <Cell style={{ padding: 0 }}>
                            <Input
                                placeholder="请输入证件号码"
                                defaultValue={sfzmhm}
                                type={sfzmmcIndex == 0 ? "idcard" : ""}
                                maxLength={18}
                                onChange={(val) => {
                                    setsfzmhm(val.toUpperCase());

                                    if (sfzmmcIndex == 0) {
                                        if (val.length >= 17) {
                                            if (parseInt(val.substring(16, 17)) % 2 == 0) {
                                                setxbIndex(1);
                                            } else {
                                                setxbIndex(0);
                                            }
                                        }
                                        if (val.length >= 14) {
                                            setcsrq(val.substring(6, 10) + "-" + val.substring(10, 12) + "-" + val.substring(12, 14));
                                        }
                                    }
                                }}
                            />
                        </Cell>
                        <view className="leftcell">
                            {/* <Cell
                                title="性别选择"
                                descTextAlign='left'
                                desc={xbList[xbIndex] ? xbList[xbIndex].text : '请选择性别'}
                                onClick={() => {
                                    setxbOpen(true);
                                }}
                            /> */}
                            <Cell
                                title="性别选择"
                                extra={
                                    <>
                                        <Picker
                                            style={{
                                                flex: "1",
                                            }}
                                            mode="selector"
                                            range={xbNameList}
                                            onChange={(e: any) => {
                                                setxbIndex(parseInt(e.detail.value));
                                            }}
                                        >
                                            {xbNameList[xbIndex] ? xbNameList[xbIndex] : "请选择性别"}
                                        </Picker>
                                    </>
                                }
                            />
                        </view>
                        <Cell style={{ padding: 0 }}>
                            <Input
                                placeholder="请输入您的手机号码"
                                defaultValue={yddh}
                                type={"digit"}
                                onChange={(val) => {
                                    setyddh(val);
                                    console.log(val);
                                }}
                                maxLength={11}
                            />
                        </Cell>
                        <view className="leftcell">
                            {/* <Cell title="出生日期" desc={csrq !== '' ? moment(new Date(csrq)).format('YYYY年MM月DD日') : '请选择您的出生日期'} onClick={() => {
                                setcsrqOpen(true);
                            }} /> */}

                            <Cell
                                title="出生日期"
                                extra={
                                    <>
                                        <Picker
                                            style={{
                                                flex: "1",
                                            }}
                                            mode="date"
                                            start={moment(new Date(new Date().getFullYear() - 90, 1, 1)).format("YYYY-MM-DD")}
                                            end={moment(new Date(new Date().getFullYear() - 15, 1, 1)).format("YYYY-MM-DD")}
                                            value={csrq}
                                            onChange={(e: any) => {
                                                setcsrq(e.detail.value);
                                            }}
                                        >
                                            {csrq != "" ? moment(new Date(csrq)).format("YYYY年MM月DD日") : "请选择生日"}
                                        </Picker>
                                    </>
                                }
                            />
                        </view>
                        <view className="tui-link__box">
                            <view
                                className="tui-link__text tui-color__primary tui-active"
                                onClick={() => {
                                    setStepIndex(0);
                                }}
                            >
                                返回重新选择车型等
                            </view>
                        </view>
                        <view
                            className="footer-placeholder"
                            style={{
                                height: "68px",
                            }}
                        ></view>
                    </view>
                    <view className="footer">
                        <view style={{ padding: "10px" }} className={"pandaButton"}>
                            {xbList[xbIndex] && csrq != "" && xm != "" && sfzmhm != "" && yddh.length == 11 && (
                                <Button block type="primary" size={"large"} onClick={addAndPay}>
                                    确认并缴费
                                </Button>
                            )}
                            {(!xbList[xbIndex] || csrq == "" || xm == "" || sfzmhm == "" || yddh.length != 11) && (
                                <Button block type="primary" size={"large"} disabled={true}>
                                    确认并缴费
                                </Button>
                            )}
                        </view>
                    </view>
                </>
            )}
            {StepIndex == 2 && (
                <>
                    <TopNavBar
                        title="支付完成"
                        leftClick={() => undefined}
                        homeClick={() => {
                            undefined;
                        }}
                        BgColor="#E41F19"
                        FontColor="#fff"
                        hideLeft={false}
                    ></TopNavBar>

                    <view className="success-container">
                        <view className="container">
                            <view className="tui-bg"></view>
                            <view className="tui-content">
                                <view className="tui-success-form">
                                    <Image src="https://cdn.51panda.com/WxAppImage/pay/img_recharge_success.png" className="tui-icon" mode="widthFix"></Image>
                                    <view className="tui-title">订单已支付成功</view>
                                    <view className="tui-sub-title">非常感谢您购买我们的产品服务</view>
                                    <view className="tui-btn-box">
                                        <Button
                                            className="tui-btn-class tui-btn tui-danger-outline tui-outline-fillet"
                                            style={{
                                                width: "240rpx",
                                                height: "70rpx",
                                                lineHeight: "70rpx",
                                                fontSize: "28rpx",
                                                margin: 0,
                                            }}
                                        >
                                            返回首页
                                        </Button>
                                        <Button
                                            className="tui-btn-class tui-btn tui-btn-danger tui-fillet tui-shadow-danger"
                                            style={{
                                                width: "240rpx",
                                                height: "70rpx",
                                                lineHeight: "70rpx",
                                                fontSize: "28rpx",
                                                margin: 0,
                                            }}
                                        >
                                            订单详情
                                        </Button>
                                    </view>
                                </view>
                            </view>
                            <view className="tui-tips">
                                <view className="tui-grey">温馨提示:</view>
                                <view className="tui-light-grey">
                                    付款成功后，我们不会以付款异常、卡单、系统升级为由联系您。请勿泄露银行卡号、手机验证码，否则会造成钱款损失！谨防电话诈骗！
                                </view>
                            </view>
                        </view>
                    </view>
                </>
            )}
            {/* Toast 和 Dialog 的通用相关代码 开始 */}
            {toastShow && !dialogShow && (
                <Toast
                    msg={toast.msg}
                    visible={toastShow}
                    type={toast.type}
                    onClose={() => {
                        setToastShow(false);
                        toast.fn();
                    }}
                    // cover={toast.cover}
                    // coverColor="rgba(6, 6, 6, 0.8)"
                    duration={toast.duration}
                    icon=<toast.icon />
                    iconSize="20"
                />
            )}
            {dialogShow && (
                <Dialog
                    closeOnOverlayClick={false}
                    title={dialog.title}
                    confirmText={dialog.okText}
                    hideCancelButton={dialog.noCancelBtn}
                    cancelText={dialog.cancelText}
                    // textAlign={dialog.textAlign}
                    visible={dialogShow}
                    lockScroll
                    footerDirection="vertical"
                    onConfirm={() => {
                        dialog.ok();
                        setDialogShow(false);
                    }}
                    onCancel={() => {
                        dialog.cancel();
                        setDialogShow(false);
                    }}
                >
                    <view
                        style={{
                            lineHeight: "40rpx",
                        }}
                    >
                        {dialog.msg}
                    </view>
                </Dialog>
            )}
            {/* Toast 和 Dialog 的通用相关代码 结束 */}
        </>
    );
};
export default App;
