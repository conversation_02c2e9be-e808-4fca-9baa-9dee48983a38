.nut-cell-group__title {
    margin-top: 20px;
}

.nut-cell {
    border-radius: 0;
    -webkit-box-shadow: 0;
    box-shadow: 0 0px 0px #ccc;;
}


.bg-img image {
    position: absolute;
    width: 100%;
    height: 440rpx;
    z-index: -1;
    background-image: url('https://cdn.51panda.com/WxAppImage/mine_bg_3x.png');
}

.nut-input {
    border-radius: 6px;
    border-bottom: 0px solid #eaf0fb;
}



.tui-bg {
    width: 100%;
    height: 200rpx;
    padding-top: 0rpx;
    // background: linear-gradient(20deg, #E41F19, #F34B0B);
    background-color: #E41F19;
    border-bottom-left-radius: 42rpx;
}

.tui-content {
    padding: 0 35rpx;
    box-sizing: border-box;
}

.tui-form {
    background: #fff;
    height: 500rpx;
    box-shadow: 0 10rpx 14rpx 0 rgba(0, 0, 0, 0.08);
    border-radius: 10rpx;
    margin-top: -160rpx;
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    flex-direction: column;
}

.tui-icon {
    width: 100rpx;
    height: 100rpx;
    display: block;
    margin-bottom: 60rpx;
}

.tui-title {
    font-size: 42rpx;
    line-height: 42rpx;
    padding-top: 28rpx;
}

.tui-sub-title {
    color: #666666;
    font-size: 28rpx;
    line-height: 28rpx;
    padding-top: 20rpx;
}

.tui-btn-box {
    width: 580rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 88rpx;
}

.tui-tips {
    font-size: 26rpx;
    padding: 48rpx 90rpx;
    box-sizing: border-box;
    text-align: justify;
    line-height: 48rpx;
}

.tui-grey {
    color: #555;
    padding-bottom: 8rpx;
}

.tui-light-grey {
    color: #888;
    line-height: 40rpx;
}


.tui-danger-hover {
    background: #c80808 !important;
    color: #e5e5e5 !important;
}

.tui-danger-outline {
    color: #eb0909 !important;
    background: transparent;
}

.tui-danger-outline::after {
    border: 1px solid #eb0909 !important;
}

.tui-btn-danger {
    background: #eb0909 !important;
    color: #fff;
}

.tui-shadow-danger {
    box-shadow: 0 10rpx 14rpx 0 rgba(235, 9, 9, 0.2);
}

.tui-btn {
    width: 100%;
    position: relative;
    border: 0 !important;
    border-radius: 6rpx;
    padding-left: 0;
    padding-right: 0;
    overflow: visible;
}

/*圆角 */

.tui-fillet {
    border-radius: 50rpx;
}

.tui-btn-white.tui-fillet::after {
    border-radius: 98rpx;
}

.tui-outline-fillet::after {
    border-radius: 98rpx;
}

// .nut-input-label {
//     display: flex;
//     -webkit-flex-direction: column;
//     -ms-flex-direction: column;
//     flex-direction: column;
//     -webkit-flex: 1;
//     -ms-flex: 1;
//     flex: 1;
// }


.nut-cell__title {
    padding-right: 0;
}