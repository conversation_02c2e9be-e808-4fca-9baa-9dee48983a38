.exam-container {
    font-family: BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
    padding: 32rpx;
    background-color: #f5f5f5;
    min-height: 100vh;

    .exam-list {
        .exam-card {
            background-color: #fff;
            border-radius: 24rpx;
            padding: 32rpx;
            margin-bottom: 32rpx;
            box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);

            .exam-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 24rpx;
                padding-bottom: 24rpx;
                border-bottom: 2rpx solid #f0f0f0;

                .exam-name {
                    font-size: 28rpx;
                    font-weight: 500;
                    color: #333;
                }

                .exam-status {
                    font-size: 28rpx;
                    font-weight: 500;
                    display: inline-block;
                    padding: 4rpx 16rpx;
                    border-radius: 8rpx;

                    &.status-pass {
                        background-color: #f6ffed;
                        border: 2rpx solid #b7eb8f;
                        color: #52c41a;
                    }

                    &.status-waiting {
                        background-color: #e6f7ff;
                        border: 2rpx solid #91d5ff;
                        color: #1890ff;
                    }

                    &.status-absent {
                        background-color: #fff2f0;
                        border: 2rpx solid #ffccc7;
                        color: #ff4d4f;
                    }

                    &.status-default {
                        background-color: #f5f5f5;
                        border: 2rpx solid #d9d9d9;
                        color: #666666;
                    }
                }
            }

            .exam-body {
                .exam-info {
                    display: flex;
                    margin-bottom: 16rpx;
                    font-size: 26rpx;
                    line-height: 1.5;

                    .info-label {
                        color: #666;
                        width: 140rpx;
                        flex-shrink: 0;
                    }

                    .info-text {
                        color: #333;
                        flex: 1;
                    }
                }
            }
        }

        .bottom-line {
            text-align: center;
            padding: 32rpx 0;
            color: #999;
            font-size: 24rpx;

            .loading-more {
                color: #1890ff;
                cursor: pointer;
            }

            .bottom-text {
                color: #999;
            }
        }
    }
}
