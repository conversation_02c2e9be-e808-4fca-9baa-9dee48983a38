import Taro, { useReady } from "@tarojs/taro";
import { useState, useCallback } from "react";
import { View, Text } from "@tarojs/components";
import { Button, Empty } from "@nutui/nutui-react-taro";
import Layout from "@components/Layout";
import { message } from "@components/MessageApi/MessageApiSingleton";
import request from "@service/request";
import { login } from "@utils/login";
import "./index.scss";

interface ExamItem {
    Id: string;
    KeMuText: string;
    ResultText: string;
    ksrq: string;
    kc: string;
    cc: string;
    cj: number;
    Times: number;
    zjcx: string;
}

const ExamPage = () => {
    const [examList, setExamList] = useState<ExamItem[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [current, setCurrent] = useState(1);
    const pageSize = 10;

    // 查询考试信息
    const fetchExamData = async (isLoadMore = false) => {
        if (isLoading) return;
        setIsLoading(true);

        if (!isLoadMore) {
            message.openLoading("正在查询信息");
        }

        try {
            const res = await request.post<API.Result<ExamItem[]>>("/Jx/Exam/WxResult/getMyExamList", {
                current,
                pageSize,
            });

            setIsLoading(false);
            if (!isLoadMore) {
                message.closeLoading();
            }

            if (res.success) {
                const newList = res.data || [];
                if (isLoadMore) {
                    setExamList((prev) => [...prev, ...newList]);
                } else {
                    setExamList(newList);
                }
                // 如果返回的数据少于pageSize，说明没有更多数据了
                setHasMore(newList.length === pageSize);
            } else {
                message.error("查询失败，请稍后再试");
            }
        } catch (error) {
            setIsLoading(false);
            if (!isLoadMore) {
                message.closeLoading();
            }
            message.error("查询出错，请稍后再试");
            console.error("Query error:", error);
        }
    };

    // 处理下拉刷新
    const onRefresh = useCallback(async () => {
        setCurrent(1);
        await fetchExamData();
        Taro.stopPullDownRefresh();
    }, []);

    // 处理加载更多
    const onLoadMore = useCallback(async () => {
        if (!hasMore || isLoading) return;
        setCurrent((prev) => prev + 1);
        await fetchExamData(true);
    }, [hasMore, isLoading]);

    // 获取状态对应的样式类
    const getStatusClass = (status: string) => {
        switch (status) {
            case "合格":
                return "status-pass";
            case "待考":
                return "status-waiting";
            case "缺考":
                return "status-absent";
            default:
                return "status-default";
        }
    };

    // 格式化日期
    const formatDate = (dateStr: string) => {
        if (!dateStr || dateStr === "0001-01-01 00:00:00") return "暂无";
        return dateStr.split(" ")[0];
    };

    // 渲染考试项目卡片
    const renderExamCard = (item: ExamItem) => (
        <View key={item.Id} className="exam-card">
            <View className="exam-header">
                <Text className="exam-name">{item.KeMuText}</Text>
                <Text className={`exam-status ${getStatusClass(item.ResultText)}`}>{item.ResultText}</Text>
            </View>
            <View className="exam-body">
                <View className="exam-info">
                    <Text className="info-label">考试时间：</Text>
                    <Text className="info-text">{formatDate(item.ksrq)}</Text>
                </View>
                <View className="exam-info">
                    <Text className="info-label">考试地点：</Text>
                    <Text className="info-text">{item.kc || "暂无"}</Text>
                </View>
                <View className="exam-info">
                    <Text className="info-label">考试场次：</Text>
                    <Text className="info-text">{item.cc || "暂无"}</Text>
                </View>
                <View className="exam-info">
                    <Text className="info-label">考试车型：</Text>
                    <Text className="info-text">{item.zjcx || "暂无"}</Text>
                </View>
                {item.cj > 0 && (
                    <View className="exam-info">
                        <Text className="info-label">考试成绩：</Text>
                        <Text className="info-text">{item.cj}分</Text>
                    </View>
                )}
                <View className="exam-info">
                    <Text className="info-label">考试次数：</Text>
                    <Text className="info-text">第{item.Times}次</Text>
                </View>
            </View>
        </View>
    );

    useReady(async () => {
        try {
            await login();
            await fetchExamData();
        } catch (error) {
            console.error("Failed to get OpenId:", error);
            message.error("获取用户信息失败，请稍后再试");
        }
    });

    return (
        <Layout>
            <View className="exam-container">
                {examList.length > 0 ? (
                    <View className="exam-list">
                        {examList.map((item) => renderExamCard(item))}
                        <View className="bottom-line">
                            {hasMore ? (
                                <View className="loading-more" onClick={onLoadMore}>
                                    {isLoading ? "加载中..." : "点击加载更多数据"}
                                </View>
                            ) : (
                                <View className="bottom-text">我是有底线的</View>
                            )}
                        </View>
                    </View>
                ) : (
                    <Empty description="暂无考试信息" />
                )}
            </View>
        </Layout>
    );
};

export default ExamPage;
