import { useReady } from "@tarojs/taro";
import Taro from "@tarojs/taro";
import React, { useState, useRef } from "react";
import { View, Text, Image } from "@tarojs/components";
import { Checkbox, Button } from "@nutui/nutui-react-taro";
import { AngleDoubleUp, AngleDoubleDown } from "@nutui/icons-react-taro";
import Layout from "@/components/Layout";
import TopNavBar from "@/components/topNavBar";
import "./index.scss";

interface ServicePackage {
    id: string;
    name: string;
    price: number;
    description: string;
    defaultSelected: boolean;
    includedIn?: string[]; // 包含在哪些套餐中
}

interface Package {
    id: string;
    name: string;
    price: number;
    description: string;
    features: string[];
    tag?: string;
    recommended?: boolean;
}

const CreateStudentVersion2025 = () => {
    const [currentStep, setCurrentStep] = useState(1);
    const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
    const [selectedServices, setSelectedServices] = useState<string[]>([]);
    const [expandedService, setExpandedService] = useState<string | null>(null);
    const [isAnimating, setIsAnimating] = useState(false);
    const scrollViewRef = useRef<any>(null);

    const packages: Package[] = [
        {
            id: "basic",
            name: "☀️ 阳光学车班",
            price: 2800,
            description: "适合自学能力强的学员",
            features: ["基础学车内容", "标准教学服务", "基础考试指导"],
            tag: "入门首选",
        },
        {
            id: "max",
            name: "☀️ 阳光学车 MAX",
            price: 3400,
            description: "含基础 + 补考全免",
            features: ["基础学车内容", "补考费用全免", "优先约车服务", "考试全程指导"],
            tag: "热门推荐",
            recommended: true,
        },
        {
            id: "pro",
            name: "☀️ 阳光学车 PRO",
            price: 4200,
            description: "含 MAX + 点对点接送",
            features: ["MAX套餐全部内容", "点对点接送服务", "VIP专属教练", "考试绿色通道"],
            tag: "最高体验",
        },
    ];

    const servicePackages: ServicePackage[] = [
        {
            id: "auto-upgrade",
            name: "自动挡车型升级包",
            price: 200,
            description: "升级至自动挡车型练车与考试，更轻松的学习体验",
            defaultSelected: false,
        },
        {
            id: "retake",
            name: "所有考试费全能包",
            price: 300,
            description: "全科目考试费用打包，考几次都不再加钱",
            defaultSelected: false,
            includedIn: ["max", "pro"],
        },
        {
            id: "practice",
            name: "考场代收费全能包",
            price: 300,
            description: "含考前模拟、考场协调等相关费用",
            defaultSelected: false,
        },
        {
            id: "subway",
            name: "地铁口接送包",
            price: 300,
            description: "指定地铁站点接送上车",
            defaultSelected: false,
        },
        {
            id: "door",
            name: "点对点接送包",
            price: 300,
            description: "家门口/公司门口一对一接送",
            defaultSelected: false,
            includedIn: ["pro"],
        },
        {
            id: "weekend",
            name: "假日周末随心练包",
            price: 200,
            description: "周末/节假日随时预约练车",
            defaultSelected: false,
        },
    ];

    const handlePackageSelect = (packageId: string) => {
        setSelectedPackage(packageId);
        const includedServices = servicePackages.filter((service) => service.includedIn?.includes(packageId)).map((service) => service.id);
        setSelectedServices(includedServices);
        // Switch to services screen after a short delay
        setTimeout(() => {
            setCurrentStep(2);
        }, 500);
    };

    const handleScreenSwitch = (direction: "up" | "down") => {
        if (isAnimating) return;
        setIsAnimating(true);

        if (direction === "down" && currentStep === 1) {
            setCurrentStep(2);
        } else if (direction === "up" && currentStep === 2) {
            setCurrentStep(1);
        }

        setTimeout(() => {
            setIsAnimating(false);
        }, 500);
    };

    const handleServiceSelect = (serviceId: string) => {
        setSelectedServices((prev) => {
            if (prev.includes(serviceId)) {
                return prev.filter((id) => id !== serviceId);
            }
            return [...prev, serviceId];
        });
    };

    const toggleServiceExpand = (serviceId: string) => {
        setExpandedService((prev) => (prev === serviceId ? null : serviceId));
    };

    const calculateTotal = () => {
        const packagePrice = packages.find((p) => p.id === selectedPackage)?.price || 0;
        const servicesPrice = servicePackages.filter((service) => selectedServices.includes(service.id)).reduce((sum, service) => sum + service.price, 0);
        return packagePrice + servicesPrice;
    };

    const handleSubmit = () => {
        // TODO: 实现订单提交逻辑
        console.log("Selected package:", selectedPackage);
        console.log("Selected services:", selectedServices);
        console.log("Total amount:", calculateTotal());
    };

    const handleHomeClick = () => {
        const entryPagePath = Taro.getApp().config.entryPagePath;
        Taro.reLaunch({
            url: "/" + entryPagePath,
        });
    };

    return (
        <Layout>
            <View className="create-student-container">
                <TopNavBar title="选择套餐" homeClick={handleHomeClick} BgColor="#f3f3f3" FontColor="#666" leftClick={undefined} />

                <View className={`screen-container ${isAnimating ? "animating" : ""}`}>
                    {/* Step 1: Package Selection */}
                    <View className={`screen package-screen ${currentStep === 1 ? "active" : ""}`}>
                        <View className="packages-section">
                            {packages.map((pkg) => (
                                <View
                                    key={pkg.id}
                                    className={`package-card ${selectedPackage === pkg.id ? "selected" : ""} ${pkg.recommended ? "recommended" : ""}`}
                                    onClick={() => handlePackageSelect(pkg.id)}
                                >
                                    {pkg.recommended && <View className="recommended-tag">推荐</View>}
                                    <View className="package-header">
                                        <Text className="package-name">{pkg.name}</Text>
                                        <Text className="package-description">{pkg.description}</Text>
                                    </View>
                                    <View className="package-features">
                                        {pkg.features.map((feature, index) => (
                                            <View key={index} className="feature-item">
                                                <Text className="feature-dot">•</Text>
                                                <Text className="feature-text">{feature}</Text>
                                            </View>
                                        ))}
                                    </View>
                                    <View className="package-price-section">
                                        <Text className="package-price">{pkg.price}</Text>
                                        {pkg.tag && <Text className="package-tag">{pkg.tag}</Text>}
                                    </View>
                                </View>
                            ))}
                        </View>
                    </View>

                    {/* Step 2: Service Selection */}
                    <View className={`screen service-screen ${currentStep === 2 ? "active" : ""}`}>
                        <View className="services-section">
                            {servicePackages.map((service) => {
                                const isIncluded = service.includedIn?.includes(selectedPackage || "");
                                return (
                                    <View key={service.id} className={`service-card ${selectedServices.includes(service.id) ? "selected" : ""} ${isIncluded ? "included" : ""}`}>
                                        <View className="service-header" onClick={() => handleServiceSelect(service.id)}>
                                            <View className="service-info">
                                                <Checkbox checked={selectedServices.includes(service.id) || isIncluded} disabled={isIncluded} onChange={() => {}} />
                                                <Text className="service-name">{service.name}</Text>
                                            </View>
                                            <Text className="service-price">￥{service.price}</Text>
                                        </View>
                                        <View
                                            className="service-description"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                toggleServiceExpand(service.id);
                                            }}
                                        >
                                            <Text className="description-text">{expandedService === service.id ? service.description : "点击查看详情"}</Text>
                                        </View>
                                    </View>
                                );
                            })}
                        </View>
                    </View>
                </View>
            </View>

            {selectedPackage && (
                <>
                    {currentStep === 1 && (
                        <View className="indicator-container">
                            <View className="indicator-left">
                                <AngleDoubleDown className="indicator-icon" onClick={() => handleScreenSwitch("down")} />
                                <Text className="indicator-text">向下滑动选择更多服务</Text>
                            </View>
                        </View>
                    )}
                    {currentStep === 2 && (
                        <View className="indicator-container">
                            <View className="indicator-right">
                                <AngleDoubleUp className="indicator-icon" onClick={() => handleScreenSwitch("up")} />
                                <Text className="indicator-text">向上滑动重新选择套餐</Text>
                            </View>
                        </View>
                    )}
                </>
            )}
            {/* Bottom Action Bar */}
            <View className="bottom-bar">
                <View className="total-section">
                    <Text className="total-label">总计</Text>
                    <Text className="total-amount">{calculateTotal()}</Text>
                </View>
                <Button className="action-button" type="primary" onClick={handleSubmit} disabled={!selectedPackage}>
                    确认报名并下单
                </Button>
            </View>
        </Layout>
    );
};

export default CreateStudentVersion2025;
