page {
    font-family: li-regular, "Microsoft YaHei", "SF Pro SC", "SF Pro Display", "PingFang SC", "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.create-student-container {
    min-height: 100vh;
    background: #f5f5f5;
    padding-bottom: 240rpx;
    overflow: hidden;

    .screen-container {
        position: relative;
        height: calc(100vh - 88rpx - 240rpx);
        overflow: hidden;

        &.animating {
            .screen {
                transition: transform 0.5s ease;
            }
        }
    }

    .screen {
        position: absolute;
        width: 100%;
        height: 100%;
        transform: translateY(100%);
        transition: transform 0.5s ease;

        &.active {
            transform: translateY(0);
        }

        &.package-screen {
            z-index: 2;
        }

        &.service-screen {
            z-index: 1;
        }
    }

    @keyframes bounce {
        0%,
        20%,
        50%,
        80%,
        100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10rpx);
        }
        60% {
            transform: translateY(-5rpx);
        }
    }

    .step-section {
        min-height: 100vh;
        position: relative;
        padding-bottom: 120rpx;

        .step-title {
            font-size: 36rpx;
            font-weight: 600;
            color: #333;
            padding: 32rpx;
            text-align: center;
        }
    }

    .scroll-indicator {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16rpx;
        padding: 24rpx;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 32rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
        z-index: 10;

        &.down {
            bottom: 32rpx;
        }

        &.up {
            top: 32rpx;
        }

        .indicator-text {
            font-size: 24rpx;
            color: #666;
        }

        .arrow-icon {
            font-size: 32rpx;
            color: #1677ff;
            animation: bounce 2s infinite;
        }
    }

    .packages-section {
        padding: 32rpx;

        .package-card {
            background: #fff;
            border-radius: 24rpx;
            padding: 28rpx;
            margin-bottom: 32rpx;
            position: relative;
            box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
            border: 4rpx solid transparent;
            transition: border-color 0.3s ease;

            &.selected {
                border: 4rpx solid #1677ff;
            }

            &.recommended {
                border: 4rpx solid rgba(22, 119, 255, 0.4);

                &.selected {
                    border: 4rpx solid #1677ff;
                }
            }

            .recommended-tag {
                position: absolute;
                top: 24rpx;
                right: 24rpx;
                background: #1677ff;
                color: #fff;
                padding: 8rpx 24rpx;
                border-radius: 8rpx;
                font-size: 20rpx;
                font-weight: 500;
            }

            .package-header {
                display: flex;
                align-items: center;
                margin-bottom: 16rpx;
                gap: 16rpx;

                .package-name {
                    font-size: 36rpx;
                    font-weight: 600;
                    color: #333;
                    display: inline-block;
                }

                .package-description {
                    font-size: 24rpx;
                    color: #666;
                    display: inline-block;
                }
            }

            .package-features {
                margin-bottom: 24rpx;

                .feature-item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 8rpx;

                    .feature-dot {
                        color: #1677ff;
                        margin-right: 12rpx;
                        font-size: 28rpx;
                    }

                    .feature-text {
                        font-size: 24rpx;
                        // color: #666;
                        color: rgba(0, 0, 0, 0.6);
                    }
                }
            }

            .package-price-section {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .package-price {
                    font-size: 44rpx;
                    font-weight: 600;
                    color: #1677ff;
                    &::before {
                        content: "¥";
                        font-size: 28rpx;
                        margin-right: 8rpx;
                    }
                }

                .package-tag {
                    font-size: 20rpx;
                    color: #1677ff;
                    background: rgba(22, 119, 255, 0.1);
                    padding: 4rpx 16rpx;
                    border-radius: 8rpx;
                }
            }
        }
    }

    .services-section {
        padding: 32rpx;
        // margin-top: 88rpx;

        .service-card {
            background: #fff;
            border-radius: 24rpx;
            padding: 32rpx;
            margin-bottom: 24rpx;
            border: 2rpx solid transparent;
            transition: border-color 0.3s ease;

            &.selected {
                border: 2rpx solid #1677ff;
            }

            &.included {
                background: #f0f7ff;
            }

            .service-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16rpx;

                .service-info {
                    display: flex;
                    align-items: center;
                    gap: 16rpx;

                    .service-name {
                        font-size: 28rpx;
                        font-weight: 500;
                        color: #333;
                    }
                }

                .service-price {
                    font-size: 32rpx;
                    font-weight: 600;
                    color: #1677ff;
                    &::before {
                        content: "¥";
                        font-size: 24rpx;
                        margin-right: 4rpx;
                    }
                }
            }

            .service-description {
                .description-text {
                    font-size: 22rpx;
                    color: #999;
                    line-height: 1.5;
                }
            }
        }
    }

    .confirmation-section {
        padding: 32rpx;

        .selected-items {
            background: #fff;
            border-radius: 24rpx;
            padding: 40rpx;

            .section-title {
                font-size: 28rpx;
                font-weight: 600;
                color: #333;
                margin-bottom: 32rpx;
                display: block;
            }

            .selected-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 24rpx;

                .item-name {
                    font-size: 24rpx;
                    color: #666;
                }

                .item-price {
                    font-size: 28rpx;
                    font-weight: 500;
                    color: #1677ff;
                    &::before {
                        content: "¥";
                        font-size: 20rpx;
                        margin-right: 4rpx;
                    }
                }
            }
        }
    }
}

.indicator-container {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 160rpx;
    z-index: 10;
    padding: 0 32rpx;
    pointer-events: none;
    display: flex;
    justify-content: center;

    .indicator-left,
    .indicator-right {
        display: flex;
        align-items: center;
        gap: 8rpx;
        background: rgba(255, 255, 255, 0.9);
        padding: 8rpx 16rpx;
        border-radius: 24rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
        pointer-events: auto;
        width: fit-content;
    }

    .indicator-left {
        margin-right: auto;
    }

    .indicator-right {
        margin-left: auto;
    }

    .indicator-icon {
        color: #1677ff;
        font-size: 28rpx;
        animation: bounce 2s infinite;
    }

    .indicator-text {
        font-size: 20rpx;
        color: rgba(102, 102, 102, 0.8);
        white-space: nowrap;
    }
}

.bottom-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    padding: 24rpx 32rpx;
    // padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.04);
    z-index: 100;

    .total-section {
        display: flex;
        align-items: center;

        .total-label {
            font-size: 24rpx;
            color: #666;
            margin-right: 8rpx;
        }

        .total-amount {
            font-size: 36rpx;
            font-weight: 600;
            color: #1677ff;
            &::before {
                content: "¥";
                font-size: 24rpx;
                margin-right: 4rpx;
            }
        }
    }

    .action-button {
        min-width: 240rpx;
        height: 80rpx;
        border-radius: 40rpx;
        font-size: 28rpx;
        font-weight: 500;
        background: #1677ff;
        border: none;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;

        &:active {
            opacity: 0.9;
        }

        &[disabled] {
            background: #ccc;
            opacity: 0.8;
        }
    }
}
