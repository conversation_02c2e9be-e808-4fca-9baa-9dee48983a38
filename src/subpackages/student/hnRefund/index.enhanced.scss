/* Enhanced styles for file preview */
.preview-content {
    width: 100%;
    height: 300rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20rpx;
    position: relative;
    overflow: hidden;
}

.file-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 16rpx;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    
    /* PDF icon styling */
    .nut-icon {
        color: #e74c3c;
        filter: drop-shadow(0 4rpx 6rpx rgba(231, 76, 60, 0.2));
        transition: transform 0.3s ease;
        
        &:active {
            transform: scale(0.98);
        }
    }
    
    /* Image styling */
    .preview-image {
        max-width: 90%;
        max-height: 90%;
        width: auto;
        height: auto;
        object-fit: contain;
        border-radius: 8rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
        
        &:active {
            transform: scale(0.98);
        }
    }
    
    /* Overlay for PDF */
    &.pdf-preview {
        position: relative;
        
        &::before {
            content: "PDF文档";
            position: absolute;
            bottom: 20rpx;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(231, 76, 60, 0.9);
            color: white;
            padding: 8rpx 24rpx;
            border-radius: 30rpx;
            font-size: 24rpx;
            font-weight: 500;
            box-shadow: 0 2rpx 8rpx rgba(231, 76, 60, 0.3);
        }
    }
    
    /* Overlay for images */
    &.image-preview {
        position: relative;
        
        &::before {
            content: "图片预览";
            position: absolute;
            bottom: 20rpx;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(52, 152, 219, 0.9);
            color: white;
            padding: 8rpx 24rpx;
            border-radius: 30rpx;
            font-size: 24rpx;
            font-weight: 500;
            box-shadow: 0 2rpx 8rpx rgba(52, 152, 219, 0.3);
        }
    }
}

/* Enhanced action buttons */
.preview-actions {
    display: flex;
    justify-content: center;
    gap: 20rpx;
    width: 100%;
    padding: 0 20rpx;
    
    .action-btn {
        flex: 1;
        padding: 16rpx 24rpx;
        border-radius: 44rpx;
        font-size: 28rpx;
        text-align: center;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
        
        /* Upload button */
        &:first-child {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            
            &:active {
                background: linear-gradient(135deg, #2980b9, #2573a7);
                transform: translateY(2rpx);
            }
        }
        
        /* Delete button */
        &.delete {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            
            &:active {
                background: linear-gradient(135deg, #c0392b, #a93226);
                transform: translateY(2rpx);
            }
        }
        
        .action-text {
            font-size: 28rpx;
            font-weight: 500;
        }
    }
}
