page {
    background-color: #f7f8fa;

    font-family: <PERSON><PERSON>, STYuanti-SC-Regular, "Microsoft Yahei", sans-serif;
    // font-family: <PERSON><PERSON>, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
}

.refund-container {
    padding: 32rpx;
    padding-bottom: 160rpx;
    min-height: 100vh;
    box-sizing: border-box;
    position: relative;
}

.refund-header {
    margin-bottom: 32rpx;
}

.refund-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
}

.order-info-card {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.order-info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24rpx;

    &:last-child {
        margin-bottom: 0;
    }
}

.order-info-label {
    color: #666;
    font-size: 28rpx;
}

.order-info-value {
    color: #333;
    font-size: 28rpx;
    font-weight: 500;
}

.refund-form {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    // margin-bottom: 120rpx;

    .form-section {
        margin-bottom: 32rpx;
        padding-bottom: 24rpx;
        border-bottom: 1rpx solid #f0f0f0;

        &:last-child {
            border-bottom: none;
        }
    }

    .section-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 24rpx;
        display: block;
        position: relative;
        padding-left: 16rpx;

        &:before {
            content: "";
            position: absolute;
            left: 0;
            top: 6rpx;
            height: 24rpx;
            width: 4rpx;
            background-color: #3b82f6;
            border-radius: 2rpx;
        }
    }
}

.form-item {
    margin-bottom: 24rpx;

    :global {
        .nut-uploader {
            width: 100%;
        }

        .nut-uploader__slot {
            width: 100%;
        }

        .nut-uploader__upload {
            width: 100%;
            height: 400rpx;
            background-color: #f5f5f5;
            border: 2rpx dashed #ddd;
            border-radius: 16rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 32rpx;
        }

        .nut-uploader__upload-icon {
            margin-bottom: 20rpx;
        }

        .nut-uploader__upload-label {
            font-size: 32rpx;
            color: #333;
        }

        .nut-uploader__preview {
            width: 100%;
        }

        .nut-uploader__preview-list {
            width: 100%;
        }

        .nut-uploader__preview-img {
            width: 100%;
            height: 400rpx;
            object-fit: contain;
        }

        .nut-uploader__preview-cover {
            background-color: rgba(0, 0, 0, 0.5);
        }
    }
}

.form-label {
    display: block;
    font-size: 28rpx;
    color: #333;
    margin-bottom: 12rpx;
}

.form-input {
    width: 100%;
    height: 88rpx;
    background-color: #f5f5f5;
    border-radius: 44rpx;
    padding: 0 24rpx;
    font-size: 28rpx;
    color: #333;
    box-sizing: border-box;

    &.date-input {
        padding-right: 80rpx;
    }
}

.date-picker-container {
    position: relative;
    width: 100%;
}

.date-picker-icon {
    position: absolute;
    right: 24rpx;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;

    .icon-calendar {
        font-size: 24rpx;
        color: #999;
    }
}

.form-textarea {
    width: 100%;
    height: 200rpx;
    background-color: #f5f5f5;
    border-radius: 12rpx;
    padding: 16rpx 24rpx;
    font-size: 28rpx;
    color: #333;
    box-sizing: border-box;
}

.select-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    margin-bottom: 8rpx;
}

.select-option {
    flex: 1;
    min-width: 120rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #666;
    transition: all 0.3s ease;

    &.selected {
        background-color: #e6f7ff;
        color: #1890ff;
        border: 1rpx solid #1890ff;
        font-weight: 500;
    }

    &:active {
        opacity: 0.8;
    }
}

.refund-tips {
    margin: 24rpx 0;
    padding: 16rpx;
    background-color: #f8f8f8;
    border-radius: 8rpx;
}

.tips-title {
    display: block;
    font-size: 26rpx;
    color: #333;
    margin-bottom: 8rpx;
    font-weight: 500;
}

.tips-content {
    display: block;
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
    margin-bottom: 4rpx;
}

.refund-warning {
    margin: 24rpx 0;
    padding: 16rpx;
    background-color: #fff7e6;
    border-radius: 8rpx;
    border-left: 4rpx solid #faad14;
}

.warning-title {
    display: block;
    font-size: 26rpx;
    color: #d46b08;
    margin-bottom: 8rpx;
    font-weight: 500;
}

.warning-content {
    display: block;
    font-size: 24rpx;
    color: #d48806;
    line-height: 1.6;
}

.submit-button {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: #fff;
    font-size: 32rpx;
    border: none;
    border-radius: 44rpx;
    // margin-top: 32rpx;
    box-shadow: 0 4rpx 12rpx rgba(37, 99, 235, 0.3);

    &::after {
        border: none;
    }

    &:active {
        opacity: 0.8;
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 6rpx rgba(37, 99, 235, 0.2);
    }
}

.screenshot-info-card {
    background-color: #fff;
    border-radius: 8rpx;
    padding: 15rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.screenshot-info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12rpx;

    &:last-child {
        margin-bottom: 0;
    }
}

.screenshot-info-label {
    color: #666;
    font-size: 24rpx;
}

.screenshot-info-value {
    color: #333;
    font-size: 24rpx;
    font-weight: 500;
}

.upload-container {
    width: 100%;
    margin-bottom: 32rpx;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

    .upload-header {
        text-align: center;
        margin-bottom: 32rpx;

        .upload-title {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
            position: relative;
            display: inline-block;
            padding: 0 20rpx;

            &::before,
            &::after {
                content: "";
                position: absolute;
                top: 50%;
                width: 40rpx;
                height: 2rpx;
                background: linear-gradient(to right, transparent, #07c160);
            }

            &::before {
                left: -20rpx;
                transform: translateX(-20rpx);
            }

            &::after {
                right: -20rpx;
                transform: translateX(20rpx) rotate(180deg);
            }
        }
    }

    .upload-content {
        margin-bottom: 32rpx;

        .preview-container {
            width: 100%;
            height: 400rpx;
            background-color: #f8f9fa;
            border-radius: 16rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;

            .preview-image {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            .choose-again-btn {
                position: absolute;
                bottom: 20rpx;
                left: 50%;
                transform: translateX(-50%);
                background-color: rgba(0, 0, 0, 0.6);
                color: #fff;
                font-size: 28rpx;
                padding: 12rpx 32rpx;
                border-radius: 40rpx;
                backdrop-filter: blur(10rpx);
            }
        }

        .upload-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 400rpx;
            background-color: #f5f5f5;
            border: 2rpx dashed #d9d9d9;
            border-radius: 16rpx;
            cursor: pointer;
            transition: all 0.3s;

            &:active {
                background-color: #e6e6e6;
            }

            .upload-icon {
                font-size: 96rpx;
                color: #999;
                margin-bottom: 32rpx;
            }

            .upload-text {
                font-size: 32rpx;
                color: #333;
                margin-bottom: 16rpx;
            }

            .upload-tip {
                font-size: 28rpx;
                color: #999;
            }
        }
    }

    .upload-tips {
        background-color: #f8f9fa;
        border-radius: 8rpx;
        padding: 20rpx;

        .tips-title {
            font-size: 28rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 12rpx;
            display: block;
        }

        .tips-content {
            font-size: 24rpx;
            color: #666;
            line-height: 1.6;
            display: block;
            margin-bottom: 8rpx;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }

    .white-btn {
        width: 100%;
        height: 88rpx;
        background-color: #fff;
        color: #07c160;
        border: 2rpx solid #07c160;
        border-radius: 44rpx;
        font-size: 32rpx;
        margin-top: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;

        &:active {
            opacity: 0.8;
        }
    }
}

.preview-container {
    width: 100%;
    height: 300rpx;
    position: relative;
    border-radius: 16rpx;
    overflow: hidden;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx;
}

.preview-image {
    max-width: 80%;
    max-height: 80%;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: 8rpx;
}

.choose-again-btn {
    width: 100%;
    height: 88rpx;
    background-color: #f5f5f5;
    color: #666;
    font-size: 32rpx;
    border: none;
    border-radius: 44rpx;
    margin-top: 16rpx;

    &::after {
        border: none;
    }

    &:active {
        opacity: 0.8;
    }
}

.upload-progress {
    margin: 20rpx 0;
    height: 8rpx;
    background-color: #eee;
    border-radius: 4rpx;
    position: relative;
}

.progress-bar {
    height: 100%;
    background-color: #07c160;
    border-radius: 4rpx;
    transition: width 0.3s ease;
}

.progress-text {
    position: absolute;
    right: 0;
    top: 10rpx;
    font-size: 24rpx;
    color: #666;
}

.scan-placeholder {
    height: 400rpx;
    border: 4rpx dashed #ddd;
    border-radius: 16rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #f9f9f9;
    margin-bottom: 40rpx;
}

.scan-icon {
    font-size: 96rpx;
    color: #07c160;
    margin-bottom: 20rpx;
}

.scan-text {
    font-size: 28rpx;
    color: #666;
}

.scan-result {
    background-color: #f0f9eb;
    border: 2rpx solid #e1f3d8;
    border-radius: 8rpx;
    padding: 20rpx;
    margin-bottom: 40rpx;
    text-align: center;
}

.result-text {
    font-size: 28rpx;
    color: #67c23a;
}

.scan-btn {
    margin-top: 40rpx;
    background-color: #07c160;
    color: #fff;
    border: none;
    border-radius: 8rpx;
    padding: 20rpx 40rpx;
    font-size: 32rpx;
}

.scan-btn[disabled] {
    background-color: #ccc;
}

.tenant-info {
    width: 100%;
    padding: 20rpx 0;
    text-align: center;
    background-color: #f8f8f8;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);

    .tenant-name {
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
        display: inline-flex;
        align-items: center;
        padding: 0 30rpx;

        &::before,
        &::after {
            content: "";
            display: block;
            height: 2rpx;
            width: 60rpx;
            background: linear-gradient(to right, transparent, #666);
            margin: 0 20rpx;
        }

        &::before {
            background: linear-gradient(to right, transparent, #666);
        }

        &::after {
            background: linear-gradient(to left, transparent, #666);
        }
    }
}
// 步骤指示器样式
.steps-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 0;
    margin-bottom: 32rpx;
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    position: relative;
}

.step {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;

    .step-number {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        background-color: #f5f5f5;
        color: #999;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        margin-bottom: 12rpx;
        border: 2rpx solid #e8e8e8;
        transition: all 0.3s ease;
    }

    .step-title {
        font-size: 24rpx;
        color: #999;
        transition: all 0.3s ease;
    }

    &.active {
        .step-number {
            background-color: #e6f7ff;
            color: #1890ff;
            border-color: #1890ff;
        }

        .step-title {
            color: #1890ff;
            font-weight: 500;
        }
    }
}

.step-line {
    flex: 1;
    height: 2rpx;
    background-color: #e8e8e8;
    margin: 0 8rpx;
    position: relative;
    top: -32rpx;

    &.active {
        background-color: #1890ff;
    }
}

// 按钮样式
.form-buttons {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    margin-top: 48rpx;
}

.prev-button {
    width: 100%;
    height: 88rpx;
    background: #f5f5f5;
    color: #666;
    font-size: 32rpx;
    border: none;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &::after {
        border: none;
        border-radius: 44rpx;
    }

    &:active {
        opacity: 0.8;
    }
}

.next-button {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: #fff;
    font-size: 32rpx;
    border: none;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(37, 99, 235, 0.3);

    &:active {
        opacity: 0.8;
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 6rpx rgba(37, 99, 235, 0.2);
    }
}

// 考试信息卡片样式
.exam-info-card {
    background-color: #f8f8f8;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-top: 24rpx;
}

.exam-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    &:last-child {
        margin-bottom: 0;
    }
}

.exam-info-label {
    font-size: 28rpx;
    color: #666;
}

.exam-info-value {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
}

.white-btn {
    width: 100%;
    height: 88rpx;
    background-color: #ffffff;
    color: #333333;
    border: 1rpx solid #dddddd;
    margin-top: 32rpx;
    font-size: 32rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

    &::after {
        border: none;
    }

    &:active {
        background-color: #f5f5f5;
        opacity: 0.8;
    }

    &[disabled] {
        background-color: #f5f5f5;
        color: #999999;
        border-color: #dddddd;
    }
}

.upload-tip {
    font-size: 24rpx;
    color: #999;
    margin-top: 8rpx;
}

.payment-upload {
    margin-bottom: 32rpx;

    .upload-placeholder {
        height: 400rpx;
        background-color: #f8f8f8;
        border: 2rpx dashed #d9d9d9;
        border-radius: 16rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40rpx;
        box-sizing: border-box;
        transition: all 0.3s ease;

        &:active {
            background-color: #f0f0f0;
            border-color: #1890ff;
        }

        .upload-icon {
            font-size: 96rpx;
            color: #d9d9d9;
            margin-bottom: 24rpx;
        }

        .upload-text {
            font-size: 32rpx;
            color: #333;
            margin-bottom: 16rpx;
        }

        .upload-tip {
            font-size: 24rpx;
            color: #999;
        }
    }
}

.file-preview {
    width: 100%;
    height: 400rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    .file-icon {
        font-size: 160rpx;
        line-height: 1;
        color: #e74c3c;
        filter: drop-shadow(0 4rpx 6rpx rgba(231, 76, 60, 0.2));
        transition: transform 0.3s ease;

        &:active {
            transform: scale(0.98);
        }
    }

    /* PDF specific styling */
    &.pdf-preview {
        position: relative;

        &::before {
            content: "PDF文档";
            position: absolute;
            bottom: 20rpx;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(231, 76, 60, 0.9);
            color: white;
            padding: 8rpx 24rpx;
            border-radius: 30rpx;
            font-size: 24rpx;
            font-weight: 500;
            box-shadow: 0 2rpx 8rpx rgba(231, 76, 60, 0.3);
        }
    }

    /* Image specific styling */
    &.image-preview {
        position: relative;

        &::before {
            content: "图片预览";
            position: absolute;
            bottom: 20rpx;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(52, 152, 219, 0.9);
            color: white;
            padding: 8rpx 24rpx;
            border-radius: 30rpx;
            font-size: 24rpx;
            font-weight: 500;
            box-shadow: 0 2rpx 8rpx rgba(52, 152, 219, 0.3);
        }
    }
}

.preview-wrapper {
    width: 100%;
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.preview-content {
    width: 100%;
    position: relative;
}

.file-preview {
    width: 100%;
    position: relative;
    background: #f5f5f5;
    min-height: 400rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-preview {
    width: 100%;
}

.preview-image-fullsize {
    width: 100%;
    height: auto;
    display: block;
}

.image-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-text {
    color: #666;
    font-size: 28rpx;
}

.preview-actions {
    padding: 24rpx;
    display: flex;
    justify-content: center;
    border-top: 2rpx solid #eee;
}

.action-btn {
    padding: 16rpx 32rpx;
    background: #f5f5f5;
    border-radius: 8rpx;
    cursor: pointer;
    transition: all 0.3s;

    width: 100%;
    text-align: center;

    &:active {
        background: #e8e8e8;
    }
}

.action-text {
    color: #333;
    font-size: 28rpx;
}

.payment-upload-placeholder {
    width: 100%;
    height: 400rpx;
    background: #f5f5f5;
    border: 4rpx dashed #ddd;
    border-radius: 16rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;

    &:active {
        background: #e8e8e8;
    }
}

.payment-upload-icon {
    font-size: 64rpx;
    color: #999;
    margin-bottom: 16rpx;
}

.payment-upload-text {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 8rpx;
}

.payment-upload-tip {
    font-size: 24rpx;
    color: #999;
}

.refund-application {
    .header-actions {
        padding: 24rpx;
        display: flex;
        justify-content: flex-end;
        background-color: #fff;
        margin-bottom: 24rpx;

        .view-list-btn {
            background-color: #1677ff;
            color: #fff;
            font-size: 28rpx;
            padding: 16rpx 32rpx;
            border-radius: 8rpx;
            border: none;
        }
    }

    .refund-container {
        padding: 24rpx;
        padding-bottom: 160rpx;
        min-height: 100vh;
        box-sizing: border-box;
        position: relative;
    }
}

.primary-btn {
    width: 100%;
    height: 88rpx;
    background-color: #3b82f6;
    color: #ffffff;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24rpx;
    border: none;
    transition: all 0.3s ease;

    &:active {
        opacity: 0.8;
    }

    &[loading] {
        opacity: 0.7;
    }
}

.face-detection-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background-color: #000;
    position: relative;
}

.face-detection-tips {
    position: absolute;
    bottom: 40px;
    left: 0;
    right: 0;
    text-align: center;
    color: #fff;
    font-size: 28px;
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.5);
}

.bank-screen-content-wrapper {
    margin-bottom: 40rpx;
    border-radius: 16rpx;
    background-color: #f5f5f5;
    border: 1rpx solid #e8e8e8;
    overflow: hidden;

    .bank-screen-content-body {
        padding: 32rpx;

        .bank-screen-content-text {
            font-size: 28rpx;
            color: #666666;
            line-height: 1.5;
        }
    }
}

.file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    padding: 16rpx;
    background: #f8f9fa;
    border-top: 2rpx solid #eee;
}

.file-item {
    position: relative;
    width: 160rpx;
    height: 160rpx;
    border-radius: 8rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .file-thumbnail {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .file-remove {
        position: absolute;
        top: 8rpx;
        right: 8rpx;
        width: 40rpx;
        height: 40rpx;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        .remove-icon {
            color: #fff;
            font-size: 32rpx;
            line-height: 1;
        }

        &:active {
            background: rgba(0, 0, 0, 0.7);
        }
    }
}

.grid-preview {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16rpx;
    padding: 16rpx;
    background: #f8f9fa;
    border-radius: 16rpx;
}

.grid-item {
    position: relative;
    width: 100%;
    padding-bottom: 100%; // 保持1:1的宽高比
    border-radius: 8rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .grid-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .grid-remove {
        position: absolute;
        top: 8rpx;
        right: 8rpx;
        width: 40rpx;
        height: 40rpx;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 1;

        .remove-icon {
            color: #fff;
            font-size: 32rpx;
            line-height: 1;
        }

        &:active {
            background: rgba(0, 0, 0, 0.7);
        }
    }
}
