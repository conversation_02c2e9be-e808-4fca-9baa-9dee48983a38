import { Component } from "react";
import Taro from "@tarojs/taro";
import { View, Text, Input, Textarea, Button, Image } from "@tarojs/components";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import Layout from "@/components/Layout";
import "./index.scss";

interface IState {
    orderId: string;
    reason: string;
    amount: string;
    contact: string;
    loading: boolean;
    orderInfo: any;
    studentName: string;
    idNumber: string;
    phoneNumber: string;
    selectedSubject: string;
    examDate: string;
}

export default class RefundApplication extends Component<{}, IState> {
    constructor(props: any) {
        super(props);
        this.state = {
            orderId: "",
            reason: "",
            amount: "",
            contact: "",
            loading: false,
            orderInfo: null,
            studentName: "",
            idNumber: "",
            phoneNumber: "",
            selectedSubject: "",
            examDate: "",
        };
    }

    componentDidMount() {
        // 获取路由参数
        const router = Taro.getCurrentInstance().router;
        const orderId = router?.params?.orderId || "";

        if (orderId) {
            this.setState({ orderId }, () => {
                this.fetchOrderInfo(orderId);
            });
        }
    }

    // 获取订单信息
    fetchOrderInfo = async (orderId: string) => {
        this.setState({ loading: true });

        try {
            // 这里替换为实际的API调用
            const response = await Taro.request({
                url: "/api/order/detail",
                data: { orderId },
                method: "GET",
            });

            if (response.statusCode === 200 && response.data.success) {
                const orderInfo = response.data.data;
                this.setState({
                    orderInfo,
                    amount: orderInfo.amount || "",
                });
            } else {
                message.error("获取订单信息失败");
            }
        } catch (error) {
            console.error("获取订单信息失败:", error);
            message.error("获取订单信息失败，请稍后重试");
        } finally {
            this.setState({ loading: false });
        }
    };

    // 处理输入变化
    handleInputChange = (field: string, value: string) => {
        this.setState({ [field]: value } as any);
    };

    // 打开日期选择器
    handleOpenDatePicker = () => {
        // 使用微信小程序的日期选择器
        Taro.showActionSheet({
            itemList: ["选择日期"],
            success: () => {
                Taro.getSystemInfo({
                    success: (res) => {
                        if (res.platform === "ios" || res.platform === "android") {
                            // 在移动端使用日期选择器
                            Taro.navigateTo({
                                url: "/pages/date-picker/index?callback=examDateCallback",
                            });
                        } else {
                            // 在小程序中使用小程序的日期选择器
                            const now = new Date();
                            const year = now.getFullYear();
                            const month = now.getMonth() + 1;
                            const day = now.getDate();

                            Taro.showModal({
                                title: "选择日期",
                                content: `请点击确定选择日期，当前日期：${year}-${month}-${day}`,
                                success: (modalRes) => {
                                    if (modalRes.confirm) {
                                        // 格式化日期
                                        const formattedMonth = String(month).padStart(2, "0");
                                        const formattedDay = String(day).padStart(2, "0");
                                        const formattedDate = `${year}-${formattedMonth}-${formattedDay}`;

                                        this.setState({
                                            examDate: formattedDate,
                                        });
                                    }
                                },
                            });
                        }
                    },
                });
            },
        });
    };

    // 提交退款申请
    handleSubmit = async () => {
        const { orderId, reason, amount, contact, studentName, idNumber, phoneNumber, selectedSubject, examDate } = this.state;

        // 表单验证
        if (!orderId) {
            message.error("订单ID不能为空");
            return;
        }

        if (!studentName) {
            message.error("请填写学员姓名");
            return;
        }

        if (!idNumber) {
            message.error("请填写证件号码");
            return;
        }

        if (!phoneNumber) {
            message.error("请填写手机号码");
            return;
        }

        if (!selectedSubject) {
            message.error("请选择考试科目");
            return;
        }

        if (!examDate) {
            message.error("请选择考试日期");
            return;
        }

        if (!reason) {
            message.error("请填写退款原因");
            return;
        }

        if (!amount) {
            message.error("请填写退款金额");
            return;
        }

        if (!contact) {
            message.error("请填写联系方式");
            return;
        }

        this.setState({ loading: true });

        try {
            // 这里替换为实际的API调用
            const response = await Taro.request({
                url: "/api/refund/apply",
                data: {
                    orderId,
                    studentName,
                    idNumber,
                    phoneNumber,
                    examSubject: selectedSubject,
                    examDate,
                    reason,
                    amount: parseFloat(amount),
                    contact,
                },
                method: "POST",
            });

            if (response.statusCode === 200 && response.data.success) {
                message.success("退款申请提交成功");
                // 延迟返回上一页
                setTimeout(() => {
                    Taro.navigateBack();
                }, 1500);
            } else {
                message.error(response.data.message || "退款申请提交失败");
            }
        } catch (error) {
            console.error("提交退款申请失败:", error);
            message.error("提交失败，请稍后重试");
        } finally {
            this.setState({ loading: false });
        }
    };

    render() {
        const { orderId, reason, amount, contact, loading, orderInfo, studentName, idNumber, phoneNumber, selectedSubject, examDate } = this.state;

        // 科目选项
        const subjectOptions = [
            { value: "subject1", label: "科目一" },
            { value: "subject2", label: "科目二" },
            { value: "subject3", label: "科目三" },
            { value: "subject4", label: "科目四" },
        ];

        return (
            <Layout>
                <View className="refund-container">
                    <View className="refund-header">
                        <Text className="refund-title">考试预约退款申请</Text>
                    </View>

                    {orderInfo && (
                        <View className="order-info-card">
                            <View className="order-info-item">
                                <Text className="order-info-label">订单号</Text>
                                <Text className="order-info-value">{orderInfo.orderNo || orderId}</Text>
                            </View>
                            <View className="order-info-item">
                                <Text className="order-info-label">商品名称</Text>
                                <Text className="order-info-value">{orderInfo.productName || "考试预约"}</Text>
                            </View>
                            <View className="order-info-item">
                                <Text className="order-info-label">订单金额</Text>
                                <Text className="order-info-value">¥{orderInfo.amount || "0.00"}</Text>
                            </View>
                            <View className="order-info-item">
                                <Text className="order-info-label">下单时间</Text>
                                <Text className="order-info-value">{orderInfo.createTime || "未知"}</Text>
                            </View>
                        </View>
                    )}

                    <View className="refund-form">
                        {/* 学员信息部分 */}
                        <View className="form-section">
                            <Text className="section-title">学员信息</Text>

                            <View className="form-item">
                                <Text className="form-label">学员姓名</Text>
                                <Input
                                    className="form-input"
                                    type="text"
                                    placeholder="请输入学员姓名"
                                    value={studentName}
                                    onInput={(e) => this.handleInputChange("studentName", e.detail.value)}
                                />
                            </View>

                            <View className="form-item">
                                <Text className="form-label">证件号码</Text>
                                <Input
                                    className="form-input"
                                    type="idcard"
                                    placeholder="请输入身份证号码"
                                    value={idNumber}
                                    onInput={(e) => this.handleInputChange("idNumber", e.detail.value)}
                                />
                            </View>

                            <View className="form-item">
                                <Text className="form-label">手机号码</Text>
                                <Input
                                    className="form-input"
                                    type="number"
                                    placeholder="请输入手机号码"
                                    value={phoneNumber}
                                    onInput={(e) => this.handleInputChange("phoneNumber", e.detail.value)}
                                />
                            </View>
                        </View>

                        {/* 考试信息部分 */}
                        <View className="form-section">
                            <Text className="section-title">考试信息</Text>

                            <View className="form-item">
                                <Text className="form-label">选择科目</Text>
                                <View className="select-container">
                                    {subjectOptions.map((option) => (
                                        <View
                                            key={option.value}
                                            className={`select-option ${selectedSubject === option.value ? "selected" : ""}`}
                                            onClick={() => this.handleInputChange("selectedSubject", option.value)}
                                        >
                                            {option.label}
                                        </View>
                                    ))}
                                </View>
                            </View>

                            <View className="form-item">
                                <Text className="form-label">考试日期</Text>
                                <View className="date-picker-container" onClick={this.handleOpenDatePicker}>
                                    <Input className="form-input date-input" type="text" placeholder="请选择考试日期" value={examDate} disabled />
                                    <View className="date-picker-icon">
                                        <Text className="icon-calendar">日历</Text>
                                    </View>
                                </View>
                            </View>
                        </View>

                        {/* 退款信息部分 */}
                        <View className="form-section">
                            <Text className="section-title">退款信息</Text>

                            <View className="form-item">
                                <Text className="form-label">退款金额</Text>
                                <Input
                                    className="form-input"
                                    type="digit"
                                    placeholder="请输入退款金额"
                                    value={amount}
                                    onInput={(e) => this.handleInputChange("amount", e.detail.value)}
                                />
                            </View>

                            <View className="form-item">
                                <Text className="form-label">退款原因</Text>
                                <Textarea
                                    className="form-textarea"
                                    placeholder="请详细描述退款原因"
                                    value={reason}
                                    onInput={(e) => this.handleInputChange("reason", e.detail.value)}
                                />
                            </View>

                            <View className="form-item">
                                <Text className="form-label">联系方式</Text>
                                <Input
                                    className="form-input"
                                    type="text"
                                    placeholder="请输入手机号码"
                                    value={contact}
                                    onInput={(e) => this.handleInputChange("contact", e.detail.value)}
                                />
                            </View>
                        </View>

                        <View className="refund-tips">
                            <Text className="tips-title">退款说明：</Text>
                            <Text className="tips-content">1. 退款申请提交后，需要等待驾校的审核，具体的退款周期请咨询驾校。</Text>
                            <Text className="tips-content">2. 退款金额将以微信红包的方式发放到你的微信零钱账户</Text>
                            <Text className="tips-content">3. 如有疑问，请联系驾校工作人员</Text>
                        </View>

                        <View className="refund-warning">
                            <Text className="warning-title">重要提示：</Text>
                            <Text className="warning-content">
                                当前微信必须是学员本人已实名认证的微信，否则无法接收到退款款项。退款周期及具体操作请咨询驾校客服。如需要帮助，请联系驾校客服人员。
                            </Text>
                        </View>

                        <Button className="submit-button" loading={loading} onClick={this.handleSubmit}>
                            提交退款申请
                        </Button>
                    </View>
                </View>
            </Layout>
        );
    }
}
