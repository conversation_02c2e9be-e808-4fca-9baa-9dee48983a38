.important-warning-modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 1;
    transition: opacity 0.3s ease;

    &.closing {
        opacity: 0;
    }

    .modal-content {
        width: 600rpx;
        background-color: #fff;
        border-radius: 16rpx;
        overflow: hidden;
        transform: translateY(0);
        transition: transform 0.3s ease;

        &.closing {
            transform: translateY(100%);
        }
    }

    .modal-header {
        padding: 20rpx;
        text-align: center;
        border-bottom: 1rpx solid #f0f0f0;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        text-align: center;
        width: 100%;
    }

    .modal-body {
        padding: 32rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 24rpx;
    }

    .warning-icon {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        background-color: #ff4d4f;
        color: #fff;
        font-size: 32rpx;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .warning-text {
        font-size: 28rpx;
        color: #333;
        line-height: 2.2;
        text-align: left;
        flex: 1;
    }

    // .modal-footer {
    //     display: flex;
    //     border-top: 1rpx solid #f0f0f0;
    // }

    .modal-footer {
        padding: 24rpx 40rpx;
        display: flex;
        justify-content: flex-end;
        gap: 24rpx;
        border-top: 2rpx solid #eee;
    }

    .cancel-btn,
    .confirm-btn {
        height: 50rpx;
        flex: 1;
        line-height: 88rpx;
        text-align: center;
        font-size: 28rpx;
        border: none;
        border-radius: 0;
        background: none;
        position: relative;
        overflow: hidden;

        &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.05);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        &:active::after {
            opacity: 1;
        }
    }

    .cancel-btn {
        color: #666;
        border-right: 1rpx solid #f0f0f0;
    }

    .confirm-btn {
        color: #2b65d9;
        font-weight: 500;
    }
}
