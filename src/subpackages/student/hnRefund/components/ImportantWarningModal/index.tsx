import { Component } from "react";
import { View, Text, Button } from "@tarojs/components";
import "./index.scss";

interface IProps {
    title?: string;
    confirmText?: string;
    cancelText?: string;
}

interface IState {
    visible: boolean;
    content: string;
    onConfirm: () => void;
    onCancel: () => void;
    isClosing: boolean;
}

export default class ImportantWarningModal extends Component<IProps, IState> {
    state: IState = {
        visible: false,
        content: "",
        onConfirm: () => {},
        onCancel: () => {},
        isClosing: false,
    };

    componentDidUpdate(prevProps: IProps, prevState: IState) {
        if (prevState.visible && !this.state.visible) {
            this.setState({ isClosing: true });
            setTimeout(() => {
                this.setState({ isClosing: false });
            }, 300);
        }
    }

    handleMaskClick = (e: any) => {
        e.stopPropagation();
        this.state.onCancel();
    };

    open = (content: string, onConfirm: () => void, onCancel: () => void) => {
        this.setState({
            visible: true,
            content,
            onConfirm,
            onCancel,
        });
    };

    close = () => {
        this.setState({ visible: false });
    };

    render() {
        const { title = "重要提示", confirmText = "我已知晓", cancelText = "返回" } = this.props;
        const { visible, content, onConfirm, onCancel, isClosing } = this.state;

        if (!visible && !isClosing) return null;

        return (
            <View className={`important-warning-modal-mask ${isClosing ? "closing" : ""}`} onClick={this.handleMaskClick}>
                <View className={`modal-content ${isClosing ? "closing" : ""}`} onClick={(e) => e.stopPropagation()}>
                    <View className="modal-header">
                        <Text className="modal-title">{title}</Text>
                    </View>
                    <View className="modal-body">
                        <View className="warning-icon">!</View>
                        <Text className="warning-text">{content}</Text>
                    </View>
                    <View className="modal-footer">
                        {cancelText && (
                            <Button
                                className="cancel-btn"
                                onClick={() => {
                                    this.setState({ visible: false });
                                    onCancel();
                                }}
                            >
                                {cancelText}
                            </Button>
                        )}
                        <Button
                            className="confirm-btn"
                            onClick={() => {
                                this.setState({ visible: false });
                                onConfirm();
                            }}
                        >
                            {confirmText}
                        </Button>
                    </View>
                </View>
            </View>
        );
    }
}
