import { View, Text, Button } from "@tarojs/components";
import { useState, useEffect } from "react";
import request from "@/service/request";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import "./index.scss";
import { Close } from "@nutui/icons-react-taro";

interface ExamItem {
    Id: string;
    KeMuText: string;
    KeMuId: number;
    ResultText: string;
    ksrq: string;
    kc: string;
    cc: string;
    cj: number;
    Times: number;
    zjcx: string;
}

interface ExamInfoModalProps {
    visible: boolean;
    onClose: () => void;
    onSelect: (examInfo: any) => void;
}

const ExamInfoModal: React.FC<ExamInfoModalProps> = ({ visible, onClose, onSelect }) => {
    const [examList, setExamList] = useState<ExamItem[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [current, setCurrent] = useState(1);
    const pageSize = 10;

    // 查询考试信息
    const fetchExamData = async (isLoadMore = false) => {
        if (isLoading) return;
        setIsLoading(true);

        try {
            const res = await request.post<API.Result<ExamItem[]>>("/Jx/Exam/WxResult/getMyExamList", {
                current,
                pageSize,
                ResultIds: [2, 3, 7],
            });
            setIsLoading(false);

            if (res.success) {
                const newList = res.data || [];
                if (isLoadMore) {
                    setExamList((prev) => [...prev, ...newList]);
                } else {
                    setExamList(newList);
                }
                setHasMore(newList.length === pageSize);
            } else {
                message.error("查询失败，请稍后再试");
            }
        } catch (error) {
            setIsLoading(false);
            message.error("查询出错，请稍后再试");
            console.error("Query error:", error);
        }
    };

    // 处理加载更多
    const handleLoadMore = () => {
        if (!hasMore || isLoading) return;
        setCurrent((prev) => prev + 1);
        fetchExamData(true);
    };

    // 获取状态对应的样式类
    const getStatusClass = (status: string) => {
        switch (status) {
            case "合格":
                return "status-pass";
            case "待考":
                return "status-waiting";
            case "缺考":
                return "status-absent";
            default:
                return "status-default";
        }
    };

    // 格式化日期
    const formatDate = (dateStr: string) => {
        if (!dateStr || dateStr === "0001-01-01 00:00:00") return "暂无";
        return dateStr.split(" ")[0];
    };

    useEffect(() => {
        if (visible) {
            setCurrent(1);
            fetchExamData();
        }
    }, [visible]);

    if (!visible) return null;

    return (
        <View className="exam-info-modal">
            <View className="modal-mask" onClick={onClose} />
            <View className="modal-content">
                <View className="modal-header">
                    <Text className="modal-title">选择考试记录 或 其他退费项目</Text>
                    <View className="modal-close" onClick={onClose}>
                        <Close size="18" />
                    </View>
                </View>
                <View className="modal-body">
                    <View
                        className="exam-record-item certification-fee"
                        onClick={() => {
                            message.openConfirm("请确认是否申请制证费退款？\n注意：需要完成科目三的预约后才能申请制证费退款", "确认选择", () => {
                                onSelect({
                                    KeMuId: "999",
                                    examDate: "1900-01-01",
                                    examTime: "",
                                    location: "",
                                    status: "待处理",
                                    score: 0,
                                    times: 0,
                                    zjcx: "",
                                });
                            });
                        }}
                    >
                        <View className="record-header">
                            <Text className="subject">制证费退款申请</Text>
                        </View>
                        <View className="record-info">
                            <Text className="info-item warning-text">注意：需要完成科目三的预约后才能申请制证费退款</Text>
                        </View>
                    </View>
                    {examList.map((item) => (
                        <View
                            key={item.Id}
                            className="exam-record-item"
                            onClick={() => {
                                message.openConfirm(`是否确认申请选择 ${item.KeMuText}  ${formatDate(item.ksrq)} 的考试记录申请相应的退费？`, "确认选择", () => {
                                    onSelect({
                                        KeMuId: item.KeMuId,
                                        examDate: formatDate(item.ksrq),
                                        examTime: item.cc,
                                        location: item.kc,
                                        status: item.ResultText,
                                        score: item.cj,
                                        times: item.Times,
                                        zjcx: item.zjcx,
                                    });
                                });
                            }}
                        >
                            <View className="record-header">
                                <Text className="subject">{item.KeMuText}</Text>
                                <Text className={`status ${getStatusClass(item.ResultText)}`}>{item.ResultText}</Text>
                            </View>
                            <View className="record-info">
                                <Text className="info-item">考试日期：{formatDate(item.ksrq)}</Text>
                                <Text className="info-item">考试时间：{item.cc || "暂无"}</Text>
                                <Text className="info-item">考试地点：{item.kc || "暂无"}</Text>
                                {/* <Text className="info-item">考试车型：{item.zjcx || "暂无"}</Text> */}
                                {item.cj > 0 && <Text className="info-item">考试成绩：{item.cj}分</Text>}
                                <Text className="info-item">考试次数：第{item.Times}次</Text>
                            </View>
                        </View>
                    ))}
                    {hasMore && (
                        <View className="load-more" onClick={handleLoadMore}>
                            {isLoading ? (
                                <View className="loading-wrapper">
                                    <View className="loading-spinner"></View>
                                    <Text className="loading-text">加载中...</Text>
                                </View>
                            ) : (
                                "点击加载更多"
                            )}
                        </View>
                    )}
                </View>
                <View className="modal-footer">
                    <Button className="cancel-btn" onClick={onClose}>
                        取消
                    </Button>
                </View>
            </View>
        </View>
    );
};

export default ExamInfoModal;
