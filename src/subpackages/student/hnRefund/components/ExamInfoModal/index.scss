.exam-info-modal {
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular, "Microsoft Yahei", sans-serif;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;

    .modal-mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.6);
    }

    .modal-content {
        position: relative;
        width: 90%;
        max-height: 80vh;
        background-color: #fff;
        border-radius: 24rpx;
        overflow: hidden;
        z-index: 1001;
    }

    .modal-header {
        padding: 24rpx;
        border-bottom: 2rpx solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .modal-title {
            font-size: 30rpx;
            // font-weight: 500;
            // color: #333;
        }

        .modal-close {
            padding: 10rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            .close-icon {
                font-size: 48rpx;
                color: #999;
                line-height: 1;
            }

            &:active {
                opacity: 0.7;
            }
        }
    }

    .modal-body {
        padding: 24rpx;
        max-height: calc(80vh - 280rpx);
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 20rpx;

        .exam-record-item {
            background-color: #f8f8f8;
            border-radius: 16rpx;
            padding: 24rpx;
            cursor: pointer;
            width: 100%;

            &:active {
                background-color: #f0f0f0;
            }

            .record-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20rpx;

                .subject {
                    font-size: 32rpx;
                    font-weight: 500;
                    color: #333;
                }

                .status {
                    font-size: 28rpx;
                    padding: 4rpx 16rpx;
                    border-radius: 8rpx;
                    border: 2rpx solid;

                    &.status-pass {
                        background-color: #f6ffed;
                        border-color: #b7eb8f;
                        color: #52c41a;
                    }

                    &.status-waiting {
                        background-color: #e6f7ff;
                        border-color: #91d5ff;
                        color: #1890ff;
                    }

                    &.status-absent {
                        background-color: #fff2f0;
                        border-color: #ffccc7;
                        color: #ff4d4f;
                    }

                    &.status-default {
                        background-color: #f5f5f5;
                        border-color: #d9d9d9;
                        color: #666666;
                    }
                }
            }

            .record-info {
                .info-item {
                    display: block;
                    font-size: 24rpx;
                    color: #666;
                    margin-bottom: 10rpx;
                }
            }
        }

        .load-more {
            text-align: center;
            padding: 20rpx 0;
            color: #1890ff;
            font-size: 28rpx;
            cursor: pointer;

            .loading-wrapper {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10rpx;

                .loading-icon {
                    animation: spin 1s linear infinite;
                }

                .loading-text {
                    color: #1890ff;
                }
            }

            &:active {
                opacity: 0.8;
            }
        }
    }

    .modal-footer {
        padding: 24rpx 40rpx;
        display: flex;
        justify-content: center;
        border-top: 2rpx solid #eee;

        .cancel-btn {
            width: 100%;
            height: 88rpx;
            background: #f5f5f5;
            color: #666;
            font-size: 32rpx;
            border: none;
            border-radius: 44rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            &::after {
                border: none;
                border-radius: 44rpx;
            }

            &:active {
                opacity: 0.8;
            }
        }
    }
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
