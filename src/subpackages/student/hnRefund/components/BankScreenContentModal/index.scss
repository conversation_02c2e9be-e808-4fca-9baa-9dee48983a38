.bank-screen-modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 1;
    transition: opacity 0.3s ease;

    &.closing {
        opacity: 0;
    }

    .modal-content {
        width: 80%;
        background-color: #fff;
        border-radius: 12rpx;
        overflow: hidden;
        transform: scale(1);
        transition: transform 0.3s ease;

        &.closing {
            transform: scale(0.8);
        }
    }

    .modal-header {
        padding: 32rpx;
        text-align: center;
        border-bottom: 1rpx solid #eee;
    }

    .modal-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
    }

    .modal-body {
        padding: 32rpx;
        display: flex;
        align-items: center;
        gap: 24rpx;
    }

    .warning-icon {
        flex-shrink: 0;
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background-color: #ff4d4f;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 48rpx;
        font-weight: bold;
    }

    .warning-text {
        flex: 1;
        font-size: 28rpx;
        color: #333;
        line-height: 1.5;
    }

    .modal-footer {
        padding: 24rpx 32rpx 32rpx;
        display: flex;
        justify-content: center;
    }

    .confirm-btn {
        width: 100%;
        height: 88rpx;
        background: #1890ff;
        color: #fff;
        font-size: 32rpx;
        border: none;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        &::after {
            border: none;
            border-radius: 44rpx;
        }

        &:active {
            opacity: 0.8;
        }
    }
}
