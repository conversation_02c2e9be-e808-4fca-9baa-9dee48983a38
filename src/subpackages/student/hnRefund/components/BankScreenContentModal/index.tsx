import { Component } from "react";
import { View, Text, Button } from "@tarojs/components";
import "./index.scss";

interface IProps {
    visible: boolean;
    content: string;
    onConfirm: () => void;
    onCancel: () => void;
}

interface IState {
    isClosing: boolean;
}

export default class BankScreenContentModal extends Component<IProps, IState> {
    state: IState = {
        isClosing: false,
    };

    componentDidUpdate(prevProps: IProps) {
        if (prevProps.visible && !this.props.visible) {
            this.setState({ isClosing: true });
            setTimeout(() => {
                this.setState({ isClosing: false });
            }, 300);
        }
    }

    handleMaskClick = (e: any) => {
        e.stopPropagation();
        this.props.onCancel();
    };

    render() {
        const { visible, content, onConfirm, onCancel } = this.props;
        const { isClosing } = this.state;

        if (!visible && !isClosing) return null;

        return (
            <View className={`bank-screen-modal-mask ${isClosing ? "closing" : ""}`} onClick={this.handleMaskClick}>
                <View className={`modal-content ${isClosing ? "closing" : ""}`} onClick={(e) => e.stopPropagation()}>
                    <View className="modal-header">
                        <Text className="modal-title">重要提示</Text>
                    </View>
                    <View className="modal-body">
                        <View className="warning-icon">!</View>
                        <Text className="warning-text">{content}</Text>
                    </View>
                    <View className="modal-footer">
                        <Button className="confirm-btn" onClick={onConfirm}>
                            我已知晓
                        </Button>
                    </View>
                </View>
            </View>
        );
    }
}
