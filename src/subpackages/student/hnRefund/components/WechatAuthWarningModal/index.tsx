import { Component } from "react";
import { View, Text, Button } from "@tarojs/components";
import "./index.scss";

interface IProps {
    visible: boolean;
    onConfirm: () => void;
    onCancel: () => void;
}

interface IState {
    isClosing: boolean;
}

export default class WechatAuthWarningModal extends Component<IProps, IState> {
    state: IState = {
        isClosing: false,
    };

    componentDidUpdate(prevProps: IProps) {
        if (prevProps.visible && !this.props.visible) {
            this.setState({ isClosing: true });
            setTimeout(() => {
                this.setState({ isClosing: false });
            }, 300);
        }
    }

    handleMaskClick = (e: any) => {
        e.stopPropagation();
        this.props.onCancel();
    };

    render() {
        const { visible, onConfirm, onCancel } = this.props;
        const { isClosing } = this.state;

        if (!visible && !isClosing) return null;

        return (
            <View className={`wechat-auth-modal-mask ${isClosing ? "closing" : ""}`} onClick={this.handleMaskClick}>
                <View className={`modal-content ${isClosing ? "closing" : ""}`} onClick={(e) => e.stopPropagation()}>
                    <View className="modal-header">
                        <Text className="modal-title">重要提示</Text>
                    </View>
                    <View className="modal-body">
                        <View className="warning-icon">!</View>
                        <Text className="warning-text">
                            请确保当前微信已进行实名认证，且实名认证的姓名<span style={{ color: "red" }}>绑定银行卡的名字</span>与后续填写的学员姓名完全一致，否则退款将无法到账！
                        </Text>
                    </View>
                    <View className="modal-footer">
                        <Button className="confirm-btn" onClick={onConfirm}>
                            我已知晓
                        </Button>
                    </View>
                </View>
            </View>
        );
    }
}
