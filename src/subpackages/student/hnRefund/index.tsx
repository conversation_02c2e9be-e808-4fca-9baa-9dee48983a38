import React, { FC, useState, useEffect, useRef } from "react";
import Taro, { useLoad } from "@tarojs/taro";
import { View, Text, Input, Textarea, Button, Image } from "@tarojs/components";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import Layout from "@/components/Layout";
import "./index.scss";
import request from "@/service/request";
import { QValue } from "@/utils/util";
import { login } from "@/utils/login";
import WechatAuthWarningModal from "./components/WechatAuthWarningModal";
import ExamInfoModal from "./components/ExamInfoModal/index";
import BankScreenContentModal from "./components/BankScreenContentModal";
import ImportantWarningModal from "./components/ImportantWarningModal";
import StudentLoginPopup from "@/components/StudentLoginPopup";
import { FaceDetectionViewRef } from "@/components/FaceDetectionView";
import { Close } from "@nutui/icons-react-taro";

interface IState {
    remark: string;
    amount: string;
    contact: string;
    loading: boolean;
    orderInfo: any;
    StudentId: string;
    xm: string;
    sfzmhm: string;
    phone: string;
    selectedSubject: string;
    ksrq: string;
    screenshotId: string;
    screenshotInfo: any;
    showUpload: boolean;
    tempFilePath: string;
    paymentProofPaths: string[];
    uploadProgress: number;
    qrCodeResult: string;
    openId: string;
    bankName: string;
    bankCardNumber: string;
    bankAccountName: string;
    bankBranchName: string;
    tenantName: string;
    lsh: string;
    zjcx: string;
    kcmc: string;
    kscc: string;
    currentStep: number;
    TenantId: string;
    subject1FirstExamRefund: number;
    subject1RetakeExamRefund: number;
    subject2FirstExamRefund: number;
    subject2RetakeExamRefund: number;
    subject3FirstExamRefund: number;
    subject3RetakeExamRefund: number;
    bankScreenshotCount: number;
    bankScreenPDFCount: number;
    bankScreenContent: string;
    enableOnlineApplication: boolean;
    refundMethod: string;
    showAuthWarning: boolean;
    showExamInfoModal: boolean;
    userInfo: any;
    showBankScreenContentModal: boolean;
    showImportantWarning: boolean;
    importantWarningContent: string;
    isFetchingUserInfo: boolean;
    showFaceDetection: boolean;
    faceDetected: boolean;
}

const RefundApplication: FC = () => {
    const studentLoginRef = useRef<any>(null);
    const importantWarningRef = useRef<any>(null);
    const faceDetectionRef = useRef<FaceDetectionViewRef>(null);

    const [state, setState] = useState<IState>({
        remark: "",
        amount: "",
        contact: "",
        loading: false,
        orderInfo: null,
        StudentId: "",
        xm: "",
        sfzmhm: "",
        phone: "",
        selectedSubject: "",
        ksrq: "",
        screenshotId: "",
        screenshotInfo: null,
        showUpload: true,
        tempFilePath: "",
        paymentProofPaths: [],
        uploadProgress: 0,
        qrCodeResult: "",
        openId: "",
        bankName: "",
        bankCardNumber: "",
        bankAccountName: "",
        bankBranchName: "",
        tenantName: "",
        lsh: "",
        zjcx: "",
        kcmc: "",
        kscc: "",
        currentStep: 1,
        TenantId: "",
        subject1FirstExamRefund: 0,
        subject1RetakeExamRefund: 0,
        subject2FirstExamRefund: 0,
        subject2RetakeExamRefund: 0,
        subject3FirstExamRefund: 0,
        subject3RetakeExamRefund: 0,
        bankScreenshotCount: 0,
        bankScreenPDFCount: 0,
        bankScreenContent: "",
        enableOnlineApplication: false,
        refundMethod: "",
        showAuthWarning: false,
        showExamInfoModal: false,
        userInfo: null,
        showBankScreenContentModal: false,
        showImportantWarning: false,
        importantWarningContent: "",
        isFetchingUserInfo: false,
        showFaceDetection: false,
        faceDetected: false,
    });

    useLoad(() => {
        fetchUserInfo().then(() => {});
    });

    useEffect(() => {
        const { currentStep, bankScreenContent } = state;
        if (currentStep === 4 && bankScreenContent && !state.showBankScreenContentModal) {
            setState((prev) => ({ ...prev, showBankScreenContentModal: true }));
        }
    }, [state.currentStep, state.bankScreenContent]);

    const fetchUserInfo = async () => {
        try {
            setState((prev) => ({ ...prev, isFetchingUserInfo: true }));
            const userData = await login(true); // 使用学生登录
            setState((prev) => ({ ...prev, userInfo: userData, TenantId: userData.TenantId }));
            fetchTenantInfo(userData.TenantId);
        } catch (error) {
            console.error("获取用户信息失败:", error);
            const tenantId = QValue("TenantId");
            if (!tenantId) {
                console.log("打开 登录窗口");
                console.log(studentLoginRef);
                studentLoginRef.current?.open();
            } else {
                fetchTenantInfo(tenantId);
                setState((prev) => ({ ...prev, TenantId: tenantId }));
            }
        } finally {
            setState((prev) => ({ ...prev, isFetchingUserInfo: false }));
        }
    };

    const fetchTenantInfo = async (tenantId: string) => {
        try {
            const response = await request.post<API.Result<any>>(`/Tenant/TenantInfo/${tenantId}`);
            const refundConfigRes = await request.post<API.Result<any>>("/Config/JxRefundConfig/getRefundConfig", {
                TenantId: tenantId,
            });

            if (refundConfigRes.data) {
                const {
                    Subject1FirstExamRefund,
                    Subject1RetakeExamRefund,
                    Subject2FirstExamRefund,
                    Subject2RetakeExamRefund,
                    Subject3FirstExamRefund,
                    Subject3RetakeExamRefund,
                    BankScreenshotCount,
                    BankScreenPDFCount,
                    BankScreenContent,
                    EnableOnlineApplication,
                    RefundMethod,
                } = refundConfigRes.data;

                if (RefundMethod !== "WeChatRedPacket" && RefundMethod !== "BankTransfer") {
                    message.error("当前公司不支持退费申请，如有疑问，请直接联系驾校工作人员");
                    return;
                }
                if (!EnableOnlineApplication) {
                    message.error("当前公司不支持退费申请，如有疑问，请直接联系驾校工作人员");
                    return;
                }

                setState((prev) => ({
                    ...prev,
                    subject1FirstExamRefund: Subject1FirstExamRefund || 0,
                    subject1RetakeExamRefund: Subject1RetakeExamRefund || 0,
                    subject2FirstExamRefund: Subject2FirstExamRefund || 0,
                    subject2RetakeExamRefund: Subject2RetakeExamRefund || 0,
                    subject3FirstExamRefund: Subject3FirstExamRefund || 0,
                    subject3RetakeExamRefund: Subject3RetakeExamRefund || 0,
                    refundMethod: RefundMethod || "",
                    bankScreenshotCount: BankScreenshotCount || 0,
                    bankScreenPDFCount: BankScreenPDFCount || 0,
                    bankScreenContent: BankScreenContent || "",
                    enableOnlineApplication: EnableOnlineApplication || false,
                }));

                if (RefundMethod === "WeChatRedPacket") {
                    setState((prev) => ({ ...prev, showAuthWarning: true }));
                }
            }

            if (response.success) {
                setState((prev) => ({
                    ...prev,
                    tenantName: response.data.TenantName || "",
                }));
            } else {
                message.error("获取公司信息失败");
            }
        } catch (error) {
            console.error("获取租户信息失败:", error);
            message.error("获取公司信息失败，请稍后重试");
        }
    };

    const handleInputChange = (field: string, value: string) => {
        setState((prev) => ({ ...prev, [field]: value }));
    };

    const handleChooseFile = () => {
        Taro.chooseImage({
            count: 1,
            sizeType: ["compressed"],
            sourceType: ["album"],
            success: (res) => {
                const file = res.tempFilePaths[0];
                setState((prev) => ({
                    ...prev,
                    tempFilePath: file,
                    loading: true,
                }));
                processFile(file);
            },
            fail: () => {
                message.error("选择图片失败，请重试");
            },
        });
    };

    const handleChoosePaymentFile = () => {
        const { bankScreenshotCount, bankScreenPDFCount, paymentProofPaths } = state;

        if (bankScreenshotCount > 0 && bankScreenPDFCount > 0) {
            // 同时支持图片和PDF
            Taro.showActionSheet({
                itemList: ["选择图片", "选择PDF文件"],
                success: (res) => {
                    if (res.tapIndex === 0) {
                        // 选择图片
                        const remainingCount = bankScreenshotCount - paymentProofPaths.length;
                        if (remainingCount <= 0) {
                            message.error(`最多只能上传${bankScreenshotCount}个文件`);
                            return;
                        }
                        Taro.chooseImage({
                            count: remainingCount,
                            sizeType: ["original", "compressed"],
                            sourceType: ["album"],
                            success: (res) => {
                                setState((prev) => ({
                                    ...prev,
                                    paymentProofPaths: [...prev.paymentProofPaths, ...res.tempFilePaths],
                                    paymentProofPath: res.tempFilePaths[0], // 保持第一个文件作为主显示
                                }));
                            },
                            fail: (err) => {
                                console.error("选择图片失败:", err);
                                message.error("选择文件失败，请重试");
                            },
                        });
                    } else if (res.tapIndex === 1) {
                        // 选择PDF
                        const remainingCount = bankScreenPDFCount - paymentProofPaths.length;
                        if (remainingCount <= 0) {
                            message.error(`最多只能上传${bankScreenPDFCount}个文件`);
                            return;
                        }
                        Taro.chooseMessageFile({
                            count: remainingCount,
                            type: "file",
                            extension: ["pdf"],
                            success: (res) => {
                                const newPaths = res.tempFiles.map((file) => file.path);
                                setState((prev) => ({
                                    ...prev,
                                    paymentProofPaths: [...prev.paymentProofPaths, ...newPaths],
                                    paymentProofPath: newPaths[0], // 保持第一个文件作为主显示
                                }));
                            },
                            fail: (err) => {
                                console.error("选择PDF失败:", err);
                                message.error("选择文件失败，请重试");
                            },
                        });
                    }
                },
            });
        } else if (bankScreenshotCount > 0) {
            // 只支持图片
            const remainingCount = bankScreenshotCount - paymentProofPaths.length;
            if (remainingCount <= 0) {
                message.error(`最多只能上传${bankScreenshotCount}个文件`);
                return;
            }
            Taro.chooseImage({
                count: remainingCount,
                sizeType: ["original", "compressed"],
                sourceType: ["album"],
                success: (res) => {
                    setState((prev) => ({
                        ...prev,
                        paymentProofPaths: [...prev.paymentProofPaths, ...res.tempFilePaths],
                        paymentProofPath: res.tempFilePaths[0], // 保持第一个文件作为主显示
                    }));
                },
                fail: (err) => {
                    console.error("选择图片失败:", err);
                    message.error("选择文件失败，请重试");
                },
            });
        } else if (bankScreenPDFCount > 0) {
            // 只支持PDF
            const remainingCount = bankScreenPDFCount - paymentProofPaths.length;
            if (remainingCount <= 0) {
                message.error(`最多只能上传${bankScreenPDFCount}个文件`);
                return;
            }
            Taro.chooseMessageFile({
                count: remainingCount,
                type: "file",
                extension: ["pdf"],
                success: (res) => {
                    const newPaths = res.tempFiles.map((file) => file.path);
                    setState((prev) => ({
                        ...prev,
                        paymentProofPaths: [...prev.paymentProofPaths, ...newPaths],
                        paymentProofPath: newPaths[0], // 保持第一个文件作为主显示
                    }));
                },
                fail: (err) => {
                    console.error("选择PDF失败:", err);
                    message.error("选择文件失败，请重试");
                },
            });
        } else {
            message.error("暂不支持上传文件");
        }
    };

    const handleRemoveFile = (index: number) => {
        setState((prev) => {
            const newPaths = [...prev.paymentProofPaths];
            newPaths.splice(index, 1);
            return {
                ...prev,
                paymentProofPaths: newPaths,
                paymentProofPath: newPaths[0] || "", // 更新主显示文件
            };
        });
    };

    const handleNextStep = () => {
        const { currentStep, refundMethod, xm, sfzmhm, phone, selectedSubject, ksrq, bankName, bankCardNumber, bankBranchName } = state;

        if (currentStep === 1) {
            if (!xm) {
                message.error("请填写学员姓名");
                return;
            }
            if (!sfzmhm) {
                message.error("请填写证件号码");
                return;
            }
            if (!phone) {
                message.error("请填写手机号码");
                return;
            }
        } else if (currentStep === 2) {
            if (!selectedSubject) {
                message.error("请选择考试科目");
                return;
            }
            if (!ksrq) {
                message.error("请选择考试日期");
                return;
            }
        } else if (currentStep === 3 && refundMethod === "BankTransfer") {
            if (!bankName) {
                message.error("请填写开户银行");
                return;
            }
            if (!bankCardNumber) {
                message.error("请填写银行卡号");
                return;
            }
            if (!bankBranchName) {
                message.error("请填写开户支行");
                return;
            }
        }

        const nextStep = currentStep + 1;
        if (refundMethod === "WeChatRedPacket" && currentStep === 2) {
            setState((prev) => ({ ...prev, currentStep: 4 }));
        } else {
            setState((prev) => ({ ...prev, currentStep: nextStep }));
        }
    };

    const handlePrevStep = () => {
        const { currentStep, refundMethod } = state;
        if (currentStep === 1) {
            setState((prev) => ({ ...prev, showUpload: true }));
        } else if (currentStep > 1) {
            if (refundMethod === "WeChatRedPacket" && currentStep === 4) {
                setState((prev) => ({ ...prev, currentStep: 2 }));
            } else {
                setState((prev) => ({ ...prev, currentStep: currentStep - 1 }));
            }
        }
    };

    const handleSubmit = async () => {
        console.log("handleSubmit F1 =================== >>>>>>>>>>>>>>>>>>>>>>>>>");
        try {
            const { xm, sfzmhm, phone, selectedSubject, ksrq, bankName, bankCardNumber, bankBranchName, refundMethod, paymentProofPaths, tempFilePath } = state;

            console.log("handleSubmit F2 =================== >>>>>>>>>>>>>>>>>>>>>>>>>");
            const openId = Taro.getStorageSync("openId");

            console.log("openId", openId);
            if (!openId) {
                console.log("handleSubmit F3 =================== >>>>>>>>>>>>>>>>>>>>>>>>>");
                message.error("用户信息获取失败，请重试");
                return;
            }
            console.log(xm, sfzmhm, phone, selectedSubject, ksrq);

            if (!xm || !sfzmhm || !phone || !selectedSubject || !ksrq) {
                message.error("请填写完整信息");
                return;
            }

            console.log("refundMethod", refundMethod);
            console.log("bankName", bankName);
            console.log("bankCardNumber", bankCardNumber);
            console.log("bankBranchName", bankBranchName);

            let warningContent = "请确保微信实名认证[绑定银行卡的名字]的姓名与填写的学员姓名完全一致，否则退款将失败。";
            if (refundMethod === "BankTransfer") {
                if (!bankName || !bankCardNumber || !bankBranchName) {
                    message.error("请填写完整银行信息");
                    warningContent = "请确保提交的银行卡的信息完全无误，否则退款将失败。";
                    return;
                }
            } else if (refundMethod !== "WeChatRedPacket") {
                message.error("当前系统暂时不支持线上提交退款申请");
            }
            importantWarningRef.current?.open(
                warningContent,
                async () => {
                    try {
                        const tmplIds = [
                            "YDxrRATd2Aju6uNKNllzQciNhEnF_u9-zipQfi21In8",
                            "zusywVlM2yVEAW9VWJdWv7GUxCZ2aKbU4edfPrXN-AM",
                            "eHZfVK2qyvyQq97WPQBicCLv7aWWCtfTJ9rWd2-5r9Q",
                        ];
                        await Taro.requestSubscribeMessage({
                            tmplIds,
                            entityIds: tmplIds,
                        } as any)
                            .then(() => {
                                submitRefundRequest();
                            })
                            .catch(() => {
                                submitRefundRequest();
                            });
                    } catch (error) {
                        console.error("订阅消息出错", error);
                        submitRefundRequest();
                    }
                },
                () => {
                    // setState((prev) => ({ ...prev, showUpload: true }));
                }
            );
            return;
        } catch (error) {
            console.error("handleSubmit F1 error", error);
            return;
        }
    };

    const handleAuthWarningConfirm = () => {
        setState((prev) => ({ ...prev, showAuthWarning: false }));
    };

    const handleAuthWarningCancel = () => {
        setState((prev) => ({ ...prev, showAuthWarning: false }));
        Taro.navigateBack();
    };

    const handleExamInfoSelect = async (examInfo: any) => {
        setState((prev) => ({
            ...prev,
            showExamInfoModal: false,
            selectedSubject: examInfo.KeMuId.toString(),
            ksrq: examInfo.examDate,
            kcmc: examInfo.location,
            kscc: examInfo.examTime,
        }));

        if (state.userInfo && state.userInfo.sfzmhm) {
            const fetchSuccess = await fetchStudentInfo(
                state.userInfo.sfzmhm,
                state.userInfo.xm,
                examInfo.lsh || "",
                examInfo.zjcx || "",
                examInfo.location,
                examInfo.examTime,
                examInfo.examDate
            );
            if (fetchSuccess) {
                setState((prev) => ({ ...prev, showUpload: false }));
            }
        }
    };

    const handleBankScreenContentConfirm = () => {
        setState((prev) => ({ ...prev, showBankScreenContentModal: false }));
    };

    const handleBankScreenContentCancel = () => {
        setState((prev) => ({ ...prev, showBankScreenContentModal: false }));
    };

    const handleStudentLoginSuccess = () => {
        fetchUserInfo();
    };

    const processFile = async (filePath: string) => {
        message.openLoading("正在处理文件...");
        try {
            const response = await request.uploadFile<API.Result<string>>("/Base/Image/RecognizeQrCode", filePath, "file", {});

            if (response.success) {
                const qrData = response.data;
                console.log(qrData);

                const dataParts = qrData.split("@");

                if (dataParts.length !== 7) {
                    message.error("上传截图解析失败，请重新截屏上传");
                    setState((prev) => ({ ...prev, loading: false }));
                    return;
                }

                const [xm, sfzmhm, lsh, zjcx, ksrq, kcmc, kscc] = dataParts;

                if (!xm || !sfzmhm || !lsh || !zjcx || !ksrq || !kcmc || !kscc) {
                    message.error("上传截图解析失败，请重新截屏上传");
                    setState((prev) => ({ ...prev, loading: false }));
                    return;
                }

                const fetchSuccess = await fetchStudentInfo(sfzmhm, xm, lsh, zjcx, kcmc, kscc, ksrq);
                if (!fetchSuccess) {
                    return;
                }
            } else {
                message.error("上传截图解析失败，请重新截屏上传");
            }
        } catch (error) {
            console.error("处理文件失败:", error);
            message.error("处理失败，请重试");
        } finally {
            setState((prev) => ({ ...prev, loading: false }));
            message.closeLoading();
        }
    };

    const fetchStudentInfo = async (sfzmhm: string, xm: string, lsh: string, zjcx: string, kcmc: string, kscc: string, ksrq: string) => {
        try {
            const maskIdCard = (idCard: string) => {
                if (!idCard) return "";
                return `${idCard.slice(0, 6)}******${idCard.slice(-4)}`;
            };
            if (sfzmhm !== state.userInfo.sfzmhm) {
                message.error(
                    `证件号码不一致，上传截图证件为 ${maskIdCard(sfzmhm)}，当前登录的账号身份证为 ${maskIdCard(
                        state.userInfo.sfzmhm
                    )}，请检查是否登录错误，如果您无法提供预约截图，您可以直接点击选择考试记录进行下一步的操作!`
                );
                setState((prev) => ({ ...prev, showUpload: true }));
                return false;
            }
            const studentResponse = await request.post<any>("/Jx/Student/Audit/WxRefundAudit/getStudentByIDCard", {
                sfzmhm: sfzmhm,
                TenantId: state.TenantId,
            });

            if (studentResponse.success) {
                setState((prev) => ({
                    ...prev,
                    screenshotInfo: {
                        xm: studentResponse.data.xm || xm,
                        sfzmhm,
                        lsh,
                        zjcx,
                        ksrq,
                        kcmc,
                        kscc,
                    },
                    StudentId: studentResponse.data.Id,
                    xm: studentResponse.data.xm || xm,
                    phone: studentResponse.data.yddh || "",
                    sfzmhm: sfzmhm,
                    lsh: lsh,
                    zjcx: zjcx,
                    kcmc: kcmc,
                    kscc: kscc,
                    ksrq: ksrq,
                    showUpload: false,
                }));

                if (state.refundMethod === "WeChatRedPacket") {
                    importantWarningRef.current?.open(
                        `当前微信必须是 ${studentResponse.data.xm || xm} 做的实名认证，否则，退款将无法到账！`,
                        () => {},
                        () => {
                            setState((prev) => ({ ...prev, showUpload: true }));
                        }
                    );
                }
                return true;
            } else {
                message.error(studentResponse.message || "获取学员信息失败");
                setState((prev) => ({ ...prev, loading: false }));
                return false;
            }
        } catch (studentError) {
            console.error("获取学员信息失败:", studentError);
            // message.error("获取学员信息失败，请重试");
            setState((prev) => ({ ...prev, loading: false }));
            return false;
        }
    };

    const submitRefundRequest = async () => {
        const {
            StudentId,
            xm,
            sfzmhm,
            phone,
            selectedSubject,
            ksrq,
            remark,
            bankName,
            bankCardNumber,
            bankBranchName,
            openId,
            refundMethod,
            paymentProofPaths,
            tempFilePath,
            lsh,
            zjcx,
            kcmc,
            kscc,
            TenantId,
            bankScreenPDFCount,
            bankScreenshotCount,
        } = state;

        // 检查支付凭证上传情况
        if (bankScreenPDFCount > 0 || bankScreenshotCount > 0) {
            if (paymentProofPaths.length === 0) {
                message.error("请上传支付凭证");
                return;
            }

            // 检查上传数量是否符合要求
            const maxAllowedFiles = Math.max(bankScreenPDFCount, bankScreenshotCount);
            if (paymentProofPaths.length > maxAllowedFiles) {
                message.error(`最多只能上传${maxAllowedFiles}个文件`);
                return;
            }
        }

        // if (!tempFilePath) {
        //     message.error("请上传预约截图");
        //     return;
        // }

        setState((prev) => ({ ...prev, loading: true }));

        try {
            const response: API.Result<any> = await request.post("/Jx/Student/Audit/WxRefundAudit/submit", {
                StudentId,
                xm,
                sfzmhm,
                phone,
                KeMuId: selectedSubject,
                ksrq,
                remark,
                bankName,
                bankCardNumber,
                bankBranchName,
                openId,
                refundMethod,
                lsh,
                zjcx,
                kcmc,
                kscc,
                TenantId,
            });

            if (response.success) {
                const refundId = response.data;

                try {
                    if (tempFilePath) {
                        await request.uploadFile("/Jx/Student/Audit/WxRefundAudit/uploadScreenshot", tempFilePath, "screenshot", {
                            id: refundId,
                        });
                    }
                } catch (error) {
                    console.error("上传预约截图失败:", error);
                    message.error("上传预约截图失败，但退款申请已提交成功");
                }

                try {
                    // 使用 Promise.all 并行上传所有支付凭证
                    if (paymentProofPaths.length > 0) {
                        const uploadPromises = paymentProofPaths.map((filePath) =>
                            request.uploadFile("/Jx/Student/Audit/WxRefundAudit/uploadBankCardScreenshot", filePath, "bankCardScreenshot", {
                                id: refundId,
                            })
                        );
                        await Promise.all(uploadPromises);
                    }
                } catch (error) {
                    console.error("上传支付凭证失败:", error);
                    message.error("上传支付凭证失败，但退款申请已提交成功");
                }

                message.success("退款申请提交成功");

                setState((prev) => ({
                    ...prev,
                    remark: "",
                    amount: "",
                    contact: "",
                    loading: false,
                    orderInfo: null,
                    StudentId: "",
                    xm: "",
                    sfzmhm: "",
                    phone: "",
                    selectedSubject: "",
                    ksrq: "",
                    screenshotId: "",
                    screenshotInfo: null,
                    showUpload: true,
                    tempFilePath: "",
                    paymentProofPaths: [],
                    uploadProgress: 0,
                    qrCodeResult: "",
                    openId: "",
                    bankName: "",
                    bankCardNumber: "",
                    bankAccountName: "",
                    bankBranchName: "",
                    tenantName: "",
                    lsh: "",
                    zjcx: "",
                    kcmc: "",
                    kscc: "",
                    currentStep: 1,
                }));
            } else {
                message.error(response.message || "退款申请提交失败");
            }
        } catch (error) {
            console.error("提交退款申请失败:", error);
            message.error("提交失败，请稍后重试");
        } finally {
            setState((prev) => ({ ...prev, loading: false }));
        }
    };

    const {
        loading,
        xm,
        sfzmhm,
        phone,
        lsh,
        zjcx,
        ksrq,
        kcmc,
        kscc,
        showUpload,
        tempFilePath,
        paymentProofPaths,
        bankName,
        bankCardNumber,
        bankBranchName,
        tenantName,
        selectedSubject,
        remark,
        currentStep,
        refundMethod,
        showAuthWarning,
        showExamInfoModal,
        bankScreenContent,
        showBankScreenContentModal,
        isFetchingUserInfo,
        bankScreenshotCount,
        bankScreenPDFCount,
    } = state;

    if (showUpload) {
        return (
            <Layout>
                <View className="upload-container">
                    <View className="upload-header">
                        <Text className="upload-title">上传12123预约成功截图（带二维码）</Text>
                    </View>

                    <View className="upload-content">
                        {tempFilePath ? (
                            <View className="preview-container">
                                <Image className="preview-image" src={tempFilePath} mode="aspectFit" />
                                <Button className="choose-again-btn" onClick={handleChooseFile}>
                                    重新选择
                                </Button>
                            </View>
                        ) : (
                            <View className="upload-placeholder" onClick={handleChooseFile}>
                                <Text className="upload-icon">+</Text>
                                <Text className="upload-text">点击上传预约文件</Text>
                            </View>
                        )}
                    </View>
                    <View className="upload-tips">
                        <Text className="tips-title">上传说明：</Text>
                        <Text className="tips-content">1. 请上传包含预约二维码的文件（支持jpg、jpeg、png格式）</Text>
                        <Text className="tips-content">2. 系统将自动识别文件中的二维码</Text>
                        <Text className="tips-content">3. 文件大小不超过10MB</Text>
                        <Text className="tips-content">4. 如无法识别，请联系驾校工作人员</Text>
                    </View>
                    <Button
                        className="white-btn"
                        loading={loading}
                        onClick={() => {
                            Taro.navigateTo({
                                url: `/subpackages/student/hnRefund/list/index`,
                            });
                        }}
                    >
                        我的申请记录
                    </Button>
                    {state.userInfo && state.userInfo.xm && state.userInfo.sfzmhm && (
                        <Button
                            className="primary-btn"
                            loading={loading}
                            onClick={() => {
                                setState((prev) => ({ ...prev, showExamInfoModal: true }));
                            }}
                        >
                            选择考试记录
                        </Button>
                    )}
                    {!state?.userInfo && !isFetchingUserInfo && (
                        <Button
                            className="primary-btn"
                            loading={loading}
                            onClick={() => {
                                studentLoginRef.current?.open();
                            }}
                        >
                            学员登录
                        </Button>
                    )}
                    {tenantName && (
                        <View className="tenant-info">
                            <Text className="tenant-name">{tenantName}</Text>
                        </View>
                    )}
                </View>
                <StudentLoginPopup ref={studentLoginRef} onLoginSuccess={handleStudentLoginSuccess} />
                <WechatAuthWarningModal visible={showAuthWarning} onConfirm={handleAuthWarningConfirm} onCancel={handleAuthWarningCancel} />
                <ExamInfoModal visible={showExamInfoModal} onClose={() => setState((prev) => ({ ...prev, showExamInfoModal: false }))} onSelect={handleExamInfoSelect} />
            </Layout>
        );
    }

    const subjectOptions = [
        { value: "1", label: "科目一" },
        { value: "2", label: "科目二" },
        { value: "3", label: "科目三" },
        { value: "999", label: "制证费" },
    ];

    return (
        <Layout>
            <View className="refund-application">
                <View className="refund-container">
                    <View className="steps-container">
                        <View className={`step ${currentStep >= 1 ? "active" : ""}`}>
                            <Text className="step-number">1</Text>
                            <Text className="step-title">基本</Text>
                        </View>
                        <View className={`step-line ${currentStep >= 2 ? "active" : ""}`}></View>
                        <View className={`step ${currentStep >= 2 ? "active" : ""}`}>
                            <Text className="step-number">2</Text>
                            <Text className="step-title">考试</Text>
                        </View>
                        {refundMethod == "BankTransfer" && (
                            <>
                                <View className={`step-line ${currentStep >= 3 ? "active" : ""}`}></View>
                                <View className={`step ${currentStep >= 3 ? "active" : ""}`}>
                                    <Text className="step-number">3</Text>
                                    <Text className="step-title">银行</Text>
                                </View>
                            </>
                        )}
                        <View className={`step-line ${currentStep >= (refundMethod === "WeChatRedPacket" ? 3 : 4) ? "active" : ""}`}></View>
                        <View className={`step ${currentStep >= (refundMethod === "WeChatRedPacket" ? 3 : 4) ? "active" : ""}`}>
                            <Text className="step-number">{refundMethod === "WeChatRedPacket" ? 3 : 4}</Text>
                            <Text className="step-title">提交</Text>
                        </View>
                    </View>

                    <View className="refund-form">
                        {currentStep === 1 && (
                            <View className="form-section">
                                <Text className="section-title">学员信息</Text>
                                <View className="form-item">
                                    <Text className="form-label">学员姓名</Text>
                                    <Input className="form-input" type="text" placeholder="请输入学员姓名" value={xm} onInput={(e) => handleInputChange("xm", e.detail.value)} />
                                </View>
                                <View className="form-item">
                                    <Text className="form-label">证件号码</Text>
                                    <Input
                                        className="form-input"
                                        type="idcard"
                                        placeholder="请输入身份证号码"
                                        value={sfzmhm}
                                        onInput={(e) => handleInputChange("sfzmhm", e.detail.value)}
                                    />
                                </View>
                                <View className="form-item">
                                    <Text className="form-label">手机号码</Text>
                                    <Input
                                        className="form-input"
                                        type="number"
                                        placeholder="请输入手机号码"
                                        value={phone}
                                        onInput={(e) => handleInputChange("phone", e.detail.value)}
                                    />
                                </View>
                            </View>
                        )}

                        {currentStep === 2 && (
                            <View className="form-section">
                                <Text className="section-title">考试信息</Text>
                                <View className="form-item">
                                    <Text className="form-label">选择科目</Text>
                                    <View className="select-container">
                                        {subjectOptions.map((option) => (
                                            <View
                                                key={option.value}
                                                className={`select-option ${selectedSubject === option.value ? "selected" : ""}`}
                                                onClick={() => handleInputChange("selectedSubject", option.value)}
                                            >
                                                {option.label}
                                            </View>
                                        ))}
                                    </View>
                                </View>
                                {selectedSubject !== "999" && (
                                    <>
                                        <View className="form-item">
                                            <Text className="form-label">考试日期</Text>
                                            <Input className="form-input" type="text" value={ksrq} disabled />
                                        </View>
                                        {kcmc && (
                                            <View className="exam-info-card">
                                                <View className="exam-info-item">
                                                    <Text className="exam-info-label">考场名称</Text>
                                                    <Text className="exam-info-value">{kcmc}</Text>
                                                </View>
                                                <View className="exam-info-item">
                                                    <Text className="exam-info-label">考试场次</Text>
                                                    <Text className="exam-info-value">{kscc}</Text>
                                                </View>
                                                <View className="exam-info-item">
                                                    <Text className="exam-info-label">考试车型</Text>
                                                    <Text className="exam-info-value">{zjcx}</Text>
                                                </View>
                                                <View className="exam-info-item">
                                                    <Text className="exam-info-label">流水编号</Text>
                                                    <Text className="exam-info-value">{lsh}</Text>
                                                </View>
                                            </View>
                                        )}
                                    </>
                                )}
                            </View>
                        )}

                        {currentStep === 3 && (
                            <View className="form-section">
                                <Text className="section-title">退款方式</Text>
                                {refundMethod === "WeChatRedPacket" && (
                                    <View className="wechat-warning">
                                        <Text className="warning-text">重要提示：请确保微信实名认证[绑定银行卡的名字]的姓名与填写的学员姓名完全一致，否则退款将失败。</Text>
                                    </View>
                                )}
                                {refundMethod === "BankTransfer" && (
                                    <>
                                        <View className="form-item">
                                            <Text className="form-label">开户银行</Text>
                                            <Input
                                                className="form-input"
                                                type="text"
                                                placeholder="请输入开户银行"
                                                value={bankName}
                                                onInput={(e) => handleInputChange("bankName", e.detail.value)}
                                            />
                                        </View>
                                        <View className="form-item">
                                            <Text className="form-label">银行卡号</Text>
                                            <Input
                                                className="form-input"
                                                type="number"
                                                placeholder="请输入银行卡号"
                                                value={bankCardNumber}
                                                onInput={(e) => handleInputChange("bankCardNumber", e.detail.value)}
                                            />
                                        </View>
                                        <View className="form-item">
                                            <Text className="form-label">开户人姓名</Text>
                                            <Input className="form-input" type="text" placeholder="请输入开户人姓名" value={xm} disabled />
                                        </View>
                                        <View className="form-item">
                                            <Text className="form-label">开户支行</Text>
                                            <Input
                                                className="form-input"
                                                type="text"
                                                placeholder="请输入开户支行"
                                                value={bankBranchName}
                                                onInput={(e) => handleInputChange("bankBranchName", e.detail.value)}
                                            />
                                        </View>
                                    </>
                                )}
                            </View>
                        )}

                        {currentStep === 4 && (
                            <View className="form-section">
                                {bankScreenshotCount > 0 || bankScreenPDFCount > 0 ? (
                                    <>
                                        <Text className="section-title">已缴凭证</Text>
                                        <View className="form-item">
                                            {bankScreenContent && (
                                                <View className="bank-screen-content-wrapper">
                                                    <View className="bank-screen-content-body">
                                                        <Text className="bank-screen-content-text">{bankScreenContent}</Text>
                                                    </View>
                                                </View>
                                            )}
                                            <View
                                                className="upload-container"
                                                style={{
                                                    padding: 0,
                                                }}
                                            >
                                                {paymentProofPaths.length > 0 ? (
                                                    <View className="preview-wrapper">
                                                        <View className="preview-content">
                                                            {state.paymentProofPaths.length === 1 ? (
                                                                <View className="file-preview image-preview">
                                                                    <Image
                                                                        className="preview-image-fullsize"
                                                                        src={paymentProofPaths[0]}
                                                                        mode="widthFix"
                                                                        onClick={() => {
                                                                            Taro.previewImage({
                                                                                current: paymentProofPaths[0],
                                                                                urls: state.paymentProofPaths,
                                                                            });
                                                                        }}
                                                                        onLoad={() => {
                                                                            console.log("图片加载完成");
                                                                            setState((prev) => ({ ...prev, loading: false }));
                                                                        }}
                                                                        onError={() => {
                                                                            console.error("图片加载失败");
                                                                            message.error("图片加载失败");
                                                                            setState((prev) => ({ ...prev, loading: false }));
                                                                        }}
                                                                    />
                                                                    {loading && (
                                                                        <View className="image-loading">
                                                                            <Text className="loading-text">图片加载中...</Text>
                                                                        </View>
                                                                    )}
                                                                </View>
                                                            ) : (
                                                                <View className="grid-preview">
                                                                    {state.paymentProofPaths.map((path, index) => (
                                                                        <View key={index} className="grid-item">
                                                                            <Image
                                                                                className="grid-image"
                                                                                src={path}
                                                                                mode="aspectFill"
                                                                                onClick={() => {
                                                                                    Taro.previewImage({
                                                                                        current: path,
                                                                                        urls: state.paymentProofPaths,
                                                                                    });
                                                                                }}
                                                                            />
                                                                            <View className="grid-remove" onClick={() => handleRemoveFile(index)}>
                                                                                <Close
                                                                                    style={{
                                                                                        color: "#fff",
                                                                                    }}
                                                                                    size={18}
                                                                                />
                                                                            </View>
                                                                        </View>
                                                                    ))}
                                                                </View>
                                                            )}
                                                        </View>
                                                        <View className="preview-actions">
                                                            <View className="action-btn" onClick={handleChoosePaymentFile}>
                                                                <Text className="action-text">
                                                                    {state.paymentProofPaths.length >= (bankScreenshotCount || bankScreenPDFCount) ? "重新上传" : "继续上传"}
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    </View>
                                                ) : (
                                                    <View className="payment-upload-placeholder" onClick={handleChoosePaymentFile}>
                                                        <View className="payment-upload-icon">+</View>
                                                        <Text className="payment-upload-text">点击上传已缴凭证</Text>
                                                        <Text className="payment-upload-tip">
                                                            {bankScreenshotCount > 0 && bankScreenPDFCount > 0
                                                                ? `支持jpg、jpeg、png、pdf格式，最多上传${Math.max(bankScreenshotCount, bankScreenPDFCount)}个文件`
                                                                : bankScreenshotCount > 0
                                                                ? `支持jpg、jpeg、png格式，最多上传${bankScreenshotCount}个文件`
                                                                : bankScreenPDFCount > 0
                                                                ? `支持pdf格式，最多上传${bankScreenPDFCount}个文件`
                                                                : "请选择文件"}
                                                        </Text>
                                                    </View>
                                                )}
                                            </View>
                                        </View>
                                    </>
                                ) : null}

                                <Text className="section-title">申请备注</Text>
                                <View className="form-item">
                                    <Textarea
                                        className="form-textarea"
                                        placeholder="财务审核的时候可以看到您的备注"
                                        value={remark}
                                        onInput={(e) => handleInputChange("remark", e.detail.value)}
                                    />
                                </View>
                            </View>
                        )}

                        <View className="form-buttons">
                            <Button className="prev-button" onClick={handlePrevStep}>
                                上一步
                            </Button>
                            {currentStep < 4 ? (
                                <Button className="next-button" onClick={handleNextStep}>
                                    下一步
                                </Button>
                            ) : (
                                <Button className="submit-button" loading={loading} onClick={handleSubmit}>
                                    提交申请
                                </Button>
                            )}
                        </View>
                    </View>

                    {tenantName && (
                        <View className="tenant-info">
                            <Text className="tenant-name">{tenantName}</Text>
                        </View>
                    )}
                </View>
            </View>
            <WechatAuthWarningModal visible={showAuthWarning} onConfirm={handleAuthWarningConfirm} onCancel={handleAuthWarningCancel} />
            <ExamInfoModal visible={showExamInfoModal} onClose={() => setState((prev) => ({ ...prev, showExamInfoModal: false }))} onSelect={handleExamInfoSelect} />
            <BankScreenContentModal
                visible={showBankScreenContentModal}
                content={bankScreenContent}
                onConfirm={handleBankScreenContentConfirm}
                onCancel={handleBankScreenContentCancel}
            />
            <ImportantWarningModal ref={importantWarningRef} />
        </Layout>
    );
};

export default RefundApplication;
