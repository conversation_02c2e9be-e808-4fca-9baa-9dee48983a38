import { useReady } from "@tarojs/taro";
import React, { useState, useEffect } from "react";
import { View, Text, Form } from "@tarojs/components";
import { Button, Calendar, Cell, ConfigProvider, Popup } from "@nutui/nutui-react-taro";
import { login } from "@utils/login";
import request from "@service/request";
import { message } from "@components/MessageApi/MessageApiSingleton";
import Taro from "@tarojs/taro";
import "./index.scss";
import "@utils/app.scss";
import Layout from "@/components/Layout";

interface RefundApplication {
    Id: string;
    xm: string;
    sfzmhm: string;
    StatusText: string;
    KeMuId: number;
    KeMuText: string;
    RegistrationDate: string;
    Ywzt: string;
    JxDeptName: string;
    JxClassName: string;
    SaleUserName: string;
    CarType: string;
    TeachTwoUserName: string;
    Phone: string;
    RefundAmount: number;
    kcmc: string;
    kscc: string;
    zjcx: string;
    lsh: string;
    ksrq: string;
    ScreenshotUrl: string;
    BankCardScreenshotUrl: string;
    CreateTime: string;
    IsPaid: boolean;
    RefundTime: string;
    AuditRemark: string;
}

const App = () => {
    const [userInfo, setUserInfo] = React.useState<API.UserInfo>(Object);
    const [applications, setApplications] = useState<RefundApplication[]>([]);
    const [showStartCalendar, setShowStartCalendar] = useState(false);
    const [showEndCalendar, setShowEndCalendar] = useState(false);
    const [loading, setLoading] = useState(false);
    const [tenantId, setTenantId] = useState<string>("");
    const [currentPage, setCurrentPage] = useState(1);
    const [hasNextPage, setHasNextPage] = useState(false);
    const [totalPages, setTotalPages] = useState(1);
    const [dateRange, setDateRange] = useState<[string, string]>([
        new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split("T")[0],
        new Date().toISOString().split("T")[0],
    ]);

    useReady(() => {
        const params = Taro.getCurrentInstance().router?.params;
        if (params?.TenantId) {
            setTenantId(params.TenantId as string);
        }

        login().then((info: API.UserInfo) => {
            setUserInfo(info);
        });
    });

    useEffect(() => {
        if (tenantId) {
            handleSearch(1);
        }
    }, [tenantId]);

    const handleSearch = async (page = 1) => {
        setLoading(true);
        try {
            const response = await request.post<any>("/Jx/Student/Audit/WxRefundAudit/getMyApplicationList", {
                current: page,
                pageSize: 10,
                CreateTimes: [dateRange[0], dateRange[1]],
            });

            if (response.success) {
                const { data: responseData } = response;
                const { data, total, pages, hasNextPages } = responseData;
                setApplications((prev) => (page === 1 ? data || [] : [...prev, ...(data || [])]));
                setCurrentPage(page);
                setHasNextPage(hasNextPages);
                setTotalPages(pages);
            } else {
                message.error(response.message || "查询失败");
            }
        } catch (error) {
            console.error("Search failed:", error);
            message.error("查询失败，请重试");
        } finally {
            setLoading(false);
        }
    };

    const loadMore = () => {
        if (hasNextPage && !loading) {
            handleSearch(currentPage + 1);
        }
    };

    const formatDate = (date: string) => {
        return date.replace(/-/g, "/");
    };

    const formatExamDate = (date: string) => {
        if (!date) return "";
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, "0");
        const day = String(d.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
    };

    const formatRefundTime = (date: string) => {
        if (!date || date === "1900-01-01 00:00:00") return "";
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, "0");
        const day = String(d.getDate()).padStart(2, "0");
        const hours = String(d.getHours()).padStart(2, "0");
        const minutes = String(d.getMinutes()).padStart(2, "0");
        const seconds = String(d.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };

    const validateDateRange = (startDate: string, endDate: string) => {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const today = new Date();
        const threeMonthsAgo = new Date();
        threeMonthsAgo.setMonth(today.getMonth() - 3);

        if (start > end) {
            message.error("开始日期不能大于结束日期");
            return false;
        }

        if (end > today) {
            message.error("不能选择未来日期");
            return false;
        }

        if (start < threeMonthsAgo) {
            message.error("只能查询最近三个月的记录");
            return false;
        }

        return true;
    };

    return (
        <Layout>
            <ConfigProvider
                theme={{
                    nutuiPickerListHeight: "40vh",
                }}
            >
                <View className="refund-list-container">
                    {/* 查询表单 */}
                    <View className="search-card">
                        <Form onSubmit={() => handleSearch(1)}>
                            <View className="form-row">
                                <View className="form-item">
                                    <View className="form-label-wrapper">
                                        <Text className="form-label">申请时间</Text>
                                    </View>
                                    <View className="form-cell-wrapper">
                                        <View className="date-picker-row">
                                            <Cell title={formatDate(dateRange[0])} onClick={() => setShowStartCalendar(true)} className="picker-cell" />
                                            <Text className="date-separator">至</Text>
                                            <Cell title={formatDate(dateRange[1])} onClick={() => setShowEndCalendar(true)} className="picker-cell" />
                                        </View>
                                    </View>
                                </View>
                            </View>
                            <View className="form-actions">
                                <Button type="primary" loading={loading} formType="submit">
                                    查询
                                </Button>
                            </View>
                        </Form>
                    </View>

                    {/* 申请列表 */}
                    <View className="application-list">
                        {applications.length === 0 ? (
                            <View className="no-data">
                                <View className="custom-divider">
                                    <View className="divider-line left"></View>
                                    <Text className="divider-text">暂无数据</Text>
                                    <View className="divider-line right"></View>
                                </View>
                            </View>
                        ) : (
                            <>
                                {applications.map((application) => (
                                    <View key={application.Id} className="application-item">
                                        <View className="application-info">
                                            <View className="application-main">
                                                {/* <Text className="application-id">#{application.Id}</Text> */}
                                                <Text
                                                    className={`application-status ${
                                                        application.StatusText === "等待审核" ? "pending" : application.StatusText === "审核通过" ? "approved" : "rejected"
                                                    }`}
                                                >
                                                    {application.StatusText}
                                                </Text>
                                                <Text className="application-title">{application.CreateTime}</Text>
                                            </View>
                                            <View className="application-details">
                                                <Text className="application-amount">退款金额: ¥{application.RefundAmount}</Text>
                                                {application.KeMuId === 999 && <Text className="application-subject">制证费退款</Text>}
                                                {application.KeMuId !== 999 && (
                                                    <>
                                                        <Text className="application-subject">科目: {application.KeMuText}</Text>
                                                        <Text className="application-exam-venue">考场: {application.kcmc}</Text>
                                                        <Text className="application-exam-session">场次: {application.kscc}</Text>
                                                        <Text className="application-exam-date">考试日期: {formatExamDate(application.ksrq)}</Text>
                                                        {application.AuditRemark && <Text className="application-remark">审核备注: {application.AuditRemark}</Text>}
                                                        {application.IsPaid && (
                                                            <>
                                                                <View className="refund-divider"></View>
                                                                <View className="refund-info">
                                                                    <Text className="refund-status">已退款</Text>
                                                                    <Text className="refund-time">退款时间: {formatRefundTime(application.RefundTime)}</Text>
                                                                </View>
                                                            </>
                                                        )}
                                                    </>
                                                )}
                                            </View>
                                        </View>
                                    </View>
                                ))}

                                {/* 加载更多 */}
                                {hasNextPage && (
                                    <View className="load-more" onClick={loadMore}>
                                        <View className="custom-divider">
                                            <View className="divider-line left"></View>
                                            <Text className="divider-text">点击加载更多</Text>
                                            <View className="divider-line right"></View>
                                        </View>
                                    </View>
                                )}

                                {/* 底部提示 */}
                                {!hasNextPage && applications.length > 0 && (
                                    <View className="no-more">
                                        <View className="custom-divider">
                                            <View className="divider-line left"></View>
                                            <Text className="divider-text">我是有底线的</Text>
                                            <View className="divider-line right"></View>
                                        </View>
                                    </View>
                                )}
                            </>
                        )}
                    </View>

                    {/* 开始日期日历 */}
                    <Popup visible={showStartCalendar} position="bottom" onClose={() => setShowStartCalendar(false)} closeable={false}>
                        <Calendar
                            visible={showStartCalendar}
                            defaultValue={dateRange[0]}
                            startDate={new Date(Date.now() - 500 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]}
                            endDate={new Date().toISOString().split("T")[0]}
                            onClose={() => setShowStartCalendar(false)}
                            onSelect={(date) => {
                                const selectedDate = date[0];
                                if (validateDateRange(selectedDate, dateRange[1])) {
                                    setDateRange([selectedDate, dateRange[1]]);
                                }
                                setShowStartCalendar(false);
                            }}
                            showTitle={false}
                        />
                    </Popup>

                    {/* 结束日期日历 */}
                    <Popup visible={showEndCalendar} position="bottom" onClose={() => setShowEndCalendar(false)} closeable={false}>
                        <Calendar
                            visible={showEndCalendar}
                            defaultValue={dateRange[1]}
                            startDate={new Date(Date.now() - 500 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]}
                            endDate={new Date().toISOString().split("T")[0]}
                            onClose={() => setShowEndCalendar(false)}
                            onSelect={(date) => {
                                const selectedDate = date[0];
                                if (validateDateRange(dateRange[0], selectedDate)) {
                                    setDateRange([dateRange[0], selectedDate]);
                                }
                                setShowEndCalendar(false);
                            }}
                            showTitle={false}
                        />
                    </Popup>
                </View>
                <Button className="fab-button" onClick={() => Taro.navigateTo({ url: "/subpackages/student/hnRefund/index" })}>
                    提交申请
                </Button>
            </ConfigProvider>
        </Layout>
    );
};

export default App;
