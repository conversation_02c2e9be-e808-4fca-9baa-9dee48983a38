page {
    background: #f7f8fa;
    min-height: 100vh;
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular, "Microsoft Yahei", sans-serif;
}

.refund-list-container {
    padding-bottom: 120rpx;
    min-height: 100vh;
    position: relative;
    background-color: #f7f8fa;
    padding: 24rpx 16rpx;

    .search-card {
        background-color: #fff;
        padding: 24rpx 20rpx;
        border-radius: 20rpx;
        margin-bottom: 24rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);

        .form-row {
            display: flex;
            flex-direction: column;
        }

        .form-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
            width: 100%;

            .form-label-wrapper {
                width: 100%;
            }

            .form-cell-wrapper {
                width: 100%;
            }
        }

        .form-label {
            font-size: 26rpx;
            color: #374151;
            font-weight: 500;
        }

        .date-picker-row {
            display: flex;
            align-items: center;
            gap: 12px;

            .picker-cell {
                flex: 1;
                background: #f8f8f8;
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 28rpx;
                color: #1f2937;
            }

            .date-separator {
                color: #666;
                font-size: 28rpx;
            }
        }

        .form-actions {
            margin-top: 20px;

            button {
                width: 100%;
                height: 80rpx;
                border-radius: 44rpx;
                font-size: 32rpx;
                font-weight: 500;
                background-color: #3b82f6;
                color: #ffffff;
                border: none;
                box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);

                &:active {
                    background-color: #2563eb;
                    transform: scale(0.98);
                    box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);
                }
            }
        }
    }

    .application-list {
        display: flex;
        flex-direction: column;
        gap: 16rpx;
        margin-bottom: calc(100rpx + env(safe-area-inset-bottom));

        .application-item {
            background-color: #fff;
            border-radius: 16rpx;
            padding: 24rpx;
            box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;

            &:active {
                transform: scale(0.98);
            }
        }

        .application-info {
            display: flex;
            flex-direction: column;
            gap: 16rpx;
        }

        .application-main {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 16rpx;
            border-bottom: 1px solid #f0f0f0;
        }

        .application-id {
            font-size: 20rpx;
            color: #6b7280;
        }

        .application-status {
            font-size: 20rpx;
            padding: 6rpx 16rpx;
            border-radius: 6rpx;
            font-weight: 500;

            &.pending {
                background-color: #fff7e6;
                color: #fa8c16;
            }

            &.approved {
                background-color: #f6ffed;
                color: #52c41a;
            }

            &.rejected {
                background-color: #fff1f0;
                color: #f5222d;
            }
        }

        .application-title {
            font-size: 20rpx;
            color: #999;
        }

        .application-details {
            display: flex;
            flex-direction: column;
            gap: 12rpx;

            .application-name,
            .application-amount,
            .application-subject,
            .application-exam-venue,
            .application-exam-session,
            .application-exam,
            .application-exam-date,
            .application-remark {
                font-size: 24rpx;
                color: #333;
                line-height: 1.5;
            }

            .application-amount {
                color: #f5222d;
                font-weight: 700;
                font-size: 28rpx;
            }

            .application-exam-venue,
            .application-exam-session,
            .application-exam,
            .application-exam-date,
            .application-remark {
                color: #666;
            }

            .application-remark {
                color: #ff4d4f;
                font-weight: 500;
            }

            .refund-divider {
                height: 1px;
                background-color: #f0f0f0;
                margin: 16rpx 0;
            }

            .refund-info {
                display: flex;
                flex-direction: column;
                gap: 8rpx;

                .refund-status {
                    font-size: 24rpx;
                    color: #3b82f6;
                    font-weight: 500;
                }

                .refund-time {
                    font-size: 24rpx;
                    color: #666;
                }
            }
        }
    }

    .load-more {
        text-align: center;
        color: #6b7280;
        font-size: 28rpx;
        margin-top: 24rpx;
        margin-bottom: 24rpx;
        transition: all 0.3s ease;

        &:active {
            opacity: 0.7;
        }
    }

    .no-more {
        text-align: center;
        color: #6b7280;
        font-size: 28rpx;
        margin-top: 24rpx;
        margin-bottom: 24rpx;
    }

    .custom-divider {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 16rpx;
        padding: 0 20rpx;

        .divider-line {
            flex: 1;
            height: 1rpx;
            background-color: #e5e7eb;
        }

        .divider-text {
            color: #6b7280;
            font-size: 24rpx;
            white-space: nowrap;
        }
    }

    .no-data {
        padding: 48rpx 0;
    }
}

.date-picker-row {
    display: flex;
    align-items: center;
    width: 100%;

    .picker-cell {
        flex: 1;
    }

    .date-separator {
        padding: 0 24rpx;
        color: #666;
    }
}

.fab-button {
    position: fixed;
    right: 32rpx;
    bottom: 120rpx;
    padding: 0 32rpx;
    height: 80rpx;
    background: -webkit-linear-gradient(315deg, #2c5ecc, #4c37c3);
    background: linear-gradient(135deg, #2c5ecc, #4c37c3);
    border-radius: 40rpx;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-shadow: 0 4rpx 12rpx rgba(44, 94, 204, 0.3);
    box-shadow: 0 4rpx 12rpx rgba(44, 94, 204, 0.3);
    z-index: 100;
    -webkit-transition: -webkit-transform 0.2s ease;
    transition: -webkit-transform 0.2s ease;
    transition: transform 0.2s ease;
    transition: transform 0.2s ease, -webkit-transform 0.2s ease;
    line-height: normal;
    color: #fff;
    font-size: 28rpx;
    line-height: normal;
    // font-weight: 500;

    .nut-icon {
        color: #fff;
        font-size: 32rpx;
        font-weight: 300;
    }

    &:active {
        transform: scale(0.95);
    }
}
