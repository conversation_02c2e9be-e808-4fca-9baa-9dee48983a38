import Taro, { useReady } from "@tarojs/taro";
import { useState } from "react";
import TopNavBar from "@components/topNavBar";
import { Button, Dialog, Toast } from "@nutui/nutui-react-taro";
import "./index.scss";
import "@utils/app.scss";
import upload from "@service/upload";

import { Image } from "@tarojs/components";

import { getLoading, getMsg } from "@utils/util";
import { login } from "@utils/login";
import request from "@service/request";

type StudentImageItem = {
    ImageId: string;
    ImagePath: string;
};

const App = () => {
    /**
     * Toast 需要的 State
     */
    const [toastShow, setToastShow] = useState(false);
    const [toast, setToast] = useState<API.pdToast>(Object);

    /**
     * Dialog 需要的 State
     */
    const [dialogShow, setDialogShow] = useState(false);
    const [dialog, setDialog] = useState<API.pdDialog>(Object);

    /**
     * 用户登录的 信息
     */
    const [userInfo, setUserInfo] = useState<API.UserInfo>(Object);

    /**
     * 寸照
     */
    const [image0, setImage0] = useState("");

    /**
     * 页面 ready 方法
     */
    useReady(() => {
        login().then((userInfo: API.UserInfo) => {
            setUserInfo(userInfo);
            GetStudentImages();

            // 需要登录的时候 再用  这个页面是全局的  会和部分模块冲突 比如 湖南驾协 长沙驾协
            // wxLogOn().then(() => {
            // });
        });
    });
    /**
     * 获取 学员的 图片
     */
    const GetStudentImages = () => {
        request.post<API.Result<StudentImageItem[]>>("/Jx/Image/StudentImage/GetStudentImages", {}).then((json) => {
            var image0 = json.data.find((m) => m.ImageId == "0");
            setImage0(image0 ? image0.ImagePath : "");
        });
    };

    /**
     *
     * @param ImageId
     */
    const TakePhoto = (ImageId: number) => {
        setDialog(
            getMsg("该寸照为申请表的照片，请不要美颜、带眼镜、戴帽子、耳环、项链等装饰物", "注意事项", function () {
                Taro.showActionSheet({
                    itemList: ["拍照上传", "相册选择"],
                    success(res) {
                        Taro.chooseImage({
                            count: 1,
                            sizeType: ["original"], //['original', 'compressed'],
                            sourceType: res.tapIndex == 0 ? ["camera"] : ["album"], //['album', 'camera'],
                            success(res) {
                                Upload(res.tempFilePaths[0], ImageId).then((json) => {
                                    // GetStudentImages();
                                });
                            },
                        });
                    },
                });
            })
        );
        setDialogShow(true);
    };

    /**
     * 上传
     * @param filePath
     */
    const Upload = (filePath: any, ImageId: number) => {
        return new Promise(function (resolve, reject) {
            setToast(getLoading("正在上传", () => {}));
            setToastShow(true);
            upload
                .post<
                    API.Result<{
                        data: string;
                    }>
                >(
                    "/Jx/Image/StudentImage/uploadStudentImage",
                    {
                        ImageId: ImageId,
                    },
                    filePath,
                    "file"
                )
                .then((json) => {
                    setToastShow(false);
                    if (json.success) {
                        json && json.message && setDialog(getMsg(json.message, "上传完成"));
                        json && json.message && setDialogShow(true);

                        GetStudentImages();
                        resolve(json);
                    } else {
                        setDialog(getMsg(json.message));
                        setDialogShow(true);
                        reject(json);
                    }
                });
        });
    };

    return (
        <>
            <TopNavBar title="打印申请表"></TopNavBar>

            <view className="container">
                <view
                    className="ul-row thorui-flex"
                    style={{
                        margin: "30rpx",
                    }}
                >
                    <view className="thorui-panel ul-cell thorui-flex__item">
                        <view className="thorui-panel__hd">申请表寸照</view>
                        <view className="thorui-media-box">
                            {/* <view className='thorui-media-box__hd'
                                style={{
                                    backgroundImage: `url('${image0}')`,
                                    backgroundSize: '100%,auto',
                                    backgroundRepeat: 'no-repeat',
                                }}>
                            </view> */}
                            <Image
                                src={image0}
                                mode={"aspectFit"}
                                style={{
                                    width: "100%",
                                }}
                            ></Image>
                        </view>
                        <view
                            className="thorui-media-button"
                            style={{
                                paddingLeft: 0,
                                paddingRight: 0,
                            }}
                        >
                            <Button
                                type="info"
                                shape={"square"}
                                plain={image0 != ""}
                                size="large"
                                onClick={() => {
                                    TakePhoto(0);
                                }}
                                style={{
                                    width: "100%",
                                }}
                            >
                                {image0 != "" ? "重新拍照上传" : "拍照上传"}
                            </Button>
                        </view>

                        {image0 != "" && (
                            <view
                                className="thorui-media-button"
                                style={{
                                    paddingLeft: 0,
                                    paddingRight: 0,
                                }}
                            >
                                <Button
                                    type="info"
                                    shape={"square"}
                                    size="large"
                                    onClick={() => {
                                        TakePhoto(0);
                                    }}
                                    style={{
                                        width: "100%",
                                    }}
                                >
                                    打开申请表
                                </Button>
                            </view>
                        )}
                    </view>
                </view>
            </view>
            {/* Toast 和 Dialog 的通用相关代码 开始 */}
            {toastShow && !dialogShow && (
                <Toast
                    msg={toast.msg}
                    visible={toastShow}
                    type={toast.type}
                    onClose={() => {
                        setToastShow(false);
                        toast.fn();
                    }}
                    // cover={toast.cover}
                    // coverColor="rgba(6, 6, 6, 0.8)"
                    duration={toast.duration}
                    icon=<toast.icon />
                    iconSize="20"
                />
            )}
            {dialogShow && (
                <Dialog
                    closeOnOverlayClick={false}
                    title={dialog.title}
                    confirmText={dialog.okText}
                    hideCancelButton={dialog.noCancelBtn}
                    cancelText={dialog.cancelText}
                    // textAlign={dialog.textAlign}
                    visible={dialogShow}
                    lockScroll
                    footerDirection="vertical"
                    onConfirm={() => {
                        dialog.ok();
                        setDialogShow(false);
                    }}
                    onCancel={() => {
                        dialog.cancel();
                        setDialogShow(false);
                    }}
                >
                    <view
                        style={{
                            lineHeight: "40rpx",
                        }}
                    >
                        {dialog.msg}
                    </view>
                </Dialog>
            )}
            {/* Toast 和 Dialog 的通用相关代码 结束 */}
        </>
    );
};
export default App;
