$primary-blue: #1890ff;
$primary-green: #07c160;
$primary-red: #ff4d4f;
$background-gray: #f5f5f5;

.practice-container {
    min-height: 100vh;
    background: $background-gray;
    padding-bottom: 120rpx;
    position: relative;

    // Common styles
    %flex-center {
        display: flex;
        align-items: center;
    }

    %flex-between {
        @extend %flex-center;
        justify-content: space-between;
    }

    // Common text styles
    %base-text {
        font-size: 28rpx;
        color: #666;
    }

    .nav-header {
        background: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx 40rpx;
        margin-bottom: 20rpx;

        .chapter-title {
            font-size: 28rpx;
            font-weight: 500;
            color: #333;
        }

        .progress {
            display: flex;
            align-items: center;
            gap: 10rpx;
            color: #666;
            font-size: 28rpx;
        }
    }

    .mode-switch {
        margin: 20rpx 40rpx;
        background: #fff;
        border-radius: 8rpx;
        display: flex;
        overflow: hidden;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        .mode-btn {
            flex: 1;
            text-align: center;
            padding: 10rpx 0;
            @extend %base-text;
            transition: all 0.3s;

            &.active {
                color: #fff;
                background: $primary-blue;
            }
        }
    }

    .question-container {
        margin: 0 40rpx;
        background: #fff;
        border-radius: 8rpx;
        padding: 40rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        .question-type {
            display: inline-block;
            padding: 4rpx 24rpx;
            background: $primary-blue;
            color: #fff;
            font-size: 22rpx;
            border-radius: 12rpx 12rpx 12rpx 0;
            margin-right: 20rpx;
            vertical-align: top;
            margin-bottom: 20rpx;
        }
        .question-content {
            margin-bottom: 40rpx;
            position: relative;

            Text {
                font-size: 32rpx;
                line-height: 1.6;
                color: #333;
            }
        }

        .options-container {
            .option-item {
                @extend %flex-center;
                padding: 12rpx;
                border-radius: 8rpx;
                margin-bottom: 20rpx;
                transition: all 0.3s;

                &.selected {
                    .option-value {
                        color: $primary-blue;
                    }
                }

                &.correct {
                    .option-value {
                        color: $primary-blue;
                    }
                }

                &.wrong {
                    .option-value {
                        color: $primary-red;
                    }
                }

                .option-key {
                    width: 60rpx;
                    height: 60rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    background: #fff;
                    border: 1px solid #e8e8e8;
                    color: #333;
                    font-size: 28rpx;
                    box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
                }
                .option-key-icon {
                    .icon {
                        // width: 60rpx;
                        // height: 60rpx;
                        // padding: 0;
                        // background: #fff;
                        box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
                    }
                }
                .option-value {
                    font-size: 28rpx;
                    color: #333;
                    margin-left: 20rpx;
                }
            }
        }

        .answer-brief {
            background: $background-gray;
            padding: 20rpx 30rpx;
            margin: 30rpx 0;
            border-radius: 8rpx;
            font-size: 28rpx;

            .correct-answer {
                color: $primary-blue;
                margin: 0 10rpx;
            }

            .your-answer {
                color: $primary-red;
                margin-left: 10rpx;
            }
        }

        .answer-analysis {
            margin-top: 40rpx;
            padding-top: 40rpx;
            border-top: 2rpx solid #e8e8e8;

            .answer,
            .tips,
            .analysis {
                margin-bottom: 30rpx;

                .label {
                    @extend %base-text;
                    margin-right: 20rpx;
                }

                .value {
                    font-size: 28rpx;
                    color: #333;
                    line-height: 1.6;
                }
            }

            .tips-section {
                margin-bottom: 40rpx;

                .tips-header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 20rpx;

                    .blue-bar {
                        width: 6rpx;
                        height: 32rpx;
                        background: $primary-blue;
                        margin-right: 16rpx;
                    }

                    Text {
                        font-size: 32rpx;
                        font-weight: 500;
                        color: #333;
                    }
                }

                .tips-content {
                    font-size: 28rpx;
                    color: #666;
                    line-height: 1.6;
                }
            }

            .detail-section {
                .detail-header {
                    font-size: 36rpx;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 30rpx;
                }

                .analysis-section {
                    margin-bottom: 30rpx;

                    .analysis-header {
                        font-size: 28rpx;
                        color: #666;
                        margin-bottom: 16rpx;
                    }

                    .analysis-content {
                        font-size: 28rpx;
                        color: #333;
                        line-height: 1.6;
                    }
                }

                .difficulty-section {
                    display: flex;
                    align-items: center;
                    margin-top: 40rpx;
                    padding-top: 30rpx;
                    border-top: 1px solid #e8e8e8;

                    .difficulty-label {
                        font-size: 28rpx;
                        color: #666;
                        margin-right: 16rpx;
                    }

                    .stars {
                        display: flex;
                        align-items: center;
                        gap: 8rpx;
                        margin-right: 40rpx;
                        color: #ffb800;
                    }

                    .correct-rate {
                        font-size: 28rpx;
                        color: #666;
                    }
                }
            }
        }
    }

    %toolbar-base {
        background: #fff;
        @extend %flex-between;
        padding: 20rpx 40rpx;
        box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
        z-index: 100;

        .collect-btn,
        .collect-tool {
            @extend %flex-center;
            gap: 10rpx;
            @extend %base-text;
        }

        .right-tools {
            @extend %flex-center;
            gap: 40rpx;

            .count-info {
                @extend %flex-center;
                gap: 30rpx;

                .correct-count,
                .incorrect-count {
                    @extend %flex-center;
                    gap: 10rpx;
                    font-size: 28rpx;
                }

                .correct-count {
                    color: $primary-green;
                }

                .incorrect-count {
                    color: $primary-red;
                }
            }

            .progress {
                @extend %flex-center;
                gap: 10rpx;
                @extend %base-text;
            }
        }
    }

    .bottom-toolbar {
        @extend %toolbar-base;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding-bottom: env(safe-area-inset-bottom);
    }

    .bottom-toolbar-up {
        background: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        // padding: 20rpx 40rpx;
        box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
        z-index: 100;

        .collect-btn {
            display: flex;
            align-items: center;
            gap: 10rpx;
            color: #666;
            font-size: 24rpx;
        }

        .right-tools {
            display: flex;
            align-items: center;
            gap: 40rpx;

            .count-info {
                display: flex;
                align-items: center;
                gap: 30rpx;

                .correct-count,
                .incorrect-count {
                    display: flex;
                    align-items: center;
                    gap: 10rpx;
                    font-size: 24rpx;
                }

                .correct-count {
                    color: $primary-green;
                }

                .incorrect-count {
                    color: $primary-red;
                }
            }

            .progress {
                display: flex;
                align-items: center;
                gap: 10rpx;
                color: #666;
                font-size: 24rpx;
            }
        }
    }

    .popup-mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
    }

    .question-list-popup {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        transform: translateY(100%);
        transition: transform 0.3s ease;
        z-index: 1001;
        height: 80vh;
        display: flex;
        flex-direction: column;

        &.show {
            transform: translateY(0);
        }

        .popup-header {
            padding: 20rpx 40rpx;
            border-bottom: 2rpx solid #e8e8e8;

            .header-tools {
                @extend %toolbar-base;
                padding: 0;
                box-shadow: none;
            }
        }

        .popup-content {
            flex: 1;
            overflow: hidden;
            position: relative;

            .fixed-chapter-title {
                position: sticky;
                top: 0;
                background: #fff;
                padding: 20rpx 40rpx;
                font-size: 28rpx;
                font-weight: 500;
                color: #333;
                border-bottom: 2rpx solid #e8e8e8;
                z-index: 1;
            }

            .chapter-header {
                padding: 20rpx 40rpx;
                background: #f5f5f5;

                .chapter-title {
                    font-size: 28rpx;
                    color: #666;
                }
            }

            .question-grid {
                display: grid;
                grid-template-columns: repeat(6, 1fr);
                gap: 20rpx;
                padding: 20rpx 40rpx 20rpx 20rpx;

                .question-item {
                    aspect-ratio: 1;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #f5f5f5;
                    border-radius: 100rpx;
                    font-size: 28rpx;
                    color: #777;
                    transition: all 0.3s;

                    &.current {
                        background: $primary-blue;
                        color: #fff;
                    }

                    &:active {
                        opacity: 0.8;
                    }
                }
            }
        }
    }
}
