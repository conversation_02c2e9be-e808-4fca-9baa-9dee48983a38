import { View, Text } from "@tarojs/components";
import { useState, useEffect, useRef } from "react";
import "./index.scss";
import Layout from "@components/Layout";
import IconFont from "@components/iconfont";
import Taro from "@tarojs/taro";
import { ScrollView } from "@tarojs/components";

interface Question {
    id: number;
    type: string;
    content: string;
    options: { key: string; value: string }[];
    answer: string;
    tips: string;
    analysis: string;
}

interface Chapter {
    id: number;
    title: string;
    questionIds: number[];
}

const PracticePage = () => {
    const [mode, setMode] = useState<"answer" | "review">("answer");
    const [statusBarHeight, setStatusBarHeight] = useState(0);
    const [safeAreaBottom, setSafeAreaBottom] = useState(0);
    const [correctCount, setCorrectCount] = useState(0);
    const [incorrectCount, setIncorrectCount] = useState(0);
    const [showQuestionList, setShowQuestionList] = useState(false);
    const [currentChapter, setCurrentChapter] = useState<Chapter>({ id: 1, title: "第1章 安全行车常识", questionIds: [] });
    const popupContentRef = useRef<any>(null);

    // 生成题目列表数据
    const questionList = Array.from({ length: 42 }, (_, index) => ({
        id: index + 1,
        isAnswered: false,
        isCorrect: false,
    }));

    // 章节数据
    const chapters: Chapter[] = [
        {
            id: 1,
            title: "第1章 安全行车常识",
            questionIds: Array.from({ length: 300 }, (_, i) => i + 1),
        },
        {
            id: 2,
            title: "第2章 文明行车常识",
            questionIds: Array.from({ length: 300 }, (_, i) => i + 301),
        },
    ];

    useEffect(() => {
        const systemInfo = Taro.getSystemInfoSync();
        setStatusBarHeight(systemInfo.statusBarHeight || 0);
        setSafeAreaBottom(systemInfo.safeArea?.bottom ? systemInfo.windowHeight - systemInfo.safeArea.bottom : 0);
    }, []);

    const [currentQuestion, setCurrentQuestion] = useState<Question>({
        id: 1,
        type: "判断题",
        content: "车辆下坡行驶，要适当控制车速，充分利用发动机进行制动。",
        options: [
            { key: "A", value: "正确" },
            { key: "B", value: "错误" },
        ],
        answer: "A",
        tips: "下坡利用发动机，挂低档，禁滑行。",
        analysis: "下坡行驶车速太快容易翻车，而长时间使用刹车制动，会造成刹车片温度升高，影响制动效果，为安全起见，可以充分利用发动机制动，来降低车速。",
    });
    const [showAnswer, setShowAnswer] = useState(false);
    const [selectedAnswer, setSelectedAnswer] = useState<string>("");

    const handleOptionSelect = (key: string) => {
        setSelectedAnswer(key);
        setShowAnswer(true);
        if (key === currentQuestion.answer) {
            setCorrectCount((prev) => prev + 1);
        } else {
            setIncorrectCount((prev) => prev + 1);
        }
    };

    // 监听滚动事件
    const handleScroll = (e: any) => {
        const scrollTop = e.detail.scrollTop;
        const chapterElements = document.querySelectorAll(".chapter-header");

        for (let i = 0; i < chapterElements.length; i++) {
            const element = chapterElements[i];
            const rect = element.getBoundingClientRect();

            if (rect.top <= 100) {
                // 当章节标题接近顶部时
                setCurrentChapter(chapters[i]);
            }
        }
    };

    return (
        <Layout>
            <View className="practice-container">
                {/* 模式切换 */}
                <View className="mode-switch">
                    <View className={`mode-btn ${mode === "answer" ? "active" : ""}`} onClick={() => setMode("answer")}>
                        答题模式
                    </View>
                    <View className={`mode-btn ${mode === "review" ? "active" : ""}`} onClick={() => setMode("review")}>
                        背题模式
                    </View>
                </View>

                {/* 题目内容 */}
                <View className="question-container">
                    <Text className="question-type">{currentQuestion.type}</Text>
                    <View className="question-content">
                        <Text>{currentQuestion.content}</Text>
                    </View>

                    {/* 选项 */}
                    <View className="options-container">
                        {currentQuestion.options.map((option) => (
                            <View
                                key={option.key}
                                className={`option-item ${
                                    showAnswer
                                        ? option.key === currentQuestion.answer
                                            ? "correct"
                                            : option.key === selectedAnswer
                                            ? "wrong"
                                            : ""
                                        : selectedAnswer === option.key
                                        ? "selected"
                                        : ""
                                }`}
                                onClick={() => handleOptionSelect(option.key)}
                            >
                                {!showAnswer && (
                                    <View className="option-key">
                                        <Text>{option.key}</Text>
                                    </View>
                                )}
                                {showAnswer && (
                                    <View className="option-key-icon">
                                        <IconFont name={option.key === currentQuestion.answer ? "duicuo2" : "duicuo1"} size={60} />
                                    </View>
                                )}
                                <Text className="option-value">{option.value}</Text>
                            </View>
                        ))}
                    </View>

                    {/* 答案解析 */}
                    {showAnswer && (
                        <>
                            <View className="answer-brief">
                                <Text>答案：</Text>
                                <Text className="correct-answer">{currentQuestion.answer}</Text>
                                <Text> 您选择：</Text>
                                <Text className="your-answer">{selectedAnswer}</Text>
                            </View>
                            <View className="answer-analysis">
                                <View className="tips-section">
                                    <View className="tips-header">
                                        <View className="blue-bar"></View>
                                        <Text>本题技巧</Text>
                                    </View>
                                    <Text className="tips-content">{currentQuestion.tips}</Text>
                                </View>
                                <View className="detail-section">
                                    <View className="detail-header">
                                        <Text>试题详解</Text>
                                    </View>
                                    <View className="analysis-section">
                                        <View className="analysis-header">
                                            <Text>题目解析</Text>
                                        </View>
                                        <Text className="analysis-content">{currentQuestion.analysis}</Text>
                                    </View>
                                    <View className="difficulty-section">
                                        <Text className="difficulty-label">难度：</Text>
                                        <View className="stars">
                                            <IconFont name="shoucang" size={24} />
                                            <IconFont name="weishoucang" size={24} />
                                            <IconFont name="weishoucang" size={24} />
                                            <IconFont name="weishoucang" size={24} />
                                            <IconFont name="weishoucang" size={24} />
                                        </View>
                                        <Text className="correct-rate">答错率：1.1%</Text>
                                    </View>
                                </View>
                            </View>
                        </>
                    )}
                </View>

                {/* 底部工具栏 */}
                <View className="bottom-toolbar" style={{ paddingBottom: `${safeAreaBottom}px` }}>
                    <View className="collect-btn">
                        <IconFont name="weishoucang" size={32} />
                        <Text>收藏</Text>
                    </View>
                    <View className="right-tools">
                        <View className="count-info">
                            <View className="correct-count">
                                <IconFont name="duicuo2" size={36} />
                                <Text>{correctCount}</Text>
                            </View>
                            <View className="incorrect-count">
                                <IconFont name="duicuo1" size={36} />
                                <Text>{incorrectCount}</Text>
                            </View>
                        </View>
                        <View className="progress" onClick={() => setShowQuestionList(true)}>
                            <IconFont name="line-list" size={32} />
                            <Text>1/1374</Text>
                        </View>
                    </View>
                </View>
                {/* 题目列表弹出窗口 */}
                {showQuestionList && (
                    <>
                        <View className="popup-mask" onClick={() => setShowQuestionList(false)} />
                        <View className={`question-list-popup ${showQuestionList ? "show" : ""}`}>
                            <View className="popup-header">
                                <View className="header-tools">
                                    <View className="collect-tool">
                                        <IconFont name="weishoucang" size={32} />
                                        <Text>收藏</Text>
                                    </View>
                                    <View className="right-tools">
                                        <View className="count-info">
                                            <View className="correct-count">
                                                <IconFont name="duicuo2" size={36} />
                                                <Text>{correctCount}</Text>
                                            </View>
                                            <View className="incorrect-count">
                                                <IconFont name="duicuo1" size={36} />
                                                <Text>{incorrectCount}</Text>
                                            </View>
                                        </View>
                                        <View className="progress" onClick={() => setShowQuestionList(true)}>
                                            <IconFont name="line-list" size={32} />
                                            <Text>1/1374</Text>
                                        </View>
                                    </View>
                                </View>
                            </View>
                            <ScrollView className="popup-content" ref={popupContentRef} onScroll={handleScroll} scrollY>
                                <View className="chapter-header">
                                    <Text className="chapter-title">{currentChapter.title}</Text>
                                </View>
                                {chapters.map((chapter) => (
                                    <View key={chapter.id}>
                                        <View className="chapter-header">
                                            <Text className="chapter-title">{chapter.title}</Text>
                                        </View>
                                        <View className="question-grid">
                                            {chapter.questionIds.map((questionId) => (
                                                <View key={questionId} className={`question-item ${questionId === currentQuestion.id ? "current" : ""}`}>
                                                    {questionId}
                                                </View>
                                            ))}
                                        </View>
                                    </View>
                                ))}
                            </ScrollView>
                        </View>
                    </>
                )}
            </View>
        </Layout>
    );
};

export default PracticePage;
