import { View, Text } from "@tarojs/components";
import { useEffect, useState } from "react";
import "./index.scss";
import Layout from "@components/Layout";
import Taro from "@tarojs/taro";
import IconFont from "@components/iconfont";

const TheoryExamPage = () => {
    const [totalQuestions] = useState(1374);
    const [completedQuestions] = useState(0);

    const [statusBarHeight, setStatusBarHeight] = useState(0);

    useEffect(() => {
        const systemInfo = Taro.getSystemInfoSync();
        setStatusBarHeight(systemInfo.statusBarHeight || 0);
        // 添加自定义样式
        const style = document.createElement("style");
        style.innerHTML = `
            :root {
                --status-bar-height: ${systemInfo.statusBarHeight}px;
            }
        `;
        document.head.appendChild(style);
    }, []);

    return (
        <Layout>
            <View
                className="theory-exam-container"
                // style={{
                //     paddingTop: `calc(${statusBarHeight}px + 88rpx + 40rpx)`,
                // }}
            >
                <View className="tab-container">
                    <View className="tab active">科一</View>
                    <View className="tab">科四</View>
                </View>

                <View className="main-content">
                    <View className="three-column-layout">
                        {/* Left Column */}
                        <View className="column left-column">
                            <View>
                                {/* <View className="feature-item">
                                    <View className="icon vip-icon"></View>
                                </View> */}
                                <IconFont name="VIP1" size={65} />
                                <Text>VIP课程</Text>
                            </View>
                            <View>
                                {/* <View className="feature-item">
                                    <View className="icon practice-icon"></View>
                                </View> */}
                                <IconFont name="icon-zhuanxianglianxi" size={65} />
                                <Text>专项练习</Text>
                            </View>
                            <View>
                                {/* <View className="feature-item">
                                    <View className="icon signs-icon"></View>
                                </View> */}
                                <IconFont name="jiaotongtubiao-youzhuan" size={65} />
                                <Text>图标技巧</Text>
                            </View>
                            <View>
                                {/* <View className="feature-item new-rules">
                                    <View className="icon new-rules-icon"></View>
                                </View> */}
                                <IconFont name="mimimiji" size={65} />
                                <Text>新规秘卷</Text>
                            </View>
                        </View>

                        {/* Middle Column */}
                        <View className="column middle-column">
                            <View className="progress-circle" onClick={() => Taro.navigateTo({ url: "/subpackages/theoretical/practice/index" })}>
                                <Text className="progress-text">顺序练习</Text>
                                <Text className="progress-count">
                                    {completedQuestions}/{totalQuestions}
                                </Text>
                            </View>
                            <View className="mock-exam-circle">
                                <Text>模拟考试</Text>
                                <Text>仿真冲刺</Text>
                            </View>
                        </View>

                        {/* Right Column */}
                        <View className="column right-column">
                            <View>
                                {/* <View className="feature-item">
                                    <View className="icon question-icon"></View>
                                </View> */}
                                <IconFont name="tikuzongliang" size={65} />
                                <Text>精简题库</Text>
                            </View>
                            <View>
                                {/* <View className="feature-item">
                                    <View className="icon random-icon"></View>
                                </View> */}
                                <IconFont name="liucheng-suijiqi1" size={65} />
                                <Text>随机练习</Text>
                            </View>
                            <View>
                                {/* <View className="feature-item">
                                    <View className="icon favorites-icon"></View>
                                </View> */}
                                <IconFont name="shoucang" size={65} />
                                <Text>错题·收藏</Text>
                            </View>
                            <View>
                                {/* <View className="feature-item">
                                    <View className="icon ranking-icon"></View>
                                </View> */}
                                <IconFont name="paihang" size={65} />
                                <Text>成绩排行</Text>
                            </View>
                        </View>
                    </View>
                </View>
                <View className="current-bank">
                    <View className="bank-info">
                        <View className="car-icon">🚗</View>
                        <Text>当前题库(小车)</Text>
                    </View>
                    <View className="city-selector">
                        <View className="location-icon">📍</View>
                        <Text>长沙市</Text>
                        <View className="arrow">›</View>
                    </View>
                </View>
            </View>
        </Layout>
    );
};

export default TheoryExamPage;
