// Variables
$primary-blue: #1890ff;
$vip-pink: #ff7b7b;
$question-coral: #ff7f50;
$practice-blue: #45aaf2;
$random-mint: #55efc4;
$wechat-green: #07c160; // WeChat brand green color
$signs-mint: #55efc4;
$new-rules-orange: #ff7f50;
$favorites-coral: #ff7f50;
$ranking-blue: #45aaf2;

@keyframes breathe {
    0% {
        transform: scale(1);
        box-shadow: 0 16rpx 48rpx rgba(255, 123, 123, 0.5);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 32rpx 80rpx rgba(255, 123, 123, 0.7);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 16rpx 48rpx rgba(255, 123, 123, 0.5);
    }
}

@keyframes breatheBlue {
    0% {
        transform: scale(1);
        box-shadow: 0 16rpx 48rpx rgba(69, 170, 242, 0.5);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 32rpx 80rpx rgba(69, 170, 242, 0.7);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 16rpx 48rpx rgba(69, 170, 242, 0.5);
    }
}

.theory-exam-container {
    font-family: "AlibabaPuHuiTi";
    // font-family: Yuanti SC, STYuanti-SC-Regular, -apple-system, "PingFang SC", "Helvetica Neue";
    min-height: 100vh;
    padding-top: calc(88rpx + 20rpx); // 导航栏高度 + 额外间距
    background-color: #f5f5f5;
    position: relative;

    .main-content {
        position: relative;
        z-index: 1;
        padding-top: 20rpx;
    }

    .tab-container {
        position: relative;
        z-index: 1;
        background: #ffffff;
        margin: 0 auto 40rpx;
    }

    .current-bank {
        position: relative;
        z-index: 1;
        background: #ffffff;
        margin: 40rpx 40rpx 40rpx;
    }

    .header {
        text-align: center;
        margin-bottom: 20px;

        .title {
            font-size: 36px;
            font-weight: bold;
        }
    }

    .tab-container {
        display: flex;
        justify-content: center;
        margin: 0rpx auto;
        padding: 16rpx;
        border-radius: 8rpx;
        width: 80%;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        .tab {
            flex: 1;
            padding: 10rpx 0;
            text-align: center;
            font-size: 32rpx;
            color: #666;
            position: relative;
            transition: all 0.3s ease;
            border-radius: 4rpx;
            margin: 0 8rpx;

            &.active {
                color: #fff;
                font-weight: 500;
                background: $vip-pink;
                box-shadow: none;
            }

            &:not(.active):hover {
                background: rgba(255, 255, 255, 0.95);
                color: #333;
                box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
            }
        }
    }

    .main-content {
        padding-top: 40rpx;
        .three-column-layout {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;

            .column {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 60rpx;

                > View {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 16rpx;
                }

                .feature-item {
                    width: 96rpx;
                    height: 96rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: #fff;
                    border-radius: 8rpx;
                    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
                    transition: all 0.3s ease;
                    position: relative;

                    &:active {
                        transform: scale(0.95);
                        box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.22);
                    }

                    .icon {
                        width: 52rpx;
                        height: 52rpx;
                    }

                    &.new-rules::after {
                        content: "必看";
                        position: absolute;
                        top: -10rpx;
                        right: -10rpx;
                        background: $new-rules-orange;
                        color: white;
                        font-size: 20rpx;
                        padding: 4rpx 12rpx;
                        border-radius: 4rpx;
                    }

                    &:hover {
                        transform: translateY(-4rpx);
                        box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.25);
                    }
                }

                Text {
                    font-size: 26rpx;
                    color: #666;
                    font-weight: 400;
                }

                &.middle-column {
                    position: relative;
                    margin-top: 60rpx;

                    .progress-circle {
                        width: 30vw;
                        height: 30vw;
                        background: $practice-blue;
                        border-radius: 50%;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        color: #fff;
                        margin: 0 auto;
                        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
                        margin-bottom: 40rpx;
                        position: relative;
                        z-index: 1;

                        .progress-text {
                            font-size: 32rpx;
                            font-weight: 500;
                            // margin-bottom: 20rpx;
                            color: #fff;
                        }

                        .progress-count {
                            font-size: 26rpx;
                            opacity: 0.9;
                            color: #fff;
                        }
                    }

                    .mock-exam-circle {
                        width: 30vw;
                        height: 30vw;
                        background: $wechat-green;
                        border-radius: 50%;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        color: #fff;
                        margin: 0 auto;
                        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

                        Text {
                            font-size: 32rpx;
                            font-weight: 500;
                            line-height: 1.4;
                            color: #fff;

                            &:last-child {
                                font-size: 26rpx;
                                opacity: 0.9;
                                color: #fff;
                            }
                        }
                    }
                }
            }
        }
    }

    .icon {
        width: 50rpx;
        height: 50rpx;
        position: relative;
        &::before {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
    }

    .vip-icon {
        &::before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23ff7b7b' d='M12 2L9 7l-5 1 3.5 4L7 17l5-2 5 2-0.5-5L20 8l-5-1z'/%3E%3C/svg%3E");
        }
    }

    .current-bank {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx;
        border-radius: 8rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        .bank-info {
            display: flex;
            align-items: center;
            gap: 16rpx;

            .car-icon {
                font-size: 40rpx;
                filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
            }

            Text {
                font-size: 28rpx;
                color: #333;
                font-weight: 500;
            }
        }

        .city-selector {
            display: flex;
            align-items: center;
            gap: 8rpx;
            padding: 12rpx 20rpx;
            background: #f8f8f8;
            border-radius: 32rpx;
            transition: all 0.3s ease;
            box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);

            &:hover {
                background: #f0f0f0;
                box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.12);
                transform: translateY(-2rpx);
            }

            &:active {
                background: #e8e8e8;
                transform: translateY(0);
                box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
            }

            .location-icon {
                font-size: 32rpx;
                filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
            }

            Text {
                font-size: 26rpx;
                color: #666;
            }

            .arrow {
                font-size: 32rpx;
                color: #999;
                margin-left: 4rpx;
            }
        }
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

// Specific icon styles
.question-icon {
    background: #fff !important;
    &::before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23ff7f50' d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 12H7v-2h10v2zm0-4H7V9h10v2z'/%3E%3C/svg%3E");
    }
}

.practice-icon {
    background: #fff !important;
    &::before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%2345aaf2' d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z'/%3E%3Cpath fill='%2345aaf2' d='M12 7v5l4.25 2.52.77-1.28-3.52-2.09V7z'/%3E%3C/svg%3E");
    }
}

.random-icon {
    background: #fff !important;
    &::before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%2355efc4' d='M10.59 9.17L5.41 4 4 5.41l5.17 5.17 1.42-1.41zM14.5 4l2.04 2.04L4 18.59 5.41 20 17.96 7.46 20 9.5V4h-5.5zm.33 9.41l-1.41 1.41 3.13 3.13L14.5 20H20v-5.5l-2.04 2.04-3.13-3.13z'/%3E%3C/svg%3E");
    }
}

.signs-icon {
    background: #fff !important;
    &::before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%2355efc4' d='M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z'/%3E%3Cpath fill='%2355efc4' d='M12 17l-5-5h10z'/%3E%3C/svg%3E");
        width: 35px;
        height: 35px;
    }
}

.new-rules-icon {
    background: #fff !important;
    &::before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23ff9f43' d='M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm-1 7V3.5L18.5 9H13z'/%3E%3C/svg%3E");
    }
    &::after {
        content: "必看";
        position: absolute;
        bottom: -5px;
        right: -5px;
        background: #ff9f43;
        color: white;
        font-size: 12px;
        padding: 2px 6px;
        border-radius: 10px;
    }
}

.favorites-icon {
    background: #fff !important;
    &::before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23ff7f50' d='M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
    }
}

.ranking-icon {
    background: #fff !important;
    &::before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%2345aaf2' d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 14h-2v-4H6v-2h4V7h2v4h4v2h-4v4z'/%3E%3C/svg%3E");
    }
}
