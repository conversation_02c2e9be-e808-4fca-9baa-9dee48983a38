import Taro, { useReady } from "@tarojs/taro";
import React from "react";
import MessageApi from "@components/MessageApi/MessageApi";
import { useState } from "react";
import TopNavBar from "@components/topNavBar";
import { Button, Checkbox, Popup, Price, PullToRefresh, TabPane, Tabs } from "@nutui/nutui-react-taro";
import "./index.scss";
import "@utils/app.scss";

import request from "@service/request";
import { Image, Picker, Text } from "@tarojs/components";
import { IconFont } from "@nutui/icons-react-taro";

type payItem = {
    Id: string;
    PayMoney: number;
    NoPay: number;
    CostTypeName: string;
    OrderNumber: number;
    Remark: string;
    CreateTime: any;
    PayTime: any;
    Checked: boolean;
    ChannelOrderSn: string;
    InsOrderSn: string;
};

const App = () => {
    const message: any = React.useRef();

    /**
     * 用户登录的 信息
     */
    // const [userInfo, setUserInfo] = useState<API.UserInfo>(Object);

    const [biilList, setBillList] = useState<payItem[]>([]);

    const [showPayPopup, setShowPayPopup] = useState(false);
    const [OrderId, setOrderId] = useState("");
    const [OrderShortId, setOrderShortId] = useState("");
    const [PayMoney, setPayMoney] = useState(0);

    const [StudentId, setStudentId] = useState("");
    const [AccountId, setAccountId] = useState("");
    const [OutTable, setOutTable] = useState("");
    /**
     * 支付成功 等待的过程
     */
    const [payLoading, setPayLoading] = useState(false);

    const [totalPayMoney, setTotalPayMoney] = useState(0);

    /**
     * 可用的 优惠券
     */
    const [CouponList, setCouponList] = useState<any[]>([]);
    const [CouponNameList, setCouponNameList] = useState<string[]>([]);
    const [CouponIndex, setCouponIndex] = useState(-1);

    /**
     * 订单支付状态的 Tab
     */
    const [statusIndex, setStatusIndex] = useState(0);

    /**
     * 当前步骤 Index
     */
    const [StepIndex, setStepIndex] = useState(0);

    /**
     * 页面 ready 方法
     */
    useReady(() => {
        message.current.openLoading("正在登录");
        GetMyShouldPayList();
    });

    /**
     * 获得 我的账单的列表
     */
    const GetMyShouldPayList = () => {
        message.current.openLoading("正在加载订单数据");
        request.post<API.Result<any>>("/Child/ShouldPay/getMyShouldPayList", {}).then((json) => {
            message.current.closeLoading();
            if (json && json.success) {
                setBillList(json.data);
            } else if (json) {
                message.current.openDialog("读取失败", json.message);
            }
        });
    };

    /**
     * 提交订单
     */
    const createOrder = () => {
        setPayLoading(false);
        message.current.openLoading("正在生成订单");

        let Ids: string[] = [];

        biilList.map((item) => {
            if (item.Checked) {
                Ids.push(item.Id);
            }
        });

        request
            .put<
                API.Result<{
                    Id: string;
                    ShortId: string;
                    PayMoney: number;
                    OutTable: string;
                    AccountId: string;
                    StudentId: string;
                }>
            >("/Child/Pay/setPayOrder", {
                Ids: Ids,
            })
            .then((json) => {
                message.current.closeLoading();
                if (json && json.success) {
                    setOrderId(json.data.Id);
                    setOrderShortId(json.data.ShortId);
                    setPayMoney(json.data.PayMoney);
                    setOutTable(json.data.OutTable);
                    setAccountId(json.data.AccountId);
                    setStudentId(json.data.StudentId);
                    setShowPayPopup(true);
                    GetMyCouponList(json.data.PayMoney);
                } else {
                    json && message.current.openDialog("生成订单错误", json.message);
                }
            });
        // }))
        // setDialogShow(true);
    };

    /**
     * 获得 我的 优惠券
     */
    const GetMyCouponList = (PayMoney) => {
        setCouponList([]);
        setCouponNameList([]);
        setCouponIndex(-1);
        request
            .post<API.Result<any>>("/Child/Coupon/getMyCouponList", {
                PayMoney: PayMoney,
            })
            .then((json) => {
                message.current.closeLoading();
                if (json && json.success) {
                    setCouponList(json.data);
                    let data: any[] = [];
                    json.data.map((item) => {
                        data.push(item.Name);
                    });

                    setCouponNameList(data);

                    if (data.length > 0) {
                        setCouponIndex(0);
                    } else {
                        setCouponIndex(-1);
                    }
                } else {
                    json && message.current.openDialog("读取失败", json.message);
                    setShowPayPopup(false);
                }
            });
    };

    const pay = () => {
        setShowPayPopup(false);
        message.current.openLoading("正在生成支付数据");

        request
            .put<
                API.Result<{
                    OrderId: string;
                    SignData: {
                        timeStamp: string;
                        package: string;
                        paySign: string;
                        appId: string;
                        signType: string;
                        nonceStr: string;
                    };
                }>
            >("/Pay/WxPay/createOrder", {
                AccountId: AccountId,
                PayMoney: PayMoney,
                OutTable: OutTable,
                OutId: OrderId,
                UserId: StudentId,
                CouponId: CouponList[CouponIndex]?.Id,
                StudentId: StudentId,
            })
            .then((json) => {
                message.current.closeLoading();
                if (json && json.success) {
                    var payInfo = json.data.SignData;
                    setPayLoading(true);
                    Taro.requestPayment({
                        timeStamp: payInfo.timeStamp,
                        nonceStr: payInfo.nonceStr,
                        package: payInfo.package,
                        signType: "MD5",
                        paySign: payInfo.paySign,
                        success: function () {
                            GetResult(json.data.OrderId);
                        },
                        fail: function (res) {
                            setPayLoading(false);
                            setShowPayPopup(false);
                            if (res.errMsg === "requestPayment:fail cancel") {
                                json && message.current.openDialog("交易取消", "当前交易被取消,请重新发起支付");
                            } else {
                                json && message.current.openDialog("", res.errMsg);
                            }
                        },
                    });
                } else {
                    setPayLoading(false);
                    setShowPayPopup(false);
                    json && message.current.openDialog("支付配置出错", json.message);
                }
            });
    };

    /**
     * 获取订单的支付状态
     * @param OrderId
     */
    const GetResult = (OrderId: string) => {
        message.current.openLoading("等待支付结果");

        request
            .post<API.Result<{}>>("/Pay/WxPay/GetStatus", {
                Id: OrderId,
            })
            .then((json) => {
                if (json && json.success) {
                    message.current.closeLoading();
                    setPayLoading(false);
                    setShowPayPopup(false);
                    setStepIndex(1);
                } else {
                    setTimeout(GetResult, 1000, OrderId);
                }
            });
    };

    return (
        <>
            <MessageApi ref={message}></MessageApi>
            {StepIndex == 0 && (
                <>
                    <TopNavBar title="我的账单"></TopNavBar>

                    <Tabs
                        value={statusIndex}
                        style={{
                            position: "fixed",
                            width: "100%",
                            zIndex: 99,
                        }}
                        onChange={(e: number) => {
                            setStatusIndex(e);
                        }}
                    >
                        <TabPane title="全部订单"></TabPane>
                        <TabPane title="已经支付"></TabPane>
                        <TabPane title="暂未支付"></TabPane>
                    </Tabs>
                    <view
                        className="tui-container"
                        style={{
                            backgroundColor: "#f1f1f1",
                            padding: "0 0 0 0",
                            height: "auto",
                            minHeight: "100vh",
                            marginTop: "46px",
                            paddingBottom: "calc(env(safe-area-inset-bottom) + 54px)",
                        }}
                    >
                        <view className="tui-order--list">
                            <PullToRefresh>
                                {biilList?.map((item, index) => (
                                    <>
                                        {(statusIndex == 0 || (statusIndex == 1 && item.NoPay == 0) || (statusIndex == 2 && item.NoPay > 0)) && (
                                            <view
                                                className="tui-card__wrap"
                                                onClick={() => {
                                                    let _biilList = [...biilList];

                                                    if (item.Checked) {
                                                        _biilList[index].Checked = false;
                                                    } else {
                                                        _biilList[index].Checked = true;
                                                    }

                                                    setBillList(_biilList);

                                                    let totalPayMoney = 0;
                                                    _biilList.map((payItem) => {
                                                        payItem.Checked && (totalPayMoney = totalPayMoney + payItem.NoPay);
                                                    });

                                                    setTotalPayMoney(totalPayMoney);
                                                }}
                                            >
                                                {item.PayTime !== "0001-01-01 00:00:00" && (
                                                    <view
                                                        className="tui-list-class tui-list-cell tui-arrow-right tui-line-left tui-line-right"
                                                        style={{
                                                            backgroundColor: "#fff",
                                                            fontSize: "14px",
                                                            color: "#333",
                                                            padding: "13px 15px",
                                                        }}
                                                    >
                                                        <view className="tui-flex__between">
                                                            <view
                                                                className="tui-text__wrap tui-text__inline tui-text__left"
                                                                style={{
                                                                    textAlign: "left",
                                                                    padding: 0,
                                                                }}
                                                            >
                                                                <view
                                                                    className="tui-text__content"
                                                                    style={{
                                                                        color: "#999",
                                                                        fontSize: "12px",
                                                                        lineHeight: "auto",
                                                                        textAlign: "left",
                                                                        textDecoration: "none",
                                                                        fontWeight: "400",
                                                                    }}
                                                                >
                                                                    付款时间: {item.PayTime}
                                                                </view>
                                                            </view>
                                                        </view>
                                                    </view>
                                                )}
                                                <view
                                                    className="tui-order__item tui-list-class tui-list-cell tui-line-left tui-line-right"
                                                    style={{
                                                        backgroundColor: "#fff",
                                                        fontSize: "14px",
                                                        color: "#333",
                                                        padding: "13px 15px",
                                                    }}
                                                >
                                                    <view className="tui-order__inner">
                                                        {/* <Image className='tui-goods__img' src=""></Image> */}
                                                        <view className="tui-goods__content">
                                                            <view className="tui-goods__title">商品名称：{item.CostTypeName}</view>

                                                            {item.InsOrderSn && item.InsOrderSn != "" && <view className="tui-goods__descr">交易单号：{item.InsOrderSn}</view>}
                                                            <view className="tui-goods__descr">订单备注：{item.Remark === "" ? " --- " : item.Remark}</view>
                                                        </view>
                                                        <view className="tui-price__box">
                                                            {item.NoPay > 0 && <Price price={item.PayMoney} size="large" thousands />}
                                                            {item.NoPay == 0 && (
                                                                <Price
                                                                    price={item.PayMoney}
                                                                    size="large"
                                                                    style={{
                                                                        color: "#50a14f",
                                                                    }}
                                                                    thousands
                                                                />
                                                            )}
                                                        </view>
                                                    </view>
                                                </view>
                                                {item.NoPay > 0 && (
                                                    <view className="tui-btn--box tui-spacing tui--flex">
                                                        <view
                                                            style={{
                                                                fontSize: "26rpx",
                                                                textAlign: "left",
                                                                flex: 1,
                                                                width: "100%",
                                                            }}
                                                        >
                                                            <Checkbox
                                                                key={item.Id}
                                                                checked={item.Checked}
                                                                onChange={(e) => {
                                                                    console.log(e);
                                                                }}
                                                            >
                                                                选择
                                                            </Checkbox>
                                                        </view>
                                                    </view>
                                                )}
                                            </view>
                                        )}
                                    </>
                                ))}
                                <view className="tui-divider" style={{ height: "100rpx" }}>
                                    <view
                                        className="tui-divider-line"
                                        style={{
                                            width: "50%",
                                            background: "#e5e5e5",
                                        }}
                                    ></view>
                                    <view
                                        style={{
                                            color: "#999",
                                            fontSize: "24rpx",
                                            lineHeight: "24rpx",
                                            backgroundColor: "#f1f1f1",
                                            fontWeight: "normal",
                                        }}
                                        className="tui-divider-text"
                                    >
                                        这是我的底线
                                    </view>
                                </view>
                            </PullToRefresh>
                        </view>
                    </view>
                    <view className="tui-goods__bar safe__area" style={{ backgroundColor: "#fff" }}>
                        <view className="tui-btn--box tui-spacing tui--flex">
                            <view
                                className="tui--flex"
                                style={{
                                    flex: 1,
                                }}
                            >
                                <Checkbox
                                    key={"check-all"}
                                    onChange={(e) => {
                                        console.log(e);
                                        let _biilList = [...biilList];

                                        _biilList.map((payItem) => {
                                            if (payItem.NoPay > 0) payItem.Checked = e;
                                        });
                                        setBillList(_biilList);

                                        let totalPayMoney = 0;
                                        _biilList.map((payItem) => {
                                            payItem.Checked && (totalPayMoney = totalPayMoney + payItem.NoPay);
                                        });

                                        setTotalPayMoney(totalPayMoney);
                                    }}
                                ></Checkbox>
                                全选
                            </view>
                            <view
                                className="tui--flex"
                                style={{
                                    textAlign: "right",
                                    float: "right",
                                }}
                            >
                                <view className="tui-price--box">
                                    <Text className="tui-size--24">合计：</Text>

                                    <Price price={totalPayMoney} size={"normal"} thousands />
                                </view>
                                {totalPayMoney > 0 && (
                                    <Button type="info" onClick={createOrder}>
                                        提交订单
                                    </Button>
                                )}
                                {totalPayMoney <= 0 && (
                                    <Button type="info" disabled>
                                        提交订单
                                    </Button>
                                )}
                            </view>
                        </view>
                    </view>

                    <Popup
                        visible={showPayPopup}
                        round
                        position="bottom"
                        zIndex={1010}
                        style={{
                            zIndex: 1010,
                        }}
                        onClose={() => {
                            setShowPayPopup(false);
                        }}
                    >
                        <view className="safe__area">
                            <view className="tui-bp__tit tui-flex__center">
                                <text>确认付款</text>
                                <view
                                    className="tui-icon--close"
                                    onClick={() => {
                                        setShowPayPopup(false);
                                    }}
                                >
                                    <IconFont name="close" size={14} />
                                </view>
                            </view>
                            <view className="tui-bp__content">
                                <view
                                    className="tui-bp__price-box tui-flex__center"
                                    style={{
                                        paddingBottom: "10rpx",
                                    }}
                                >
                                    <Price price={PayMoney} size="large" thousands />
                                </view>
                                {CouponList[CouponIndex] && (
                                    <view className="tui-bp__price-box tui-flex__center">
                                        <text
                                            style={{
                                                color: "#40AE36",
                                                fontSize: "24rpx",
                                            }}
                                        >
                                            立减优惠 {CouponList[CouponIndex].DiscountMoney.toFixed(2)}
                                        </text>
                                    </view>
                                )}
                                <view
                                    className="tui-list-class tui-list-cell tui-arrow-right"
                                    style={{
                                        backgroundColor: "#fff",
                                        fontSize: "14px",
                                        color: "#333",
                                        padding: "15px 0",
                                    }}
                                >
                                    <view className="tui-flex__between">
                                        <text className="tui-bp__name">订单编号</text>
                                        <text>{OrderShortId}</text>
                                    </view>
                                </view>
                                <view
                                    className="tui-list-class tui-list-cell tui-arrow-right"
                                    style={{
                                        backgroundColor: "#fff",
                                        fontSize: "14px",
                                        color: "#333",
                                        padding: "15px 0",
                                    }}
                                >
                                    <view className="tui-flex__between">
                                        <text className="tui-bp__name">付款方式</text>
                                        <text>微信支付</text>
                                    </view>
                                </view>
                                {CouponList.length > 0 && (
                                    <view
                                        className="tui-list-class tui-list-cell tui-arrow-right"
                                        style={{
                                            backgroundColor: "#fff",
                                            fontSize: "14px",
                                            color: "#333",
                                            padding: "15px 0",
                                        }}
                                    >
                                        <view className="tui-flex__between">
                                            <text className="tui-bp__name">减免方案</text>
                                            {CouponList.length == 0 && <text>无可用减免方案</text>}
                                            {CouponList.length > 0 && (
                                                <Picker
                                                    mode="selector"
                                                    range={CouponNameList}
                                                    onChange={(e: any) => {
                                                        setCouponIndex(parseInt(e.detail.value));
                                                    }}
                                                >
                                                    {CouponNameList[CouponIndex] ? CouponNameList[CouponIndex] : "请选择减免方案"}
                                                </Picker>
                                            )}
                                        </view>
                                    </view>
                                )}
                                <view className="ti-btn--box">
                                    {!payLoading && (
                                        <Button
                                            size="large"
                                            type="primary"
                                            onClick={pay}
                                            style={{
                                                width: "100%",
                                            }}
                                        >
                                            立即付款
                                        </Button>
                                    )}
                                    {payLoading && (
                                        <Button
                                            size="large"
                                            type="primary"
                                            disabled={true}
                                            style={{
                                                width: "100%",
                                            }}
                                            loading={true}
                                        >
                                            等待付款结果
                                        </Button>
                                    )}
                                </view>
                            </view>
                        </view>
                    </Popup>
                </>
            )}
            {StepIndex == 1 && (
                <>
                    <TopNavBar
                        title="支付完成"
                        leftClick={() => undefined}
                        homeClick={() => {
                            undefined;
                        }}
                        BgColor="#E41F19"
                        FontColor="#fff"
                        hideLeft={false}
                    ></TopNavBar>
                    <view className="success-container">
                        <view className="container">
                            <view className="tui-bg"></view>
                            <view className="tui-content">
                                <view className="tui-success-form">
                                    <Image src="https://cdn.51panda.com/WxAppImage/pay/img_recharge_success.png" className="tui-icon" mode="widthFix"></Image>
                                    <view className="tui-title">订单已支付成功</view>
                                    <view className="tui-sub-title">非常感谢完成订单的支付</view>
                                    <view className="tui-btn-box">
                                        <Button
                                            className="tui-btn-class tui-btn tui-danger-outline tui-outline-fillet"
                                            style={{
                                                width: "240rpx",
                                                height: "70rpx",
                                                lineHeight: "70rpx",
                                                fontSize: "28rpx",
                                                margin: 0,
                                            }}
                                            onClick={() => {
                                                Taro.navigateTo({
                                                    url: "/subpackages/child/login/index",
                                                });
                                            }}
                                        >
                                            返回首页
                                        </Button>
                                        <Button
                                            className="tui-btn-class tui-btn tui-btn-danger tui-fillet tui-shadow-danger"
                                            style={{
                                                width: "240rpx",
                                                height: "70rpx",
                                                lineHeight: "70rpx",
                                                fontSize: "28rpx",
                                                margin: 0,
                                            }}
                                            onClick={() => {
                                                Taro.navigateTo({
                                                    url: "/subpackages/child/order/index",
                                                });
                                            }}
                                        >
                                            订单详情
                                        </Button>
                                    </view>
                                </view>
                            </view>
                            <view className="tui-tips">
                                <view className="tui-grey">温馨提示:</view>
                                <view className="tui-light-grey">
                                    付款成功后，我们不会以付款异常、卡单、系统升级为由联系您。请勿泄露银行卡号、手机验证码，否则会造成钱款损失！谨防电话诈骗！
                                </view>
                            </view>
                        </view>
                    </view>
                </>
            )}
        </>
    );
};
export default App;
