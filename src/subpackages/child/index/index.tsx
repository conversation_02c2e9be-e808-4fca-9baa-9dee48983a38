import Taro, { useReady } from "@tarojs/taro";
import { useRef, useState } from "react";
import TopNavBar from "@components/topNavBar";
import MessageApi from "@components/MessageApi/MessageApi";
import "./index.scss";
import "@utils/app.scss";

import { login } from "@utils/login";
import request from "@service/request";
import { Image } from "@tarojs/components";
import { tool } from "@utils/tool";
import IconFont from "@components/iconfont";
import config from "@/config";

const App = () => {
    const message: any = useRef();

    const [xm, setxm] = useState("");
    const [sfzmhm, setsfzmhm] = useState("");
    const [className, setClassName] = useState("");

    /**
     * 页面 ready 方法
     */
    useReady(() => {
        login().then(() => {
            GetMyInfo();
        });
    });

    /**
     * 获取 当前登录用户的信息
     */
    const GetMyInfo = () => {
        request.post<API.Result<any>>("/Child/WxLogin/getMyInfo", {}).then((json) => {
            if (json && json.success) {
                setxm(json.data.xm);
                setsfzmhm(json.data.sfzmhm);
                setClassName(json.data.className);
            } else {
                message.current.openDialog("配置错误", json.message);
            }
        });
    };

    /**
     * 退出系统
     */
    const logOut = () => {
        message.current.openDialog(
            "确认操作",
            "是否确认退出系统?",
            () => {
                message.current.openLoading("正在退出系统");
                const accessTokenKey = config.ACCESS_TOKEN_KEY;
                let token = tool.data.get(accessTokenKey);

                request
                    .post<API.Result<{}>>("/Child/WxLogin/logOut", {
                        Token: token,
                    })
                    .then(() => {
                        message.current.closeLoading();
                        Taro.navigateTo({
                            url: "/subpackages/child/login/index",
                        });
                    });
            },
            "确认",
            "取消"
        );
    };

    return (
        <>
            <MessageApi ref={message}></MessageApi>
            <TopNavBar
                title=""
                leftClick={() => undefined}
                homeClick={() => {
                    undefined;
                }}
                BgColor="#5677FC"
                FontColor="#fff"
                hideLeft={true}
            ></TopNavBar>
            <view className="container">
                <view className="top-container">
                    <Image className="bg-img" src="https://cdn.51panda.com/WxAppImage/thorui-my/mine_bg_3x.png"></Image>
                    <view
                        className="user"
                        style={{
                            marginTop: "100rpx",
                        }}
                    >
                        <Image className="avatar-img" src="https://cdn.51panda.com/avator.jpg"></Image>
                        <view className="user-info-xm">
                            <text>
                                {xm} {className}
                            </text>
                        </view>
                        <view className="user-info--xm">
                            <text>{sfzmhm}</text>
                        </view>
                    </view>
                </view>
                <view className="bottom-container">
                    <view className="tui-box tui-tool-box">
                        <view
                            style={{
                                backgroundColor: "#fff",
                                fontSize: "14px",
                                color: "#333",
                                padding: 0,
                            }}
                            className={"tui-list-class tui-list-cell"}
                        >
                            <view className="tui-cell-header">
                                <view className="tui-cell-title">常用功能</view>
                            </view>
                        </view>
                        <view className="ul-item">
                            <view
                                className="item"
                                onClick={() => {
                                    Taro.navigateTo({
                                        url: "/subpackages/child/order/index",
                                    });
                                }}
                            >
                                <IconFont name="Discount" size={50}></IconFont>
                                <text className="item-name">我的账单</text>
                            </view>
                            <view className="item">
                                <IconFont name="Ticket" size={50}></IconFont>
                                <text className="item-name">我的电券</text>
                            </view>
                            <view
                                className="item"
                                onClick={() => {
                                    Taro.navigateTo({
                                        url: "/subpackages/child/mall/index",
                                    });
                                }}
                            >
                                <IconFont name="Buy" size={50}></IconFont>
                                <text className="item-name">线上商城</text>
                            </view>
                            <view className="item" onClick={logOut}>
                                <IconFont name="Send" size={50}></IconFont>
                                <text className="item-name">退出系统</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </>
    );
};
export default App;
