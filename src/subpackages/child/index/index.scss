// .img {
//     width: 100%;
//     // height:auto;
//     display: block;
//     border: 0;
// }

// .aui-flex {
//     display: -webkit-box;
//     display: -webkit-flex;
//     display: flex;
//     -webkit-box-align: center;
//     -webkit-align-items: center;
//     align-items: center;
//     padding: 15px;
//     position: relative;
// }

// .aui-sure-img {
//     width: 40vw;
//     margin-right: 0.5rem;
// }

// .aui-flex-box {
//     -webkit-box-flex: 1;
//     -webkit-flex: 1;
//     flex: 1;
//     min-width: 0;
//     // font-size: 14px;
//     color: #333;
// }

// .tab-panel-item .b-line:after {
//     left: 15px;
// }

// .b-line:after {
//     content: '';
//     position: absolute;
//     z-index: 2;
//     bottom: 0;
//     left: 0;
//     width: 100%;
//     height: 1px;
//     border-bottom: 1px solid #e2e2e2;
//     -webkit-transform: scaleY(0.5);
//     transform: scaleY(0.5);
//     -webkit-transform-origin: 0 100%;
//     transform-origin: 0 100%;
// }








// .echengExamIndex {
//     .nut-navbar__left {
//         .nut-navbar__text {
//             color: #666666;
//         }
//     }
// }

// .nut-picker__control {
//     height: 56rpx;
//     padding: 15rpx;
// }

// .linkList {
//     background-color: #fff;
// }

// .linkList .weui-grids {
//     width: 100%;
//     display: flex;
//     flex-flow: row wrap;
// }

// .linkList .weui-grids,
// .linkList .weui-grid {
//     border: 0;
//     overflow: hidden;
//     text-align: center;
// }

// .linkList .weui-grid {
//     width: 25%;
//     font-size: 28rpx;
//     color: #666666;
//     padding: 30px 0 20px 0;
// }

// .linkList .weui-grid__icon {
//     width: 80rpx;
//     height: 80rpx;
// }



// .field .panel-item {
//     margin: 25px 0;
// }

// .training .panel-item {
//     padding: 10px 0 10px 10px;
// }

// .training .panel-img image {
//     height: 200rpx;
//     display: block;
//     border-radius: 5px;
// }

// .field .panel-img image {
//     height: 280rpx;
//     display: block;
// }

// .panel-img .spec-type {
//     width: 100%;
//     position: absolute;
//     left: 0;
//     bottom: 0;
//     background: rgba(0, 0, 0, .3);
//     padding: 5px 2px;
//     font-size: 0.75rem;
//     color: #FFCA20;
// }

// .field .panel-img .spec-type {
//     font-size: 0.85rem;
//     padding: 6px 10px;
// }

// .panel-con {
//     position: relative;
//     padding: 5px 5px 0 10px;
//     overflow: hidden;
// }

// .field .panel-con {
//     padding: 10px;
// }

// .panel-con .h3 {
//     margin-bottom: 7px;
// }

// .panel-con .h4 {
//     margin-top: 7px;
//     line-height: 22px;
// }

// // 我的页面



// .aui-flex-pu-top {
//     display: -webkit-box;
//     display: -webkit-flex;
//     display: flex;
//     -webkit-box-align: center;
//     -webkit-align-items: center;
//     align-items: center;
//     padding: 15px 35px 10px 40px;
//     position: relative;
// }

// .aui-flex {
//     display: -webkit-box;
//     display: -webkit-flex;
//     display: flex;
//     -webkit-box-align: center;
//     -webkit-align-items: center;
//     align-items: center;
//     padding: 40px;
//     position: relative;
// }

// .aui-flex-box {
//     -webkit-box-flex: 1;
//     -webkit-flex: 1;
//     flex: 1;
//     min-width: 0;
//     font-size: 14px;
//     color: #333;
//     padding-left: 15px;
// }

// .aui-flex-box-play {
//     -webkit-box-flex: 1;
//     -webkit-flex: 1;
//     flex: 1;
//     min-width: 0;
//     font-size: 14px;
//     color: #333;
//     padding-left: 15px;
//     height: 180px;

//     h2 {
//         height: 150px;
//     }
// }

// .aui-flex-box-footer {
//     // position: absolute;
//     right: 0px;
//     bottom: 5px;
// }

// /* 必要布局样式css */
// .aui-flexView {
//     width: 100%;
//     height: 100%;
//     margin: 0 auto;
//     display: -webkit-box;
//     display: -webkit-flex;
//     display: -ms-flexbox;
//     display: flex;
//     -webkit-box-orient: vertical;
//     -webkit-box-direction: normal;
//     -webkit-flex-direction: column;
//     -ms-flex-direction: column;
//     flex-direction: column;
// }

// .aui-scrollView {
//     width: 100%;
//     height: 100%;
//     -webkit-box-flex: 1;
//     -webkit-flex: 1;
//     -ms-flex: 1;
//     flex: 1;
//     overflow-y: auto;
//     overflow-x: hidden;
//     -webkit-overflow-scrolling: touch;
//     position: relative;
//     margin-top: 0;
//     // padding-bottom: 15px;
//     text-align: center;
//     font-size: 0.8rem;
// }

// .aui-navBar {
//     height: 44px;
//     position: relative;
//     display: -webkit-box;
//     display: -webkit-flex;
//     display: -ms-flexbox;
//     display: flex;
//     z-index: 1002;
//     background: #f6f6f6;
// }

// .aui-navBar-item {
//     height: 44px;
//     min-width: 25%;
//     -webkit-box-flex: 0;
//     -webkit-flex: 0 0 25%;
//     -ms-flex: 0 0 25%;
//     flex: 0 0 25%;
//     padding: 0 0.9rem;
//     display: -webkit-box;
//     display: -webkit-flex;
//     display: -ms-flexbox;
//     display: flex;
//     -webkit-box-align: center;
//     -webkit-align-items: center;
//     -ms-flex-align: center;
//     align-items: center;
//     font-size: 0.7rem;
//     white-space: nowrap;
//     overflow: hidden;
//     color: #808080;
//     position: relative;
// }

// .aui-navBar-item:first-child {
//     -webkit-box-ordinal-group: 2;
//     -webkit-order: 1;
//     -ms-flex-order: 1;
//     order: 1;
//     margin-right: -25%;
//     font-size: 0.9rem;
//     font-weight: bold;
// }

// .aui-navBar-item:last-child {
//     -webkit-box-ordinal-group: 4;
//     -webkit-order: 3;
//     -ms-flex-order: 3;
//     order: 3;
//     -webkit-box-pack: end;
//     -webkit-justify-content: flex-end;
//     -ms-flex-pack: end;
//     justify-content: flex-end;
// }

// .aui-center {
//     -webkit-box-ordinal-group: 3;
//     -webkit-order: 2;
//     -ms-flex-order: 2;
//     order: 2;
//     display: -webkit-box;
//     display: -webkit-flex;
//     display: -ms-flexbox;
//     display: flex;
//     -webkit-box-pack: center;
//     -webkit-justify-content: center;
//     -ms-flex-pack: center;
//     justify-content: center;
//     -webkit-box-align: center;
//     -webkit-align-items: center;
//     -ms-flex-align: center;
//     align-items: center;
//     height: 44px;
//     width: 50%;
//     margin-left: 25%;
// }

// .aui-center-title {
//     text-align: center;
//     width: 100%;
//     white-space: nowrap;
//     overflow: hidden;
//     display: block;
//     text-overflow: ellipsis;
//     font-size: 0.95rem;
//     color: #333;
// }

// .icon {
//     width: 40px;
//     height: 40px;
//     display: block;
//     border: none;
//     float: left;
//     background-size: 40px;
//     background-repeat: no-repeat;
// }

// .aui-centers-box {
//     background-image: url("https://smallimage.122erp.com/WxApp/taro/images/auiimage/head.jpg");
//     background-size: cover;
//     width: 100%;
//     height: 440px;
// }

// .aui-centers-ad {
//     background: #eff3f7;
//     padding: 20px 20px;
// }

// .aui-centers-img {
//     width: 100%;
//     height: auto;
// }

// .aui-centers-img img {
//     width: 100%;
//     height: auto;
//     display: block;
//     border: none;
//     border-radius: 10px;
// }

// .aui-palace {
//     padding: 10px 10px;
//     position: relative;
//     overflow: hidden;
// }

// .aui-palace-grid {
//     position: relative;
//     float: left;
//     padding: 5px 1px 5px 1px;
//     width: 33.333%;
//     box-sizing: border-box;
//     display: inline-block;
// }

// .aui-palace-grid-icon {
//     width: 80px;
//     height: 80px;
//     margin: 0 auto;
//     text-align: center;
// }

// .aui-palace-grid-icon img {
//     display: block;
//     width: 80px;
//     height: 80px;
//     border: none;
// }

// .aui-palace-grid-text {
//     display: block;
//     text-align: center;
//     color: #333333;
//     font-size: 13px;
//     white-space: nowrap;
//     text-overflow: ellipsis;
//     overflow: hidden;
//     padding-top: 5px;
// }

// .aui-palace-grid-text h2 {
//     font-size: 0.8rem;
//     font-weight: normal;
//     color: #3e3e3e;
// }

// .aui-palace-grid-text p {
//     font-size: 0.7rem;
//     font-weight: normal;
//     color: #999999;
// }

// .aui-button-btn {
//     width: 100%;
//     padding: 40px 0;
// }

// .aui-button-btn a {
//     background: #e7edf4;
//     width: 80%;
//     height: 84px;
//     line-height: 84px;
//     margin: 0 auto;
//     display: block;
//     border-radius: 40px;
// }

// .aui-flex-box-play h2 {
//     font-size: 0.9rem;
//     text-align: left;
// }

// .aui-flex-box h2 {
//     font-size: 0.9rem;
//     text-align: left;
// }

// .aui-flex-box-all {
//     font-size: 0.8rem;
// }

// .aui-head-title h2 {
//     font-size: 36px;
//     color: #fff;
//     text-align: left;
//     padding-top: 80px;
//     padding-left: 60px;
// }

// .aui-head-title p {
//     color: #fff;
//     text-align: left;
//     font-size: 0.8rem;
//     padding-left: 5px;
// }

// .aui-flex-pu {
//     line-height: 42px;
// }

// .aui-flex-pu em {
//     font-size: 0.7rem;
//     font-style: normal;
//     background: #7b8289;
//     border-radius: 40px;
//     display: block;
//     width: 170px;
//     color: #fff;
//     position: relative;
//     padding-right: 5px;
//     text-align: left;
//     padding-top: 2px;
//     padding-bottom: 2px;
// }

// .aui-flex-pu em:after {
//     content: " ";
//     display: inline-block;
//     height: 6px;
//     width: 6px;
//     border-width: 2px 2px 0 0;
//     border-color: #fff;
//     border-style: solid;
//     -webkit-transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
//     transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
//     position: relative;
//     top: -2px;
//     position: absolute;
//     top: 50%;
//     margin-top: -4px;
//     right: 8px;
// }

// .icon-vip {
//     background-image: url('data:image/png;base64,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');
// }

// .aui-edit-btn {
//     position: relative;
// }

// .aui-edit-btn a {
//     border-radius: 100%;
//     background: #4b5762;
//     box-shadow: 0 3px 9px #38424b;
//     display: block;
//     position: absolute;
//     right: 20px;
//     top: -15px;
//     padding: 15px;
// }

// .icon-edit {
//     background-size: 50px;
//     width: 50px;
//     height: 50px;
//     background-image: url('data:image/png;base64,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');
// }

// .aui-img-logo {
//     width: 160px;
//     height: 160px;
//     overflow: hidden;
//     box-shadow: 0 3px 10px #4b5762;
//     border-radius: 100%;
// }

// .aui-img-logo img {
//     width: 160px;
//     height: 160px;
//     display: block;
//     border: none;
//     border-radius: 100%;
// }

// .aui-flex-top {
//     padding-top: 70px;
// }

// .aui-flex-box-item {
//     position: relative;
//     float: left;
//     padding: 5px 1px 5px 1px;
//     width: 33.333%;
//     box-sizing: border-box;
//     display: inline-block;
//     text-align: center;
// }

// .aui-flex-box-item p {
//     color: #9b9b9b;
//     font-size: 0.8rem;
// }

// .aui-flex-box-item .aui-bod {
//     color: #333;
//     font-weight: bold;
// }

// .aui-flex-top .aui-flex-box {
//     padding-top: 30px;
// }

// .aui-footer {
//     width: 100%;
//     position: relative;
//     z-index: 100;
//     display: -webkit-box;
//     display: -webkit-flex;
//     display: -ms-flexbox;
//     display: flex;
//     -webkit-box-align: center;
//     -webkit-align-items: center;
//     -ms-flex-align: center;
//     align-items: center;
//     padding: 7px 5px 7px 5px;
//     background: #ffffff;
//     box-shadow: 0 -2px 9px #efefef;
// }

// .aui-tabBar-item {
//     -webkit-box-flex: 1;
//     -webkit-flex: 1;
//     -ms-flex: 1;
//     flex: 1;
//     display: -webkit-box;
//     display: -webkit-flex;
//     display: -ms-flexbox;
//     display: flex;
//     -webkit-box-orient: vertical;
//     -webkit-box-direction: normal;
//     -webkit-flex-direction: column;
//     -ms-flex-direction: column;
//     flex-direction: column;
//     -webkit-box-pack: center;
//     -webkit-justify-content: center;
//     -ms-flex-pack: center;
//     justify-content: center;
//     -webkit-box-align: center;
//     -webkit-align-items: center;
//     -ms-flex-align: center;
//     align-items: center;
//     color: #979797;
// }

// .aui-tabBar-item-text {
//     display: inline-block;
//     font-size: 0.65rem;
//     color: #3b4048;
//     padding-top: 2px;
// }

// .aui-tabBar-item-active .aui-tabBar-item-text {
//     color: #fa9829;
// }

// .icon-home {
//     background-image: url('data:image/png;base64,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');
// }

// .icon-loan {
//     background-image: url('data:image/png;base64,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');
// }

// .icon-credit {
//     background-image: url('data:image/png;base64,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');
// }

// .icon-me {
//     background-image: url('data:image/png;base64,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');
// }

// .icon-meTo {
//     background-image: url('data:image/png;base64,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');
// }

// .aui-footer-fixed {
//     position: fixed;
//     bottom: 0;
//     left: 0;
//     z-index: 49;
// }




// .tui-my_header {
//     width: 100%;
//     height: 600rpx;
//     background: linear-gradient(to right, #f1f3f7, #f1f3f7, #fff4fc);
//     position: relative;
// }

// .tui-userinfo__box {
//     width: 100%;
//     padding: 120rpx 30rpx 0;
//     box-sizing: border-box;
//     display: flex;
//     justify-content: space-between;
// }

// .tui-name {
//     font-size: 48rpx;
//     font-weight: 700;
//     color: #152338;
//     width: 500rpx;
//     white-space: nowrap;
//     overflow: hidden;
//     text-overflow: ellipsis;
// }

// .tui-intro {
//     font-size: 26rpx;
//     line-height: 30rpx;
//     color: #aeafb7;
//     width: 500rpx;
//     white-space: nowrap;
//     overflow: hidden;
//     text-overflow: ellipsis;
//     padding-top: 20rpx;
// }

// .tui-avatar__box {
//     position: relative;
//     flex-shrink: 0;
// }

// .tui-avatar {
//     width: 160rpx;
//     height: 160rpx;
//     border-radius: 50%;
//     overflow: hidden;
//     justify-content: center;
// }

// .tui-edit-ic {
//     position: absolute;
//     width: 44rpx;
//     height: 44rpx;
//     right: 0;
//     bottom: 10rpx;
//     overflow: visible;
// }

// .tui-edit-ic::after {
//     content: '';
//     width: 100%;
//     height: 100%;
//     border: 1rpx solid #fff;
//     position: absolute;
//     left: -1rpx;
//     top: -1rpx;
//     border-radius: 50%;
//     z-index: 2;
// }

// .tui-statistics__box {
//     width: 100%;
//     padding: 0 30rpx;
//     box-sizing: border-box;
//     display: flex;
// }

// .tui-statistics__item {
//     padding-right: 80rpx;
// }

// .tui-count {
//     font-size: 40rpx;
//     font-weight: 500;
//     color: #152338;
// }

// .tui-title {
//     font-size: 26rpx;
//     line-height: 28rpx;
//     color: #aeafb7;
//     padding-top: 12rpx;
// }

// .tui-vip__box {
//     width: 100%;
//     padding: 0 30rpx;
//     box-sizing: border-box;
//     position: absolute;
//     left: 0;
//     bottom: 0;
// }

// .tui-vip__bar {
//     width: 100%;
//     padding: 30rpx 40rpx;
//     box-sizing: border-box;
//     background-color: #383f54;
//     border-top-left-radius: 20rpx;
//     border-top-right-radius: 20rpx;
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
// }

// .tui-vip__ic {
//     width: 80rpx;
//     height: 30rpx;
// }

// .tui-vip__desc {
//     font-size: 24rpx;
//     line-height: 24rpx;
//     color: rgba(255, 255, 255, 0.7);
//     padding-top: 4rpx;
// }

// .tui-menu__box {
//     // width: 100%;
//     padding: 52rpx 0rpx 30rpx;
//     box-sizing: border-box;
//     background: #fff;
//     margin: 0 30rpx;
// }

// .tui-menu__list {
//     width: 100%;
//     display: flex;
//     align-items: center;
//     flex-wrap: wrap;
// }

// .tui-menu__item {
//     width: 25%;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     flex-direction: column;
//     margin-bottom: 30rpx;
//     position: relative;
// }

// .tui-menu__ic {
//     width: 80rpx;
//     height: 80rpx;
// }

// .tui-menu__title {
//     font-size: 24rpx;
//     line-height: 24rpx;
//     padding-top: 32rpx;
//     color: #4e4d52;
// }

// .tui-cell-header {
//     width: 100%;
//     padding-bottom: 72rpx;
//     box-sizing: border-box;
//     display: flex;
//     align-items: center;
//     font-size: 34rpx;
//     font-weight: 600;
//     color: #152338;
// }


/* pages/my/my.wxss */


page {
    background: #fafafa;
}

.container {
    position: relative;
}

.top-container {
    height: 440rpx;
    position: relative;
    display: flex;
    flex-direction: column;
}

.bg-img {
    position: absolute;
    width: 100%;
    height: 440rpx;
    z-index: -1;
}

.logout {
    width: 110rpx;
    height: 36rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 42rpx 0 24rpx 32rpx;
}

.logout-img {
    width: 36rpx;
    height: 36rpx;
    margin-right: 11rpx;
}

.logout-txt {
    font-size: 28rpx;
    color: #FEFEFE;
    line-height: 28rpx;
}

.user-wrapper {
    display: flex;
    justify-content: center;
}

.user {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.avatar-img {
    width: 160rpx;
    height: 160rpx;
    border-radius: 50%;
    align-self: center;
    position: absolute;
    left: 50rpx;
    top: 0rpx;
}

.user-info {
    display: flex;
    flex-direction: row;
    margin-top: 30rpx;
    align-items: center;
}
.user-info-xm {
    font-size: 36rpx;
    font-weight: 900;
    color: #FEFEFE;
    line-height: 28rpx;
    position: absolute;
    left: 240rpx;
    top: 40rpx;
    
}
.user-info--xm {
    font-size: 28rpx;
    font-weight: 300;
    color: #FEFEFE;
    line-height: 28rpx;
    position: absolute;
    left: 240rpx;
    top: 90rpx;
}


.user-info-mobile {
    margin-top: 30rpx;
    position: relative;
    font-size: 28rpx;
    color: #FEFEFE;
    line-height: 28rpx;
    align-self: center;
    padding: 0 50rpx;
}

.edit-img {
    position: absolute;
    width: 42rpx;
    height: 42rpx;
    right: 0;
    bottom: -4rpx;
}

.edit-img>image {
    width: 42rpx;
    height: 42rpx;
    padding-left: 25rpx;
}

.middle-container {
    height: 138rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    border-radius: 10rpx;
    background-color: #FFFFFF;
    margin: -30rpx 30rpx 26rpx 30rpx;
    box-shadow: 0 15rpx 10rpx -15rpx #efefef;

}

.middle-item {
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
}

.ticket-img {
    width: 80rpx;
    height: 80rpx;
    margin-left: 65rpx;
}

.middle-tag {
    font-size: 28rpx;
    color: #333333;
    line-height: 28rpx;
    font-weight: bold;
    padding-left: 22rpx;
}

.car-img {
    width: 80rpx;
    height: 80rpx;
    margin-left: 97rpx;
}

.bottom-container {
    // height: 334rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // padding: 40rpx 54rpx 40rpx 54rpx;
    // margin: 0 30rpx;
    margin: -180rpx 30rpx 26rpx 30rpx;
    // background-color: #FFFFFF;
    border-radius: 10rpx;
    box-sizing: border-box;
    box-shadow: 0 0 10rpx #efefef
}

.ul-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 40rpx 54rpx 40rpx 54rpx;
}

.item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}
.item .iconfont{
    font-size: 50rpx;
    // color: #fff;
    width: 70rpx;
    height: 70rpx;
}

.item-img {
    width: 64rpx;
    height: 64rpx;
}

.item-name {
    padding-top: 24rpx;
    font-size: 24rpx;
    color: #666666;
    min-width: 80rpx;
    text-align: center;
}

.btn-feedback {
    background: transparent !important;
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
}


.tui-box {
    width: 100%;
    background: #fff;
    box-shadow: 0 3rpx 20rpx rgba(183, 183, 183, 0.1);
    border-radius: 10rpx;
    overflow: hidden;
}
.tui-cell-header {
    width: 100%;
    height: 80rpx;
    padding: 0 26rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.tui-cell-title {
    font-size: 30rpx;
    line-height: 30rpx;
    font-weight: 600;
    color: #333;
}
.tui-flex-wrap {
    flex-wrap: wrap;
    height: auto;
    padding-bottom: 30rpx;
}
.tui-order-list {
    width: 100%;
    height: 134rpx;
    padding: 0 30rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40rpx 54rpx 40rpx 54rpx;
}

.tui-list-cell {
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

.tui-list-cell::after {
    content: '';
    position: absolute;
    border-bottom: 1px solid #eaeef1;
    -webkit-transform: scaleY(0.5) translateZ(0);
    transform: scaleY(0.5) translateZ(0);
    transform-origin: 0 100%;
    bottom: 0;
    right: 0;
    left: 0;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: rgb(234, 238, 241);
}

