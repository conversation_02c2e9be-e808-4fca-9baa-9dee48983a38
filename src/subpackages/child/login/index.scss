page {
    width: 100%;
    height: 100%;
    font-family: BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Microsoft Yahei", sans-serif;
}

view,
image,
text {
    box-sizing: border-box;
    flex-shrink: 0;
}

.page {
    background-color: #ffffff;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
}

.group {
    overflow-y: auto;
}

.section {
    // margin-left: -166rpx;
    // margin-top: -238rpx;
    padding: 200rpx 0 240rpx;
    background-image: url("https://cdn.51panda.com/WxAppImage/pandaWxApp/login/17213954514997315782.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 100%;
}

.group_2 {
    width: 327.32rpx;
}

.font {
    font-size: 48rpx;
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular;
    line-height: 58rpx;
    color: #383838;
}

.text {
    font-weight: 700;
}

.group_3 {
    margin-top: -164rpx;
}

.section_2 {
    margin-left: 42rpx;
    margin-right: 42rpx;
    padding: 26rpx 20rpx 150rpx;
    background-image: url("https://cdn.51panda.com/17372846228951652320.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.section_2_tab2 {
    margin-left: 42rpx;
    margin-right: 22rpx;
    padding: 26rpx 20rpx 150rpx;
    background-image: url("https://cdn.51panda.com/WxAppImage/pandaWxApp/login/17214018591595596885.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.group_4 {
    padding: 0 68rpx;
}

.group_5 {
    padding-bottom: 20rpx;
}

.font_2 {
    font-size: 32rpx;
    font-family: Yuanti SC, STYuanti-SC-Regular;
    line-height: 33.2rpx;
}

.text_2 {
    color: #3264ed;
}

.text_3 {
    margin-right: 16rpx;
    color: #9e9e9e;
    line-height: 32.3rpx;
}

.text_2_tab2 {
    color: #3264ed;
    line-height: 32.3rpx;
    margin-right: 16rpx;
}

.text_3_tab2 {
    color: #9e9e9e;
}

.section_3 {
    margin-left: 40rpx;
    background-color: #3264ed;
    border-radius: 228rpx;
    width: 58rpx;
    height: 8rpx;
}

.section_3_tab2 {
    margin-right: 48rpx;
    background-color: #3264ed;
    border-radius: 228rpx;
    width: 58rpx;
    height: 8rpx;
}

.group_6 {
    margin-top: 100rpx;
}

.text-wrapper {
    // margin-right: 16rpx;
    padding: 36rpx 0;
    background-color: #f7fafc;
    border-radius: 20rpx;
    border-left: solid 2rpx #3264ed;
    border-right: solid 2rpx #3264ed;
    border-top: solid 2rpx #3264ed;
    border-bottom: solid 2rpx #3264ed;
}

.font_3 {
    font-size: 28rpx;
    font-family: Yuanti SC, STYuanti-SC-Regular;
    line-height: 28.38rpx;
}

.text_4 {
    margin-left: 52rpx;
    color: #202020;
    line-height: 22.92rpx;
}

.section_4 {
    // margin-right: 18rpx;
    padding: 19rpx 18rpx 19rpx 36rpx;
    background-color: #f7fafc;
    border-radius: 20rpx;
}

.text_5 {
    color: #cccccc;
    line-height: 28rpx;
}

.text-wrapper_2 {
    padding: 20rpx 0;
    background-color: #ffffff;
    border-radius: 12rpx;
    width: 228rpx;
    height: 72rpx;
}

.text_6 {
    color: #3264ed;
    line-height: 29.72rpx;
}

.text-wrapper_3 {
    margin-right: 18rpx;
    margin-top: 80rpx;
    padding: 32rpx 0;
    background-color: #3264ed;
    border-radius: 20rpx;
}

.text_7 {
    color: #ffffff;
}

.group_7 {
    margin-top: 350rpx;
}

.group_8 {
    border-radius: 344rpx;
    width: 30rpx;
    height: 30rpx;
    border-left: solid 2rpx #3264ed;
    border-right: solid 2rpx #3264ed;
    border-top: solid 2rpx #3264ed;
    border-bottom: solid 2rpx #3264ed;
}

.group_9 {
    line-height: 22.32rpx;
    height: 22.46rpx;
}

.font_4 {
    font-size: 24rpx;
    font-family: Yuanti SC, STYuanti-SC-Regular;
    line-height: 22.32rpx;
    color: #000000;
}

.font_5 {
    font-size: 24rpx;
    font-family: Yuanti SC, STYuanti-SC-Regular;
    line-height: 22.32rpx;
    color: #3264ed;
}

.text_8 {
    line-height: 21.24rpx;
}

.text_9 {
    line-height: 22.18rpx;
}

.section_5 {
    background-color: #ffffff;
    height: 68rpx;
}

.pos {
    position: absolute;
    left: 0;
    right: 0;
    top: 1132rpx;
}

.group_8_bottom {
    padding: 40rpx 0;
    // border-top: solid 2rpx #11182714;
}

.text_13_bottom {
    color: #9e9e9e;
    line-height: 22.12rpx;
}

.section_4_bottom {
    background-image: linear-gradient(90deg, #cccccc00 0%, #cccccc 100%);
    width: 116rpx;
    height: 2rpx;
}

.section_5_bottom {
    background-image: linear-gradient(90deg, #cccccc 0%, #cccccc00 100%);
    width: 116rpx;
    height: 2rpx;
}

.font_4 {
    font-size: 24rpx;
    font-family: Yuanti SC, STYuanti-SC-Regular;
    line-height: 22.28rpx;
    color: #cccccc;
}

.nut-picker-control {
    height: 80rpx;
}

.nut-popup-bottom.nut-popup-round {
    border-radius: 24rpx 24rpx 0 0;
}

.nut-picker-roller-item-title {
    font-size: 30rpx;
}

.nut-picker-view-panel {
    height: 40vh;
}

.nut-popup {
    font-family: Yuanti SC, STYuanti-SC-Regular;
}
