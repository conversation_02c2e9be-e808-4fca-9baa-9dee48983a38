import { useRef, useState } from "react";
import "./index.scss";
import "@utils/app.scss";
import { Button } from "@nutui/nutui-react-taro";
import MessageApi from "@components/MessageApi/MessageApi";
import request from "@service/request";
import Taro, { Current, useDidShow, useLaunch } from "@tarojs/taro";
import IconFont from "@components/iconfont";
import PdAgreement from "@components/pdAgreemen";
import { Input } from "@tarojs/components";
import { base64ToGuid } from "@utils/util";

// 微信登录的页面 驾校
const App = () => {
    const message: any = useRef();

    const [xm, setXm] = useState("");
    const [sfzmhm, setSfzmhm] = useState("");

    const pdAgreemen = useRef<any>(null);

    const [tenantName, setTenantName] = useState("盼达软件");
    const [tenantId, setTenantId] = useState("");

    useLaunch(() => {
        // 在小程序启动时获取 scene 参数
        console.log("App Launch, options:", Current.router?.params);
        getTenantName(Current.router?.params.scene);
    });

    useDidShow(() => {
        // 当小程序从后台切回前台时获取 scene 参数
        console.log("App Show, options:", Current.router?.params);
        getTenantName(Current.router?.params?.scene);
    });

    const getTenantName = (scene: any) => {
        if (scene && scene.length > 0) {
            message.current.openLoading("正在读取单位信息");
            let id: any = undefined;
            try {
                id = base64ToGuid(scene);
            } catch (e) {
                console.log(e);
                message.current.openDialog("参数错误", e.message);
            }
            if (id && id.length > 0) {
                setTenantId(id);
                message.current.closeLoading();
                request
                    .post<any>("/Wx/Sys/getTenantName", {
                        Id: id,
                    })
                    .then((json) => {
                        if (json && json.success) {
                            setTenantName(json.data.data);
                        } else {
                            message.current.openDialog("读取失败", json.message);
                        }
                        message.current.closeLoading();
                    })
                    .catch(() => {
                        message.current.openDialog("操作失败", "系统故障，稍后重试!");
                    });
            } else {
                message.current.openDialog("操作失败", "请重新扫码进入!");
            }
        } else {
            message.current.openDialog("操作失败", "请重新扫码进入!");
        }
    };

    /**
     * 验证登录
     */
    const logOn = () => {
        if (!tenantId || tenantId.length == 0) {
            message.current.openDialog("操作失败", "请重新扫码进入!");
        } else if (!pdAgreemen.current.state.agreeChecked) {
            message.current.openDialog("协议确认", "请先阅读并确认同意《用户服务协议与隐私政策》", () => {
                pdAgreemen.current.state.showDetail = true;
            });
        } else if (xm.length < 2) {
            message.current.openDialog("验证失败", "请完整输入您的姓名");
        } else if (sfzmhm.length < 6) {
            message.current.openDialog("验证失败", "请输入正确的证件号码");
        }
        // else if (!identityIDCard(sfzmhm).isPass) {
        //     setDialog(getMsg("请输入正确的证件号码", '验证失败'));
        //     setDialogShow(true);
        // }
        else {
            console.log(`tenantId:${tenantId}`);
            message.current.openLoading("验证身份中");
            request
                .post<API.Result<any>>("/Child/WxLogin/logOn", {
                    xm: xm,
                    sfzmhm: sfzmhm,
                    TenantId: tenantId,
                })
                .then((json) => {
                    message.current.closeLoading();
                    if (json && json.success) {
                        Taro.setStorageSync("accessToken", json.data.accessToken);
                        Taro.navigateTo({
                            url: "/subpackages/child/index/index",
                        });
                    } else {
                        json && message.current.openDialog("读取失败", json.message);
                    }
                });
        }
    };
    return (
        <>
            <MessageApi ref={message}></MessageApi>
            <view className="flex-col page">
                <view className="flex-col flex-1 group">
                    <view className="flex-col justify-start items-center self-start relative section">
                        <view
                            className="group_2 justify-start self-stretch"
                            style={{
                                marginLeft: "42rpx",
                            }}
                        >
                            {/* <text className="font text">Hello!</text>
                            <text className="font"></text>
                            <text className="font">欢迎来到全能考场</text> */}
                        </view>
                    </view>
                    <view className="flex-col justify-start self-stretch relative group_3">
                        <view className={`${"flex-col section_2"}`}>
                            <view className="flex-col group_4">
                                <view className="flex-row justify-between self-stretch group_5">
                                    <text className="font_2 text_2">身份验证</text>
                                </view>
                                <view className="self-start section_3"></view>
                            </view>
                            <view className="flex-col group_6">
                                <view
                                    className="flex-row justify-start items-start text-wrapper"
                                    style={{
                                        paddingLeft: "30rpx",
                                        display: "flex",
                                        border: "0",
                                    }}
                                >
                                    <IconFont name="Profile" color="#999" size={38} />
                                    <Input
                                        maxlength={20}
                                        className="font_3 text_4"
                                        placeholder="学生的姓名"
                                        style={{
                                            marginLeft: "20rpx",
                                        }}
                                        onInput={(e) => {
                                            setXm(e.detail.value);
                                        }}
                                        defaultValue={xm}
                                    ></Input>
                                </view>
                                <view
                                    className="mt-24 flex-row justify-start items-start text-wrapper"
                                    style={{
                                        paddingLeft: "30rpx",
                                        display: "flex",
                                        border: "0",
                                    }}
                                >
                                    <IconFont name="Wallet" color="#999" size={38} />
                                    <Input
                                        type="idcard"
                                        maxlength={18}
                                        className="font_3 text_4"
                                        placeholder="学生的证件号码"
                                        style={{
                                            marginLeft: "20rpx",
                                        }}
                                        onInput={(e) => {
                                            setSfzmhm(e.detail.value);
                                        }}
                                        defaultValue={sfzmhm}
                                    ></Input>
                                </view>
                            </view>

                            <Button
                                className="font_3 text_7 flex-col justify-start items-center text-wrapper_3"
                                style={{
                                    border: 0,
                                    marginTop: "40rpx",
                                    borderRadius: "36px",
                                }}
                                onClick={logOn}
                            >
                                立即登录
                            </Button>
                            {/* <view className="flex-col justify-start items-center text-wrapper_3"><text className="font_3 text_7">登录</text></view> */}
                            <view
                                className="flex-row group_7"
                                style={{
                                    // position: 'fixed',
                                    marginTop: "60rpx",
                                }}
                            >
                                <view className="group_9">
                                    <PdAgreement ref={pdAgreemen}></PdAgreement>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view
                className="flex-col group_8_bottom"
                style={{
                    position: "fixed",
                    bottom: "100rpx",
                    width: "100%",
                }}
            >
                <view className="flex-row justify-evenly items-center">
                    <view className="section_4_bottom"></view>
                    <text className="font_4 text_13_bottom">{tenantName}</text>
                    <view className="section_5_bottom"></view>
                </view>
            </view>
            <view className="safe__area"></view>
        </>
    );
};
export default App;
