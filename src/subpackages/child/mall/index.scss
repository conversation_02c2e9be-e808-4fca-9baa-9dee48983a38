.nut-toast__overlay-default {
    --nutui-overlay-bg-color: rgba(6, 6, 6, 0.8);
}

.tui-container {
    padding-bottom: 24rpx;
    background-color: #f1f1f1;
}

.tui-order--list {
    width: 100%;
    padding: 30rpx 30rpx 0;
    box-sizing: border-box;
}

.tui-btn--box {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    padding-top: 18rpx;
    padding-bottom: 18rpx;
    background-color: #fff;
}
x .tui-list--hidden {
    opacity: 0;
    visibility: hidden;
    position: fixed;
    transform: translateX(100%);
}

.tui-item__header {
    width: 100%;
    font-size: 24rpx;
    display: flex;
    justify-content: space-between;
    padding-bottom: 30rpx;
}

.tui-primary__color {
    color: var(--tui-primary, #40ae36);
}

.tui-order__item {
    width: 100%;
    flex: 1;
    background: #fff;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
}

.tui-order__inner {
    width: 100%;
    display: flex;
}

.tui-order__item-radius {
    border-radius: 20rpx;
    overflow: hidden;
}

.tui-goods__img {
    width: 148rpx;
    height: 148rpx;
    margin-right: 30rpx;
    display: block;
    border-radius: 16rpx;
}

.tui-goods__content {
    flex: 1;
    position: relative;
}

.tui-goods__title {
    width: 100%;
    font-size: 26rpx;
    font-weight: 500;
    color: #333;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    margin-bottom: 12rpx;
}

.tui-goods__descr {
    font-size: 27rpx;
    color: #999999;
    zoom: 0.8;
    margin-bottom: 2rpx;
}

.tui-price__sign {
    font-size: 24rpx;
    line-height: 24rpx;
}

.tui-price__num {
    font-size: 32rpx;
    line-height: 32rpx;
    font-weight: 500;
}

.tui-price__box {
    width: 202rpx;
    padding-left: 16rpx;
    flex-shrink: 0;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tui-list-cell {
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

.tui-radius {
    border-radius: 6rpx;
    overflow: hidden;
}

.tui-cell-hover {
    background-color: #f1f1f1 !important;
}

.tui-list-cell::after {
    content: "";
    position: absolute;
    border-bottom: 2rpx solid #eaeef1;
    -webkit-transform: scaleY(0.5) translateZ(0);
    transform: scaleY(0.5) translateZ(0);
    transform-origin: 0 100%;
    bottom: 0;
    right: 0;
    left: 0;
}

.tui-line-left::after {
    left: 30rpx !important;
}

.tui-line-right::after {
    right: 30rpx !important;
}

.tui-cell-unlined::after {
    border-bottom: 0 !important;
}

.tui-cell-arrow::before {
    content: " ";
    height: 20rpx;
    width: 20rpx;
    border-width: 4rpx 4rpx 0 0;
    border-color: #c0c0c0;
    border-style: solid;
    -webkit-transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
    transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
    position: absolute;
    top: 50%;
    margin-top: -12rpx;
    right: 60rpx;
}

.tui-arrow-right::before {
    right: 0 !important;
}

.tui-arrow-gray::before {
    border-color: #666666 !important;
}

.tui-arrow-white::before {
    border-color: #ffffff !important;
}

.tui-arrow-warning::before {
    border-color: #ff7900 !important;
}

.tui-arrow-success::before {
    border-color: #19be6b !important;
}

.tui-arrow-danger::before {
    border-color: #eb0909 !important;
}
.tui-card__wrap {
    padding-bottom: 30rpx;
}

.nut-cell-group__title {
    margin-top: 20px;
}

.tui-container {
    padding-bottom: 130rpx;
}

.bg-img image {
    position: absolute;
    width: 100%;
    height: 440rpx;
    z-index: -1;
    background-image: url("https://cdn.51panda.com/WxAppImage/mine_bg_3x.png");
}

.nut-input {
    border-radius: 6px;
    border-bottom: 0px solid #eaf0fb;
}

.tui-bg {
    width: 100%;
    height: 200rpx;
    padding-top: 0rpx;
    // background: linear-gradient(20deg, #E41F19, #F34B0B);
    background-color: #e41f19;
    border-bottom-left-radius: 42rpx;
}

.tui-content {
    padding: 0 35rpx;
    box-sizing: border-box;
    margin-top: -160rpx;
}

.tui-form {
    background: #fff;
    // height: 500rpx;
    box-shadow: 0 10rpx 14rpx 0 rgba(0, 0, 0, 0.08);
    border-radius: 10rpx;
    // margin-top: -160rpx;
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 0 40rpx 40rpx 40rpx;
    width: auto;
}

// .tui-icon {
//     width: 100rpx;
//     height: 100rpx;
//     display: block;
//     margin-bottom: 60rpx;
// }

// .tui-title {
//     font-size: 42rpx;
//     line-height: 42rpx;
//     padding-top: 28rpx;
// }

// .tui-sub-title {
//     color: #666666;
//     font-size: 28rpx;
//     line-height: 28rpx;
//     padding-top: 20rpx;
// }

.tui-btn-box {
    width: 580rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 88rpx;
}

// .tui-tips {
//     font-size: 26rpx;
//     padding: 48rpx 0rpx;
//     box-sizing: border-box;
//     text-align: justify;
//     line-height: 48rpx;
// }

.tui-grey {
    color: #555;
    padding-bottom: 8rpx;
}

.tui-light-grey {
    color: #888;
    line-height: 40rpx;
}

.tui-danger-hover {
    background: #c80808 !important;
    color: #e5e5e5 !important;
}

.tui-danger-outline {
    color: #eb0909 !important;
    background: transparent;
}

.tui-danger-outline::after {
    border: 1px solid #eb0909 !important;
}

.tui-btn-danger {
    background: #eb0909 !important;
    color: #fff;
}

.tui-shadow-danger {
    box-shadow: 0 10rpx 14rpx 0 rgba(235, 9, 9, 0.2);
}

.tui-btn {
    width: 100%;
    position: relative;
    border: 0 !important;
    border-radius: 6rpx;
    padding-left: 0;
    padding-right: 0;
    overflow: visible;
}

/*圆角 */

.tui-fillet {
    border-radius: 50rpx;
}

.tui-btn-white.tui-fillet::after {
    border-radius: 98rpx;
}

.tui-outline-fillet::after {
    border-radius: 98rpx;
}

.tui-invoice__box {
    background-color: #ffffff;
    margin-top: 20rpx;
    border-radius: 20rpx;
    overflow: hidden;
}

.tui-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.tui-notice {
    font-size: 24rpx;
    font-weight: 400;
    color: #999;
}

.tui-bold {
    font-weight: bold;
}

.tui-attr__box {
    padding: 4rpx 0 12rpx 0;
}

.tui-pbtm__0 {
    padding-bottom: 0;
}

.tui-attr-item {
    max-width: 100%;
    min-width: 180rpx;
    height: 64rpx;
    display: -webkit-inline-flex;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #f7f7f7;
    border: 1rpx solid #f7f7f7;
    padding: 0 26rpx;
    box-sizing: border-box;
    border-radius: 12rpx;
    margin-right: 32rpx;
    font-size: 26rpx;
}

.tui-attr-active {
    background-color: #fcedea;
    border-color: #e41f19;
    color: #e41f19;
    font-weight: bold;
}

.tui-tips {
    color: #999;
    font-size: 24rpx;
    font-weight: 400;
    padding: 10rpx 0;
}

.tui-input__item {
    width: 100%;
    display: flex;
    align-items: center;
    font-size: 28rpx;
    padding-top: 40rpx;
}

.tui-input__title {
    width: 156rpx;
    flex-shrink: 0;
}

.tui-input__item input {
    font-size: 28rpx;
    flex: 1;
}

.tui-placeholder {
    color: #bfbfbf;
}

.tui-more__optional {
    font-size: 24rpx;
    display: flex;
    align-items: center;
    color: #999;
}

.tui-between {
    justify-content: space-between;
}

.tui-btn__box {
    padding: 60rpx 30rpx 80rpx;
}

.tui-modal__title {
    text-align: center;
    font-weight: bold;
    padding-bottom: 8rpx;
}

.tui-modal__p {
    font-size: 26rpx;
    color: #888;
    padding-top: 20rpx;
}

.tui-modal__btn {
    width: 100%;
    padding: 60rpx 0 20rpx;
    display: flex;
    justify-content: center;
}

.tui-list-cell {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 30rpx 0;
}

.tui-radius {
    border-radius: 6rpx;
    overflow: hidden;
}

.tui-cell-hover {
    background-color: #f1f1f1 !important;
}

.tui-list-cell::after {
    content: "";
    position: absolute;
    border-bottom: 2rpx solid #eaeef1;
    -webkit-transform: scaleY(0.5) translateZ(0);
    transform: scaleY(0.5) translateZ(0);
    transform-origin: 0 100%;
    bottom: 0;
    right: 0;
    left: 0;
}

.tui-line-left::after {
    left: 30rpx !important;
}

.tui-line-right::after {
    right: 30rpx !important;
}

.tui-cell-unlined::after {
    border-bottom: 0 !important;
}

.tui-cell-arrow::before {
    content: " ";
    height: 20rpx;
    width: 20rpx;
    border-width: 4rpx 4rpx 0 0;
    border-color: #c0c0c0;
    border-style: solid;
    -webkit-transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
    transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
    position: absolute;
    top: 50%;
    margin-top: -12rpx;
    right: 60rpx;
}

.tui-arrow-right::before {
    right: 0 !important;
}

.tui-arrow-gray::before {
    border-color: #666666 !important;
}

.tui-arrow-white::before {
    border-color: #ffffff !important;
}

.tui-arrow-warning::before {
    border-color: #ff7900 !important;
}

.tui-arrow-success::before {
    border-color: #19be6b !important;
}

.tui-arrow-danger::before {
    border-color: #eb0909 !important;
}

.tui-goods__bar {
    position: fixed;
    z-index: 10;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
}

.tui-goods__bar::before {
    content: "";
    width: 100%;
    border-top: 2rpx solid #eaeef1;
    position: absolute;
    top: 0;
    left: 0;
    transform: scaleY(0.5);
    transform-origin: 0 0;
}

.tui-goods__bar-inner {
    width: 100%;
    height: 100rpx;
    padding: 0 30rpx;
    box-sizing: border-box;
}

.tui--flex {
    display: flex;
    align-items: center;
}

.tui-flex--between {
    justify-content: space-between;
}

.tui-flex--end {
    justify-content: flex-end;
}

.tui-check--all {
    font-size: 26rpx;
    padding-left: 16rpx;
}

.tui-price--box {
    padding-right: 30rpx;
}

.tui-outer__box {
    width: 100%;
    padding-top: 0rpx;
    padding-left: 0rpx;
    padding-right: 0rpx;
    box-sizing: border-box;
}

.tui-bp__tit {
    width: 100%;
    padding: 30rpx;
    box-sizing: border-box;
    position: relative;
    font-weight: 500;
}

.tui-icon--close {
    position: absolute;
    right: 30rpx;
    top: 30rpx;
    // padding: 8rpx;
}

.tui-bp__content {
    width: 100%;
    padding: 24rpx 50rpx 30rpx;
    box-sizing: border-box;
}

.tui-bp--top {
    padding-top: 40rpx;
}

.ti-btn--box {
    width: 100%;
    padding-top: 100rpx;
    box-sizing: border-box;
}

.tui-bp__price-box {
    width: 100%;
    align-items: flex-end;
    // padding-bottom: 80rpx;
}

.tui-bp__price {
    font-size: 70rpx;
    line-height: 70rpx;
}

/* .tui-flex__between{
    width: 100%;
    font-size: 30rpx;
    padding-top:56rpx;
  } */
.tui-bp__name {
    color: #999999;
}

.tui-bottom-popup {
    width: 100%;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    transform: translate3d(0, 100%, 0);
    transform-origin: center;
    transition: all 0.3s ease-in-out;
    min-height: 20rpx;
}

.tui-popup-radius {
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
    padding-bottom: env(safe-area-inset-bottom);
    overflow: hidden;
}

.tui-popup-show {
    /* transform: translate3d(0, 0, 0); */
    opacity: 1;
}

.tui-popup-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    transition: all 0.3s ease-in-out;
    opacity: 0;
    visibility: hidden;
}

.tui-mask-show {
    opacity: 1;
    visibility: visible;
}

.nut-checkbox__label {
    width: auto;
}

// .nut-tabs__content__wrap{
//     height: 0;
// }

.nut-tabpane {
    padding: 0;
}
