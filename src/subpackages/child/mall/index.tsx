import Taro, { useReady } from "@tarojs/taro";
import { useState } from "react";
import TopNavBar from "@components/topNavBar";
import { Button, Dialog, Popup, Price, Toast } from "@nutui/nutui-react-taro";
import "./index.scss";
import "@utils/app.scss";

import { getLoading, getMsg } from "@utils/util";
import { login } from "@utils/login";
import request from "@service/request";
import { Image } from "@tarojs/components";
// import {Icon} from '@nutui/nutui-react';
import { Close } from "@nutui/icons-react-taro";

const App = () => {
    /**
     * Toast 需要的 State
     */
    const [toastShow, setToastShow] = useState(false);
    const [toast, setToast] = useState<API.pdToast>(Object);

    /**
     * Dialog 需要的 State
     */
    const [dialogShow, setDialogShow] = useState(false);
    const [dialog, setDialog] = useState<API.pdDialog>(Object);

    /**
     * 商品列表
     */
    const [itemList, setItemList] = useState<any[]>([]);
    /**
     * 用户登录的 信息
     */
    // const [userInfo, setUserInfo] = useState<API.UserInfo>(Object);

    /**
     * 支付成功 等待的过程
     */
    const [payLoading, setPayLoading] = useState(false);

    const [showPayPopup, setShowPayPopup] = useState(false);
    const [ItemId, setItemId] = useState("");
    const [ItemName, setItemName] = useState("");
    // const [OrderShortId, setOrderShortId] = useState('');
    const [PayMoney, setPayMoney] = useState(0);

    const [StudentId, setStudentId] = useState("");
    const [AccountId, setAccountId] = useState("");
    const [OutTable, setOutTable] = useState("");
    /**
     * 当前步骤 Index
     */
    const [StepIndex, setStepIndex] = useState(0);
    /**
     * 页面 ready 方法
     */
    useReady(() => {
        login().then(() => {
            // setUserInfo(userInfo);

            // 需要登录的时候 再用  这个页面是全局的  会和部分模块冲突 比如 湖南驾协 长沙驾协
            // wxLogOn().then(() => {
            // });
            GetCostTypeOnlineList();
        });
    });

    /**
     * 获得 我的账单的列表
     */
    const GetCostTypeOnlineList = () => {
        request.post<API.Result<any>>("/Child/CostType/GetCostTypeOnlineList", {}).then((json) => {
            if (json && json.success) {
                setItemList(json.data);
            } else {
                json && setDialog(getMsg(json.message, "读取失败"));
                json && setDialogShow(true);
            }
        });
    };

    /**
     * 购买商品
     * @param Id
     */
    const buyItem = (Id: string) => {
        setToast(getLoading("读取数据", () => {}));
        setToastShow(true);
        request
            .post<API.Result<any>>("/Child/CostType/GetCostTypePayInfo", {
                Id: Id,
            })
            .then((json) => {
                setToastShow(false);
                if (json && json.success) {
                    setItemId(json.data.ItemId);
                    // setOrderShortId(json.data.ShortId);
                    setPayMoney(json.data.PayMoney);
                    setOutTable(json.data.OutTable);
                    setAccountId(json.data.AccountId);
                    setStudentId(json.data.StudentId);
                    setItemName(json.data.ItemName);
                    setShowPayPopup(true);
                } else {
                    json && setDialog(getMsg(json.message, "读取失败"));
                    json && setDialogShow(true);
                }
            });
    };

    const pay = () => {
        setToast(getLoading("正在生成支付数据", () => {}));
        setToastShow(true);
        setShowPayPopup(false);

        request
            .put<
                API.Result<{
                    OrderId: string;
                    SignData: {
                        timeStamp: string;
                        package: string;
                        paySign: string;
                        appId: string;
                        signType: string;
                        nonceStr: string;
                    };
                }>
            >("/Pay/WxPay/createOrder", {
                AccountId: AccountId,
                PayMoney: PayMoney,
                OutTable: OutTable,
                OutId: ItemId,
                UserId: StudentId,
                StudentId: StudentId,
            })
            .then((json) => {
                setToastShow(false);
                if (json && json.success) {
                    var payInfo = json.data.SignData;
                    setPayLoading(true);
                    Taro.requestPayment({
                        timeStamp: payInfo.timeStamp,
                        nonceStr: payInfo.nonceStr,
                        package: payInfo.package,
                        signType: "MD5",
                        paySign: payInfo.paySign,
                        success: function () {
                            GetResult(json.data.OrderId);
                        },
                        fail: function (res) {
                            setPayLoading(false);
                            setShowPayPopup(false);
                            if (res.errMsg === "requestPayment:fail cancel") {
                                setDialog(getMsg("当前交易被取消,请重新发起支付", "交易取消"));
                                setDialogShow(true);
                            } else {
                                setDialog(getMsg(res.errMsg));
                                setDialogShow(true);
                            }
                        },
                    });
                } else {
                    setPayLoading(false);
                    setShowPayPopup(false);
                    json && setDialog(getMsg(json.message, "支付配置出错"));
                    json && setDialogShow(true);
                }
            });
    };

    /**
     * 获取订单的支付状态
     * @param OrderId
     */
    const GetResult = (OrderId: string) => {
        setToast(getLoading("等待支付结果", () => {}));
        setToastShow(true);

        request
            .post<API.Result<{}>>("/Pay/WxPay/GetStatus", {
                Id: OrderId,
            })
            .then((json) => {
                if (json && json.success) {
                    setToastShow(false);
                    setPayLoading(false);
                    setShowPayPopup(false);
                    setStepIndex(1);
                } else {
                    setTimeout(GetResult, 1000, OrderId);
                }
            });
    };

    return (
        <>
            {StepIndex == 0 && (
                <>
                    <TopNavBar
                        title="线上商城"
                        leftClick={() => {
                            Taro.redirectTo({
                                url: "/subpackages/child/index/index",
                            });
                        }}
                        homeClick={() => {
                            Taro.redirectTo({
                                url: "/subpackages/child/index/index",
                            });
                        }}
                    ></TopNavBar>

                    <view
                        className="tui-container"
                        style={{
                            backgroundColor: "#f1f1f1",
                            padding: "0 0 0 0",
                            height: "auto",
                            minHeight: "100vh",
                            paddingBottom: "calc(env(safe-area-inset-bottom) )",
                        }}
                    >
                        <view className="tui-order--list">
                            {itemList?.map((item) => (
                                <>
                                    <view className="tui-card__wrap">
                                        <view
                                            className="tui-order__item tui-list-class tui-list-cell tui-line-left tui-line-right"
                                            style={{
                                                backgroundColor: "#fff",
                                                fontSize: "14px",
                                                color: "#333",
                                                padding: "13px 15px",
                                            }}
                                            onClick={() => {
                                                buyItem(item.Id);
                                            }}
                                        >
                                            <view className="tui-order__inner">
                                                {/* <Image className='tui-goods__img' src=""></Image> */}
                                                <view className="tui-goods__content">
                                                    <view className="tui-goods__title">商品名称：{item.Name}</view>
                                                    {/* <view className='tui-goods__descr'>商户单号：{item.ChannelOrderSn}</view>
                                                    <view className='tui-goods__descr'>交易单号：{item.InsOrderSn}</view> */}
                                                    <view className="tui-goods__descr">备注：{item.Remark === "" ? " --- " : item.Remark}</view>
                                                </view>
                                                <view className="tui-price__box">
                                                    <Price price={item.PayMoney} size="large" thousands />
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                </>
                            ))}
                            <view className="tui-divider" style={{ height: "100rpx" }}>
                                <view
                                    className="tui-divider-line"
                                    style={{
                                        width: "50%",
                                        background: "#e5e5e5",
                                    }}
                                ></view>
                                <view
                                    style={{
                                        color: "#999",
                                        fontSize: "24rpx",
                                        lineHeight: "24rpx",
                                        backgroundColor: "#f1f1f1",
                                        fontWeight: "normal",
                                    }}
                                    className="tui-divider-text"
                                >
                                    这是我的底线
                                </view>
                            </view>
                        </view>
                    </view>

                    <Popup
                        visible={showPayPopup}
                        round
                        position="bottom"
                        zIndex={1010}
                        style={{
                            zIndex: 1010,
                        }}
                        onClose={() => {
                            setShowPayPopup(false);
                        }}
                    >
                        <view className="safe__area">
                            <view className="tui-bp__tit tui-flex__center">
                                <text>确认付款</text>
                                <view
                                    className="tui-icon--close"
                                    onClick={() => {
                                        setShowPayPopup(false);
                                    }}
                                >
                                    <Close size={14} />
                                </view>
                            </view>
                            <view className="tui-bp__content">
                                <view
                                    className="tui-bp__price-box tui-flex__center"
                                    style={{
                                        paddingBottom: "10rpx",
                                    }}
                                >
                                    <Price price={PayMoney} size="large" thousands />
                                </view>
                                {/* <view className='tui-list-class tui-list-cell tui-arrow-right' style={{
                                    backgroundColor: '#fff',
                                    fontSize: '14px',
                                    color: '#333',
                                    padding: '15px 0'
                                }}>
                                    <view className='tui-flex__between'>
                                        <text className='tui-bp__name'>
                                            订单编号
                                        </text>
                                        <text>{OrderShortId}</text>
                                    </view>
                                </view> */}
                                <view
                                    className="tui-list-class tui-list-cell tui-arrow-right"
                                    style={{
                                        backgroundColor: "#fff",
                                        fontSize: "14px",
                                        color: "#333",
                                        padding: "15px 0",
                                    }}
                                >
                                    <view className="tui-flex__between">
                                        <text className="tui-bp__name">商品名称</text>
                                        <text>{ItemName}</text>
                                    </view>
                                </view>
                                <view
                                    className="tui-list-class tui-list-cell tui-arrow-right"
                                    style={{
                                        backgroundColor: "#fff",
                                        fontSize: "14px",
                                        color: "#333",
                                        padding: "15px 0",
                                    }}
                                >
                                    <view className="tui-flex__between">
                                        <text className="tui-bp__name">付款方式</text>
                                        <text>微信支付</text>
                                    </view>
                                </view>
                                <view className="ti-btn--box">
                                    {!payLoading && (
                                        <Button size="large" type="primary" onClick={pay}>
                                            立即付款
                                        </Button>
                                    )}
                                    {payLoading && (
                                        <Button size="large" type="primary" disabled={true} loading={true}>
                                            等待付款结果
                                        </Button>
                                    )}
                                </view>
                            </view>
                        </view>
                    </Popup>
                </>
            )}
            {StepIndex == 1 && (
                <>
                    <TopNavBar
                        title="支付完成"
                        leftClick={() => {
                            Taro.reLaunch({
                                url: "/pages/index/index",
                            });
                        }}
                        homeClick={() => {
                            Taro.reLaunch({
                                url: "/pages/index/index",
                            });
                        }}
                        BgColor="#E41F19"
                        FontColor="#fff"
                        hideLeft={false}
                    ></TopNavBar>
                    <view className="success-container">
                        <view className="container">
                            <view className="tui-bg"></view>
                            <view className="tui-content">
                                <view className="tui-success-form">
                                    <Image src="https://cdn.51panda.com/WxAppImage/pay/img_recharge_success.png" className="tui-icon" mode="widthFix"></Image>
                                    <view className="tui-title">订单已支付成功</view>
                                    <view className="tui-sub-title">非常感谢完成订单的支付</view>
                                    <view className="tui-btn-box">
                                        <Button
                                            className="tui-btn-class tui-btn tui-danger-outline tui-outline-fillet"
                                            style={{
                                                width: "240rpx",
                                                height: "70rpx",
                                                lineHeight: "70rpx",
                                                fontSize: "28rpx",
                                                margin: 0,
                                            }}
                                            onClick={() => {
                                                Taro.reLaunch({
                                                    url: "/pages/index/index",
                                                });
                                            }}
                                        >
                                            返回首页
                                        </Button>
                                        <Button
                                            className="tui-btn-class tui-btn tui-btn-danger tui-fillet tui-shadow-danger"
                                            style={{
                                                width: "240rpx",
                                                height: "70rpx",
                                                lineHeight: "70rpx",
                                                fontSize: "28rpx",
                                                margin: 0,
                                            }}
                                            onClick={() => {
                                                Taro.reLaunch({
                                                    url: "/pages/index/index",
                                                });
                                            }}
                                        >
                                            订单详情
                                        </Button>
                                    </view>
                                </view>
                            </view>
                            <view className="tui-tips">
                                <view className="tui-grey">温馨提示:</view>
                                <view className="tui-light-grey">
                                    付款成功后，我们不会以付款异常、卡单、系统升级为由联系您。请勿泄露银行卡号、手机验证码，否则会造成钱款损失！谨防电话诈骗！
                                </view>
                            </view>
                        </view>
                    </view>
                </>
            )}
            {/* Toast 和 Dialog 的通用相关代码 开始 */}
            {toastShow && !dialogShow && (
                <Toast
                    msg={toast.msg}
                    visible={toastShow}
                    type={toast.type}
                    onClose={() => {
                        setToastShow(false);
                        toast.fn();
                    }}
                    // cover={toast.cover}
                    // coverColor="rgba(6, 6, 6, 0.8)"
                    duration={toast.duration}
                    icon=<toast.icon />
                    iconSize="20"
                />
            )}
            {dialogShow && (
                <Dialog
                    closeOnOverlayClick={false}
                    title={dialog.title}
                    confirmText={dialog.okText}
                    hideCancelButton={dialog.noCancelBtn}
                    cancelText={dialog.cancelText}
                    // textAlign={dialog.textAlign}
                    visible={dialogShow}
                    lockScroll
                    footerDirection="vertical"
                    onConfirm={() => {
                        dialog.ok();
                        setDialogShow(false);
                    }}
                    onCancel={() => {
                        dialog.cancel();
                        setDialogShow(false);
                    }}
                >
                    <view
                        style={{
                            lineHeight: "40rpx",
                        }}
                    >
                        {dialog.msg}
                    </view>
                </Dialog>
            )}
            {/* Toast 和 Dialog 的通用相关代码 结束 */}
        </>
    );
};
export default App;
