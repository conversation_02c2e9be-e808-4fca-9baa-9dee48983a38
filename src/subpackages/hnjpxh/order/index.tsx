import Taro, { useReady } from "@tarojs/taro";
import { useState } from "react";
import TopNavBar from "@components/topNavBar";
import { Button, Dialog, Price, PullToRefresh, Toast } from "@nutui/nutui-react-taro";
import "./index.scss";
import "@utils/app.scss";

import { getMsg } from "@utils/util";
import { login } from "@utils/login";
import request from "@service/request";

const App = () => {
    /**
     * Toast 需要的 State
     */
    const [toastShow, setToastShow] = useState(false);
    const [toast, setToast] = useState<API.pdToast>(Object);

    /**
     * Dialog 需要的 State
     */
    const [dialogShow, setDialogShow] = useState(false);
    const [dialog, setDialog] = useState<API.pdDialog>(Object);

    /**
     * 用户登录的 信息
     */
    const [userInfo, setUserInfo] = useState<API.UserInfo>(Object);

    const [payList, setPayList] = useState<any[]>();

    /**
     * 页面 ready 方法
     */
    useReady(() => {
        login().then((userInfo: API.UserInfo) => {
            setUserInfo(userInfo);
            GetMyPayList();
        });
    });

    /**
     * 下载全部的支付的订单
     */
    const GetMyPayList = () => {
        request.post<API.Result<any>>("/HNJiaXie/HnjxPay/GetMyPayList", {}).then((json) => {
            if (json && json.success) {
                setPayList(json.data.data);
            } else {
                json && setDialog(getMsg(json.message, "操作失败", () => {}));
                json && setDialogShow(true);
            }
        });
    };

    return (
        <>
            <TopNavBar title="我的订单"></TopNavBar>

            <view className="tui-order--list">
                <PullToRefresh>
                    {payList?.map((item) => (
                        <>
                            <view className="tui-card__wrap">
                                <view
                                    className="tui-list-class tui-list-cell tui-arrow-right tui-line-left tui-line-right"
                                    style={{
                                        backgroundColor: "#fff",
                                        fontSize: "14px",
                                        color: "#333",
                                        padding: "13px 15px",
                                    }}
                                >
                                    <view className="tui-flex__between">
                                        <view
                                            className="tui-text__wrap tui-text__inline tui-text__left"
                                            style={{
                                                textAlign: "left",
                                                padding: 0,
                                            }}
                                        >
                                            <view
                                                className="tui-text__content"
                                                style={{
                                                    color: "#999",
                                                    fontSize: "12px",
                                                    lineHeight: "auto",
                                                    textAlign: "left",
                                                    textDecoration: "none",
                                                    fontWeight: "400",
                                                }}
                                            >
                                                {item.CreatorTime}
                                            </view>
                                        </view>
                                        <view
                                            className="tui-text__wrap tui-text__inline tui-text__left"
                                            style={{
                                                textAlign: "left",
                                                padding: "0",
                                            }}
                                        >
                                            <view
                                                className="tui-text__content"
                                                style={{
                                                    color: "#50a14f",
                                                    fontSize: "12px",
                                                    lineHeight: "auto",
                                                    textAlign: "left",
                                                    textDecoration: "none",
                                                    fontWeight: "400",
                                                }}
                                            >
                                                付款成功
                                            </view>
                                        </view>
                                    </view>
                                </view>
                                <view
                                    className="tui-order__item tui-list-class tui-list-cell tui-line-left tui-line-right"
                                    style={{
                                        backgroundColor: "#fff",
                                        fontSize: "14px",
                                        color: "#333",
                                        padding: "13px 15px",
                                    }}
                                >
                                    <view className="tui-item__header">
                                        <text>湖南驾协</text>
                                        <text className="tui-primary__color"></text>
                                    </view>
                                    <view className="tui-order__inner">
                                        {/* <Image className='tui-goods__img' src=""></Image> */}
                                        <view className="tui-goods__content">
                                            <view className="tui-goods__title">{item.CostTypeName}</view>
                                            <view className="tui-goods__descr">商户单号：{item.ChannelOrderSn}</view>
                                            <view className="tui-goods__descr">交易单号：{item.InsOrderSn}</view>
                                            {/* <view className='tui-goods__descr'>交易备注：{item.Remark === '' ? ' --- ' : item.Remark}</view> */}
                                        </view>
                                        <view className="tui-price__box">
                                            <Price price={item.PayMoney} size="large" thousands />
                                        </view>
                                    </view>
                                </view>
                                <view className="tui-btn--box tui-spacing">
                                    {item.SendCardTrackingNumber === "" && (
                                        <Button
                                            type="primary"
                                            disabled
                                            plain
                                            style={{
                                                marginRight: "10px",
                                            }}
                                        >
                                            查看物流
                                        </Button>
                                    )}
                                    {item.SendCardTrackingNumber !== "" && (
                                        <Button
                                            type="primary"
                                            onClick={() => {
                                                setDialog(getMsg(item.SendCardTrackingNumber, "快递单号", () => {}));
                                                setDialogShow(true);
                                            }}
                                            style={{
                                                marginRight: "10px",
                                            }}
                                        >
                                            查看物流
                                        </Button>
                                    )}
                                    {item.TaxFile === "" && (
                                        <Button type="info" disabled plain>
                                            查看发票
                                        </Button>
                                    )}
                                    {item.TaxFile !== "" && (
                                        <Button
                                            type="info"
                                            onClick={() => {
                                                Taro.downloadFile({
                                                    url: item.TaxFile,
                                                });
                                            }}
                                        >
                                            下载发票
                                        </Button>
                                    )}
                                </view>
                            </view>
                        </>
                    ))}
                </PullToRefresh>
            </view>
            {/* Toast 和 Dialog 的通用相关代码 开始 */}
            {toastShow && !dialogShow && (
                <Toast
                    msg={toast.msg}
                    visible={toastShow}
                    type={toast.type}
                    onClose={() => {
                        setToastShow(false);
                        toast.fn();
                    }}
                    // cover={toast.cover}
                    // coverColor="rgba(6, 6, 6, 0.8)"
                    duration={toast.duration}
                    icon=<toast.icon />
                    iconSize="20"
                />
            )}
            {dialogShow && (
                <Dialog
                    closeOnOverlayClick={false}
                    title={dialog.title}
                    confirmText={dialog.okText}
                    hideCancelButton={dialog.noCancelBtn}
                    cancelText={dialog.cancelText}
                    // textAlign={dialog.textAlign}
                    visible={dialogShow}
                    lockScroll
                    footerDirection="vertical"
                    onConfirm={() => {
                        dialog.ok();
                        setDialogShow(false);
                    }}
                    onCancel={() => {
                        dialog.cancel();
                        setDialogShow(false);
                    }}
                >
                    <view
                        style={{
                            lineHeight: "40rpx",
                        }}
                    >
                        {dialog.msg}
                    </view>
                </Dialog>
            )}
            {/* Toast 和 Dialog 的通用相关代码 结束 */}
        </>
    );
};
export default App;
