.tui-container {
    padding-bottom: 24rpx;
}

.tui-order--list {
    width: 100%;
    padding: 30rpx 30rpx 0;
    box-sizing: border-box;
}

.tui-btn--box {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    padding-top: 18rpx;
    padding-bottom: 18rpx;
    background-color: #fff;
}
x .tui-list--hidden {
    opacity: 0;
    visibility: hidden;
    position: fixed;
    transform: translateX(100%);
}

.tui-item__header {
    width: 100%;
    font-size: 24rpx;
    display: flex;
    justify-content: space-between;
    padding-bottom: 30rpx;
}

.tui-primary__color {
    color: var(--tui-primary, #40ae36);
}

.tui-order__item {
    width: 100%;
    flex: 1;
    background: #fff;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
}

.tui-order__inner {
    width: 100%;
    display: flex;
}

.tui-order__item-radius {
    border-radius: 20rpx;
    overflow: hidden;
}

.tui-goods__img {
    width: 148rpx;
    height: 148rpx;
    margin-right: 30rpx;
    display: block;
    border-radius: 16rpx;
}

.tui-goods__content {
    flex: 1;
    position: relative;
}

.tui-goods__title {
    width: 100%;
    font-size: 26rpx;
    font-weight: 500;
    color: #333;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    margin-bottom: 12rpx;
}

.tui-goods__descr {
    font-size: 27rpx;
    color: #999999;
    zoom: 0.8;
    margin-bottom: 2rpx;
}

.tui-price__sign {
    font-size: 24rpx;
    line-height: 24rpx;
}

.tui-price__num {
    font-size: 32rpx;
    line-height: 32rpx;
    font-weight: 500;
}

.tui-price__box {
    width: 142rpx;
    padding-left: 16rpx;
    flex-shrink: 0;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tui-list-cell {
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

.tui-radius {
    border-radius: 6rpx;
    overflow: hidden;
}

.tui-cell-hover {
    background-color: #f1f1f1 !important;
}

.tui-list-cell::after {
    content: "";
    position: absolute;
    border-bottom: 1px solid #eaeef1;
    -webkit-transform: scaleY(0.5) translateZ(0);
    transform: scaleY(0.5) translateZ(0);
    transform-origin: 0 100%;
    bottom: 0;
    right: 0;
    left: 0;
    pointer-events: none;
}

.tui-line-left::after {
    left: 30rpx !important;
}

.tui-line-right::after {
    right: 30rpx !important;
}

.tui-cell-unlined::after {
    border-bottom: 0 !important;
}

.tui-cell-arrow::before {
    content: " ";
    height: 10px;
    width: 10px;
    border-width: 2px 2px 0 0;
    border-color: #c0c0c0;
    border-style: solid;
    -webkit-transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
    transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
    position: absolute;
    top: 50%;
    margin-top: -6px;
    right: 30rpx;
    pointer-events: none;
}

.tui-arrow-right::before {
    right: 0 !important;
}

.tui-arrow-gray::before {
    border-color: #666666 !important;
}

.tui-arrow-white::before {
    border-color: #ffffff !important;
}

.tui-arrow-warning::before {
    border-color: #ff7900 !important;
}

.tui-arrow-success::before {
    border-color: #19be6b !important;
}

.tui-arrow-danger::before {
    border-color: #eb0909 !important;
}
