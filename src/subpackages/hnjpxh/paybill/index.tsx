import Taro, { useReady } from "@tarojs/taro";
import { useState, useEffect } from "react";
import { Button, Cell, CellGroup, Popup, Price, Loading } from "@nutui/nutui-react-taro";
import "./index.scss";
import "@utils/app.scss";

import { QValue } from "@utils/util";
import { login } from "@utils/login";
import request from "@service/request";

import { Text, View } from "@tarojs/components";
// import {Icon} from '@nutui/nutui-react';
import { Close } from "@nutui/icons-react-taro";
import Layout from "@/components/Layout";
import { message } from "@/components/MessageApi/MessageApiSingleton";

const App = () => {
    /**
     * 用户登录的 信息
     */
    // const [userInfo, setUserInfo] = useState<API.UserInfo>(Object);

    /**
     * 订单的 列表
     */
    const [payList, setPayList] = useState<any[]>([]);

    /**
     * 付款的金额
     */
    const [payMoney, setPayMoney] = useState(0);

    /**
     * 支付成功 等待的过程
     */
    const [payLoading, setPayLoading] = useState(false);

    const [showPayPopup, setShowPayPopup] = useState(false);
    const [OrderShortId, setOrderShortId] = useState("");

    const [accountId, setAccountId] = useState("");
    const [outTable, setOutTable] = useState("");
    const [Id, setId] = useState("");

    // Add a loading state for initial page load
    const [isPageLoading, setIsPageLoading] = useState(true);

    // Add state for statusBarHeight
    const [statusBarHeight, setStatusBarHeight] = useState(0);

    // Get system info including status bar height
    useEffect(() => {
        const systemInfo = Taro.getSystemInfoSync();
        setStatusBarHeight(systemInfo.statusBarHeight || 0);
    }, []);

    /**
     * 页面 ready 方法
     */
    useReady(() => {
        setIsPageLoading(true);
        login().then(() => {
            // WxLogin();
            if (QValue("Id")) {
                setId(QValue("Id"));
                getPayList(QValue("Id"));
            } else {
                setIsPageLoading(false);
                message.openDialog("扫码失败", "二维码无效，请重新扫码进去", () => {
                    Taro.navigateTo({
                        url: "/subpackages/csjpxh/jxjy/login/index",
                    });
                });
            }
        });
    });

    // const WxLogin = () => {
    //     request.post<API.Result<any>>("/HNJiaXie/WxLogin/AnonymousLogOn", {}).then((json) => {
    //         if (json && json.success) {
    //             Taro.setStorageSync("accessToken", json.data.accessToken);
    //             // CheckLogOn();

    //             if (QValue("Id")) {
    //                 setId(QValue("Id"));
    //                 getPayList(QValue("Id"));
    //             } else {
    //                 setIsPageLoading(false);
    //                 message.openDialog("扫码失败", "二维码无效，请重新扫码进去", () => {
    //                     Taro.navigateTo({
    //                         url: "/subpackages/csjpxh/jxjy/login/index",
    //                     });
    //                 });
    //             }
    //         } else {
    //             setIsPageLoading(false);
    //         }
    //     });
    // };
    /**
     * 加载账单
     */
    const getPayList = (Id: string) => {
        request
            .post<
                API.Result<
                    {
                        Id: string;
                        xm: string;
                        CostTypeName: string;
                        PayMoney: number;
                    }[]
                >
            >("/HNJiaXie/HnjxPay/GetPayBillList", {
                Id: Id,
            })
            .then((json) => {
                setIsPageLoading(false);
                if (json && json.success) {
                    setPayList(json.data);

                    let payMoney = 0;
                    json.data.map((item) => {
                        payMoney += item.PayMoney;
                    });

                    setPayMoney(payMoney);
                } else {
                    message.openDialog("获取数据失败", json.message);
                }
            });
    };

    /**
     * 获取订单的 信息
     */
    const createOrder = () => {
        if (Id == "" || Id == undefined) {
            message.openDialog("扫码失败", "二维码无效，请重新扫码进去");
        } else {
            setPayLoading(false);
            message.openDialog(
                "确认操作",
                "是否继续提交订单?",
                () => {
                    message.openLoading("正在生成订单");
                    request
                        .put<
                            API.Result<{
                                ShortId: string;
                                AccountId: string;
                                OutTable: string;
                            }>
                        >("/HNJiaXie/HnjxPay/GetPayBillDetail", {
                            Id: Id,
                        })
                        .then((json) => {
                            message.closeLoading();

                            if (json && json.success) {
                                setOrderShortId(json.data.ShortId);
                                setOutTable(json.data.OutTable);
                                setAccountId(json.data.AccountId);
                                setShowPayPopup(true);
                            } else {
                                message.openDialog("生成订单错误", json.message);
                            }
                        });
                },
                "确认",
                "取消"
            );
        }
    };

    /**
     *
     */
    const pay = () => {
        message.openLoading("正在生成支付数据");
        setShowPayPopup(false);

        request
            .put<
                API.Result<{
                    OrderId: string;
                    SignData: {
                        timeStamp: string;
                        package: string;
                        paySign: string;
                        appId: string;
                        signType: string;
                        nonceStr: string;
                    };
                }>
            >("/Pay/WxPay/createOrder", {
                AccountId: accountId,
                PayMoney: payMoney,
                OutTable: outTable,
                OutId: Id,
            })
            .then((json) => {
                message.closeLoading();
                if (json && json.success) {
                    var payInfo = json.data.SignData;
                    setPayLoading(true);
                    Taro.requestPayment({
                        timeStamp: payInfo.timeStamp,
                        nonceStr: payInfo.nonceStr,
                        package: payInfo.package,
                        signType: "MD5",
                        paySign: payInfo.paySign,
                        success: function () {
                            GetResult(json.data.OrderId);
                        },
                        fail: function (res) {
                            setPayLoading(false);
                            if (res.errMsg === "requestPayment:fail cancel") {
                                message.error("当前交易被取消,请重新发起支付", "交易取消");
                            } else {
                                message.error(res.errMsg, "支付失败");
                            }
                        },
                    });
                } else {
                    setPayLoading(false);
                    setShowPayPopup(false);
                    message.error(json.message, "支付配置出错");
                }
            });
    };

    /**
     * 获取订单的支付状态
     * @param OrderId
     */
    const GetResult = (OrderId: string) => {
        message.openLoading("等待支付结果");

        request
            .post<API.Result<{}>>("/Pay/WxPay/GetStatus", {
                Id: OrderId,
            })
            .then((json) => {
                if (json && json.success) {
                    setPayLoading(false);
                    setShowPayPopup(false);
                    message.openDialog("交易成功", "交易已经成功，请退出当前页面");
                } else {
                    setTimeout(GetResult, 1000, OrderId);
                }
            });
    };

    return (
        <Layout>
            {isPageLoading ? (
                <View className="loading-container">
                    <View style={{ marginBottom: "20rpx" }}>
                        <View className="loading-spinner"></View>
                    </View>
                    <Text className="loading-text">订单详情加载中...</Text>
                </View>
            ) : (
                <View className="payment-container">
                    <View
                        className="payment-header"
                        style={{
                            paddingTop: `calc(${statusBarHeight}px + 20rpx)`,
                        }}
                    >
                        <Text className="payment-title">缴费账单</Text>
                        <Text className="payment-subtitle">请确认以下缴费信息</Text>
                    </View>

                    <View className="payment-content" style={{ paddingBottom: "calc(env(safe-area-inset-bottom) + 98rpx)" }}>
                        {payList.length > 0 ? (
                            <CellGroup className="payment-cell-group">
                                {payList.map((item, index) => (
                                    <Cell
                                        className="payment-cell"
                                        key={index}
                                        title={
                                            <View className="payment-cell-title">
                                                <Text className="payment-name">姓名: {item.xm}</Text>
                                            </View>
                                        }
                                        description={
                                            <View className="payment-fee">
                                                <View className="currency-icon">¥</View>
                                                <Text className="payment-fee-text">收费金额: ¥{item.PayMoney}</Text>
                                            </View>
                                        }
                                        extra={
                                            <View className="payment-type">
                                                <Text className="payment-type-text">{item.CostTypeName}</Text>
                                            </View>
                                        }
                                        align="center"
                                    />
                                ))}
                            </CellGroup>
                        ) : (
                            <View className="empty-list">
                                <Text>暂无缴费项目</Text>
                            </View>
                        )}
                    </View>

                    <View className="payment-footer safe__area">
                        <View className="payment-total">
                            <Text className="total-label">合计：</Text>
                            <Price price={payMoney} size={"normal"} thousands className="total-price" />
                        </View>
                        <Button type="primary" className="submit-button" onClick={createOrder}>
                            提交订单
                        </Button>
                    </View>
                </View>
            )}

            <Popup
                visible={showPayPopup}
                round
                position="bottom"
                zIndex={1010}
                style={{
                    zIndex: 1010,
                }}
                onClose={() => {
                    setShowPayPopup(false);
                }}
            >
                <View className="payment-popup safe__area">
                    <View className="popup-header">
                        <Text className="popup-title">确认付款</Text>
                        <View
                            className="popup-close"
                            onClick={() => {
                                setShowPayPopup(false);
                            }}
                        >
                            <Close size="20" />
                        </View>
                    </View>
                    <View className="popup-content">
                        <View className="amount-section">
                            <Text className="currency">￥</Text>
                            <Text className="amount">{payMoney.toFixed(2)}</Text>
                        </View>

                        <View className="order-info">
                            <View className="info-item">
                                <Text className="label">订单编号</Text>
                                <Text className="value">{OrderShortId}</Text>
                            </View>

                            <View className="info-item">
                                <Text className="label">支付方式</Text>
                                <View className="payment-method-container">
                                    <Text className="value">微信支付</Text>
                                </View>
                            </View>
                        </View>

                        <View className="button-container">
                            {!payLoading && (
                                <Button size="large" type="primary" className="pay-button" onClick={pay}>
                                    立即付款
                                </Button>
                            )}
                            {payLoading && (
                                <Button size="large" type="primary" disabled={true} loading={true} className="pay-button">
                                    等待付款结果
                                </Button>
                            )}
                        </View>
                    </View>
                </View>
            </Popup>
        </Layout>
    );
};
export default App;
