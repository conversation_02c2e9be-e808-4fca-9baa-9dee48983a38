page {
    background-color: #f6f7fb;

    font-family: BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Microsoft Yahei", sans-serif;
}

.tui-container {
    background-color: #f0f0f0;
    padding: 0rpx 15rpx;
    height: auto;
}

.tui-bg {
    width: 100%;
    height: 200rpx;
    padding-top: 0rpx;
    // background: linear-gradient(20deg, #E41F19, #F34B0B);
    background-color: #e41f19;
    border-bottom-left-radius: 42rpx;
}

.tui-content {
    padding: 0 35rpx;
    box-sizing: border-box;
    margin-top: -160rpx;
}

.tui-form {
    background: #fff;
    // height: 500rpx;
    box-shadow: 0 10rpx 14rpx 0 rgba(0, 0, 0, 0.08);
    border-radius: 10rpx;
    // margin-top: -160rpx;
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 0 40rpx 40rpx 40rpx;
    width: auto;
}

// .tui-icon {
//     width: 100rpx;
//     height: 100rpx;
//     display: block;
//     margin-bottom: 60rpx;
// }

// .tui-title {
//     font-size: 42rpx;
//     line-height: 42rpx;
//     padding-top: 28rpx;
// }

// .tui-sub-title {
//     color: #666666;
//     font-size: 28rpx;
//     line-height: 28rpx;
//     padding-top: 20rpx;
// }

.tui-btn-box {
    width: 580rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 88rpx;
}

// .tui-tips {
//     font-size: 26rpx;
//     padding: 48rpx 0rpx;
//     box-sizing: border-box;
//     text-align: justify;
//     line-height: 48rpx;
// }

.tui-grey {
    color: #555;
    padding-bottom: 8rpx;
}

.tui-light-grey {
    color: #888;
    line-height: 40rpx;
}

.tui-danger-hover {
    background: #c80808 !important;
    color: #e5e5e5 !important;
}

.tui-danger-outline {
    color: #eb0909 !important;
    background: transparent;
}

.tui-danger-outline::after {
    border: 1px solid #eb0909 !important;
}

.tui-btn-danger {
    background: #eb0909 !important;
    color: #fff;
}

.tui-shadow-danger {
    box-shadow: 0 10rpx 14rpx 0 rgba(235, 9, 9, 0.2);
}

.tui-btn {
    width: 100%;
    position: relative;
    border: 0 !important;
    border-radius: 6rpx;
    padding-left: 0;
    padding-right: 0;
    overflow: visible;
}

/*圆角 */

.tui-fillet {
    border-radius: 50rpx;
}

.tui-btn-white.tui-fillet::after {
    border-radius: 98rpx;
}

.tui-outline-fillet::after {
    border-radius: 98rpx;
}

.tui-invoice__box {
    background-color: #ffffff;
    margin-top: 20rpx;
    border-radius: 20rpx;
    overflow: hidden;
}

.tui-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.tui-notice {
    font-size: 24rpx;
    font-weight: 400;
    color: #999;
}

.tui-bold {
    font-weight: bold;
}

.tui-attr__box {
    padding: 4rpx 0 12rpx 0;
}

.tui-pbtm__0 {
    padding-bottom: 0;
}

.tui-attr-item {
    max-width: 100%;
    min-width: 180rpx;
    height: 64rpx;
    display: -webkit-inline-flex;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #f7f7f7;
    border: 1rpx solid #f7f7f7;
    padding: 0 26rpx;
    box-sizing: border-box;
    border-radius: 12rpx;
    margin-right: 32rpx;
    font-size: 26rpx;
}

.tui-attr-active {
    background-color: #fcedea;
    border-color: #e41f19;
    color: #e41f19;
    font-weight: bold;
}

.tui-tips {
    color: #999;
    font-size: 24rpx;
    font-weight: 400;
    padding: 10rpx 0;
}

.tui-input__item {
    width: 100%;
    display: flex;
    align-items: center;
    font-size: 28rpx;
    padding-top: 40rpx;
}

.tui-input__title {
    width: 156rpx;
    flex-shrink: 0;
}

.tui-input__item input {
    font-size: 28rpx;
    flex: 1;
}

.tui-placeholder {
    color: #bfbfbf;
}

.tui-more__optional {
    font-size: 24rpx;
    display: flex;
    align-items: center;
    color: #999;
}

.tui-between {
    justify-content: space-between;
}

.tui-btn__box {
    padding: 60rpx 30rpx 80rpx;
}

.tui-modal__title {
    text-align: center;
    font-weight: bold;
    padding-bottom: 8rpx;
}

.tui-modal__p {
    font-size: 26rpx;
    color: #888;
    padding-top: 20rpx;
}

.tui-modal__btn {
    width: 100%;
    padding: 60rpx 0 20rpx;
    display: flex;
    justify-content: center;
}

.tui-list-cell {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 30rpx 0;
}

.tui-radius {
    border-radius: 6rpx;
    overflow: hidden;
}

.tui-cell-hover {
    background-color: #f1f1f1 !important;
}

.tui-list-cell::after {
    content: "";
    position: absolute;
    border-bottom: 1px solid #eaeef1;
    -webkit-transform: scaleY(0.5) translateZ(0);
    transform: scaleY(0.5) translateZ(0);
    transform-origin: 0 100%;
    bottom: 0;
    right: 0;
    left: 0;
}

.tui-line-left::after {
    left: 30rpx !important;
}

.tui-line-right::after {
    right: 30rpx !important;
}

.tui-cell-unlined::after {
    border-bottom: 0 !important;
}

.tui-cell-arrow::before {
    content: " ";
    height: 10px;
    width: 10px;
    border-width: 2px 2px 0 0;
    border-color: #c0c0c0;
    border-style: solid;
    -webkit-transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
    transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
    position: absolute;
    top: 50%;
    margin-top: -6px;
    right: 30rpx;
}

.tui-arrow-right::before {
    right: 0 !important;
}

.tui-arrow-gray::before {
    border-color: #666666 !important;
}

.tui-arrow-white::before {
    border-color: #ffffff !important;
}

.tui-arrow-warning::before {
    border-color: #ff7900 !important;
}

.tui-arrow-success::before {
    border-color: #19be6b !important;
}

.tui-arrow-danger::before {
    border-color: #eb0909 !important;
}

.tui-goods__bar {
    position: fixed;
    z-index: 10;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
}

.tui-goods__bar::before {
    content: "";
    width: 100%;
    border-top: 1px solid #eaeef1;
    position: absolute;
    top: 0;
    left: 0;
    transform: scaleY(0.5);
    transform-origin: 0 0;
}

.tui-goods__bar-inner {
    width: 100%;
    height: 100rpx;
    padding: 0 30rpx;
    box-sizing: border-box;
}

.tui--flex {
    display: flex;
    align-items: center;
}

.tui-flex--between {
    justify-content: space-between;
}

.tui-flex--end {
    justify-content: flex-end;
}

.tui-check--all {
    font-size: 26rpx;
    padding-left: 16rpx;
}

.tui-price--box {
    padding-right: 30rpx;
}

.tui-outer__box {
    width: 100%;
    padding-top: 0rpx;
    padding-left: 0rpx;
    padding-right: 0rpx;
    box-sizing: border-box;
}

.tui-bp__tit {
    width: 100%;
    padding: 30rpx;
    box-sizing: border-box;
    position: relative;
    font-weight: 500;
}

.tui-icon--close {
    position: absolute;
    right: 30rpx;
    top: 30rpx;
    // padding: 8rpx;
}

.tui-bp__content {
    width: 100%;
    padding: 24rpx 50rpx 30rpx;
    box-sizing: border-box;
}

.tui-bp--top {
    padding-top: 40rpx;
}

.ti-btn--box {
    width: 100%;
    padding-top: 100rpx;
    box-sizing: border-box;
}

.tui-bp__price-box {
    width: 100%;
    align-items: flex-end;
    padding-bottom: 80rpx;
}

.tui-bp__price {
    font-size: 70rpx;
    line-height: 70rpx;
}

/* .tui-flex__between{
    width: 100%;
    font-size: 30rpx;
    padding-top:56rpx;
  } */
.tui-bp__name {
    color: #999999;
}

.tui-bottom-popup {
    width: 100%;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    transform: translate3d(0, 100%, 0);
    transform-origin: center;
    transition: all 0.3s ease-in-out;
    min-height: 20rpx;
}

.tui-popup-radius {
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
    padding-bottom: env(safe-area-inset-bottom);
    overflow: hidden;
}

.tui-popup-show {
    /* transform: translate3d(0, 0, 0); */
    opacity: 1;
}

.tui-popup-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    transition: all 0.3s ease-in-out;
    opacity: 0;
    visibility: hidden;
}

.tui-mask-show {
    opacity: 1;
    visibility: visible;
}

/* 加载中样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background-color: #ffffff;
    text-align: center;

    .loading-text {
        margin-top: 20px;
        font-size: 28px;
        color: #666;
    }
}

.loading-spinner {
    display: inline-block;
    width: 40rpx;
    height: 40rpx;
    border: 3rpx solid rgba(47, 84, 235, 0.2);
    border-radius: 50%;
    border-top-color: #2f54eb;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 支付容器样式 */
.payment-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f6f7fb;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 头部样式 */
.payment-header {
    padding: 60rpx 30rpx 30rpx;
    background: linear-gradient(135deg, #2f54eb, #1d3eca);
    color: #fff;
    border-bottom-left-radius: 20rpx;
    border-bottom-right-radius: 20rpx;
    box-shadow: 0 3rpx 15rpx rgba(47, 84, 235, 0.2);
    margin-bottom: 15rpx;

    .payment-title {
        display: block;
        font-size: 36rpx;
        font-weight: bold;
        margin-bottom: 6rpx;
    }

    .payment-subtitle {
        display: block;
        font-size: 24rpx;
        opacity: 0.9;
    }
}

/* 内容区域样式 */
.payment-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 20px;
}

.payment-cell-group {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

    .payment-cell {
        margin-bottom: 2px;
        background-color: #fff;
        position: relative;
        padding: 22rpx 16rpx;
        transition: background-color 0.2s;
        animation: slideInRight 0.3s ease-out;
        animation-fill-mode: both;

        &:active {
            background-color: #f9f9f9;
        }

        &:last-child {
            margin-bottom: 0;
        }

        &:after {
            content: "";
            position: absolute;
            left: 30px;
            right: 30px;
            bottom: 0;
            height: 1px;
            background-color: #f2f2f2;
        }

        &:last-child:after {
            display: none;
        }
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Make each cell animate in with a slight delay between them */
.payment-cell:nth-child(1) {
    animation-delay: 0.1s;
}
.payment-cell:nth-child(2) {
    animation-delay: 0.15s;
}
.payment-cell:nth-child(3) {
    animation-delay: 0.2s;
}
.payment-cell:nth-child(4) {
    animation-delay: 0.25s;
}
.payment-cell:nth-child(5) {
    animation-delay: 0.3s;
}

.payment-cell-title {
    display: flex;
    align-items: center;

    .payment-name {
        font-size: 26rpx;
        font-weight: 400;
        color: #333;
        position: relative;
        padding-left: 8rpx;

        &:before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3rpx;
            height: 13rpx;
            background: linear-gradient(to bottom, #2f54eb, #597ef7);
            border-radius: 1rpx;
        }
    }
}

.payment-fee {
    display: flex;
    align-items: center;
    margin-top: 8rpx;
    color: #2f54eb;

    .currency-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15rpx;
        font-weight: bold;
        width: 26rpx;
        height: 26rpx;
        background: linear-gradient(135deg, #597ef7, #2f54eb);
        color: white;
        border-radius: 50%;
        box-shadow: 0 1rpx 3rpx rgba(47, 84, 235, 0.15);
    }

    .payment-fee-text {
        margin-left: 6rpx;
        font-size: 24rpx;
        font-weight: 400;
    }
}

.payment-type {
    padding: 6rpx 11rpx;
    background-color: rgba(47, 84, 235, 0.08);
    border-radius: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(65, 131, 255, 0.08);
    border: 1px solid rgba(47, 84, 235, 0.08);

    .payment-type-text {
        font-size: 19rpx;
        color: #2f54eb;
        font-weight: 400;
    }
}

.empty-list {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200rpx;
    background-color: #fff;
    border-radius: 12rpx;
    color: #999;
    font-size: 26rpx;
}

/* 底部工具栏样式 */
.payment-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15rpx 20rpx;
    background-color: #fff;
    border-top: 1px solid #f2f2f2;
    box-shadow: 0 -1rpx 7rpx rgba(0, 0, 0, 0.05);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;

    .payment-total {
        display: flex;
        align-items: baseline;

        .total-label {
            font-size: 26rpx;
            color: #666;
        }

        .total-price {
            color: #2f54eb !important;
            font-weight: bold;
            font-size: 36rpx;
        }
    }

    .submit-button {
        border-radius: 35rpx;
        padding: 0 30rpx;
        background: linear-gradient(135deg, #597ef7, #2f54eb);
        border: none;
        box-shadow: 0 3rpx 7rpx rgba(47, 84, 235, 0.3);
        transition: transform 0.2s, box-shadow 0.2s;
        font-size: 26rpx;

        &:active {
            transform: translateY(1rpx);
            box-shadow: 0 1rpx 4rpx rgba(47, 84, 235, 0.2);
        }
    }
}

/* 弹出层样式 - 改进版 */
.payment-popup {
    background-color: #fff;
    border-top-left-radius: 15rpx;
    border-top-right-radius: 15rpx;
    overflow: hidden;

    .popup-header {
        position: relative;
        padding: 30rpx 0;
        text-align: center;
        border-bottom: 1px solid #f5f5f5;

        .popup-title {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
        }

        .popup-close {
            position: absolute;
            right: 30rpx;
            top: 50%;
            transform: translateY(-50%);
            width: 60rpx;
            height: 60rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            background-color: rgba(0, 0, 0, 0.02);
            border-radius: 50%;
        }
    }

    .popup-content {
        padding: 20rpx;
    }

    .amount-section {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 40rpx;
        padding: 20rpx 0;

        .currency {
            font-size: 40rpx;
        }

        .amount {
            font-size: 56rpx;
            font-weight: bold;
        }
    }

    .order-info {
        background-color: #f9f9f9;
        border-radius: 8rpx;
        padding: 20rpx;
        margin-bottom: 40rpx;

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16rpx;

            &:last-child {
                margin-bottom: 0;
            }

            .label {
                font-size: 24rpx;
            }

            .value {
                font-size: 24rpx;
                color: #333 !important;
            }
        }
    }

    .button-container {
        margin-top: 40rpx;

        .pay-button {
            width: 100%;
            height: 88rpx;
            border-radius: 44rpx;
            font-size: 28rpx;
            font-weight: 500;
            background: linear-gradient(135deg, #2f54eb, #1d3eca) !important;
            border: none;
            box-shadow: 0 6rpx 12rpx rgba(47, 84, 235, 0.25);
        }
    }
}

.wechat-payment-icon {
    display: none;
}
