import Taro, { useReady } from "@tarojs/taro";
import { useState } from "react";
import { Button, Popup } from "@nutui/nutui-react-taro";
import { Close } from "@nutui/icons-react-taro";
import { Image } from "@tarojs/components";
import "./index.scss";
import request from "@service/request";
import { login } from "@utils/login";
import Layout from "@components/Layout";
import { message } from "@components/MessageApi/MessageApiSingleton";

const App = () => {
    const [xm, setXm] = useState("");
    const [sfzmhm, setSfzmhm] = useState("");
    const [CompanyName, setCompanyName] = useState("");
    const [CostTypeName, setCostTypeName] = useState("");
    const [PayMoney, setPayMoney] = useState(0);
    const [CoachId, setCoachId] = useState("");
    const [CostTypeId, setCostTypeId] = useState("");
    const [AccountId, setAccountId] = useState("");
    const [OutTable, setOutTable] = useState("");
    const [invoiceName, setInvoiceName] = useState("");
    const [taxNumber, setTaxNumber] = useState("");
    const [showPayPopup, setShowPayPopup] = useState(false);
    const [OrderId, setOrderId] = useState("");
    const [OrderShortId, setOrderShortId] = useState("");
    const [payLoading, setPayLoading] = useState(false);
    const [stepIndex, setStepIndex] = useState(0);
    const [logoutLoading, setLogoutLoading] = useState(false);

    useReady(() => {
        login().then(() => {
            GetPayInfo();
        });
    });

    const GetPayInfo = () => {
        request.post<API.Result<any>>("/HNJiaXie/HnjxPay/getPayInfo", {}).then((json) => {
            if (json && json.message) {
                if (json.data.IsPay) {
                    Taro.navigateTo({
                        url: "/subpackages/hnjpxh/my/index",
                    });
                } else {
                    setXm(json.data.xm);
                    setSfzmhm(json.data.sfzmhm);
                    setCompanyName(json.data.CompanyName);
                    setCostTypeName(json.data.CostTypeName);
                    setPayMoney(json.data.PayMoney);
                    setCoachId(json.data.CoachId);
                    setCostTypeId(json.data.CostTypeId);
                    setAccountId(json.data.AccountId);
                    setOutTable(json.data.OutTable);
                }
            } else if (json) {
                message.openDialog("操作失败", json.message, () => {
                    Taro.navigateTo({
                        url: "/subpackages/hnjpxh/login/index",
                    });
                });
            } else {
                Taro.navigateTo({
                    url: "/subpackages/hnjpxh/login/index",
                });
            }
        });
    };

    const createOrder = () => {
        setPayLoading(false);
        message.openLoading("正在生成订单");
        request
            .put<
                API.Result<{
                    Id: string;
                    ShortId: string;
                }>
            >("/HNJiaXie/HnjxPay/setPayOrder", {
                CostTypeId: CostTypeId,
                PayMoney: PayMoney,
                InvoiceName: invoiceName,
                TaxNumber: taxNumber,
            })
            .then((json) => {
                message.closeLoading();
                if (json && json.success) {
                    setOrderId(json.data.Id);
                    setOrderShortId(json.data.ShortId);
                    setShowPayPopup(true);
                } else if (json) {
                    message.openDialog("生成订单错误", json.message);
                }
            });
    };

    const pay = () => {
        message.openLoading("正在生成支付数据");
        setShowPayPopup(false);
        request
            .put<
                API.Result<{
                    OrderId: string;
                    SignData: {
                        timeStamp: string;
                        package: string;
                        paySign: string;
                        appId: string;
                        signType: string;
                        nonceStr: string;
                    };
                }>
            >("/Pay/WxPay/createOrder", {
                AccountId: AccountId,
                PayMoney: PayMoney,
                CostTypeId: CostTypeId,
                OutTable: OutTable,
                OutId: OrderId,
                UserId: CoachId,
            })
            .then((json) => {
                message.closeLoading();
                if (json && json.success) {
                    var payInfo = json.data.SignData;
                    setPayLoading(true);
                    Taro.requestPayment({
                        timeStamp: payInfo.timeStamp,
                        nonceStr: payInfo.nonceStr,
                        package: payInfo.package,
                        signType: "MD5",
                        paySign: payInfo.paySign,
                        success: function () {
                            GetResult(json.data.OrderId);
                        },
                        fail: function (res) {
                            setPayLoading(false);
                            setShowPayPopup(false);
                            if (res.errMsg === "requestPayment:fail cancel") {
                                message.openAlert("交易取消", "当前交易被取消,请重新发起支付");
                            } else {
                                message.error("操作失败", res.errMsg);
                            }
                        },
                    });
                } else {
                    setPayLoading(false);
                    setShowPayPopup(false);
                    message.openDialog("支付配置出错", json.message);
                }
            });
    };

    const GetResult = (OrderId: string) => {
        message.openLoading("等待支付结果");
        request
            .post<API.Result<{}>>("/Pay/WxPay/getStatus", {
                Id: OrderId,
            })
            .then((json) => {
                if (json && json.success) {
                    message.closeLoading();
                    setPayLoading(false);
                    setShowPayPopup(false);
                    setStepIndex(1);
                } else {
                    setTimeout(GetResult, 1000, OrderId);
                }
            });
    };

    const handleLogout = () => {
        setLogoutLoading(true);
        request
            .post<API.Result<{}>>("/HNJiaXie/HnjxLogOut", {})
            .then((json) => {
                setLogoutLoading(false);
                if (json.success) {
                    Taro.removeStorageSync("token");
                    Taro.removeStorageSync("openId");
                    Taro.navigateTo({
                        url: "/pages/hnjxLogin/index",
                    });
                    message.success("已退出登录");
                } else {
                    message.error(json.message, "退出失败");
                }
            })
            .catch(() => {
                setLogoutLoading(false);
                message.error("网络错误", "退出失败");
            });
    };

    return (
        <Layout>
            {stepIndex == 0 && (
                <view className="payment-container">
                    {/* 主要内容区 */}
                    <view className="main-content">
                        {/* 付款信息卡片 */}
                        <view className="payment-card">
                            <view className="payment-header">
                                <view className="title">付款信息</view>
                                <view className="amount">¥{PayMoney}</view>
                            </view>
                            <view className="payment-body">
                                <view className="info-row">
                                    <view className="label">教练姓名</view>
                                    <view className="value">{xm}</view>
                                </view>
                                <view className="info-row">
                                    <view className="label">证件号码</view>
                                    <view className="value">{sfzmhm}</view>
                                </view>
                                <view className="info-row">
                                    <view className="label">所属驾校</view>
                                    <view className="value">{CompanyName}</view>
                                </view>
                                <view className="info-row">
                                    <view className="label">费用类型</view>
                                    <view className="value">{CostTypeName}</view>
                                </view>
                            </view>
                        </view>

                        {/* 发票信息卡片 */}
                        <view className="invoice-card">
                            <view className="invoice-header">
                                <view className="title">发票信息</view>
                                <view
                                    className="info-btn"
                                    onClick={() => {
                                        message.openAlert(
                                            "发票须知",
                                            "1. 发票金额不含优惠券、红包等优惠扣减金额\n2. 请确保发票信息准确，开票后无法修改\n3. 电子发票将在支付完成后7个工作日内开具",
                                            "我知道了"
                                        );
                                    }}
                                >
                                    ?
                                </view>
                            </view>
                            <view className="invoice-body">
                                <view className="input-group">
                                    <view className="label"></view>
                                    <input type="text" placeholder="请输入单位名称" value={invoiceName} onChange={(e) => setInvoiceName(e.target.value)} />
                                </view>
                                <view className="input-group">
                                    <view className="label"></view>
                                    <input type="text" placeholder="请输入纳税人识别号" value={taxNumber} onChange={(e) => setTaxNumber(e.target.value)} />
                                </view>
                            </view>
                        </view>

                        {/* 确认支付按钮 */}
                        <view className="action-button">
                            <Button block type="primary" className="pay-btn" onClick={createOrder} disabled={PayMoney <= 0}>
                                确认支付
                            </Button>

                            <Button
                                block
                                className="logout-btn"
                                style={{
                                    marginTop: "12px",
                                    backgroundColor: "#ffffff",
                                    color: "#000000",
                                    borderRadius: "48rpx",
                                    border: "none",
                                }}
                                onClick={handleLogout}
                                loading={logoutLoading}
                                disabled={logoutLoading}
                            >
                                退出登录
                            </Button>
                        </view>
                    </view>

                    {/* 支付确认弹窗 */}
                    <Popup visible={showPayPopup} position="bottom" round onClose={() => setShowPayPopup(false)}>
                        <view className="popup-content">
                            <view className="popup-header">
                                <span className="title">确认付款</span>
                                <Close size={24} onClick={() => setShowPayPopup(false)} />
                            </view>
                            <view className="popup-body">
                                <view className="amount-section">
                                    <span className="currency">¥</span>
                                    <span className="amount">{PayMoney.toFixed(2)}</span>
                                </view>
                                <view className="order-info">
                                    <view className="info-item">
                                        <span className="label">订单编号</span>
                                        <span className="value">{OrderShortId}</span>
                                    </view>
                                    <view className="info-item">
                                        <span className="label">支付方式</span>
                                        <span className="value">微信支付</span>
                                    </view>
                                </view>
                                <Button block type="primary" className="confirm-btn" onClick={pay} loading={payLoading} disabled={payLoading}>
                                    {payLoading ? "处理中..." : "立即支付"}
                                </Button>
                            </view>
                        </view>
                    </Popup>
                </view>
            )}

            {/* 支付成功页面 */}
            {stepIndex == 1 && (
                <view className="success-page">
                    <view className="success-content">
                        <Image src="https://cdn.51panda.com/WxAppImage/pay/img_recharge_success.png" className="success-icon" mode="widthFix" />
                        <view className="success-title">支付成功</view>
                        <view className="success-subtitle">感谢您的支付</view>
                        <view className="button-group">
                            <Button
                                className="back-btn"
                                onClick={() => {
                                    Taro.navigateTo({
                                        url: "/subpackages/hnjpxh/login/index",
                                    });
                                }}
                            >
                                返回首页
                            </Button>
                            <Button
                                type="primary"
                                className="detail-btn"
                                onClick={() => {
                                    Taro.navigateTo({
                                        url: "/subpackages/hnjpxh/order/index",
                                    });
                                }}
                            >
                                查看订单
                            </Button>
                        </view>
                    </view>
                </view>
            )}
        </Layout>
    );
};

export default App;
