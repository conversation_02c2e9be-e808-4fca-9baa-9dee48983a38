/* 现代化支付界面样式 */
page {
    background-color: #3f3f3f;
    width: 100%;
    height: 100%;
    font-family: BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Microsoft Yahei", sans-serif;
}

.payment-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f6f8fc 0%, #e9f0f7 100%);
    padding: 48rpx 32rpx;

    .main-content {
        max-width: 1600rpx;
        margin: 0 auto;
    }

    .payment-card {
        background: #ffffff;
        border-radius: 32rpx;
        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
        padding: 40rpx;
        margin-bottom: 40rpx;

        .payment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40rpx;

            .title {
                font-size: 36rpx;
                font-weight: 600;
                color: #333;
            }

            .amount {
                font-size: 48rpx;
                font-weight: bold;
                color: #ff6b6b;
            }
        }

        .payment-body {
            .info-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 24rpx;

                .label {
                    color: #666;
                    font-size: 28rpx;
                }

                .value {
                    color: #333;
                    font-size: 28rpx;
                    font-weight: 500;
                }
            }
        }
    }

    .invoice-card {
        background: #ffffff;
        border-radius: 32rpx;
        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
        padding: 40rpx;
        margin-bottom: 40rpx;

        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40rpx;

            .title {
                font-size: 36rpx;
                font-weight: 600;
                color: #333;
            }

            .info-btn {
                width: 48rpx;
                height: 48rpx;
                border-radius: 50%;
                background: #f0f0f0;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #666;
                font-size: 28rpx;
            }
        }

        .invoice-body {
            .input-group {
                margin-bottom: 32rpx;
                position: relative;

                input {
                    width: 100%;
                    height: 88rpx;
                    padding: 0 32rpx;
                    border: 2rpx solid #e8e8e8;
                    border-radius: 16rpx;
                    font-size: 28rpx;
                    box-sizing: border-box;
                    background: #f8f9fa;
                    transition: all 0.3s ease;

                    &::placeholder {
                        color: #999;
                    }

                    &:focus {
                        border-color: #4a90e2;
                        background: #ffffff;
                        outline: none;
                        box-shadow: 0 0 0 2rpx rgba(74, 144, 226, 0.1);
                    }
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }

    .action-button {
        margin-top: 64rpx;

        .pay-btn {
            height: 96rpx;
            font-size: 32rpx;
            font-weight: 600;
            border-radius: 48rpx;
        }
    }
}

/* 支付确认弹窗 */
.popup-content {
    min-height: 40vh;
    padding: 40rpx 32rpx calc(40rpx + constant(safe-area-inset-bottom));
    padding: 40rpx 32rpx calc(40rpx + env(safe-area-inset-bottom));

    .popup-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 40rpx;

        .title {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
        }
    }

    .popup-body {
        .amount-section {
            display: flex;
            align-items: baseline;
            justify-content: center;
            margin: 40rpx 0;

            .currency {
                font-size: 40rpx;
                font-weight: 500;
                margin-right: 8rpx;
            }

            .amount {
                font-size: 64rpx;
                font-weight: 600;
                line-height: 1;
            }
        }

        .order-info {
            background: #f8f8f8;
            border-radius: 16rpx;
            padding: 24rpx;
            margin-bottom: 40rpx;

            .info-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 16rpx;

                &:last-child {
                    margin-bottom: 0;
                }

                .label {
                    color: #666;
                    font-size: 28rpx;
                }

                .value {
                    color: #333;
                    font-size: 28rpx;
                }
            }
        }

        .confirm-btn {
            margin-top: 40rpx;
            height: 88rpx;
            font-size: 32rpx;
            border-radius: 44rpx;
        }
    }
}

/* 支付成功页面 */
.success-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f6f8fc 0%, #e9f0f7 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 48rpx 32rpx;

    .success-content {
        text-align: center;
        background: #ffffff;
        border-radius: 32rpx;
        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
        padding: 64rpx;
        width: 100%;
        max-width: 800rpx;

        .success-icon {
            width: 240rpx;
            height: 240rpx;
            margin-bottom: 48rpx;
        }

        .success-title {
            font-size: 48rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 16rpx;
        }

        .success-subtitle {
            font-size: 28rpx;
            color: #666;
            margin-bottom: 64rpx;
        }

        .button-group {
            display: flex;
            gap: 32rpx;

            .back-btn,
            .detail-btn {
                flex: 1;
                height: 88rpx;
                font-size: 30rpx;
                font-weight: 600;
                border-radius: 44rpx;
            }
        }
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}
