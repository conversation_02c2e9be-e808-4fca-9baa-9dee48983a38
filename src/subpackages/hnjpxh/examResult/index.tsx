import { useDidHide, useDidShow } from "@tarojs/taro";
import { useState } from "react";
import "./index.scss";
import "@utils/app.scss";

import { login } from "@utils/login";
import request from "@service/request";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import { Image, View, Text } from "@tarojs/components";
import Layout from "@/components/Layout";

/**
 * 6. 签署完成 数据上传页面
 * @returns
 */
const App = () => {
    const [examResultList, setExamResultList] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);

    /**
     * 页面 ready 方法
     */
    useDidShow(() => {
        login().then(() => {});
        GetMyResultList();
    });

    /**
     * 页面销毁 的方法
     */
    useDidHide(() => {});

    const GetMyResultList = () => {
        setLoading(true);
        request
            .post<
                API.Result<{
                    data: [
                        {
                            Score: number;
                            CreateTime: string;
                        }
                    ];
                }>
            >("/HNJiaXie/HnjxQuestion/getMyResultList", {})
            .then((json) => {
                setLoading(false);
                if (json.success) {
                    setExamResultList(json.data.data);
                } else {
                    message.openDialog("操作失败", json.message);
                }
            })
            .catch(() => {
                setLoading(false);
            });
    };

    // 根据分数返回CSS类名
    const getScoreClass = (score: number) => {
        if (score >= 80) return "high-score";
        if (score >= 60) return "medium-score";
        return "low-score";
    };

    // 格式化日期时间
    const formatDateTime = (dateTimeStr: string) => {
        try {
            const date = new Date(dateTimeStr);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, "0");
            const day = String(date.getDate()).padStart(2, "0");
            const hours = String(date.getHours()).padStart(2, "0");
            const minutes = String(date.getMinutes()).padStart(2, "0");

            return `${year}-${month}-${day} ${hours}:${minutes}`;
        } catch (e) {
            return dateTimeStr;
        }
    };

    return (
        <Layout>
            <view
                className="exam-results-container"
                style={{
                    minHeight: "100vh",
                    backgroundColor: "#f5f5f5",
                    paddingBottom: "60px", // Add padding for the footer
                }}
            >
                {loading ? (
                    <view className="loading-container">
                        <view className="loading-spinner"></view>
                        <view className="loading-text">加载成绩中...</view>
                    </view>
                ) : examResultList.length > 0 ? (
                    <view className="results-list">
                        {examResultList.map((item, key) => {
                            return (
                                <view className={`result-card ${getScoreClass(item.Score)}`} key={key}>
                                    <view className="result-score">
                                        <text className="score-value">{item.Score.toFixed(1)}</text>
                                        <text className="score-unit">分</text>
                                    </view>
                                    <view className="result-time">{formatDateTime(item.CreateTime)}</view>
                                </view>
                            );
                        })}
                    </view>
                ) : (
                    <view className="empty-container">
                        <Image
                            className="empty-image"
                            src="data:image/png;base64,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"
                        />
                        <view className="empty-text">
                            <view>暂无测试记录</view>
                            <view>完成测试后可查看成绩</view>
                        </view>
                    </view>
                )}
            </view>

            {/* 底部版权信息 - 吸底 */}
            <View
                style={{
                    backgroundColor: "white",
                    padding: "15px 0",
                    textAlign: "center",
                    position: "fixed",
                    bottom: 0,
                    left: 0,
                    right: 0,
                    borderTop: "1px solid #eee",
                    paddingBottom: "calc(15px + env(safe-area-inset-bottom))", // Add safe area padding
                    zIndex: 100, // Ensure it's above other content
                }}
            >
                <Text
                    style={{
                        fontSize: "12px",
                        color: "#666",
                        textAlign: "center",
                        width: "100%",
                        display: "block",
                    }}
                >
                    © {new Date().getFullYear()} 湖南省机动车驾驶员培训协会
                </Text>
            </View>
        </Layout>
    );
};
export default App;
