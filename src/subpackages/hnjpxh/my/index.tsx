import Taro, { useDidHide, useDidShow, useLaunch } from "@tarojs/taro";
import { useState } from "react";
import { ImagePreview } from "@nutui/nutui-react-taro";
import "./index.scss";
import "@utils/app.scss";
import { View, Text } from "@tarojs/components";

// import {getUserInfo} from '@utils/login'
import request from "@service/request";
import { tool } from "@utils/tool";
import config from "@/config";
import Layout from "@/components/Layout";
import { message } from "@/components/MessageApi/MessageApiSingleton";

/**
 * 用户个人中心页面 - 简约现代风格
 */
const App = () => {
    const [userName, setUserName] = useState("");
    const [CompanyName, setCompanyName] = useState("");

    const [ShowStudy, setShowStudy] = useState(false);
    const [ShowExam, setShowExam] = useState(false);
    const [ExamPass, setExamPass] = useState(false);
    const [ClassName, setClassName] = useState("");

    const [resultImage, setResultImage] = useState<any[]>([]);

    const [isLoggedIn, setIsLoggedIn] = useState(true);

    /**
     * 页面 ready 方法
     */
    useDidShow(() => {
        // 错误处理更完善的系统信息获取
        try {
            // 首先检查登录状态
            const token = tool.data.get(config.ACCESS_TOKEN_KEY);
            if (!token) {
                console.log("未检测到有效token，跳转登录页");
                setTimeout(() => {
                    Taro.navigateTo({ url: "/pages/hnjxLogin/index" });
                }, 1500);
                return;
            }
        } catch (error) {
            console.error("获取系统信息失败:", error);
            // 如果是认证错误，尝试刷新或重新登录
            if (String(error).includes("INVALID_LOGIN") || String(error).includes("access_token expired")) {
                console.log("登录已过期，需要重新登录");
                tool.data.clear(); // 清除本地存储的过期凭证

                setTimeout(() => {
                    Taro.navigateTo({ url: "/pages/hnjxLogin/index" });
                }, 1500);
                return;
            }
        }

        // 检查登录状态
        const token = tool.data.get(config.ACCESS_TOKEN_KEY);
        setIsLoggedIn(!!token);

        // 获取用户信息
        getUserInfo();
    });

    /**
     * 页面销毁 的方法
     */
    useDidHide(() => {});

    const handleLogout = () => {
        message.openLoading("正在退出系统");
        request
            .post<API.Result<{}>>("/HNJiaXie/HnjxLogOut", {})
            .then((json) => {
                message.closeLoading();
                if (json.success) {
                    Taro.removeStorageSync("token");
                    Taro.removeStorageSync("openId");
                    Taro.navigateTo({
                        url: "/pages/hnjxLogin/index",
                    });
                    message.success("已退出登录");
                } else {
                    message.error(json.message, "退出失败");
                }
            })
            .catch(() => {
                message.closeLoading();
                message.error("网络错误", "退出失败");
            });
    };

    /**
     * 获取用户信息的方法
     */
    const getUserInfo = () => {
        // console.log("开始获取用户信息");

        // 先关闭可能存在的旧loading，确保UI干净
        // if (message.isLoading && message.isLoading()) {
        //     message.closeLoading();
        // }

        // 使用setTimeout确保UI已经渲染完成再显示loading
        setTimeout(() => {
            // message.openLoading("获取用户信息");

            // 添加额外延迟模拟网络请求，确保loading能显示出来
            setTimeout(() => {
                request
                    .post<
                        API.Result<{
                            UserName: string;
                            CompanyName: string;
                            ShowStudy: boolean;
                            ShowExam: boolean;
                            ExamPass: boolean;
                            ClassName: string;
                        }>
                    >("/HNJiaXie/HnjxUser/getUserInfo")
                    .then((json) => {
                        // message.closeLoading();
                        if (json.success) {
                            setUserName(json.data.UserName);
                            setCompanyName(json.data.CompanyName);
                            setShowStudy(json.data.ShowStudy);
                            setShowExam(json.data.ShowExam);
                            setExamPass(json.data.ExamPass);
                            setClassName(json.data.ClassName || "");
                        }
                    })
                    .catch((error) => {
                        // message.closeLoading();
                        console.error("获取用户信息失败:", error);
                    });
            }, 100); // 延迟100毫秒确保loading显示
        }, 50); // 延迟50毫秒确保页面渲染完成
    };

    // 菜单项配置 - 简洁统一的图标样式
    const menuItems = [
        {
            id: "study",
            title: "在线学习",
            iconClass: "wechat-icon icon-study",
            show: ShowStudy,
            onClick: () => {
                Taro.navigateTo({ url: "/subpackages/hnjpxh/studyList/index" });
            },
        },
        {
            id: "exam",
            title: "在线测试",
            iconClass: "wechat-icon icon-exam",
            show: ShowExam,
            onClick: () => {
                Taro.navigateTo({ url: "/subpackages/hnjpxh/exam/index" });
            },
        },
        {
            id: "result",
            title: "测试成绩",
            iconClass: "wechat-icon icon-score",
            show: ShowExam,
            onClick: () => {
                Taro.navigateTo({ url: "/subpackages/hnjpxh/examResult/index" });
            },
        },
        {
            id: "certificate",
            title: "证书下载",
            iconClass: "wechat-icon icon-certificate",
            show: ExamPass,
            onClick: () => {
                message.openLoading("正在下载证书");
                request.post<API.Result<{}>>("/HNJiaXie/HnjxQuestion/getMyResultImage", {}).then((json) => {
                    message.closeLoading();
                    if (json.success) {
                        Taro.previewImage({
                            current: "data:image/png;base64," + json.data,
                            urls: ["data:image/png;base64," + json.data],
                        });
                    } else {
                        message.error(json.message, "下载失败");
                    }
                });
            },
        },
    ];

    // 底部菜单项
    const footerMenuItems = [
        {
            id: "about",
            title: "关于我们",
            iconClass: "wechat-icon icon-about",
            show: true,
            onClick: () => {
                message.openAlert(
                    "关于我们",
                    '湖南省机动车驾驶员培训协会，成立于2003年，位于湖南省长沙市，业务范围是"沟通交流、指导培训、咨询服务、研讨推广、编辑出版协会刊物"。',
                    "我已知晓",
                    () => {},
                    "left"
                );
            },
        },
        {
            id: "logout",
            title: "退出登录",
            iconClass: "wechat-icon icon-logout",
            show: true,
            onClick: () => {
                handleLogout();
            },
        },
    ];

    return (
        <Layout>
            <View className="my-container">
                <View className="wechat-page-container">
                    {/* 整体蓝色背景区域 */}
                    <View className="wechat-blue-area">
                        {/* 顶部导航栏 */}
                        <View className="wechat-nav" style={{ paddingTop: "10vh" }}>
                            <Text className="wechat-nav-title"></Text>
                        </View>

                        {/* 用户信息头部 */}
                        <View className="wechat-profile">
                            <View className="wechat-avatar">
                                <Text>{userName ? userName.charAt(0).toUpperCase() : "U"}</Text>
                            </View>
                            <View className="wechat-user-info">
                                <Text className="wechat-username">{userName || "未登录"}</Text>
                                <Text className="wechat-company">{CompanyName || "未关联单位"}</Text>
                            </View>
                        </View>

                        {/* 平台信息 - 移动到用户信息下方 */}
                        <View className="wechat-platform">
                            <Text className="wechat-platform-text">湖南省机动车驾驶员培训协会教练微信服务平台</Text>
                        </View>
                    </View>

                    {/* 登录提示 */}
                    {!isLoggedIn && (
                        <View className="wechat-login-tip">
                            <Text>登录信息已过期，正在跳转到登录页面...</Text>
                        </View>
                    )}

                    {/* 主内容区 */}
                    <View className="wechat-content-area">
                        {/* 卡片区域开始 */}
                        <View className="wechat-card-container">
                            {/* 主要菜单列表 */}
                            {menuItems.some((item) => item.show) && (
                                <View className="wechat-card">
                                    {menuItems.map(
                                        (item) =>
                                            item.show && (
                                                <View key={item.id} className="wechat-card-item" onClick={item.onClick}>
                                                    <View className="wechat-item-left">
                                                        <View className={item.iconClass}></View>
                                                        <Text className="wechat-item-text">{item.title}</Text>
                                                    </View>
                                                    <View className="wechat-arrow"></View>
                                                </View>
                                            )
                                    )}
                                </View>
                            )}

                            {/* 底部菜单列表 */}
                            <View className="wechat-card">
                                {footerMenuItems.map(
                                    (item) =>
                                        item.show && (
                                            <View key={item.id} className="wechat-card-item" onClick={item.onClick}>
                                                <View className="wechat-item-left">
                                                    <View className={item.iconClass}></View>
                                                    <Text className="wechat-item-text">{item.title}</Text>
                                                </View>
                                                <View className="wechat-arrow"></View>
                                            </View>
                                        )
                                )}
                            </View>
                        </View>

                        {/* 班级信息 - 只在有班级时显示 */}
                        {ClassName && ClassName !== "暂未分班" && (
                            <View className="wechat-class-info">
                                <Text className="wechat-class-text">{ClassName}</Text>
                            </View>
                        )}
                    </View>

                    {/* 底部版权信息 - 吸底 */}
                    <View className="wechat-footer safe-area-bottom">
                        <Text className="wechat-footer-text">© {new Date().getFullYear()} 湖南省机动车驾驶员培训协会</Text>
                    </View>

                    {/* 图片预览 */}
                    <ImagePreview
                        images={resultImage}
                        visible={resultImage.length > 0}
                        onClose={() => {
                            setResultImage([]);
                        }}
                    />
                </View>
            </View>
        </Layout>
    );
};

export default App;
