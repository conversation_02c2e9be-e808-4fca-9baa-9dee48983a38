/* 清除微信样式前缀，使用更清晰的wechat前缀 */

/* 基础重置 */
page {
    background-color: #f7f7f7;
    color: #333;
    font-family: BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Microsoft Yahei", sans-serif;
    line-height: 1.5;
    font-size: 28rpx;
    height: 100%;
}

/* 页面容器 - 使用flex实现吸底 */
.wechat-page-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - constant(safe-area-inset-bottom) - constant(safe-area-inset-top));
    height: calc(100vh - env(safe-area-inset-bottom) - env(safe-area-inset-top));
}

/* 内容区域 - 自动伸展 */
.wechat-content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto; /* 允许内容区域滚动 */
    padding-bottom: 80rpx; /* 添加底部间距，防止内容被底部版权栏遮挡 */
}

/* 整体蓝色背景区域 */
.wechat-blue-area {
    background-color: #2f54eb;
    min-height: 30vh;
    border-bottom-left-radius: 20rpx;
    border-bottom-right-radius: 20rpx;
    padding-bottom: 40rpx;
    z-index: 1; /* 确保层级正确 */
}

/* 导航栏 - 使用主色调并延伸至顶部 */
.wechat-nav {
    position: relative;
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.wechat-nav-title {
    font-size: 36rpx;
    font-weight: 500;
    color: white;
}

/* 用户信息区域 - 保持主色调一致性 */
.wechat-profile {
    padding: 20rpx 40rpx;
    display: flex;
    align-items: center;
}

.wechat-avatar {
    width: 130rpx;
    height: 130rpx;
    border-radius: 15rpx;
    background-color: rgba(255, 255, 255, 0.2);
    margin-right: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 64rpx;
    color: white;
    box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.15);
    border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.wechat-user-info {
    flex: 1;
}

.wechat-username {
    font-size: 40rpx;
    color: white;
    font-weight: 500;
    display: block;
    margin-bottom: 10rpx;
}

.wechat-company {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.85);
    display: block;
}

/* 平台信息 - 移到用户信息下方 */
.wechat-platform {
    padding: 30rpx 40rpx 10rpx;
}

.wechat-platform-text {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.9);
    text-align: center;
    display: block;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 16rpx 20rpx;
    border-radius: 12rpx;
    position: relative;
}

/* 卡片容器 - 提高与背景的对比度 */
.wechat-card-container {
    padding: 30rpx 24rpx;
    margin-top: 20rpx;
}

/* 卡片 - 更精致的阴影和圆角 */
.wechat-card {
    background-color: #fff;
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

/* 卡片项 - 简洁明了的设计 */
.wechat-card-item {
    position: relative;
    padding: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1rpx solid #f5f5f5;
}

.wechat-card-item:last-child {
    border-bottom: none;
}

.wechat-card-item:active {
    background-color: #f9f9f9;
}

.wechat-item-left {
    display: flex;
    align-items: center;
    flex: 1;
}

.wechat-item-text {
    font-size: 32rpx;
    color: #333;
    margin-left: 20rpx;
}

.wechat-arrow {
    width: 16rpx;
    height: 16rpx;
    border-top: 3rpx solid #bbb;
    border-right: 3rpx solid #bbb;
    transform: rotate(45deg);
}

/* 统一使用主色调的简洁图标 */
.wechat-icon {
    width: 44rpx;
    height: 44rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background-color: #eef2ff;
}

/* 使用简洁的字体图标 */
.icon-study::before {
    content: "S";
    color: #2f54eb;
    font-weight: bold;
    font-size: 28rpx;
}

.icon-exam::before {
    content: "E";
    color: #2f54eb;
    font-weight: bold;
    font-size: 28rpx;
}

.icon-score::before {
    content: "R";
    color: #2f54eb;
    font-weight: bold;
    font-size: 28rpx;
}

.icon-certificate::before {
    content: "C";
    color: #2f54eb;
    font-weight: bold;
    font-size: 28rpx;
}

.icon-about::before {
    content: "A";
    color: #2f54eb;
    font-weight: bold;
    font-size: 28rpx;
}

.icon-logout::before {
    content: "L";
    color: #2f54eb;
    font-weight: bold;
    font-size: 28rpx;
}

/* 班级信息 - 更现代的分隔线设计 */
.wechat-class-info {
    text-align: center;
    padding: 30rpx 0;
}

.wechat-class-text {
    display: inline-block;
    font-size: 26rpx;
    color: #888;
    position: relative;
    padding: 0 30rpx;
}

.wechat-class-text:before,
.wechat-class-text:after {
    content: "";
    position: absolute;
    top: 50%;
    width: 60rpx;
    height: 1rpx;
    background-color: #ddd;
}

.wechat-class-text:before {
    left: -40rpx;
}

.wechat-class-text:after {
    right: -40rpx;
}

/* 底部版权信息 - 固定在屏幕底部 */
.wechat-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
    padding: 20rpx 0 0;
    background-color: #fff;
    border-top: 1rpx solid #f0f0f0;
    z-index: 100;
}

.wechat-footer-text {
    font-size: 24rpx;
    color: #999;
    display: block;
    padding-bottom: 20rpx;
}

/* 安全区域适配 - 使其与版权区域视觉连贯 */
.safe-area-bottom {
    padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
    padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
    background-color: #fff;
}

/* 在组件最外层也设置样式 */
.my-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}
