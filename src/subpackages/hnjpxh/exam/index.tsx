import { useReady, useUnload } from "@tarojs/taro";
import { useRef, useState } from "react";
import { Button, Checkbox, Popup, TabPane, Tabs } from "@nutui/nutui-react-taro";
import "./index.scss";
import "@utils/app.scss";

import { login } from "@utils/login";
import request from "@service/request";
import { Image } from "@tarojs/components";
import { Close, List } from "@nutui/icons-react-taro";
import MessageApi from "@components/MessageApi/MessageApi";
import Layout from "@/components/Layout";

/**
 * 题目的 类型 定义
 */
type question = {
    Id: string;
    Question: string;
    QuestionType: number;
    Answer: string;
    ImagePath: string;
    UserAnswer: string;
};

let timer;

const App = () => {
    const message: any = useRef();

    const [showNumber, setShowNumber] = useState(false);

    const [numberIndex, setNumberIndex] = useState(0);

    const [numberTabIndex, setNumberTabIndex] = useState(0);
    const numberTabs = ["01-25", "26-50", "51-75", "76-100"];

    const [UserAnswer, setUserAnswer] = useState("");

    const leftTimeRef = useRef(0);
    const [leftTime, setLeftTime] = useState(3600);

    const [exmaData, setExamData] = useState<question[]>([]);
    const [CoachQuestionId, setCoachQuestionId] = useState("");

    const [stopAnswer, setStopAnswer] = useState(false);

    /**
     * 页面 ready 方法
     */
    useReady(() => {
        login().then(() => {
            Get100Question();
        });
    });

    useUnload(() => {
        clearInterval(timer);
    });

    /**
     * 获取 题目
     */
    const Get100Question = () => {
        request
            .post<
                API.Result<{
                    CoachQuestionId: string;
                    Questions: question[];
                }>
            >("/HNJiaXie/HnjxQuestion/Get100QuestionIndex", {})
            .then((json) => {
                if (json && json.success) {
                    setExamData(json.data.Questions);
                    setCoachQuestionId(json.data.CoachQuestionId);
                    GoQuestion(0);
                    leftTimeRef.current = leftTime;

                    timer = setInterval(SetTime, 1000);
                } else {
                    message.current.openDialog("操作失败", json.message);
                }
            });
    };

    /**
     * 设置 计时 时间
     */
    const SetTime = () => {
        console.log(leftTimeRef.current);
        leftTimeRef.current = leftTimeRef.current - 1;
        setLeftTime(leftTimeRef.current);

        if (leftTimeRef.current == 0) {
            clearInterval(timer);
            SubmitAnswer();
        }
    };

    /**
     * 用户答题 的保存
     */
    const UserAnswerChange = (e: string) => {
        if (!stopAnswer) {
            let _exmaData = exmaData;
            _exmaData[numberIndex].UserAnswer = e;
            setUserAnswer(e);

            request
                .put<API.Result<{}>>("/HNJiaXie/HnjxQuestion/SetAnswer", {
                    CoachQuestionId: CoachQuestionId,
                    QuestionId: _exmaData[numberIndex].Id,
                    Answer: e,
                })
                .then(() => {
                    setExamData(_exmaData);

                    if (numberIndex < exmaData.length - 1) {
                        GoQuestion(numberIndex + 1);
                    }
                });
        }
    };

    /**
     * 跳转至 制定的题目
     * @param i
     */
    const GoQuestion = (i) => {
        setNumberIndex(i);
        setUserAnswer(exmaData[i]?.UserAnswer);
    };

    /**
     * 交卷
     */
    const SubmitAnswer = () => {
        message.current.openLoading("正在上传答卷");
        setStopAnswer(true);
        clearInterval(timer);
        request
            .put<API.Result<{}>>("/HNJiaXie/HnjxQuestion/SubmitAnswer", {
                CoachQuestionId: CoachQuestionId,
            })
            .then((json) => {
                message.current.closeLoading();
                if (json && json.success) {
                    message.current.openDialog("提交成功", json.message);
                } else {
                    message.current.openDialog("操作失败", json.message);
                }
            });
    };

    return (
        <>
            <MessageApi ref={message}></MessageApi>

            <view className="tui-outer__box safe__area">
                {/* Improved header with timer and question counter */}
                <view className="exam-header">
                    <view className="exam-progress">
                        <view className="progress-text">
                            题目进度: {numberIndex + 1} / {exmaData?.length}
                        </view>
                        <view className="progress-bar">
                            <view className="progress-filled" style={{ width: `${((numberIndex + 1) / exmaData.length) * 100}%` }}></view>
                        </view>
                    </view>

                    <view className={`exam-timer ${leftTime <= 300 ? "exam-timer-warning" : leftTime <= 600 ? "exam-timer-alert" : ""}`}>
                        <view className="timer-icon">⏱️</view>
                        <view className="timer-text">
                            {parseInt(leftTime / 60 + "") < 10 ? "0" + parseInt(leftTime / 60 + "") : parseInt(leftTime / 60 + "")}:
                            {leftTime % 60 < 10 ? "0" + (leftTime % 60) : leftTime % 60}
                        </view>
                    </view>
                </view>

                {/* Enhanced question card */}
                <view className="question-card">
                    <view className="question-header">
                        <view className="question-number">#{numberIndex + 1}</view>
                        <view className="question-type">{exmaData[numberIndex]?.QuestionType === 0 ? "选择题" : "判断题"}</view>
                    </view>

                    <view className="question-content">{exmaData[numberIndex]?.Question.replace("\r\r", "</br>")}</view>

                    {exmaData[numberIndex]?.ImagePath !== "" && (
                        <view className="question-image">
                            <Image src={exmaData[numberIndex]?.ImagePath} mode={"aspectFit"}></Image>
                        </view>
                    )}
                </view>

                {/* Enhanced answer options */}
                <view className="answer-options">
                    {
                        // 选择题
                        exmaData[numberIndex]?.QuestionType == 0 && (
                            <>
                                <view
                                    onClick={() => !stopAnswer && UserAnswerChange("A")}
                                    className={`answer-option ${UserAnswer === "A" ? "selected" : ""} ${stopAnswer && exmaData[numberIndex]?.Answer === "A" ? "correct" : ""} ${
                                        stopAnswer && UserAnswer === "A" && exmaData[numberIndex]?.Answer !== "A" ? "incorrect" : ""
                                    }`}
                                >
                                    <view className="option-letter">A</view>
                                    <Checkbox
                                        key={"A"}
                                        checked={UserAnswer === "A"}
                                        disabled={stopAnswer}
                                        onChange={(e) => {
                                            e ? UserAnswerChange("A") : setUserAnswer("");
                                        }}
                                    />
                                    {stopAnswer && exmaData[numberIndex]?.Answer == "A" && <view className="correct-marker">✓</view>}
                                </view>

                                <view
                                    onClick={() => !stopAnswer && UserAnswerChange("B")}
                                    className={`answer-option ${UserAnswer === "B" ? "selected" : ""} ${stopAnswer && exmaData[numberIndex]?.Answer === "B" ? "correct" : ""} ${
                                        stopAnswer && UserAnswer === "B" && exmaData[numberIndex]?.Answer !== "B" ? "incorrect" : ""
                                    }`}
                                >
                                    <view className="option-letter">B</view>
                                    <Checkbox
                                        key={"B"}
                                        checked={UserAnswer === "B"}
                                        disabled={stopAnswer}
                                        onChange={(e) => {
                                            e ? UserAnswerChange("B") : setUserAnswer("");
                                        }}
                                    />
                                    {stopAnswer && exmaData[numberIndex]?.Answer == "B" && <view className="correct-marker">✓</view>}
                                </view>

                                <view
                                    onClick={() => !stopAnswer && UserAnswerChange("C")}
                                    className={`answer-option ${UserAnswer === "C" ? "selected" : ""} ${stopAnswer && exmaData[numberIndex]?.Answer === "C" ? "correct" : ""} ${
                                        stopAnswer && UserAnswer === "C" && exmaData[numberIndex]?.Answer !== "C" ? "incorrect" : ""
                                    }`}
                                >
                                    <view className="option-letter">C</view>
                                    <Checkbox
                                        key={"C"}
                                        checked={UserAnswer === "C"}
                                        disabled={stopAnswer}
                                        onChange={(e) => {
                                            e ? UserAnswerChange("C") : setUserAnswer("");
                                        }}
                                    />
                                    {stopAnswer && exmaData[numberIndex]?.Answer == "C" && <view className="correct-marker">✓</view>}
                                </view>
                            </>
                        )
                    }
                    {
                        // 判断题
                        exmaData[numberIndex]?.QuestionType == 1 && (
                            <>
                                <view
                                    onClick={() => !stopAnswer && UserAnswerChange("A")}
                                    className={`answer-option ${UserAnswer === "A" ? "selected" : ""} ${stopAnswer && exmaData[numberIndex]?.Answer === "A" ? "correct" : ""} ${
                                        stopAnswer && UserAnswer === "A" && exmaData[numberIndex]?.Answer !== "A" ? "incorrect" : ""
                                    }`}
                                >
                                    <view className="option-letter true-option">✓</view>
                                    <view className="option-text">对</view>
                                    <Checkbox
                                        key={"A"}
                                        checked={UserAnswer === "A"}
                                        disabled={stopAnswer}
                                        onChange={(e) => {
                                            e ? UserAnswerChange("A") : setUserAnswer("");
                                        }}
                                    />
                                    {stopAnswer && exmaData[numberIndex]?.Answer == "A" && <view className="correct-marker">✓</view>}
                                </view>

                                <view
                                    onClick={() => !stopAnswer && UserAnswerChange("B")}
                                    className={`answer-option ${UserAnswer === "B" ? "selected" : ""} ${stopAnswer && exmaData[numberIndex]?.Answer === "B" ? "correct" : ""} ${
                                        stopAnswer && UserAnswer === "B" && exmaData[numberIndex]?.Answer !== "B" ? "incorrect" : ""
                                    }`}
                                >
                                    <view className="option-letter false-option">✗</view>
                                    <view className="option-text">错</view>
                                    <Checkbox
                                        key={"B"}
                                        checked={UserAnswer === "B"}
                                        disabled={stopAnswer}
                                        onChange={(e) => {
                                            e ? UserAnswerChange("B") : setUserAnswer("");
                                        }}
                                    />
                                    {stopAnswer && exmaData[numberIndex]?.Answer == "B" && <view className="correct-marker">✓</view>}
                                </view>
                            </>
                        )
                    }
                </view>

                {/* Improved navigation buttons */}
                <view className="navigation-buttons">
                    <Button
                        className={`nav-button prev-button ${numberIndex == 0 ? "disabled" : ""}`}
                        disabled={numberIndex == 0}
                        onClick={() => {
                            GoQuestion(numberIndex - 1);
                        }}
                    >
                        <view className="button-content">
                            <view className="button-icon">◀</view>
                            <view className="button-text">上一题</view>
                        </view>
                    </Button>

                    <Button
                        className="submit-button"
                        onClick={() => {
                            message.current.openDialog(
                                "确认操作",
                                "是否提交当前的答题?",
                                () => {
                                    SubmitAnswer();
                                },
                                "确认提交",
                                "取消"
                            );
                        }}
                    >
                        <view className="button-content">
                            <view className="button-text">交卷</view>
                        </view>
                    </Button>

                    <Button
                        className={`nav-button next-button ${numberIndex == exmaData.length - 1 ? "disabled" : ""}`}
                        disabled={numberIndex == exmaData.length - 1}
                        onClick={() => {
                            GoQuestion(numberIndex + 1);
                        }}
                    >
                        <view className="button-content">
                            <view className="button-text">下一题</view>
                            <view className="button-icon">▶</view>
                        </view>
                    </Button>
                </view>

                <view style={{ height: "130rpx" }}></view>
            </view>

            {/* Enhanced Popup for question selection */}
            <Popup
                visible={showNumber}
                round
                position="bottom"
                zIndex={1010}
                style={{ zIndex: 1010 }}
                onClose={() => {
                    setShowNumber(false);
                }}
            >
                <view className="safe__area">
                    <view className="question-selector-header">
                        <text>题目选择</text>
                        <view
                            className="close-icon"
                            onClick={() => {
                                setShowNumber(false);
                            }}
                        >
                            <Close size={14} />
                        </view>
                    </view>
                    <Tabs
                        className="question-tabs"
                        value={numberTabIndex}
                        onChange={(paneKey: number) => {
                            setNumberTabIndex(paneKey);
                        }}
                        direction="vertical"
                    >
                        {numberTabs.map((item, index) => (
                            <TabPane key={index} title={`${item}`}>
                                <view className="question-grid">
                                    {exmaData?.map(
                                        (item, index) =>
                                            index >= numberTabIndex * 25 &&
                                            index < (numberTabIndex + 1) * 25 && (
                                                <view
                                                    className={`grid-item 
                                                        ${numberIndex == index ? "current" : ""}
                                                        ${item.UserAnswer !== "" ? "answered" : ""}
                                                        ${stopAnswer && item.UserAnswer !== item.Answer && item.UserAnswer !== "" ? "incorrect" : ""}
                                                        ${stopAnswer && item.UserAnswer === item.Answer ? "correct" : ""}
                                                    `}
                                                    key={index}
                                                    onClick={() => {
                                                        GoQuestion(index);
                                                        setUserAnswer(exmaData[index].UserAnswer);
                                                        setShowNumber(false);
                                                    }}
                                                >
                                                    {index + 1}
                                                </view>
                                            )
                                    )}
                                </view>
                            </TabPane>
                        ))}
                    </Tabs>
                </view>
            </Popup>

            {/* Floating action button to open question selector */}
            <view
                className="fab-button"
                onClick={() => {
                    setShowNumber(true);
                }}
            >
                <List />
            </view>
        </>
    );
};
export default App;
