page {
    background-color: #f0f0f0;
}

.tui-goods__bar {
    position: fixed;
    z-index: 10;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
}

.tui-goods__bar::before {
    content: "";
    width: 100%;
    border-top: 1px solid #eaeef1;
    position: absolute;
    top: 0;
    left: 0;
    transform: scaleY(0.5);
    transform-origin: 0 0;
}

.tui-goods__bar-inner {
    width: 100%;
    height: 100rpx;
    padding: 0 30rpx;
    box-sizing: border-box;
}

.tui--flex {
    display: flex;
    align-items: center;
}

.tui-flex--between {
    justify-content: space-between;
}

.tui-flex--end {
    justify-content: flex-end;
}

.tui-price--box {
    padding-right: 30rpx;
}

.tui-bp__tit {
    width: 100%;
    padding: 30rpx;
    box-sizing: border-box;
    position: relative;
    font-weight: 500;
}

.tui-icon--close {
    position: absolute;
    right: 30rpx;
    top: 30rpx;
    // padding: 8rpx;
}

.tui-bp__content {
    width: 100%;
    padding: 24rpx 50rpx 30rpx;
    box-sizing: border-box;
}

.number_content {
    width: 85rpx;
    height: 85rpx;
    color: #4a4a4a;
    font-size: 13px;
    border: 1px solid #eee;
    text-align: center;
    float: left;
    box-sizing: border-box;
    cursor: pointer;
    border-radius: 15rpx;
    margin: 0 15rpx 15rpx 0;
    font-size: 28rpx;
    line-height: 70rpx;
}

.nut-tabs.vertical .nut-tabs__titles {
    width: 80px;
}

.tui-list-view .thorui-radio {
    width: 100%;
    height: 100%;
}

.thorui-align__center {
    display: flex;
    align-items: center;
}

.thorui-padding {
    padding: 30rpx;
    box-sizing: border-box;
    word-break: break-all;
}

.tui-list-cell {
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

.tui-line-left::after {
    left: 30rpx !important;
}

.tui-line-right::after {
    right: 30rpx !important;
}

.tui-list-cell::after {
    content: "";
    position: absolute;
    border-bottom: 1px solid #eaeef1;
    -webkit-transform: scaleY(0.5) translateZ(0);
    transform: scaleY(0.5) translateZ(0);
    transform-origin: 0 100%;
    bottom: 0;
    right: 0;
    left: 0;
    pointer-events: none;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: rgb(234, 238, 241);
}

.tui-order--list {
    width: 100%;
    padding: 30rpx 30rpx 0;
    box-sizing: border-box;
}

// Enhanced exam page styling
.exam-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    background-color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    margin-bottom: 32rpx;
    border-radius: 16rpx;
}

.exam-progress {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-right: 32rpx;
}

.progress-text {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 8rpx;
}

.progress-bar {
    width: 100%;
    height: 12rpx;
    background-color: #e0e0e0;
    border-radius: 6rpx;
    overflow: hidden;
}

.progress-filled {
    height: 100%;
    background-color: #4a90e2;
    border-radius: 6rpx;
    transition: width 0.3s ease;
}

.exam-timer {
    display: flex;
    align-items: center;
    padding: 16rpx 24rpx;
    background-color: #f0f7ff;
    border-radius: 40rpx;
    transition: all 0.3s ease;
}

.exam-timer-alert {
    background-color: #fff3e0;
    animation: pulse 1s infinite;
}

.exam-timer-warning {
    background-color: #ffebee;
    animation: pulse 0.7s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.timer-icon {
    margin-right: 12rpx;
    font-size: 32rpx;
}

.timer-text {
    font-size: 32rpx;
    font-weight: 500;
    font-family: monospace;
}

.question-card {
    background-color: #fff;
    border-radius: 24rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
    padding: 32rpx;
    margin: 0 32rpx 40rpx;
}

.question-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24rpx;
    align-items: center;
}

.question-number {
    font-size: 28rpx;
    color: #999;
    font-weight: 500;
}

.question-type {
    font-size: 24rpx;
    color: #fff;
    background-color: #4a90e2;
    border-radius: 24rpx;
    padding: 8rpx 16rpx;
}

.question-content {
    font-size: 32rpx;
    line-height: 1.6;
    color: #333;
    margin-bottom: 32rpx;
}

.question-image {
    display: flex;
    justify-content: center;
    margin: 32rpx 0;
}

.question-image image {
    max-width: 100%;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.answer-options {
    margin: 0 32rpx 48rpx;
}

.answer-option {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    position: relative;
}

.answer-option.selected {
    background-color: #ecf5ff;
    border-left: 8rpx solid #4a90e2;
}

.answer-option.correct {
    background-color: #f0f8f0;
    border-left: 8rpx solid #50a14f;
}

.answer-option.incorrect {
    background-color: #fff0f0;
    border-left: 8rpx solid #fa2c19;
}

.option-letter {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 56rpx;
    height: 56rpx;
    background-color: #e0e0e0;
    color: #333;
    border-radius: 50%;
    margin-right: 24rpx;
    font-weight: 500;
}

.true-option {
    background-color: #e6f7e6;
    color: #50a14f;
}

.false-option {
    background-color: #ffebee;
    color: #fa2c19;
}

.option-text {
    margin-right: 16rpx;
}

.correct-marker {
    position: absolute;
    right: 32rpx;
    color: #50a14f;
    font-size: 36rpx;
}

.navigation-buttons {
    display: flex;
    justify-content: space-between;
    padding: 0 32rpx;
    margin-bottom: 48rpx;
}

.nav-button {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;
    border: none;
    margin: 0 16rpx;
    transition: all 0.2s ease;
}

.nav-button.disabled {
    opacity: 0.6;
}

.prev-button {
    background-color: #f5f5f5;
    color: #333;
}

.next-button {
    background-color: #4a90e2;
    color: #fff;
}

.submit-button {
    height: 88rpx;
    border-radius: 44rpx;
    background-color: #fa2c19;
    color: #fff;
    border: none;
    margin: 0 16rpx;
    transition: all 0.2s ease;
    flex: 1;
}

.button-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

.button-icon {
    margin: 0 8rpx;
}

.button-text {
    font-size: 28rpx;
}

.question-selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1rpx solid #eee;
    font-size: 32rpx;
    font-weight: 500;
}

.close-icon {
    padding: 16rpx;
}

.question-tabs {
    height: 600rpx;
}

.question-grid {
    display: flex;
    flex-wrap: wrap;
    padding: 32rpx;
}

.grid-item {
    width: 80rpx;
    height: 80rpx;
    margin: 12rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 16rpx;
    background-color: #f5f5f5;
    color: #333;
    font-size: 28rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.grid-item.current {
    transform: scale(1.1);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
    z-index: 1;
}

.grid-item.answered {
    background-color: #4a90e2;
    color: #fff;
}

.grid-item.correct {
    background-color: #50a14f;
    color: #fff;
}

.grid-item.incorrect {
    background-color: #fa2c19;
    color: #fff;
}

.fab-button {
    position: fixed;
    right: 40rpx;
    bottom: 160rpx;
    width: 112rpx;
    height: 112rpx;
    border-radius: 56rpx;
    background-color: #4a90e2;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 8rpx 20rpx rgba(74, 144, 226, 0.3);
    z-index: 100;
    transition: all 0.2s ease;
}

.fab-button:active {
    transform: scale(0.95);
}
