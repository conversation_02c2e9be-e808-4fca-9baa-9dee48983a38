import Taro, { useDidHide, useDidShow } from "@tarojs/taro";
import { useState } from "react";
import { Progress } from "@nutui/nutui-react-taro";
import "./index.scss";
import "@utils/app.scss";
import { View, Image, Text } from "@tarojs/components";

import { login } from "@utils/login";
import { getLoading, getMsg } from "@utils/util";
import request from "@service/request";
import Layout from "@/components/Layout";
import { message } from "@/components/MessageApi/MessageApiSingleton";

/**
 * 6. 签署完成 数据上传页面
 * @returns
 */
const App = () => {
    const [videoList, setVideoList] = useState<
        {
            CoverUrl: string;
            Id: string;
            TimeLength: number;
            PlayTime: number;
            VideoName: string;
            Vid: string;
            NoPlay: boolean;
        }[]
    >([]);

    /**
     * 页面 ready 方法
     */
    useDidShow(() => {
        message.openLoading("正在加载数据");
        login().then(() => {
            GetVideoList();
            message.closeLoading();
        });
    });

    /**
     * 页面销毁 的方法
     */
    useDidHide(() => {});

    const GetVideoList = () => {
        request
            .post<
                API.Result<
                    {
                        CoverUrl: string;
                        Id: string;
                        TimeLength: number;
                        PlayTime: number;
                        VideoName: string;
                        Vid: string;
                        NoPlay: boolean;
                    }[]
                >
            >("/HNJiaXie/HnjxStudy/GetVideoList", {})
            .then((json) => {
                if (json.success) {
                    setVideoList(json.data);
                } else {
                    message.error(json.message, "读取失败", () => {
                        Taro.navigateTo({
                            url: "/subpackages/hnjpxh/login/index",
                        });
                    });
                }
            });
    };

    const goVideo = (Id) => {
        Taro.navigateTo({
            url: "/subpackages/hnjpxh/study/index?Id=" + Id,
        });
    };

    // 计算视频播放进度百分比，最大为100%
    const calculateProgress = (playTime, timeLength) => {
        const progress = parseFloat(((playTime / timeLength) * 100).toFixed(2));
        return progress > 100 ? 100 : progress;
    };

    return (
        <Layout>
            <View className="video-list-container">
                <View className="video-grid">
                    {videoList.map((item, key) => {
                        const progress = calculateProgress(item.PlayTime, item.TimeLength);
                        return (
                            <View
                                key={key}
                                className="video-item"
                                onClick={() => {
                                    if (!item.NoPlay) {
                                        goVideo(item.Id);
                                    }
                                }}
                            >
                                <View className="video-thumbnail-wrapper">
                                    <Image src={item.CoverUrl} className="video-thumbnail" mode="aspectFill" />
                                    <View className={`status-indicator ${progress >= 100 ? "completed" : "in-progress"}`} />
                                    <View className="progress-mask">
                                        <Text className="progress-text">{progress}%</Text>
                                    </View>
                                </View>
                                <View className="video-title">
                                    <Text className={`title-text ${item.NoPlay ? "disabled" : ""}`}>{item.VideoName}</Text>
                                </View>
                            </View>
                        );
                    })}
                </View>
            </View>
        </Layout>
    );
};
export default App;
