import Taro, { useDidHide, useDidShow, useRouter } from "@tarojs/taro";
import { useState } from "react";
import "./index.scss";
import "@utils/app.scss";
import { Video, View, Text, Image } from "@tarojs/components";
import request from "@service/request";
import Layout from "@/components/Layout";
import { message } from "@/components/MessageApi/MessageApiSingleton";

/**
 * 在线培训视频学习页面
 * @returns
 */
const App = () => {
    const router = useRouter();

    const [src, setSrc] = useState("");
    const [videoName, setVideoName] = useState("");
    const [loading, setLoading] = useState(true);
    const [coverUrl, setCoverUrl] = useState("");
    const [isPaused, setIsPaused] = useState(false);
    const [showControls, setShowControls] = useState(false);

    /**
     * 页面 ready 方法
     */
    useDidShow(() => {
        try {
            if (!router.params.Id || router.params.Id == "") {
                message.error("请参加线下学习，及时录入学习记录", "页面参数错误", () => {
                    Taro.navigateTo({
                        url: "/subpackages/hnjpxh/studyList/index",
                    });
                });
            } else {
                GetVideoDetail();
            }
        } catch (e) {
            console.log("error");
            console.log(e);
        }
    });

    /**
     * 获取视频的详情信息
     */
    const GetVideoDetail = () => {
        setLoading(true);
        request
            .post<
                API.Result<{
                    CoverUrl: string;
                    Id: string;
                    TimeLength: number;
                    PlayTime: number;
                    VideoName: string;
                    VideoUrl2: string;
                }>
            >("/HNJiaXie/HnjxStudy/GetVideoDetail", {
                Id: router.params.Id,
            })
            .then((json) => {
                setLoading(false);
                if (json.success) {
                    setSrc(json.data.VideoUrl2);
                    setVideoName(json.data.VideoName);
                    setCoverUrl(json.data.CoverUrl);
                    setTotalTimeLength(json.data.TimeLength);

                    let pandaVideo = Taro.createVideoContext("pandaVideo", this);
                    setCurrentTime(json.data.PlayTime);
                    setPlayTime(json.data.PlayTime);
                    setIsPaused(true); // 初始状态设为暂停

                    // 短暂延迟播放视频，让用户看到封面
                    setTimeout(() => {
                        pandaVideo.play();
                        setIsPaused(false);
                    }, 500);
                } else {
                    message.error(json.message, "读取失败", () => {
                        Taro.navigateBack();
                    });
                }
            });
    };

    /**
     * 页面销毁 的方法
     */
    useDidHide(() => {
        // 页面隐藏时上传播放时间
        if (totalTime > 0) {
            uploadTime(totalTime / 4);
        }
    });

    const [totalTime, setTotalTime] = useState(0);
    const [currentTime, setCurrentTime] = useState(0);
    const [totalTimeLength, setTotalTimeLength] = useState(0);

    /**
     * 播放器 时间更新的时候 回调的方法
     * @param e
     */
    const timeUpdate = (e) => {
        if (e.detail.currentTime - currentTime > 2) {
            let pandaVideo = Taro.createVideoContext("pandaVideo", this);
            pandaVideo.seek(currentTime);
            pandaVideo.stop();
            message.error("请勿快进", "操作错误", () => {
                pandaVideo.play();
                setIsPaused(false);
            });
        } else {
            setCurrentTime(e.detail.currentTime);
        }
        setTotalTime(totalTime + 1);

        if (totalTime == 40) {
            //10秒记录一次
            setTotalTime(0);
            uploadTime(10);
        }
    };

    /**
     * 视频播放完毕
     */
    const playEnd = () => {
        let timeLength = totalTime / 4;
        setTotalTime(0);
        uploadTime(timeLength);
        setIsPaused(true);

        // 显示重播提示
        Taro.showToast({
            title: "视频播放完毕",
            icon: "success",
            duration: 2000,
        });
    };

    /**
     * 播放时间的 State
     */
    const [playTime, setPlayTime] = useState(0);

    /**
     * 上传 Play 的时间
     * @param timeLength 秒数
     */
    const uploadTime = (timeLength) => {
        request.post<API.Result<{}>>("/HNJiaXie/HnjxStudy/UploadPlayVideoTimeLength", {
            VideoId: router.params.Id,
            TimeLength: parseInt(timeLength),
            currentTime: currentTime,
        });
    };

    /**
     * 格式化时间为 mm:ss
     */
    const formatTime = (time) => {
        const minutes = Math.floor(time / 60);
        const seconds = Math.floor(time % 60);
        return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    };

    // 计算进度百分比
    const progressPercent = totalTimeLength > 0 ? (currentTime / totalTimeLength) * 100 : 0;

    // 视频播放暂停控制
    const handlePlayPause = () => {
        let pandaVideo = Taro.createVideoContext("pandaVideo", this);
        if (isPaused) {
            pandaVideo.play();
        } else {
            pandaVideo.pause();
        }
        setIsPaused(!isPaused);
    };

    // 处理进度条点击，跳转到指定位置
    const handleProgressClick = (e) => {
        // 由于进度条操作涉及快进，这里暂不实现
        // 实际应用中需要根据业务需求决定是否允许用户通过进度条跳转
    };

    // 显示/隐藏控制条
    const toggleControls = () => {
        setShowControls(!showControls);
    };

    return (
        <Layout>
            <View className="video-study-page">
                <View className="video-container">
                    <View className="video-title">{videoName}</View>
                    {src !== "" ? (
                        <>
                            <View className="video-wrapper" onClick={toggleControls}>
                                {coverUrl && loading && <Image className="video-cover" src={coverUrl} mode="aspectFill" />}
                                <Video
                                    id="pandaVideo"
                                    className="video-player"
                                    src={src}
                                    onTimeUpdate={timeUpdate}
                                    onEnded={playEnd}
                                    showBottomProgress={false}
                                    initialTime={playTime}
                                    controls={false}
                                    onPlay={() => setIsPaused(false)}
                                    onPause={() => setIsPaused(true)}
                                />

                                {/* 自定义控制按钮 */}
                                <View
                                    className={`video-controls ${showControls ? "show" : ""}`}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        toggleControls();
                                    }}
                                >
                                    <View
                                        className="play-pause-btn"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handlePlayPause();
                                        }}
                                    >
                                        <View className={`control-icon ${isPaused ? "play-icon" : "pause-icon"}`}></View>
                                    </View>
                                </View>

                                {/* 大型播放/暂停按钮覆盖 */}
                                <View
                                    className={`center-play-btn ${isPaused ? "visible" : ""}`}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handlePlayPause();
                                    }}
                                >
                                    <View className="play-icon-large"></View>
                                </View>
                            </View>
                            <View className="video-info">
                                <View className="progress-bar" onClick={handleProgressClick}>
                                    <View className="progress-inner" style={{ width: `${progressPercent}%` }} />
                                </View>
                                <View className="progress-info">
                                    <Text>{formatTime(currentTime)}</Text>
                                    <Text>{formatTime(totalTimeLength)}</Text>
                                </View>

                                {/* 播放控制按钮 */}
                                <View className="playback-controls">
                                    <View className={`control-btn ${isPaused ? "play" : "pause"}`} onClick={handlePlayPause}>
                                        {isPaused ? "播放" : "暂停"}
                                    </View>
                                </View>

                                {/* 学习提示 */}
                                <View className="study-tips">
                                    <Text className="tip-text">学习进度已自动保存</Text>
                                    <Text className="tip-text">已学习: {Math.floor(progressPercent)}%</Text>
                                </View>
                            </View>
                        </>
                    ) : (
                        <View
                            style={{
                                height: "420rpx",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                color: "#999",
                                fontSize: "28rpx",
                                flexDirection: "column",
                            }}
                        >
                            {loading ? (
                                <>
                                    <View className="loading-icon"></View>
                                    <Text style={{ marginTop: "20rpx" }}>视频加载中...</Text>
                                </>
                            ) : (
                                <Text>暂无视频</Text>
                            )}
                        </View>
                    )}
                </View>
            </View>
        </Layout>
    );
};
export default App;
