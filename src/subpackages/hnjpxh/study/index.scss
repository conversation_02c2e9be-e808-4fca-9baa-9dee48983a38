/* 视频学习页面样式 */
.video-study-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 30rpx;
    position: relative;

    /* 背景图案 */
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: radial-gradient(circle at 10% 20%, rgba(22, 119, 255, 0.05) 0%, transparent 20%),
            radial-gradient(circle at 90% 80%, rgba(22, 119, 255, 0.05) 0%, transparent 20%), radial-gradient(circle at 50% 50%, rgba(22, 119, 255, 0.03) 0%, transparent 30%);
        pointer-events: none;
        z-index: 0;
    }

    .video-container {
        position: relative;
        z-index: 1;
        margin-top: 30rpx;
        border-radius: 24rpx;
        overflow: hidden;
        box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.12);
        background: #fff;
        padding-bottom: 30rpx;
        transition: transform 0.3s ease, box-shadow 0.3s ease;

        &:active {
            transform: translateY(2rpx);
            box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
        }
    }

    .video-title {
        padding: 30rpx;
        font-size: 34rpx;
        font-weight: 600;
        color: #333;
        background: #fff;
        border-bottom: 2rpx solid #f0f0f0;
        display: flex;
        align-items: center;

        &::before {
            content: "";
            display: block;
            width: 8rpx;
            height: 34rpx;
            background: #1677ff;
            border-radius: 4rpx;
            margin-right: 16rpx;
        }
    }

    .video-wrapper {
        position: relative;
        width: 100%;
        height: 420rpx;
        background: #000;
        overflow: hidden;
    }

    .video-cover {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        object-fit: cover;
        opacity: 0.7;
        animation: fadeBg 0.5s forwards;
    }

    .video-player {
        width: 100%;
        height: 420rpx;
        background: #000;
        z-index: 2;
        position: relative;
    }

    /* 视频控制按钮 */
    .video-controls {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 80rpx;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        padding: 0 20rpx;
        opacity: 0;
        transform: translateY(80rpx);
        transition: all 0.3s ease;
        z-index: 10;

        &.show {
            opacity: 1;
            transform: translateY(0);
        }

        .play-pause-btn {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20rpx;

            &:active {
                background: rgba(255, 255, 255, 0.3);
            }

            .control-icon {
                width: 30rpx;
                height: 30rpx;
                position: relative;

                &.play-icon {
                    &:before {
                        content: "";
                        position: absolute;
                        width: 0;
                        height: 0;
                        top: 0;
                        left: 5rpx;
                        border-top: 15rpx solid transparent;
                        border-bottom: 15rpx solid transparent;
                        border-left: 25rpx solid #fff;
                    }
                }

                &.pause-icon {
                    &:before,
                    &:after {
                        content: "";
                        position: absolute;
                        width: 8rpx;
                        height: 30rpx;
                        background: #fff;
                        top: 0;
                    }

                    &:before {
                        left: 8rpx;
                    }

                    &:after {
                        right: 8rpx;
                    }
                }
            }
        }
    }

    /* 中央大型播放按钮 */
    .center-play-btn {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.9);
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        background: rgba(22, 119, 255, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 5;
        pointer-events: none;

        &.visible {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
            pointer-events: auto;
        }

        .play-icon-large {
            width: 0;
            height: 0;
            margin-left: 10rpx;
            border-top: 30rpx solid transparent;
            border-bottom: 30rpx solid transparent;
            border-left: 50rpx solid #fff;
        }

        &:active {
            background: rgba(22, 119, 255, 0.9);
            transform: translate(-50%, -50%) scale(0.95);
        }
    }

    .video-info {
        padding: 20rpx 30rpx 30rpx;
        display: flex;
        flex-direction: column;
        background: #fff;
        border-radius: 0 0 24rpx 24rpx;

        .progress-info {
            display: flex;
            justify-content: space-between;
            margin-top: 10rpx;
            font-size: 26rpx;
            color: #666;
        }

        .progress-bar {
            height: 10rpx;
            background: #eee;
            border-radius: 10rpx;
            margin: 20rpx 0 10rpx;
            position: relative;
            overflow: hidden;

            .progress-inner {
                position: absolute;
                left: 0;
                top: 0;
                height: 100%;
                background: linear-gradient(to right, #4fa1ff, #1677ff);
                border-radius: 10rpx;
                transition: width 0.3s ease-out;

                &::after {
                    content: "";
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 14rpx;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.6);
                    border-radius: 50%;
                    box-shadow: 0 0 6rpx rgba(255, 255, 255, 0.8);
                    animation: pulse 1.5s infinite;
                }
            }
        }

        /* 播放控制区域 */
        .playback-controls {
            display: flex;
            justify-content: center;
            margin: 20rpx 0;

            .control-btn {
                width: 160rpx;
                height: 60rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #1677ff;
                color: #fff;
                border-radius: 30rpx;
                font-size: 28rpx;
                position: relative;
                transition: all 0.2s ease;

                &:active {
                    transform: translateY(2rpx);
                    background: #0e5ecc;
                }

                &.play {
                    background: #1677ff;

                    &:before {
                        content: "▶";
                        margin-right: 6rpx;
                        font-size: 24rpx;
                    }
                }

                &.pause {
                    background: #f5f5f5;
                    color: #333;

                    &:before {
                        content: "⏸";
                        margin-right: 6rpx;
                        font-size: 24rpx;
                    }
                }
            }
        }

        .study-tips {
            display: flex;
            justify-content: space-between;
            margin-top: 20rpx;
            padding-top: 20rpx;
            border-top: 2rpx dashed #eee;

            .tip-text {
                font-size: 24rpx;
                color: #888;
                display: flex;
                align-items: center;

                &:last-child {
                    color: #1677ff;
                    font-weight: 500;
                }
            }
        }
    }

    .loading-icon {
        width: 60rpx;
        height: 60rpx;
        border: 6rpx solid #f3f3f3;
        border-top: 6rpx solid #1677ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
}

/* 加载中动画 */
@keyframes pulse {
    0% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.6;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes fadeBg {
    0% {
        opacity: 0.7;
    }
    100% {
        opacity: 0;
    }
}
