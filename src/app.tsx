import React, { Component, PropsWithChildren } from "react";
import Taro, { UpdateManager } from "@tarojs/taro";
import "./app.scss";
import { getCurrentInstance } from "@tarojs/runtime";
import { resetMessageApi } from "./components/MessageApi/MessageApiSingleton";
import { applyNutUIFixes } from "./utils/nutui-fixes";

class App extends Component<PropsWithChildren<{}>> {
    private handleUpdateConfirm = (update: UpdateManager) => {
        Taro.showModal({
            title: "更新提示",
            content: "新版本已经准备好，是否重启应用？",
            success: (res) => {
                if (res.confirm) {
                    update.applyUpdate();
                }
            },
        });
    };

    private handleUpdateFailed = () => {
        Taro.showModal({
            title: "已经有新版本了",
            content: "新版本已经上线，请您删除当前小程序，重新打开。",
        });
    };

    private showVersionError = () => {
        Taro.showModal({
            title: "提示",
            content: "当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。",
        });
    };

    private checkForUpdates = () => {
        console.log("  ===== >>>  checkForUpdates ");
        if (!Taro.canIUse("getUpdateManager")) {
            this.showVersionError();
            return;
        }

        const update = Taro.getUpdateManager();

        update.onCheckForUpdate((res) => {
            if (res.hasUpdate) {
                update.onUpdateReady(() => this.handleUpdateConfirm(update));
                update.onUpdateFailed(this.handleUpdateFailed);
            }
        });
    };

    componentDidMount() {
        console.log("  ===== >>>  componentDidMount ");
        if (Taro.getEnv() === "WEAPP") {
            this.checkForUpdates();
        }

        // 设置全局页面显示监听
        Taro.eventCenter.on("__taroRouterChange", this.handleRouterChange);

        // 小程序页面生命周期监听可以通过Taro的页面监听钩子实现
        Taro.eventCenter.on("__taroRouterBeforeChange", () => {
            console.log("路由变化前");
            resetMessageApi();
        });

        // 添加页面显示事件监听
        Taro.eventCenter.on("__taroRouterAfterChange", () => {
            console.log("路由变化后");
            // 触发显示事件
            Taro.eventCenter.trigger("taroGlobalShowEvent");
            resetMessageApi();
        });
    }

    // 页面路由变化处理
    handleRouterChange = (params: any) => {
        console.log("路由变化:", params);
        // 在路由变化时重置状态，但保留loading
        resetMessageApi(false); // 不关闭loading
    };

    onLaunch() {
        console.log("  ===== >>>  onLaunch ");

        // Apply fixes for NutUI in mini-program environments as early as possible
        if (Taro.getEnv() === "WEAPP") {
            applyNutUIFixes();
        }
    }

    componentDidShow() {
        console.log("  ===== >>>  componentDidShow ");
        // 触发全局事件
        Taro.eventCenter.trigger("taroGlobalShowEvent");
        resetMessageApi(false); // 不关闭loading
    }

    componentDidHide() {
        console.log("  ===== >>>  componentDidHide ");
        // 触发全局事件
        Taro.eventCenter.trigger("taroGlobalHideEvent");
        resetMessageApi(false); // 不关闭loading
    }

    onError(err: string) {
        console.error("App onError: ", err);
    }

    onPageNotFound(res: any) {
        console.error("App onPageNotFound: ", res);
    }

    onUnhandledRejection(res: any) {
        console.error("App onUnhandledRejection: ", res);
    }

    refreshCurrentPage = () => {
        const instance = getCurrentInstance();
        const currentPath = instance.router?.path || "";
        const currentParams = instance.router?.params || {};

        // Convert params object to query string
        const queryParams = Object.keys(currentParams)
            .map((key) => `${key}=${currentParams[key]}`)
            .join("&");

        const url = queryParams ? `/${currentPath}?${queryParams}` : `/${currentPath}`;

        console.log("Refreshing page:", url);
        // 使用reLaunch前先重置状态，但保留loading
        resetMessageApi(false); // 不关闭loading
        Taro.reLaunch({
            url: url,
        });
    };

    render() {
        return <>{this.props.children}</>;
    }

    checkIsLoginPage = () => {
        const instance = getCurrentInstance();
        const currentPath = instance.router?.path || "";

        console.log('currentPath.toLowerCase().includes("login")');
        console.log(currentPath.toLowerCase().includes("login"));
        // 判断路径中是否包含 login
        return currentPath.toLowerCase().includes("login");
    };

    componentWillUnmount() {
        // 移除路由监听
        Taro.eventCenter.off("__taroRouterChange", this.handleRouterChange);
        Taro.eventCenter.off("__taroRouterBeforeChange");
        Taro.eventCenter.off("__taroRouterAfterChange");
    }
}

export default App;
