import React, { useState, forwardRef, useImperativeHandle } from "react";
import "./index.scss";
import "@utils/app.scss";
import { login } from "@utils/login";
import { Image } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { Button, Empty, Popup, Step, Steps } from "@nutui/nutui-react-taro";
import { getDomain } from "@service/base";
import request from "@service/request";
import moment from "moment";
import upload from "@service/upload";
import { Close } from "@nutui/icons-react-taro";
import { message } from "../MessageApi/MessageApiSingleton";
import { QValue } from "@utils/util";

interface StudentDetailProps {
    onClose?: () => void;
    fullScreen?: boolean;
}

export interface StudentDetailRef {
    open: (studentId: string) => void;
}

const StudentDetail = forwardRef<StudentDetailRef, StudentDetailProps>(({ onClose, fullScreen = false }, ref) => {
    const [userInfo, setUserInfo] = React.useState<API.UserInfo>(Object);
    const [studentId, setStudentId] = useState<string>("");
    const [visible, setVisible] = useState<boolean>(false);

    const [studentInfo, setStudentInfo] = useState<any>(undefined);
    const [examList, setExamList] = useState<any[]>([]);
    const [shouldPayList, setShouldPayList] = useState<any[]>([]);
    const [payList, setPayList] = useState<any[]>([]);
    const [imageList, setImageList] = useState<any[]>([]);

    const [step, setStep] = useState(0);
    const [stepDesc, setStepDesc] = useState<any[]>(["", "", "", "", "", ""]);

    const [tabIndex, setTabIndex] = useState(1);
    const weeks = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];

    const imageIds = [2, 4, 6, 0, 7, 21];
    const imageTexts = ["现场拍照", "身份证正反面", "申请表", "寸照(数码回执)", "体检表", "其他资料"];

    const [openTakePhoto, setOpenTakePhoto] = useState(-1);
    const [loading, setLoading] = useState(false);

    // 获取页面参数中的 TenantId
    const tenantId = QValue("TenantId");

    React.useEffect(() => {
        if (!studentId) return;

        login().then((info: API.UserInfo) => {
            setUserInfo(info);
        });
        loadStudentInfo();
    }, [studentId]);

    useImperativeHandle(ref, () => ({
        open: (id: string) => {
            setStudentId(id);
            setVisible(true);
        },
    }));

    const handleClose = () => {
        setVisible(false);
        setStudentId("");
        onClose?.();
    };

    const loadStudentInfo = () => {
        setLoading(true);
        const url = tenantId ? "/Jx/Student/StudentInfo/" + studentId + "/" + tenantId : "/Jx/Student/StudentInfo/" + studentId;
        request
            .post<any>(url, {})
            .then((json) => {
                if (json.success) {
                    setStudentInfo(json.data);

                    if (moment(json.data.RegisterTime) > moment("2000-01-01")) {
                        setStep(1);
                        setStepDesc([moment(json.data.RegisterTime).diff(moment(json.data.RegistrationDate), "days") + "天", "", "", "", "", ""]);
                    }

                    let startDate = moment(json.data.RegistrationDate);
                    if (moment(json.data.KeMu1Date) > moment("2000-01-01")) {
                        setStep(2);
                        setStepDesc(["", moment(json.data.KeMu1Date).diff(moment(json.data.RegistrationDate), "days") + "天", "", "", "", ""]);
                        startDate = moment(json.data.KeMu1Date);
                    }

                    if (moment(json.data.KeMu2Date) > moment("2000-01-01")) {
                        setStep(3);
                        setStepDesc(["", "", moment(json.data.KeMu2Date).diff(startDate, "days") + "天", "", "", ""]);
                    }
                    if (moment(json.data.KeMu3Date) > moment("2000-01-01")) {
                        setStep(4);
                        setStepDesc(["", "", "", moment(json.data.KeMu3Date).diff(startDate, "days") + "天", "", ""]);
                    }
                    if (moment(json.data.KeMu4Date) > moment("2000-01-01")) {
                        setStep(5);
                        setStepDesc(["", "", "", "", moment(json.data.KeMu4Date).diff(startDate, "days") + "天", ""]);
                    }

                    if (json.data.KeMu4Text == "合格" || json.data.StatusText == "毕业") {
                        setStep(6);

                        if (moment(json.data.KeMu3Date) > moment("2000-01-01")) {
                            setStepDesc(["", "", "", "", "", moment(json.data.KeMu3Date).diff(startDate, "days") + "天"]);
                        }
                        if (moment(json.data.KeMu4Date) > moment("2000-01-01")) {
                            setStepDesc(["", "", "", "", "", moment(json.data.KeMu4Date).diff(startDate, "days") + "天"]);
                        }
                    }

                    getMyExamList();
                } else {
                    setLoading(false);
                    message.error("加载失败", json.message || "获取学员信息失败");
                }
            })
            .catch((error) => {
                setLoading(false);
                message.error("加载失败", "网络请求失败");
            });
    };

    const getMyExamList = () => {
        request
            .post<any>("/Jx/Exam/Result/getMyExamList", {
                StudentId: studentId,
                ...(tenantId && { TenantId: tenantId }),
            })
            .then((json) => {
                if (json && json.success) {
                    setExamList(json.data);
                }
                getMyShouldPayList();
                getStudentImages();
            });
    };

    const getMyShouldPayList = () => {
        request
            .post<any>("/Jx/Pay/My/JxShouldPay/getMyShouldPayList", {
                StudentId: studentId,
                ...(tenantId && { TenantId: tenantId }),
            })
            .then((json) => {
                if (json && json.success) {
                    setShouldPayList(json.data.data);
                    getMyPayList();
                }
            });
    };

    const getMyPayList = () => {
        request
            .post<any>("/Jx/Pay/My/JxPay/getMyPayList", {
                StudentId: studentId,
                ...(tenantId && { TenantId: tenantId }),
            })
            .then((json) => {
                if (json && json.success) {
                    setPayList(json.data.data);
                }
            });
    };

    const getStudentImages = () => {
        request
            .post<any>("/Jx/Image/StudentImage/getStudentImages", {
                Id: studentId,
                ...(tenantId && { TenantId: tenantId }),
            })
            .then((json) => {
                if (json && json.success) {
                    setImageList(json.data);
                }
                setLoading(false);
            })
            .catch((error) => {
                setLoading(false);
                console.error("获取学员图片失败:", error);
            });
    };

    const chooseImage = (imageId, sourceType) => {
        setOpenTakePhoto(-1);
        Taro.chooseImage({
            count: imageId == 4 ? 2 : 1,
            sizeType: ["original"],
            sourceType: sourceType,
            success: (res) => {
                if (imageId == 4) {
                    if (res.tempFilePaths.length == 1) {
                        message.openToast("请同时选择身份证正反面两张图片");
                    } else {
                        message.openLoading("正在上传第一张");
                        upload
                            .post<
                                API.Result<{
                                    data: string;
                                }>
                            >(
                                "/Jx/Image/StudentImage/uploadStudentImage",
                                {
                                    StudentId: studentId,
                                    ImageId: 40,
                                },
                                res.tempFilePaths[0],
                                "file"
                            )
                            .then((json) => {
                                message.openLoading("正在上传第二张");
                                upload
                                    .post<
                                        API.Result<{
                                            data: string;
                                        }>
                                    >(
                                        "/Jx/Image/StudentImage/uploadStudentImage",
                                        {
                                            StudentId: studentId,
                                            ImageId: 41,
                                        },
                                        res.tempFilePaths[1],
                                        "file"
                                    )
                                    .then((json) => {
                                        message.openLoading("正在合成正反面");
                                        request
                                            .post<API.Result<string>>("/Jx/Image/StudentImage/makeMyIdCard", {
                                                Id: studentId,
                                            })
                                            .then((json) => {
                                                message.closeLoading();
                                                if (json && json.success) {
                                                    getStudentImages();
                                                } else {
                                                    message.error("上传失败", json.message);
                                                }
                                            });
                                    });
                            });
                    }
                } else {
                    message.openLoading("正在上传图片");
                    upload
                        .post<
                            API.Result<{
                                data: string;
                            }>
                        >(
                            "/Jx/Image/StudentImage/uploadStudentImage",
                            {
                                StudentId: studentId,
                                ImageId: imageId,
                            },
                            res.tempFilePaths[0],
                            "file"
                        )
                        .then((json) => {
                            message.closeLoading();
                            if (json && json.success) {
                                getStudentImages();
                            } else {
                                message.error("上传失败", json.message);
                            }
                        });
                }
            },
        });
    };

    const renderContent = () => (
        <view
            style={{
                width: "100%",
                maxWidth: fullScreen ? "100vw" : "750rpx",
                height: fullScreen ? "100vh" : "90vh",
                backgroundColor: "#fff",
                display: "flex",
                flexDirection: "column",
                position: "relative",
            }}
        >
            {!fullScreen && (
                <view
                    style={{
                        position: "absolute",
                        top: "30rpx",
                        right: "30rpx",
                        zIndex: "1000",
                        width: "60rpx",
                        height: "60rpx",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        backgroundColor: "rgba(0, 0, 0, 0.05)",
                        borderRadius: "50%",
                    }}
                    onClick={handleClose}
                >
                    <Close />
                </view>
            )}

            {loading && (
                <view
                    style={{
                        width: "100%",
                        height: "100%",
                        background: "#eff1f7",
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center",
                    }}
                >
                    <view className="custom-spinner" />
                    <view style={{ fontSize: "32rpx", color: "#333", marginTop: "30rpx" }}>正在加载学员信息...</view>
                </view>
            )}

            {!loading && (
                <view
                    className="scroll-container"
                    style={{
                        padding: `30rpx 30rpx 0`,
                        paddingBottom: `calc(30rpx + env(safe-area-inset-bottom, 30rpx))`,
                        paddingTop: fullScreen ? "20rpx" : "30rpx",
                        backgroundColor: "#eff1f7",
                    }}
                >
                    <view className="flex-col relative">
                        <view className="flex-row justify-between items-center mb-20">
                            <text className="font_8 text_18" style={{ height: "40rpx" }}></text>
                        </view>
                        <view className="flex-col group_4">
                            <view className="flex-col">
                                <view className="flex-col section_2">
                                    <view className="flex-row justify-between items-end relative group_2">
                                        {studentId != "" && studentInfo && (
                                            <Image
                                                className="image_5"
                                                src={`${getDomain()}/Jx/Image/StudentImage/getStudentZp?Id=${studentId}&TenantId=${studentInfo?.TenantId}`}
                                            />
                                        )}
                                        {(studentId == "" || !studentInfo) && <Image className="image_5" src={`https://cdn.51panda.com/avator.jpg`} />}
                                        <view
                                            className="flex-row items-center section_3"
                                            style={{
                                                top: "80rpx",
                                                position: "absolute",
                                                right: "0",
                                            }}
                                        >
                                            <text
                                                className="font text"
                                                style={{
                                                    margin: "0 10rpx",
                                                }}
                                            >
                                                {studentInfo?.StatusText}
                                            </text>
                                        </view>
                                    </view>
                                    <view className="flex-row items-baseline group_5">
                                        <text className="text_2">{studentInfo?.xm}</text>
                                        <text
                                            className="font_2 text_3 ml-10"
                                            onClick={() => {
                                                message.openAlert("身份证号码", studentInfo?.sfzmhm, "复制号码", () => {
                                                    Taro.setClipboardData({
                                                        data: studentInfo.sfzmhm,
                                                        success: () => {},
                                                        fail: () => {},
                                                    });
                                                });
                                            }}
                                        >
                                            {studentInfo?.sfzmhm
                                                ? studentInfo?.sfzmhm.length == 18
                                                    ? studentInfo?.sfzmhm.substring(0, 4) +
                                                      "*".repeat(studentInfo?.sfzmhm.length - 8) +
                                                      studentInfo?.sfzmhm.substring(studentInfo?.sfzmhm.length - 4)
                                                    : studentInfo?.sfzmhm
                                                : ""}
                                        </text>
                                    </view>
                                    <view className="flex-row items-center group_6">
                                        <view
                                            className="flex-col justify-start items-center text-wrapper"
                                            style={{
                                                width: "auto",
                                                paddingLeft: "15rpx",
                                                paddingRight: "15rpx",
                                            }}
                                        >
                                            <text className="font_3">电话</text>
                                        </view>
                                        <text
                                            className="font_2 text_5 ml-9"
                                            onClick={() => {
                                                message.openAlert("电话号码", studentInfo?.yddh, "拨打电话", () => {
                                                    Taro.makePhoneCall({
                                                        phoneNumber: studentInfo.yddh,
                                                        success: () => {},
                                                        fail: () => {},
                                                    });
                                                });
                                            }}
                                        >
                                            {studentInfo?.yddh
                                                ? studentInfo?.yddh.substring(0, 3) +
                                                  "*".repeat(studentInfo?.yddh.length - 7) +
                                                  studentInfo?.yddh.substring(studentInfo?.yddh.length - 4)
                                                : ""}
                                        </text>
                                    </view>
                                    <view className="flex-row items-center group_7">
                                        <view className="group_8">
                                            <text className="font_4 text_6">业务</text>
                                            <text className="font_4 text_7">状态</text>
                                        </view>
                                        <text
                                            className="font_5 ml-8"
                                            style={{
                                                lineHeight: "30px",
                                            }}
                                        >
                                            {studentInfo?.Ywzt}
                                        </text>
                                    </view>
                                    <Steps
                                        dot
                                        value={step}
                                        style={{
                                            margin: "40rpx -25px 0",
                                        }}
                                    >
                                        <Step value={1} title="注册" description={stepDesc[0]} />
                                        <Step value={2} title="科目一" description={stepDesc[1]} />
                                        <Step value={3} title="科目二" description={stepDesc[2]} />
                                        <Step value={4} title="科目三" description={stepDesc[3]} />
                                        <Step value={5} title="科目四" description={stepDesc[4]} />
                                        <Step value={6} title="拿证" description={stepDesc[5]} />
                                    </Steps>
                                </view>
                            </view>
                            <view className="flex-col group_1 mt-18" style={{ paddingTop: "18rpx" }}>
                                <view className="flex-row items-center self-stretch group_19">
                                    <text
                                        className={tabIndex == 1 ? "text_32" : "font_9 text_33"}
                                        onClick={() => {
                                            setTabIndex(1);
                                        }}
                                    >
                                        详情
                                    </text>
                                    <text
                                        className={tabIndex == 2 ? "text_32 ml-41" : "font_9 text_33 ml-41"}
                                        onClick={() => {
                                            setTabIndex(2);
                                        }}
                                    >
                                        考试
                                    </text>
                                    <text
                                        className={tabIndex == 3 ? "text_32 ml-41" : "font_9 text_33 ml-41"}
                                        onClick={() => {
                                            setTabIndex(3);
                                        }}
                                    >
                                        费用
                                    </text>
                                    <text
                                        className={tabIndex == 4 ? "text_32 ml-41" : "font_9 text_33 ml-41"}
                                        onClick={() => {
                                            setTabIndex(4);
                                        }}
                                    >
                                        照片
                                    </text>
                                    <text
                                        className={tabIndex == 5 ? "text_32 ml-41" : "font_9 text_33 ml-41"}
                                        onClick={() => {
                                            setTabIndex(5);
                                        }}
                                    >
                                        练车
                                    </text>
                                </view>
                                <view
                                    className="self-start section_11"
                                    style={{
                                        marginLeft:
                                            tabIndex == 1
                                                ? "0rpx"
                                                : tabIndex == 2
                                                ? "139rpx"
                                                : tabIndex == 3
                                                ? "275rpx"
                                                : tabIndex == 4
                                                ? "410rpx"
                                                : tabIndex == 5
                                                ? "550rpx"
                                                : "6rpx",
                                        transition: "margin-left 0.3s ease",
                                    }}
                                ></view>
                            </view>
                            {tabIndex == 1 && (
                                <>
                                    <view className="flex-col section_4 mt-12">
                                        <view
                                            className="flex-row justify-between items-center group_15"
                                            style={{
                                                borderTop: 0,
                                            }}
                                        >
                                            <text className="font_8 text_18">报名时间</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.RegistrationDate}</text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">登记地址</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.djzsxxdz}</text>
                                            </view>
                                        </view>
                                    </view>
                                    <view className="flex-col section_4 mt-12">
                                        <view
                                            className="flex-row justify-between items-center group_15"
                                            style={{
                                                borderTop: 0,
                                            }}
                                        >
                                            <text className="font_8 text_18">招生人员</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.SaleUserName}</text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">培训班型</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.JxClassName}</text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">培训车型</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.CarType}</text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">报名店面</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.JxDeptName}</text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">培训场地</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.JxFieldName}</text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">科一教练</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.TeachOneUserName}</text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">科二教练</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.TeachTwoUserName}</text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">科三教练</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.TeachThreeUserName}</text>
                                            </view>
                                        </view>
                                    </view>
                                    <view className="flex-col section_4 mt-12">
                                        <view
                                            className="flex-row justify-between items-center group_15"
                                            style={{
                                                borderTop: 0,
                                            }}
                                        >
                                            <text className="font_8 text_18">注册时间</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">
                                                    {moment(studentInfo?.RegisterTime) > moment("2000-01-01") ? moment(studentInfo?.RegisterTime).format("YYYY-MM-DD") : ""}
                                                </text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">注册驾校</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.RegisterSchoolName}</text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">科一日期</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">
                                                    {moment(studentInfo?.KeMu1Date) > moment("2000-01-01") ? moment(studentInfo?.KeMu1Date).format("YYYY-MM-DD") : ""}
                                                </text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">科一成绩</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.KeMu1Text}</text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">科二日期</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">
                                                    {moment(studentInfo?.KeMu2Date) > moment("2000-01-01") ? moment(studentInfo?.KeMu2Date).format("YYYY-MM-DD") : ""}
                                                </text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">科二成绩</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.KeMu2Text}</text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">科三日期</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">
                                                    {moment(studentInfo?.KeMu3Date) > moment("2000-01-01") ? moment(studentInfo?.KeMu3Date).format("YYYY-MM-DD") : ""}
                                                </text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">科三成绩</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.KeMu3Text}</text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">科四日期</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">
                                                    {moment(studentInfo?.KeMu4Date) > moment("2000-01-01") ? moment(studentInfo?.KeMu4Date).format("YYYY-MM-DD") : ""}
                                                </text>
                                            </view>
                                        </view>
                                        <view className="flex-row justify-between items-center group_15">
                                            <text className="font_8 text_18">科四成绩</text>
                                            <view className="flex-row items-center">
                                                <text className="font_5 text_19">{studentInfo?.KeMu4Text}</text>
                                            </view>
                                        </view>
                                    </view>
                                    <view className="flex-col section_4 mt-12">
                                        <view
                                            className="flex-row justify-between items-center"
                                            style={{
                                                borderTop: 0,
                                                width: "100%",
                                                padding: "31rpx 2.17rpx 33rpx",
                                            }}
                                        >
                                            <text className="font_8 text_18">学员备注</text>
                                        </view>
                                        {studentInfo?.Remark != "" && (
                                            <view
                                                className="flex-row justify-between items-center group_15 font_5 text_19"
                                                style={{
                                                    wordWrap: "break-word",
                                                    whiteSpace: "normal",
                                                    marginLeft: 0,
                                                    lineHeight: "40rpx",
                                                }}
                                            >
                                                {studentInfo?.Remark}
                                            </view>
                                        )}
                                    </view>
                                </>
                            )}
                            {tabIndex == 2 && (
                                <>
                                    {examList.length == 0 && (
                                        <view className="mt-12">
                                            <Empty description="无考试记录" style={{ margin: "0px" }} />
                                        </view>
                                    )}
                                    {examList.length > 0 && (
                                        <view className="exam-list">
                                            {examList.map((item, index) => {
                                                return (
                                                    <view className="exam-item" key={index}>
                                                        <view className="flex-col exam-section_3">
                                                            <view className="exam-flex-row">
                                                                <view className="self-start font_2 text_11">{item.KeMuText}</view>
                                                                <view className="self-end text_11">{item.ResultText}</view>
                                                            </view>
                                                            <view className="self-stretch divider"></view>
                                                            <view className="flex-row self-stretch group_7">
                                                                <text className="font_8 text_18">考试日期：</text>
                                                                <text className="font_5 text_19">
                                                                    {moment(item.ksrq).format("YYYY-MM-DD")} {weeks[moment(item.ksrq).format("d")]}
                                                                </text>
                                                            </view>
                                                            <view className="flex-row self-stretch group_7">
                                                                <text className="font_8 text_18">考试场地：</text>
                                                                <text className="font_5 text_19">{item.kc}</text>
                                                            </view>
                                                            <view className="flex-row self-stretch group_7">
                                                                <text className="font_8 text_18">考场场次：</text>
                                                                <text className="font_5 text_19">{item.cc}</text>
                                                            </view>
                                                            <view className="flex-row self-stretch group_7">
                                                                <text className="font_8 text_18">考试成绩：</text>
                                                                <text className="font_5 text_19">{item.cj}</text>
                                                            </view>
                                                            <view className="flex-row self-stretch group_7">
                                                                <text className="font_8 text_18">考试次数：</text>
                                                                <text className="font_5 text_19">{item.Times}</text>
                                                            </view>
                                                            <view className="flex-row self-stretch group_7">
                                                                <text className="font_8 text_18">考试车型：</text>
                                                                <text className="font_5 text_19">{item.zjcx}</text>
                                                            </view>
                                                        </view>
                                                    </view>
                                                );
                                            })}
                                        </view>
                                    )}
                                </>
                            )}
                            {tabIndex == 3 && (
                                <>
                                    {shouldPayList.length == 0 && (
                                        <view className="mt-12">
                                            <Empty description="无缴费记录" style={{ margin: "0px" }} />
                                        </view>
                                    )}
                                    {shouldPayList.length > 0 && (
                                        <view className="pay-list">
                                            {shouldPayList.map((item, index) => {
                                                return (
                                                    <view className="pay-item" key={index}>
                                                        <view className="flex-col pay-section_3">
                                                            <view className="pay-flex-row">
                                                                <view className="self-start font_2 text_11">{item.CostTypeName}</view>
                                                                <view className="self-end text_11">{item.ResultText}</view>
                                                            </view>
                                                            <view className="self-stretch divider"></view>
                                                            <view className="flex-row self-stretch group_7">
                                                                <text className="font_8 text_18">应缴总额：</text>
                                                                <text className="font_5 text_19">{item.PayMoney.toFixed(2)}</text>
                                                            </view>
                                                            <view className="flex-row self-stretch group_7">
                                                                <text className="font_8 text_18">创建时间：</text>
                                                                <text className="font_5 text_19">{item.CreateTime}</text>
                                                            </view>
                                                            {payList.map((payItem) => {
                                                                if (payItem.JxShouldPayId == item.Id)
                                                                    return (
                                                                        <React.Fragment key={payItem.Id}>
                                                                            <view className="self-stretch divider"></view>
                                                                            <view className="flex-row self-stretch group_7">
                                                                                <text className="font_8 text_18">缴费日期：</text>
                                                                                <text className="font_5 text_19">{payItem.CreateTime}</text>
                                                                            </view>
                                                                            <view className="flex-row self-stretch group_7">
                                                                                <text className="font_8 text_18">缴费金额：</text>
                                                                                <text className="font_5 text_19">{payItem.PayMoney.toFixed(2)}</text>
                                                                            </view>
                                                                            <view className="flex-row self-stretch group_7">
                                                                                <text className="font_8 text_18">操作人员：</text>
                                                                                <text className="font_5 text_19">{payItem.CreateUserName}</text>
                                                                            </view>
                                                                            <view className="flex-row self-stretch group_7">
                                                                                <text className="font_8 text_18">缴费店面：</text>
                                                                                <text className="font_5 text_19">{payItem.CreateJxDeptName}</text>
                                                                            </view>
                                                                        </React.Fragment>
                                                                    );
                                                                else return null;
                                                            })}

                                                            {item.NoPay > 0 && (
                                                                <>
                                                                    <view className="self-stretch divider"></view>
                                                                    <view className="flex-row self-stretch group_7">
                                                                        <text className="font_8 text_18">欠费金额：</text>
                                                                        <text className="font_5 text_19">{item.NoPay.toFixed(2)}</text>
                                                                    </view>
                                                                    <view className="pay-flex-row bottom-pay">
                                                                        <view></view>
                                                                        <view>
                                                                            <Button
                                                                                size="small"
                                                                                type="primary"
                                                                                color="#504de5"
                                                                                onClick={() => {
                                                                                    message.openAlert("耐心等待", "系统暂未开通该功能，具体请咨询驾校工作人员!");
                                                                                }}
                                                                            >
                                                                                在线支付
                                                                            </Button>
                                                                        </view>
                                                                    </view>
                                                                </>
                                                            )}
                                                        </view>
                                                    </view>
                                                );
                                            })}
                                        </view>
                                    )}
                                </>
                            )}
                            {tabIndex == 4 && (
                                <>
                                    <view
                                        className="image-list"
                                        style={{
                                            display: "flex",
                                            flexWrap: "wrap",
                                        }}
                                    >
                                        {imageIds.map((item: any, index) => {
                                            return (
                                                <view
                                                    className="flex-col relative mt-10 list-item"
                                                    key={index}
                                                    style={{
                                                        width: "calc(50% - 10rpx)",
                                                        marginLeft: index % 2 === 0 ? "0" : "10rpx",
                                                    }}
                                                >
                                                    <view className="exam-flex-row">
                                                        <view className="self-start text_11 mt-10">{imageTexts[index]}</view>
                                                        <view className="self-end text_11"></view>
                                                    </view>
                                                    <view className="self-stretch divider"></view>
                                                    <view className="flex-row items-start">
                                                        <Image
                                                            className="image_51"
                                                            src={
                                                                imageList.find((m: any) => parseInt(m.ImageId) == parseInt(item))
                                                                    ? imageList.find((m: any) => parseInt(m.ImageId) == parseInt(item)).ImagePath
                                                                    : ""
                                                            }
                                                        />
                                                        <view className="ml-16 flex-col items-start flex-1">
                                                            <text className="font_2 text_4"></text>
                                                        </view>
                                                    </view>
                                                    <view className="mt-12 flex-row justify-between items-center">
                                                        <view className="flex-row items-center font_5 text_19"></view>
                                                        <view className="flex-col justify-start items-center">
                                                            <Button
                                                                size="small"
                                                                type="primary"
                                                                color="#504de5"
                                                                onClick={() => {
                                                                    setOpenTakePhoto(item);
                                                                }}
                                                            >
                                                                拍照上传
                                                            </Button>
                                                        </view>
                                                    </view>
                                                </view>
                                            );
                                        })}
                                    </view>
                                </>
                            )}
                            {tabIndex == 5 && (
                                <>
                                    <view className="mt-12">
                                        <Empty description="练车功能正在开发中" style={{ margin: "0px" }} />
                                    </view>
                                </>
                            )}
                        </view>
                    </view>
                </view>
            )}
        </view>
    );

    return (
        <>
            {fullScreen ? (
                // 全屏模式：不显示时返回null，显示时直接渲染内容
                visible ? (
                    renderContent()
                ) : null
            ) : (
                // 非全屏模式：使用Popup包装
                <Popup
                    visible={visible}
                    position="bottom"
                    lockScroll={false}
                    closeOnOverlayClick
                    duration={300}
                    onClose={handleClose}
                    style={{
                        maxHeight: "90vh",
                        borderRadius: "24rpx 24rpx 0 0",
                    }}
                >
                    <view className="student-detail-modal">
                        <view className="student-detail-content">{renderContent()}</view>
                    </view>
                </Popup>
            )}

            <Popup
                visible={openTakePhoto > -1}
                position="bottom"
                onClose={() => {
                    setOpenTakePhoto(-1);
                }}
                lockScroll
                duration={0}
            >
                <view className="take-photo">
                    <view className="flex-col justify-start mask pos">
                        <view className="flex-col popup">
                            <view
                                className="flex-row items-center group_3"
                                onClick={() => {
                                    chooseImage(openTakePhoto, ["album"]);
                                }}
                            >
                                <Image className="image_6" src="https://cdn.51panda.com/wx/photo/87b2f9bf65bcda607ce47bdb409b9491.png" />
                                <view className="flex-col items-start flex-1 ml-23">
                                    <text className="font text_3">相册</text>
                                    <text className="mt-10 font_2">直接从相册上传照片</text>
                                </view>
                            </view>
                            <view
                                className="flex-row items-center group_3 view"
                                onClick={() => {
                                    chooseImage(openTakePhoto, ["camera"]);
                                }}
                            >
                                <Image className="image_7" src="https://cdn.51panda.com/wx/photo/a4fab962dc018dcec846d1befb430aff.png" />
                                <view className="flex-col items-start flex-1 ml-23">
                                    <text className="font">拍照</text>
                                    <text className="mt-10 font_2">直接用手机相机拍照</text>
                                </view>
                            </view>
                            <view className="flex-col justify-start items-center text-wrapper">
                                <view
                                    className="footer"
                                    style={{
                                        paddingLeft: "20rpx",
                                        paddingRight: "20rpx",
                                    }}
                                >
                                    <Button
                                        block
                                        type="primary"
                                        onClick={() => {
                                            setOpenTakePhoto(-1);
                                        }}
                                        color={"#ebeef4"}
                                        style={{
                                            color: "#000",
                                        }}
                                    >
                                        取消
                                    </Button>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </Popup>
        </>
    );
});

export default StudentDetail;
