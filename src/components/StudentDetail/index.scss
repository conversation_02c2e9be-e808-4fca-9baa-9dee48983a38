.student-detail-wrapper {
    /* 全局页面样式 */
    page {
        height: 100%;
        background: #eff1f7;
    }

    /* 基础工具类 - 这些类在组件中被大量使用 */
    .flex-col {
        display: flex;
        flex-direction: column;
    }

    .flex-row {
        display: flex;
        flex-direction: row;
    }

    .justify-between {
        justify-content: space-between;
    }

    .justify-center {
        justify-content: center;
    }

    .justify-start {
        justify-content: flex-start;
    }

    .justify-end {
        justify-content: flex-end;
    }

    .justify-evenly {
        justify-content: space-evenly;
    }

    .items-center {
        align-items: center;
    }

    .items-start {
        align-items: flex-start;
    }

    .items-end {
        align-items: flex-end;
    }

    .items-baseline {
        align-items: baseline;
    }

    .self-start {
        align-self: flex-start;
    }

    .self-end {
        align-self: flex-end;
    }

    .self-center {
        align-self: center;
    }

    .self-stretch {
        align-self: stretch;
    }

    .relative {
        position: relative;
    }

    .absolute {
        position: absolute;
    }

    .mb-20 {
        margin-bottom: 38.46rpx;
    }

    .mt-12 {
        margin-top: 23.08rpx;
    }

    .mt-18 {
        margin-top: 34.62rpx;
    }

    .ml-8 {
        margin-left: 15.38rpx;
    }

    .ml-9 {
        margin-left: 17.31rpx;
    }

    .ml-10 {
        margin-left: 19.23rpx;
    }

    .ml-16 {
        margin-left: 30.77rpx;
    }

    .ml-41 {
        margin-left: 78.85rpx;
    }

    .flex-1 {
        flex: 1;
    }

    .safe__area {
        height: 34rpx;
    }

    .shrink-0 {
        flex-shrink: 0;
    }

    .mask {
        background-color: rgba(0, 0, 0, 0.5);
    }

    .pos {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .popup {
        background-color: #ffffff;
        border-radius: 36.23rpx 36.23rpx 0rpx 0rpx;
        padding: 101.45rpx 28.99rpx 97.83rpx;
    }

    .group_3 {
        padding: 0 3.62rpx;
    }

    .image_6 {
        border-radius: 7.25rpx;
        width: 68.84rpx;
        height: 54.35rpx;
    }

    .image_7 {
        width: 68.84rpx;
        height: 57.97rpx;
    }

    .ml-23 {
        margin-left: 41.67rpx;
    }

    .view {
        margin-top: 119.57rpx;
    }

    .footer {
        margin-top: 100rpx;
        padding: 28.99rpx 3.62rpx;
    }

    /* 学员详情组件包装器 */
    .student-detail-wrapper {
        font-family: Yuanti SC, STYuanti-SC-Regular, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;

        * {
            box-sizing: border-box;
        }
    }

    /* Loading 转圈样式 */
    .custom-spinner {
        width: 80rpx;
        height: 80rpx;
        border: 6rpx solid #f3f3f3;
        border-top: 6rpx solid #585ce5;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

    /* 专门的滚动容器 */
    .scroll-container {
        flex: 1;
        height: 100%;
        position: relative;
    }

    /* 学员信息相关样式 */
    .ml-5 {
        margin-left: 9.62rpx;
    }

    .ml-3 {
        margin-left: 5.77rpx;
    }

    .mt-11 {
        margin-top: 21.15rpx;
    }

    .mt-15 {
        margin-top: 28.85rpx;
    }

    .section {
        padding: 33rpx 30.77rpx 263.46rpx;
        background-image: linear-gradient(180deg, #59bdff1a -36.7%, #585ce500 136.7%);
    }

    .group {
        padding-left: 33.56rpx;
        padding-right: 25.63rpx;
    }

    .image {
        width: 53.85rpx;
        height: 21.15rpx;
    }

    .image_2 {
        width: 128.85rpx;
        height: 21.15rpx;
    }

    .image_3 {
        width: 61.54rpx;
        height: 61.54rpx;
    }

    .image_4 {
        width: 46.15rpx;
        height: 46.15rpx;
    }

    .group_4 {
        padding-bottom: 15.38rpx;
    }

    .section_2 {
        padding: 0 30.75rpx 30.77rpx;
        background-color: #ffffff;
        border-radius: 15.38rpx;
    }

    .group_2 {
        margin-top: -46.15rpx;
    }

    .image_5 {
        margin-bottom: 5.77rpx;
        border-radius: 50%;
        width: 160rpx;
        height: 190rpx;
    }

    .section_3 {
        padding: 10.58rpx 12.17rpx 10.58rpx 15.38rpx;
        background-color: #666bff1a;
        border-radius: 23.08rpx;
        height: 51.92rpx;
    }

    .font {
        font-size: 26.92rpx;
        line-height: 23.83rpx;
        color: #585ce5;
    }

    .text {
        line-height: 24.85rpx;
    }

    .group_5 {
        margin-top: 24.06rpx;
        padding: 0 6.04rpx;
    }

    .text_2 {
        color: #15151a;
        font-size: 46.15rpx;
        line-height: 43.38rpx;
    }

    .font_2 {
        font-size: 26.92rpx;
        line-height: 23.83rpx;
        color: #585ce5cc;
    }

    .text_3 {
        color: #15151a;
        line-height: 24.77rpx;
    }

    .text_4 {
        line-height: 24.85rpx;
    }

    .group_6 {
        margin-top: 32.56rpx;
    }

    .text-wrapper {
        padding: 9.29rpx 0 6.92rpx;
        background-color: #00b587;
        border-radius: 7.69rpx;
        width: 53.85rpx;
        height: 32.69rpx;
    }

    .font_3 {
        font-size: 19.23rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 16.48rpx;
        color: #ffffff;
    }

    .text_5 {
        color: #000000;
        line-height: 25.23rpx;
    }

    .group_7 {
        margin-top: 20rpx;
    }

    .group_8 {
        line-height: 22.15rpx;
        height: 22.15rpx;
    }

    .font_4 {
        font-size: 30.77rpx;
        font-family: YouSheBiaoTiHei;
        line-height: 21.46rpx;
    }

    .text_6 {
        color: #ffc300;
        line-height: 21.63rpx;
    }

    .text_7 {
        color: #00b588;
        line-height: 22.15rpx;
    }

    .font_5 {
        font-size: 26.92rpx;
        line-height: 23.83rpx;
        color: #15151a99;
    }

    .text_8 {
        line-height: 25.15rpx;
    }

    .group_9 {
        margin-top: 15.52rpx;
    }

    .text_9 {
        line-height: 24.98rpx;
    }

    .group_10 {
        margin-right: 15.4rpx;
        width: 145.19rpx;
    }

    .text_10 {
        margin-left: -145.19rpx;
        color: #00b588;
        line-height: 25.02rpx;
    }

    .group_11 {
        margin-top: 33.65rpx;
    }

    .font_6 {
        font-size: 34.62rpx;
        font-weight: 700;
        color: #585ce5;
    }

    .text_11 {
        margin-bottom: 8.46rpx;
        line-height: 26.58rpx;
    }

    .group_12 {
        width: 166.88rpx;
    }

    .font_7 {
        font-size: 26.92rpx;
        line-height: 23.83rpx;
        color: #15151a4d;
    }

    .text_13 {
        margin-left: 28.42rpx;
        line-height: 24.9rpx;
    }

    .image_8 {
        margin-left: -166.88rpx;
    }

    .text_14 {
        margin-bottom: 8.46rpx;
        line-height: 26.56rpx;
    }

    .group_13 {
        width: 137.98rpx;
    }

    .text_15 {
        margin-left: 26.44rpx;
        line-height: 24.88rpx;
    }

    .image_9 {
        margin-left: -137.98rpx;
    }

    .text_12 {
        margin-right: 28.77rpx;
        margin-bottom: 5.83rpx;
        line-height: 32.67rpx;
    }

    .section_4 {
        padding-left: 26.92rpx;
        padding-right: 26.92rpx;
        background-color: #ffffff;
        border-radius: 15.38rpx;
    }

    .group_14 {
        margin-left: 6.25rpx;
        margin-right: 23.56rpx;
        padding: 40.5rpx 0 22.96rpx;
    }

    .font_8 {
        font-size: 23.08rpx;
        font-family: Yuanti SC, STYuanti-SC-Regular;
        line-height: 21.46rpx;
        color: #000000;
    }

    .text_16 {
        line-height: 21.27rpx;
    }

    .font_9 {
        font-size: 30.77rpx;
    }

    .text_17 {
        color: #15151a99;
        line-height: 36.54rpx;
        text-align: center;
        width: 163.46rpx;
    }

    .group_15 {
        padding: 31rpx 2.17rpx 33rpx;
        border-top: solid 1.92rpx #15151a33;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .text_18 {
        line-height: 21.38rpx;
    }

    .text_19 {
        line-height: 25.19rpx;
        margin-left: 20rpx;
    }

    .section_5 {
        padding: 38.46rpx 0 26.92rpx 30.77rpx;
        overflow: hidden;
        background-color: #ffffff;
        border-radius: 15.38rpx;
    }

    .group_16 {
        margin-right: 30.77rpx;
    }

    .text_20 {
        margin-top: 2.15rpx;
        color: #000000;
        line-height: 28.27rpx;
    }

    .text_21 {
        line-height: 23.67rpx;
    }

    .horiz-list {
        overflow-x: auto;
    }

    .horiz-list-item {
        flex-shrink: 0;
    }

    .section_6 {
        padding-bottom: 15.85rpx;
        background-color: #edf3ff;
        border-radius: 15.38rpx;
        width: 100rpx;
        height: 115.38rpx;
    }

    .group_18 {
        padding: 23.96rpx 0 2.21rpx;
    }

    .text-wrapper_2 {
        padding: 6.02rpx 0 26.1rpx;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 50rpx;
    }

    .text_22 {
        margin-left: 6.44rpx;
        line-height: 17.88rpx;
    }

    .font_10 {
        font-size: 38.46rpx;
        font-family: AlimamaShuHeiTi;
        line-height: 27.92rpx;
        color: #15151a;
    }

    .section_7 {
        flex-shrink: 0;
        margin-left: 23.08rpx;
    }

    .horiz-list-item_1 {
        padding: 23.96rpx 0 16.35rpx;
        background-color: #edf3ff;
        border-radius: 15.38rpx;
        width: 100rpx;
        height: 115.38rpx;
    }

    .text_24 {
        line-height: 26.92rpx;
    }

    .section_8 {
        padding: 23.96rpx 0 15.85rpx;
        background-color: #585ce5;
        border-radius: 15.38rpx;
        width: 100rpx;
        height: 115.38rpx;
    }

    .text_23 {
        color: #ffffffcc;
    }

    .text_25 {
        color: #ffffff;
        line-height: 27.42rpx;
    }

    .horiz-list-item_2 {
        padding: 16.73rpx 0 13.21rpx;
        background-color: #585ce5;
        border-radius: 15.38rpx;
        width: 100rpx;
        height: 115.38rpx;
    }

    .text_26 {
        margin-top: 12.17rpx;
        line-height: 28.31rpx;
    }

    .text_27 {
        margin-top: 6.25rpx;
        line-height: 27.44rpx;
    }

    .group_19 {
        padding: 0 5.77rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 20rpx;
    }

    .text_28 {
        line-height: 25.75rpx;
    }

    .group_20 {
        margin-top: 16.35rpx;
        margin-left: 1.92rpx;
        margin-right: 11.54rpx;
    }

    .section_9 {
        padding: 19.23rpx 0 17.31rpx 17.31rpx;
        background-color: #ffffff;
        border-radius: 15.38rpx;
    }

    .group_21 {
        margin-right: 26.92rpx;
    }

    .group_22 {
        padding: 17.31rpx 30.77rpx 30.77rpx;
    }

    .text_29 {
        line-height: 23.73rpx;
    }

    .font_11 {
        font-size: 34.62rpx;
        font-weight: 700;
        color: #15151a;
    }

    .text_30 {
        line-height: 32.5rpx;
    }

    .group_23 {
        margin-top: 30.77rpx;
    }

    .section_10 {
        padding: 17.31rpx 30.77rpx 32.69rpx;
        background-color: #ffffff;
        border-radius: 15.38rpx;
    }

    .text_31 {
        line-height: 24.04rpx;
    }

    .text_32 {
        color: #585ce5;
        font-size: 28rpx;
        font-weight: 700;
        line-height: 36.54rpx;
        text-align: center;
        cursor: pointer;
        transition: color 0.3s ease;
    }

    .text_33 {
        color: #15151a99;
        font-size: 30.77rpx;
        line-height: 36.54rpx;
        text-align: center;
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
            color: #585ce5;
        }
    }

    .section_11 {
        background-color: #585ce5;
        border-radius: 1.92rpx;
        width: 69.23rpx;
        height: 3.85rpx;
        transition: margin-left 0.3s ease;
    }

    .group_1 {
        padding-left: 26.92rpx;
        padding-right: 30.77rpx;
        background-color: #ffffff;
        border-radius: 15.38rpx;
    }

    .text-wrapper_3 {
        padding: 14.96rpx 0 9.77rpx;
        background-color: #15151a0d;
        border-radius: 28.85rpx;
        width: 188.46rpx;
        height: 46.15rpx;
    }

    .text_34 {
        line-height: 21.42rpx;
    }

    .text-wrapper_4 {
        padding: 14.87rpx 0 9.83rpx;
        background-color: #15151a0d;
        border-radius: 28.85rpx;
        width: 151.92rpx;
        height: 46.15rpx;
    }

    .text_35 {
        line-height: 21.42rpx;
    }

    .text-wrapper_6 {
        padding: 14.87rpx 0 9.83rpx;
        background-color: #15151a0d;
        border-radius: 28.85rpx;
        width: 151.92rpx;
        height: 46.15rpx;
    }

    /* 费用列表相关样式 */
    .pay-list {
        margin-top: 20rpx;
    }

    .pay-item {
        margin-bottom: 20rpx;
        background-color: #ffffff;
        border-radius: 15.38rpx;
        overflow: hidden;
    }

    .pay-section_3 {
        padding: 30.77rpx;
    }

    .pay-flex-row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15rpx;
    }

    .bottom-pay {
        margin-top: 20rpx;
        margin-bottom: 0;
    }

    .divider {
        height: 1rpx;
        background-color: #15151a1a;
        margin: 15rpx 0;
    }

    /* 照片列表相关样式 */
    .image-list {
        margin-top: 20rpx;
        padding: 0;
    }

    .list-item {
        background-color: #ffffff;
        border-radius: 15.38rpx;
        padding: 20rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    }

    .exam-flex-row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;
    }

    .image_51 {
        width: 120rpx;
        height: 120rpx;
        border-radius: 8rpx;
        object-fit: cover;
        background-color: #f5f5f5;
    }

    .mt-10 {
        margin-top: 19.23rpx;
    }

    .mb-10 {
        margin-bottom: 19.23rpx;
    }

    /* 考试列表相关样式 */
    .exam-list {
        margin-top: 20rpx;
    }

    .exam-item {
        margin-bottom: 20rpx;
        background-color: #ffffff;
        border-radius: 15.38rpx;
        overflow: hidden;
    }

    .exam-section_3 {
        padding: 30.77rpx;
    }
}
