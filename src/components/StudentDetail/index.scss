/* 全局页面样式 */
page {
    height: 100%;
    background: #eff1f7;
}

/* 学员详情组件包装器 */
.student-detail-wrapper {
    font-family: <PERSON>ti SC, STYuanti-SC-Regular, -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;

    /* 确保所有子元素都能正确继承样式 */
    * {
        box-sizing: border-box;
    }

    /* 基础工具类 - 这些类在组件中被大量使用 */
    .flex-col {
        display: flex;
        flex-direction: column;
    }

    .flex-row {
        display: flex;
        flex-direction: row;
    }

    .justify-between {
        justify-content: space-between;
    }

    .justify-center {
        justify-content: center;
    }

    .justify-start {
        justify-content: flex-start;
    }

    .justify-end {
        justify-content: flex-end;
    }

    .justify-evenly {
        justify-content: space-evenly;
    }

    .items-center {
        align-items: center;
    }

    .items-start {
        align-items: flex-start;
    }

    .items-end {
        align-items: flex-end;
    }

    .items-baseline {
        align-items: baseline;
    }

    .self-start {
        align-self: flex-start;
    }

    .self-end {
        align-self: flex-end;
    }

    .self-center {
        align-self: center;
    }

    .self-stretch {
        align-self: stretch;
    }

    .relative {
        position: relative;
    }

    .absolute {
        position: absolute;
    }

    .mb-20 {
        margin-bottom: 38.46rpx;
    }

    .mt-12 {
        margin-top: 23.08rpx;
    }

    .mt-18 {
        margin-top: 34.62rpx;
    }

    .ml-8 {
        margin-left: 15.38rpx;
    }

    .ml-9 {
        margin-left: 17.31rpx;
    }

    .ml-10 {
        margin-left: 19.23rpx;
    }

    .ml-16 {
        margin-left: 30.77rpx;
    }

    .ml-41 {
        margin-left: 78.85rpx;
    }

    .flex-1 {
        flex: 1;
    }

    .safe__area {
        height: 34rpx;
    }

    .shrink-0 {
        flex-shrink: 0;
    }

    .mask {
        background-color: rgba(0, 0, 0, 0.5);
    }

    .pos {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .popup {
        background-color: #ffffff;
        border-radius: 36.23rpx 36.23rpx 0rpx 0rpx;
        padding: 101.45rpx 28.99rpx 97.83rpx;
    }

    .group_3 {
        padding: 0 3.62rpx;
    }

    .image_6 {
        border-radius: 7.25rpx;
        width: 68.84rpx;
        height: 54.35rpx;
    }

    .image_7 {
        width: 68.84rpx;
        height: 57.97rpx;
    }

    .ml-23 {
        margin-left: 41.67rpx;
    }

    .view {
        margin-top: 119.57rpx;
    }

    .footer {
        margin-top: 100rpx;
        padding: 28.99rpx 3.62rpx;
    }

    /* 学员详情弹窗样式 */
    .student-detail-modal {
        // position: fixed;
        // top: 0;
        // left: 0;
        // right: 0;
        // bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(4px);

        /* 确保触摸滚动在移动端正常工作 */
        -webkit-overflow-scrolling: touch;
        touch-action: manipulation;
    }

    /* Loading 转圈样式 */
    .custom-spinner {
        width: 80rpx;
        height: 80rpx;
        border: 6rpx solid #f3f3f3;
        border-top: 6rpx solid #585ce5;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

    .student-detail-content {
        width: 100%;
        max-width: 750rpx;
        max-height: 85vh;
        background-color: #fff;
        border-radius: 24rpx;
        box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
        animation: modalFadeIn 0.3s ease-out;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: relative;

        /* 移动端滚动优化 */
        -webkit-overflow-scrolling: touch;
        touch-action: pan-y;
    }

    @keyframes modalFadeIn {
        from {
            opacity: 0;
            transform: scale(0.9) translateY(-20px);
        }
        to {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }

    .student-detail-modal .student-info {
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        background-color: #eff1f7;
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;

        /* 优化滚动条样式 */
        &::-webkit-scrollbar {
            width: 4px;
        }

        &::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.5);
        }
    }

    page {
        background-color: #eff1f7;
        font-family: Yuanti SC, STYuanti-SC-Regular;
    }

    .page {
        background-color: #eff1f7;
        width: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
        position: relative;
    }

    .student-info {
        .ml-5 {
            margin-left: 9.62rpx;
        }

        .ml-9 {
            margin-left: 17.31rpx;
        }

        .ml-3 {
            margin-left: 5.77rpx;
        }

        .mt-11 {
            margin-top: 21.15rpx;
        }

        .mt-15 {
            margin-top: 28.85rpx;
        }

        .ml-41 {
            margin-left: 78.85rpx;
        }

        .section {
            padding: 33rpx 30.77rpx 263.46rpx;
            background-image: linear-gradient(180deg, #59bdff1a -36.7%, #585ce500 136.7%);
        }

        .group {
            padding-left: 33.56rpx;
            padding-right: 25.63rpx;
        }

        .image {
            width: 53.85rpx;
            height: 21.15rpx;
        }

        .image_2 {
            width: 128.85rpx;
            height: 21.15rpx;
        }

        .image_3 {
            width: 61.54rpx;
            height: 61.54rpx;
        }

        .image_4 {
            width: 46.15rpx;
            height: 46.15rpx;
        }

        .view {
            margin-top: -186.54rpx;
            padding: 0 30.77rpx;
        }

        .group_4 {
            padding-bottom: 15.38rpx;
        }

        .section_2 {
            padding: 0 30.75rpx 30.77rpx;
            background-color: #ffffff;
            border-radius: 15.38rpx;
        }

        .group_2 {
            margin-top: -46.15rpx;
        }

        .image_5 {
            margin-bottom: 5.77rpx;
            border-radius: 50%;
            width: 160rpx;
            height: 190rpx;
        }

        .section_3 {
            padding: 10.58rpx 12.17rpx 10.58rpx 15.38rpx;
            background-color: #666bff1a;
            border-radius: 23.08rpx;
            height: 51.92rpx;
        }

        .image_6 {
            width: 30.77rpx;
            height: 30.77rpx;
        }

        .font {
            font-size: 26.92rpx;
            // font-family: Yuanti SC, STYuanti-SC-Regular;
            line-height: 23.83rpx;
            color: #585ce5;
        }

        .text {
            line-height: 24.85rpx;
        }

        .group_5 {
            margin-top: 24.06rpx;
            padding: 0 6.04rpx;
        }

        .text_2 {
            color: #15151a;
            font-size: 46.15rpx;
            // font-family: Yuanti SC, STYuanti-SC-Regular;
            line-height: 43.38rpx;
        }

        .font_2 {
            font-size: 26.92rpx;
            // font-family: Yuanti SC, STYuanti-SC-Regular;
            line-height: 23.83rpx;
            color: #585ce5cc;
        }

        .text_3 {
            color: #15151a;
            line-height: 24.77rpx;
        }

        .text_4 {
            line-height: 24.85rpx;
        }

        .group_6 {
            margin-top: 32.56rpx;
        }

        .text-wrapper {
            padding: 9.29rpx 0 6.92rpx;
            background-color: #00b587;
            border-radius: 7.69rpx;
            width: 53.85rpx;
            height: 32.69rpx;
        }

        .font_3 {
            font-size: 19.23rpx;
            font-family: Yuanti SC, STYuanti-SC-Regular;
            line-height: 16.48rpx;
            color: #ffffff;
        }

        .text_5 {
            color: #000000;
            line-height: 25.23rpx;
        }

        .group_7 {
            margin-top: 20rpx;
        }

        .group_8 {
            line-height: 22.15rpx;
            height: 22.15rpx;
        }

        .font_4 {
            font-size: 30.77rpx;
            font-family: YouSheBiaoTiHei;
            line-height: 21.46rpx;
        }

        .text_6 {
            color: #ffc300;
            line-height: 21.63rpx;
        }

        .text_7 {
            color: #00b588;
            line-height: 22.15rpx;
        }

        .font_5 {
            font-size: 26.92rpx;
            // font-family: Yuanti SC, STYuanti-SC-Regular;
            line-height: 23.83rpx;
            color: #15151a99;
        }

        .text_8 {
            line-height: 25.15rpx;
        }

        .group_9 {
            margin-top: 15.52rpx;
        }

        .text_9 {
            line-height: 24.98rpx;
        }

        .group_10 {
            margin-right: 15.4rpx;
            width: 145.19rpx;
        }

        .image_7 {
            margin-left: 106.73rpx;
            width: 38.46rpx;
            height: 38.46rpx;
        }

        .text_10 {
            margin-left: -145.19rpx;
            color: #00b588;
            line-height: 25.02rpx;
        }

        .group_11 {
            margin-top: 33.65rpx;
        }

        .font_6 {
            font-size: 34.62rpx;
            // font-family: Yuanti SC, STYuanti-SC-Regular;
            font-weight: 700;
            color: #585ce5;
        }

        .text_11 {
            margin-bottom: 8.46rpx;
            line-height: 26.58rpx;
        }

        .group_12 {
            width: 166.88rpx;
        }

        .font_7 {
            font-size: 26.92rpx;
            // font-family: Yuanti SC, STYuanti-SC-Regular;
            line-height: 23.83rpx;
            color: #15151a4d;
        }

        .text_13 {
            margin-left: 28.42rpx;
            line-height: 24.9rpx;
        }

        .image_8 {
            margin-left: -166.88rpx;
        }

        .text_14 {
            margin-bottom: 8.46rpx;
            line-height: 26.56rpx;
        }

        .group_13 {
            width: 137.98rpx;
        }

        .text_15 {
            margin-left: 26.44rpx;
            line-height: 24.88rpx;
        }

        .image_9 {
            margin-left: -137.98rpx;
        }

        .text_12 {
            margin-right: 28.77rpx;
            margin-bottom: 5.83rpx;
            line-height: 32.67rpx;
        }

        :global(.section_4) {
            padding-left: 26.92rpx;
            padding-right: 26.92rpx;
            background-color: #ffffff;
            border-radius: 15.38rpx;
        }

        :global(.group_14) {
            margin-left: 6.25rpx;
            margin-right: 23.56rpx;
            padding: 40.5rpx 0 22.96rpx;
        }

        :global(.font_8) {
            font-size: 23.08rpx;
            font-family: Yuanti SC, STYuanti-SC-Regular;
            line-height: 21.46rpx;
            color: #000000;
        }

        :global(.text_16) {
            line-height: 21.27rpx;
        }

        :global(.font_9) {
            font-size: 30.77rpx;
            // font-family: Yuanti SC, STYuanti-SC-Regular;
        }

        :global(.text_17) {
            color: #15151a99;
            line-height: 36.54rpx;
            text-align: center;
            width: 163.46rpx;
        }

        :global(.group_15) {
            padding: 31rpx 2.17rpx 33rpx;
            border-top: solid 1.92rpx #15151a33;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        :global(.text_18) {
            line-height: 21.38rpx;
        }

        :global(.text_19) {
            line-height: 25.19rpx;
            margin-left: 20rpx;
        }

        :global(.section_5) {
            padding: 38.46rpx 0 26.92rpx 30.77rpx;
            overflow: hidden;
            background-color: #ffffff;
            border-radius: 15.38rpx;
        }

        :global(.group_16) {
            margin-right: 30.77rpx;
        }

        :global(.text_20) {
            margin-top: 2.15rpx;
            color: #000000;
            line-height: 28.27rpx;
        }

        :global(.text_21) {
            line-height: 23.67rpx;
        }

        :global(.horiz-list) {
            overflow-x: auto;
        }

        :global(.horiz-list-item) {
            flex-shrink: 0;
        }

        :global(.section_6) {
            padding-bottom: 15.85rpx;
            background-color: #edf3ff;
            border-radius: 15.38rpx;
            width: 100rpx;
            height: 115.38rpx;
        }

        :global(.group_18) {
            padding: 23.96rpx 0 2.21rpx;
        }

        :global(.text-wrapper_2) {
            padding: 6.02rpx 0 26.1rpx;
            // background-image: url("https://ide.code.fun/api/image?token=6695557547b10e0011255241&name=8f7f3f58f41a09516dcf772cf0032779.png");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            width: 50rpx;
        }

        :global(.pos) {
            position: absolute;
            left: 0;
            top: 0;
        }

        :global(.text_22) {
            margin-left: 6.44rpx;
            line-height: 17.88rpx;
        }

        :global(.font_10) {
            font-size: 38.46rpx;
            font-family: AlimamaShuHeiTi;
            line-height: 27.92rpx;
            color: #15151a;
        }

        :global(.section_7) {
            flex-shrink: 0;
            margin-left: 23.08rpx;
        }

        :global(.horiz-list-item_1) {
            padding: 23.96rpx 0 16.35rpx;
            background-color: #edf3ff;
            border-radius: 15.38rpx;
            width: 100rpx;
            height: 115.38rpx;
        }

        :global(.text_24) {
            line-height: 26.92rpx;
        }

        :global(.section_8) {
            padding: 23.96rpx 0 15.85rpx;
            background-color: #585ce5;
            border-radius: 15.38rpx;
            width: 100rpx;
            height: 115.38rpx;
        }

        :global(.text_23) {
            color: #ffffffcc;
        }

        :global(.text_25) {
            color: #ffffff;
            line-height: 27.42rpx;
        }

        :global(.horiz-list-item_2) {
            padding: 16.73rpx 0 13.21rpx;
            background-color: #585ce5;
            border-radius: 15.38rpx;
            width: 100rpx;
            height: 115.38rpx;
        }

        :global(.text_26) {
            margin-top: 12.17rpx;
            line-height: 28.31rpx;
        }

        :global(.text_27) {
            margin-top: 6.25rpx;
            line-height: 27.44rpx;
        }

        :global(.group_19) {
            padding: 0 5.77rpx;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-start;
            margin-bottom: 20rpx;
        }

        :global(.text_28) {
            line-height: 25.75rpx;
        }

        :global(.group_20) {
            margin-top: 16.35rpx;
            margin-left: 1.92rpx;
            margin-right: 11.54rpx;
        }

        :global(.section_9) {
            padding: 19.23rpx 0 17.31rpx 17.31rpx;
            background-color: #ffffff;
            border-radius: 15.38rpx;
        }

        :global(.group_21) {
            margin-right: 26.92rpx;
        }

        :global(.group_22) {
            padding: 17.31rpx 30.77rpx 30.77rpx;
        }

        :global(.text_29) {
            line-height: 23.73rpx;
        }

        :global(.font_11) {
            font-size: 34.62rpx;
            // font-family: Yuanti SC, STYuanti-SC-Regular;
            font-weight: 700;
            color: #15151a;
        }

        :global(.text_30) {
            line-height: 32.5rpx;
        }

        :global(.group_23) {
            margin-top: 30.77rpx;
        }

        :global(.section_10) {
            padding: 17.31rpx 30.77rpx 32.69rpx;
            background-color: #ffffff;
            border-radius: 15.38rpx;
        }

        :global(.text_31) {
            line-height: 24.04rpx;
        }

        :global(.text_32) {
            color: #585ce5;
            font-size: 28rpx;
            // font-family: Yuanti SC, STYuanti-SC-Regular;
            font-weight: 700;
            line-height: 36.54rpx;
            text-align: center;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        :global(.text_33) {
            color: #15151a99;
            font-size: 30.77rpx;
            // font-family: Yuanti SC, STYuanti-SC-Regular;
            line-height: 36.54rpx;
            text-align: center;
            cursor: pointer;
            transition: color 0.3s ease;

            &:hover {
                color: #585ce5;
            }
        }

        :global(.section_11) {
            background-color: #585ce5;
            border-radius: 1.92rpx;
            width: 69.23rpx;
            height: 3.85rpx;
            transition: margin-left 0.3s ease;
        }

        :global(.group_1) {
            padding-left: 26.92rpx;
            padding-right: 30.77rpx;
            background-color: #ffffff;
            border-radius: 15.38rpx;
        }

        :global(.mt-12) {
            margin-top: 23.08rpx;
        }

        :global(.mt-18) {
            margin-top: 34.62rpx;
        }

        :global(.mt-10) {
            margin-left: 19.23rpx;
        }

        :global(.ml-8) {
            margin-left: 15.38rpx;
        }

        :global(.ml-10) {
            margin-left: 19.23rpx;
        }

        :global(.ml-16) {
            margin-left: 30.77rpx;
        }

        :global(.flex-1) {
            flex: 1;
        }

        :global(.safe__area) {
            height: 34rpx;
        }

        :global(.text-wrapper_3) {
            padding: 14.96rpx 0 9.77rpx;
            background-color: #15151a0d;
            border-radius: 28.85rpx;
            width: 188.46rpx;
            height: 46.15rpx;
        }

        :global(.text_34) {
            line-height: 21.42rpx;
        }

        :global(.text-wrapper_4) {
            padding: 14.87rpx 0 9.83rpx;
            background-color: #15151a0d;
            border-radius: 28.85rpx;
            width: 151.92rpx;
            height: 46.15rpx;
        }

        :global(.text_35) {
            line-height: 21.42rpx;
        }

        :global(.text-wrapper_6) {
            padding: 14.87rpx 0 9.83rpx;
            background-color: #15151a0d;
            border-radius: 28.85rpx;
            width: 151.92rpx;
            height: 46.15rpx;
        }
    }

    :global(.exam-list) {
        :global(.exam-item) {
            margin-top: 24rpx;
        }

        :global(.ml-1) {
            margin-left: 2rpx;
        }

        :global(.font) {
            font-size: 28rpx;
            font-family: PingFang SC;
            line-height: 25.98rpx;
            color: #b3b3b3;
        }

        :global(.font_2) {
            font-size: 30rpx;
            font-family: PingFang SC;
            font-weight: 700;
            color: #253738;
        }

        :global(.exam-section_3) {
            padding: 38rpx 32rpx 34rpx 38rpx;
            background-color: #ffffff;
            border-radius: 15.38rpx;
        }

        :global(.text_11) {
            line-height: 27.8rpx;
        }

        :global(.divider) {
            margin-top: 16rpx;
            background-color: #e0e1e4;
            height: 0.8rpx;
        }

        :global(.group_7) {
            padding: 0 2rpx;
        }

        :global(.view_3) {
            margin-top: 38rpx;
        }

        :global(.view_4) {
            margin-top: 42rpx;
        }

        :global(.text_13) {
            line-height: 25.9rpx;
        }

        :global(.group_8) {
            margin-top: 42rpx;
            padding: 0 4rpx;
        }

        :global(.text_15) {
            line-height: 25.88rpx;
        }

        :global(.font_3) {
            font-size: 28rpx;
            font-family: PingFang SC;
            line-height: 25.98rpx;
            color: #253738;
        }

        :global(.text_16) {
            line-height: 26.8rpx;
        }

        :global(.text_14) {
            line-height: 26.74rpx;
        }

        :global(.text_12) {
            line-height: 26.14rpx;
        }

        :global(.exam-flex-row) {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }
    }

    :global(.pay-list) {
        :global(.pay-item) {
            margin-top: 24rpx;
        }

        :global(.ml-1) {
            margin-left: 2rpx;
        }

        :global(.font) {
            font-size: 28rpx;
            font-family: PingFang SC;
            line-height: 25.98rpx;
            color: #b3b3b3;
        }

        :global(.font_2) {
            font-size: 30rpx;
            font-family: PingFang SC;
            font-weight: 700;
            color: #253738;
        }

        :global(.pay-section_3) {
            padding: 38rpx 32rpx 34rpx 38rpx;
            background-color: #ffffff;
            border-radius: 15.38rpx;
        }

        :global(.text_11) {
            line-height: 27.8rpx;
        }

        :global(.divider) {
            margin-top: 16rpx;
            background-color: #e0e1e4;
            height: 0.8rpx;
        }

        :global(.group_7) {
            padding: 0 2rpx;
        }

        :global(.view_3) {
            margin-top: 38rpx;
        }

        :global(.view_4) {
            margin-top: 42rpx;
        }

        :global(.text_13) {
            line-height: 25.9rpx;
        }

        :global(.group_8) {
            margin-top: 42rpx;
            padding: 0 4rpx;
        }

        :global(.text_15) {
            line-height: 25.88rpx;
        }

        :global(.font_3) {
            font-size: 28rpx;
            font-family: PingFang SC;
            line-height: 25.98rpx;
            color: #253738;
        }

        :global(.text_16) {
            line-height: 26.8rpx;
        }

        :global(.text_14) {
            line-height: 26.74rpx;
        }

        :global(.text_12) {
            line-height: 26.14rpx;
        }

        :global(.pay-flex-row) {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }

        :global(.bottom-pay) {
            margin-bottom: -20rpx;
        }
    }

    :global(.image-list) {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: space-between;

        :global(.divider) {
            margin-top: 16rpx;
            background-color: #e0e1e4;
            height: 0.8rpx;
        }

        :global(.mt-31) {
            margin-top: 62rpx;
        }

        :global(.ml-9) {
            margin-left: 18rpx;
        }

        :global(.ml-17) {
            margin-left: 34rpx;
        }

        :global(.page) {
            background-color: #504de5;
            width: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            height: 100%;
        }

        :global(.group) {
            padding: 88rpx 28rpx 64rpx;
        }

        :global(.image) {
            width: 40rpx;
            height: 28rpx;
        }

        :global(.text) {
            color: #ffffff;
            font-size: 32rpx;
            font-family: SF Pro Text;
            font-weight: 700;
            line-height: 23.06rpx;
        }

        :global(.image_2) {
            width: 32rpx;
            height: 30rpx;
        }

        :global(.section) {
            background-color: #ffffff;
            border-radius: 50rpx 50rpx 0rpx 0rpx;
        }

        :global(.group_2) {
            padding: 36rpx 0 40rpx 28rpx;
            border-bottom: solid 2rpx #f0f2f6;
        }

        :global(.font) {
            font-size: 28rpx;
            font-family: SF Pro Text;
            letter-spacing: 2rpx;
            line-height: 34rpx;
            font-weight: 600;
            color: #3b566e;
        }

        :global(.text_2) {
            width: 206rpx;
        }

        :global(.horiz-list) {
            align-self: stretch;
        }

        :global(.group_3) {
            overflow-x: auto;
        }

        :global(.image_3) {
            flex-shrink: 0;
        }

        :global(.horiz-list-item) {
            border-radius: 50%;
            width: 92rpx;
            height: 92rpx;
        }

        :global(.horiz-list-item_2) {
            flex-shrink: 0;
            margin-right: -48rpx;
        }

        :global(.image_4) {
            border-radius: 50% 0 0 50%;
            width: 86rpx;
            height: 92rpx;
        }

        :global(.text_3) {
            margin-left: 32rpx;
            width: 216rpx;
        }

        :global(.list) {
            padding: 0 28rpx;
        }

        :global(.list-item) {
            width: calc(50% - 15rpx);
            padding: 8rpx 28rpx 28rpx;
            background-color: #ffffff;
            border-radius: 24rpx;
            box-shadow: 0rpx 4rpx 96rpx #00000021;
        }

        :global(.image_51) {
            margin-top: 20rpx;
            width: 100%;
            object-fit: cover;
        }

        :global(.font_2) {
            font-size: 28rpx;
            font-family: SF Pro Text;
            letter-spacing: 2rpx;
            line-height: 34rpx;
            color: #3b566e;
        }

        :global(.text_4) {
            width: 276rpx;
        }

        :global(.font_3) {
            font-size: 24rpx;
            font-family: SF Pro Text;
            letter-spacing: 2rpx;
            line-height: 23.12rpx;
            color: #504de5;
        }

        :global(.text_5) {
            line-height: 22.88rpx;
        }

        :global(.group_4) {
            width: 288.98rpx;
        }

        :global(.image_6) {
            width: 22rpx;
            height: 26rpx;
        }

        :global(.font_4) {
            font-size: 24rpx;
            font-family: SF Pro Text;
            letter-spacing: 2rpx;
            line-height: 28rpx;
            color: #6f8ba4;
        }

        :global(.image_7) {
            width: 30rpx;
            height: 30rpx;
        }

        :global(.text_6) {
            width: 164rpx;
        }

        :global(.text-wrapper) {
            padding: 20rpx 0;
            background-color: #504de5;
            border-radius: 30rpx;
            width: 180rpx;
            height: 60rpx;
        }

        :global(.font_5) {
            font-family: SF Pro Text;
            line-height: 18.24rpx;
        }

        :global(.section_2) {
            margin: 0 28rpx;
            padding: 8rpx 28rpx 56rpx;
            background-color: #ffffff;
            border-radius: 24rpx 24rpx 0 0;
            box-shadow: 0rpx 4rpx 96rpx #00000021;
        }

        :global(.text_9) {
            width: 272rpx;
        }

        :global(.section_3) {
            padding: 20rpx 50rpx;
            background-color: #ffffff;
            border-radius: 50rpx 50rpx 0rpx 0rpx;
            box-shadow: 0rpx -4rpx 32rpx #00000029;
        }

        :global(.pos) {
            position: absolute;
            left: 0;
            right: 0;
            top: 64rpx;
        }

        :global(.image_9) {
            width: 42rpx;
            height: 40rpx;
        }

        :global(.image_8) {
            width: 100rpx;
            height: 100rpx;
        }

        :global(.image_10) {
            width: 38rpx;
            height: 40rpx;
        }

        :global(.image_11) {
            width: 42rpx;
            height: 38rpx;
        }
    }

    :global(.take-photo) {
        :global(.ml-23) {
            margin-left: 41.67rpx;
        }

        :global(.pos) {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
        }

        :global(.popup) {
            padding: 101.45rpx 28.99rpx 97.83rpx;
            background-color: #ffffff;
            border-radius: 36.23rpx 36.23rpx 0rpx 0rpx;
            z-index: 2;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        :global(.group_3) {
            padding: 0 3.62rpx;
        }

        :global(.image_6) {
            border-radius: 7.25rpx;
            width: 68.84rpx;
            height: 54.35rpx;
        }

        :global(.font) {
            font-size: 28.99rpx;
            font-family: PingFangSC;
            line-height: 26.81rpx;
            font-weight: 600;
            color: #000000cc;
        }

        :global(.text_3) {
            line-height: 26.85rpx;
        }

        :global(.font_2) {
            font-size: 25.36rpx;
            font-family: PingFangSC;
            line-height: 24.11rpx;
            color: #00000066;
        }

        :global(.view) {
            margin-top: 119.57rpx;
        }

        :global(.image_7) {
            width: 68.84rpx;
            height: 57.97rpx;
        }

        :global(.text-wrapper) {
            margin-top: 100rpx;
            padding: 28.99rpx 3.62rpx;
        }

        :global(.text_4) {
            line-height: 26.7rpx;
            font-weight: unset;
        }
    }

    /* 专门的滚动容器 */
    :global(.scroll-container) {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
        position: relative;
        height: 100%;

        /* 确保在iOS设备上滚动流畅 */
        -webkit-transform: translateZ(0);
        transform: translateZ(0);

        /* 移动端触摸优化 */
        touch-action: pan-y;

        /* 额外的移动端兼容性优化 */
        overscroll-behavior: contain;
        will-change: scroll-position;

        /* 防止滚动时的闪烁 */
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
    }
}
