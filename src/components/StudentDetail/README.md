# StudentDetail 组件使用说明

## 概述

`StudentDetail` 组件是一个学员详情页面组件，使用 `forwardRef` 和 `useImperativeHandle` 实现，通过 `open` 方法来打开学员详情页面。

## 使用方法

### 1. 导入组件和类型

```tsx
import StudentDetail, { StudentDetailRef } from "@components/StudentDetail";
```

### 2. 创建 ref 引用

```tsx
const studentDetailRef = useRef<StudentDetailRef>(null);
```

### 3. 在组件中使用

```tsx
<StudentDetail
    ref={studentDetailRef}
    onClose={() => {
        // 关闭时的回调处理
        console.log("学员详情页面已关闭");
    }}
/>
```

### 4. 打开学员详情页面

```tsx
// 点击某个学员时调用
const handleStudentClick = (studentId: string) => {
    studentDetailRef.current?.open(studentId);
};
```

## 完整示例

```tsx
import React, { useRef } from "react";
import StudentDetail, { StudentDetailRef } from "@components/StudentDetail";

const StudentList: React.FC = () => {
    const studentDetailRef = useRef<StudentDetailRef>(null);

    const handleStudentClick = (studentId: string) => {
        studentDetailRef.current?.open(studentId);
    };

    return (
        <view>
            {/* 学员列表 */}
            <view onClick={() => handleStudentClick("student-123")}>点击查看学员详情</view>

            {/* 学员详情组件 */}
            <StudentDetail
                ref={studentDetailRef}
                onClose={() => {
                    console.log("学员详情页面已关闭");
                }}
            />
        </view>
    );
};

export default StudentList;
```

## API

### Props

| 属性    | 类型         | 默认值 | 说明             |
| ------- | ------------ | ------ | ---------------- |
| onClose | `() => void` | -      | 关闭时的回调函数 |

### Ref 方法

| 方法 | 参数                          | 说明             |
| ---- | ----------------------------- | ---------------- |
| open | `(studentId: string) => void` | 打开学员详情页面 |

## 注意事项

1. 组件使用了 `forwardRef` 包装，需要通过 ref 来调用 `open` 方法
2. 组件内部维护了显示状态，无需外部控制显示/隐藏
3. 关闭时会自动清空 `studentId` 状态
4. 支持通过右上角关闭按钮或 `onClose` 回调关闭页面
