.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-modal-container {
    width: 90%;
    max-width: 680rpx;
    background-color: #fff;
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.custom-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 30rpx;
    border-bottom: 1px solid #eee;
}

.custom-modal-title {
    font-size: 32rpx;
    // font-weight: 600;
    color: #333;
}

.custom-modal-close {
    font-size: 44rpx;
    color: #999;
    padding: 0 10rpx;
    line-height: 1;
}

.phone-list-container {
    max-height: 75vh;
    display: flex;
    flex-direction: column;
}

.phone-list {
    padding: 20rpx 30rpx;
    max-height: calc(75vh - 200rpx);
    overflow-y: auto;
}

.account-title {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
}

.user-item {
    display: flex;
    justify-content: space-between;
    padding: 30rpx 20rpx;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    background-color: #f9f9f9;
    border: 2rpx solid transparent;
}

.user-item.selected {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.user-info {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.user-name {
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 8rpx;
}

.user-account {
    font-size: 26rpx;
    color: #666;
}

.tenant-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 100rpx;
}

.user-tenant {
    font-size: 26rpx;
    color: #1890ff;
    background-color: rgba(24, 144, 255, 0.1);
    padding: 6rpx 12rpx;
    border-radius: 6rpx;
    text-align: center;
}

.phone-popup-buttons {
    display: flex;
    padding: 20rpx 30rpx 30rpx;
    border-top: 1px solid #eee;
    justify-content: space-between;
}

.cancel-button,
.confirm-button {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    border-radius: 12rpx;
    font-size: 30rpx;
}

.cancel-button {
    background-color: #f5f5f5;
    color: #666;
    margin-right: 20rpx;
}

.confirm-button {
    background-color: #1890ff;
    color: #fff;
}

.confirm-button.disabled {
    background-color: #a6d5fa;
    color: #fff;
}
