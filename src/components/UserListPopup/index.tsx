import { View, Text, Button } from "@tarojs/components";
import "./index.scss";
import { useState } from "react";
import { Close } from "@nutui/icons-react-taro";

interface UserListPopupProps {
    visible: boolean;
    userList: Array<{
        UserId: string;
        UserName: string;
        Phone: string;
        TenantName: string;
        Account: string;
        RealName?: string;
    }>;
    onClose: () => void;
    onSelect: (userId: string) => void;
}

const UserListPopup = ({ visible, userList, onClose, onSelect }: UserListPopupProps) => {
    const [selectedUserId, setSelectedUserId] = useState<string>("");

    const handleConfirm = () => {
        if (selectedUserId) {
            onSelect(selectedUserId);
        }
    };

    const handleOverlayClick = (e) => {
        e.stopPropagation();
        onClose();
    };

    const handleModalClick = (e) => {
        e.stopPropagation();
    };

    if (!visible) return null;

    return (
        <View className="custom-modal-overlay" onClick={handleOverlayClick}>
            <View className="custom-modal-container" onClick={handleModalClick}>
                {/* Modal Header */}
                <View className="custom-modal-header">
                    <Text className="custom-modal-title">选择登录账号</Text>
                    <Close className="close-btn" onClick={() => onClose()} size={20} color="#666" />
                </View>

                {/* Modal Content */}
                <View className="phone-list-container">
                    <View className="phone-list">
                        {/* <Text className="account-title">请选择要登录的账号</Text> */}

                        {userList.length > 0 ? (
                            userList.map((user) => (
                                <View key={user.UserId} className={`user-item ${selectedUserId === user.UserId ? "selected" : ""}`} onClick={() => setSelectedUserId(user.UserId)}>
                                    <View className="user-info">
                                        <Text className="user-name">{user.RealName || user.UserName}</Text>
                                        <Text className="user-account">账号: {user.Account}</Text>
                                    </View>
                                    <View className="tenant-container">
                                        <Text className="user-tenant">{user.TenantName}</Text>
                                    </View>
                                </View>
                            ))
                        ) : (
                            <View style={{ textAlign: "center", padding: "30rpx", color: "#999" }}>没有关联账号</View>
                        )}
                    </View>

                    {/* Modal Footer */}
                    <View className="phone-popup-buttons">
                        {/* 关闭按钮 */}
                        <Button onClick={onClose} className="cancel-button">
                            关闭
                        </Button>

                        {/* 确认按钮 */}
                        <Button onClick={handleConfirm} disabled={!selectedUserId} className={`confirm-button ${!selectedUserId ? "disabled" : ""}`}>
                            确认登录
                        </Button>
                    </View>
                </View>
            </View>
        </View>
    );
};

export default UserListPopup;
