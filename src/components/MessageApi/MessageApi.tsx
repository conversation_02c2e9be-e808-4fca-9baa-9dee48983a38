// src/components/MessageApi/MessageApi.tsx
import { forwardRef, useState, useImperativeHandle, useEffect } from "react";
import { Overlay, Toast } from "@nutui/nutui-react-taro";
import { getDialog, getLoading, getToast, TagType, getAlert } from "@utils/util";
import "./MessageApi.scss";
import { useDidShow, useDidHide } from "@tarojs/taro";
import { View, Text, Button } from "@tarojs/components";
import { Close, Success } from "@nutui/icons-react-taro";

interface MessageApiProps {}

const MessageApi = forwardRef<any, MessageApiProps>((props, ref) => {
    // Toast 状态
    const [toastShow, setToastShow] = useState(false);
    const [toast, setToast] = useState<any>({});

    // Dialog 状态
    const [dialogShow, setDialogShow] = useState(false);
    const [dialog, setDialog] = useState<any>({});

    // Loading 状态跟踪
    const [loadingActive, setLoadingActive] = useState(false);

    // Notification 状态
    const [notificationShow, setNotificationShow] = useState(false);
    const [notificationMessage, setNotificationMessage] = useState("");
    const [type, setType] = useState<"success" | "error">("success");

    // 组件挂载时初始化
    useEffect(() => {
        console.log("MessageApi component has been mounted");

        // 组件卸载时清理
        return () => {
            console.log("MessageApi component will unmount");
            setToastShow(false);
            setDialogShow(false);
            setLoadingActive(false);
        };
    }, []);

    // 使用Taro的页面生命周期钩子 - 页面显示时
    useDidShow(() => {
        console.log("MessageApi: 页面显示");
        // 只重置toast和dialog，保留loading
        setToastShow(false);
        setDialogShow(false);
        // 注意：我们不重置loading状态，让它保持原样
    });

    // 使用Taro的页面生命周期钩子 - 页面隐藏时
    useDidHide(() => {
        console.log("MessageApi: 页面隐藏");
        // 只隐藏toast和dialog，保留loading
        setToastShow(false);
        setDialogShow(false);
        // 注意：我们不重置loading状态，让它保持原样
    });

    // Handle focus management when dialog shows
    useEffect(() => {
        if (dialogShow) {
            // Use setTimeout to ensure the DOM is updated
            setTimeout(() => {
                const confirmButton = document.querySelector(".confirm-button");
                const cancelButton = document.querySelector(".cancel-button");

                if (confirmButton) {
                    (confirmButton as HTMLElement).focus();
                } else if (cancelButton) {
                    (cancelButton as HTMLElement).focus();
                }
            }, 100);
        }
    }, [dialogShow]);

    // Handle overlay click (close dialog if allowed)
    const handleOverlayClick = () => {
        // Only close if closeOnOverlayClick is enabled
        if (dialog.closeOnOverlayClick) {
            setDialogShow(false);
        }
    };

    // Prevent clicks inside the dialog from closing it
    const handleDialogClick = (e) => {
        e.stopPropagation();
    };

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
        openLoading: (msg: string) => {
            // 原代码保留
            setToast(getLoading(msg, () => {}));
            setToastShow(true);
            setLoadingActive(true); // 标记 loading 已激活
            // Taro.showLoading({
            //     title: msg,
            //     mask: true
            // });
        },
        closeLoading: () => {
            // 原代码保留
            // console.log("closeLoading");
            setToastShow(false);
            setLoadingActive(false); // 标记 loading 已结束
            // Taro.hideLoading();
        },
        isLoading: () => {
            return loadingActive; // 返回当前 loading 状态
        },
        openToast: (msg: string, type: TagType, fn?: Function) => {
            console.log("openToast");
            setToast(getToast(msg, type, fn));
            setToastShow(true);
        },
        closeToast: () => {
            setToastShow(false);
        },
        openDialog: (title: string, msg: string, fn?: Function, okText?: string, cancelText?: string, cancelFn?: Function, textAlign?: string) => {
            setDialog(getDialog(title, msg, okText || "确定", cancelText || "取消", fn, cancelFn, textAlign));
            setDialogShow(true);
        },
        openAlert: (msg: string, title: string = "提示", okText: string = "确定", fn?: Function, textAlign?: string) => {
            // setDialog(getAlert(msg, title, okText, fn, textAlign));
            setDialog(
                getDialog(msg, title, okText, "", () => {
                    setDialogShow(false);
                    if (fn) {
                        fn();
                    }
                })
            );
            setDialogShow(true);
        },
        closeDialog: () => {
            setDialogShow(false);
        },
        success: (msg: string, title: string = "操作完成", fn: Function) => {
            setDialog(
                getDialog(
                    title,
                    msg,
                    "确定",
                    "",
                    () => {
                        setDialogShow(false);
                        if (fn) {
                            fn();
                        }
                    },
                    undefined
                )
            );
            setDialogShow(true);
        },
        error: (msg: string, title: string = "错误提示", fn: Function) => {
            setDialog(
                getDialog(
                    title,
                    msg,
                    "确定",
                    "",
                    () => {
                        setDialogShow(false);
                        if (fn) {
                            fn();
                        }
                    },
                    undefined
                )
            );
            setDialogShow(true);
        },
        openConfirm: (msg: string, title: string, fn: Function) => {
            console.log("openConfirm", msg, title, fn);
            setDialog(getDialog(title, msg, "确定", "取消", fn));
            setDialogShow(true);
        },
        showNotification: (message: string, type: "success" | "error" = "success") => {
            setNotificationMessage(message);
            setNotificationShow(true);
            setType(type);

            // 3秒后自动隐藏
            setTimeout(() => {
                setNotificationShow(false);
            }, 3000);
        },
    }));

    return (
        <>
            {/* Notification Bar */}
            {notificationShow && (
                <>
                    <View className={`notification-bar ${notificationShow ? "show" : ""}`}>
                        {type === "success" && <View className="notification-icon success-icon">✓</View>}
                        {type === "error" && <View className="notification-icon error-icon">✕</View>}
                        <Text className="notification-message">{notificationMessage}</Text>
                    </View>
                </>
            )}

            {/* Toast 相关代码 */}
            {toastShow && !dialogShow && (
                <Overlay
                    visible={toastShow}
                    style={{
                        zIndex: 1100,
                    }}
                >
                    {loadingActive ? (
                        <View
                            style={{
                                position: "fixed",
                                top: "50%",
                                left: "50%",
                                transform: "translate(-50%, -50%)",
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                                justifyContent: "center",
                                minWidth: "400rpx",
                                background: "#fff",
                                borderRadius: "16rpx",
                                padding: "80rpx 64rpx 48rpx 64rpx",
                            }}
                        >
                            <View className="custom-spinner" />
                            <View style={{ fontSize: "32rpx", color: "#333", marginTop: "62rpx" }}>{toast.msg}</View>
                        </View>
                    ) : (
                        <>
                            {toast.icon && toast.icon != "" && (
                                <Toast
                                    content={toast.msg}
                                    visible={toastShow}
                                    type={toast.type}
                                    onClose={() => {
                                        setToastShow(false);
                                        toast.fn();
                                    }}
                                    duration={toast.duration}
                                    icon={<toast.icon />}
                                    iconSize="20"
                                    style={{
                                        zIndex: 1101,
                                    }}
                                />
                            )}
                            {(!toast.icon || toast.icon == "") && (
                                <Toast
                                    content={toast.msg}
                                    visible={toastShow}
                                    type={toast.type}
                                    onClose={() => {
                                        setToastShow(false);
                                        toast.fn();
                                    }}
                                    duration={toast.duration}
                                    iconSize="20"
                                    style={{
                                        zIndex: 1101,
                                    }}
                                />
                            )}
                        </>
                    )}
                </Overlay>
            )}

            {/* 自定义 Dialog 实现 */}
            {dialogShow && (
                <View className="custom-modal-overlay" onClick={handleOverlayClick} style={{ zIndex: 999999 }}>
                    <View className="custom-modal-container" onClick={handleDialogClick}>
                        {/* Dialog Header */}
                        <View className="custom-modal-header" style={{ justifyContent: "flex-start" }}>
                            <Text className="custom-modal-title" style={{ textAlign: "left" }}>
                                {dialog.title}
                            </Text>
                            <View
                                className="custom-modal-close"
                                onClick={() => {
                                    setDialogShow(false);
                                    if (dialog.cancel) dialog.cancel();
                                }}
                            >
                                <Close />
                            </View>
                        </View>

                        {/* Dialog Content */}
                        <View className="dialog-content-container">
                            <View
                                className="dialog-content"
                                style={{
                                    textAlign: dialog.msg.length < 19 ? "center" : "left",
                                }}
                            >
                                {dialog.msg}
                            </View>
                        </View>

                        {/* Dialog Footer */}
                        <View className={`dialog-popup-buttons ${dialog.footerDirection === "horizontal" ? "horizontal" : "vertical"}`}>
                            {!dialog.noCancelBtn && (
                                <Button
                                    onClick={() => {
                                        dialog.cancel();
                                        setDialogShow(false);
                                    }}
                                    className="cancel-btn-custom"
                                    style={{ borderRadius: "8px" }}
                                >
                                    {dialog.cancelText}
                                </Button>
                            )}
                            <Button
                                onClick={() => {
                                    dialog.ok();
                                    setDialogShow(false);
                                }}
                                className="confirm-btn-custom"
                                style={{ borderRadius: "8px" }}
                            >
                                {dialog.okText}
                            </Button>
                        </View>
                    </View>
                </View>
            )}
        </>
    );
});

export default MessageApi;
