import React, { PropsWithChildren } from "react";
import MessageA<PERSON> from "./MessageApi";
import { useDidShow, useDidHide } from "@tarojs/taro";

// 创建随时可更新的引用
const MessageApiRef = React.createRef<any>();

type ToastFunction = {
    (msg: string): void; // 单参数重载
    (msg: string, type: string, fn?: Function): void; // 完整参数重载
};

// 定义 MessageApiType 接口
export interface MessageApiType {
    openLoading: (msg: string) => void;
    closeLoading: () => void;
    isLoading?: () => boolean; // 新增：检查是否有loading正在显示
    openToast: ToastFunction;
    closeToast: () => void;
    openDialog: (title: string, msg: string, fn?: Function, okText?: string, cancelText?: string, cancelFn?: Function, textAlign?: string) => void;
    openAlert: (msg: string, title?: string, okText?: string, fn?: Function, textAlign?: string) => void;
    closeDialog: () => void;
    success: (msg: string, title?: string, fn?: Function) => void;
    error: (msg: string, title?: string, fn?: Function) => void;
    openConfirm: (msg: string, title: string, fn: Function) => void;
    showNotification: (content: string, type?: "success" | "error") => void;
}

// 使用 MessageApiType 类型来定义 message 对象
export const message: MessageApiType = {
    openLoading: (msg: string) => MessageApiRef.current?.openLoading(msg),
    closeLoading: () => MessageApiRef.current?.closeLoading(),
    isLoading: () => MessageApiRef.current?.isLoading() || false, // 新增：检查是否有loading正在显示
    openToast: ((msg: string, type?: string, fn?: Function) => MessageApiRef.current?.openToast(msg, type || "", fn)) as ToastFunction,
    closeToast: () => MessageApiRef.current?.closeToast(),
    openDialog: (title, msg, fn, okText, cancelText, cancelFn, textAlign) => MessageApiRef.current?.openDialog(title, msg, fn, okText, cancelText, cancelFn, textAlign),
    openAlert: (msg, title = "提示", okText = "确定", fn, textAlign) => MessageApiRef.current?.openAlert(msg, title, okText, fn, textAlign),
    closeDialog: () => MessageApiRef.current?.closeDialog(),
    success: (msg, title = "操作完成", fn = undefined) => MessageApiRef.current?.success(msg, title, fn),
    error: (msg, title = "操作失败", fn = undefined) => MessageApiRef.current?.error(msg, title, fn),
    openConfirm: (msg, title, fn) => MessageApiRef.current?.openConfirm(msg, title, fn),
    showNotification: (content, type = "success") => MessageApiRef.current?.showNotification(content, type),
};

// 添加一个重置函数
export const resetMessageApi = (closeLoading: boolean = false) => {
    // 重置操作如需要
    console.log("重置MessageApi引用", closeLoading ? "关闭loading" : "保留loading");
    if (MessageApiRef.current) {
        MessageApiRef.current.closeToast();
        MessageApiRef.current.closeDialog();
        // 只有在明确要求时才关闭 loading
        if (closeLoading) {
            MessageApiRef.current.closeLoading();
        }
    }
};

// 使用函数组件来访问Taro的页面生命周期钩子
export const MessageApiProvider: React.FC<PropsWithChildren<{}>> = ({ children }) => {
    // 使用Taro的页面生命周期钩子
    useDidShow(() => {
        console.log("MessageApiProvider: 页面显示");
        resetMessageApi(false); // 不关闭loading
    });

    useDidHide(() => {
        console.log("MessageApiProvider: 页面隐藏");
        // 页面隐藏时也重置相关状态，但保留loading
        resetMessageApi(false); // 不关闭loading
    });

    return (
        <>
            <MessageApi ref={MessageApiRef} />
            {children}
        </>
    );
};
