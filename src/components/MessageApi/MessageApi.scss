// .my-dialog {
//     .nut-dialog-header {
//         font-weight: 800;
//     }

//     .nut-dialog-footer-ok {
//         border-radius: 20rpx;
//         background-color: #2b5de0;
//     }

//     .nut-button {
//         border-radius: 20px;
//         height: 66rpx;
//     }

//     .nut-button .nut-button-wrap {
//         font-size: 26rpx;
//         line-height: normal;
//     }

//     .nut-button {
//         line-height: normal;
//         color: #2b5de0;
//     }
// }
// 遮罩层
.nut-overlay.nut-dialog-overlay {
    background: rgba(0, 0, 0, 0.6) !important;
}

// 对话框容器
.nut-dialog-outer {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    z-index: 2000;
}

// 主对话框
.nut-dialog {
    // width: 460px !important;
    border-radius: 12px !important;
    background: #ffffff !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1) !important;
    overflow: hidden;
    // width: 90vw !important;
}

// 标题和标题下的线条
.nut-dialog-title {
    color: #333 !important;
    font-size: 30px !important;
    font-weight: normal !important;
    text-align: center;
    padding: 20px 16px 8px !important;
    position: relative;

    &::after {
        content: "";
        display: block;
        width: 30px;
        height: 2px;
        background: linear-gradient(135deg, rgb(22, 93, 255) 0%, rgb(22, 93, 255) 100%);
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 8px;
    }
}

// 对话框内容
.nut-dialog-content {
    padding: 8px 8px 24px 8px !important;
    min-height: 40px;
    color: #666 !important;
    // font-size: 26px !important;
    text-align: center;
    // line-height: 1.5;
    margin: 0;
    view {
        margin-top: 0;
    }
}

// 底部按钮区域
.nut-dialog-footer {
    padding: 0 0 20rpx 0 !important;
    text-align: center;

    .nut-button {
        width: 100% !important;
        line-height: normal;
        height: 40px;
        // height: 88px !important;
        // line-height: 88px !important;
        border-radius: 44rpx !important;
        // font-size: 28px !important;
        color: #fff !important;
        // background: linear-gradient(120deg, #2c5ecc, #4c37c3) !important;
        border: none !important;
        box-shadow: 0 4px 8px rgba(44, 94, 204, 0.2);

        &:active {
            opacity: 0.9;
        }
    }
    .nut-button.nut-dialog-footer-cancel {
        color: #2c5ecc !important; // 使用主色
        background: transparent !important; // 透明背景
        border: 0.5px solid #2c5ecc !important; // 添加边框
        box-shadow: none; // 移除阴影
        // margin-bottom: 15px;
    }
}

// 确保弹窗垂直居中
.h5-div.nut-dialog-outer.my-dialog {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    max-width: 90% !important;
}

// 内容区样式
.nut-dialog-content .h5-div {
    font-size: 26px !important;
    color: #666 !important;
}

// 蓝色下划线
.nut-dialog-header::after {
    content: "";
    display: block;
    width: 40px;
    height: 3px;
    margin: 8px auto 0;
    background: linear-gradient(135deg, rgb(22, 93, 255) 0%, rgb(22, 93, 255) 100%);
    border-radius: 2px;
}
.nut-dialog-footer {
    padding: 0 0 10rpx 0 !important;
}

// Custom Dialog styles
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-modal-container {
    background-color: #fff;
    border-radius: 12px;
    width: 85%;
    max-width: 650rpx;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 999999;
}

.custom-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 24rpx 30rpx;
    border-bottom: 1px solid #eee;
    position: relative;
}

.custom-modal-title {
    font-size: 32rpx;
    // font-weight: 500;
    color: #333;
    text-align: left !important;
    justify-self: flex-start;
    margin-right: auto;
}

.custom-modal-close {
    position: absolute;
    right: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    cursor: pointer;
    color: #999;
}

.dialog-content-container {
    padding: 30rpx;
}

.dialog-content {
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    line-height: 50rpx;
    width: 100%;
    overflow-y: auto;
    word-break: break-word;
}

.dialog-popup-buttons {
    display: flex;
    padding: 20rpx 30rpx;
    border-top: 1px solid #eee;

    &.horizontal {
        flex-direction: row;
        justify-content: space-between;
        gap: 20rpx;
    }

    &.vertical {
        flex-direction: column;
        gap: 20rpx;
    }

    // .cancel-btn-custom {
    //     width: 100%;
    //     flex: 1;
    //     color: #666 !important;
    //     background: #f5f5f5 !important;
    //     border: none !important;
    //     border-radius: 44rpx !important;
    //     padding: 20rpx 0 !important;
    //     text-align: center !important;
    //     font-size: 30rpx !important;
    //     height: 88rpx;
    //     line-height: normal;

    //     &::before {
    //         display: none;
    //     }

    //     &:active {
    //         background-color: rgba(0, 0, 0, 0.05) !important;
    //     }
    // }

    .cancel-btn-custom {
        width: 100%;
        height: 88rpx;
        background: #f5f5f5;
        color: #666;
        font-size: 32rpx;
        border: none;
        border-radius: 44rpx !important;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        &::after {
            border: none;
            border-radius: 44rpx;
        }
        &:active {
            opacity: 0.8;
        }
    }

    .confirm-btn-custom {
        width: 100%;
        flex: 1;
        background-color: #2f54eb !important;
        color: #fff !important;
        border-radius: 44rpx !important;
        padding: 20rpx 0 !important;
        text-align: center !important;
        font-size: 30rpx !important;
        transition: all 0.3s ease;
        height: 88rpx;
        line-height: normal;

        &:active {
            background-color: #1d39c4 !important;
        }
    }
}

// Notification Bar styles
.notification-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 20rpx 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    z-index: 9999;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);

    &.show {
        transform: translateY(0);
    }

    .notification-icon {
        margin-right: 16rpx;
        font-size: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;

        &.success-icon {
            background-color: #4caf50;
            color: #fff;
        }

        &.error-icon {
            background-color: #f44336;
            color: #fff;
        }
    }

    .notification-message {
        font-size: 28rpx;
        color: #333;
    }
}

// Custom Spinner
.custom-spinner {
    width: 80rpx;
    height: 80rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #2f54eb;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
