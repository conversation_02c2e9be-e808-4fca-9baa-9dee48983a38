import React, { createContext, useContext, PropsWithChildren } from "react";
import { MessageApiProvider } from "./MessageApiSingleton";
import { useDidShow, useDidHide } from "@tarojs/taro";

const MessageApiContext = createContext<null | React.FC>(null);

export const MessageApiProviderWithContext: React.FC<PropsWithChildren<{}>> = ({ children }) => {
    // 使用Taro的页面生命周期钩子 - 页面显示
    useDidShow(() => {
        console.log("MessageApiContext: 页面显示");
    });

    // 使用Taro的页面生命周期钩子 - 页面隐藏
    useDidHide(() => {
        console.log("MessageApiContext: 页面隐藏");
    });

    return (
        <MessageApiProvider>
            <MessageApiContext.Provider value={null}>{children}</MessageApiContext.Provider>
        </MessageApiProvider>
    );
};

export const useMessageApi = () => useContext(MessageApiContext);
