// src/components/Layout.tsx
import React, { PropsWithChildren, useState } from "react";
import { View } from "@tarojs/components";
import { MessageApiProviderWithContext } from "./MessageApi/MessageApiContext";
import Taro, { useDidShow, useDidHide } from "@tarojs/taro";
import { resetMessageApi } from "./MessageApi/MessageApiSingleton";

const Layout: React.FC<PropsWithChildren<{}>> = ({ children }) => {
    // 使用状态来触发重新渲染
    const [renderKey, setRenderKey] = useState(Date.now());

    // 使用Taro的页面生命周期钩子
    useDidShow(() => {
        console.log("Layout页面显示，强制重新渲染");
        // 重置MessageApi状态，但保留loading
        resetMessageApi(false); // 不关闭loading
        // 强制重新渲染整个布局
        setRenderKey(Date.now());
    });

    // 页面隐藏时也可以做一些清理工作
    useDidHide(() => {
        console.log("Layout页面隐藏");
        // 可以在这里执行一些清理操作
    });

    return (
        <MessageApiProviderWithContext key={renderKey}>
            <View className="layout">{children}</View>
        </MessageApiProviderWithContext>
    );
};

export default Layout;
