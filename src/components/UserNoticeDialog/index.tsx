import React from "react";
import { View, Text } from "@tarojs/components";
import "./index.scss";

interface UserNoticeDialogProps {
    show: boolean;
    isLoading: boolean;
    onConfirm: () => void;
    onExit: () => void;
}

const UserNoticeDialog: React.FC<UserNoticeDialogProps> = ({ show, isLoading, onConfirm, onExit }) => {
    if (!show) return null;

    return (
        <View className="custom-dialog-overlay">
            <View className="custom-dialog">
                <View className="custom-dialog-header">
                    <Text className="custom-dialog-title">请注意</Text>
                </View>
                <View className="custom-dialog-content" style={{ fontSize: "26rpx" }}>
                    <Text className="custom-dialog-message">本小程序是驾校内部的管理系统的一部分，只允许以下人员使用：</Text>
                    <View className="dialog-list">
                        <View className="dialog-list-item">• 驾校内部员工</View>
                        <View className="dialog-list-item">• 教练</View>
                        <View className="dialog-list-item">• 已完成登记的学员</View>
                    </View>
                    <Text className="custom-dialog-message">这些用户需要利用个人身份验证登录。如果您不属于以上人员，请退出当前程序。</Text>
                </View>
                <View className="custom-dialog-footer">
                    <View className="custom-dialog-btn exit" onClick={onExit}>
                        退出当前程序
                    </View>
                    <View className={`custom-dialog-btn confirm ${isLoading ? "disabled" : ""}`} onClick={!isLoading ? onConfirm : undefined}>
                        {isLoading ? "处理中..." : "我已经知道了"}
                    </View>
                </View>
            </View>
        </View>
    );
};

export default UserNoticeDialog;
