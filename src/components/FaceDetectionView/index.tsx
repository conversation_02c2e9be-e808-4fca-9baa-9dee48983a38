import React, { useEffect, useRef, useState, forwardRef, useImperativeHandle } from "react";
import { View, Camera } from "@tarojs/components";
import Taro from "@tarojs/taro";
import upload from "@service/upload";
import "./index.scss";

interface FaceDetectionViewProps {
    onError?: (error: any) => void;
    onAuthSuccess?: (result: any) => void;
    onAuthFailed?: (message: string, remainingAttempts: number) => void;
    onAllAttemptsFailed?: () => void;
}

export interface FaceDetectionViewRef {
    open: () => void;
    close: () => void;
    stopDetection: () => void;
}

const FaceDetectionView = forwardRef<FaceDetectionViewRef, FaceDetectionViewProps>(({ onError, onAuthSuccess, onAuthFailed, onAllAttemptsFailed }, ref) => {
    const [visible, setVisible] = useState(false);
    const [isCustomNav, setIsCustomNav] = useState(false);
    const [statusBarHeight, setStatusBarHeight] = useState(0);
    const [isRecording, setIsRecording] = useState(false);
    const [uploadStatus, setUploadStatus] = useState("");
    const [failedAttempts, setFailedAttempts] = useState(0);
    const [countdown, setCountdown] = useState(0);
    const cameraContextRef = useRef<any>(null);
    const recordingTimerRef = useRef<any>(null);
    const countdownIntervalRef = useRef<any>(null);

    useImperativeHandle(ref, () => ({
        open: () => {
            setVisible(true);
            setFailedAttempts(0);
            checkNavigationStyle();
            initFaceDetector();
        },
        close: () => {
            stopRecording();
            setVisible(false);
        },
        stopDetection: () => {
            stopRecording();
        },
    }));

    useEffect(() => {
        return () => {
            stopRecording();
        };
    }, []);

    const checkNavigationStyle = () => {
        try {
            // 获取系统信息
            const systemInfo = Taro.getSystemInfoSync();
            const statusBarHeightPx = systemInfo.statusBarHeight || 0;

            // 获取当前页面的路由信息
            const pages = Taro.getCurrentPages();
            const currentPage = pages[pages.length - 1];

            // 检查页面配置中的navigationStyle
            if (currentPage?.config?.navigationStyle === "custom") {
                setIsCustomNav(true);
                setStatusBarHeight(statusBarHeightPx);
            } else {
                setIsCustomNav(false);
                setStatusBarHeight(0);
            }
            console.log("🟡 当前页面配置中的navigationStyle:", currentPage);
            console.log("🟡 当前页面配置中的statusBarHeight:", statusBarHeightPx);
        } catch (error) {
            console.log("检查导航样式失败:", error);
            setIsCustomNav(false);
            setStatusBarHeight(0);
        }
    };

    const initFaceDetector = async () => {
        try {
            // 获取相机上下文
            const cameraContext = Taro.createCameraContext();
            cameraContextRef.current = cameraContext;
        } catch (error) {
            console.error("初始化相机失败:", error);
            onError?.(error);
        }
    };

    const recordVideoAndUpload = async () => {
        if (!cameraContextRef.current) {
            console.error("相机上下文未初始化");
            return;
        }

        try {
            setUploadStatus("开始录制视频...");
            setCountdown(5);

            // 开始录制视频
            cameraContextRef.current.startRecord({
                success: () => {
                    console.log("开始录制视频");

                    // 开始倒计时显示
                    countdownIntervalRef.current = setInterval(() => {
                        setCountdown((prev) => {
                            if (prev <= 1) {
                                clearInterval(countdownIntervalRef.current);
                                countdownIntervalRef.current = null;
                                return 0;
                            }
                            return prev - 1;
                        });
                    }, 1000);

                    // 5秒后停止录制
                    recordingTimerRef.current = setTimeout(() => {
                        setUploadStatus("录制完成，正在处理...");

                        // 停止录制
                        cameraContextRef.current.stopRecord({
                            success: async (res) => {
                                console.log("录制完成", res.tempVideoPath);
                                setUploadStatus("正在上传视频...");

                                try {
                                    // 上传视频进行人脸认证
                                    const result = await upload.post<{
                                        success: boolean;
                                        message: string;
                                        data: any;
                                    }>(
                                        "/JiaXiao/Face/Student/faceVideoAuth", // 人脸认证API endpoint
                                        {},
                                        res.tempVideoPath,
                                        "file"
                                    );

                                    if (result.success) {
                                        setUploadStatus("认证成功");
                                        onAuthSuccess?.({
                                            ...result.data,
                                            video: res.tempVideoPath,
                                        });
                                        // 认证成功后停止检测
                                        stopRecording();
                                    } else {
                                        // 认证失败，停止检测并恢复按钮状态
                                        const newFailedAttempts = failedAttempts + 1;
                                        setFailedAttempts(newFailedAttempts);
                                        setUploadStatus(`认证失败: ${result.message}`);
                                        onAuthFailed?.(result.message, newFailedAttempts);

                                        // 停止检测，让用户手动重新开始
                                        stopRecording(false);
                                    }
                                } catch (uploadError) {
                                    console.error("上传失败:", uploadError);
                                    setUploadStatus("上传失败");
                                    onError?.(uploadError);
                                    stopRecording(false);
                                }
                            },
                            fail: (error) => {
                                console.error("停止录制失败:", error);
                                setUploadStatus("录制失败");
                                onError?.(error);
                                stopRecording(false);
                            },
                        });
                    }, 5000);
                },
                fail: (error) => {
                    console.error("开始录制失败:", error);
                    setUploadStatus("录制失败");
                    onError?.(error);
                    stopRecording(false);
                },
            });
        } catch (error) {
            console.error("录制过程出错:", error);
            setUploadStatus("录制出错");
            onError?.(error);
            stopRecording(false);
        }
    };

    const startRecording = () => {
        if (!cameraContextRef.current) {
            console.error("相机上下文未初始化");
            return;
        }

        if (isRecording) {
            console.log("录制已在进行中");
            return;
        }

        // 重置状态
        setFailedAttempts(0);
        setIsRecording(true);
        setCountdown(0);
        setUploadStatus("准备录制...");

        // 开始录制
        recordVideoAndUpload();

        console.log("开始录制5秒视频");
    };

    const stopRecording = (clearStatus: boolean = true) => {
        if (recordingTimerRef.current) {
            clearTimeout(recordingTimerRef.current);
            recordingTimerRef.current = null;
        }

        if (countdownIntervalRef.current) {
            clearInterval(countdownIntervalRef.current);
            countdownIntervalRef.current = null;
        }

        // 如果正在录制，停止录制
        if (isRecording && cameraContextRef.current) {
            try {
                cameraContextRef.current.stopRecord({
                    success: () => {
                        console.log("强制停止录制成功");
                    },
                    fail: (error) => {
                        console.error("强制停止录制失败:", error);
                    },
                });
            } catch (error) {
                console.error("停止录制异常:", error);
            }
        }

        setIsRecording(false);
        setCountdown(0);

        // 只有在需要清空状态时才清空
        if (clearStatus) {
            setUploadStatus("");
        }

        console.log("停止录制");
    };

    const handleStopRecording = () => {
        stopRecording(true); // 用户手动停止时清空状态
    };

    if (!visible) {
        return null;
    }

    return (
        <View
            className="face-detection-container"
            style={{
                paddingTop: `${statusBarHeight + 50}px`,
            }}
        >
            {/* 顶部标题栏 */}
            <View className="fdv-header">
                <View className="fdv-header-title">刷脸认证</View>
            </View>
            {/* 提示文字 */}
            <View className="fdv-tip">脸放置取景框内，点击同意按钮开始录制5秒视频</View>
            {/* 圆形取景框和相机 */}
            <View className="fdv-camera-wrapper">
                <View className="fdv-camera-mask">
                    <Camera className="fdv-camera" devicePosition="front" flash="off" frameSize="medium" mode="normal" resolution="medium" />
                    <View className="fdv-camera-icon" />
                    {/* 倒计时显示 */}
                    {countdown > 0 && <View className="fdv-countdown">{countdown}</View>}
                </View>
            </View>
            {/* 状态显示 */}
            {uploadStatus && <View className="fdv-status">{uploadStatus}</View>}
            {/* 说明文字 */}
            <View className="fdv-desc-row">
                <View className="fdv-desc-item">正对手机</View>
                <View className="fdv-desc-item">光线充足</View>
                <View className="fdv-desc-item">脸无遮挡</View>
            </View>
            {/* 按钮 */}
            <View className="fdv-btn-container">
                {!isRecording ? (
                    <View className="fdv-btn" onClick={startRecording}>
                        同意协议，开始录制
                    </View>
                ) : (
                    <View className="fdv-btn fdv-btn-stop" onClick={handleStopRecording}>
                        停止录制
                    </View>
                )}
            </View>
            {/* 隐私说明 */}
            <View className="fdv-privacy-notice">
                <View className="fdv-privacy-item">1. 不会保存认证视频资料</View>
                <View className="fdv-privacy-item">2. 仅用于人脸对比算法验证</View>
                <View className="fdv-privacy-item">3. 不会用于其他用途</View>
                <View className="fdv-privacy-item">4. 对比完成后将自动清理所有缓存数据</View>
                <View className="fdv-privacy-item">5. 最多尝试3次，失败后自动停止</View>
            </View>
        </View>
    );
});

export default FaceDetectionView;
