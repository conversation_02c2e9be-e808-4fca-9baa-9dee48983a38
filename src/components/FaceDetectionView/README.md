# FaceDetectionView 人脸检测组件

## 功能说明

这是一个人脸检测和认证组件，支持：

-   实时相机预览
-   每 2 秒自动拍照
-   自动上传服务器进行人脸认证
-   认证成功/失败回调
-   **最多 3 次认证机会**
-   **认证失败后随机 1-3 秒间隔重试**

## 使用方法

```tsx
import React, { useRef } from "react";
import FaceDetectionView, { FaceDetectionViewRef } from "@components/FaceDetectionView";

const YourComponent = () => {
    const faceDetectionRef = useRef<FaceDetectionViewRef>(null);

    const handleAuthSuccess = (result: any) => {
        console.log("人脸认证成功:", result);
        // 处理认证成功逻辑
    };

    const handleAuthFailed = (message: string, remainingAttempts: number) => {
        console.log("人脸认证失败:", message, "剩余次数:", remainingAttempts);
        // 处理认证失败逻辑
    };

    const handleAllAttemptsFailed = () => {
        console.log("所有认证尝试均失败");
        // 处理所有尝试失败的逻辑，如关闭弹窗或跳转页面
    };

    const handleError = (error: any) => {
        console.log("组件错误:", error);
        // 处理错误逻辑
    };

    const openFaceDetection = () => {
        faceDetectionRef.current?.open();
    };

    const closeFaceDetection = () => {
        faceDetectionRef.current?.close();
    };

    return (
        <div>
            <button onClick={openFaceDetection}>开始人脸认证</button>

            <FaceDetectionView
                ref={faceDetectionRef}
                onAuthSuccess={handleAuthSuccess}
                onAuthFailed={handleAuthFailed}
                onAllAttemptsFailed={handleAllAttemptsFailed}
                onError={handleError}
                onFaceDetected={(count) => {
                    console.log("检测到人脸数量:", count);
                }}
            />
        </div>
    );
};
```

## API 参数

### Props

| 参数                | 类型                                                   | 必填 | 说明                       |
| ------------------- | ------------------------------------------------------ | ---- | -------------------------- |
| onFaceDetected      | `(faceCount: number) => void`                          | 否   | 人脸检测回调               |
| onError             | `(error: any) => void`                                 | 否   | 错误回调                   |
| onAuthSuccess       | `(result: any) => void`                                | 否   | 认证成功回调               |
| onAuthFailed        | `(message: string, remainingAttempts: number) => void` | 否   | 认证失败回调（含剩余次数） |
| onAllAttemptsFailed | `() => void`                                           | 否   | 所有尝试失败回调           |

### Ref 方法

| 方法            | 说明                   |
| --------------- | ---------------------- |
| open()          | 打开人脸检测界面       |
| close()         | 关闭人脸检测界面       |
| stopDetection() | 停止检测（不关闭界面） |

## 工作流程

1. 调用 `open()` 打开检测界面
2. 用户点击"同意协议，开始认证"按钮
3. 组件立即拍照并开始每 2 秒定时拍照
4. 每张照片自动上传到服务器进行人脸认证
5. **认证成功时调用 `onAuthSuccess` 并自动停止检测**
6. **认证失败时：**
    - 调用 `onAuthFailed` 并传入剩余尝试次数
    - 暂停检测 1-3 秒（随机间隔）
    - 如果还有剩余次数，继续检测
    - 如果达到 3 次失败，调用 `onAllAttemptsFailed` 并停止检测
7. 用户可随时点击"停止检测"按钮停止

## 重试机制详解

-   **最大尝试次数**: 3 次
-   **失败间隔**: 每次失败后随机等待 1-3 秒
-   **计数显示**: 实时显示剩余尝试次数
-   **自动停止**: 达到最大尝试次数后自动停止
-   **状态提示**: 显示详细的失败原因和等待时间

## 注意事项

-   需要用户授权相机权限
-   确保网络连接正常
-   服务器 API endpoint: `/JiaXiao/Face/Student/faceAuth`
-   照片质量设置为 `high`
-   建议在光线充足的环境下使用
-   **认证失败 3 次后将自动停止检测**
-   **每次失败后会有 1-3 秒的随机等待时间**
