.face-detection-container {
    font-family: <PERSON><PERSON>, STYuanti-SC-Regular, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;

    position: fixed;
    top: 0;
    left: 0;
    width: 750rpx;
    height: 100vh;
    background-color: #fff;
    z-index: 1000;
    display: flex;
    flex-direction: column;

    .fdv-header {
        width: 750rpx;
        height: 96rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        background: #fff;
        font-weight: bold;
        font-size: 40rpx;
        margin-top: 0;
    }
    .fdv-header-title {
        font-size: 40rpx;
        color: #222;
        font-weight: 600;
    }
    .fdv-sub-title {
        margin-top: 24rpx;
        font-size: 36rpx;
        color: #222;
        font-weight: 600;
        text-align: center;
    }
    .fdv-tip {
        margin-top: 32rpx;
        font-size: 32rpx;
        color: #666;
        text-align: center;
    }
    .fdv-camera-wrapper {
        width: 750rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 48rpx;
        margin-bottom: 48rpx;
    }
    .fdv-camera-mask {
        position: relative;
        width: 70vw;
        height: 70vw;
        border-radius: 50%;
        overflow: hidden;
        background: #111;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 0 0 8rpx #1563b3, 0 8rpx 32rpx 0 rgba(21, 99, 179, 0.1);
    }
    .fdv-camera {
        width: 70vw;
        height: 70vw;
        border-radius: 50%;
        object-fit: cover;
        z-index: 1;
    }
    .fdv-camera-icon {
        position: absolute;
        top: 46%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 54vw;
        height: 54vw;
        background: url("https://cdn.51panda.com/face.png") no-repeat center/contain;
        z-index: 2;
        pointer-events: none;
    }
    .fdv-countdown {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 3;
        font-size: 120rpx;
        font-weight: bold;
        color: #fff;
        text-shadow: 2rpx 2rpx 8rpx rgba(0, 0, 0, 0.8);
        background: rgba(21, 99, 179, 0.8);
        border-radius: 50%;
        width: 200rpx;
        height: 200rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: countdown-pulse 1s infinite;
    }
    @keyframes countdown-pulse {
        0% {
            transform: translate(-50%, -50%) scale(1);
        }
        50% {
            transform: translate(-50%, -50%) scale(1.1);
        }
        100% {
            transform: translate(-50%, -50%) scale(1);
        }
    }
    .fdv-desc-row {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 24rpx;
        margin-bottom: 64rpx;
        gap: 64rpx;
    }
    .fdv-desc-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 30rpx;
        color: #1563b3;
        font-weight: 500;
    }

    .fdv-attempts {
        text-align: center;
        font-size: 26rpx;
        color: #ff6b35;
        font-weight: 600;
        margin: 16rpx 0;
        padding: 8rpx 16rpx;
        background: rgba(255, 107, 53, 0.1);
        border-radius: 20rpx;
        margin-left: 15%;
        margin-right: 15%;
        border: 1rpx solid rgba(255, 107, 53, 0.3);
    }

    .fdv-status {
        text-align: center;
        font-size: 28rpx;
        color: #1563b3;
        font-weight: 500;
        margin: 24rpx 0;
        padding: 12rpx 24rpx;
        background: rgba(21, 99, 179, 0.1);
        border-radius: 24rpx;
        margin-left: 10%;
        margin-right: 10%;
    }

    .fdv-btn-container {
        display: flex;
        justify-content: center;
        width: 100%;
        margin-bottom: 64rpx;
    }

    .fdv-btn {
        width: 80%;
        height: 80rpx;
        flex: none;
        background-color: #2f54eb !important;
        color: #fff !important;
        border-radius: 44rpx !important;
        padding: 20rpx 0 !important;
        text-align: center !important;
        font-size: 30rpx !important;
        transition: all 0.3s ease;
        line-height: normal;
        font-weight: 600;
        box-shadow: 0 4rpx 16rpx 0 rgba(21, 99, 179, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .fdv-btn:active {
        background-color: #1d39c4 !important;
    }

    .fdv-btn-stop {
        background-color: #ff4d4f !important;

        &:active {
            background-color: #d9363e !important;
        }
    }
}

.fdv-privacy-notice {
    padding: 20px;
    margin: 20px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;

    .fdv-privacy-item {
        font-size: 24px;
        color: #666;
        line-height: 1.6;
        margin-bottom: 8px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}
