.signature-board-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: #fff;
    z-index: 9999;
    display: flex;
    flex-direction: column;

    // 横屏模式样式
    &.landscape {
        .signature-board-container {
            padding: 20rpx 40rpx;
        }

        .signature-header {
            // margin-bottom: 20rpx;
            padding: 10rpx 0;
        }

        .signature-title {
            font-size: 32rpx;
        }

        .signature-content-area {
            gap: 20rpx; // 横屏时减小间距
        }

        .signature-canvas-container {
            min-height: calc(100vh - 200rpx);
        }

        .signature-actions {
            gap: 16rpx; // 横屏时按钮间距更紧凑
            min-width: 100rpx;
        }

        .custom-btn {
            width: 90rpx; // 横屏时缩小10% (100rpx * 0.9)
            height: 70rpx;
            font-size: 24rpx;
        }

        .clear-btn {
            height: 140rpx; // 横屏时清除按钮高度一倍 (70rpx * 2)
        }

        .confirm-btn {
            height: 210rpx; // 横屏时确认签名按钮高度三倍 (70rpx * 3)
        }
    }

    // 竖屏模式样式（提示横屏）
    &.portrait {
        .signature-canvas-container {
            border-color: #ffcc02;
            background-color: #fffbe6;
        }

        .orientation-tip {
            color: #fa8c16;
            font-size: 24rpx;
            margin-top: 8rpx;
            animation: blink 2s infinite;
        }
    }
}

.signature-board-container {
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    padding: 40rpx;
}

.signature-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin-bottom: 40rpx;
    padding: 20rpx 0;
}

.signature-title-section {
    display: flex;
    flex-direction: column;
}

.signature-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
}

.orientation-tip {
    color: #fa8c16;
    font-size: 24rpx;
    margin-top: 8rpx;
}

.close-btn {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    background-color: #f5f5f5;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40rpx;
    color: #666;
    line-height: 1;
    padding: 0;

    &:active {
        background-color: #e5e5e5;
    }
}

.signature-canvas-container {
    position: relative;
    width: 100%;
    flex: 1;
    border: 4rpx dashed #d9d9d9;
    border-radius: 16rpx;
    background-color: #fafafa;
    min-height: 600rpx;
    overflow: hidden; // 防止Canvas溢出
}

.signature-canvas {
    width: 100%;
    height: 100%;
    border-radius: 12rpx;
    touch-action: none; // 禁用默认触摸行为
}

.signature-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    text-align: center;
}

.placeholder-text {
    color: #999;
    font-size: 28rpx;
    text-align: center;
}

.signature-content-area {
    display: flex;
    flex: 1;
    gap: 32rpx;
    align-items: stretch;
}

.signature-actions {
    display: flex;
    flex-direction: column; // 改为竖向排列
    gap: 24rpx;
    justify-content: center;
    align-items: center;
    padding: 0;
    min-width: 120rpx; // 设置固定宽度
}

// 自定义按钮样式
.custom-btn {
    width: 108rpx; // 缩小10% (120rpx * 0.9)
    height: 88rpx;
    border-radius: 16rpx;
    font-size: 28rpx;
    font-weight: 500;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
    }

    &:active:not(.disabled) {
        transform: scale(0.95);
    }
}

.clear-btn {
    background-color: #f5f5f5;
    color: #666;
    height: 176rpx; // 增加一倍高度 (88rpx * 2)

    &:active:not(.disabled) {
        background-color: #e5e5e5;
    }
}

.confirm-btn {
    background-color: #1890ff;
    color: #fff;
    height: 264rpx; // 增加三倍高度 (88rpx * 3)

    &:active:not(.disabled) {
        background-color: #096dd9;
    }
}

// 竖排文字样式 - 顺时针旋转90度
.vertical-text {
    transform: rotate(90deg);
    white-space: nowrap;
    display: inline-block;
}

// 闪烁动画
@keyframes blink {
    0%,
    50% {
        opacity: 1;
    }
    51%,
    100% {
        opacity: 0.5;
    }
}

// 横屏媒体查询
@media screen and (orientation: landscape) {
    .signature-board-overlay {
        .signature-board-container {
            padding: 20rpx 40rpx;
        }

        // .signature-header {
        //     margin-bottom: 20rpx;
        // }

        .signature-content-area {
            gap: 20rpx;
        }

        .signature-canvas-container {
            min-height: calc(100vh - 180rpx);
        }

        .signature-actions {
            gap: 16rpx;
            min-width: 100rpx;
        }

        .custom-btn {
            width: 90rpx; // 媒体查询中也缩小10% (100rpx * 0.9)
            height: 70rpx;
            font-size: 24rpx;
        }
    }
}

// 竖屏媒体查询
@media screen and (orientation: portrait) {
    .signature-board-overlay {
        .orientation-tip {
            animation: blink 2s infinite;
        }
    }
}
