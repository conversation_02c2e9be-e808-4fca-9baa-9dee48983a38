import React, { useRef, useState, forwardRef, useImperativeHandle, useEffect } from "react";
import { View, Text, Button, Canvas } from "@tarojs/components";
import Taro from "@tarojs/taro";
import "./index.scss";

interface SignatureBoardProps {
    name?: string;
    onSignatureComplete?: (signatureData: string) => void;
    onCancel?: () => void;
}

export interface SignatureBoardRef {
    open: () => void;
    close: () => void;
    clearSignature: () => void;
}

const SignatureBoard = forwardRef<SignatureBoardRef, SignatureBoardProps>(({ name, onSignatureComplete, onCancel }, ref) => {
    const [visible, setVisible] = useState(false);
    const [isDrawing, setIsDrawing] = useState(false);
    const [hasSignature, setHasSignature] = useState(false);
    const [isLandscape, setIsLandscape] = useState(false);
    const [isCustomNav, setIsCustomNav] = useState(false);
    const [statusBarHeight, setStatusBarHeight] = useState(0);
    const [showPlaceholder, setShowPlaceholder] = useState(true);
    const canvasRef = useRef<any>(null);
    const contextRef = useRef<any>(null);
    const lastPointRef = useRef({ x: 0, y: 0 });
    const canvasRectRef = useRef<{ left: number; top: number }>({ left: 0, top: 0 });

    useImperativeHandle(ref, () => ({
        open: () => {
            setVisible(true);
            setShowPlaceholder(true); // 重置提示文本显示状态
            // 检查导航样式
            checkNavigationStyle();
            // 强制横屏
            forceScreenOrientation();
        },
        close: () => {
            setVisible(false);
            setHasSignature(false);
            // 恢复原始屏幕方向
            restoreScreenOrientation();
        },
        clearSignature: () => {
            clearCanvas();
        },
    }));

    // 检查导航样式
    const checkNavigationStyle = () => {
        try {
            // 获取系统信息
            const systemInfo = Taro.getSystemInfoSync();
            const statusBarHeightPx = systemInfo.statusBarHeight || 0;

            // 获取当前页面的路由信息
            const pages = Taro.getCurrentPages();
            const currentPage = pages[pages.length - 1];

            // 检查页面配置中的navigationStyle
            if (currentPage?.config?.navigationStyle === "custom") {
                setIsCustomNav(true);
                setStatusBarHeight(statusBarHeightPx);
            } else {
                setIsCustomNav(false);
                setStatusBarHeight(0);
            }
        } catch (error) {
            console.log("检查导航样式失败:", error);
            setIsCustomNav(false);
            setStatusBarHeight(0);
        }
    };

    // 强制横屏
    const forceScreenOrientation = async () => {
        try {
            // 设置页面为横屏
            await Taro.setScreenBrightness({ value: 1 });

            // 检查系统信息
            const systemInfo = Taro.getSystemInfoSync();
            const isCurrentlyLandscape = systemInfo.screenWidth > systemInfo.screenHeight;
            setIsLandscape(isCurrentlyLandscape);

            // 延迟初始化Canvas，确保横屏布局完成
            setTimeout(() => {
                initCanvas();
            }, 300);
        } catch (error) {
            console.warn("无法强制横屏:", error);
            setTimeout(() => {
                initCanvas();
            }, 100);
        }
    };

    // 恢复屏幕方向
    const restoreScreenOrientation = () => {
        try {
            // 这里可以添加恢复竖屏的逻辑，但小程序通常会自动恢复
            setIsLandscape(false);
        } catch (error) {
            console.warn("恢复屏幕方向失败:", error);
        }
    };

    // 监听屏幕方向变化
    useEffect(() => {
        if (visible) {
            const handleOrientationChange = () => {
                const systemInfo = Taro.getSystemInfoSync();
                const isCurrentlyLandscape = systemInfo.screenWidth > systemInfo.screenHeight;
                setIsLandscape(isCurrentlyLandscape);

                // 重新初始化Canvas
                setTimeout(() => {
                    initCanvas();
                }, 100);
            };

            // 监听屏幕尺寸变化
            Taro.onWindowResize && Taro.onWindowResize(handleOrientationChange);

            return () => {
                Taro.offWindowResize && Taro.offWindowResize(handleOrientationChange);
            };
        }
    }, [visible]);

    // 监听name变化，重新绘制田字格
    useEffect(() => {
        if (contextRef.current && canvasRef.current && name && name.length <= 5) {
            const dpr = Taro.getSystemInfoSync().pixelRatio;

            // 清除画布
            contextRef.current.fillStyle = "#ffffff";
            contextRef.current.fillRect(0, 0, canvasRef.current.width / dpr, canvasRef.current.height / dpr);

            // 重新绘制田字格
            drawTianGrid(contextRef.current, canvasRef.current.width / dpr, canvasRef.current.height / dpr);
        }
    }, [name]);

    // 3秒后隐藏提示文本
    useEffect(() => {
        if (visible && showPlaceholder) {
            const timer = setTimeout(() => {
                setShowPlaceholder(false);
            }, 3000);

            return () => clearTimeout(timer);
        }
    }, [visible, showPlaceholder]);

    // 绘制田字格
    const drawTianGrid = (ctx, width, height) => {
        console.log("drawTianGrid called:", { name, width, height });
        if (!name || name.length > 5) {
            console.log("田字格绘制条件不满足:", { name, nameLength: name?.length });
            return;
        }

        const padding = 10; // 上下边距
        const availableHeight = height - padding * 2; // 可用高度
        const gridSize = availableHeight / name.length; // 每个格子平分可用高度
        const totalHeight = gridSize * name.length;
        const startY = padding; // 从顶部边距开始
        const startX = (width - gridSize) / 2; // 保证田字格居中（横向只占用一列）

        console.log("田字格参数:", { gridSize, startX, startY, totalHeight, padding, availableHeight });

        ctx.save();
        ctx.strokeStyle = "#e0e0e0"; // 浅灰色网格线
        ctx.lineWidth = 2; // 增加线宽让田字格更明显
        ctx.globalAlpha = 0.4; // 降低60%透明度

        // 绘制每个字的田字格（竖向排列）
        for (let i = 0; i < name.length; i++) {
            const y = startY + i * gridSize;
            console.log(`绘制第${i + 1}个格子:`, { x: startX, y, gridSize });

            // 外框
            ctx.strokeRect(startX, y, gridSize, gridSize);

            // 田字线（横线）
            ctx.beginPath();
            ctx.moveTo(startX, y + gridSize / 2);
            ctx.lineTo(startX + gridSize, y + gridSize / 2);
            ctx.stroke();

            // 田字线（竖线）
            ctx.beginPath();
            ctx.moveTo(startX + gridSize / 2, y);
            ctx.lineTo(startX + gridSize / 2, y + gridSize);
            ctx.stroke();

            // 对角虚线
            ctx.save();
            ctx.setLineDash([5, 5]); // 设置虚线样式，5px实线，5px空白

            // 左上到右下的对角线
            ctx.beginPath();
            ctx.moveTo(startX, y);
            ctx.lineTo(startX + gridSize, y + gridSize);
            ctx.stroke();

            // 右上到左下的对角线
            ctx.beginPath();
            ctx.moveTo(startX + gridSize, y);
            ctx.lineTo(startX, y + gridSize);
            ctx.stroke();

            ctx.restore(); // 恢复线条样式

            // 绘制竖排文字（旋转90度）
            ctx.save();
            ctx.fillStyle = "rgba(102, 102, 102, 0.4)"; // 字的颜色改深一点，降低60%透明度
            ctx.translate(startX + gridSize / 2, y + gridSize / 2);
            ctx.rotate(Math.PI / 2); // 顺时针旋转90度

            // 使用仿宋字体
            ctx.font = `${gridSize * 0.6}px "SimSun", "仿宋", "STFangsong", serif`;
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";
            ctx.fillText(name[i], 0, 0);
            ctx.restore();
        }

        ctx.restore();
        console.log("田字格绘制完成");
    };

    const initCanvas = () => {
        const query = Taro.createSelectorQuery();
        query
            .select("#signatureCanvas")
            .fields({ node: true, size: true, rect: true })
            .exec((res) => {
                if (res[0]) {
                    const canvas = res[0].node;
                    const ctx = canvas.getContext("2d");

                    // 缓存 canvas 位置信息，避免后续频繁查询
                    if (res[0].left !== undefined && res[0].top !== undefined) {
                        canvasRectRef.current = { left: res[0].left, top: res[0].top };
                    }

                    const dpr = Taro.getSystemInfoSync().pixelRatio;
                    canvas.width = res[0].width * dpr;
                    canvas.height = res[0].height * dpr;
                    ctx.scale(dpr, dpr);

                    // 设置画笔样式
                    ctx.lineCap = "round";
                    ctx.lineJoin = "round";
                    ctx.lineWidth = 4; // 增加线条粗细，横屏时更容易看清
                    ctx.strokeStyle = "#000000";
                    ctx.fillStyle = "#ffffff";
                    ctx.fillRect(0, 0, canvas.width, canvas.height);

                    console.log("name", name);
                    // 绘制田字格
                    if (name && name.length <= 5) {
                        drawTianGrid(ctx, res[0].width, res[0].height);
                    }

                    canvasRef.current = canvas;
                    contextRef.current = ctx;
                }
            });
    };

    // 优化：使用缓存的位置信息，避免频繁异步查询
    const getTouchPosition = (e): { x: number; y: number } => {
        const touch = e.touches[0];
        const rect = canvasRectRef.current;
        return {
            x: touch.clientX - rect.left,
            y: touch.clientY - rect.top,
        };
    };

    const handleTouchStart = (e) => {
        if (!contextRef.current) return;

        e.preventDefault();
        e.stopPropagation();

        const pos = getTouchPosition(e);
        lastPointRef.current = pos;
        setIsDrawing(true);
        contextRef.current.beginPath();
        contextRef.current.moveTo(pos.x, pos.y);
    };

    const handleTouchMove = (e) => {
        if (!isDrawing || !contextRef.current) return;

        e.preventDefault();
        e.stopPropagation();

        const pos = getTouchPosition(e);

        // 使用贝塞尔曲线平滑线条
        const midPoint = {
            x: (lastPointRef.current.x + pos.x) / 2,
            y: (lastPointRef.current.y + pos.y) / 2,
        };

        contextRef.current.quadraticCurveTo(lastPointRef.current.x, lastPointRef.current.y, midPoint.x, midPoint.y);
        contextRef.current.stroke();

        lastPointRef.current = pos;

        // 优化：只在第一次绘制时设置 hasSignature，避免频繁 setState
        if (!hasSignature) {
            setHasSignature(true);
        }
    };

    const handleTouchEnd = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDrawing(false);
    };

    const clearCanvas = () => {
        if (!contextRef.current || !canvasRef.current) return;

        const dpr = Taro.getSystemInfoSync().pixelRatio;
        const canvasWidth = canvasRef.current.width / dpr;
        const canvasHeight = canvasRef.current.height / dpr;

        // 清除画布
        contextRef.current.fillStyle = "#ffffff";
        contextRef.current.fillRect(0, 0, canvasWidth, canvasHeight);

        // 重新绘制田字格
        if (name && name.length <= 5) {
            drawTianGrid(contextRef.current, canvasWidth, canvasHeight);
        }

        setHasSignature(false);
    };

    const saveSignature = () => {
        if (!hasSignature) {
            Taro.showToast({
                title: "请先签名",
                icon: "none",
            });
            return;
        }

        if (!canvasRef.current) return;

        Taro.canvasToTempFilePath({
            canvasId: "signatureCanvas",
            canvas: canvasRef.current,
            success: (res) => {
                onSignatureComplete?.(res.tempFilePath);
                setVisible(false);
                restoreScreenOrientation();
            },
            fail: (error) => {
                Taro.showToast({
                    title: "保存签名失败",
                    icon: "none",
                });
                console.error("保存签名失败:", error);
            },
        });
    };

    if (!visible) {
        return null;
    }

    return (
        <View
            className={`signature-board-overlay ${isLandscape ? "landscape" : "portrait"}`}
            style={{
                paddingTop: `${statusBarHeight + 10}px`,
            }}
        >
            <View className="signature-board-container">
                <View className="signature-header">
                    <View className="signature-title-section">
                        <Text className="signature-title">请横屏签名</Text>
                        {!isLandscape && <Text className="orientation-tip">请将设备旋转至横屏模式</Text>}
                    </View>
                </View>

                <View className="signature-content-area">
                    <View className="signature-actions">
                        <View className={`custom-btn clear-btn ${!hasSignature ? "disabled" : ""}`} onClick={!hasSignature ? undefined : clearCanvas}>
                            <View className="vertical-text">清除</View>
                        </View>
                        <View className={`custom-btn confirm-btn ${!hasSignature ? "disabled" : ""}`} onClick={!hasSignature ? undefined : saveSignature}>
                            <View className="vertical-text">确认签名</View>
                        </View>
                    </View>

                    <View className="signature-canvas-container">
                        <Canvas
                            id="signatureCanvas"
                            canvasId="signatureCanvas"
                            className="signature-canvas"
                            type="2d"
                            disableScroll={true}
                            onTouchStart={handleTouchStart}
                            onTouchMove={handleTouchMove}
                            onTouchEnd={handleTouchEnd}
                        />
                        <View className="signature-placeholder">
                            {!hasSignature && showPlaceholder && <Text className="placeholder-text">{isLandscape ? "请在此区域签名" : "请横屏后在此区域签名"}</Text>}
                        </View>
                    </View>
                </View>
            </View>
        </View>
    );
});

export default SignatureBoard;
