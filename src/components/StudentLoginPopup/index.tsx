import { Input, Button } from "@tarojs/components";
import { Close } from "@nutui/icons-react-taro";
import Taro from "@tarojs/taro";
import { message } from "@/components/MessageApi/MessageApiSingleton";
import request from "@/service/request";
import { Component } from "react";
import { Popup } from "@nutui/nutui-react-taro";
import "./index.scss";

interface Account {
    Id: string;
    xm: string;
    sfzmhm: string;
    TenantName: string;
    TenantId: string;
    CarType: string;
    RegistrationDate: string;
}

interface Props {
    onLoginSuccess: () => void;
}

interface State {
    visible: boolean;
    loading: boolean;
    loginForm: {
        sfzmmc: string;
        sfzmhm: string;
        xm: string;
    };
    multipleAccounts: Account[];
    showAccountSelection: boolean;
    showIdTypePopup: boolean;
    selectedIdType: { label: string; value: string } | null;
    idTypeList: Array<{ label: string; value: string }>;
}

class StudentLoginPopup extends Component<Props, State> {
    constructor(props: Props) {
        super(props);
        this.state = {
            visible: false,
            loading: false,
            loginForm: {
                sfzmmc: "",
                sfzmhm: "",
                xm: "",
            },
            multipleAccounts: [],
            showAccountSelection: false,
            showIdTypePopup: false,
            selectedIdType: null,
            idTypeList: [],
        };
    }

    open = async () => {
        console.log("open");
        this.setState({
            visible: true,
            loginForm: {
                sfzmmc: "",
                sfzmhm: "",
                xm: "",
            },
            multipleAccounts: [],
            showAccountSelection: false,
            loading: false,
        });
        await this.fetchIdTypes();
    };

    close = () => {
        this.setState({ visible: false });
    };

    handleClose = () => {
        this.setState({ visible: false });
    };

    handleInputChange = (field: string, value: string) => {
        this.setState((prevState) => ({
            loginForm: {
                ...prevState.loginForm,
                [field]: value,
            },
        }));
    };

    fetchIdTypes = async () => {
        try {
            this.setState({ loading: true });
            const response = await request.post<any>("/Base/Select/getSfzmmcSelectList", {});
            if (response && response.success) {
                const formattedList = response.data.map((item) => ({
                    label: item.text || item.label,
                    value: item.value,
                }));
                this.setState({
                    idTypeList: formattedList,
                    selectedIdType: formattedList.length > 0 ? formattedList[0] : null,
                });
            } else {
                message.error("获取证件类型列表失败");
            }
        } catch (error) {
            console.error("获取证件类型失败:", error);
            message.error("获取证件类型列表失败");
        } finally {
            this.setState({ loading: false });
        }
    };

    handleStudentSelect = async (student: Account) => {
        try {
            this.setState({ loading: true });
            const loginResult = await Taro.login();
            const response = await request.post<any>(`/Jx/Student/WxLogin/login/${loginResult.code}`, {
                Id: student.Id,
                TenantId: student.TenantId,
            });
            if (response && response.success) {
                this.handleClose();
                this.props.onLoginSuccess();
            } else {
                message.error(response?.message || "登录失败，请检查登录信息");
            }
        } catch (error) {
            console.error("学员登录失败:", error);
            message.error("登录失败，请稍后重试");
        } finally {
            this.setState({ loading: false });
        }
    };

    handleSubmitLogin = async () => {
        const { selectedIdType } = this.state;
        const { loginForm } = this.state;

        if (!selectedIdType?.value) {
            message.error("请选择证件类型");
            return;
        }
        if (!loginForm.sfzmhm) {
            message.error("请输入证件号码");
            return;
        }
        if (!loginForm.xm) {
            message.error("请输入学员姓名");
            return;
        }

        try {
            this.setState({ loading: true });
            const loginResult = await Taro.login();
            const response = await request.post<any>(`/Jx/Student/WxLogin/login/${loginResult.code}`, {
                sfzmhm: loginForm.sfzmhm,
                xm: loginForm.xm,
            });
            if (response && response.success) {
                if (Array.isArray(response.data) && response.data.length > 1) {
                    this.setState({
                        multipleAccounts: response.data,
                        showAccountSelection: true,
                    });
                } else if (Array.isArray(response.data) && response.data.length === 1) {
                    this.handleStudentSelect(response.data[0]);
                } else {
                    this.handleClose();
                    this.props.onLoginSuccess();
                }
            } else {
                message.error(response?.message || "登录失败，请检查登录信息");
            }
        } catch (error) {
            console.error("学员登录失败:", error);
            message.error("登录失败，请稍后重试");
        } finally {
            this.setState({ loading: false });
        }
    };

    render() {
        const { visible, loading, loginForm, multipleAccounts, showAccountSelection, showIdTypePopup, selectedIdType, idTypeList } = this.state;

        if (!visible) return null;

        if (showAccountSelection) {
            return (
                <view className="student-login-overlay" onClick={this.handleClose}>
                    <view className="student-login-container" onClick={(e) => e.stopPropagation()}>
                        {loading && (
                            <view className="loading-overlay">
                                <view className="loading-spinner"></view>
                            </view>
                        )}
                        <view className="student-login-header">
                            <text className="student-login-title">选择账号</text>
                            <view className="student-login-close" onClick={this.handleClose}>
                                <Close />
                            </view>
                        </view>

                        <view className="account-list">
                            {multipleAccounts.map((student) => (
                                <view key={student.Id} className="account-item" onClick={() => this.handleStudentSelect(student)}>
                                    <view className="account-info">
                                        <text className="school-name">{student.TenantName}</text>
                                        <text className="car-type">车型：{student.CarType}</text>
                                        <text className="registration-date">报名时间：{student.RegistrationDate.split(" ")[0]}</text>
                                    </view>
                                </view>
                            ))}
                        </view>
                    </view>
                </view>
            );
        }

        return (
            <view className="student-login-overlay" onClick={this.handleClose}>
                <view className="student-login-container" onClick={(e) => e.stopPropagation()}>
                    {loading && (
                        <view className="loading-overlay">
                            <view className="loading-spinner"></view>
                        </view>
                    )}
                    <view className="student-login-header">
                        <text className="student-login-title">学员登录</text>
                        <view className="student-login-close" onClick={this.handleClose}>
                            <Close />
                        </view>
                    </view>

                    <view className="student-login-form">
                        {/* 证件类型 */}
                        <view className="form-item">
                            <view className="form-select" onClick={() => this.setState({ showIdTypePopup: true })}>
                                <view className="select-value">{selectedIdType ? selectedIdType.label : "请选择证件类型"}</view>
                                <view className="select-arrow"></view>
                            </view>
                        </view>

                        {/* 证件号码 */}
                        <view className="form-item">
                            <Input
                                className="form-input"
                                type="text"
                                value={loginForm.sfzmhm}
                                onInput={(e) => this.handleInputChange("sfzmhm", e.detail.value)}
                                placeholder="请输入证件号码"
                            />
                        </view>

                        {/* 学员姓名 */}
                        <view className="form-item">
                            <Input
                                className="form-input"
                                type="text"
                                value={loginForm.xm}
                                onInput={(e) => this.handleInputChange("xm", e.detail.value)}
                                placeholder="请输入学员姓名"
                            />
                        </view>
                    </view>

                    <view className="student-login-buttons">
                        <Button className={`login-submit-button ${loading ? "disabled" : ""}`} onClick={this.handleSubmitLogin} disabled={loading}>
                            {loading ? "登录中..." : "登录"}
                        </Button>
                    </view>
                </view>

                {/* IdTypePopup */}
                <Popup visible={showIdTypePopup} position="bottom" onClose={() => this.setState({ showIdTypePopup: false })}>
                    <view className="id-type-popup-container">
                        <view className="id-type-popup-header">
                            <text className="id-type-popup-title">选择证件类型</text>
                            <view className="id-type-popup-close" onClick={() => this.setState({ showIdTypePopup: false })}>
                                <Close size={24} />
                            </view>
                        </view>
                        <view className="id-type-list">
                            {idTypeList.map((type) => (
                                <view
                                    key={type.value}
                                    className={`id-type-item ${selectedIdType?.value === type.value ? "selected" : ""}`}
                                    onClick={() => {
                                        this.setState({ selectedIdType: type, showIdTypePopup: false });
                                    }}
                                >
                                    {type.label}
                                </view>
                            ))}
                        </view>
                        {/* <view className="safe-area" /> */}
                    </view>
                </Popup>
            </view>
        );
    }
}

export default StudentLoginPopup;
