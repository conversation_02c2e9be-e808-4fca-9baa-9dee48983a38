.student-login-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.student-login-container {
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(245, 247, 250, 0.9));
    border-radius: 0 0 32rpx 32rpx;
    width: 85%;
    max-width: 650rpx;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    overflow: visible;
    box-shadow: 0 30rpx 70rpx rgba(50, 50, 93, 0.2), 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
    position: relative;
    animation: slideUp 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    transform: translateY(0);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(60rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 装饰元素 */
.student-login-container::before {
    content: "";
    position: absolute;
    top: -80rpx;
    right: -80rpx;
    width: 200rpx;
    height: 200rpx;
    background: rgba(99, 69, 237, 0.1);
    border-radius: 50%;
    filter: blur(30px);
    z-index: -1;
}

.student-login-container::after {
    content: "";
    position: absolute;
    bottom: -60rpx;
    left: -60rpx;
    width: 180rpx;
    height: 180rpx;
    background: rgba(86, 119, 252, 0.1);
    border-radius: 40rpx;
    filter: blur(25px);
    z-index: -1;
}

/* 装饰图案 */
.student-login-form::before {
    content: "";
    position: absolute;
    top: 30rpx;
    right: 30rpx;
    width: 180rpx;
    height: 180rpx;
    background-image: radial-gradient(circle, rgba(99, 69, 237, 0.03) 1px, transparent 1px);
    background-size: 16rpx 16rpx;
    border-radius: 20rpx;
    opacity: 0.6;
    z-index: -1;
}

.form-item {
    margin-bottom: 36rpx;
    position: relative;

    &:last-child {
        margin-bottom: 0;
    }
}

.form-label {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 16rpx;
    display: block;
    font-weight: 500;
}

.form-input {
    width: 100%;
    height: 88rpx;
    border: 2rpx solid rgba(220, 225, 235, 0.9);
    border-radius: 16rpx;
    padding: 0 32rpx;
    font-size: 28rpx;
    box-sizing: border-box;
    background-color: rgba(250, 251, 253, 0.6);
    transition: all 0.2s ease;

    &:focus {
        border-color: rgba(99, 69, 237, 0.4);
        background-color: #fff;
        box-shadow: 0 0 0 6rpx rgba(99, 69, 237, 0.1);
    }
}

.form-select {
    width: 100%;
    height: 88rpx;
    border: 2rpx solid rgba(220, 225, 235, 0.9);
    border-radius: 16rpx;
    padding: 0 32rpx;
    font-size: 28rpx;
    background-color: rgba(250, 251, 253, 0.6);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.2s ease;

    &:active {
        border-color: rgba(99, 69, 237, 0.4);
        background-color: #fff;
    }
}

.select-value {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
}

.select-arrow {
    width: 0;
    height: 0;
    border-left: 12rpx solid transparent;
    border-right: 12rpx solid transparent;
    border-top: 12rpx solid #999;
    margin-left: 16rpx;
    transition: transform 0.3s ease;
}

.student-login-buttons {
    display: flex;
    flex-direction: column;
    padding: 24rpx;
    border-top: 2rpx solid rgba(230, 235, 245, 0.8);
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.95), rgba(245, 247, 250, 0.9));
    border-radius: 0 0 32rpx 32rpx;
    gap: 16rpx;
}

.login-submit-button {
    width: 100% !important;
    height: 88rpx !important;
    line-height: 88rpx !important;
    background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
    color: #fff !important;
    font-size: 32rpx !important;
    font-weight: 500 !important;
    border: none !important;
    border-radius: 44rpx !important;
    box-shadow: 0 4rpx 12rpx rgba(37, 99, 235, 0.3) !important;
    transition: all 0.2s ease !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    &:active {
        opacity: 0.8;
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 6rpx rgba(37, 99, 235, 0.2) !important;
    }

    &.disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
}

.student-login-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 28rpx 34rpx;
    border-bottom: 1px solid rgba(230, 235, 245, 0.8);
    background: linear-gradient(to right, rgba(245, 247, 250, 0.8), rgba(255, 255, 255, 0.95));
    position: relative;
}

.student-login-title {
    font-size: 34rpx;
    font-weight: 600;
    color: #333;
    position: relative;
    padding-left: 16rpx;

    &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        height: 28rpx;
        width: 6rpx;
        background: linear-gradient(to bottom, #6345ed, #4568dc);
        border-radius: 3rpx;
    }
}

.student-login-close {
    width: 38rpx;
    height: 38rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 38rpx;
    color: #999;
    cursor: pointer;
    border-radius: 50%;
    background-color: rgba(245, 247, 250, 0.8);
    transition: all 0.2s ease;

    &:active {
        background-color: rgba(230, 235, 245, 0.9);
        transform: scale(0.95);
    }
}

.student-login-form {
    padding: 40rpx 34rpx 30rpx;
    position: relative;
    overflow: hidden;
}

/* Subtle decorative pattern */
.student-login-form::before {
    content: "";
    position: absolute;
    top: 30rpx;
    right: 30rpx;
    width: 180rpx;
    height: 180rpx;
    background-image: radial-gradient(circle, rgba(99, 69, 237, 0.03) 1px, transparent 1px);
    background-size: 16rpx 16rpx;
    border-radius: 20rpx;
    opacity: 0.6;
    z-index: -1;
}

.form-item {
    margin-bottom: 30rpx;
    position: relative;
}

.form-label {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 14rpx;
    display: block;
    font-weight: 500;
}

.form-input {
    width: 100%;
    height: 84rpx;
    border: 1px solid rgba(220, 225, 235, 0.9);
    border-radius: 10rpx;
    padding: 0 24rpx;
    font-size: 28rpx;
    box-sizing: border-box;
    background-color: rgba(250, 251, 253, 0.6);
    transition: all 0.2s ease;

    &:focus {
        border-color: rgba(99, 69, 237, 0.4);
        background-color: #fff;
        box-shadow: 0 0 0 3px rgba(99, 69, 237, 0.1);
    }
}

.form-select {
    width: 100%;
    height: 84rpx;
    border: 1px solid rgba(220, 225, 235, 0.9);
    border-radius: 10rpx;
    padding: 0 24rpx;
    font-size: 28rpx;
    background-color: rgba(250, 251, 253, 0.6);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.2s ease;

    &:active {
        border-color: rgba(99, 69, 237, 0.4);
        background-color: #fff;
    }
}

.select-value {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
}

.select-arrow {
    width: 0;
    height: 0;
    border-left: 12rpx solid transparent;
    border-right: 12rpx solid transparent;
    border-top: 12rpx solid #999;
    margin-left: 10rpx;
    transition: transform 0.3s ease;
}

.select-options {
    position: fixed;
    background-color: #fff;
    border: 1px solid rgba(220, 225, 235, 0.9);
    border-radius: 10rpx;
    width: 80%;
    max-width: 500rpx;
    max-height: 600rpx;
    overflow-y: auto;
    z-index: 99999;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    animation: fadeInDown 0.2s ease;
}

.account-list {
    padding: 40rpx;
    max-height: 60vh;
    overflow-y: auto;
}

.account-item {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.98);
        background: #f5f5f5;
    }
}

.account-info {
    display: flex;
    flex-direction: column;
    gap: 16rpx;

    .school-name {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
    }

    .car-type {
        font-size: 28rpx;
        color: #666;
    }

    .registration-date {
        font-size: 28rpx;
        color: #666;
    }
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    border-radius: 32rpx;
}

.loading-spinner {
    width: 80rpx;
    height: 80rpx;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.id-type-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.id-type-popup-container {
    width: 100%;
    background-color: #fff;
    border-radius: 24rpx;
    max-width: 1200rpx;
    max-height: 80vh;
    overflow-y: auto;
    .safe-area {
        height: constant(safe-area-inset-bottom); /* iOS 11.0 */
        height: env(safe-area-inset-bottom); /* iOS 11.2+ */
    }
}

.id-type-popup-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    border-bottom: 2rpx solid #eee;
}

.id-type-popup-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
}

.id-type-popup-close {
    padding: 8rpx;
    cursor: pointer;
}

.id-type-list {
    padding: 16rpx 0;
}

.id-type-item {
    padding: 24rpx 32rpx;
    font-size: 28rpx;
    color: #333;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
        background-color: #f5f5f5;
    }

    &.selected {
        color: #1677ff;
        background-color: #e6f4ff;
    }
}
