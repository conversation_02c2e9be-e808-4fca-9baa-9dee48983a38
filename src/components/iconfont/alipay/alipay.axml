<!--arrow-right-circle-line-->
<view a:if="{{name === 'arrow-right-circle-line'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 469.333333V341.333333l170.666667 170.666667-170.666667 170.666667v-128H341.333333v-85.333334h170.666667z m0-384c235.52 0 426.666667 191.146667 426.666667 426.666667s-191.146667 426.666667-426.666667 426.666667S85.333333 747.52 85.333333 512 276.48 85.333333 512 85.333333z m0 768c188.586667 0 341.333333-152.746667 341.333333-341.333333s-152.746667-341.333333-341.333333-341.333333-341.333333 152.746667-341.333333 341.333333 152.746667 341.333333 341.333333 341.333333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(0,0,0)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--arrow-right-circle-fill-->
<view a:if="{{name === 'arrow-right-circle-fill'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 85.333333c235.52 0 426.666667 191.146667 426.666667 426.666667s-191.146667 426.666667-426.666667 426.666667S85.333333 747.52 85.333333 512 276.48 85.333333 512 85.333333z m0 384H341.333333v85.333334h170.666667v128l170.666667-170.666667-170.666667-170.666667v128z' fill='{{(isStr ? colors : colors[0]) || 'rgb(0,0,0)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--image-->
<view a:if="{{name === 'image'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M145.6 0c-44.8 0-80 36.8-80 81.6V944c0 44.8 35.2 80 80 80h732.8c44.8 0 81.6-35.2 81.6-80V326.4L657.6 0h-512z' fill='{{(isStr ? colors : colors[0]) || 'rgb(73,201,167)'}}' /%3E%3Cpath d='M960 326.4v16H755.2s-100.8-20.8-99.2-108.8c0 0 4.8 92.8 97.6 92.8H960z' fill='{{(isStr ? colors : colors[1]) || 'rgb(55,187,145)'}}' /%3E%3Cpath d='M657.6 0v233.6c0 25.6 17.6 92.8 97.6 92.8H960L657.6 0z' fill='{{(isStr ? colors : colors[2]) || 'rgb(255,255,255)'}}' opacity='.5' /%3E%3Cpath d='M225.6 859.2V524.8H560v334.4H225.6z m300.8-300.8H259.2v201.6h267.2V558.4z m-153.6 134.4l62.4-84.8 20.8 33.6 20.8-6.4 16 89.6H283.2l56-52.8 33.6 20.8z m-60.8-59.2c-14.4 0-27.2-9.6-27.2-24 0-12.8 12.8-24 27.2-24 14.4 0 25.6 11.2 25.6 24 0 14.4-11.2 24-25.6 24z' fill='{{(isStr ? colors : colors[3]) || 'rgb(255,255,255)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--pdf-->
<view a:if="{{name === 'pdf'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M145.6 0C100.8 0 64 36.8 64 81.6v860.8C64 987.2 100.8 1024 145.6 1024h732.8c44.8 0 81.6-36.8 81.6-81.6V324.8L657.6 0h-512z' fill='{{(isStr ? colors : colors[0]) || 'rgb(140,24,26)'}}' /%3E%3Cpath d='M960 326.4v16H755.2s-100.8-20.8-97.6-107.2c0 0 3.2 91.2 96 91.2H960z' fill='{{(isStr ? colors : colors[1]) || 'rgb(107,13,18)'}}' /%3E%3Cpath d='M657.6 0v233.6c0 27.2 17.6 92.8 97.6 92.8H960L657.6 0z' fill='{{(isStr ? colors : colors[2]) || 'rgb(255,255,255)'}}' opacity='.5' /%3E%3Cpath d='M302.4 784h-52.8v65.6c0 6.4-4.8 11.2-12.8 11.2-6.4 0-11.2-4.8-11.2-11.2V686.4c0-9.6 8-17.6 17.6-17.6h59.2c38.4 0 60.8 27.2 60.8 57.6 0 32-22.4 57.6-60.8 57.6z m-1.6-94.4h-51.2v73.6h51.2c22.4 0 38.4-14.4 38.4-36.8s-16-36.8-38.4-36.8z m166.4 171.2h-48c-9.6 0-17.6-8-17.6-17.6v-156.8c0-9.6 8-17.6 17.6-17.6h48c59.2 0 99.2 41.6 99.2 96s-38.4 96-99.2 96z m0-171.2h-41.6v148.8h41.6c46.4 0 73.6-33.6 73.6-75.2 1.6-40-25.6-73.6-73.6-73.6z m260.8 0h-92.8V752h91.2c6.4 0 9.6 4.8 9.6 11.2s-4.8 9.6-9.6 9.6h-91.2v76.8c0 6.4-4.8 11.2-12.8 11.2-6.4 0-11.2-4.8-11.2-11.2V686.4c0-9.6 8-17.6 17.6-17.6h99.2c6.4 0 9.6 4.8 9.6 11.2 1.6 4.8-3.2 9.6-9.6 9.6z' fill='{{(isStr ? colors : colors[3]) || 'rgb(255,255,255)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--zhuceshibai-->
<view a:if="{{name === 'zhuceshibai'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1391 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M1050.964505 234.645126a75.119845 75.119845 0 0 0-53.597204-21.979961H625.936155a75.138485 75.138485 0 0 0-53.558679 21.999845 75.137243 75.137243 0 0 0-21.999845 53.557437V873.879612a75.139728 75.139728 0 0 0 21.999845 53.557437A75.138485 75.138485 0 0 0 625.936155 949.436893h371.431146a75.121087 75.121087 0 0 0 53.597204-21.981204 75.113631 75.113631 0 0 0 22.024699-53.576077V288.222447a75.111146 75.111146 0 0 0-22.024699-53.577321z' fill='{{(isStr ? colors : colors[0]) || 'rgb(223,239,247)'}}' /%3E%3Cpath d='M606.299961 873.878369v-585.655922a75.137243 75.137243 0 0 1 21.999845-53.557437 75.140971 75.140971 0 0 1 53.559922-21.999845h-55.92233a75.138485 75.138485 0 0 0-53.55868 21.999845 75.137243 75.137243 0 0 0-21.999844 53.557437V873.879612a75.139728 75.139728 0 0 0 21.999844 53.557437A75.138485 75.138485 0 0 0 625.937398 949.436893h55.92233a75.143456 75.143456 0 0 1-53.559922-21.999844 75.142214 75.142214 0 0 1-21.999845-53.55868z' fill='{{(isStr ? colors : colors[1]) || 'rgb(199,230,249)'}}' /%3E%3Cpath d='M299.99099 384.520699c-0.69965-7.826641-7.61165-13.602796-15.437048-12.903146a14.634252 14.634252 0 0 0-9.565204 5.069049 13.669903 13.669903 0 0 0-3.181359 10.323262 14.610641 14.610641 0 0 0 5.142369 9.563961 13.103223 13.103223 0 0 0 9.009708 3.385165h1.125903c7.827883-0.69965 13.604039-7.61165 12.905631-15.438291z m-8.669204 11.414369a11.539883 11.539883 0 0 1-12.766446-1.399301 10.566835 10.566835 0 0 1-4.053748-7.805515 11.546097 11.546097 0 0 1 2.593554-8.49398 10.587961 10.587961 0 0 1 7.82664-4.056233h1.033942a11.455379 11.455379 0 0 1 11.516272 10.48233 11.534913 11.534913 0 0 1-6.150214 11.272699zM1199.57499 189.799146c-0.970563-10.827806-10.534524-18.822214-21.361087-17.852894a20.235184 20.235184 0 0 0-13.232466 7.012661 18.904233 18.904233 0 0 0-4.404194 14.283805 20.222757 20.222757 0 0 0 7.117048 13.233709 18.125049 18.125049 0 0 0 12.465709 4.682563h1.559612c10.826563-0.965592 18.820971-10.529553 17.855378-21.359844z m-11.995961 15.793708a15.966447 15.966447 0 0 1-17.664-1.936155 14.620583 14.620583 0 0 1-5.610874-10.801709 15.971417 15.971417 0 0 1 3.590214-11.753631 14.64668 14.64668 0 0 1 10.829048-5.612116h1.431612c8.283961-0.074563 15.230757 6.245903 15.932893 14.503767a15.957748 15.957748 0 0 1-8.508893 15.599844zM168.874252 171.866718a3.060816 3.060816 0 0 1-3.057087-3.057087v-19.769165l-13.961942 13.961942a3.134136 3.134136 0 0 1-4.431534-4.430291l13.964428-13.961942h-19.803962a3.069515 3.069515 0 1 1 0-6.142758h19.803962l-13.964428-13.795417a2.914175 2.914175 0 0 1 0-4.430291 2.915417 2.915417 0 0 1 2.21701-1.016544c0.851262 0 1.656544 0.369087 2.214524 1.016544l13.961942 13.827728v-19.771651a3.054602 3.054602 0 1 1 5.97499 0v19.771651l14.097398-13.964427a2.922874 2.922874 0 0 1 4.431534 0 2.920388 2.920388 0 0 1 0 4.431534l-13.964427 13.96567h19.769165a3.069515 3.069515 0 1 1 0 6.140271h-19.769165l13.964427 13.96567c0.791612 0.787883 1.098563 1.942369 0.810253 3.023534a3.14035 3.14035 0 0 1-2.214524 2.21701 3.136621 3.136621 0 0 1-3.027263-0.813981l-14.097398-13.994252v19.769165c0.335534 1.712466-1.373204 3.055845-3.053359 3.055845h0.135456zM120.141049 910.39565h-8.449243v-8.450485c0-1.405515-1.137087-2.542602-2.542602-2.542602s-2.542602 1.137087-2.542602 2.542602v8.450485h-8.449243a2.542602 2.542602 0 1 0 0 5.085204h8.449243v8.450486a2.527689 2.527689 0 0 0 2.530175 2.527689l0.026097-0.109359a2.536388 2.536388 0 0 0 2.530175-2.531418v-8.336155h8.449242a2.543845 2.543845 0 0 0-0.001242-5.086447zM1199.655767 819.01235c0 16.750602-11.342291 29.92466-25.368854 29.92466 14.067573 0 25.368854 13.39899 25.368854 29.92466 0-16.750602 11.343534-29.92466 25.366369-29.92466-14.068816 0-25.366369-13.397748-25.366369-29.92466z m0 0' fill='{{(isStr ? colors : colors[2]) || 'rgb(204,204,204)'}}' /%3E%3Cpath d='M1254.105476 1018.617786H1180.060583a7.044971 7.044971 0 1 1 0-14.089941h74.044893a7.044971 7.044971 0 1 1 0 14.089941zM1126.209864 1018.617786H141.251107a7.044971 7.044971 0 1 1 0-14.089941h984.958757a7.044971 7.044971 0 1 1 0 14.089941zM109.147961 1018.617786H85.328777a7.044971 7.044971 0 1 1 0-14.089941H109.147961a7.044971 7.044971 0 1 1 0 14.089941z' fill='{{(isStr ? colors : colors[3]) || 'rgb(126,202,252)'}}' /%3E%3Cpath d='M997.845748 956.483107l-0.524428-0.002486H625.936155c-21.764971 0.10066-43.114874-8.643107-58.539495-24.061514-15.420893-15.425864-24.190757-36.77701-24.065243-58.581748V288.222447c-0.126757-21.762485 8.64435-43.113631 24.062758-58.538253 15.315262-15.309049 36.473786-24.065243 58.114485-24.065243l0.467262 0.001243h371.390136l0.524427-0.001243c21.597204 0 42.744544 8.74501 58.053593 24.044117 15.434563 15.41965 24.215612 36.779495 24.088854 58.601631v108.475651a7.044971 7.044971 0 1 1-14.089942 0v-108.515418c0.104388-18.12132-7.170485-35.817631-19.959301-48.592777-12.78633-12.777631-30.50501-20.067417-48.57165-19.918291H625.936155c-18.100194-0.135456-35.797748 7.161786-48.577864 19.936932-12.773903 12.778874-20.040078 30.468971-19.935689 48.534369v585.696932c-0.104388 18.105165 7.161786 35.796505 19.936932 48.576621 12.686913 12.681942 30.219184 19.936932 48.150369 19.936932l0.386485-0.001242H997.368544l0.436194 0.001242c17.977165 0 35.498252-7.246291 48.181437-19.919533 12.790058-12.776388 20.064932-30.472699 19.960543-48.551767V561.087379a7.044971 7.044971 0 1 1 14.089942 0v312.79099c0.128 21.779883-8.654291 43.140971-24.090097 58.560621-15.307806 15.296621-36.458874 24.042874-58.100815 24.044117z' fill='{{(isStr ? colors : colors[4]) || 'rgb(126,202,252)'}}' /%3E%3Cpath d='M1072.989204 474.307107a7.046214 7.046214 0 0 1-7.044971-7.044971v-34.485437a7.044971 7.044971 0 1 1 14.089942 0V467.262136a7.044971 7.044971 0 0 1-7.044971 7.044971z' fill='{{(isStr ? colors : colors[5]) || 'rgb(126,202,252)'}}' /%3E%3Cpath d='M914.265476 321.635417a75.119845 75.119845 0 0 0-53.597204-21.979961H489.237126a75.138485 75.138485 0 0 0-53.558679 21.999845 75.137243 75.137243 0 0 0-21.999845 53.557437V936.015534a75.139728 75.139728 0 0 0 21.999845 53.557437A75.138485 75.138485 0 0 0 489.237126 1011.572816h371.431146a75.121087 75.121087 0 0 0 53.597204-21.981204 75.113631 75.113631 0 0 0 22.024699-53.576078V375.212738a75.111146 75.111146 0 0 0-22.024699-53.577321z' fill='{{(isStr ? colors : colors[6]) || 'rgb(223,239,247)'}}' /%3E%3Cpath d='M914.265476 321.635417a75.119845 75.119845 0 0 0-53.597204-21.979961h-62.135922a75.119845 75.119845 0 0 1 53.597203 21.979961 75.116117 75.116117 0 0 1 22.024699 53.577321V936.015534a75.10866 75.10866 0 0 1-22.024699 53.576078A75.121087 75.121087 0 0 1 798.53235 1011.572816h62.135922a75.121087 75.121087 0 0 0 53.597204-21.981204 75.113631 75.113631 0 0 0 22.024699-53.576078V375.212738a75.111146 75.111146 0 0 0-22.024699-53.577321z' fill='{{(isStr ? colors : colors[7]) || 'rgb(199,230,249)'}}' /%3E%3Cpath d='M861.146718 1018.619029l-0.524427-0.002485H489.237126c-21.777398 0.152854-43.114874-8.643107-58.539495-24.061515-15.420893-15.425864-24.190757-36.77701-24.065243-58.581747v-20.503612a7.046214 7.046214 0 0 1 14.089942 0v20.544621c-0.104388 18.105165 7.161786 35.796505 19.936932 48.576622 12.778874 12.773903 30.40932 20.033864 48.535612 19.935689h371.472155l0.436194 0.001243c17.977165 0 35.498252-7.245049 48.181437-19.919534 12.790058-12.776388 20.064932-30.472699 19.960544-48.551767V375.212738c0.104388-18.12132-7.170485-35.817631-19.959301-48.592777-12.785087-12.777631-30.480155-20.004039-48.571651-19.919534H489.237126c-18.133748-0.105631-35.797748 7.160544-48.577864 19.936932-12.773903 12.778874-20.040078 30.468971-19.935689 48.534369v193.16567a7.046214 7.046214 0 0 1-14.089942 0V375.212738c-0.126757-21.762485 8.64435-43.113631 24.062757-58.538253 15.425864-15.420893 36.816777-24.199456 58.582991-24.064H860.669515l0.524427-0.001242c21.597204 0 42.744544 8.74501 58.053592 24.042874 15.434563 15.41965 24.215612 36.779495 24.088854 58.601631V936.015534c0.128 21.779883-8.654291 43.140971-24.090097 58.560621-15.306563 15.295379-36.458874 24.041631-58.099573 24.042874z' fill='{{(isStr ? colors : colors[8]) || 'rgb(126,202,252)'}}' /%3E%3Cpath d='M330.61033 751.17732m-161.754718 0a161.754718 161.754718 0 1 0 323.509437 0 161.754718 161.754718 0 1 0-323.509437 0Z' fill='{{(isStr ? colors : colors[9]) || 'rgb(126,202,252)'}}' /%3E%3Cpath d='M830.240311 780.791301H564.630369a7.044971 7.044971 0 1 1 0-14.089942h265.609942a7.044971 7.044971 0 1 1 0 14.089942zM830.240311 688.019883H564.630369a7.044971 7.044971 0 1 1 0-14.089941h265.609942a7.044971 7.044971 0 1 1 0 14.089941zM830.240311 595.248466H484.141981a7.044971 7.044971 0 1 1 0-14.089942h346.09833a7.044971 7.044971 0 1 1 0 14.089942zM830.240311 502.477049H484.141981a7.046214 7.046214 0 0 1 0-14.089942h346.09833a7.046214 7.046214 0 0 1 0 14.089942z' fill='{{(isStr ? colors : colors[10]) || 'rgb(126,202,252)'}}' /%3E%3Cpath d='M424.684117 845.248621c-4.916194 4.917437-12.885748 4.917437-17.801942 0L236.536544 674.905476c-4.916194-4.914951-4.916194-12.88699 0-17.803185 4.916194-4.912466 12.884505-4.916194 17.800699 0l170.345631 170.346874c4.916194 4.916194 4.916194 12.884505 0.001243 17.799456z' fill='{{(isStr ? colors : colors[11]) || 'rgb(255,255,255)'}}' /%3E%3Cpath d='M236.536544 845.248621c-4.916194-4.914951-4.917437-12.883262 0-17.799456l170.345631-170.346874c4.916194-4.916194 12.885748-4.912466 17.800699 0 4.916194 4.916194 4.917437 12.884505 0 17.803185L254.337243 845.248621c-4.916194 4.917437-12.885748 4.917437-17.800699 0z' fill='{{(isStr ? colors : colors[12]) || 'rgb(255,255,255)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--biyejieye-->
<view a:if="{{name === 'biyejieye'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M834.56 552.96v189.44c0 20.48-5.12 35.84-20.48 51.2-76.8 81.92-184.32 122.88-296.96 122.88-112.64 5.12-220.16-40.96-296.96-122.88-10.24-15.36-20.48-30.72-20.48-51.2v-189.44l261.12 102.4c20.48 10.24 35.84 10.24 56.32 10.24 20.48 0 40.96-5.12 56.32-10.24l261.12-102.4z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,153,255)'}}' /%3E%3Cpath d='M977.92 276.48L547.84 102.4c-10.24-5.12-20.48-5.12-30.72-5.12-10.24 0-20.48 0-25.6 5.12L66.56 276.48C25.6 291.84 10.24 332.8 25.6 373.76c5.12 20.48 20.48 30.72 40.96 40.96l430.08 174.08c15.36 10.24 35.84 10.24 56.32 0l424.96-174.08c35.84-15.36 56.32-56.32 40.96-97.28-10.24-20.48-25.6-35.84-40.96-40.96zM35.84 793.6c-20.48 0-35.84-15.36-35.84-35.84v-240.64c0-20.48 15.36-35.84 35.84-35.84s35.84 15.36 35.84 35.84v240.64c5.12 20.48-15.36 35.84-35.84 35.84z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,153,255)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--xunliandaka-xuanzhong-->
<view a:if="{{name === 'xunliandaka-xuanzhong'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M401.6 307.029333l233.322667 133.397334 0.085333-0.021334 144.234667-38.656a42.666667 42.666667 0 0 1 22.101333 82.432L657.066667 522.816c-3.925333 1.066667-7.893333 1.536-11.733334 1.472a42.709333 42.709333 0 0 1-38.912-1.856l-84.117333-48.085333-15.189333 26.154666c0.768 0.682667 1.514667 1.386667 2.261333 2.112l0.149333 0.149334 127.936 129.28c10.453333 10.581333 14.293333 25.216 11.52 38.784 0.085333 1.130667 0.149333 2.261333 0.149334 3.413333v106.666667c0 12.288-5.205333 23.36-13.504 31.146666h111.744l56.256-97.450666a64 64 0 0 1 110.869333 64l-74.666667 129.322666a64 64 0 0 1-63.210666 31.552 63.850667 63.850667 0 0 1-8.64 0.576H170.666667a64 64 0 1 1 0-128h406.656a42.538667 42.538667 0 0 1-13.504-31.146666l-0.021334-101.994667-101.013333-102.058667-63.786667 109.909334a42.773333 42.773333 0 0 1-39.04 21.248 42.901333 42.901333 0 0 1-10.304 1.258666H192a42.666667 42.666667 0 1 1 0-85.333333h144.768l111.466667-191.957333-73.024-41.749334-112.213334 63.04c-19.925333 11.2-45.184 4.266667-56.618666-15.530666a40.789333 40.789333 0 0 1 15.36-55.978667l132.629333-74.496a41.6 41.6 0 0 1 19.157333-5.333333c9.344-1.514667 19.221333 0.042667 28.074667 5.098666z m102.613333-199.829333l257.856 148.864a154.965333 154.965333 0 1 1-257.834666-148.864zM611.605333 64a155.029333 155.029333 0 0 1 149.312 113.386667l-188.010666-108.522667A155.242667 155.242667 0 0 1 611.584 64z' fill='{{(isStr ? colors : colors[0]) || 'rgb(84,178,119)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--a-xueshengxiuxuetuixue-->
<view a:if="{{name === 'a-xueshengxiuxuetuixue'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M460.597968 502.674158A251.321484 251.321484 0 0 1 212.894415 247.765931 251.321484 251.321484 0 0 1 460.597968 0a251.383862 251.383862 0 0 1 247.76593 254.877038 251.383862 251.383862 0 0 1-247.76593 247.79712z m0-464.280419v38.36255a174.658763 174.658763 0 0 0-171.539857 177.559346 174.658763 174.658763 0 0 0 171.539857 171.539856 174.658763 174.658763 0 0 0 171.539856-177.590535 174.658763 174.658763 0 0 0-171.539856-171.539857z' fill='{{(isStr ? colors : colors[0]) || 'rgb(34,114,212)'}}' /%3E%3Cpath d='M536.325017 937.855179H149.36229a26.479516 26.479516 0 0 1-25.886924-25.575033 339.555349 339.555349 0 0 1 149.177297-282.572927 25.949302 25.949302 0 0 1 27.758268-0.873294 319.469591 319.469591 0 0 0 320.374073 0 26.323571 26.323571 0 0 1 27.820646 0.810916 327.235668 327.235668 0 0 1 28.444427 23.267042 265.107051 265.107051 0 0 1 84.584744-26.292382 406.455893 406.455893 0 0 0-70.050639-60.537974 103.516506 103.516506 0 0 0-109.380051-3.524365 242.245466 242.245466 0 0 1-243.087571 0 103.079859 103.079859 0 0 0-109.442428 3.617932A417.278498 417.278498 0 0 0 46.999779 915.274296a102.70559 102.70559 0 0 0 101.801108 99.305983h414.315537a256.904327 256.904327 0 0 1-26.791407-76.7251z' fill='{{(isStr ? colors : colors[1]) || 'rgb(34,114,212)'}}' /%3E%3Cpath d='M877.970033 880.685623l89.668561-89.69975a31.189065 31.189065 0 0 0 0-44.101338 31.189065 31.189065 0 0 0-44.101337 0l-89.668562 89.699751-89.730939-89.73094a31.189065 31.189065 0 0 0-44.101338 0 31.189065 31.189065 0 0 0 0 44.101338l89.73094 89.730939-89.73094 89.699751a31.189065 31.189065 0 0 0 0 44.101337 30.970741 30.970741 0 0 0 22.050669 9.138396 30.970741 30.970741 0 0 0 22.050669-9.138396l89.730939-89.69975 89.668562 89.668561a30.970741 30.970741 0 0 0 22.050669 9.138396 30.970741 30.970741 0 0 0 22.050668-9.138396 31.189065 31.189065 0 0 0 0-44.101337z' fill='{{(isStr ? colors : colors[2]) || 'rgb(162,196,253)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--duicuo2-->
<view a:if="{{name === 'duicuo2'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 960A448 448 0 1 0 512 64a448 448 0 0 0 0 896z m226.304-674.24a44.8 44.8 0 0 1 0 63.36L575.424 512.064l162.88 162.88a44.8 44.8 0 1 1-63.36 63.36L512.064 575.424l-162.944 162.88a44.8 44.8 0 1 1-63.36-63.36l162.944-162.88-162.944-162.944a44.8 44.8 0 1 1 63.36-63.36l162.944 162.944 162.88-162.944a44.8 44.8 0 0 1 63.36 0z' fill='{{(isStr ? colors : colors[0]) || 'rgb(242,29,29)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--duicuo1-->
<view a:if="{{name === 'duicuo1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 64a448 448 0 1 1 0 896A448 448 0 0 1 512 64z m161.664 266.304L473.728 556.992a32 32 0 0 1-43.648 4.096L336 488a39.168 39.168 0 1 0-48.064 61.888l128.448 99.84a64 64 0 0 0 87.296-8.192l228.8-259.328a39.232 39.232 0 0 0-58.816-51.84z' fill='{{(isStr ? colors : colors[0]) || 'rgb(1,191,26)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--a-shoucang-yishoucang-->
<view a:if="{{name === 'a-shoucang-yishoucang'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M249.027212 1024a81.085086 81.085086 0 0 1-47.614289-15.359448 82.461037 82.461037 0 0 1-34.302767-81.917056l40.958528-251.894948a31.99885 31.99885 0 0 0-8.703687-27.647006L23.755308 466.452037a83.932984 83.932984 0 0 1-19.455301-84.988946 82.301042 82.301042 0 0 1 65.917631-55.805994L307.905096 289.306403a31.198879 31.198879 0 0 0 24.063135-17.919356l104.956229-223.351973a82.90902 82.90902 0 0 1 150.394595 0l104.540243 223.351973a31.99885 31.99885 0 0 0 24.063135 17.919356l237.463466 36.350694a83.453001 83.453001 0 0 1 46.590326 140.79494l-175.609689 180.729505a32.606828 32.606828 0 0 0-8.703687 27.647006l40.958528 251.894948a83.804988 83.804988 0 0 1-34.302767 81.917056 81.853058 81.853058 0 0 1-88.060836 4.607834l-206.712571-114.683878a32.670826 32.670826 0 0 0-30.718896 0l-207.352548 115.19586a87.964839 87.964839 0 0 1-40.446547 10.239632z' fill='{{(isStr ? colors : colors[0]) || 'rgb(254,180,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--weishoucang-->
<view a:if="{{name === 'weishoucang'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M249.027212 1024a81.085086 81.085086 0 0 1-47.614289-15.359448 82.461037 82.461037 0 0 1-34.302767-81.917056l40.958528-251.894948a31.99885 31.99885 0 0 0-8.703687-27.647006L23.755308 466.452037a83.932984 83.932984 0 0 1-19.455301-84.988946 82.301042 82.301042 0 0 1 65.917631-55.805994L307.905096 289.306403a31.198879 31.198879 0 0 0 24.063135-17.919356l104.956229-223.351973a82.90902 82.90902 0 0 1 150.394595 0l104.540243 223.351973a31.99885 31.99885 0 0 0 24.063135 17.919356l237.463466 36.350694a83.453001 83.453001 0 0 1 46.590326 140.79494l-175.609689 180.729505a32.606828 32.606828 0 0 0-8.703687 27.647006l40.958528 251.894948a83.804988 83.804988 0 0 1-34.302767 81.917056 81.853058 81.853058 0 0 1-88.060836 4.607834l-206.712571-114.683878a32.670826 32.670826 0 0 0-30.718896 0l-207.352548 115.19586a87.964839 87.964839 0 0 1-40.446547 10.239632z' fill='{{(isStr ? colors : colors[0]) || 'rgb(219,219,219)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--mimi-->
<view a:if="{{name === 'mimi'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M261.76 793.41H119.95c-27.57 0-50-22.43-50-50V271.56c0-27.57 22.43-50 50-50h141.81c16.57 0 30 13.43 30 30s-13.43 30-30 30H129.95v451.85h131.81c16.57 0 30 13.43 30 30s-13.43 30-30 30z' fill='{{(isStr ? colors : colors[0]) || 'rgb(1,123,254)'}}' /%3E%3Cpath d='M899.47 797.3L432.53 911.76c-25.2 6.18-49.52-12.9-49.52-38.85V144.64c0-25.89 24.22-44.96 49.38-38.88l466.94 112.69c17.96 4.33 30.62 20.41 30.62 38.88v501.13c-0.01 18.41-12.59 34.46-30.48 38.84z' fill='{{(isStr ? colors : colors[1]) || 'rgb(183,217,255)'}}' /%3E%3Cpath d='M422.86 942.94c-15.46 0-30.55-5.14-43.05-14.95-17.03-13.36-26.79-33.43-26.79-55.08V144.64c0-21.59 9.73-41.64 26.7-55s38.73-18.11 59.73-13.05l466.94 112.69c31.55 7.61 53.58 35.59 53.58 68.05v501.13a69.85 69.85 0 0 1-53.33 67.99L439.67 940.9a70.467 70.467 0 0 1-16.81 2.04z m0.07-808.32c-2.87 0-4.99 1.27-6.11 2.16-1.42 1.12-3.81 3.63-3.81 7.86v728.28c0 4.23 2.4 6.75 3.83 7.87 1.43 1.12 4.44 2.85 8.55 1.84l466.94-114.46 7.14 29.14-7.14-29.14a9.97 9.97 0 0 0 7.62-9.71V257.32c0-4.64-3.15-8.63-7.65-9.72L425.35 134.92c-0.85-0.21-1.66-0.3-2.42-0.3z' fill='{{(isStr ? colors : colors[2]) || 'rgb(1,123,254)'}}' /%3E%3Cpath d='M513.28 508.87m-45 0a45 45 0 1 0 90 0 45 45 0 1 0-90 0Z' fill='{{(isStr ? colors : colors[3]) || 'rgb(1,123,254)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--mimimiji-->
<view a:if="{{name === 'mimimiji'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1025 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M223.104235 882.216261A442.846235 442.846235 0 0 1 56.987033 575.170126V172.670711a36.699649 36.699649 0 0 1 36.870611-35.901831h41.543547a573.17558 573.17558 0 0 0 176.659803-36.528689 807.563248 807.563248 0 0 0 153.86499-78.243196l24.789359-16.640214a37.440481 37.440481 0 0 1 44.221938 0l24.789359 16.640214a794.513217 794.513217 0 0 0 153.86499 78.243196 571.465969 571.465969 0 0 0 176.203907 35.844844h41.543547a37.383494 37.383494 0 0 1 36.87061 35.901831v403.18326a442.618287 442.618287 0 0 1-166.174188 308.29985 368.193222 368.193222 0 0 1-578.931271-1.253715z m470.313985-413.440926h-13.391953v-68.38444a150.274807 150.274807 0 0 0-153.864989-146.114753c-4.21704 0-8.434081 0-12.651122 0.569871A151.015638 151.015638 0 0 0 347.335967 386.941956a131.981969 131.981969 0 0 0-0.683844 13.448939v68.38444h-13.391953a49.17981 49.17981 0 0 0-53.567811 40.403807v159.563693A89.469642 89.469642 0 0 0 341.922199 739.064834h341.9222a90.210474 90.210474 0 0 0 63.19862-70.265012V511.116701a49.122823 49.122823 0 0 0-53.567812-40.34682v-1.937559z m-276.045189-56.987033a96.877956 96.877956 0 0 1 193.015082 0v53.738772H417.430018v-53.852746z' fill='{{(isStr ? colors : colors[0]) || 'rgb(237,157,62)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--paihang-->
<view a:if="{{name === 'paihang'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M0 0m228.968944 0l566.062112 0q228.968944 0 228.968944 228.968944l0 566.062112q0 228.968944-228.968944 228.968944l-566.062112 0q-228.968944 0-228.968944-228.968944l0-566.062112q0-228.968944 228.968944-228.968944Z' fill='{{(isStr ? colors : colors[0]) || 'rgb(52,204,254)'}}' /%3E%3Cpath d='M240.417391 291.299379h101.763975v259.498137c-56.202971 0-101.763975-45.561004-101.763975-101.763976V291.299379zM783.582609 291.299379h-101.763975v259.498137c56.202971 0 101.763975-45.561004 101.763975-101.763976V291.299379z' fill='{{(isStr ? colors : colors[1]) || 'rgb(255,255,255)'}}' opacity='.5' /%3E%3Cpath d='M325.64472 212.432298h372.71056v303.383851c0 94.071891-69.701963 171.862817-160.278261 184.545153V758.141615h79.503105c14.402147 0 26.077019 11.674872 26.077019 26.077019s-11.674872 26.077019-26.077019 26.077018h-209.888199c-14.402147 0-26.077019-11.674872-26.077018-26.077018s11.674872-26.077019 26.077018-26.077019H485.922981v-57.780313c-90.576298-12.682335-160.278261-90.473262-160.278261-184.545153V212.432298z' fill='{{(isStr ? colors : colors[2]) || 'rgb(255,255,255)'}}' /%3E%3Cpath d='M498.78086 346.332065c5.069118-10.622887 20.188701-10.622887 25.257818 0l15.424875 32.326598a13.992547 13.992547 0 0 0 10.799702 7.846003l35.511811 4.681143c11.668512 1.537908 16.34075 15.91843 7.804025 24.021386l-25.977799 24.659955a13.992547 13.992547 0 0 0-4.125257 12.695056l6.521799 35.220512c2.143404 11.571836-10.088626 20.459647-20.432935 14.84482l-31.480685-17.084899a13.986186 13.986186 0 0 0-13.34889 0l-31.480685 17.084899c-10.344308 5.614827-22.576338-3.272984-20.434207-14.84482l6.521799-35.220512a13.98873 13.98873 0 0 0-4.125257-12.695056l-25.977799-24.659955c-8.535453-8.102957-3.863215-22.483478 7.805297-24.021386l35.511811-4.681143a13.993819 13.993819 0 0 0 10.799702-7.846003l15.424875-32.326598z' fill='{{(isStr ? colors : colors[3]) || 'rgb(52,204,254)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--shoucang-->
<view a:if="{{name === 'shoucang'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M887.7 380.2L693 331.4c-3.6-0.9-6.7-3.2-8.7-6.3l-106.5-170c-4.6-7.3-10.8-13.5-18.1-18.1-26.8-16.8-62.2-8.7-79 18.1L374 325.2c-2 3.1-5.1 5.4-8.7 6.3l-194.7 48.8c-8.4 2.1-16.2 6.1-22.9 11.7-24.3 20.3-27.5 56.5-7.2 80.8l33.6 40.2 42.6 3.8c201.6 18 379.4 143.6 464 327.9l18.9 41.1 21 8.4c8 3.2 16.7 4.6 25.4 4 31.6-2.2 55.4-29.5 53.3-61.1l-13.8-200.2c-0.3-3.7 0.9-7.3 3.3-10.2l128.8-153.9c5.6-6.6 9.5-14.5 11.7-22.9 7.7-30.9-10.9-62-41.6-69.7z' fill='{{(isStr ? colors : colors[0]) || 'rgb(40,103,206)'}}' /%3E%3Cpath d='M337.6 894.1l186.2-75c1.7-0.7 3.5-1 5.4-1 1.8 0 3.6 0.3 5.4 1l107.5 43.3c-76.5-166.7-238-286.2-428.9-303.2l56.4 67.4c2.4 2.8 3.6 6.5 3.3 10.2L259 836.9c-0.6 8.6 0.8 17.3 4 25.4 9 22.3 30.5 35.9 53.2 35.9 7.2 0 14.4-1.3 21.4-4.1z' fill='{{(isStr ? colors : colors[1]) || 'rgb(189,210,239)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--jiaotongtubiao-youzhuan-->
<view a:if="{{name === 'jiaotongtubiao-youzhuan'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M968.704 451.072l-392.192-392.192c-29.184-29.184-75.776-29.184-104.96 0L78.848 451.072c-29.184 29.184-29.184 75.776 0 104.96L471.04 948.224c29.184 29.184 75.776 29.184 104.96 0l392.192-392.192c29.184-29.184 29.184-75.776 0.512-104.96z m-392.192 100.352v-49.152H405.504v121.856H335.36V435.2h241.152V382.976l135.68 84.48-135.68 83.968z' fill='{{(isStr ? colors : colors[0]) || 'rgb(251,176,59)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--liucheng-suijiqi1-->
<view a:if="{{name === 'liucheng-suijiqi1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 0c341.333333 0 512 170.666667 512 512s-170.666667 512-512 512S0 853.333333 0 512 170.666667 0 512 0z' fill='{{(isStr ? colors : colors[0]) || 'rgb(114,99,221)'}}' /%3E%3Cpath d='M552.789333 604.373333l32 41.173334c9.728 12.629333 25.258667 18.901333 40.704 19.968l122.538667-0.554667a19.754667 19.754667 0 0 1 7.168 1.365333c2.56 0.853333 4.949333 2.389333 6.997333 4.437334a19.797333 19.797333 0 0 1 0 27.818666l-52.48 52.48a19.456 19.456 0 0 1-13.909333 5.76 19.456 19.456 0 0 1-13.909333-5.76 19.797333 19.797333 0 0 1 0-27.818666l18.688-18.730667-75.093334 0.341333c-27.818667 0-54.570667-13.141333-71.637333-35.157333l-32-41.216a19.84 19.84 0 0 1 3.413333-27.562667 19.797333 19.797333 0 0 1 27.52 3.413334z m156.928-299.989333l52.48 52.48 0.768 0.853333a20.096 20.096 0 0 1 0.981334 1.194667l-1.706667-2.048a19.328 19.328 0 0 1 4.352 6.741333 18.816 18.816 0 0 1 0 14.506667l-0.341333 0.64a17.493333 17.493333 0 0 1-3.413334 5.205333 6.101333 6.101333 0 0 1-0.64 0.725334l-0.853333 0.810666a20.138667 20.138667 0 0 1-0.853333 0.682667l1.706666-1.493333a19.925333 19.925333 0 0 1-3.84 2.986666l-0.682666 0.384a14.08 14.08 0 0 1-2.432 1.152l-1.024 0.341334a14.08 14.08 0 0 1-2.176 0.554666l-3.712 0.341334-119.424-0.512a50.773333 50.773333 0 0 0-42.752 22.826666l-167.68 251.434667a89.728 89.728 0 0 1-75.349334 40.405333H342.613333l-66.944-0.256A19.84 19.84 0 0 1 256 684.672c0-10.752 8.96-19.712 19.669333-19.712l66.944 0.298667a50.773333 50.773333 0 0 0 42.752-22.869334l167.68-251.392a90.368 90.368 0 0 1 75.349334-40.448h0.512l71.68 0.298667-18.688-18.645333a19.797333 19.797333 0 0 1 0-27.818667 19.797333 19.797333 0 0 1 27.818666 0zM344.96 350.848c29.141333 0 56.448 14.165333 73.472 37.546667l28.373333 39.338666a19.754667 19.754667 0 0 1-4.48 27.562667 20.181333 20.181333 0 0 1-27.818666-4.48l-28.330667-39.381333a52.224 52.224 0 0 0-41.728-21.248l-68.522667 0.256c-12.074667-0.256-19.626667-8.661333-19.626666-19.626667a19.626667 19.626667 0 0 1 19.626666-19.712l68.522667-0.256h0.512z' fill='{{(isStr ? colors : colors[1]) || 'rgb(255,255,255)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--tikuzongliang-->
<view a:if="{{name === 'tikuzongliang'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1248 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M43.866656 545.344085l534.928652-314.6001a88.793166 88.793166 0 0 1 90.040718 0l534.928652 314.6001a88.793166 88.793166 0 0 1 0 153.177703l-534.982893 313.244065a88.793166 88.793166 0 0 1-89.71527 0L43.866656 698.630271a88.793166 88.793166 0 0 1 0-153.177703z' fill='{{(isStr ? colors : colors[0]) || 'rgb(60,100,209)'}}' /%3E%3Cpath d='M43.920898 326.85974l534.928652-314.6001a88.793166 88.793166 0 0 1 90.040718 0l534.928652 314.6001a88.793166 88.793166 0 0 1 0 153.177704l-535.091377 313.244064a88.793166 88.793166 0 0 1-89.715269 0L43.920898 480.037444a88.793166 88.793166 0 0 1 0-153.177704z' fill='{{(isStr ? colors : colors[1]) || 'rgb(141,176,255)'}}' /%3E%3Cpath d='M665.039129 190.117179l483.99598 284.387642-484.212946 283.574021a88.793166 88.793166 0 0 1-89.71527 0L90.893947 474.504821 574.889928 190.062938a88.793166 88.793166 0 0 1 90.040718 0z' fill='{{(isStr ? colors : colors[2]) || 'rgb(79,126,220)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--icon-zhuanxianglianxi-->
<view a:if="{{name === 'icon-zhuanxianglianxi'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M0 0m398.222222 0l227.555556 0q398.222222 0 398.222222 398.222222l0 227.555556q0 398.222222-398.222222 398.222222l-227.555556 0q-398.222222 0-398.222222-398.222222l0-227.555556q0-398.222222 398.222222-398.222222Z' fill='{{(isStr ? colors : colors[0]) || 'rgb(0,220,168)'}}' /%3E%3Cpath d='M693.361778 199.168H330.581333a113.777778 113.777778 0 0 0-113.777777 113.777778v398.222222a113.777778 113.777778 0 0 0 113.777777 113.777778h362.780445a113.777778 113.777778 0 0 0 113.777778-113.777778v-398.222222a113.777778 113.777778 0 0 0-113.777778-113.777778z m-242.403556 312.888889h-129.365333a28.444444 28.444444 0 0 1 0-56.888889h129.365333a28.444444 28.444444 0 0 1 0 56.888889z m-129.365333-134.4a28.444444 28.444444 0 0 1 0-56.888889h240.298667a28.444444 28.444444 0 0 1 0 56.888889z m416.142222 283.278222l-30.293333 29.639111a9.500444 9.500444 0 0 0-2.844445 8.533334l7.168 41.671111a9.557333 9.557333 0 0 1-13.937777 10.097777l-37.518223-19.655111a9.614222 9.614222 0 0 0-8.874666 0l-37.489778 19.712a9.614222 9.614222 0 0 1-12.970667-3.925333 9.870222 9.870222 0 0 1-0.967111-6.229333l7.196445-41.756445a9.557333 9.557333 0 0 0-2.844445-8.533333l-30.321778-29.525333a9.585778 9.585778 0 0 1 5.290667-16.355556l41.898667-6.058667a9.642667 9.642667 0 0 0 7.224889-5.290666l18.801777-37.973334a9.585778 9.585778 0 0 1 17.237334 0l18.716444 37.973334a9.728 9.728 0 0 0 7.253333 5.290666l41.984 6.058667a9.585778 9.585778 0 0 1 8.135112 10.808889 9.472 9.472 0 0 1-2.844445 5.461333z' fill='{{(isStr ? colors : colors[1]) || 'rgb(255,255,255)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--VIP1-->
<view a:if="{{name === 'VIP1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M515.4816 517.632m-436.2752 0a436.2752 436.2752 0 1 0 872.5504 0 436.2752 436.2752 0 1 0-872.5504 0Z' fill='{{(isStr ? colors : colors[0]) || 'rgb(219,144,246)'}}' /%3E%3Cpath d='M86.8864 598.9888a428.5952 354.9696 0 1 0 857.1904 0 428.5952 354.9696 0 1 0-857.1904 0Z' fill='{{(isStr ? colors : colors[1]) || 'rgb(209,134,247)'}}' /%3E%3Cpath d='M110.6432 680.2944a404.8384 273.664 0 1 0 809.6768 0 404.8384 273.664 0 1 0-809.6768 0Z' fill='{{(isStr ? colors : colors[2]) || 'rgb(194,118,246)'}}' /%3E%3Cpath d='M153.7024 761.6a361.7792 192.3072 0 1 0 723.5584 0 361.7792 192.3072 0 1 0-723.5584 0Z' fill='{{(isStr ? colors : colors[3]) || 'rgb(175,100,246)'}}' /%3E%3Cpath d='M224.8704 842.9568c77.1584 69.0176 178.9952 111.0016 290.6624 111.0016s213.504-41.984 290.6624-111.0016c-77.1584-69.0176-178.9952-111.0016-290.6624-111.0016s-213.504 41.984-290.6624 111.0016z' fill='{{(isStr ? colors : colors[4]) || 'rgb(162,91,244)'}}' /%3E%3Cpath d='M306.2272 330.4448h131.328v233.5232l197.4272-233.5232h150.6816l-331.3664 416.8704H334.4384V383.3344h-28.2112z' fill='{{(isStr ? colors : colors[5]) || 'rgb(255,255,255)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--VIP-->
<view a:if="{{name === 'VIP'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M511.0784 512m-436.2752 0a436.2752 436.2752 0 1 0 872.5504 0 436.2752 436.2752 0 1 0-872.5504 0Z' fill='{{(isStr ? colors : colors[0]) || 'rgb(248,210,255)'}}' /%3E%3Cpath d='M556.032 206.1824L747.52 316.7232A89.7024 89.7024 0 0 1 792.3712 394.24v221.1328a89.7024 89.7024 0 0 1-44.8512 77.824L556.032 803.84a89.7024 89.7024 0 0 1-89.7024 0l-191.5392-110.592a89.6512 89.6512 0 0 1-44.8-77.6704V394.24a89.6512 89.6512 0 0 1 44.8-77.6704l191.5392-110.3872a89.7024 89.7024 0 0 1 89.7024 0z' fill='{{(isStr ? colors : colors[1]) || 'rgb(219,144,246)'}}' /%3E%3Cpath d='M760.3712 430.08l-217.1904-125.44a64.1536 64.1536 0 0 0-64.0512 0L261.9904 430.08a63.9488 63.9488 0 0 0-32 55.4496v144.896a63.9488 63.9488 0 0 0 32 55.3984l217.1392 125.3888a63.8976 63.8976 0 0 0 64.0512 0l217.1904-125.3888a64.0512 64.0512 0 0 0 32-55.3984V485.4784A64.0512 64.0512 0 0 0 760.3712 430.08z' fill='{{(isStr ? colors : colors[2]) || 'rgb(209,134,247)'}}' /%3E%3Cpath d='M760.3712 535.9104l-217.1904-125.3888a64.1536 64.1536 0 0 0-64.0512 0l-217.1392 125.3888a63.9488 63.9488 0 0 0-32 55.4496v39.0144a63.9488 63.9488 0 0 0 32 55.3984l217.1392 125.3888a63.8976 63.8976 0 0 0 64.0512 0l217.1904-125.3888a64.0512 64.0512 0 0 0 32-55.3984v-39.0144a64.0512 64.0512 0 0 0-32-55.4496z' fill='{{(isStr ? colors : colors[3]) || 'rgb(194,118,246)'}}' /%3E%3Cpath d='M543.1808 516.4032a64.1536 64.1536 0 0 0-64.0512 0l-217.1392 125.3888a64.1536 64.1536 0 0 0-22.5792 22.016 64.4608 64.4608 0 0 0 22.5792 21.9648l217.1392 125.3888a63.8976 63.8976 0 0 0 64.0512 0l217.1904-125.3888a64.3072 64.3072 0 0 0 22.528-21.9648 64 64 0 0 0-22.528-22.016z' fill='{{(isStr ? colors : colors[4]) || 'rgb(175,100,246)'}}' /%3E%3Cpath d='M479.1296 622.2848L315.5968 716.8l163.5328 94.4128a63.8976 63.8976 0 0 0 64.0512 0L706.56 716.8l-163.5328-94.464a64.1536 64.1536 0 0 0-63.8976-0.0512z' fill='{{(isStr ? colors : colors[5]) || 'rgb(162,91,244)'}}' /%3E%3Cpath d='M392.9088 617.6256A21.8624 21.8624 0 0 1 372.736 604.16l-76.8-181.504a21.9136 21.9136 0 0 1 40.3968-17.1008l54.9888 130.304 47.4624-129.28a21.9136 21.9136 0 0 1 40.96 15.0528l-66.56 181.5552a21.8112 21.8112 0 0 1-20.0192 14.336zM530.6368 617.6256a21.9136 21.9136 0 0 1-21.9136-21.9136V414.208a21.9136 21.9136 0 0 1 43.8272 0v181.504a21.8624 21.8624 0 0 1-21.9136 21.9136zM613.632 617.6256a21.9136 21.9136 0 0 1-21.9136-21.9136V414.208a21.9136 21.9136 0 0 1 21.9136-21.9136h55.6544a69.376 69.376 0 1 1 0 138.7008h-33.6896v64.7168a21.9136 21.9136 0 0 1-21.9648 21.9136z m21.9648-130.4576h33.6896a25.6 25.6 0 0 0 0-51.2h-33.6896z' fill='{{(isStr ? colors : colors[6]) || 'rgb(255,255,255)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-qrcode-->
<view a:if="{{name === 'line-qrcode'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M384 96H192c-52.935 0-96 43.065-96 96v192c0 52.935 43.065 96 96 96h192c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m32 288c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h192c17.645 0 32 14.355 32 32v192zM384 544H192c-52.935 0-96 43.065-96 96v192c0 52.935 43.065 96 96 96h192c52.935 0 96-43.065 96-96V640c0-52.935-43.065-96-96-96z m32 288c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V640c0-17.645 14.355-32 32-32h192c17.645 0 32 14.355 32 32v192zM832 96H640c-52.935 0-96 43.065-96 96v192c0 52.935 43.065 96 96 96h192c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m32 288c0 17.645-14.355 32-32 32H640c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h192c17.645 0 32 14.355 32 32v192zM608 576c-17.673 0-32 14.327-32 32v32c0 17.673 14.327 32 32 32s32-14.327 32-32v-32c0-17.673-14.327-32-32-32zM608 704c-17.673 0-32 14.327-32 32v128c0 17.673 14.327 32 32 32s32-14.327 32-32V736c0-17.673-14.327-32-32-32zM864 576c-17.673 0-32 14.327-32 32v32c0 17.673 14.327 32 32 32s32-14.327 32-32v-32c0-17.673-14.327-32-32-32zM864 704c-17.673 0-32 14.327-32 32v128c0 17.673 14.327 32 32 32s32-14.327 32-32V736c0-17.673-14.327-32-32-32zM736 800c-17.673 0-32 14.327-32 32v32c0 17.673 14.327 32 32 32s32-14.327 32-32v-32c0-17.673-14.327-32-32-32zM736 576c-17.673 0-32 14.327-32 32v128c0 17.673 14.327 32 32 32s32-14.327 32-32V608c0-17.673-14.327-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-picture-->
<view a:if="{{name === 'line-picture'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M864 128H160c-52.935 0-96 43.065-96 96v576c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V224c0-52.935-43.065-96-96-96z m32 672c0 17.645-14.355 32-32 32H160c-17.645 0-32-14.355-32-32V224c0-17.645 14.355-32 32-32h704c17.645 0 32 14.355 32 32v576z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M745.372 425.373L575.999 594.745 470.627 489.373c-12.497-12.497-32.758-12.497-45.255 0l-192 192c-12.497 12.497-12.497 32.759 0 45.256C239.621 732.876 247.811 736 256 736s16.379-3.124 22.627-9.372L448 557.255l105.334 105.334 0.038 0.04a31.852 31.852 0 0 0 16.516 8.786 32.117 32.117 0 0 0 12.224 0 31.86 31.86 0 0 0 16.516-8.786l192-192c12.496-12.497 12.496-32.758 0-45.255-12.498-12.499-32.758-12.499-45.256-0.001zM304 416c26.51 0 48-21.49 48-48s-21.49-48-48-48-48 21.49-48 48 21.49 48 48 48z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-relation-->
<view a:if="{{name === 'line-relation'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M416 640H256c-70.58 0-128-57.421-128-128 0-70.58 57.42-128 128-128h160c17.673 0 32-14.327 32-32s-14.327-32-32-32H256c-105.869 0-192 86.131-192 192s86.131 192 192 192h160c17.673 0 32-14.327 32-32s-14.327-32-32-32zM768 320H608c-17.673 0-32 14.327-32 32s14.327 32 32 32h160c70.579 0 128 57.42 128 128 0 70.579-57.421 128-128 128H608c-17.673 0-32 14.327-32 32s14.327 32 32 32h160c105.869 0 192-86.131 192-192s-86.131-192-192-192z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M288 512c0 17.673 14.327 32 32 32h384c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32H320c-17.673 0-32 14.327-32 32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-reduceuser1-->
<view a:if="{{name === 'line-reduceuser1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M731.701 579.397c8.85-15.298 3.622-34.873-11.676-43.723-24.291-14.052-49.921-25.536-76.437-34.357a226.986 226.986 0 0 0 26.803-22.925C712.7 436.084 736 379.833 736 320s-23.3-116.084-65.608-158.392C628.084 119.3 571.832 96 512 96c-59.833 0-116.084 23.3-158.393 65.608C311.3 203.916 288 260.167 288 320s23.3 116.084 65.607 158.392a226.96 226.96 0 0 0 26.734 22.875 416.054 416.054 0 0 0-30.278 11.437c-49.541 20.954-94.026 50.945-132.221 89.14s-68.185 82.68-89.139 132.221C107.003 785.371 96 839.854 96 896c0 17.673 14.327 32 32 32s32-14.327 32-32c0-94.022 36.614-182.418 103.099-248.901C329.582 580.614 417.978 544 512 544c61.892 0 122.744 16.277 175.979 47.073 15.299 8.851 34.874 3.621 43.722-11.676zM512 480c-88.225 0-160-71.776-160-160s71.775-160 160-160 160 71.776 160 160-71.775 160-160 160zM895 759H687c-17.673 0-32 14.327-32 32s14.327 32 32 32h208c17.673 0 32-14.327 32-32s-14.327-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-purchase-->
<view a:if="{{name === 'line-purchase'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M832 96H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m-192 64v32c0 17.645-14.355 32-32 32H416c-17.645 0-32-14.355-32-32v-32h256z m224 672c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h128v32c0 52.935 43.065 96 96 96h192c52.935 0 96-43.065 96-96v-32h128c17.645 0 32 14.355 32 32v640z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M672 516H471.426l62.851-36.287c15.306-8.836 20.55-28.408 11.713-43.713s-28.406-20.551-43.713-11.713l-166.277 96c-0.044 0.025-0.085 0.053-0.128 0.079-0.352 0.206-0.698 0.418-1.041 0.637-0.134 0.085-0.267 0.169-0.399 0.256-0.275 0.181-0.545 0.367-0.814 0.556-0.193 0.135-0.386 0.27-0.575 0.409-0.184 0.136-0.365 0.276-0.546 0.415-0.259 0.199-0.518 0.399-0.77 0.605l-0.277 0.233c-0.325 0.272-0.646 0.547-0.959 0.831l-0.025 0.023a31.78 31.78 0 0 0-5.803 7.031c-0.127 0.208-0.245 0.423-0.368 0.634-0.125 0.216-0.254 0.43-0.373 0.649a31.75 31.75 0 0 0-3.186 8.538l-0.008 0.035a29.96 29.96 0 0 0-0.239 1.243c-0.021 0.12-0.044 0.239-0.064 0.359-0.052 0.319-0.096 0.64-0.138 0.962-0.031 0.23-0.062 0.459-0.088 0.69a31.94 31.94 0 0 0-0.066 0.694c-0.03 0.33-0.056 0.66-0.075 0.991-0.009 0.156-0.015 0.312-0.022 0.468-0.018 0.409-0.029 0.818-0.031 1.229 0 0.049-0.004 0.097-0.004 0.146 0 0.056 0.004 0.111 0.004 0.167 0.002 0.447 0.013 0.895 0.034 1.343l0.014 0.269a30.933 30.933 0 0 0 0.132 1.636 31.515 31.515 0 0 0 0.408 2.707c0.036 0.185 0.072 0.37 0.111 0.554 0.064 0.298 0.134 0.596 0.206 0.893 0.058 0.239 0.117 0.477 0.18 0.714a36.85 36.85 0 0 0 0.468 1.584c0.059 0.18 0.12 0.36 0.182 0.539 0.117 0.342 0.239 0.681 0.368 1.017 0.051 0.133 0.104 0.265 0.156 0.397 0.151 0.38 0.308 0.756 0.474 1.129 0.043 0.098 0.088 0.195 0.132 0.292a29.47 29.47 0 0 0 0.698 1.436c0.204 0.395 0.417 0.785 0.637 1.17 0.029 0.05 0.054 0.102 0.083 0.152 0.019 0.033 0.041 0.064 0.06 0.097a31.993 31.993 0 0 0 2.214 3.309 35.719 35.719 0 0 0 1.164 1.432c0.185 0.216 0.367 0.433 0.557 0.643 0.342 0.378 0.694 0.747 1.053 1.108 0.265 0.266 0.538 0.521 0.811 0.777 0.18 0.168 0.359 0.336 0.543 0.499 0.266 0.238 0.535 0.472 0.808 0.7 0.204 0.171 0.413 0.335 0.622 0.5 0.365 0.29 0.735 0.573 1.111 0.845 0.334 0.242 0.67 0.482 1.014 0.711 0.203 0.135 0.409 0.264 0.615 0.394 0.355 0.226 0.714 0.444 1.078 0.656 0.162 0.094 0.322 0.188 0.485 0.279a31.854 31.854 0 0 0 5.432 2.397l0.056 0.019c1.281 0.425 2.596 0.774 3.944 1.037l0.06 0.011c0.582 0.112 1.169 0.206 1.762 0.286 0.165 0.023 0.331 0.043 0.497 0.063 0.444 0.053 0.89 0.096 1.34 0.13 0.221 0.017 0.441 0.038 0.662 0.05 0.421 0.024 0.845 0.034 1.27 0.041 0.195 0.004 0.388 0.019 0.583 0.019 0.051 0 0.102-0.005 0.153-0.005H672c17.673 0 32-14.327 32-32S689.673 516 672 516zM672 672H352c-17.673 0-32 14.327-32 32s14.327 32 32 32h320c17.673 0 32-14.327 32-32s-14.327-32-32-32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-matting-->
<view a:if="{{name === 'line-matting'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M832 288h-96v-96c0-52.9-43.1-96-96-96H192c-52.9 0-96 43.1-96 96v448c0 52.9 43.1 96 96 96h96v96c0 52.9 43.1 96 96 96h448c52.9 0 96-43.1 96-96V384c0-52.9-43.1-96-96-96zM160 640V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v96h-32c-17.7 0-32 14.3-32 32s14.3 32 32 32h32v288c0 17.6-14.4 32-32 32H352v-32c0-17.7-14.3-32-32-32s-32 14.3-32 32v32h-96c-17.6 0-32-14.4-32-32z m704 192c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32v-96h288c52.9 0 96-43.1 96-96V352h96c17.6 0 32 14.4 32 32v448z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M320 576c17.7 0 32-14.3 32-32v-64c0-17.7-14.3-32-32-32s-32 14.3-32 32v64c0 17.7 14.3 32 32 32zM480 352h64c17.7 0 32-14.3 32-32s-14.3-32-32-32h-64c-17.7 0-32 14.3-32 32s14.3 32 32 32zM320 416c17.7 0 32-14.3 32-32 0-17.6 14.4-32 32-32 17.7 0 32-14.3 32-32s-14.3-32-32-32c-52.9 0-96 43.1-96 96 0 17.7 14.3 32 32 32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-organizational-->
<view a:if="{{name === 'line-organizational'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M864 608.002h-47.999V512c0-2.209-0.224-4.366-0.65-6.449s-1.055-4.092-1.865-6.007a31.976 31.976 0 0 0-4.793-7.899 32.157 32.157 0 0 0-9.44-7.783A31.905 31.905 0 0 0 784 479.999H547v-64h48c52.935 0 96-43.065 96-96V160c0-52.935-43.065-96-96-96H435c-52.935 0-96 43.065-96 96v160c0 52.935 43.065 96 96 96h48v64H239.999a31.89 31.89 0 0 0-15.253 3.863 32.075 32.075 0 0 0-9.44 7.783 32.037 32.037 0 0 0-4.793 7.899c-0.81 1.914-1.438 3.924-1.865 6.007s-0.65 4.24-0.65 6.449v96.002H160c-52.935 0-96 43.065-96 95.999V864c0 52.935 43.065 96 96 96h159.999c52.935 0 96-43.065 96-96V704.001c0-52.934-43.065-95.999-96-95.999H272V544h480.001v64.002h-47.999c-52.935 0-96 43.065-96 96V864c0 52.935 43.065 96 96 96H864c52.935 0 96-43.065 96-96V704.002c0-52.935-43.065-96-96-96zM403 320V160c0-17.645 14.355-32 32-32h160c17.645 0 32 14.355 32 32v160c0 17.645-14.355 32-32 32H435c-17.645 0-32-14.355-32-32z m-51.001 384.001V864c0 17.645-14.355 32-32 32H160c-17.645 0-32-14.355-32-32V704.001c0-17.645 14.355-31.999 32-31.999h77.997c0.663 0.041 1.329 0.069 2.002 0.069 0.673 0 1.339-0.028 2.002-0.069h77.997c17.646 0 32.001 14.354 32.001 31.999zM896 864c0 17.645-14.355 32-32 32H704.002c-17.645 0-32-14.355-32-32V704.002c0-17.645 14.355-32 32-32H864c17.645 0 32 14.355 32 32V864z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-role-->
<view a:if="{{name === 'line-role'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M775.6 84.8C770.1 73.8 759 67 746.8 67H277.2c-12.3 0-23.3 6.8-28.9 17.8-5.5 11-4.3 23.9 3.1 33.7l126.8 167.4-86.1 388.6c-2.1 9.3 0.1 19 6 26.5l188.1 243.1c0.8 1.1 1.7 2.1 2.7 3.1l0.5 0.5 0.6 0.6c0.3 0.3 0.7 0.6 1 0.9l0.1 0.1c4.4 3.8 9.8 6.3 15.6 7.2h0.1c0.7 0.1 1.5 0.2 2.2 0.3 0.2 0 0.4 0 0.7 0.1 0.7 0.1 1.4 0.1 2.2 0.1h0.4c0.7 0 1.5 0 2.2-0.1 0.2 0 0.4 0 0.7-0.1 0.7-0.1 1.5-0.2 2.2-0.3h0.1c5.8-1 11.1-3.4 15.6-7.2 0 0 0.1 0 0.1-0.1 0.4-0.3 0.7-0.6 1-0.9l0.5-0.5 0.6-0.6c0.9-1 1.8-2 2.6-3L726 701c5.8-7.6 8-17.2 6-26.5l-86.3-388.6 126.8-167.4c7.4-9.8 8.6-22.7 3.1-33.7zM429.1 246.2l-87.1-115h340l-87.1 115H429.1z m83 626.1L358.5 673.7l80.4-363.2h146.2l80.6 363.2-153.6 198.6z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-report-->
<view a:if="{{name === 'line-report'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M800 64H224c-52.935 0-96 43.065-96 96v704c0 52.935 43.065 96 96 96h576c52.935 0 96-43.065 96-96V160c0-52.935-43.065-96-96-96z m32 800c0 17.645-14.355 32-32 32H224c-17.645 0-32-14.355-32-32V160c0-17.645 14.355-32 32-32h576c17.645 0 32 14.355 32 32v704z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M672 256H352c-17.673 0-32 14.327-32 32s14.327 32 32 32h320c17.673 0 32-14.327 32-32s-14.327-32-32-32zM544 480H352c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h192c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-service-->
<view a:if="{{name === 'line-service'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M320 96h-64c-88.225 0-160 71.776-160 160v64c0 88.224 71.775 160 160 160h192c17.673 0 32-14.327 32-32V256c0-88.224-71.775-160-160-160z m96 224v96H256c-52.935 0-96-43.065-96-96v-64c0-52.935 43.065-96 96-96h64c52.935 0 96 43.065 96 96v64zM768 96h-64c-88.225 0-160 71.776-160 160v192c0 17.673 14.327 32 32 32h192c88.225 0 160-71.776 160-160v-64c0-88.224-71.775-160-160-160z m96 224c0 52.935-43.065 96-96 96H608V256c0-52.935 43.065-96 96-96h64c52.935 0 96 43.065 96 96v64zM448 544H256c-88.225 0-160 71.775-160 160v64c0 88.225 71.775 160 160 160h64c88.225 0 160-71.775 160-160V576c0-17.673-14.327-32-32-32z m-32 160v64c0 52.935-43.065 96-96 96h-64c-52.935 0-96-43.065-96-96v-64c0-52.935 43.065-96 96-96h160v96zM768 544H576c-17.673 0-32 14.327-32 32v192c0 88.225 71.775 160 160 160h64c88.225 0 160-71.775 160-160v-64c0-88.225-71.775-160-160-160z m96 224c0 52.935-43.065 96-96 96h-64c-52.935 0-96-43.065-96-96V608h160c52.935 0 96 43.065 96 96v64z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-phone-->
<view a:if="{{name === 'line-phone'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M768 64H256c-52.935 0-96 43.065-96 96v704c0 52.935 43.065 96 96 96h512c52.935 0 96-43.065 96-96V160c0-52.935-43.065-96-96-96z m32 800c0 17.645-14.355 32-32 32H256c-17.645 0-32-14.355-32-32V160c0-17.645 14.355-32 32-32h512c17.645 0 32 14.355 32 32v704z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M608 192H416c-17.673 0-32 14.327-32 32s14.327 32 32 32h192c17.673 0 32-14.327 32-32s-14.327-32-32-32zM512 736c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-shoppingcart-->
<view a:if="{{name === 'line-shoppingcart'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M945.933 289.588c-13.157-17.293-33.16-27.208-54.896-27.208H156.77l-24.327-145.632c-3.13-18.731-20.727-31.368-39.654-28.285-18.776 3.127-31.444 20.864-28.315 39.612L165.77 734.499C171.338 767.816 199.922 792 233.737 792h547.795c32.486 0 60.733-22.763 67.731-54.333l108.161-387.972c5.837-20.907 1.648-42.814-11.491-60.107zM782.592 720.239c-0.236 0.84-0.892 2.075-1.06 2.924H233.738l-65.461-391.947h722.76L782.592 720.239zM320 840c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zM704 840c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M384 664h256c17.673 0 32-14.327 32-32s-14.327-32-32-32H384c-17.673 0-32 14.327-32 32s14.327 32 32 32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-shop-->
<view a:if="{{name === 'line-shop'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M928 342.01c0.06-12.75-5-27.04-55.02-138.29-11.91-26.48-23.19-51.57-30.46-68.74C832.49 111.3 809.36 96 783.61 96H242.2c-25.6 0-48.66 15.16-58.74 38.59-7.39 17.17-18.94 42.37-31.11 68.98C101.19 315.31 96 329.65 96 342.48c0.06 53.18 22.84 102.76 61.4 137.23V832c0 52.935 43.065 96 96 96h517.215c52.935 0 96-43.065 96-96h-0.015V479.32c38.61-34.55 61.41-84.28 61.4-137.31z m-717.46-111.8c12.41-27.11 24.16-52.79 31.66-70.21h541.41c7.41 17.49 18.88 43.02 30.99 69.96 17.92 39.86 44.63 99.28 49.36 114.8-0.84 36.22-17.91 69.73-45.81 91.73a31.844 31.844 0 0 0-5.08 3.77c-3.76 2.65-7.67 5.12-11.77 7.35-15.95 8.75-35.33 13.23-57.44 13.34-35.72 0-69.25-15.53-91.99-42.62-12.2-14.5-36.89-14.48-49.03 0.01-22.54 26.85-55.62 42.41-90.34 42.69-35.46-0.21-68.77-15.75-91.4-42.6a31.971 31.971 0 0 0-24.47-11.39h-0.03a32 32 0 0 0-24.47 11.42c-22.78 27.13-56.24 42.75-91.71 42.85-21.41 0-40.17-4.13-55.8-12.28-0.28-0.17-0.58-0.32-0.86-0.48-3.99-2.13-7.82-4.47-11.5-6.99-2.3-2.36-4.98-4.37-7.92-5.92a120.182 120.182 0 0 1-44.29-90.17c4.92-15.71 32.19-75.28 50.49-115.26zM770.615 864H253.4c-17.645 0-32-14.355-32-32V516.67c18.17 5.73 37.94 8.64 59.11 8.64 21.42-0.07 42.35-3.81 61.97-10.85a183.46 183.46 0 0 0 54.2-30.69c32.3 26.26 72.96 41 115.87 41.26 42.31-0.33 82.69-15.11 114.82-41.37 32.53 26.54 73.52 41.29 116.65 41.29 20.97-0.1 40.57-3.03 58.58-8.7V832h0.015c0 17.645-14.355 32-32 32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M512 640c-17.673 0-32 14.327-32 32v96c0 17.673 14.327 32 32 32 17.674 0 32-14.327 32-32v-96c0-17.673-14.327-32-32-32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-see-->
<view a:if="{{name === 'line-see'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M921.7 394.2c-23.5-34.2-56.7-64.6-98.8-90.5C739.4 252.3 629 224 512 224s-227.4 28.3-310.9 79.7c-42 25.9-75.3 56.3-98.8 90.5C76.9 431.2 64 470.8 64 512s12.9 80.8 38.3 117.8c23.5 34.2 56.7 64.6 98.8 90.5C284.6 771.7 395 800 512 800s227.4-28.3 310.9-79.7c42-25.9 75.3-56.3 98.8-90.5 25.4-37 38.3-76.6 38.3-117.8s-12.9-80.8-38.3-117.8zM789.4 665.8c-35.5 21.8-77.1 39.1-123.5 51.1C617.2 729.6 565.4 736 512 736s-105.2-6.4-153.9-19.1c-46.5-12.1-88-29.3-123.5-51.1C165.9 623.5 128 568.8 128 512s37.9-111.5 106.6-153.8c35.5-21.8 77.1-39.1 123.5-51.1C406.8 294.4 458.6 288 512 288s105.2 6.4 153.9 19.1c46.5 12.1 88 29.3 123.5 51.1C858.1 400.5 896 455.2 896 512s-37.9 111.5-106.6 153.8z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M512 384c-70.6 0-128 57.4-128 128s57.4 128 128 128 128-57.4 128-128-57.4-128-128-128z m0 192c-35.3 0-64-28.7-64-64s28.7-64 64-64 64 28.7 64 64-28.7 64-64 64z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-system-->
<view a:if="{{name === 'line-system'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M832 97H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V193c0-52.935-43.065-96-96-96z m32 736c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V193c0-17.645 14.355-32 32-32h640c17.645 0 32 14.355 32 32v640z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M768 321H544a32.36 32.36 0 0 0-3.986 0.256C525.858 265.981 475.625 225 416 225s-109.857 40.981-124.014 96.256A32.165 32.165 0 0 0 288 321h-32c-17.673 0-32 14.327-32 32s14.327 32 32 32h32a32.36 32.36 0 0 0 3.986-0.256C306.142 440.019 356.375 481 416 481s109.857-40.981 124.014-96.256c1.307 0.163 2.635 0.256 3.986 0.256h224c17.673 0 32-14.327 32-32s-14.327-32-32-32z m-352 96c-35.29 0-64-28.71-64-64s28.71-64 64-64 64 28.71 64 64-28.71 64-64 64zM768 641h-32a32.36 32.36 0 0 0-3.986 0.256C717.858 585.981 667.625 545 608 545s-109.857 40.981-124.014 96.256A32.165 32.165 0 0 0 480 641H288c-17.673 0-32 14.327-32 32s14.327 32 32 32h192a32.36 32.36 0 0 0 3.986-0.256C498.142 760.019 548.375 801 608 801s109.857-40.981 124.014-96.256c1.307 0.163 2.635 0.256 3.986 0.256h32c17.673 0 32-14.327 32-32s-14.327-32-32-32z m-160 96c-35.29 0-64-28.71-64-64s28.71-64 64-64 64 28.71 64 64-28.71 64-64 64z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-shipment-->
<view a:if="{{name === 'line-shipment'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M832 96H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m-192 64v32c0 17.645-14.355 32-32 32H416c-17.645 0-32-14.355-32-32v-32h256z m224 672c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h128v32c0 52.935 43.065 96 96 96h192c52.935 0 96-43.065 96-96v-32h128c17.645 0 32 14.355 32 32v640z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M672 672H352c-17.673 0-32 14.327-32 32s14.327 32 32 32h320c17.673 0 32-14.327 32-32s-14.327-32-32-32zM703.965 546.608a32.074 32.074 0 0 0-0.02-0.438c-0.02-0.341-0.047-0.681-0.078-1.021-0.02-0.223-0.039-0.445-0.063-0.667-0.027-0.24-0.059-0.48-0.091-0.719a28.624 28.624 0 0 0-0.134-0.932c-0.022-0.132-0.047-0.264-0.071-0.396a31.747 31.747 0 0 0-0.232-1.207l-0.018-0.076a31.77 31.77 0 0 0-2.494-7.182l-0.023-0.044c-0.211-0.43-0.43-0.856-0.659-1.275-0.132-0.242-0.275-0.48-0.413-0.719-0.109-0.188-0.214-0.379-0.327-0.565a31.793 31.793 0 0 0-5.815-7.043l-0.014-0.013a32.408 32.408 0 0 0-0.967-0.837c-0.09-0.076-0.179-0.153-0.269-0.227a33.464 33.464 0 0 0-0.774-0.609c-0.18-0.139-0.359-0.277-0.542-0.412-0.19-0.139-0.384-0.275-0.578-0.411a32.279 32.279 0 0 0-0.812-0.555 27.471 27.471 0 0 0-0.402-0.257 32.455 32.455 0 0 0-1.04-0.636c-0.044-0.025-0.085-0.053-0.129-0.079l-166.277-96c-15.303-8.837-34.876-3.593-43.712 11.713-8.836 15.305-3.592 34.876 11.713 43.713L552.574 516H352c-17.673 0-32 14.327-32 32s14.327 32 32 32h319.817c0.051 0 0.102 0.005 0.153 0.005 0.194 0 0.387-0.015 0.581-0.019 0.426-0.007 0.851-0.017 1.273-0.041 0.22-0.013 0.439-0.033 0.658-0.05 0.451-0.035 0.9-0.078 1.346-0.131 0.163-0.02 0.326-0.04 0.489-0.062a32.327 32.327 0 0 0 1.775-0.288l0.047-0.008a31.83 31.83 0 0 0 3.956-1.04l0.036-0.012c0.64-0.214 1.272-0.446 1.894-0.698l0.03-0.012a31.823 31.823 0 0 0 3.53-1.697c0.155-0.086 0.307-0.176 0.46-0.265a31.13 31.13 0 0 0 1.108-0.674c0.198-0.126 0.397-0.25 0.592-0.38 0.364-0.243 0.72-0.495 1.072-0.753 0.351-0.255 0.695-0.52 1.036-0.79 0.219-0.174 0.439-0.346 0.653-0.525a32.23 32.23 0 0 0 1.355-1.204c0.256-0.239 0.512-0.477 0.761-0.725a32.12 32.12 0 0 0 1.094-1.151c0.187-0.207 0.368-0.422 0.55-0.635a32.659 32.659 0 0 0 1.166-1.435 32.105 32.105 0 0 0 2.216-3.312c0.019-0.033 0.041-0.064 0.06-0.097 0.029-0.05 0.054-0.102 0.083-0.153 0.22-0.385 0.432-0.774 0.636-1.168l0.129-0.252c0.197-0.39 0.386-0.784 0.568-1.182l0.136-0.3a31.49 31.49 0 0 0 0.995-2.533c0.063-0.183 0.126-0.365 0.186-0.549 0.093-0.286 0.179-0.574 0.264-0.863 0.071-0.24 0.14-0.481 0.205-0.722 0.062-0.231 0.119-0.464 0.176-0.698 0.074-0.303 0.145-0.607 0.21-0.911 0.038-0.177 0.072-0.356 0.107-0.534 0.071-0.363 0.137-0.727 0.195-1.091 0.021-0.131 0.04-0.263 0.059-0.394 0.061-0.414 0.114-0.828 0.158-1.243l0.029-0.292c0.044-0.448 0.078-0.896 0.102-1.344 0.004-0.082 0.009-0.165 0.012-0.248 0.021-0.456 0.033-0.912 0.035-1.368 0-0.051 0.004-0.102 0.004-0.153 0-0.045-0.003-0.088-0.003-0.133 0.001-0.423-0.01-0.843-0.029-1.262z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-ranking-->
<view a:if="{{name === 'line-ranking'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M480 800h64c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96h-64c-52.935 0-96 43.065-96 96v512c0 52.935 43.065 96 96 96z m-32-608c0-17.645 14.355-32 32-32h64c17.645 0 32 14.355 32 32v512c0 17.645-14.355 32-32 32h-64c-17.645 0-32-14.355-32-32V192zM160 800h64c52.935 0 96-43.065 96-96V352c0-52.935-43.065-96-96-96h-64c-52.935 0-96 43.065-96 96v352c0 52.935 43.065 96 96 96z m-32-448c0-17.645 14.355-32 32-32h64c17.645 0 32 14.355 32 32v352c0 17.645-14.355 32-32 32h-64c-17.645 0-32-14.355-32-32V352zM864 416h-64c-52.935 0-96 43.065-96 96v192c0 52.935 43.065 96 96 96h64c52.935 0 96-43.065 96-96V512c0-52.935-43.065-96-96-96z m32 288c0 17.645-14.355 32-32 32h-64c-17.645 0-32-14.355-32-32V512c0-17.645 14.355-32 32-32h64c17.645 0 32 14.355 32 32v192zM928 864H96c-17.673 0-32 14.327-32 32s14.327 32 32 32h832c17.673 0 32-14.327 32-32s-14.327-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-template-->
<view a:if="{{name === 'line-template'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M832 96H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m-640 64h640c17.645 0 32 14.355 32 32v160H160V192c0-17.645 14.355-32 32-32z m-32 672V416h192v448H192c-17.645 0-32-14.355-32-32z m672 32H416V416h448v416c0 17.645-14.355 32-32 32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-time-->
<view a:if="{{name === 'line-time'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M924.781 337.608c-22.566-53.352-54.864-101.259-95.997-142.393-41.133-41.133-89.041-73.431-142.393-95.997C631.14 75.85 572.466 64 512 64S392.86 75.85 337.608 99.219c-53.352 22.565-101.26 54.864-142.393 95.997s-73.431 89.041-95.997 142.392C75.85 392.861 64 451.535 64 512s11.85 119.139 35.219 174.391c22.566 53.352 54.864 101.26 95.997 142.393s89.041 73.431 142.393 95.997C392.86 948.15 451.534 960 512 960s119.14-11.85 174.392-35.219c53.352-22.566 101.26-54.864 142.393-95.997 41.133-41.134 73.431-89.041 95.997-142.393C948.15 631.14 960 572.465 960 512s-11.85-119.14-35.219-174.392zM783.529 783.529C711.001 856.057 614.57 896 512 896s-199.001-39.943-271.529-112.471S128 614.57 128 512s39.943-199 112.471-271.529C312.999 167.943 409.43 128 512 128s199.001 39.943 271.529 112.471S896 409.43 896 512s-39.943 199.001-112.471 271.529z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M544 498.745V224c0-17.673-14.327-32-32-32s-32 14.327-32 32v288l0.002 0.085c0.001 0.498 0.013 0.997 0.038 1.495 0.013 0.26 0.037 0.517 0.056 0.775 0.019 0.261 0.034 0.523 0.06 0.784 0.031 0.32 0.074 0.636 0.114 0.953 0.025 0.196 0.046 0.392 0.075 0.587 0.05 0.342 0.111 0.68 0.172 1.018 0.031 0.172 0.058 0.344 0.092 0.516 0.067 0.342 0.145 0.681 0.223 1.019 0.039 0.169 0.075 0.338 0.117 0.506 0.081 0.327 0.172 0.651 0.263 0.974 0.051 0.182 0.1 0.364 0.155 0.544 0.091 0.3 0.189 0.596 0.288 0.893 0.068 0.205 0.135 0.41 0.208 0.614 0.095 0.265 0.196 0.526 0.297 0.788 0.09 0.234 0.18 0.467 0.276 0.699 0.094 0.227 0.194 0.45 0.293 0.674 0.115 0.261 0.231 0.523 0.353 0.782 0.09 0.19 0.186 0.378 0.28 0.566 0.142 0.285 0.285 0.569 0.436 0.851 0.086 0.159 0.176 0.316 0.264 0.474 0.168 0.3 0.337 0.6 0.515 0.896 0.085 0.14 0.174 0.278 0.26 0.416 0.189 0.303 0.379 0.605 0.579 0.902 0.091 0.135 0.186 0.266 0.279 0.4 0.203 0.292 0.406 0.584 0.619 0.871 0.108 0.146 0.222 0.286 0.333 0.43 0.205 0.266 0.409 0.532 0.623 0.793 0.149 0.181 0.306 0.356 0.458 0.534 0.187 0.217 0.369 0.437 0.563 0.65 0.274 0.302 0.557 0.594 0.841 0.885 0.082 0.084 0.159 0.171 0.242 0.255L693.02 738.275c6.249 6.249 14.438 9.373 22.628 9.373s16.379-3.124 22.627-9.373c12.497-12.496 12.497-32.758 0-45.255L544 498.745z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-send-->
<view a:if="{{name === 'line-send'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M957.203 525.079l0.067-0.149 0.043-0.096a32.101 32.101 0 0 0 0.509-1.233l0.024-0.06 0.076-0.192 0.021-0.052c0.143-0.378 0.275-0.76 0.404-1.145l0.019-0.057c0.016-0.048 0.033-0.095 0.048-0.143l0.072-0.213a31.092 31.092 0 0 0 0.335-1.136c0.042-0.151 0.088-0.302 0.128-0.453 0.067-0.256 0.126-0.515 0.187-0.773l0.025-0.103 0.038-0.156c0.044-0.194 0.091-0.387 0.131-0.581 0.03-0.145 0.055-0.291 0.083-0.436 0.021-0.106 0.041-0.213 0.06-0.319l0.035-0.185c0.041-0.229 0.082-0.458 0.118-0.688l0.023-0.164c0.021-0.142 0.04-0.285 0.059-0.427l0.039-0.283c0.032-0.254 0.063-0.508 0.089-0.761v-0.003c0.014-0.133 0.023-0.266 0.035-0.399 0.013-0.145 0.027-0.289 0.038-0.434 0.025-0.335 0.044-0.671 0.059-1.006 0.009-0.205 0.019-0.409 0.024-0.614 0.007-0.263 0.008-0.525 0.008-0.788l0.001-0.028-0.001-0.028c0-0.263-0.002-0.525-0.008-0.788-0.005-0.205-0.015-0.41-0.024-0.614a30.828 30.828 0 0 0-0.059-1.006c-0.011-0.145-0.025-0.289-0.038-0.433a33.12 33.12 0 0 0-0.124-1.165l-0.039-0.282c-0.02-0.144-0.038-0.288-0.06-0.432l-0.022-0.159c-0.036-0.23-0.077-0.46-0.118-0.69l-0.034-0.181a37.8 37.8 0 0 0-0.062-0.328c-0.028-0.143-0.052-0.286-0.081-0.428-0.041-0.195-0.087-0.39-0.132-0.585l-0.036-0.15-0.028-0.114c-0.06-0.255-0.118-0.511-0.184-0.764-0.04-0.153-0.087-0.304-0.129-0.457l-0.035-0.122c-0.096-0.338-0.192-0.676-0.299-1.01-0.024-0.075-0.051-0.149-0.076-0.224l-0.045-0.134-0.019-0.057a31.528 31.528 0 0 0-0.403-1.143l-0.025-0.063-0.072-0.183-0.024-0.06a31.263 31.263 0 0 0-0.497-1.209l-0.015-0.033-0.04-0.088-0.066-0.147c-0.18-0.401-0.367-0.799-0.562-1.191l-0.019-0.042-0.002-0.003c-0.045-0.089-0.092-0.177-0.138-0.266a31.14 31.14 0 0 0-0.526-0.988c-0.095-0.171-0.195-0.34-0.293-0.51a29.49 29.49 0 0 0-0.446-0.748c-0.066-0.107-0.13-0.215-0.197-0.321-0.039-0.062-0.082-0.122-0.122-0.184-0.046-0.072-0.095-0.143-0.143-0.215-0.111-0.168-0.22-0.337-0.334-0.503-0.092-0.135-0.182-0.271-0.276-0.405-0.093-0.133-0.192-0.26-0.287-0.391l-0.078-0.107c-0.071-0.096-0.14-0.193-0.212-0.288-0.118-0.157-0.233-0.315-0.354-0.469-0.131-0.168-0.267-0.329-0.401-0.494l-0.051-0.062-0.112-0.138c-0.141-0.171-0.28-0.344-0.425-0.512-0.161-0.186-0.327-0.365-0.491-0.547l-0.016-0.018-0.053-0.059c-0.162-0.178-0.321-0.357-0.487-0.531-0.176-0.185-0.359-0.364-0.539-0.545l-0.034-0.034-0.002-0.002c-0.175-0.174-0.347-0.351-0.526-0.521-0.179-0.171-0.364-0.335-0.547-0.502l-0.044-0.039a4.652 4.652 0 0 0-0.076-0.068c-0.162-0.146-0.322-0.295-0.488-0.438-0.173-0.15-0.351-0.293-0.527-0.439l-0.124-0.101-0.128-0.104c-0.142-0.115-0.281-0.233-0.426-0.346-0.159-0.124-0.321-0.243-0.482-0.364-0.094-0.071-0.19-0.14-0.286-0.21l-0.175-0.129c-0.105-0.076-0.207-0.156-0.314-0.231-0.137-0.096-0.276-0.189-0.414-0.283-0.167-0.114-0.336-0.224-0.505-0.335-0.106-0.07-0.211-0.142-0.318-0.211-0.023-0.015-0.045-0.031-0.069-0.045-0.109-0.069-0.22-0.136-0.331-0.204a29.946 29.946 0 0 0-0.754-0.449c-0.167-0.097-0.334-0.195-0.503-0.289a32.85 32.85 0 0 0-0.999-0.532l-0.273-0.141-0.075-0.038c-0.146-0.073-0.29-0.148-0.437-0.219L110.311 67.378c-0.179-0.09-0.361-0.168-0.541-0.254-0.279-0.133-0.557-0.269-0.84-0.394-0.286-0.127-0.575-0.242-0.863-0.359-0.189-0.077-0.377-0.158-0.568-0.232a33.548 33.548 0 0 0-1.032-0.373c-0.139-0.048-0.276-0.099-0.415-0.146a31.395 31.395 0 0 0-1.133-0.349c-0.113-0.033-0.225-0.068-0.339-0.1-0.39-0.109-0.781-0.207-1.173-0.301-0.11-0.026-0.22-0.055-0.331-0.08a31.332 31.332 0 0 0-1.159-0.238c-0.127-0.024-0.253-0.049-0.38-0.072-0.367-0.065-0.733-0.12-1.1-0.172-0.156-0.022-0.311-0.045-0.467-0.065a31.13 31.13 0 0 0-1.005-0.107c-0.196-0.018-0.392-0.037-0.589-0.052a32.417 32.417 0 0 0-0.883-0.051c-0.24-0.011-0.481-0.022-0.722-0.028-0.252-0.006-0.503-0.006-0.755-0.006-0.283 0-0.565 0.001-0.849 0.008-0.212 0.006-0.424 0.016-0.636 0.026-0.318 0.015-0.635 0.031-0.953 0.056-0.183 0.014-0.364 0.032-0.546 0.05-0.34 0.032-0.679 0.066-1.019 0.109-0.169 0.021-0.336 0.047-0.504 0.071-0.346 0.049-0.691 0.101-1.037 0.161-0.173 0.03-0.344 0.066-0.516 0.099-0.333 0.064-0.666 0.129-0.999 0.205-0.203 0.046-0.403 0.099-0.605 0.148-0.298 0.074-0.597 0.146-0.894 0.229-0.26 0.072-0.517 0.154-0.774 0.232-0.238 0.073-0.476 0.141-0.713 0.219-0.38 0.125-0.756 0.263-1.131 0.402-0.109 0.041-0.219 0.075-0.328 0.117l-0.026 0.011c-0.503 0.194-1 0.399-1.493 0.618l-0.085 0.04c-0.45 0.201-0.895 0.412-1.335 0.634-0.201 0.101-0.395 0.211-0.593 0.315-0.263 0.139-0.527 0.276-0.786 0.423-0.241 0.136-0.474 0.281-0.71 0.423-0.209 0.126-0.421 0.248-0.627 0.379-0.248 0.157-0.489 0.321-0.731 0.484-0.191 0.128-0.383 0.254-0.572 0.386-0.237 0.167-0.467 0.34-0.698 0.513-0.188 0.141-0.378 0.279-0.564 0.423-0.218 0.17-0.429 0.347-0.642 0.522-0.193 0.159-0.387 0.315-0.576 0.479-0.195 0.168-0.383 0.342-0.573 0.514-0.2 0.181-0.401 0.361-0.596 0.547-0.17 0.163-0.335 0.33-0.501 0.497-0.206 0.205-0.412 0.409-0.613 0.621-0.15 0.159-0.295 0.322-0.442 0.484-0.205 0.226-0.411 0.45-0.611 0.682-0.139 0.162-0.271 0.328-0.407 0.492-0.196 0.238-0.394 0.474-0.584 0.719-0.138 0.178-0.269 0.36-0.403 0.541-0.176 0.237-0.354 0.472-0.525 0.714-0.154 0.22-0.299 0.445-0.448 0.669-0.14 0.21-0.283 0.417-0.419 0.632-0.231 0.366-0.451 0.739-0.667 1.114-0.046 0.079-0.095 0.155-0.14 0.235-0.263 0.465-0.513 0.937-0.753 1.415l-0.016 0.03c-0.065 0.13-0.121 0.261-0.184 0.391-0.161 0.332-0.321 0.665-0.47 1.003-0.111 0.25-0.21 0.503-0.314 0.755-0.093 0.226-0.19 0.451-0.278 0.68-0.12 0.312-0.23 0.626-0.34 0.94-0.059 0.17-0.122 0.339-0.179 0.51-0.115 0.348-0.22 0.697-0.323 1.047-0.042 0.143-0.087 0.286-0.127 0.43-0.101 0.362-0.192 0.725-0.279 1.089-0.034 0.139-0.069 0.278-0.101 0.418-0.081 0.358-0.153 0.718-0.222 1.077-0.03 0.155-0.061 0.309-0.088 0.464-0.059 0.338-0.11 0.675-0.158 1.014-0.027 0.186-0.054 0.372-0.077 0.559-0.038 0.305-0.069 0.61-0.098 0.915-0.022 0.226-0.043 0.451-0.06 0.677-0.019 0.265-0.033 0.53-0.046 0.795-0.013 0.27-0.025 0.541-0.032 0.812-0.005 0.221-0.005 0.441-0.006 0.661-0.001 0.313 0.001 0.627 0.009 0.941 0.005 0.181 0.014 0.362 0.022 0.543 0.015 0.347 0.034 0.694 0.061 1.042 0.012 0.152 0.027 0.304 0.041 0.456 0.034 0.369 0.071 0.738 0.118 1.108 0.017 0.136 0.038 0.271 0.057 0.407 0.053 0.377 0.109 0.753 0.176 1.13 0.025 0.14 0.054 0.279 0.081 0.419 0.07 0.365 0.142 0.729 0.224 1.094 0.038 0.167 0.082 0.332 0.122 0.498 0.081 0.332 0.162 0.664 0.254 0.995 0.063 0.225 0.134 0.448 0.201 0.671 0.082 0.271 0.16 0.541 0.249 0.811 0.112 0.338 0.235 0.672 0.358 1.007 0.054 0.147 0.101 0.294 0.157 0.441l0.029 0.075 0.001 0.003L221.715 512 66.162 916.436l-0.001 0.001-0.029 0.076c-0.057 0.148-0.104 0.297-0.159 0.446-0.122 0.333-0.245 0.665-0.356 1.001-0.09 0.271-0.168 0.544-0.25 0.816-0.067 0.222-0.138 0.443-0.2 0.667-0.093 0.332-0.174 0.665-0.255 0.998-0.04 0.165-0.084 0.329-0.122 0.495-0.083 0.365-0.155 0.73-0.225 1.096-0.026 0.139-0.056 0.277-0.08 0.417-0.067 0.377-0.123 0.754-0.176 1.131-0.019 0.135-0.04 0.27-0.057 0.406-0.047 0.37-0.085 0.74-0.119 1.109-0.014 0.152-0.03 0.303-0.041 0.455-0.027 0.348-0.045 0.695-0.061 1.043-0.008 0.181-0.017 0.361-0.022 0.543-0.008 0.314-0.01 0.628-0.009 0.941a27.336 27.336 0 0 0 0.038 1.472c0.013 0.265 0.026 0.53 0.046 0.795 0.017 0.226 0.038 0.451 0.06 0.677 0.029 0.305 0.06 0.611 0.098 0.916 0.023 0.187 0.051 0.372 0.077 0.559 0.048 0.338 0.099 0.677 0.158 1.014 0.027 0.155 0.058 0.309 0.088 0.464 0.068 0.36 0.14 0.719 0.222 1.077 0.032 0.14 0.067 0.279 0.101 0.419 0.088 0.363 0.178 0.726 0.279 1.088 0.04 0.145 0.085 0.288 0.128 0.432 0.102 0.349 0.207 0.697 0.322 1.044 0.057 0.173 0.121 0.344 0.181 0.516 0.109 0.311 0.217 0.623 0.337 0.933 0.09 0.233 0.188 0.462 0.283 0.693 0.102 0.247 0.2 0.495 0.308 0.741 0.154 0.349 0.319 0.692 0.485 1.035 0.058 0.12 0.109 0.242 0.169 0.361l0.012 0.023a32.19 32.19 0 0 0 1.251 2.251c0.321 0.53 0.654 1.053 1.006 1.565 0.04 0.058 0.076 0.118 0.116 0.175 0.373 0.535 0.768 1.055 1.174 1.568 0.124 0.157 0.251 0.311 0.378 0.466 0.34 0.413 0.691 0.819 1.053 1.216 0.09 0.099 0.177 0.202 0.268 0.3 0.44 0.471 0.898 0.927 1.368 1.372 0.127 0.121 0.258 0.237 0.387 0.355 0.388 0.355 0.785 0.701 1.191 1.038 0.117 0.097 0.232 0.196 0.351 0.291a31.98 31.98 0 0 0 1.59 1.202c0.067 0.047 0.136 0.091 0.203 0.137a31.648 31.648 0 0 0 3.342 2.026c0.108 0.057 0.214 0.118 0.323 0.174 0.577 0.295 1.167 0.574 1.768 0.837 0.155 0.068 0.312 0.128 0.468 0.194 0.295 0.123 0.586 0.252 0.887 0.367 0.179 0.069 0.359 0.127 0.538 0.193 0.157 0.057 0.314 0.115 0.472 0.169 1.275 0.443 2.559 0.799 3.847 1.073l0.265 0.055c1.355 0.277 2.714 0.463 4.069 0.561l0.257 0.017c0.678 0.043 1.356 0.073 2.031 0.073 0.037 0 0.074-0.004 0.112-0.004a32.5 32.5 0 0 0 2.067-0.078c0.201-0.014 0.4-0.034 0.601-0.051a31.552 31.552 0 0 0 1.672-0.19c0.178-0.025 0.356-0.048 0.533-0.077a32.348 32.348 0 0 0 2.118-0.402l0.011-0.002a31.402 31.402 0 0 0 2.197-0.589c0.12-0.037 0.238-0.079 0.358-0.117a31.687 31.687 0 0 0 3.736-1.453c0.204-0.094 0.409-0.185 0.61-0.283 0.101-0.049 0.203-0.09 0.303-0.141l831.485-415.743c0.128-0.061 0.253-0.127 0.379-0.19l0.135-0.067 0.083-0.044 0.166-0.085 0.239-0.122c0.287-0.15 0.571-0.303 0.852-0.461 0.108-0.06 0.214-0.123 0.321-0.185 0.328-0.189 0.653-0.383 0.974-0.583a33.222 33.222 0 0 0 1.224-0.804c0.104-0.072 0.212-0.138 0.316-0.211 0.146-0.103 0.285-0.213 0.429-0.318a42.922 42.922 0 0 0 1.1-0.838c0.129-0.104 0.257-0.209 0.385-0.315 0.025-0.02 0.048-0.042 0.072-0.063 0.34-0.283 0.674-0.572 1.001-0.867l0.098-0.09 0.029-0.026c0.128-0.117 0.258-0.231 0.384-0.351a30.701 30.701 0 0 0 0.665-0.653c0.245-0.245 0.49-0.49 0.727-0.743 0.073-0.078 0.142-0.16 0.215-0.239l0.083-0.093 0.039-0.044c0.324-0.357 0.639-0.721 0.945-1.091l0.082-0.099c0.097-0.119 0.194-0.237 0.29-0.357 0.185-0.233 0.367-0.468 0.546-0.706 0.069-0.091 0.135-0.184 0.203-0.276 0.221-0.302 0.44-0.605 0.65-0.914 0.111-0.163 0.219-0.329 0.327-0.494a34.586 34.586 0 0 0 0.728-1.156l0.071-0.123 0.109-0.189c0.1-0.172 0.201-0.344 0.297-0.517 0.181-0.324 0.354-0.653 0.523-0.983 0.046-0.091 0.095-0.18 0.14-0.271v-0.001l0.019-0.04c0.193-0.394 0.379-0.792 0.559-1.193zM277.978 480L155.485 161.52 792.445 480H277.978z m0 64h514.468L155.485 862.48 277.978 544z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-news-->
<view a:if="{{name === 'line-news'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M864 112H160c-52.935 0-96 43.065-96 96v480c0 52.935 43.065 96 96 96h210.746l118.627 118.627C495.621 908.876 503.811 912 512 912c8.189 0 16.379-3.124 22.627-9.373L653.254 784H864c52.935 0 96-43.065 96-96V208c0-52.935-43.065-96-96-96z m32 576c0 17.645-14.355 32-32 32H640.02c-0.538 0-1.076 0.014-1.613 0.04-0.212 0.01-0.421 0.031-0.631 0.045-0.309 0.021-0.619 0.04-0.927 0.07-0.289 0.028-0.575 0.067-0.861 0.103-0.227 0.029-0.454 0.053-0.681 0.087-0.319 0.047-0.634 0.104-0.949 0.16-0.194 0.035-0.389 0.066-0.583 0.104-0.325 0.064-0.647 0.138-0.969 0.212-0.185 0.042-0.37 0.082-0.554 0.128-0.315 0.078-0.627 0.165-0.938 0.253-0.193 0.054-0.387 0.106-0.579 0.164-0.29 0.088-0.577 0.183-0.864 0.279-0.214 0.071-0.428 0.141-0.641 0.217-0.258 0.092-0.512 0.191-0.767 0.289-0.24 0.092-0.479 0.184-0.717 0.283-0.222 0.092-0.441 0.19-0.661 0.287-0.265 0.117-0.529 0.233-0.791 0.358-0.19 0.09-0.377 0.186-0.565 0.28-0.284 0.141-0.567 0.283-0.847 0.434-0.164 0.088-0.326 0.181-0.489 0.273-0.294 0.164-0.587 0.33-0.877 0.504-0.15 0.091-0.297 0.186-0.446 0.279-0.292 0.182-0.583 0.365-0.871 0.558-0.151 0.101-0.298 0.208-0.447 0.312-0.275 0.192-0.551 0.383-0.821 0.584-0.173 0.128-0.34 0.263-0.51 0.395-0.238 0.185-0.478 0.367-0.712 0.559-0.233 0.192-0.459 0.392-0.687 0.59-0.165 0.143-0.333 0.281-0.495 0.429-0.399 0.361-0.789 0.732-1.169 1.112L512 834.746 406.632 729.378a33.24 33.24 0 0 0-1.154-1.098c-0.178-0.162-0.362-0.314-0.544-0.471-0.212-0.184-0.422-0.371-0.64-0.549-0.242-0.199-0.489-0.387-0.734-0.577-0.163-0.126-0.323-0.255-0.488-0.378-0.275-0.205-0.555-0.399-0.835-0.594-0.145-0.101-0.288-0.204-0.434-0.303-0.29-0.195-0.585-0.38-0.88-0.564-0.146-0.091-0.289-0.184-0.437-0.273a30.054 30.054 0 0 0-0.884-0.508c-0.161-0.09-0.32-0.182-0.482-0.269a32.5 32.5 0 0 0-0.851-0.436c-0.187-0.093-0.373-0.188-0.562-0.278a31.135 31.135 0 0 0-0.793-0.359c-0.219-0.097-0.437-0.194-0.659-0.286-0.238-0.099-0.479-0.191-0.719-0.284-0.254-0.098-0.508-0.197-0.765-0.289-0.213-0.076-0.428-0.146-0.642-0.217a32.129 32.129 0 0 0-0.863-0.279c-0.192-0.058-0.386-0.11-0.579-0.164a29.355 29.355 0 0 0-0.937-0.253c-0.185-0.046-0.37-0.086-0.555-0.128a30.811 30.811 0 0 0-0.968-0.212c-0.194-0.038-0.389-0.07-0.583-0.104-0.315-0.056-0.63-0.113-0.949-0.16-0.227-0.033-0.454-0.058-0.681-0.087-0.287-0.036-0.572-0.075-0.861-0.103-0.309-0.03-0.618-0.049-0.927-0.07-0.211-0.014-0.419-0.035-0.631-0.045a30.98 30.98 0 0 0-1.613-0.04H160c-17.645 0-32-14.355-32-32V208c0-17.645 14.355-32 32-32h704c17.645 0 32 14.355 32 32v480z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M512 400.002c-26.508 0-47.998 21.489-47.998 47.998 0 26.508 21.489 47.998 47.998 47.998 26.508 0 47.998-21.489 47.998-47.998 0-26.508-21.49-47.998-47.998-47.998zM320.01 400.002c-26.508 0-47.998 21.489-47.998 47.998 0 26.508 21.489 47.998 47.997 47.998s47.998-21.489 47.998-47.998c0-26.508-21.489-47.998-47.997-47.998zM703.99 400.002c-26.508 0-47.998 21.489-47.998 47.998 0 26.508 21.489 47.998 47.998 47.998 26.508 0 47.997-21.489 47.997-47.998 0.001-26.508-21.488-47.998-47.997-47.998z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-team1-->
<view a:if="{{name === 'line-team1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M669.102 654.089c39.847-23.658 73.758-56.33 99.051-95.648 31.072-48.305 47.496-104.344 47.496-162.058 0-79.952-31.053-155.122-87.437-211.665C671.804 128.152 596.798 97 517.013 97s-154.791 31.152-211.199 87.719c-56.384 56.543-87.437 131.713-87.437 211.665 0 60.505 17.923 118.808 51.831 168.605 27.755 40.762 64.655 73.7 107.763 96.426l-57.494 218.441c-4.498 17.091 5.71 34.593 22.801 39.091 17.095 4.498 34.593-5.71 39.092-22.801l64.276-244.211c4.079-15.498-3.935-31.622-18.75-37.73-42.518-17.528-78.753-47.002-104.787-85.236-26.646-39.133-40.731-84.98-40.731-132.584 0-62.89 24.418-122.011 68.755-166.473C395.445 185.473 454.356 161 517.013 161s121.567 24.473 165.881 68.911c44.337 44.461 68.755 103.583 68.755 166.473 0 45.41-12.905 89.476-37.322 127.434-23.799 36.997-57.281 66.507-96.829 85.341-14.104 6.717-21.298 22.637-17.016 37.662l71.233 249.951c4.009 14.066 16.824 23.238 30.758 23.238a32 32 0 0 0 8.787-1.234c16.996-4.844 26.848-22.548 22.004-39.545l-64.162-225.142z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M236.612 619.767c-30.481 0-51.497-21.59-63.76-39.703C155.412 554.305 145 519.582 145 487.18c0-35.3 16.112-75.12 39.182-96.834 12.869-12.113 13.482-32.365 1.369-45.234-12.114-12.869-32.365-13.481-45.234-1.369C104.283 377.658 81 433.961 81 487.18c0 45.359 14.163 92.292 38.856 128.766 20.209 29.85 45.943 50.547 74.768 60.681l-62.74 188.692c-5.576 16.771 3.498 34.886 20.269 40.462a31.976 31.976 0 0 0 10.102 1.644c13.405 0 25.898-8.493 30.36-21.912l74.363-223.647a32 32 0 0 0-30.366-42.099zM911.144 615.945C935.837 579.472 950 532.539 950 487.18c0-53.219-23.283-109.521-59.316-143.438-12.87-12.112-33.121-11.5-45.234 1.369s-11.5 33.122 1.369 45.234C869.888 412.06 886 451.879 886 487.18c0 32.402-10.412 67.125-27.853 92.885-12.263 18.112-33.278 39.703-63.76 39.703a32 32 0 0 0-30.365 42.097l74.363 223.647c4.462 13.42 16.954 21.912 30.36 21.912 3.347 0 6.751-0.529 10.102-1.644 16.771-5.576 25.845-23.691 20.269-40.462l-62.74-188.692c28.824-10.133 54.559-30.831 74.768-60.681z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-safe-->
<view a:if="{{name === 'line-safe'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 64L128 207.921v251.135c-0.014 141.513 68.574 274.488 184.512 357.718L512 960l199.488-143.289C827.389 733.507 895.973 600.588 896 459.119V208.047L512 64z m320 395.056c-0.009 121.27-58.793 235.216-158.144 306.543L512 881.848 350.144 765.599C250.793 694.272 192.009 580.326 192 459.056V251.514l320-120.04 320 120.04v207.542z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M384 384c0 59.534 40.855 109.705 96 123.95V704c0 17.673 14.327 32 32 32 17.673 0 32-14.327 32-32v-64h64c17.673 0 32-14.327 32-32s-14.327-32-32-32h-64v-68.05c55.145-14.245 96-64.416 96-123.95 0-70.58-57.421-128-128-128-70.58 0-128 57.42-128 128z m192 0c0 35.29-28.71 64-64 64s-64-28.71-64-64 28.71-64 64-64 64 28.71 64 64z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-statistics-->
<view a:if="{{name === 'line-statistics'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M924.534 434.485c0.261-0.091 0.525-0.177 0.782-0.275 0.096-0.037 0.189-0.079 0.285-0.116 14.981-5.851 23.488-22.151 19.238-38.052C903.616 241.805 782.194 120.383 627.958 79.16c-17.003-4.546-34.469 5.499-39.118 22.449-0.018 0.064-0.039 0.126-0.056 0.19l-0.001 0.005-0.003 0.009-0.002 0.009-10.168 37.948C546.475 131.954 513.436 128 480 128c-56.146 0-110.628 11.003-161.936 32.705-49.541 20.954-94.026 50.945-132.221 89.139s-68.185 82.68-89.139 132.221C75.003 433.372 64 487.854 64 544s11.003 110.629 32.705 161.936c20.954 49.541 50.945 94.026 89.139 132.221s82.68 68.186 132.221 89.14C369.372 948.996 423.854 960 480 960s110.629-11.004 161.936-32.704c49.541-20.954 94.026-50.945 132.221-89.14s68.186-82.68 89.14-132.221C884.996 654.629 896 600.146 896 544c0-33.436-3.954-66.475-11.769-98.61l37.971-10.174c0.424-0.114 0.841-0.241 1.256-0.371 0.104-0.032 0.21-0.059 0.313-0.093 0.258-0.083 0.509-0.178 0.763-0.267z m-51.081-52.465l-37.413 10.025-0.049 0.013-278.737 74.687 74.685-278.73 0.018-0.067 10.022-37.402c107.534 38.667 192.808 123.94 231.474 231.474zM832 544c0 94.022-36.614 182.417-103.099 248.901S574.022 896 480 896s-182.417-36.614-248.901-103.099C164.614 726.417 128 638.022 128 544s36.614-182.417 103.099-248.901C297.583 228.614 385.978 192 480 192c27.823 0 55.302 3.237 82.038 9.618l-80.947 302.1-0.039 0.162c-0.114 0.433-0.219 0.868-0.315 1.307l-0.056 0.263a31.53 31.53 0 0 0-0.245 1.304c-0.017 0.103-0.035 0.206-0.051 0.31-0.064 0.405-0.119 0.812-0.166 1.221-0.016 0.138-0.033 0.277-0.047 0.415-0.037 0.36-0.067 0.722-0.093 1.084-0.013 0.188-0.026 0.376-0.036 0.564-0.016 0.305-0.025 0.61-0.032 0.916-0.006 0.246-0.011 0.491-0.011 0.736s0.005 0.491 0.011 0.736c0.007 0.306 0.016 0.611 0.032 0.916 0.01 0.188 0.023 0.376 0.036 0.564 0.025 0.363 0.055 0.724 0.093 1.085 0.014 0.138 0.031 0.276 0.047 0.414a31.24 31.24 0 0 0 0.836 4.409c0.014 0.053 0.024 0.106 0.038 0.159l0.026 0.083c0.221 0.813 0.472 1.616 0.755 2.407 0.041 0.114 0.085 0.226 0.127 0.34 0.274 0.739 0.568 1.47 0.895 2.186 0.065 0.144 0.138 0.282 0.206 0.424 0.263 0.554 0.543 1.1 0.838 1.637 0.111 0.204 0.218 0.411 0.334 0.612 0.328 0.57 0.679 1.126 1.042 1.675a31.5 31.5 0 0 0 1.648 2.278c0.138 0.173 0.28 0.34 0.421 0.51 0.421 0.508 0.856 1.006 1.309 1.489 0.058 0.062 0.113 0.127 0.171 0.188a32.078 32.078 0 0 0 1.697 1.641l0.214 0.194c0.609 0.54 1.239 1.059 1.891 1.554l0.024 0.018a31.747 31.747 0 0 0 4.252 2.715l0.134 0.071c0.721 0.38 1.458 0.737 2.215 1.064l0.148 0.061c0.644 0.274 1.301 0.525 1.969 0.759 0.174 0.061 0.348 0.125 0.523 0.184 0.588 0.194 1.182 0.377 1.788 0.539 0.061 0.016 0.122 0.027 0.182 0.043 0.366 0.096 0.737 0.175 1.108 0.258 0.408 0.092 0.817 0.181 1.225 0.256a33.05 33.05 0 0 0 2.467 0.352c0.318 0.033 0.635 0.07 0.955 0.094 0.729 0.054 1.456 0.086 2.18 0.091 0.049 0 0.096 0.006 0.145 0.006l0.02-0.001 0.02 0.001c0.049 0 0.097-0.006 0.146-0.006a31.76 31.76 0 0 0 2.175-0.09c0.324-0.024 0.645-0.061 0.968-0.095a31.382 31.382 0 0 0 2.463-0.352c0.405-0.075 0.81-0.163 1.215-0.254 0.371-0.083 0.743-0.162 1.11-0.259 0.061-0.016 0.123-0.026 0.184-0.043l302.101-80.947C828.763 488.698 832 516.177 832 544z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-userconfirmation1-->
<view a:if="{{name === 'line-userconfirmation1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M731.701 579.397c8.85-15.298 3.622-34.873-11.676-43.723-24.291-14.052-49.921-25.536-76.437-34.357a226.986 226.986 0 0 0 26.803-22.925C712.7 436.084 736 379.833 736 320s-23.3-116.084-65.608-158.392S571.832 96 512 96c-59.833 0-116.084 23.3-158.393 65.608C311.3 203.916 288 260.167 288 320s23.3 116.084 65.607 158.392a226.96 226.96 0 0 0 26.734 22.875 416.054 416.054 0 0 0-30.278 11.437c-49.541 20.954-94.026 50.945-132.221 89.14s-68.185 82.68-89.139 132.221C107.003 785.371 96 839.854 96 896c0 17.673 14.327 32 32 32s32-14.327 32-32c0-94.022 36.614-182.418 103.099-248.901C329.582 580.614 417.978 544 512 544c61.892 0 122.744 16.277 175.979 47.073 15.299 8.851 34.874 3.621 43.722-11.676zM352 320c0-88.224 71.775-160 160-160s160 71.776 160 160-71.775 160-160 160-160-71.776-160-160zM918.628 663.373c-12.499-12.499-32.76-12.496-45.255-0.001L685.999 850.745l-82.105-82.106c-12.496-12.496-32.758-12.496-45.254 0-12.497 12.497-12.497 32.759 0 45.256l104.732 104.732c0.28 0.28 0.569 0.547 0.857 0.815 0.113 0.105 0.222 0.217 0.336 0.32 6.079 5.493 13.757 8.239 21.435 8.239 8.189 0 16.379-3.124 22.628-9.373l0.038-0.04 209.961-209.96c12.497-12.497 12.497-32.759 0.001-45.255z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-video-->
<view a:if="{{name === 'line-video'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M864 128H160c-52.935 0-96 43.065-96 96v576c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V224c0-52.935-43.065-96-96-96z m-704 64h704c17.645 0 32 14.355 32 32v96H128v-96c0-17.645 14.355-32 32-32z m704 640H160c-17.645 0-32-14.355-32-32V384h768v416c0 17.645-14.355 32-32 32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M656.124 580.359l-192-112A31.998 31.998 0 0 0 416 496v224a32 32 0 0 0 48.124 27.641l192-112a32 32 0 0 0 0-55.282zM480 664.287V551.713L576.492 608 480 664.287z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-wallet-->
<view a:if="{{name === 'line-wallet'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M864 128H160c-52.93 0-96 43.07-96 96v576c0 52.93 43.07 96 96 96h704c52.93 0 96-43.07 96-96V224c0-52.93-43.07-96-96-96z m32 512H672c-17.645 0-32-14.355-32-32V416c0-17.645 14.355-32 32-32h224v256zM672 320c-52.935 0-96 43.065-96 96v192c0 52.935 43.065 96 96 96h224v96c0 17.65-14.35 32-32 32H160c-17.65 0-32-14.35-32-32V224c0-17.65 14.35-32 32-32h704c17.65 0 32 14.35 32 32v96H672z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M768 560c26.51 0 48-21.49 48-48s-21.49-48-48-48-48 21.49-48 48 21.49 48 48 48z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-workset-->
<view a:if="{{name === 'line-workset'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M832 96H192c-52.935 0-96 43.065-96 96v448c0 52.935 43.065 96 96 96h288v128H288c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h448c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32H544V736h288c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m32 544c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h640c17.645 0 32 14.355 32 32v448z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M603.998 289.016A31.998 31.998 0 0 0 576.276 273h-128a31.999 31.999 0 0 0-27.722 16.016l-64 111a32 32 0 0 0 0 31.968l64 111A31.998 31.998 0 0 0 448.276 559h128a31.999 31.999 0 0 0 27.722-16.016l64-111a32 32 0 0 0 0-31.968l-64-111zM557.789 495h-91.025l-45.549-79 45.549-79h91.025l45.549 79-45.549 79z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-share-->
<view a:if="{{name === 'line-share'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M896 544c-17.673 0-32 14.327-32 32v256c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h256c17.673 0 32-14.327 32-32s-14.327-32-32-32H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V576c0-17.673-14.327-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M927.996 127.828a31.33 31.33 0 0 0-0.036-1.405c-0.013-0.271-0.038-0.538-0.058-0.807-0.019-0.25-0.033-0.501-0.057-0.751-0.032-0.328-0.075-0.652-0.117-0.977-0.024-0.188-0.044-0.376-0.072-0.563-0.051-0.35-0.113-0.696-0.176-1.042-0.029-0.163-0.055-0.327-0.088-0.49-0.069-0.35-0.148-0.695-0.228-1.041-0.037-0.161-0.072-0.323-0.112-0.484a32.505 32.505 0 0 0-0.269-0.998c-0.049-0.173-0.095-0.346-0.147-0.518a29.848 29.848 0 0 0-0.297-0.92c-0.065-0.196-0.129-0.392-0.198-0.586-0.098-0.274-0.203-0.545-0.308-0.816-0.087-0.224-0.172-0.448-0.264-0.67-0.098-0.237-0.203-0.471-0.306-0.705-0.111-0.251-0.221-0.501-0.339-0.749-0.096-0.202-0.197-0.401-0.297-0.601a32.914 32.914 0 0 0-0.417-0.815c-0.093-0.174-0.191-0.344-0.287-0.515a31.28 31.28 0 0 0-0.49-0.852c-0.095-0.158-0.196-0.313-0.294-0.47-0.178-0.284-0.356-0.568-0.544-0.848-0.106-0.157-0.216-0.31-0.325-0.466-0.188-0.27-0.376-0.54-0.573-0.806-0.129-0.174-0.265-0.342-0.397-0.512-0.185-0.238-0.367-0.478-0.559-0.712-0.187-0.227-0.382-0.447-0.574-0.669-0.149-0.171-0.292-0.345-0.445-0.514a32.155 32.155 0 0 0-2.246-2.246c-0.174-0.158-0.354-0.307-0.532-0.46-0.216-0.187-0.429-0.377-0.65-0.558-0.239-0.197-0.484-0.383-0.728-0.572-0.165-0.128-0.328-0.259-0.496-0.384-0.272-0.202-0.549-0.394-0.825-0.587-0.148-0.103-0.294-0.209-0.444-0.309a29.825 29.825 0 0 0-0.871-0.558c-0.149-0.093-0.296-0.188-0.446-0.279a31.845 31.845 0 0 0-0.875-0.503c-0.164-0.092-0.326-0.185-0.491-0.274-0.279-0.149-0.56-0.291-0.842-0.431-0.19-0.095-0.379-0.191-0.572-0.283-0.259-0.123-0.52-0.238-0.782-0.353-0.223-0.099-0.445-0.198-0.671-0.292-0.234-0.097-0.47-0.187-0.705-0.278-0.259-0.1-0.517-0.2-0.779-0.294-0.208-0.074-0.418-0.142-0.627-0.212-0.291-0.097-0.582-0.194-0.877-0.283-0.188-0.057-0.378-0.108-0.567-0.161a30.17 30.17 0 0 0-0.95-0.256c-0.179-0.044-0.359-0.083-0.538-0.124a29.392 29.392 0 0 0-0.988-0.216c-0.184-0.036-0.368-0.066-0.552-0.099-0.326-0.058-0.651-0.117-0.98-0.165-0.214-0.032-0.429-0.055-0.644-0.082-0.298-0.038-0.596-0.078-0.897-0.108-0.291-0.029-0.582-0.046-0.874-0.066-0.229-0.016-0.456-0.038-0.686-0.049a32.743 32.743 0 0 0-1.598-0.04H640c-17.673 0-32 14.327-32 32s14.327 32 32 32h178.746L489.373 489.373c-12.497 12.497-12.497 32.758 0 45.254C495.621 540.876 503.811 544 512 544c8.189 0 16.379-3.124 22.627-9.373L864 205.254V384c0 17.673 14.327 32 32 32s32-14.327 32-32V128c0-0.058-0.004-0.114-0.004-0.172z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-transaction-->
<view a:if="{{name === 'line-transaction'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M832 96H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m-192 64v32c0 17.645-14.355 32-32 32H416c-17.645 0-32-14.355-32-32v-32h256z m224 672c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h128v32c0 52.935 43.065 96 96 96h192c52.935 0 96-43.065 96-96v-32h128c17.645 0 32 14.355 32 32v640z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M672 484.001H471.426l62.851-36.287c15.306-8.836 20.55-28.408 11.713-43.713s-28.406-20.551-43.713-11.713l-166.277 96c-0.044 0.025-0.085 0.053-0.128 0.079-0.352 0.206-0.698 0.418-1.041 0.637-0.134 0.085-0.267 0.169-0.399 0.256-0.275 0.181-0.545 0.367-0.814 0.556-0.193 0.135-0.386 0.27-0.575 0.409-0.184 0.136-0.365 0.276-0.546 0.415-0.259 0.199-0.518 0.399-0.77 0.605l-0.277 0.233c-0.325 0.272-0.646 0.547-0.959 0.831l-0.025 0.023a31.78 31.78 0 0 0-5.803 7.031c-0.127 0.208-0.245 0.423-0.368 0.634-0.125 0.216-0.254 0.43-0.373 0.649a31.75 31.75 0 0 0-3.186 8.538l-0.008 0.035a29.96 29.96 0 0 0-0.239 1.243c-0.021 0.12-0.044 0.239-0.064 0.359-0.052 0.319-0.096 0.64-0.138 0.962-0.031 0.23-0.062 0.459-0.088 0.69a31.94 31.94 0 0 0-0.066 0.694c-0.03 0.33-0.056 0.66-0.075 0.991-0.009 0.156-0.015 0.312-0.022 0.468-0.018 0.409-0.029 0.818-0.031 1.229 0 0.049-0.004 0.097-0.004 0.146 0 0.056 0.004 0.111 0.004 0.167 0.002 0.447 0.013 0.895 0.034 1.343l0.014 0.269a30.933 30.933 0 0 0 0.132 1.636 31.515 31.515 0 0 0 0.408 2.707c0.036 0.185 0.072 0.37 0.111 0.554 0.064 0.298 0.134 0.596 0.206 0.893 0.058 0.239 0.117 0.477 0.18 0.714a36.85 36.85 0 0 0 0.468 1.584c0.059 0.18 0.12 0.36 0.182 0.539 0.117 0.342 0.239 0.681 0.368 1.017 0.051 0.133 0.104 0.265 0.156 0.397 0.151 0.38 0.308 0.756 0.474 1.129 0.043 0.098 0.088 0.195 0.132 0.292a29.47 29.47 0 0 0 0.698 1.436c0.204 0.395 0.417 0.785 0.637 1.17 0.029 0.05 0.054 0.102 0.083 0.152 0.019 0.033 0.041 0.064 0.06 0.097a31.993 31.993 0 0 0 2.214 3.309 35.719 35.719 0 0 0 1.164 1.432c0.185 0.216 0.367 0.433 0.557 0.643 0.342 0.378 0.694 0.747 1.053 1.108 0.265 0.266 0.538 0.521 0.811 0.777 0.18 0.168 0.359 0.336 0.543 0.499 0.266 0.238 0.535 0.472 0.808 0.7 0.204 0.171 0.413 0.335 0.622 0.5 0.365 0.29 0.735 0.573 1.111 0.845 0.334 0.242 0.67 0.482 1.014 0.711 0.203 0.135 0.409 0.264 0.615 0.394 0.355 0.226 0.714 0.444 1.078 0.656 0.162 0.094 0.322 0.188 0.485 0.279a31.854 31.854 0 0 0 5.432 2.397l0.056 0.019c1.281 0.425 2.596 0.774 3.944 1.037l0.06 0.011c0.582 0.112 1.169 0.206 1.762 0.286 0.165 0.023 0.331 0.043 0.497 0.063 0.444 0.053 0.89 0.096 1.34 0.13 0.221 0.017 0.441 0.038 0.662 0.05 0.421 0.024 0.845 0.034 1.27 0.041 0.195 0.004 0.388 0.019 0.583 0.019 0.051 0 0.102-0.005 0.153-0.005H672c17.673 0 32-14.327 32-32s-14.327-31.997-32-31.997zM352.001 675.999h200.574l-62.851 36.287c-15.306 8.836-20.55 28.408-11.713 43.713 8.837 15.305 28.406 20.551 43.713 11.713l166.277-96c0.044-0.025 0.085-0.053 0.128-0.079 0.352-0.206 0.698-0.418 1.041-0.637 0.134-0.085 0.267-0.169 0.399-0.256 0.275-0.181 0.545-0.367 0.814-0.556 0.193-0.135 0.386-0.27 0.575-0.409 0.184-0.136 0.365-0.276 0.546-0.415 0.259-0.199 0.518-0.399 0.77-0.605l0.277-0.233c0.325-0.272 0.646-0.547 0.959-0.831l0.025-0.023a31.78 31.78 0 0 0 5.803-7.031c0.127-0.208 0.245-0.423 0.368-0.634 0.125-0.216 0.254-0.43 0.373-0.649a31.75 31.75 0 0 0 3.186-8.538l0.008-0.035c0.089-0.411 0.167-0.826 0.239-1.243 0.021-0.12 0.044-0.239 0.064-0.359 0.052-0.319 0.096-0.64 0.138-0.962 0.031-0.23 0.062-0.459 0.088-0.69 0.025-0.231 0.046-0.462 0.066-0.694 0.03-0.33 0.056-0.66 0.075-0.991 0.009-0.156 0.015-0.312 0.022-0.468 0.018-0.409 0.029-0.818 0.031-1.229 0-0.049 0.004-0.097 0.004-0.146 0-0.056-0.004-0.111-0.004-0.167a31.72 31.72 0 0 0-0.034-1.343l-0.014-0.269a30.933 30.933 0 0 0-0.132-1.636 31.515 31.515 0 0 0-0.408-2.707c-0.036-0.185-0.072-0.37-0.111-0.554a34.91 34.91 0 0 0-0.206-0.893 33.864 33.864 0 0 0-0.18-0.714 36.85 36.85 0 0 0-0.468-1.584c-0.059-0.18-0.12-0.36-0.182-0.539a29.262 29.262 0 0 0-0.368-1.017c-0.051-0.133-0.104-0.265-0.156-0.397-0.151-0.38-0.308-0.756-0.474-1.129-0.043-0.098-0.088-0.195-0.132-0.292a29.47 29.47 0 0 0-0.571-1.188l-0.127-0.248a31.413 31.413 0 0 0-0.637-1.17c-0.029-0.05-0.054-0.102-0.083-0.152-0.019-0.033-0.041-0.064-0.06-0.097a31.993 31.993 0 0 0-2.214-3.309 35.719 35.719 0 0 0-1.164-1.432c-0.185-0.216-0.367-0.433-0.557-0.643a32.41 32.41 0 0 0-1.053-1.108c-0.265-0.266-0.538-0.521-0.811-0.777-0.18-0.168-0.359-0.336-0.543-0.499a31.378 31.378 0 0 0-0.808-0.7c-0.204-0.171-0.413-0.335-0.622-0.5a30.95 30.95 0 0 0-1.111-0.845c-0.334-0.242-0.67-0.482-1.014-0.711-0.203-0.135-0.409-0.264-0.615-0.394a31.906 31.906 0 0 0-1.078-0.656c-0.162-0.094-0.322-0.188-0.485-0.279a31.854 31.854 0 0 0-3.512-1.688l-0.054-0.021a32.21 32.21 0 0 0-1.866-0.688l-0.056-0.019a31.944 31.944 0 0 0-3.944-1.037l-0.06-0.011a31.855 31.855 0 0 0-1.762-0.286c-0.165-0.023-0.331-0.043-0.497-0.063-0.444-0.053-0.89-0.096-1.34-0.13-0.221-0.017-0.441-0.038-0.662-0.05a31.44 31.44 0 0 0-1.27-0.041c-0.195-0.004-0.388-0.019-0.583-0.019-0.051 0-0.102 0.005-0.153 0.005H352.001c-17.673 0-32 14.327-32 32s14.327 31.997 32 31.997z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--logo-moment-->
<view a:if="{{name === 'logo-moment'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M895.197 350.039l-0.028-0.067c-2.918-6.775-5.437-12.34-7.898-17.45a10.1 10.1 0 0 0-0.607-1.252c-20.169-41.944-47.309-80.128-80.675-113.502-38.231-38.24-82.724-68.23-132.236-89.136C622.559 106.979 568.122 96 511.955 96c-56.212 0-110.682 11.011-161.882 32.72-49.518 20.908-94.011 50.899-132.242 89.139-38.232 38.241-68.216 82.743-89.11 132.251C107.009 401.246 96 455.698 96 511.955c0 56.224 11.008 110.706 32.74 161.984 2.896 6.724 5.396 12.25 7.835 17.315 0.189 0.493 0.426 0.994 0.723 1.496 20.165 41.902 47.286 80.047 80.623 113.392 38.232 38.24 82.725 68.231 132.242 89.14C401.587 916.992 456.052 928 512.045 928c56.214 0 110.684-11.01 161.882-32.719 49.518-20.908 94.01-50.899 132.243-89.14 38.231-38.24 68.215-82.742 89.118-132.271C916.994 622.436 928 567.959 928 511.955c0-55.861-11.036-110.338-32.803-161.916zM671.421 489.36V196.636c33.375 16.925 63.663 38.931 90.212 65.562l0.01 0.01c23.275 23.28 43.104 49.479 59.015 77.961L671.421 489.36z m155.778 182.186c-16.871 33.258-38.875 63.559-65.557 90.246-23.271 23.276-49.464 43.109-77.939 59.026L534.542 671.546h292.657z m-630.58-319.091c16.872-33.259 38.876-63.56 65.557-90.247 23.274-23.279 49.465-43.111 77.942-59.027l149.24 149.274H196.619z m425.453 495.336c-35.537 11.606-72.506 17.481-110.118 17.481-32.898 0-65.441-4.492-96.84-13.359V640.787l206.958 207.004zM511.955 608.818c-53.398 0-96.84-43.453-96.84-96.864s43.442-96.864 96.84-96.864c53.397 0 96.839 43.453 96.839 96.864s-43.442 96.864-96.839 96.864zM401.84 176.208c35.538-11.607 72.503-17.481 110.115-17.481 32.898 0 65.436 4.492 96.839 13.36v211.041L401.84 176.208z m463.351 335.837c0 32.909-4.49 65.455-13.357 96.864H640.671l207.042-207.008c11.604 35.547 17.478 72.525 17.478 110.144z m-482.122-96.954L176.167 622.118c-11.585-35.298-17.449-72.281-17.449-110.164 0-32.906 4.491-65.453 13.357-96.864h210.994v0.001z m-30.672 119.468v292.742c-33.204-16.789-63.495-38.776-90.221-65.509-23.273-23.278-43.103-49.479-59.016-77.963l149.237-149.27z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-signout-->
<view a:if="{{name === 'line-signout'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M576 864H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h384c17.673 0 32-14.327 32-32s-14.327-32-32-32H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h384c17.673 0 32-14.327 32-32s-14.327-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M920.323 532.782c0.135-0.158 0.273-0.312 0.405-0.472 0.226-0.274 0.44-0.554 0.655-0.834 0.1-0.13 0.203-0.258 0.302-0.389 0.221-0.296 0.431-0.598 0.64-0.9 0.086-0.124 0.174-0.246 0.258-0.371 0.205-0.306 0.401-0.616 0.594-0.927 0.081-0.131 0.165-0.26 0.245-0.391 0.183-0.304 0.357-0.611 0.529-0.919 0.084-0.15 0.169-0.298 0.251-0.449 0.155-0.289 0.302-0.581 0.447-0.873 0.09-0.181 0.182-0.362 0.269-0.545 0.126-0.265 0.244-0.533 0.362-0.801 0.096-0.217 0.193-0.434 0.284-0.654 0.099-0.238 0.191-0.478 0.283-0.718 0.099-0.256 0.198-0.511 0.29-0.769 0.075-0.209 0.143-0.42 0.213-0.63a33.1 33.1 0 0 0 0.283-0.876c0.056-0.187 0.107-0.375 0.16-0.562 0.09-0.318 0.178-0.635 0.258-0.957 0.043-0.174 0.081-0.349 0.121-0.524 0.077-0.333 0.153-0.666 0.219-1.002 0.035-0.177 0.063-0.356 0.095-0.533 0.06-0.332 0.12-0.664 0.169-0.999 0.03-0.204 0.052-0.41 0.078-0.615 0.039-0.309 0.081-0.617 0.111-0.928 0.027-0.271 0.042-0.543 0.062-0.815 0.018-0.248 0.041-0.495 0.053-0.744 0.026-0.527 0.04-1.055 0.04-1.582v-0.001c0-0.527-0.014-1.054-0.04-1.581-0.012-0.251-0.036-0.498-0.054-0.748-0.02-0.271-0.035-0.541-0.062-0.811-0.031-0.313-0.072-0.622-0.112-0.932-0.026-0.203-0.048-0.407-0.078-0.61-0.049-0.337-0.11-0.671-0.17-1.004-0.032-0.176-0.06-0.352-0.094-0.528a30.045 30.045 0 0 0-0.221-1.008c-0.04-0.172-0.077-0.345-0.119-0.517-0.081-0.324-0.17-0.645-0.261-0.966-0.052-0.184-0.102-0.369-0.157-0.552-0.09-0.298-0.188-0.593-0.286-0.887-0.069-0.207-0.136-0.414-0.209-0.619-0.094-0.263-0.195-0.522-0.295-0.782-0.091-0.235-0.181-0.471-0.278-0.705-0.093-0.225-0.192-0.446-0.29-0.668-0.116-0.263-0.232-0.527-0.356-0.787-0.089-0.188-0.184-0.374-0.277-0.56a32.957 32.957 0 0 0-0.439-0.857c-0.085-0.158-0.174-0.313-0.262-0.469a29.758 29.758 0 0 0-0.517-0.898c-0.085-0.141-0.174-0.278-0.261-0.418a31.86 31.86 0 0 0-0.577-0.9c-0.092-0.137-0.188-0.269-0.282-0.404a34.13 34.13 0 0 0-0.616-0.867c-0.109-0.147-0.224-0.289-0.336-0.433-0.204-0.265-0.407-0.53-0.621-0.79-0.153-0.186-0.313-0.365-0.47-0.548-0.182-0.212-0.361-0.426-0.549-0.634a33.396 33.396 0 0 0-0.899-0.946c-0.064-0.065-0.122-0.132-0.187-0.196l-224-224c-12.498-12.497-32.758-12.497-45.256 0-12.496 12.497-12.496 32.758 0 45.255L818.744 480H384c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h434.745L649.372 713.372c-12.496 12.497-12.496 32.759 0 45.256C655.621 764.876 663.811 768 672 768s16.379-3.124 22.628-9.372l224-224c0.139-0.139 0.267-0.283 0.403-0.424 0.227-0.235 0.456-0.469 0.676-0.711 0.212-0.233 0.412-0.473 0.616-0.711z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--logo-wechat-->
<view a:if="{{name === 'logo-wechat'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M308.788 298.212c-13.238 0-26.634 4.411-36.753 12.101-11.914 9.054-18.475 21.8-18.475 35.89s6.561 26.835 18.475 35.89c10.09 7.668 23.438 12.075 36.639 12.101 0.319 0.005 0.636 0.008 0.953 0.008 13.056 0 24.868-4.553 33.354-12.881 8.875-8.708 13.762-21.18 13.762-35.117 0-13.569-4.78-25.826-13.459-34.513-8.681-8.693-20.932-13.479-34.496-13.479zM549.856 346.606c0-13.569-4.78-25.826-13.46-34.514-8.683-8.69-20.934-13.477-34.497-13.477-13.238 0-26.634 4.411-36.753 12.101-11.914 9.054-18.475 21.8-18.475 35.89 0 29.642 28.667 47.99 55.228 47.99 28.237 0.001 47.957-19.734 47.957-47.99z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M928 601.773c0-56.698-25.041-111.096-70.468-153.134-41.971-39.21-97.243-64.224-156.525-71.012-13.899-57.481-51.261-110.031-105.729-148.492C539.443 189.712 469.695 168 398.88 168 231.871 168 96 285.129 96 429.101c0 38.591 9.743 75.517 28.957 109.75 17.878 31.853 43.692 60.866 76.807 86.347l-21.49 64.891c-3.457 9.876-0.506 20.64 7.721 28.127l0.474 0.409c8.092 6.627 19.638 8.019 28.73 3.469l88.298-44.191 8.032 1.787h0.547l3.838 0.76c26.362 5.224 51.261 10.158 80.966 10.158 3.076 0 16.632-0.868 27.902-3.02 39.851 84.708 135.453 140.915 241.566 140.915 27.203 0 54.896-6.078 79.19-12.158l67.198 36.515 0.652 0.261c0.306 0.123 0.622 0.257 0.946 0.394 2.491 1.05 5.902 2.487 9.978 2.487 8.214 0 13.567-3.21 16.745-6.146 7.647-6.154 10.985-16.404 8.725-26.968l-0.108-0.505-15.967-52.21C895.294 720.644 928 661.097 928 601.773z m-519.331 28.218c-3.708 0.137-7.339 0.221-11.001 0.221-26.133 0-48.938-4.786-73.083-9.854l-15.641-2.888-0.478-0.078c-5.433-0.779-10.55 0.196-13.885 1.149l-1.103 0.315-37.639 19.079 6.911-20.078c3.729-10.559 0.099-22.025-9.261-29.232l-0.456-0.334c-63.076-44.003-95.058-97.291-95.058-158.382 0-53.152 24.747-103.358 69.682-141.371 45.54-38.524 106.061-59.74 170.414-59.74 109.807 0 209.281 63.301 238.381 149.422-59.762 4.473-115.446 27.178-158.184 64.768-46.949 41.294-72.805 95.674-72.805 153.124 0.001 11.109 1.076 22.454 3.206 33.879z m374.329 102.726c-8.748 6.034-12.655 17.691-9.345 27.934l1.616 5.371-17.759-9.727-0.671-0.269c-0.305-0.122-0.621-0.256-0.945-0.393-2.491-1.051-5.903-2.489-9.979-2.489-2.077 0-4.443 0.127-6.813 0.93l-4.553 1.125c-22.42 5.55-45.603 11.29-67.414 11.29-108.999 0-197.676-73.892-197.676-164.716 0-90.602 88.677-164.312 197.676-164.312 106.714 0 196.868 75.431 196.868 164.816 0.377 45.036-28.389 91.353-81.005 130.44z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M777.442 510.93c-8.958-8.091-21.158-12.547-34.354-12.547-21.12 0-41.088 20.178-41.088 41.52 0 21.343 19.968 41.521 41.088 41.521 29.621 0 47.956-21.552 47.956-41.521 0-10.605-4.958-21.165-13.602-28.973zM625.942 510.93c-8.958-8.091-21.158-12.547-34.354-12.547-21.328 0-41.492 20.178-41.492 41.52 0 21.343 20.164 41.521 41.492 41.521 29.621 0 47.956-21.552 47.956-41.521 0-10.605-4.958-21.165-13.602-28.973z' fill='{{(isStr ? colors : colors[2]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-task-->
<view a:if="{{name === 'line-task'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M832 96H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m32 736c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h640c17.645 0 32 14.355 32 32v640z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M352 544c-70.58 0-128 57.421-128 128s57.42 128 128 128 128-57.421 128-128-57.42-128-128-128z m0 192c-35.29 0-64-28.71-64-64s28.71-64 64-64 64 28.71 64 64-28.71 64-64 64zM425.627 260.627L311.89 374.365l-33.127-33.127c-12.497-12.497-32.758-12.497-45.255 0s-12.497 32.758 0 45.255l53.301 53.301c0.719 0.893 1.49 1.759 2.319 2.589a31.847 31.847 0 0 0 13.748 8.117 32.088 32.088 0 0 0 9.26 1.366c8.189 0 16.379-3.124 22.627-9.373a32.28 32.28 0 0 0 3.078-3.568l133.042-133.042c12.497-12.497 12.497-32.758 0-45.255-12.498-12.498-32.758-12.498-45.256-0.001zM768 320H576c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h192c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM768 640H576c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h192c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-addcommodity-->
<view a:if="{{name === 'line-addcommodity'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M832 96H192c-52.9 0-96 43.1-96 96v640c0 52.9 43.1 96 96 96h320c17.7 0 32-14.3 32-32s-14.3-32-32-32H192c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h640c17.6 0 32 14.4 32 32v320c0 17.7 14.3 32 32 32s32-14.3 32-32V192c0-52.9-43.1-96-96-96z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M338.2 272c0-26.5-21.5-48-48-48s-48 21.5-48 48c0 24.3 18 44.3 41.4 47.5 22.5 29.2 50.4 53.9 82.3 72.7C410 418.3 460.5 432 512 432s102-13.7 146.1-39.7c31.8-18.8 59.8-43.6 82.3-72.7 23.4-3.2 41.4-23.3 41.4-47.5 0-26.5-21.5-48-48-48s-48 21.5-48 48c0 4 0.5 7.9 1.4 11.6-17.1 21.4-37.9 39.6-61.6 53.5C591.3 357.3 552 368 512 368s-79.3-10.7-113.6-30.9c-23.7-13.9-44.5-32.2-61.6-53.5 1-3.7 1.4-7.6 1.4-11.6zM896 759h-73v-73c0-17.7-14.3-32-32-32s-32 14.3-32 32v73h-73c-17.7 0-32 14.3-32 32s14.3 32 32 32h73v73c0 17.7 14.3 32 32 32s32-14.3 32-32v-73h73c17.7 0 32-14.3 32-32s-14.3-32-32-32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-accurate-->
<view a:if="{{name === 'line-accurate'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 384c-70.58 0-128 57.42-128 128 0 70.579 57.42 128 128 128 70.579 0 128-57.421 128-128 0-70.58-57.421-128-128-128z m0 192c-35.29 0-64-28.71-64-64s28.71-64 64-64 64 28.71 64 64-28.71 64-64 64z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M928 480h-33.312c-3.327-40.537-12.995-79.93-28.877-117.48-19.343-45.73-47.026-86.793-82.281-122.049-35.256-35.255-76.319-62.938-122.05-82.281-37.55-15.882-76.942-25.55-117.479-28.877V96c0-17.673-14.327-32-32-32-17.673 0-32 14.327-32 32v33.312c-40.537 3.327-79.93 12.995-117.48 28.878-45.73 19.342-86.793 47.025-122.049 82.281-35.255 35.255-62.938 76.319-82.281 122.049-15.882 37.55-25.55 76.943-28.877 117.48H96c-17.673 0-32 14.327-32 32s14.327 32 32 32h33.312c3.327 40.537 12.995 79.931 28.878 117.48 19.342 45.729 47.025 86.793 82.281 122.049 35.255 35.255 76.319 62.938 122.049 82.281 37.55 15.882 76.943 25.55 117.48 28.877V928c0 17.673 14.327 32 32 32s32-14.327 32-32v-33.312c40.537-3.327 79.931-12.995 117.48-28.877 45.729-19.343 86.793-47.026 122.049-82.281 35.255-35.256 62.938-76.319 82.281-122.05 15.882-37.55 25.55-76.942 28.877-117.479H928c17.673 0 32-14.327 32-32C960 494.327 945.673 480 928 480zM738.274 738.274C685.441 791.107 617.387 823.189 544 830.428V800c0-17.673-14.327-32-32-32-17.673 0-32 14.327-32 32v30.428c-73.387-7.239-141.441-39.321-194.274-92.154S200.811 617.387 193.572 544H224c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32h-30.428c7.239-73.387 39.32-141.441 92.154-194.274 52.833-52.833 120.887-84.915 194.274-92.154V224c0 17.673 14.327 32 32 32 17.673 0 32-14.327 32-32v-30.428c73.387 7.239 141.441 39.32 194.274 92.154 52.833 52.833 84.914 120.887 92.154 194.274H800c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h30.428c-7.239 73.387-39.321 141.441-92.154 194.274z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-briefcase-->
<view a:if="{{name === 'line-briefcase'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M864 256h-64v-32c0-52.9-43.1-96-96-96H320c-52.9 0-96 43.1-96 96v32h-64c-52.9 0-96 43.1-96 96v448c0 52.9 43.1 96 96 96h704c52.9 0 96-43.1 96-96V352c0-52.9-43.1-96-96-96z m-576-32c0-17.6 14.4-32 32-32h384c17.6 0 32 14.4 32 32v32H288v-32z m608 576c0 17.6-14.4 32-32 32H160c-17.6 0-32-14.4-32-32V544h260c14.2 55.1 64.4 96 124 96 59.5 0 109.7-40.9 124-96h260v256zM448 512c0-35.3 28.7-64 64-64s64 28.7 64 64-28.7 64-64 64-64-28.7-64-64z m448-32H636c-14.2-55.1-64.4-96-124-96-59.5 0-109.7 40.9-124 96H128V352c0-17.6 14.4-32 32-32h704c17.6 0 32 14.4 32 32v128z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-calendar-->
<view a:if="{{name === 'line-calendar'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M864 192h-96v-32c0-17.673-14.327-32-32-32s-32 14.327-32 32v32H320v-32c0-17.673-14.327-32-32-32s-32 14.327-32 32v32h-96c-52.935 0-96 43.065-96 96v512c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V288c0-52.935-43.065-96-96-96z m-704 64h96v32c0 17.673 14.327 32 32 32s32-14.327 32-32v-32h384v32c0 17.673 14.327 32 32 32s32-14.327 32-32v-32h96c17.645 0 32 14.355 32 32v96H128v-96c0-17.645 14.355-32 32-32z m704 576H160c-17.645 0-32-14.355-32-32V448h768v352c0 17.645-14.355 32-32 32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M640 608H384c-17.673 0-32 14.327-32 32s14.327 32 32 32h256c17.673 0 32-14.327 32-32s-14.327-32-32-32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-achievement-->
<view a:if="{{name === 'line-achievement'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M825 96H185c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m32 736c0 17.645-14.355 32-32 32H185c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h640c17.645 0 32 14.355 32 32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M792.906 253.671c-0.02-0.271-0.035-0.542-0.062-0.812-0.03-0.313-0.072-0.622-0.112-0.932-0.026-0.204-0.047-0.407-0.078-0.61a29.96 29.96 0 0 0-0.169-1.003c-0.032-0.176-0.06-0.353-0.094-0.529-0.066-0.339-0.143-0.673-0.22-1.008-0.04-0.172-0.077-0.345-0.12-0.517-0.081-0.324-0.17-0.645-0.26-0.965-0.052-0.185-0.102-0.369-0.157-0.553-0.09-0.297-0.188-0.592-0.286-0.886-0.069-0.207-0.137-0.414-0.21-0.62-0.094-0.263-0.195-0.523-0.295-0.783-0.091-0.235-0.181-0.47-0.277-0.703-0.094-0.225-0.193-0.447-0.292-0.67-0.116-0.262-0.231-0.524-0.355-0.785-0.09-0.189-0.185-0.375-0.278-0.562a31.227 31.227 0 0 0-0.438-0.854c-0.085-0.159-0.175-0.314-0.263-0.472-0.168-0.3-0.337-0.6-0.515-0.896-0.085-0.142-0.175-0.28-0.262-0.42a31.728 31.728 0 0 0-0.576-0.898c-0.092-0.138-0.189-0.271-0.284-0.407-0.201-0.29-0.402-0.579-0.614-0.863-0.111-0.149-0.227-0.293-0.34-0.439-0.203-0.263-0.404-0.527-0.617-0.785-0.154-0.188-0.316-0.369-0.475-0.553-0.181-0.21-0.357-0.422-0.545-0.629-0.299-0.33-0.608-0.651-0.921-0.969-0.056-0.057-0.108-0.117-0.165-0.173-0.054-0.054-0.111-0.104-0.166-0.158-0.32-0.315-0.644-0.627-0.977-0.929-0.204-0.184-0.413-0.358-0.619-0.536-0.188-0.162-0.372-0.327-0.563-0.484-0.254-0.209-0.515-0.408-0.774-0.608-0.15-0.116-0.298-0.235-0.451-0.349a30.849 30.849 0 0 0-0.854-0.607c-0.139-0.097-0.276-0.196-0.417-0.29a29.791 29.791 0 0 0-0.89-0.57c-0.143-0.089-0.284-0.181-0.429-0.268a31.35 31.35 0 0 0-0.888-0.511c-0.16-0.089-0.318-0.181-0.48-0.268-0.281-0.15-0.564-0.293-0.848-0.435-0.189-0.094-0.376-0.19-0.567-0.28-0.26-0.123-0.521-0.239-0.784-0.354-0.223-0.098-0.445-0.198-0.67-0.291-0.234-0.097-0.471-0.188-0.707-0.279-0.258-0.1-0.516-0.2-0.777-0.293-0.208-0.075-0.418-0.143-0.628-0.213a33.169 33.169 0 0 0-0.877-0.283c-0.187-0.056-0.375-0.107-0.563-0.16a30.362 30.362 0 0 0-0.953-0.257c-0.178-0.044-0.357-0.083-0.536-0.124a30.06 30.06 0 0 0-0.99-0.216c-0.183-0.036-0.367-0.065-0.55-0.099-0.327-0.058-0.653-0.117-0.983-0.166-0.213-0.032-0.428-0.055-0.642-0.082-0.299-0.038-0.597-0.079-0.898-0.108-0.29-0.029-0.581-0.046-0.872-0.066-0.229-0.016-0.457-0.038-0.688-0.049a32.743 32.743 0 0 0-1.598-0.04H569c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h114.745L541 430.745l-68.872-68.872c-12.498-12.497-32.758-12.497-45.256 0l-200.5 200.5c-12.496 12.497-12.496 32.759 0 45.256C232.621 613.876 240.811 617 249 617s16.379-3.124 22.628-9.372L449.5 429.755l68.872 68.872C524.621 504.876 532.811 508 541 508s16.379-3.124 22.628-9.373L729 333.255V448c0 17.673 14.327 32 32 32s32-14.327 32-32V255.999c0-0.527-0.014-1.055-0.04-1.581-0.012-0.251-0.036-0.498-0.054-0.747zM761 736H249c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h512c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-check1-->
<view a:if="{{name === 'line-check1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M416 896H224c-17.6 0-32-14.4-32-32V160c0-17.6 14.4-32 32-32h576c17.6 0 32 14.4 32 32v256c0 17.7 14.3 32 32 32s32-14.3 32-32V160c0-52.9-43.1-96-96-96H224c-52.9 0-96 43.1-96 96v704c0 52.9 43.1 96 96 96h192c17.7 0 32-14.3 32-32s-14.3-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M704 288c0-17.7-14.3-32-32-32H352c-17.7 0-32 14.3-32 32s14.3 32 32 32h320c17.7 0 32-14.3 32-32zM352 480c-17.7 0-32 14.3-32 32s14.3 32 32 32h192c17.7 0 32-14.3 32-32s-14.3-32-32-32H352zM950.6 649.4c-12.5-12.5-32.8-12.5-45.3 0L672 882.7 534.6 745.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l160 160c4.7 4.7 10.5 7.6 16.5 8.8 2 0.4 4.1 0.6 6.1 0.6s4.1-0.2 6.1-0.6c6.1-1.2 11.8-4.1 16.5-8.8l256-256c12.6-12.6 12.6-32.8 0.1-45.3z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-application-->
<view a:if="{{name === 'line-application'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M895.746 276.142l-368-208a32.004 32.004 0 0 0-31.492 0l-368 208A32 32 0 0 0 112 304v416a32 32 0 0 0 16.254 27.858l368 208A31.987 31.987 0 0 0 512 960c5.431 0 10.86-1.381 15.746-4.142l368-208A32 32 0 0 0 912 720V304a32 32 0 0 0-16.254-27.858zM848 701.329L512 891.242 176 701.329V322.671l336-189.913 336 189.913v378.658z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M480 320v173.525l-150.277 86.762c-15.305 8.837-20.549 28.407-11.713 43.713 5.927 10.267 16.683 16.005 27.743 16.005a31.858 31.858 0 0 0 15.97-4.292L512 548.951l150.277 86.762a31.858 31.858 0 0 0 15.97 4.292c11.06 0 21.816-5.739 27.743-16.005 8.837-15.306 3.593-34.876-11.713-43.713L544 493.525V320c0-17.673-14.327-32-32-32-17.673 0-32 14.327-32 32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-businesscard-->
<view a:if="{{name === 'line-businesscard'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M864 128H160c-52.935 0-96 43.065-96 96v576c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V224c0-52.935-43.065-96-96-96z m32 672c0 17.645-14.355 32-32 32H160c-17.645 0-32-14.355-32-32V224c0-17.645 14.355-32 32-32h704c17.645 0 32 14.355 32 32v576z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M757.189 520.625C773.896 501.056 784 475.687 784 448c0-61.757-50.243-112-112-112s-112 50.243-112 112c0 27.687 10.104 53.056 26.811 72.625C541.897 548.989 512 599.065 512 656c0 17.673 14.327 32 32 32s32-14.327 32-32c0-52.935 43.065-96 96-96s96 43.065 96 96c0 17.673 14.327 32 32 32s32-14.327 32-32c0-56.935-29.897-107.011-74.811-135.375zM624 448c0-26.467 21.532-48 48-48s48 21.533 48 48-21.532 48-48 48-48-21.533-48-48zM384 352H256c-17.673 0-32 14.327-32 32s14.327 32 32 32h128c17.673 0 32-14.327 32-32s-14.327-32-32-32zM384 480H256c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h128c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM384 608H256c-17.673 0-32 14.327-32 32s14.327 32 32 32h128c17.673 0 32-14.327 32-32s-14.327-32-32-32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-coin-->
<view a:if="{{name === 'line-coin'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M924.781 337.608c-22.566-53.352-54.864-101.259-95.997-142.393-41.134-41.133-89.041-73.431-142.393-95.997C631.14 75.85 572.465 64 512 64S392.86 75.85 337.608 99.219c-53.352 22.565-101.259 54.864-142.393 95.997-41.133 41.133-73.431 89.041-95.997 142.392C75.85 392.861 64 451.535 64 512c0 60.466 11.85 119.14 35.219 174.392 22.565 53.352 54.864 101.26 95.997 142.393s89.041 73.431 142.392 95.997C392.861 948.15 451.535 960 512 960c60.466 0 119.14-11.85 174.392-35.219 53.352-22.566 101.26-54.864 142.393-95.997 41.133-41.134 73.431-89.041 95.997-142.393C948.15 631.14 960 572.465 960 512s-11.85-119.14-35.219-174.392zM783.529 783.529C711.001 856.057 614.57 896 512 896s-199.001-39.943-271.529-112.471S128 614.57 128 512s39.943-199 112.471-271.529C312.999 167.943 409.43 128 512 128s199.001 39.943 271.529 112.471S896 409.43 896 512s-39.943 199.001-112.471 271.529z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M656 496c17.673 0 32-14.327 32-32s-14.327-32-32-32h-98.496l105.623-105.623c12.497-12.497 12.497-32.758 0-45.255-12.496-12.497-32.758-12.497-45.254 0L512 386.995 406.627 281.623c-12.497-12.497-32.758-12.497-45.255 0s-12.497 32.758 0 45.255L466.495 432H368c-17.673 0-32 14.327-32 32s14.327 32 32 32h112v64H368c-17.673 0-32 14.327-32 32s14.327 32 32 32h112v96.25c0 17.673 14.327 32 32 32 17.673 0 32-14.327 32-32V624h112c17.673 0 32-14.327 32-32s-14.327-32-32-32H544v-64h112z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-card-->
<view a:if="{{name === 'line-card'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M864 128H160c-52.935 0-96 43.065-96 96v576c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V224c0-52.935-43.065-96-96-96z m32 672c0 17.645-14.355 32-32 32H160c-17.645 0-32-14.355-32-32V384h768v416z m0-480H128v-96c0-17.645 14.355-32 32-32h704c17.645 0 32 14.355 32 32v96z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M512 736h256c17.673 0 32-14.327 32-32s-14.327-32-32-32H512c-17.673 0-32 14.327-32 32s14.327 32 32 32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-applets-->
<view a:if="{{name === 'line-applets'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M869.276 256.694L554.957 75.432c-26.509-15.243-59.29-15.243-85.799 0L154.84 256.694c-26.39 15.243-42.84 43.72-42.84 74.088v362.405c0 30.486 16.45 58.845 42.84 74.206l314.319 181.143c13.254 7.681 28.047 11.462 42.84 11.462s29.704-3.781 42.84-11.462l314.318-181.261c26.391-15.243 42.84-43.72 42.84-74.088V330.782c0.237-30.486-16.212-58.845-42.721-74.088z m-16.213 74.088v171.336l-26.39-15.243-256.095-147.468 28.047-16.188 133.846-77.16 107.219 61.799c8.284 4.726 13.373 13.47 13.373 22.924zM512.117 651.12L270.934 512.161l240.473-138.605L752.59 512.634 512.117 651.12zM184.426 307.858l314.319-181.261c4.142-2.363 8.639-3.545 13.254-3.545s9.231 1.182 13.254 3.545l147.929 85.313-502.01 289.498V330.782c-0.001-9.453 5.088-18.197 13.254-22.924z m-13.255 385.446V569.588l40.592-23.396 241.183 139.077-161.065 92.876-107.337-61.917c-8.284-4.727-13.373-13.471-13.373-22.924z m668.519 22.924L525.371 897.489c-8.166 4.727-18.343 4.727-26.627 0l-147.81-85.195 142.84-82.359 27.456-15.834 290.413-167.436 41.183 23.751v122.889c0.237 9.452-4.852 18.196-13.136 22.923z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-commodity-->
<view a:if="{{name === 'line-commodity'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M832 96H192c-52.9 0-96 43.1-96 96v640c0 52.9 43.1 96 96 96h640c52.9 0 96-43.1 96-96V192c0-52.9-43.1-96-96-96z m32 736c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h640c17.6 0 32 14.4 32 32v640z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M733.8 224c-26.5 0-48 21.5-48 48 0 4 0.5 7.9 1.4 11.6-17.1 21.4-37.9 39.6-61.6 53.5C591.3 357.3 552 368 512 368s-79.3-10.7-113.6-30.9c-23.7-13.9-44.5-32.2-61.6-53.5 0.9-3.7 1.4-7.6 1.4-11.6 0-26.5-21.5-48-48-48s-48 21.5-48 48c0 24.3 18 44.3 41.4 47.5 22.5 29.2 50.4 53.9 82.3 72.7C410 418.3 460.5 432 512 432s102-13.7 146.1-39.7c31.8-18.8 59.8-43.6 82.3-72.7 23.4-3.2 41.4-23.3 41.4-47.5 0-26.6-21.5-48.1-48-48.1z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-assembly-->
<view a:if="{{name === 'line-assembly'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M830.8 534.4c-5.5-102-59.3-195.1-144.7-250.8 0.7-6.6 1.1-13.2 1.1-20 0-97-79-***********s-176 79-176 176c0 6.8 0.4 13.4 1.1 20C250.9 339.5 197 433 191.7 535.1c-62.1 27.2-105.5 89.3-105.5 161.3 0 97 79 176 176 176 39.3 0 75.7-13 105-34.9 44.5 22.4 93.8 34.2 144.1 34.2 50.6 0 100.2-11.9 144.9-34.6 29.4 22.2 66 35.3 105.6 35.3 97 0 176-79 176-176 0-72.6-44.1-135.1-107-162zM511.3 151.7c61.8 0 112 50.2 112 112s-50.2 112-***********-50.2-112-112 50.2-***********zM262.2 808.3c-61.8 0-112-50.2-112-112s50.2-*********** 112 50.2 112 112-50.3 112-112 112z m249.1-0.6c-34.1 0-67.7-6.8-98.8-19.8 16.3-26.7 25.7-58 25.7-91.6 0-97-79-***********-1.7 0-3.3 0-5 0.1 8.5-68.6 44.4-131 99.4-172.8 29.9 54.8 88 92 154.6 92 66.7 0 124.8-37.3 154.7-92 55 41.7 91 104 99.4 172.7h-3.5c-97 0-176 79-176 176 0 33.3 9.3 64.5 25.4 91.1-31.4 13.3-65.4 20.3-99.9 20.3z m250.5 0.6c-61.8 0-112-50.2-112-112s50.2-*********** 112 50.2 112 112-50.2 112-112 112z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-company-->
<view a:if="{{name === 'line-company'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M832 352H608V192c0-52.935-43.065-96-96-96H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V448c0-52.935-43.065-96-96-96zM544 864H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h320c17.645 0 32 14.355 32 32v672z m320-32c0 17.645-14.355 32-32 32H608V416h224c17.645 0 32 14.355 32 32v384z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M768 512h-64c-17.673 0-32 14.327-32 32s14.327 32 32 32h64c17.673 0 32-14.327 32-32s-14.327-32-32-32zM768 704h-64c-17.673 0-32 14.327-32 32s14.327 32 32 32h64c17.673 0 32-14.327 32-32s-14.327-32-32-32zM416 480H288c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h128c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM416 288H288c-17.673 0-32 14.327-32 32s14.327 32 32 32h128c17.673 0 32-14.327 32-32s-14.327-32-32-32zM416 672H288c-17.673 0-32 14.327-32 32s14.327 32 32 32h128c17.673 0 32-14.327 32-32s-14.327-32-32-32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-ancrown-->
<view a:if="{{name === 'line-ancrown'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M935.016 351.844c-20.479-17.255-48.429-21.38-72.947-10.719l-143.824 62.367-91.254-109.815c8.447-15.248 13.815-32.065 15.767-49.734 4.342-38.947-8.397-77.514-34.967-105.842-25.324-26.979-59.517-41.932-96.47-42.101-36.836 0.026-71.079 14.624-96.404 41.106-26.924 28.151-40.049 66.71-35.977 105.792 1.885 18.023 7.286 35.177 15.886 50.72l-91.221 109.789-141.451-61.903c-24.568-10.744-52.552-6.696-73.081 10.568-20.512 17.255-29.363 44.184-23.087 70.278l108.671 451.843C182.262 905.87 210.28 928 242.79 928h538.357c32.51 0 60.528-22.13 68.133-53.792l108.739-452.146c6.26-26.059-2.558-52.963-23.003-70.218zM781.146 858.91l-539.484-0.894-108.671-451.859 1.599-1.332 165.161 72.277c14.051 6.173 30.44 2.244 40.267-9.556l124.675-150.051c10.971-13.216 10.584-32.512-0.926-45.272-9.356-10.373-14.825-22.653-16.272-36.492-1.952-18.833 4.292-37.353 17.164-50.805 12.401-12.972 28.556-19.828 46.528-19.836 17.686 0.084 34.177 7.312 46.41 20.342 12.688 13.536 18.762 32.065 16.676 50.855-1.497 13.451-7.101 25.883-16.205 35.936-11.544 12.752-11.948 32.082-0.96 45.323l124.674 150.026c9.794 11.765 26.15 15.712 40.167 9.59L889.43 404.53l-13.681-31.703 15.263 33.043-109.866 453.04z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M322.766 587.142c-3.846-17.25-20.943-28.117-38.196-24.271-17.25 3.846-28.116 20.946-24.271 38.196l35 157c3.323 14.906 16.542 25.044 31.204 25.044a32.16 32.16 0 0 0 6.993-0.773c17.25-3.846 28.116-20.946 24.271-38.196l-35.001-157z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-code-->
<view a:if="{{name === 'line-code'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M960 350.4c0-0.3 0-0.5-0.1-0.7 0-0.3 0-0.5-0.1-0.8 0-0.3-0.1-0.6-0.1-0.9 0-0.2 0-0.4-0.1-0.6 0-0.3-0.1-0.7-0.2-1 0-0.2-0.1-0.4-0.1-0.5-0.1-0.3-0.1-0.7-0.2-1 0-0.2-0.1-0.3-0.1-0.5-0.1-0.3-0.2-0.6-0.3-1-0.1-0.2-0.1-0.4-0.2-0.6l-0.3-0.9c-0.1-0.2-0.1-0.4-0.2-0.6-0.1-0.3-0.2-0.5-0.3-0.8-0.1-0.2-0.2-0.5-0.3-0.7s-0.2-0.4-0.3-0.7c-0.1-0.3-0.2-0.5-0.4-0.8l-0.3-0.6c-0.1-0.3-0.3-0.6-0.4-0.9-0.1-0.2-0.2-0.3-0.3-0.5-0.2-0.3-0.3-0.6-0.5-0.9-0.1-0.1-0.2-0.3-0.3-0.4l-0.6-0.9c-0.1-0.1-0.2-0.3-0.3-0.4l-0.6-0.9c-0.1-0.1-0.2-0.3-0.3-0.4-0.2-0.3-0.4-0.5-0.6-0.8-0.1-0.2-0.3-0.3-0.5-0.5s-0.4-0.4-0.6-0.7c-0.3-0.3-0.6-0.6-0.8-0.9-0.1-0.1-0.2-0.2-0.2-0.3l-192-192c-0.1-0.1-0.2-0.2-0.3-0.2-0.3-0.3-0.6-0.6-0.9-0.8l-0.6-0.6c-0.2-0.2-0.4-0.3-0.5-0.5-0.3-0.2-0.5-0.4-0.8-0.6-0.1-0.1-0.3-0.2-0.4-0.3l-0.9-0.6c-0.1-0.1-0.3-0.2-0.4-0.3l-0.9-0.6c-0.1-0.1-0.3-0.2-0.4-0.3-0.3-0.2-0.6-0.3-0.9-0.5-0.2-0.1-0.3-0.2-0.5-0.3-0.3-0.2-0.6-0.3-0.9-0.4l-0.6-0.3c-0.3-0.1-0.5-0.2-0.8-0.4-0.2-0.1-0.4-0.2-0.7-0.3-0.2-0.1-0.5-0.2-0.7-0.3-0.3-0.1-0.5-0.2-0.8-0.3-0.2-0.1-0.4-0.1-0.6-0.2l-0.9-0.3c-0.2-0.1-0.4-0.1-0.6-0.2l-0.9-0.3c-0.2 0-0.4-0.1-0.5-0.1-0.3-0.1-0.7-0.2-1-0.2-0.2 0-0.4-0.1-0.6-0.1-0.3-0.1-0.7-0.1-1-0.2-0.2 0-0.4-0.1-0.6-0.1-0.3 0-0.6-0.1-0.9-0.1-0.3 0-0.6 0-0.9-0.1H160c-52.9 0-96 43.1-96 96v576c0 52.9 43.1 96 96 96h704c52.9 0 96-43.1 96-96V352v-1.6zM864 832H160c-17.6 0-32-14.4-32-32V224c0-17.6 14.4-32 32-32h562.7L896 365.3V800c0 17.6-14.4 32-32 32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M320 345.7c-15.3-8.8-34.9-3.6-43.7 11.7l-80 138.6s0 0.1-0.1 0.1c-0.2 0.4-0.4 0.7-0.6 1.1-0.1 0.1-0.1 0.3-0.2 0.4-0.1 0.3-0.3 0.6-0.4 0.9l-0.3 0.6-0.3 0.6c-0.1 0.3-0.2 0.6-0.4 0.9 0 0.1-0.1 0.2-0.1 0.3-0.1 0.4-0.3 0.8-0.4 1.2-0.9 3-1.4 6-1.5 9v1.4c0.1 3 0.6 6 1.5 9 0.1 0.4 0.3 0.8 0.4 1.2 0 0.1 0.1 0.2 0.1 0.3 0.1 0.3 0.2 0.6 0.4 0.9l0.3 0.6 0.3 0.6c0.1 0.3 0.3 0.6 0.4 0.9 0.1 0.1 0.1 0.3 0.2 0.4 0.2 0.4 0.4 0.7 0.6 1.1 0 0 0 0.1 0.1 0.1l80 138.6c5.9 10.3 16.7 16 27.7 16 5.4 0 10.9-1.4 16-4.3 15.3-8.8 20.5-28.4 11.7-43.7L261 512l70.8-122.6c8.7-15.3 3.5-34.8-11.8-43.7zM832 511.3c-0.1-3-0.6-6-1.5-9v-0.1c-0.1-0.4-0.3-0.8-0.4-1.2 0-0.1-0.1-0.2-0.1-0.4-0.1-0.3-0.2-0.6-0.4-0.9-0.1-0.2-0.2-0.4-0.3-0.7l-0.3-0.6c-0.1-0.3-0.3-0.6-0.4-0.9-0.1-0.1-0.1-0.3-0.2-0.4-0.2-0.4-0.4-0.7-0.6-1.1 0 0 0-0.1-0.1-0.1l-80-138.6c-8.8-15.3-28.4-20.6-43.7-11.7-15.3 8.8-20.5 28.4-11.7 43.7L763 512l-70.8 122.6c-8.8 15.3-3.6 34.9 11.7 43.7 5 2.9 10.5 4.3 16 4.3 11.1 0 21.8-5.7 27.7-16l80-138.6s0-0.1 0.1-0.1c0.2-0.4 0.4-0.7 0.6-1.1 0.1-0.1 0.1-0.3 0.2-0.4 0.1-0.3 0.3-0.6 0.4-0.9l0.3-0.6 0.3-0.6c0.1-0.3 0.2-0.6 0.4-0.9 0-0.1 0.1-0.2 0.1-0.4 0.1-0.4 0.3-0.8 0.4-1.2 0.9-2.9 1.4-6 1.5-9v-0.7c0.1-0.3 0.1-0.6 0.1-0.8zM608 345.7c-15.3-8.8-34.9-3.6-43.7 11.7l-160 277.1c-8.8 15.3-3.6 34.9 11.7 43.7 5 2.9 10.5 4.3 16 4.3 11.1 0 21.8-5.7 27.7-16l160-277.1c8.8-15.3 3.6-34.8-11.7-43.7z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-collection-->
<view a:if="{{name === 'line-collection'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M416 896H224c-17.6 0-32-14.4-32-32V160c0-17.6 14.4-32 32-32h576c17.6 0 32 14.4 32 32v256c0 17.7 14.3 32 32 32s32-14.3 32-32V160c0-52.9-43.1-96-96-96H224c-52.9 0-96 43.1-96 96v704c0 52.9 43.1 96 96 96h192c17.7 0 32-14.3 32-32s-14.3-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M352 256c-17.7 0-32 14.3-32 32s14.3 32 32 32h320c17.7 0 32-14.3 32-32s-14.3-32-32-32H352zM544 480H352c-17.7 0-32 14.3-32 32s14.3 32 32 32h192c17.7 0 32-14.3 32-32s-14.3-32-32-32zM928 800h-96v-64h96c17.7 0 32-14.3 32-32s-14.3-32-32-32h-82.7l67.9-67.9c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L800 626.7l-67.9-67.9c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l67.9 67.9H672c-17.7 0-32 14.3-32 32s14.3 32 32 32h96v64h-96c-17.7 0-32 14.3-32 32s14.3 32 32 32h96v64c0 17.7 14.3 32 32 32s32-14.3 32-32v-64h96c17.7 0 32-14.3 32-32s-14.3-32-32-32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-contacts-->
<view a:if="{{name === 'line-contacts'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M800 64H288c-52.935 0-96 43.065-96 96v96h-32c-17.673 0-32 14.327-32 32s14.327 32 32 32h32v384h-32c-17.673 0-32 14.327-32 32s14.327 32 32 32h32v96c0 52.935 43.065 96 96 96h512c52.935 0 96-43.065 96-96V160c0-52.935-43.065-96-96-96z m32 800c0 17.645-14.355 32-32 32H288c-17.645 0-32-14.355-32-32v-96h32c17.673 0 32-14.327 32-32s-14.327-32-32-32h-32V320h32c17.673 0 32-14.327 32-32s-14.327-32-32-32h-32v-96c0-17.645 14.355-32 32-32h512c17.645 0 32 14.355 32 32v704z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M736 256c-17.673 0-32 14.327-32 32v448c0 17.673 14.327 32 32 32s32-14.327 32-32V288c0-17.673-14.327-32-32-32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-discount-->
<view a:if="{{name === 'line-discount'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M928 448c3.314 0 6.51-0.504 9.516-1.439C950.542 442.51 960 430.36 960 416V256c0-52.935-43.065-96-96-96H160c-52.935 0-96 43.065-96 96v160c0 17.673 14.327 32 32 32 35.29 0 64 28.71 64 64s-28.71 64-64 64c-14.36 0-26.51 9.458-30.561 22.484A31.989 31.989 0 0 0 64 608v160c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V608c0-17.673-14.327-32-32-32-35.29 0-64-28.71-64-64s28.71-64 64-64z m-32 187.95V768c0 17.645-14.355 32-32 32H160c-17.645 0-32-14.355-32-32V635.95c55.145-14.245 96-64.417 96-123.95 0-59.534-40.855-109.705-96-123.95V256c0-17.645 14.355-32 32-32h704c17.645 0 32 14.355 32 32v132.05c-55.145 14.245-96 64.416-96 123.95s40.855 109.705 96 123.95z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M384 352c-17.673 0-32 14.327-32 32v256c0 17.673 14.327 32 32 32s32-14.327 32-32V384c0-17.673-14.327-32-32-32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-deleteuser-->
<view a:if="{{name === 'line-deleteuser'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M731.701 579.397c8.85-15.298 3.622-34.873-11.676-43.723-24.291-14.052-49.921-25.536-76.437-34.357a226.986 226.986 0 0 0 26.803-22.925C712.7 436.084 736 379.833 736 320s-23.3-116.084-65.608-158.392C628.084 119.3 571.832 96 512 96c-59.833 0-116.084 23.3-158.393 65.608C311.3 203.916 288 260.167 288 320s23.3 116.084 65.607 158.392a226.96 226.96 0 0 0 26.734 22.875 416.054 416.054 0 0 0-30.278 11.437c-49.541 20.954-94.026 50.945-132.221 89.14s-68.185 82.68-89.139 132.221C107.003 785.371 96 839.854 96 896c0 17.673 14.327 32 32 32s32-14.327 32-32c0-94.022 36.614-182.418 103.099-248.901C329.582 580.614 417.978 544 512 544c61.892 0 122.744 16.277 175.979 47.073 15.299 8.851 34.874 3.621 43.722-11.676zM352 320c0-88.224 71.775-160 160-160s160 71.776 160 160-71.775 160-160 160-160-71.776-160-160zM836.255 791l82.373-82.372c12.497-12.497 12.497-32.759 0.001-45.255-12.499-12.499-32.76-12.496-45.255-0.001L791 745.745l-82.374-82.373c-12.496-12.496-32.757-12.497-45.255 0.001-12.496 12.496-12.496 32.758 0.001 45.255L745.745 791l-82.373 82.372c-12.497 12.497-12.497 32.759-0.001 45.255 6.25 6.25 14.438 9.373 22.628 9.373 8.188 0 16.38-3.125 22.627-9.372L791 836.255l82.374 82.373C879.621 924.876 887.811 928 896 928s16.379-3.124 22.628-9.373c12.496-12.496 12.496-32.758-0.001-45.255L836.255 791z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-delete-->
<view a:if="{{name === 'line-delete'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M928 224H704v-64c0-52.935-43.065-96-96-96H416c-52.935 0-96 43.065-96 96v64H96c-17.673 0-32 14.327-32 32s14.327 32 32 32h96v576c0 52.935 43.065 96 96 96h448c52.935 0 96-43.065 96-96V288h96c17.673 0 32-14.327 32-32s-14.327-32-32-32z m-544-64c0-17.645 14.355-32 32-32h192c17.645 0 32 14.355 32 32v64H384v-64z m384 704c0 17.645-14.355 32-32 32H288c-17.645 0-32-14.355-32-32V288h512v576z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M448 720V464c0-17.673-14.327-32-32-32s-32 14.327-32 32v256c0 17.673 14.327 32 32 32s32-14.327 32-32zM640 720V464c0-17.673-14.327-32-32-32s-32 14.327-32 32v256c0 17.673 14.327 32 32 32s32-14.327 32-32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-cooperation-->
<view a:if="{{name === 'line-cooperation'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M933.198 390.894L733.134 190.816c-16.316-16.316-38.009-25.302-61.081-25.302-23.073 0-44.766 8.986-61.081 25.302l-58.96 58.963-98.975-98.978c-16.312-16.316-38.002-25.302-61.075-25.302s-44.764 8.985-61.08 25.302L90.802 390.894C74.485 407.21 65.5 428.903 65.5 451.977c0 23.074 8.985 44.767 25.302 61.083l360.114 360.139C467.232 889.515 488.925 898.5 512 898.5c23.074 0 44.765-8.986 61.078-25.302L933.199 513.06c33.681-33.682 33.68-88.485-0.001-122.166zM670.992 570.961c-5.629 5.627-8.729 13.109-8.729 21.068 0 7.958 3.1 15.441 8.729 21.069l38.948 38.953-37.888 37.894-78.964-78.968c-11.616-11.616-30.521-11.617-42.137 0-5.629 5.627-8.729 13.108-8.729 21.067s3.1 15.441 8.729 21.068l78.964 78.97-37.888 37.893-118.98-118.982c-11.617-11.615-30.52-11.614-42.137 0-5.629 5.627-8.729 13.108-8.729 21.067s3.1 15.44 8.729 21.067l118.979 118.986-18.944 18.947c-10.447 10.444-27.446 10.445-37.895 0L132.939 470.924c-5.062-5.062-7.85-11.791-7.85-18.947 0-7.157 2.788-13.886 7.85-18.948l240.074-240.091c5.225-5.223 12.085-7.833 18.948-7.833 6.861 0 13.724 2.612 18.947 7.833l98.976 98.979-38.956 38.954c-16.316 16.316-25.302 38.009-25.302 61.084s8.985 44.768 25.302 61.083l40.016 40.014c16.312 16.316 38.002 25.302 61.074 25.302s44.764-8.985 61.079-25.302l38.955-38.954 98.976 98.977c10.443 10.447 10.443 27.445 0 37.893l-18.951 18.948-38.955-38.956c-5.625-5.627-13.105-8.727-21.062-8.727s-15.44 3.101-21.068 8.728z m1.06-188.794c-7.956 0-15.438 3.099-21.064 8.726l-60.021 60.024c-10.447 10.445-27.445 10.445-37.895 0L513.064 410.9c-5.062-5.061-7.85-11.789-7.85-18.946s2.788-13.886 7.85-18.947l140.044-140.054c5.059-5.061 11.786-7.848 18.943-7.848s13.885 2.787 18.943 7.848l200.065 200.075c5.062 5.062 7.85 11.792 7.85 18.948s-2.787 13.886-7.85 18.948l-58.96 58.963-58.952-58.963-80.032-80.03c-5.625-5.627-13.105-8.727-21.062-8.728l-0.001 0.001z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-collect-->
<view a:if="{{name === 'line-collect'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M958.414 407.625a32 32 0 0 0-25.763-21.712l-270.749-39.775L540.757 97.962a32.002 32.002 0 0 0-57.514 0L362.097 346.137 91.349 385.913a32 32 0 0 0-17.81 54.452l196.123 193.281-46.313 273.002a32.003 32.003 0 0 0 31.552 37.353c5.14 0 10.301-1.237 15.016-3.743L512 811.583l242.083 128.674a32 32 0 0 0 46.568-33.61l-46.313-273.002 196.124-193.281a32 32 0 0 0 7.952-32.739zM697.538 599.766a32.002 32.002 0 0 0-9.087 28.145l38.221 225.297L527.02 747.087a31.994 31.994 0 0 0-15.02-3.743 31.99 31.99 0 0 0-15.019 3.743l-199.652 106.12 38.22-225.297a32 32 0 0 0-9.088-28.145L164.236 439.892 388.1 407.004a32 32 0 0 0 24.105-17.623L512 184.947l99.794 204.434a32.001 32.001 0 0 0 24.105 17.623l223.864 32.888-162.225 159.874z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-download-->
<view a:if="{{name === 'line-download'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M896 480c-17.673 0-32 14.327-32 32v320c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V512c0-17.673-14.327-32-32-32s-32 14.327-32 32v320c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V512c0-17.673-14.327-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M234.928 440.083l256 224c0.1 0.087 0.203 0.168 0.304 0.254 0.254 0.217 0.508 0.432 0.767 0.64 0.127 0.102 0.257 0.199 0.386 0.299l0.202 0.157c0.103 0.079 0.204 0.161 0.308 0.239 0.298 0.223 0.602 0.434 0.907 0.645 0.14 0.097 0.279 0.196 0.42 0.291 0.288 0.193 0.58 0.377 0.873 0.56 0.151 0.094 0.301 0.19 0.453 0.281 0.085 0.051 0.168 0.103 0.253 0.153 0.21 0.123 0.422 0.239 0.634 0.357 0.138 0.077 0.274 0.156 0.413 0.231 0.127 0.068 0.254 0.134 0.382 0.201 0.195 0.101 0.39 0.198 0.586 0.295 0.125 0.062 0.247 0.127 0.372 0.187 0.177 0.085 0.356 0.163 0.534 0.244 0.167 0.076 0.334 0.151 0.502 0.224l0.124 0.056c0.072 0.031 0.142 0.065 0.214 0.096 0.249 0.105 0.501 0.2 0.752 0.299 0.116 0.045 0.231 0.093 0.348 0.137 0.101 0.038 0.201 0.08 0.302 0.117 0.133 0.049 0.269 0.091 0.403 0.138 0.334 0.118 0.669 0.234 1.006 0.34 0.202 0.064 0.407 0.12 0.611 0.18 0.275 0.081 0.549 0.161 0.825 0.234 0.221 0.058 0.445 0.111 0.668 0.164 0.266 0.064 0.532 0.127 0.799 0.184a38.923 38.923 0 0 0 1.5 0.282 29.71 29.71 0 0 0 1.55 0.215c0.204 0.023 0.407 0.048 0.612 0.067 0.342 0.033 0.685 0.057 1.027 0.078 0.164 0.01 0.326 0.024 0.49 0.032a32.6 32.6 0 0 0 1.532 0.039H512.013c0.51 0 1.021-0.015 1.531-0.039 0.166-0.008 0.331-0.022 0.496-0.033 0.34-0.021 0.68-0.045 1.019-0.078 0.209-0.02 0.416-0.044 0.623-0.068 0.291-0.033 0.581-0.069 0.871-0.111 0.227-0.032 0.452-0.068 0.677-0.104 0.269-0.044 0.537-0.093 0.805-0.144a26.758 26.758 0 0 0 1.472-0.318c0.231-0.055 0.461-0.109 0.69-0.17 0.265-0.07 0.528-0.148 0.791-0.225 0.215-0.063 0.432-0.122 0.645-0.19 0.313-0.099 0.623-0.208 0.934-0.316 0.158-0.055 0.318-0.105 0.475-0.163 0.099-0.036 0.198-0.078 0.297-0.116 0.115-0.043 0.228-0.09 0.342-0.135 0.25-0.098 0.502-0.193 0.75-0.297 0.098-0.042 0.194-0.088 0.292-0.131l0.055-0.025c0.166-0.072 0.331-0.147 0.496-0.222 0.177-0.081 0.355-0.158 0.531-0.243 0.132-0.063 0.26-0.131 0.391-0.196 0.191-0.094 0.381-0.189 0.57-0.287 0.127-0.066 0.253-0.131 0.379-0.199 0.145-0.078 0.287-0.161 0.431-0.241 0.207-0.115 0.414-0.228 0.619-0.348 0.084-0.049 0.167-0.101 0.251-0.152 0.159-0.095 0.314-0.194 0.471-0.292 0.287-0.179 0.572-0.359 0.854-0.548 0.149-0.1 0.295-0.204 0.442-0.306 0.298-0.206 0.595-0.413 0.887-0.631 0.115-0.086 0.225-0.176 0.339-0.263l0.16-0.124c0.135-0.105 0.271-0.207 0.405-0.315 0.254-0.204 0.504-0.416 0.753-0.629 0.104-0.089 0.211-0.172 0.314-0.262l256-224c13.3-11.638 14.648-31.854 3.01-45.155s-31.854-14.647-45.154-3.01L544 569.48V128c0-17.673-14.327-32-32-32-17.673 0-32 14.327-32 32v441.48L277.072 391.917c-13.301-11.638-33.518-10.29-45.155 3.01-11.637 13.301-10.29 33.518 3.011 45.156z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-examine1-->
<view a:if="{{name === 'line-examine1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M416 896H224c-17.6 0-32-14.4-32-32V160c0-17.6 14.4-32 32-32h576c17.6 0 32 14.4 32 32v256c0 17.7 14.3 32 32 32s32-14.3 32-32V160c0-52.9-43.1-96-96-96H224c-52.9 0-96 43.1-96 96v704c0 52.9 43.1 96 96 96h192c17.7 0 32-14.3 32-32s-14.3-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M704 288c0-17.7-14.3-32-32-32H352c-17.7 0-32 14.3-32 32s14.3 32 32 32h320c17.7 0 32-14.3 32-32zM352 480c-17.7 0-32 14.3-32 32s14.3 32 32 32h192c17.7 0 32-14.3 32-32s-14.3-32-32-32H352zM864 768h-96v-37.5c37.2-13.2 64-48.8 64-90.5 0-52.9-43.1-96-96-96s-96 43.1-96 96c0 41.7 26.8 77.3 64 90.5V768h-96c-52.9 0-96 43.1-96 96v32c0 35.3 28.7 64 64 64h320c35.3 0 64-28.7 64-64v-32c0-52.9-43.1-96-96-96zM736 608c17.6 0 32 14.4 32 32s-14.4 32-32 32-32-14.4-32-32 14.4-32 32-32z m160 288H576v-32c0-17.6 14.4-32 32-32h256c17.6 0 32 14.4 32 32v32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-express-->
<view a:if="{{name === 'line-express'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M352.715 574.459h-0.002zM352.693 381.336c-17.861 0.011-32.332 14.474-32.32 32.304v95.655H223.91c-17.861 0.011-32.332 14.474-32.32 32.304s14.5 32.274 32.361 32.263h128.764v0.597c17.86-0.001 32.339-14.455 32.339-32.284V413.639v-0.04c-0.011-17.83-14.5-32.274-32.361-32.263z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M960 799.245V188.85c0.063-51.675-40.527-94.298-92.231-96.85H400.625c-44.322 0-80.253 35.867-80.253 80.111v93.263L124.531 363.42h-4.192c-36.29 25.543-57.41 67.485-56.297 111.797v322.374a32.507 32.507 0 0 0-0.042 1.656c0 17.829 14.454 32.283 32.283 32.283h100.318v0.001c17.861 71.098 90.079 114.281 161.304 96.451 47.56-11.906 84.695-48.975 96.622-96.451h126.967c19.184 70.07 91.64 111.348 161.834 92.197 46.587-12.71 80.46-48.847 92.373-92.198h92.017c17.829 0 32.283-14.454 32.283-32.283l-0.001-0.002z m-633.04 65.167h-2.396c-37.353-1.321-66.561-32.618-65.238-69.905 1.323-37.287 32.676-66.443 70.029-65.122 36.405 1.287 65.257 31.108 65.28 67.471 0.001 37.31-30.298 67.556-67.675 67.556z m247.347-97.448h-119.78c-17.861-71.098-90.079-114.281-161.304-96.451-49.213 12.32-85.069 50.547-96.642 96.451h-67.857V477.608c-1.726-23.196 8.415-45.69 26.951-59.784L367.087 313.8h3.593l5.39-4.185 3.593-4.783a26.297 26.297 0 0 0 0-5.381 31.02 31.02 0 0 0 0-6.576c0.093-1.593 0.093-3.19 0-4.783V172.111c0-8.915 7.24-16.142 16.17-16.142h467.144c17.861-0.003 32.343 14.448 32.346 32.278 0 0.201-0.002 0.402-0.006 0.603v578.114h-58.424c-0.374-1.595-0.768-3.19-1.204-4.783-15.671-57.234-67.79-96.909-127.228-96.85h-3.593c-62.071-1.056-116.461 41.282-130.561 101.633z m131.159 97.448c-37.376 0-67.676-30.246-67.676-67.556s30.3-67.556 67.676-67.556c37.376 0 67.676 30.246 67.676 67.556s-30.299 67.556-67.676 67.556z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-eye-->
<view a:if="{{name === 'line-eye'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M800 512c0-46.9-32.9-89.3-92.7-119.1-52.7-26.4-122-40.9-195.3-40.9s-142.6 14.5-195.3 40.9C256.9 422.7 224 465.1 224 512s32.9 89.3 92.7 119.1c52.7 26.4 122 40.9 195.3 40.9s142.6-14.5 195.3-40.9C767.1 601.3 800 558.9 800 512z m-512 0c0-45.3 95.8-96 224-96s224 50.7 224 96-95.8 96-224 96-224-50.7-224-96zM896 608c-17.7 0-32 14.3-32 32v192c0 17.6-14.4 32-32 32H640c-17.7 0-32 14.3-32 32s14.3 32 32 32h192c52.9 0 96-43.1 96-96V640c0-17.7-14.3-32-32-32zM832 96H640c-17.7 0-32 14.3-32 32s14.3 32 32 32h192c17.6 0 32 14.4 32 32v192c0 17.7 14.3 32 32 32s32-14.3 32-32V192c0-52.9-43.1-96-96-96zM128 416c17.7 0 32-14.3 32-32V192c0-17.6 14.4-32 32-32h192c17.7 0 32-14.3 32-32s-14.3-32-32-32H192c-52.9 0-96 43.1-96 96v192c0 17.7 14.3 32 32 32zM384 864H192c-17.6 0-32-14.4-32-32V640c0-17.7-14.3-32-32-32s-32 14.3-32 32v192c0 52.9 43.1 96 96 96h192c17.7 0 32-14.3 32-32s-14.3-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M512 464c-26.5 0-48 21.5-48 48s21.5 48 48 48 48-21.5 48-48-21.5-48-48-48z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-healthreport-->
<view a:if="{{name === 'line-healthreport'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M800 64H224c-52.935 0-96 43.065-96 96v704c0 52.935 43.065 96 96 96h576c52.935 0 96-43.065 96-96V160c0-52.935-43.065-96-96-96z m32 800c0 17.645-14.355 32-32 32H224c-17.645 0-32-14.355-32-32V160c0-17.645 14.355-32 32-32h576c17.645 0 32 14.355 32 32v704z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M672 193c-23.854 0-46.377 8.646-64 24.448C590.377 201.646 567.854 193 544 193c-25.643 0-49.751 9.986-67.883 28.118-37.43 37.43-37.43 98.333 0 135.764l0.255-0.255 109 109C591.621 471.876 599.811 475 608 475s16.379-3.124 22.628-9.373L736.23 360.025a32.14 32.14 0 0 0 3.653-3.144c37.43-37.43 37.43-98.333 0-135.764C721.751 202.986 697.643 193 672 193z m24.894 116.105a32.257 32.257 0 0 0-2.521 2.267L608 397.745l-86.372-86.372a32.257 32.257 0 0 0-2.521-2.267c-10.155-12.555-9.402-31.065 2.266-42.733C527.417 260.329 535.452 257 544 257c7.408 0 14.43 2.504 20.105 7.105 0.704 0.87 1.458 1.713 2.268 2.522l19 19C591.621 291.876 599.811 295 608 295s16.379-3.124 22.627-9.373l19-19a32.487 32.487 0 0 0 2.268-2.522C657.57 259.504 664.592 257 672 257c8.548 0 16.583 3.329 22.627 9.373 11.668 11.668 12.422 30.177 2.267 42.732zM544 768H288c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h256c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM288 704h128c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32H288c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-file-->
<view a:if="{{name === 'line-file'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M800 64H352c-52.935 0-96 43.065-96 96v32h-32c-52.935 0-96 43.065-96 96v576c0 52.935 43.065 96 96 96h448c52.935 0 96-43.065 96-96v-32h32c52.935 0 96-43.065 96-96V160c0-52.935-43.065-96-96-96z m-96 800c0 17.645-14.355 32-32 32H224c-17.645 0-32-14.355-32-32V288c0-17.645 14.355-32 32-32h448c17.645 0 32 14.355 32 32v576z m128-128c0 17.645-14.355 32-32 32h-32V288c0-52.935-43.065-96-96-96H320v-32c0-17.645 14.355-32 32-32h448c17.645 0 32 14.355 32 32v576z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M608 383.99H288c-17.673 0-32 14.327-32 32s14.327 32 32 32h320c17.673 0 32-14.327 32-32s-14.327-32-32-32zM480 576H288c-17.673 0-32 14.327-32 32s14.327 32 32 32h192c17.673 0 32-14.327 32-32s-14.327-32-32-32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-location-->
<view a:if="{{name === 'line-location'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M860.462 299.156c-18.925-46.791-46.715-88.76-82.599-124.738-35.881-35.977-77.735-63.839-124.398-82.812C608.412 73.288 560.816 64 512 64s-96.411 9.288-141.465 27.606c-46.664 18.974-88.517 46.835-124.399 82.812-35.883 35.978-63.673 77.947-82.598 124.738C145.265 344.336 136 392.066 136 441.02s9.265 96.683 27.538 141.864c18.925 46.791 46.715 88.759 82.598 124.739l0.011 0.01 241.692 242.331c0.308 0.309 0.625 0.603 0.942 0.897 0.108 0.101 0.213 0.208 0.322 0.307 0.315 0.285 0.638 0.556 0.96 0.827 0.121 0.102 0.238 0.21 0.361 0.311 0.302 0.248 0.612 0.48 0.92 0.715 0.15 0.114 0.295 0.235 0.445 0.345 0.294 0.216 0.594 0.419 0.893 0.625 0.171 0.118 0.339 0.242 0.511 0.357 0.311 0.206 0.629 0.399 0.945 0.593 0.166 0.102 0.327 0.21 0.493 0.31 0.336 0.199 0.679 0.385 1.021 0.571 0.152 0.083 0.299 0.173 0.452 0.253 0.328 0.173 0.661 0.332 0.993 0.493 0.171 0.083 0.339 0.173 0.511 0.253 0.351 0.164 0.707 0.313 1.063 0.464 0.156 0.066 0.31 0.139 0.467 0.204 0.346 0.141 0.698 0.268 1.047 0.397 0.17 0.062 0.338 0.133 0.508 0.193 0.393 0.138 0.79 0.262 1.188 0.385 0.129 0.04 0.256 0.087 0.387 0.125 0.375 0.111 0.753 0.208 1.13 0.307 0.156 0.041 0.31 0.088 0.466 0.126 0.432 0.106 0.865 0.197 1.299 0.285 0.104 0.021 0.205 0.047 0.309 0.068 0.438 0.085 0.878 0.155 1.318 0.223 0.101 0.016 0.202 0.036 0.303 0.051a34.564 34.564 0 0 0 1.631 0.196c0.435 0.042 0.871 0.069 1.306 0.094 0.111 0.006 0.221 0.018 0.332 0.024a34.151 34.151 0 0 0 4.917-0.118l0.013-0.001a33.956 33.956 0 0 0 16.926-6.478c0.064-0.047 0.125-0.1 0.189-0.147 0.395-0.296 0.788-0.597 1.173-0.912 0.107-0.088 0.209-0.183 0.316-0.272a33.89 33.89 0 0 0 1.003-0.864c0.172-0.156 0.338-0.324 0.507-0.483 0.253-0.239 0.51-0.474 0.757-0.721l241.701-242.341c35.885-35.979 63.674-77.948 82.599-124.739C878.736 537.703 888 489.974 888 441.019c0-48.953-9.265-96.683-27.538-141.863zM729.555 659.087l-0.015 0.014L512 877.217 294.47 659.112l-0.01-0.011c-58.101-58.254-90.097-135.702-90.097-218.081s31.997-159.829 90.097-218.083c58.105-58.258 135.362-90.344 217.539-90.344 82.178 0 159.435 32.086 217.541 90.345 58.099 58.253 90.096 135.702 90.096 218.081 0 82.374-31.992 159.817-90.081 218.068z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M512 219.346c-94.239 0-170.909 76.927-170.909 171.483S417.761 562.312 512 562.312c94.24 0 170.909-76.927 170.909-171.483S606.24 219.346 512 219.346z m0 274.373c-56.544 0-102.545-46.156-102.545-102.89S455.456 287.939 512 287.939s102.545 46.156 102.545 102.89S568.544 493.719 512 493.719z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-list-->
<view a:if="{{name === 'line-list'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M384 96H192c-52.935 0-96 43.065-96 96v192c0 52.935 43.065 96 96 96h192c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m32 288c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h192c17.645 0 32 14.355 32 32v192zM384 544H192c-52.935 0-96 43.065-96 96v192c0 52.935 43.065 96 96 96h192c52.935 0 96-43.065 96-96V640c0-52.935-43.065-96-96-96z m32 288c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V640c0-17.645 14.355-32 32-32h192c17.645 0 32 14.355 32 32v192zM896 160H576c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h320c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM896 352H576c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h320c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM896 608H576c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h320c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM896 800H576c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h320c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-open-->
<view a:if="{{name === 'line-open'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M864 160H160c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h704c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM864 480H160c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h704c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM864 800H160c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h704c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-find-->
<view a:if="{{name === 'line-find'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M924.781 337.608c-22.566-53.352-54.864-101.259-95.997-142.393-41.134-41.133-89.041-73.431-142.393-95.997C631.14 75.85 572.465 64 512 64S392.86 75.85 337.608 99.219c-53.352 22.565-101.259 54.864-142.393 95.997-41.133 41.133-73.431 89.041-95.997 142.392C75.85 392.861 64 451.535 64 512c0 60.466 11.85 119.14 35.219 174.392 22.565 53.352 54.864 101.26 95.997 142.393s89.041 73.431 142.392 95.997C392.861 948.15 451.535 960 512 960c60.466 0 119.14-11.85 174.392-35.219 53.352-22.566 101.26-54.864 142.393-95.997 41.133-41.134 73.431-89.041 95.997-142.393C948.15 631.14 960 572.465 960 512s-11.85-119.14-35.219-174.392zM783.529 783.529C711.001 856.057 614.57 896 512 896s-199.001-39.943-271.529-112.471S128 614.57 128 512s39.943-199 112.471-271.529C312.999 167.943 409.43 128 512 128s199.001 39.943 271.529 112.471S896 409.43 896 512s-39.943 199.001-112.471 271.529z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M725.015 330.202c-0.007-0.293-0.024-0.584-0.039-0.876-0.012-0.226-0.02-0.451-0.036-0.677-0.024-0.333-0.06-0.665-0.095-0.996-0.02-0.185-0.034-0.37-0.057-0.555-0.048-0.39-0.108-0.777-0.17-1.163-0.021-0.128-0.036-0.255-0.058-0.383a32.24 32.24 0 0 0-0.294-1.486l-0.011-0.053a32.051 32.051 0 0 0-1.411-4.63c-0.082-0.21-0.172-0.415-0.257-0.622-0.11-0.265-0.217-0.531-0.333-0.794-0.14-0.316-0.29-0.624-0.439-0.933-0.071-0.146-0.138-0.294-0.211-0.44a31.156 31.156 0 0 0-0.568-1.07c-0.05-0.091-0.097-0.182-0.148-0.272a31.793 31.793 0 0 0-0.655-1.105l-0.13-0.213a32.374 32.374 0 0 0-0.705-1.07l-0.155-0.227a31.802 31.802 0 0 0-0.714-0.978c-0.075-0.099-0.149-0.199-0.225-0.297a31.139 31.139 0 0 0-0.683-0.846c-0.111-0.133-0.221-0.267-0.334-0.398a36.594 36.594 0 0 0-1.091-1.206 30.9 30.9 0 0 0-0.507-0.517 30.061 30.061 0 0 0-0.65-0.637c-0.126-0.119-0.254-0.236-0.382-0.353a32.073 32.073 0 0 0-0.826-0.735c-0.084-0.072-0.169-0.142-0.253-0.212-0.325-0.272-0.654-0.54-0.991-0.8l-0.143-0.108c-0.37-0.283-0.747-0.559-1.131-0.826l-0.061-0.042a32.452 32.452 0 0 0-1.263-0.831 32.26 32.26 0 0 0-1.287-0.763l-0.061-0.033c-0.42-0.234-0.845-0.461-1.278-0.678-0.059-0.03-0.12-0.057-0.179-0.086a33.345 33.345 0 0 0-1.192-0.561c-0.142-0.063-0.287-0.12-0.431-0.181-0.325-0.138-0.649-0.277-0.98-0.405-0.34-0.132-0.685-0.252-1.03-0.373-0.137-0.048-0.27-0.102-0.409-0.148a31.952 31.952 0 0 0-3.124-0.869c-0.112-0.025-0.225-0.044-0.337-0.068a32.58 32.58 0 0 0-1.207-0.239c-0.182-0.032-0.363-0.055-0.545-0.083-0.334-0.052-0.668-0.105-1.005-0.147-0.221-0.027-0.442-0.046-0.664-0.069-0.297-0.03-0.594-0.063-0.893-0.085-0.245-0.018-0.49-0.027-0.735-0.04-0.276-0.014-0.551-0.03-0.827-0.037-0.264-0.007-0.526-0.004-0.79-0.004-0.258 0-0.515-0.003-0.773 0.004-0.283 0.007-0.565 0.024-0.848 0.038-0.238 0.012-0.475 0.021-0.713 0.038-0.308 0.023-0.614 0.056-0.92 0.087-0.213 0.022-0.425 0.04-0.638 0.066-0.348 0.043-0.694 0.097-1.039 0.152-0.17 0.027-0.339 0.048-0.509 0.078-0.421 0.073-0.839 0.159-1.256 0.248-0.096 0.021-0.192 0.037-0.288 0.058a31.98 31.98 0 0 0-3.129 0.871l-271.387 90.462c-0.545 0.179-1.084 0.373-1.618 0.581-0.222 0.086-0.438 0.181-0.656 0.271-0.253 0.105-0.507 0.206-0.757 0.318-0.328 0.145-0.648 0.301-0.969 0.456-0.134 0.065-0.27 0.127-0.403 0.194-0.374 0.187-0.74 0.384-1.103 0.585-0.079 0.044-0.159 0.085-0.238 0.129a32.35 32.35 0 0 0-1.142 0.677l-0.173 0.106c-0.376 0.236-0.745 0.481-1.11 0.732-0.061 0.042-0.124 0.083-0.185 0.126-0.347 0.242-0.687 0.492-1.022 0.747-0.083 0.063-0.167 0.125-0.249 0.189-0.303 0.235-0.6 0.476-0.893 0.72-0.117 0.098-0.235 0.195-0.351 0.294-0.25 0.215-0.494 0.434-0.737 0.656-0.157 0.143-0.313 0.286-0.467 0.432-0.194 0.184-0.383 0.372-0.572 0.561-0.195 0.195-0.389 0.39-0.579 0.59-0.139 0.147-0.276 0.296-0.412 0.446-0.229 0.25-0.455 0.502-0.676 0.759-0.093 0.108-0.183 0.218-0.274 0.327-0.251 0.301-0.498 0.605-0.739 0.916l-0.171 0.226c-0.261 0.343-0.516 0.691-0.764 1.046l-0.107 0.157c-0.257 0.372-0.507 0.75-0.749 1.135l-0.09 0.147c-0.238 0.383-0.47 0.772-0.692 1.167-0.039 0.069-0.075 0.139-0.113 0.208-0.206 0.373-0.408 0.749-0.6 1.133-0.061 0.121-0.117 0.245-0.177 0.367-0.161 0.332-0.322 0.664-0.472 1.004-0.105 0.235-0.2 0.475-0.299 0.713-0.097 0.233-0.197 0.463-0.289 0.699-0.206 0.53-0.399 1.065-0.577 1.606l-90.461 271.384a32.106 32.106 0 0 0-0.879 3.156c-0.009 0.042-0.016 0.084-0.026 0.126-0.103 0.469-0.198 0.941-0.28 1.416-0.024 0.137-0.041 0.273-0.063 0.41-0.06 0.378-0.119 0.756-0.166 1.136-0.024 0.193-0.039 0.386-0.06 0.578-0.034 0.324-0.069 0.647-0.093 0.973-0.017 0.23-0.025 0.46-0.037 0.69-0.015 0.289-0.032 0.577-0.039 0.866-0.006 0.26-0.003 0.519-0.004 0.779 0 0.259-0.003 0.518 0.004 0.779 0.007 0.29 0.024 0.578 0.039 0.866 0.012 0.23 0.02 0.46 0.037 0.69 0.024 0.325 0.059 0.649 0.093 0.973 0.02 0.193 0.036 0.386 0.06 0.579 0.047 0.381 0.106 0.759 0.166 1.136 0.022 0.137 0.039 0.273 0.063 0.41 0.082 0.475 0.177 0.947 0.28 1.416 0.009 0.042 0.016 0.084 0.026 0.126a32.085 32.085 0 0 0 1.708 5.343c0.056 0.135 0.109 0.271 0.167 0.404a32.097 32.097 0 0 0 1.235 2.528c0.303 0.559 0.617 1.111 0.953 1.651 0.133 0.214 0.272 0.42 0.41 0.63 0.281 0.429 0.572 0.851 0.873 1.267 0.146 0.201 0.289 0.404 0.439 0.601 0.397 0.522 0.812 1.03 1.241 1.527 0.139 0.161 0.281 0.318 0.424 0.476 0.452 0.503 0.916 0.997 1.401 1.472 0.026 0.025 0.05 0.052 0.076 0.078a32.287 32.287 0 0 0 1.694 1.523c0.081 0.068 0.161 0.136 0.243 0.203a31.781 31.781 0 0 0 3.808 2.699l0.151 0.092a31.86 31.86 0 0 0 2.033 1.116c0.068 0.034 0.138 0.066 0.207 0.099 0.593 0.292 1.198 0.566 1.815 0.823 0.146 0.061 0.291 0.125 0.438 0.184 0.59 0.236 1.187 0.461 1.798 0.665 0.044 0.015 0.087 0.024 0.131 0.038 0.632 0.208 1.274 0.386 1.919 0.555 0.179 0.047 0.357 0.106 0.536 0.15 0.714 0.174 1.437 0.315 2.164 0.44 0.111 0.019 0.222 0.048 0.333 0.066 1.653 0.265 3.335 0.403 5.032 0.403h0.008c1.698 0 3.381-0.139 5.035-0.404 0.09-0.015 0.181-0.038 0.271-0.054 0.747-0.126 1.49-0.273 2.224-0.452 0.178-0.043 0.356-0.103 0.534-0.149a32.04 32.04 0 0 0 1.915-0.554c0.045-0.015 0.09-0.024 0.135-0.039l271.167-90.389 0.038-0.013 0.324-0.108a32.52 32.52 0 0 0 1.803-0.667c0.142-0.057 0.282-0.119 0.424-0.178a32.563 32.563 0 0 0 1.84-0.834c0.061-0.03 0.124-0.058 0.185-0.089a31.544 31.544 0 0 0 2.047-1.124l0.131-0.08a31.852 31.852 0 0 0 4.053-2.901 31.265 31.265 0 0 0 1.71-1.538l0.046-0.047c0.497-0.486 0.972-0.991 1.434-1.506 0.137-0.152 0.275-0.304 0.409-0.46 0.433-0.502 0.852-1.014 1.252-1.54 0.146-0.192 0.285-0.389 0.427-0.585 0.308-0.424 0.605-0.855 0.891-1.293 0.128-0.197 0.26-0.39 0.385-0.59 0.343-0.55 0.663-1.114 0.971-1.684a32.167 32.167 0 0 0 1.232-2.526c0.056-0.129 0.107-0.26 0.161-0.39 0.323-0.773 0.623-1.556 0.885-2.353l90.452-271.356c0.349-1.042 0.645-2.103 0.886-3.179l0.011-0.053c0.109-0.492 0.208-0.988 0.294-1.486 0.022-0.128 0.038-0.255 0.058-0.383 0.062-0.387 0.122-0.774 0.17-1.163 0.023-0.185 0.038-0.37 0.057-0.555 0.035-0.331 0.07-0.663 0.095-0.996 0.017-0.226 0.025-0.452 0.036-0.677 0.015-0.292 0.032-0.583 0.039-0.876 0.006-0.26 0.003-0.519 0.004-0.778 0-0.256 0.003-0.515-0.003-0.775z m-147.803 247.01l-195.635 65.212 65.212-195.635 195.635-65.212-65.212 195.635z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-message-->
<view a:if="{{name === 'line-message'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M864 128H160c-52.935 0-96 43.065-96 96v576c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V224c0-52.935-43.065-96-96-96z m32 672c0 17.645-14.355 32-32 32H160c-17.645 0-32-14.355-32-32V224c0-17.645 14.355-32 32-32h704c17.645 0 32 14.355 32 32v576z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M747.33 270.957L512 470.082 276.67 270.957c-13.491-11.417-33.683-9.733-45.098 3.758-11.416 13.491-9.733 33.683 3.758 45.098L491.26 536.37c0.304 0.259 0.616 0.506 0.928 0.752 0.113 0.089 0.224 0.182 0.338 0.269 0.279 0.215 0.563 0.42 0.848 0.625a27.33 27.33 0 0 0 1.806 1.205c0.252 0.156 0.504 0.312 0.759 0.46 0.26 0.151 0.523 0.294 0.786 0.437 0.187 0.102 0.372 0.208 0.56 0.306 0.463 0.24 0.932 0.467 1.403 0.683 0.106 0.048 0.213 0.092 0.319 0.139 0.396 0.176 0.795 0.345 1.196 0.504 0.116 0.046 0.232 0.089 0.348 0.134 0.414 0.159 0.83 0.308 1.248 0.449l0.268 0.09c1.576 0.514 3.181 0.902 4.801 1.164l0.179 0.027a31.683 31.683 0 0 0 3.044 0.326c0.104 0.006 0.209 0.015 0.313 0.02 0.526 0.026 1.053 0.043 1.579 0.043h0.032c0.526 0 1.053-0.017 1.579-0.043 0.105-0.005 0.21-0.014 0.314-0.02a32.336 32.336 0 0 0 3.041-0.326l0.182-0.027c1.62-0.262 3.226-0.65 4.802-1.164l0.263-0.088c0.421-0.141 0.84-0.292 1.256-0.451 0.113-0.043 0.227-0.086 0.34-0.131 0.405-0.16 0.806-0.33 1.206-0.508 0.104-0.046 0.208-0.089 0.312-0.136 0.473-0.216 0.942-0.444 1.407-0.685 0.184-0.095 0.363-0.199 0.545-0.298 0.268-0.146 0.537-0.292 0.802-0.446 0.252-0.146 0.501-0.3 0.749-0.454a32.604 32.604 0 0 0 1.364-0.891c0.153-0.106 0.305-0.215 0.456-0.324 0.281-0.201 0.561-0.404 0.836-0.616 0.12-0.092 0.238-0.19 0.357-0.285 0.306-0.242 0.612-0.484 0.911-0.739L788.67 319.813c13.491-11.416 15.174-31.607 3.759-45.098-11.416-13.493-31.61-15.174-45.099-3.758z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-pc-->
<view a:if="{{name === 'line-pc'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M832 96H192c-52.935 0-96 43.065-96 96v448c0 52.935 43.065 96 96 96h288v128H288c-17.673 0-32 14.327-32 32s14.327 32 32 32h448c17.673 0 32-14.327 32-32s-14.327-32-32-32H544V736h288c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m-640 64h640c17.645 0 32 14.355 32 32v352H160V192c0-17.645 14.355-32 32-32z m640 512H192c-17.645 0-32-14.355-32-32v-32h704v32c0 17.645-14.355 32-32 32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-partake-->
<view a:if="{{name === 'line-partake'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M800 640c-51.015 0-96.529 23.999-125.847 61.299l-295.619-147.81A159.67 159.67 0 0 0 384 512c0-14.346-1.909-28.252-5.466-41.49L674.153 322.7C703.471 360.001 748.985 384 800 384c88.225 0 160-71.776 160-160S888.225 64 800 64s-160 71.776-160 160c0 14.346 1.909 28.252 5.466 41.49L349.847 413.3C320.529 375.999 275.014 352 224 352c-88.224 0-160 71.776-160 160 0 88.225 71.776 160 160 160 51.014 0 96.529-23.999 125.847-61.299l295.619 147.81A159.676 159.676 0 0 0 640 800c0 88.225 71.775 160 160 160s160-71.775 160-160-71.775-160-160-160z m0-512c52.935 0 96 43.065 96 96s-43.065 96-96 96-96-43.065-96-96 43.065-96 96-96zM224 608c-52.935 0-96-43.065-96-96s43.065-96 96-96 96 43.065 96 96-43.065 96-96 96z m576 288c-52.935 0-96-43.065-96-96s43.065-96 96-96 96 43.065 96 96-43.065 96-96 96z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-link-->
<view a:if="{{name === 'line-link'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M901.816 122.207C864.309 84.671 814.349 64 761.141 64c-53.205 0-103.178 20.67-140.714 58.205L461.799 280.83c-13.431 13.429-13.431 35.281 0 48.712 6.5 6.499 15.15 10.079 24.356 10.079s17.856-3.58 24.356-10.08l158.633-158.626c24.532-24.533 57.197-38.044 91.977-38.044s67.447 13.511 91.979 38.044c50.717 50.717 50.717 133.238 0 183.954L665.101 542.865c-24.532 24.532-57.197 38.043-91.977 38.043s-67.446-13.511-91.979-38.044c-6.5-6.5-15.151-10.08-24.356-10.08s-17.856 3.581-24.354 10.079c-6.502 6.498-10.082 15.149-10.082 24.356 0 9.206 3.581 17.857 10.081 24.357 37.519 37.519 87.472 58.187 140.668 58.199h0.051c53.194-0.012 103.146-20.681 140.667-58.199l187.995-187.99c37.529-37.504 58.194-87.463 58.187-140.675-0.008-53.198-20.672-103.167-58.186-140.704zM573.1 641.18h0.05-0.05z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M537.861 684.379c-9.206 0-17.857 3.58-24.359 10.08L354.875 853.085c-24.533 24.531-57.199 38.041-91.979 38.041-34.781 0-67.446-13.51-91.977-38.041-24.534-24.532-38.045-57.199-38.045-91.978 0-34.78 13.511-67.444 38.045-91.974l187.996-187.996c24.534-24.533 57.199-38.044 91.98-38.044 34.78 0 67.447 13.511 91.978 38.043 6.499 6.5 15.15 10.081 24.356 10.081 9.207 0 17.857-3.58 24.359-10.08 13.43-13.429 13.43-35.281 0.002-48.71-37.51-37.535-87.468-58.206-140.675-58.206-53.206 0-103.179 20.671-140.713 58.204l-187.995 187.99C84.672 657.924 64 707.882 64 761.089c0 53.205 20.67 103.176 58.205 140.706 37.516 37.52 87.472 58.19 140.664 58.205h0.054c53.191-0.015 103.148-20.685 140.663-58.204l158.63-158.626c6.5-6.5 10.08-15.151 10.08-24.357 0-9.207-3.581-17.857-10.079-24.353-6.498-6.501-15.149-10.081-24.356-10.081zM262.869 951.403h0.054-0.054z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-label-->
<view a:if="{{name === 'line-label'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M773.818 64H250.182C200.456 64 160 107.065 160 160v768c0 0.133 0.008 0.264 0.01 0.397 0.004 0.342 0.011 0.684 0.026 1.027 0.009 0.195 0.021 0.389 0.033 0.584 0.021 0.333 0.045 0.666 0.076 0.999 0.019 0.206 0.042 0.411 0.065 0.616a29.602 29.602 0 0 0 0.221 1.598 32.635 32.635 0 0 0 0.488 2.393 29.106 29.106 0 0 0 0.406 1.5c0.073 0.245 0.148 0.49 0.226 0.733a34.617 34.617 0 0 0 0.804 2.206 32.265 32.265 0 0 0 0.983 2.18 31.832 31.832 0 0 0 2.472 4.148c0.113 0.16 0.225 0.321 0.341 0.48 0.201 0.276 0.409 0.546 0.619 0.815 0.082 0.105 0.158 0.214 0.242 0.318 0.037 0.046 0.077 0.088 0.114 0.134 0.291 0.359 0.59 0.71 0.896 1.055 0.086 0.098 0.17 0.2 0.257 0.297 0.367 0.406 0.745 0.802 1.133 1.188 0.205 0.205 0.417 0.399 0.627 0.598 0.189 0.179 0.377 0.359 0.571 0.533 0.249 0.224 0.502 0.44 0.757 0.655a31.654 31.654 0 0 0 1.312 1.053c0.154 0.117 0.31 0.23 0.465 0.344 0.288 0.21 0.577 0.419 0.871 0.619 0.158 0.107 0.319 0.209 0.479 0.314 0.297 0.194 0.592 0.39 0.894 0.573 0.213 0.129 0.432 0.249 0.648 0.374 0.392 0.226 0.786 0.444 1.186 0.652 0.344 0.18 0.689 0.357 1.04 0.525 0.152 0.072 0.307 0.138 0.46 0.208 0.374 0.171 0.75 0.337 1.132 0.494 0.133 0.055 0.268 0.106 0.402 0.159 0.41 0.162 0.823 0.316 1.241 0.462 0.112 0.039 0.225 0.076 0.337 0.114 0.445 0.149 0.895 0.289 1.348 0.418 0.092 0.026 0.183 0.053 0.275 0.078 0.474 0.131 0.952 0.251 1.435 0.36 0.078 0.018 0.155 0.037 0.233 0.054 0.492 0.108 0.989 0.203 1.489 0.288l0.213 0.038c0.498 0.081 0.999 0.149 1.504 0.207 0.074 0.009 0.148 0.02 0.222 0.028 0.488 0.052 0.979 0.091 1.473 0.121 0.089 0.005 0.178 0.015 0.267 0.02 0.499 0.026 1.002 0.038 1.508 0.041 0.074 0 0.147 0.006 0.221 0.006 0.51 0 1.02-0.016 1.53-0.04 0.113-0.005 0.224-0.014 0.337-0.021 0.391-0.023 0.782-0.052 1.173-0.089 0.149-0.014 0.298-0.03 0.447-0.046 0.357-0.039 0.713-0.085 1.07-0.136 0.151-0.022 0.303-0.042 0.453-0.066 0.386-0.061 0.772-0.131 1.157-0.207 0.105-0.021 0.211-0.038 0.316-0.059 0.477-0.099 0.953-0.208 1.427-0.329 0.138-0.035 0.275-0.076 0.412-0.113 0.333-0.09 0.666-0.182 0.997-0.283 0.186-0.057 0.369-0.118 0.553-0.178a33.113 33.113 0 0 0 1.42-0.498 37.055 37.055 0 0 0 1.372-0.554c0.26-0.112 0.518-0.231 0.776-0.351 0.188-0.087 0.377-0.173 0.563-0.263 0.262-0.128 0.522-0.265 0.782-0.401 0.175-0.091 0.352-0.179 0.524-0.273a32.477 32.477 0 0 0 1.857-1.107c0.196-0.125 0.393-0.246 0.586-0.376 0.226-0.151 0.447-0.31 0.669-0.467 0.177-0.125 0.355-0.247 0.529-0.377 0.215-0.158 0.424-0.322 0.635-0.486 0.128-0.099 0.258-0.193 0.385-0.294L512 712.979l300.01 240.008c0.129 0.103 0.261 0.199 0.391 0.299 0.208 0.161 0.415 0.323 0.626 0.479 0.18 0.133 0.364 0.26 0.546 0.388 0.216 0.153 0.43 0.307 0.651 0.455 0.215 0.144 0.434 0.279 0.651 0.417 0.243 0.154 0.486 0.309 0.733 0.457 0.354 0.212 0.71 0.418 1.069 0.615 0.158 0.086 0.319 0.166 0.478 0.249 0.276 0.145 0.552 0.289 0.831 0.426 0.174 0.085 0.351 0.165 0.527 0.247 0.271 0.126 0.542 0.251 0.815 0.369a32.976 32.976 0 0 0 1.376 0.555 27.915 27.915 0 0 0 1.427 0.499c0.173 0.056 0.345 0.114 0.519 0.167 0.35 0.107 0.701 0.205 1.053 0.299 0.12 0.032 0.238 0.068 0.358 0.099 0.479 0.122 0.96 0.233 1.442 0.333 0.094 0.019 0.189 0.035 0.283 0.053 0.395 0.078 0.791 0.149 1.188 0.212 0.146 0.023 0.293 0.043 0.44 0.064a30.394 30.394 0 0 0 1.52 0.182c0.393 0.037 0.786 0.067 1.179 0.09l0.333 0.021c0.51 0.024 1.02 0.04 1.531 0.04 0.074 0 0.147-0.006 0.221-0.006a32.804 32.804 0 0 0 1.508-0.041c0.088-0.005 0.176-0.015 0.264-0.02 0.495-0.03 0.988-0.069 1.477-0.121 0.073-0.008 0.145-0.019 0.218-0.028 0.507-0.058 1.01-0.126 1.509-0.207 0.069-0.011 0.138-0.025 0.207-0.037a30.863 30.863 0 0 0 1.496-0.29l0.227-0.052c0.485-0.11 0.965-0.23 1.441-0.362l0.269-0.076a31.245 31.245 0 0 0 2.934-0.996c0.132-0.052 0.265-0.103 0.397-0.157 0.383-0.157 0.761-0.324 1.136-0.496 0.152-0.069 0.306-0.135 0.457-0.207 0.351-0.167 0.695-0.345 1.039-0.524 0.409-0.213 0.812-0.437 1.213-0.668 0.207-0.12 0.417-0.234 0.621-0.358 0.305-0.185 0.603-0.382 0.902-0.578 0.157-0.103 0.315-0.202 0.47-0.308 0.296-0.201 0.587-0.412 0.878-0.624 0.153-0.112 0.307-0.224 0.458-0.338a32.182 32.182 0 0 0 1.311-1.052c0.256-0.216 0.511-0.434 0.762-0.66 0.192-0.173 0.378-0.351 0.565-0.528 0.211-0.2 0.425-0.396 0.631-0.602 0.388-0.386 0.765-0.782 1.133-1.188 0.085-0.094 0.166-0.194 0.251-0.289 0.309-0.349 0.612-0.704 0.906-1.067 0.036-0.044 0.074-0.085 0.109-0.129 0.08-0.101 0.153-0.205 0.232-0.306a24.214 24.214 0 0 0 0.961-1.296c0.196-0.278 0.388-0.558 0.575-0.843 0.111-0.168 0.218-0.338 0.325-0.508a32.63 32.63 0 0 0 0.828-1.389 33.284 33.284 0 0 0 1.118-2.183 33.498 33.498 0 0 0 1.171-2.88 28.95 28.95 0 0 0 0.478-1.48 30.562 30.562 0 0 0 0.736-3.016 32.26 32.26 0 0 0 0.517-4.085c0.012-0.193 0.024-0.386 0.033-0.581 0.015-0.344 0.022-0.688 0.026-1.032 0.002-0.131 0.01-0.261 0.01-0.393V160c0.003-52.935-40.452-96-90.179-96zM224 160c0-17.645 11.745-32 26.182-32h523.637C788.255 128 800 142.355 800 160v701.421L532.466 647.394c-0.048-0.04-0.098-0.077-0.146-0.117l-0.331-0.264c-0.031-0.025-0.063-0.047-0.094-0.071a31.42 31.42 0 0 0-1.162-0.882l-0.141-0.1c-0.384-0.274-0.772-0.54-1.166-0.795l-0.23-0.147a31.111 31.111 0 0 0-1.074-0.656c-0.128-0.075-0.257-0.148-0.386-0.221a34.642 34.642 0 0 0-1.495-0.794 37.286 37.286 0 0 0-1.515-0.705 29.802 29.802 0 0 0-1.957-0.772c-0.36-0.129-0.721-0.251-1.084-0.366l-0.326-0.101c-0.394-0.12-0.789-0.234-1.186-0.338l-0.266-0.068a31.222 31.222 0 0 0-1.237-0.29l-0.264-0.054a32.398 32.398 0 0 0-2.715-0.434c-0.141-0.016-0.283-0.03-0.424-0.045a31.332 31.332 0 0 0-1.056-0.092c-0.189-0.013-0.379-0.022-0.568-0.032a30.471 30.471 0 0 0-0.906-0.035c-0.246-0.006-0.491-0.006-0.737-0.006s-0.492 0.001-0.737 0.006c-0.302 0.007-0.604 0.02-0.906 0.035-0.19 0.01-0.379 0.019-0.569 0.032-0.352 0.024-0.704 0.056-1.055 0.092-0.142 0.015-0.284 0.028-0.425 0.045a32.95 32.95 0 0 0-2.714 0.433l-0.268 0.055c-0.412 0.088-0.823 0.184-1.232 0.288l-0.272 0.069a32.859 32.859 0 0 0-3.034 0.965c-0.31 0.116-0.618 0.237-0.926 0.364a27.496 27.496 0 0 0-1.344 0.587 32.141 32.141 0 0 0-2.269 1.166c-0.123 0.07-0.245 0.139-0.367 0.21-0.369 0.215-0.733 0.438-1.094 0.668l-0.21 0.134c-0.402 0.261-0.797 0.531-1.189 0.811l-0.116 0.083c-0.402 0.29-0.798 0.591-1.188 0.902l-0.078 0.059-0.272 0.218c-0.07 0.057-0.142 0.111-0.212 0.169L224 861.421V160z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M352 288h320c17.673 0 32-14.327 32-32s-14.327-32-32-32H352c-17.673 0-32 14.327-32 32s14.327 32 32 32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-order-->
<view a:if="{{name === 'line-order'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M416 896H224c-17.645 0-32-14.355-32-32V160c0-17.645 14.355-32 32-32h576c17.645 0 32 14.355 32 32v256c0 17.673 14.327 32 32 32s32-14.327 32-32V160c0-52.935-43.065-96-96-96H224c-52.935 0-96 43.065-96 96v704c0 52.935 43.065 96 96 96h192c17.673 0 32-14.327 32-32s-14.327-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M704 288c0-17.673-14.327-32-32-32H352c-17.673 0-32 14.327-32 32s14.327 32 32 32h320c17.673 0 32-14.327 32-32zM352 480c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h192c17.673 0 32-14.327 32-32s-14.327-32-32-32H352zM894.392 577.606c-42.308-42.308-98.559-65.607-158.392-65.607s-116.084 23.3-158.393 65.607C535.3 619.915 512 676.166 512 735.999c0 59.832 23.3 116.084 65.607 158.392 42.309 42.309 98.56 65.608 158.393 65.608s116.084-23.3 158.393-65.608C936.7 852.082 960 795.831 960 735.998c0-59.832-23.3-116.083-65.608-158.392zM736 895.999c-88.225 0-160-71.776-160-160.001 0-88.224 71.775-159.999 160-159.999s160 71.775 160 160-71.775 160-160 160z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M768 727.425v-55.426c0-17.673-14.327-32-32-32s-32 14.327-32 32v64c0 0.051 0.004 0.102 0.004 0.153 0.002 0.456 0.013 0.912 0.035 1.368l0.012 0.248a31.968 31.968 0 0 0 0.543 4.364c0.035 0.178 0.069 0.357 0.107 0.534 0.065 0.304 0.136 0.608 0.21 0.911 0.057 0.233 0.114 0.466 0.176 0.698 0.065 0.241 0.134 0.482 0.205 0.722 0.085 0.289 0.171 0.577 0.264 0.863a39.418 39.418 0 0 0 0.551 1.557 31.49 31.49 0 0 0 1.334 3.007l0.129 0.252c0.204 0.394 0.416 0.784 0.636 1.168 0.029 0.051 0.054 0.102 0.083 0.153l32 55.426c5.928 10.267 16.683 16.005 27.743 16.005a31.858 31.858 0 0 0 15.97-4.292c15.306-8.837 20.55-28.407 11.713-43.713L768 727.425z' fill='{{(isStr ? colors : colors[2]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-notice-->
<view a:if="{{name === 'line-notice'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M648.084 113.604c-22.308-11.621-48.997-9.856-69.647 4.605L295.962 316.372l-16.415 10.735H163.234C126.153 327.108 96 357.27 96 394.342v235.317c0 37.073 30.153 67.234 67.234 67.234h116.378l298.825 208.9c11.556 8.109 25.032 12.228 38.574 12.228 10.604 0 21.257-2.528 31.057-7.634 22.356-11.621 36.227-34.47 36.227-59.642V173.246c0-25.171-13.871-48.028-36.211-59.642z m-31.022 737.141L332.829 651.309l-24.95-16.217a33.613 33.613 0 0 0-18.319-5.433H163.234V394.342H289.56a33.606 33.606 0 0 0 18.401-5.482l25.705-16.833 283.396-198.779v677.497zM885.236 300.241c-6.884-16.278-25.663-23.89-41.938-17.007-16.276 6.884-23.891 25.661-17.007 41.938C851.313 384.329 864 447.187 864 512.001c0 64.433-12.543 126.95-37.281 185.814-6.847 16.293 0.811 35.051 17.104 41.898a31.913 31.913 0 0 0 12.384 2.507c12.497 0 24.369-7.366 29.515-19.61C913.775 655.854 928 584.994 928 512c0-73.425-14.388-144.671-42.764-211.759z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-home-->
<view a:if="{{name === 'line-home'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M928 448.145V448h-0.003c-0.005-9.163-3.922-18.258-11.511-24.583l-384-320c-0.042-0.035-0.086-0.066-0.128-0.101-0.337-0.279-0.68-0.549-1.026-0.812-0.078-0.059-0.155-0.119-0.234-0.177a31.687 31.687 0 0 0-1.073-0.764c-0.088-0.06-0.175-0.121-0.263-0.18-0.34-0.227-0.684-0.445-1.03-0.658-0.121-0.074-0.242-0.15-0.363-0.223a31.53 31.53 0 0 0-0.927-0.528c-0.169-0.093-0.338-0.188-0.508-0.278-0.26-0.137-0.522-0.267-0.785-0.396a30.438 30.438 0 0 0-0.681-0.33c-0.206-0.096-0.414-0.185-0.622-0.276a32.374 32.374 0 0 0-0.863-0.368c-0.154-0.062-0.309-0.119-0.463-0.179a32.699 32.699 0 0 0-1.03-0.381c-0.111-0.038-0.222-0.073-0.334-0.11-0.388-0.13-0.776-0.255-1.167-0.369l-0.237-0.066c-0.42-0.119-0.84-0.232-1.263-0.333l-0.203-0.046a30.518 30.518 0 0 0-1.297-0.276c-0.077-0.015-0.154-0.026-0.231-0.04a30.331 30.331 0 0 0-1.266-0.207c-0.111-0.016-0.222-0.027-0.333-0.042-0.387-0.05-0.774-0.097-1.161-0.134-0.165-0.016-0.331-0.025-0.497-0.038a33.294 33.294 0 0 0-0.996-0.067c-0.234-0.011-0.468-0.014-0.702-0.02-0.264-0.006-0.527-0.015-0.791-0.015-0.307 0-0.613 0.009-0.92 0.018-0.191 0.005-0.382 0.008-0.573 0.016-0.374 0.017-0.747 0.045-1.12 0.075-0.124 0.01-0.249 0.017-0.373 0.029-0.426 0.039-0.851 0.09-1.275 0.147-0.074 0.01-0.148 0.017-0.221 0.028-0.458 0.064-0.914 0.14-1.369 0.224l-0.129 0.022a33.31 33.31 0 0 0-1.389 0.296l-0.113 0.025c-0.447 0.107-0.892 0.226-1.336 0.352-0.056 0.016-0.112 0.03-0.169 0.047a32.4 32.4 0 0 0-1.23 0.388l-0.272 0.09c-0.362 0.125-0.721 0.26-1.08 0.399-0.139 0.054-0.279 0.106-0.418 0.161a32.03 32.03 0 0 0-0.901 0.384c-0.196 0.086-0.392 0.171-0.586 0.26-0.237 0.11-0.473 0.227-0.708 0.343-0.255 0.126-0.51 0.252-0.762 0.385-0.174 0.092-0.347 0.189-0.52 0.284-0.309 0.17-0.616 0.344-0.92 0.525-0.121 0.072-0.241 0.148-0.362 0.222-0.348 0.214-0.694 0.433-1.035 0.661-0.085 0.057-0.169 0.116-0.254 0.174-0.365 0.249-0.727 0.506-1.083 0.771l-0.221 0.168c-0.352 0.267-0.7 0.542-1.042 0.825-0.039 0.032-0.08 0.061-0.119 0.094l-384 320c-7.59 6.325-11.506 15.42-11.511 24.583H96V832c0 52.935 43.065 96 96 96h192c17.673 0 32-14.327 32-32V704c0-52.935 43.065-96 96-96s96 43.065 96 96v192c0 17.673 14.327 32 32 32h192c52.935 0 96-43.065 96-96V448.188v-0.043zM832 864H672V704c0-88.225-71.775-160-160-160-88.224 0-160 71.775-160 160v160H192c-17.645 0-32-14.355-32-32V462.988l352-293.333 352 293.334V832c0 17.645-14.355 32-32 32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-overstep-->
<view a:if="{{name === 'line-overstep'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M832 676V348c55.1-14.2 96-64.4 96-124 0-70.6-57.4-128-128-128-59.5 0-109.7 40.9-124 96H348c-14.2-55.1-64.4-96-124-96-70.6 0-128 57.4-128 128 0 59.5 40.9 109.7 96 124v328c-55.1 14.2-96 64.4-96 124 0 70.6 57.4 128 128 128 59.5 0 109.7-40.9 124-96h328c14.2 55.1 64.4 96 124 96 70.6 0 128-57.4 128-128 0-59.5-40.9-109.7-96-124z m-32-516c35.3 0 64 28.7 64 64s-28.7 64-64 64-64-28.7-64-64 28.7-64 64-64z m-640 64c0-35.3 28.7-64 64-64s64 28.7 64 64-28.7 64-64 64-64-28.7-64-64z m64 640c-35.3 0-64-28.7-64-64s28.7-64 64-64 64 28.7 64 64-28.7 64-64 64z m452-96H348c-11.6-44.9-47-80.3-92-92V348c44.9-11.6 80.3-47 92-92h328c11.6 44.9 47 80.3 92 92v328c-44.9 11.7-80.3 47.1-92 92z m124 96c-35.3 0-64-28.7-64-64s28.7-64 64-64 64 28.7 64 64-28.7 64-64 64z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M444.1 398.9c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l181 181c6.2 6.2 14.4 9.4 22.6 9.4s16.4-3.1 22.6-9.4c12.5-12.5 12.5-32.8 0-45.3l-180.9-181z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--refresh-->
<view a:if="{{name === 'refresh'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M892.586667 658.944l-170.368 30.549333a31.402667 31.402667 0 0 0-18.389334 51.2l32.256 38.144a349.013333 349.013333 0 0 1-491.562666-41.6 346.282667 346.282667 0 0 1-81.365334-203.562666h-0.426666c0-1.877333 0-3.84-0.256-5.76a38.656 38.656 0 0 0-44.16-32.512 38.869333 38.869333 0 0 0-32.597334 44.117333c0.085333 0.426667 0.256 0.810667 0.256 1.152a423.850667 423.850667 0 0 0 99.2 246.613333c152.021333 179.882667 420.949333 202.624 600.917334 50.773334l29.397333 34.773333a31.402667 31.402667 0 0 0 53.546667-9.642667l58.624-162.730666a31.36 31.36 0 0 0-35.114667-41.514667z m0 0M128 363.434667l170.325333-30.549334a31.402667 31.402667 0 0 0 18.432-51.2l-47.786666-56.618666 17.066666 20.266666a348.970667 348.970667 0 0 1 491.733334 41.386667 346.197333 346.197333 0 0 1 81.408 206.848h0.426666c0.128 1.152 0 2.432 0.213334 3.584a38.613333 38.613333 0 1 0 76.928-5.973333l-0.085334-1.109334c-0.085333-1.536 0-2.986667-0.256-4.522666l-0.298666-0.981334a423.424 423.424 0 0 0-99.2-247.893333C685.013333 56.746667 415.914667 34.133333 235.989333 186.026667l-30.890666-36.48a31.402667 31.402667 0 0 0-53.546667 9.685333L92.885333 321.92A31.402667 31.402667 0 0 0 128 363.434667z m0 0' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--identity-card-->
<view a:if="{{name === 'identity-card'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M585.152 430.336h295.104v51.84H585.152v-51.84z m89.984 380.736l-2.496-2.496H92.544a40.832 40.832 0 0 1-41.28-41.28V223.488c0-23.168 18.176-43.776 41.28-43.776h826.432c23.104 0 41.28 18.112 41.28 41.28v326.336l2.496 2.496c13.12 7.488 25.6 18.112 35.648 31.296l10.048 13.12V210.368a80 80 0 0 0-79.36-80H79.36A80 80 0 0 0 0 210.368v569.472c0 43.776 35.648 80 79.36 80h628.928l-7.552-10.688a347.2 347.2 0 0 1-25.6-38.08zM585.152 544.128h87.488v51.904H585.152v-51.904z m0-225.024h295.104v51.904H585.152v-51.904z m-418.24-20.672a34.56 34.56 0 0 0-35.648 33.792v338.88c0 18.112 15.616 33.792 35.648 33.792H485.12a34.56 34.56 0 0 0 35.648-33.792V334.72c0-18.112-18.112-33.728-35.648-33.728H166.912v-2.56z m36.224 359.424c0-7.488 2.496-25.6 10.048-36.224 2.496-2.496 4.992-4.992 4.992-7.488 7.488-10.624 15.616-23.168 51.264-28.8a53.568 53.568 0 0 0 30.656-15.616c9.984-15.616 7.488-36.288 7.488-43.776v-18.176s-23.168-31.232-23.168-54.4v-23.104s0-33.792 15.68-51.904a33.664 33.664 0 0 1 23.104-10.624l35.648 13.12h2.496c35.648 0 38.72 46.912 38.72 49.408v28.736s0 36.288-23.104 46.912l-2.496 2.496v15.616c-2.496 7.552-10.048 31.296 0 46.912 4.992 10.624 15.616 15.616 28.16 15.616 38.72 5.056 51.264 20.672 58.752 28.8l4.992 4.992s4.992 4.992 9.984 36.288H201.984v1.28l1.152-0.064z m651.456-105.6c-95.04 0-169.408 75.008-169.408 170.688 0 93.12 76.864 170.688 169.408 170.688 95.04 0 169.408-75.008 169.408-170.688s-76.928-170.688-169.408-170.688z m-2.496 274.432c-7.552 7.552-23.168 7.552-33.152 0l-71.936-72.512c-7.488-7.552-7.488-23.168 0-31.296 7.552-7.488 23.168-7.488 33.152 0l56.256 56.896 110.656-108.736c7.552-7.488 23.168-7.488 33.152 0 7.488 7.488 7.488 23.168 0 31.296l-128.128 124.352z' fill='{{(isStr ? colors : colors[0]) || 'rgb(153,153,153)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-control-->
<view a:if="{{name === 'line-control'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M864 208H443.334c-13.803-46.213-56.693-80-107.334-80s-93.531 33.787-107.334 80H160c-17.673 0-32 14.327-32 32s14.327 32 32 32h68.666c13.803 46.213 56.693 80 107.334 80s93.531-33.787 107.334-80H864c17.673 0 32-14.327 32-32 0-17.674-14.327-32-32-32z m-528 79.999c-26.467 0-48-21.533-48-48s21.533-48 48-48 48 21.533 48 48-21.533 48-48 48zM864 751.999H443.334c-13.802-46.213-56.693-80-107.334-80s-93.531 33.787-107.334 80H160c-17.673 0-32 14.327-32 32s14.327 32 32 32h68.666c13.802 46.213 56.693 80 107.334 80s93.531-33.787 107.334-80H864c17.673 0 32-14.327 32-32s-14.327-32-32-32z m-528 80c-26.467 0-48-21.532-48-48C288 757.532 309.533 736 336 736s48 21.533 48 48-21.533 47.999-48 47.999zM864 480h-68.667C781.531 433.787 738.64 400 688 400s-93.531 33.787-107.333 80H160c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h420.667C594.47 590.213 637.36 623.999 688 623.999S781.531 590.213 795.333 544H864c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32z m-176 79.999c-26.467 0-47.999-21.532-47.999-47.999s21.532-48 47.999-48 47.999 21.533 47.999 48-21.532 47.999-47.999 47.999z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-reward-->
<view a:if="{{name === 'line-reward'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M511.202 268.48c-70.579 0-128 57.421-128 128s57.42 128 128 128c70.579 0 128-57.421 128-128s-57.421-128-128-128z m0 192c-35.29 0-64-28.71-64-64s28.71-64 64-64 64 28.71 64 64-28.71 64-64 64z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M874.196 847.453a32.12 32.12 0 0 0 1.408-1.894l0.062-0.096c0.419-0.619 0.809-1.255 1.181-1.899 0.083-0.143 0.168-0.284 0.249-0.429a32.755 32.755 0 0 0 2.11-4.601c0.039-0.107 0.074-0.217 0.113-0.325a32.064 32.064 0 0 0 1.13-3.962c0.04-0.186 0.071-0.373 0.108-0.559 0.064-0.325 0.13-0.65 0.184-0.977 0.045-0.273 0.079-0.546 0.117-0.819 0.033-0.24 0.07-0.48 0.098-0.721 0.036-0.308 0.061-0.615 0.088-0.923 0.018-0.209 0.04-0.418 0.054-0.627 0.021-0.318 0.033-0.636 0.044-0.953 0.008-0.203 0.018-0.405 0.021-0.608 0.005-0.311 0.002-0.621-0.002-0.931-0.002-0.215-0.003-0.429-0.01-0.643-0.009-0.291-0.027-0.58-0.044-0.869-0.014-0.237-0.027-0.474-0.046-0.712-0.021-0.261-0.05-0.52-0.077-0.779a26.946 26.946 0 0 0-0.092-0.802c-0.03-0.226-0.066-0.451-0.101-0.676a30.324 30.324 0 0 0-0.147-0.895c-0.035-0.192-0.076-0.383-0.115-0.575a30.303 30.303 0 0 0-0.21-0.976c-0.039-0.165-0.083-0.329-0.124-0.493a31.756 31.756 0 0 0-0.273-1.027c-0.043-0.15-0.092-0.299-0.137-0.448a33.033 33.033 0 0 0-0.331-1.041c-0.051-0.148-0.107-0.295-0.16-0.442-0.122-0.339-0.244-0.678-0.378-1.015-0.066-0.166-0.139-0.329-0.207-0.495-0.13-0.312-0.259-0.624-0.399-0.933-0.099-0.218-0.207-0.431-0.31-0.646-0.122-0.253-0.239-0.508-0.368-0.759a33.431 33.431 0 0 0-0.648-1.192c-0.036-0.064-0.068-0.129-0.105-0.192L760.393 609.764c22.006-25.651 39.8-54.345 53.02-85.601 17.112-40.458 25.789-83.417 25.789-127.683s-8.677-87.224-25.789-127.682c-16.521-39.062-40.167-74.136-70.28-104.249-30.112-30.113-65.187-53.758-104.248-70.28-40.459-17.112-83.417-25.789-127.683-25.789s-87.224 8.677-127.682 25.789c-39.062 16.522-74.136 40.167-104.249 70.28-30.113 30.112-53.758 65.187-70.28 104.249-17.112 40.458-25.789 83.417-25.789 127.682s8.677 87.224 25.789 127.683c13.22 31.256 31.014 59.95 53.02 85.601L147.124 808.755c-0.013 0.022-0.024 0.045-0.036 0.067-0.25 0.435-0.49 0.876-0.72 1.323-0.117 0.227-0.222 0.456-0.332 0.684-0.116 0.241-0.237 0.479-0.347 0.723-0.131 0.289-0.251 0.581-0.373 0.873-0.078 0.186-0.159 0.37-0.234 0.557-0.126 0.318-0.241 0.638-0.357 0.959-0.06 0.167-0.124 0.333-0.181 0.501a28.74 28.74 0 0 0-0.313 0.983c-0.052 0.17-0.107 0.339-0.156 0.509-0.092 0.321-0.175 0.642-0.257 0.965-0.047 0.186-0.097 0.371-0.141 0.558-0.071 0.303-0.134 0.606-0.195 0.91-0.044 0.214-0.089 0.427-0.128 0.642-0.05 0.275-0.093 0.551-0.136 0.826-0.039 0.248-0.078 0.497-0.111 0.747-0.032 0.243-0.057 0.486-0.083 0.729-0.031 0.283-0.061 0.567-0.085 0.851-0.017 0.213-0.028 0.425-0.041 0.638-0.019 0.314-0.038 0.628-0.047 0.944-0.006 0.187-0.006 0.373-0.008 0.56-0.005 0.338-0.008 0.676-0.002 1.016 0.003 0.171 0.012 0.341 0.018 0.511 0.012 0.35 0.025 0.699 0.049 1.05 0.012 0.173 0.03 0.345 0.044 0.517 0.029 0.343 0.057 0.686 0.098 1.03 0.023 0.197 0.054 0.392 0.08 0.588 0.043 0.317 0.084 0.634 0.137 0.952 0.042 0.256 0.095 0.51 0.144 0.765 0.049 0.256 0.093 0.512 0.148 0.769 0.105 0.488 0.223 0.974 0.351 1.456l0.019 0.08 0.002 0.006c0.215 0.802 0.469 1.593 0.746 2.377 0.042 0.12 0.082 0.243 0.126 0.362 0.261 0.713 0.552 1.415 0.864 2.108a31.736 31.736 0 0 0 1.236 2.471c0.085 0.153 0.175 0.302 0.263 0.454 0.365 0.63 0.746 1.251 1.155 1.857l0.091 0.14a31.81 31.81 0 0 0 1.384 1.862c0.086 0.108 0.17 0.217 0.257 0.324a31.93 31.93 0 0 0 1.523 1.733c0.074 0.079 0.151 0.155 0.226 0.233a31.868 31.868 0 0 0 3.868 3.408c0.611 0.456 1.238 0.897 1.89 1.314 0.082 0.053 0.167 0.101 0.25 0.153 0.339 0.213 0.678 0.427 1.028 0.629 0.197 0.114 0.4 0.21 0.599 0.32a29.578 29.578 0 0 0 2.264 1.129c0.224 0.101 0.445 0.207 0.672 0.303 0.699 0.297 1.403 0.57 2.113 0.814 0.033 0.011 0.065 0.025 0.097 0.036 0.775 0.263 1.556 0.491 2.341 0.691 0.174 0.045 0.352 0.079 0.527 0.121a32.07 32.07 0 0 0 1.798 0.376c0.258 0.046 0.518 0.086 0.777 0.126 0.543 0.083 1.086 0.149 1.631 0.203 0.268 0.027 0.535 0.057 0.804 0.077 0.696 0.051 1.391 0.081 2.085 0.087 0.091 0.001 0.181 0.011 0.273 0.011 1.81 0 3.641-0.171 5.475-0.491 0.232-0.04 0.466-0.07 0.697-0.115 0.274-0.054 0.549-0.127 0.823-0.188 0.412-0.092 0.826-0.179 1.234-0.288 0.024-0.006 0.049-0.01 0.073-0.017l100.228-26.856 26.857 100.229 0.002 0.006c0.215 0.802 0.469 1.593 0.746 2.377 0.042 0.12 0.082 0.243 0.126 0.362 0.261 0.713 0.552 1.415 0.864 2.108a31.736 31.736 0 0 0 1.236 2.471c0.085 0.153 0.175 0.302 0.263 0.454 0.365 0.63 0.746 1.251 1.155 1.857l0.091 0.14a31.81 31.81 0 0 0 1.384 1.862c0.086 0.108 0.17 0.217 0.257 0.324a31.93 31.93 0 0 0 1.523 1.733c0.074 0.079 0.151 0.155 0.226 0.233a31.868 31.868 0 0 0 3.868 3.408c0.611 0.456 1.238 0.897 1.89 1.314 0.082 0.053 0.167 0.101 0.25 0.153 0.339 0.213 0.678 0.427 1.028 0.629 0.197 0.114 0.4 0.21 0.599 0.32a29.578 29.578 0 0 0 2.264 1.129c0.224 0.101 0.445 0.207 0.672 0.303 0.699 0.297 1.403 0.57 2.113 0.814 0.033 0.011 0.065 0.025 0.097 0.036 0.775 0.263 1.556 0.491 2.341 0.691 0.174 0.045 0.352 0.079 0.527 0.121a32.07 32.07 0 0 0 1.798 0.376c0.258 0.046 0.518 0.086 0.777 0.126 0.543 0.083 1.086 0.149 1.631 0.203 0.268 0.027 0.535 0.057 0.804 0.077 0.696 0.051 1.391 0.081 2.085 0.087 0.091 0.001 0.181 0.011 0.273 0.011 1.874 0 3.77-0.18 5.667-0.522 0.166-0.03 0.333-0.05 0.498-0.082 0.299-0.059 0.597-0.137 0.896-0.205 0.39-0.088 0.781-0.17 1.168-0.272 0.024-0.007 0.049-0.011 0.073-0.017a32.5 32.5 0 0 0 1.734-0.52c0.13-0.043 0.258-0.091 0.387-0.136 0.483-0.166 0.96-0.341 1.431-0.527 0.154-0.061 0.307-0.125 0.46-0.188 0.478-0.198 0.949-0.407 1.413-0.626 0.111-0.052 0.223-0.103 0.333-0.156a31.828 31.828 0 0 0 3.364-1.899c0.122-0.079 0.242-0.161 0.363-0.242 0.414-0.277 0.821-0.562 1.22-0.856 0.139-0.102 0.278-0.203 0.416-0.308a32.35 32.35 0 0 0 1.41-1.134l0.2-0.176a31.409 31.409 0 0 0 2.577-2.523c0.148-0.162 0.299-0.322 0.444-0.488 0.398-0.453 0.784-0.917 1.155-1.391 0.105-0.134 0.204-0.274 0.307-0.41a32.234 32.234 0 0 0 1.189-1.677 32.38 32.38 0 0 0 1.031-1.654L488.831 728.91a32.217 32.217 0 0 0 2.351-5.05c6.644 0.398 13.317 0.622 20.025 0.622 6.948 0 13.859-0.239 20.737-0.666a32.021 32.021 0 0 0 3.234 7.859l120 207.846 0.009 0.014a31.998 31.998 0 0 0 1.351 2.125c0.275 0.399 0.559 0.792 0.851 1.179 0.106 0.141 0.209 0.285 0.318 0.424 0.369 0.471 0.752 0.931 1.148 1.382 0.15 0.171 0.305 0.336 0.458 0.503 0.279 0.305 0.563 0.604 0.854 0.899 0.167 0.169 0.334 0.338 0.504 0.502 0.382 0.369 0.772 0.729 1.173 1.079 0.093 0.081 0.184 0.163 0.277 0.243 0.441 0.376 0.894 0.739 1.357 1.092 0.147 0.112 0.296 0.22 0.444 0.329a30.9 30.9 0 0 0 1.183 0.83c0.132 0.088 0.262 0.177 0.395 0.263 1.066 0.69 2.18 1.32 3.34 1.885 0.122 0.06 0.246 0.115 0.369 0.173a31.432 31.432 0 0 0 3.257 1.322c0.139 0.048 0.276 0.1 0.416 0.146 0.568 0.187 1.143 0.362 1.727 0.518l0.069 0.016c0.405 0.107 0.814 0.194 1.223 0.285 0.281 0.063 0.561 0.138 0.842 0.193 0.187 0.037 0.377 0.06 0.564 0.093a31.91 31.91 0 0 0 5.604 0.512c0.092 0 0.182-0.011 0.273-0.011a31.65 31.65 0 0 0 2.085-0.087c0.269-0.02 0.536-0.051 0.805-0.078a31.168 31.168 0 0 0 4.212-0.707c0.173-0.041 0.347-0.075 0.519-0.119a31.688 31.688 0 0 0 2.346-0.693l0.072-0.027c0.72-0.246 1.433-0.524 2.141-0.825 0.219-0.093 0.434-0.196 0.651-0.294a31.194 31.194 0 0 0 2.27-1.132c0.202-0.111 0.407-0.208 0.607-0.324 0.355-0.205 0.699-0.422 1.043-0.638 0.076-0.048 0.153-0.092 0.229-0.14a32.69 32.69 0 0 0 1.904-1.324l0.308-0.234a31.978 31.978 0 0 0 3.564-3.179l0.206-0.212a32.165 32.165 0 0 0 1.539-1.751c0.081-0.099 0.159-0.2 0.238-0.3a32.12 32.12 0 0 0 1.408-1.894l0.062-0.096c0.419-0.619 0.809-1.255 1.181-1.899 0.083-0.143 0.168-0.284 0.249-0.429a32.755 32.755 0 0 0 2.11-4.601c0.039-0.107 0.074-0.217 0.113-0.325 0.284-0.803 0.544-1.614 0.763-2.436l26.849-100.2 100.229 26.856 0.069 0.016c0.405 0.107 0.814 0.194 1.223 0.285 0.281 0.063 0.561 0.138 0.842 0.193 0.187 0.037 0.377 0.06 0.564 0.093a31.91 31.91 0 0 0 5.604 0.512c0.092 0 0.182-0.011 0.273-0.011a31.65 31.65 0 0 0 2.085-0.087c0.269-0.02 0.536-0.051 0.805-0.078a31.168 31.168 0 0 0 4.212-0.707c0.173-0.041 0.347-0.075 0.519-0.119a31.688 31.688 0 0 0 2.346-0.693l0.072-0.027c0.72-0.246 1.433-0.524 2.141-0.825 0.219-0.093 0.434-0.196 0.651-0.294a31.194 31.194 0 0 0 2.27-1.132c0.202-0.111 0.407-0.208 0.607-0.324 0.355-0.205 0.699-0.422 1.043-0.638 0.076-0.048 0.153-0.092 0.229-0.14a32.69 32.69 0 0 0 1.904-1.324l0.308-0.234a31.978 31.978 0 0 0 3.564-3.179l0.206-0.212a32.165 32.165 0 0 0 1.539-1.751c0.075-0.099 0.153-0.2 0.233-0.3z m-522.168-9.603l-15.144-56.516a31.963 31.963 0 0 0-2.297-6.061c-6.656-13.313-21.959-20.573-36.895-16.566l-56.516 15.143 68.461-118.578c22.579 17.63 47.305 32.177 73.882 43.419a326.58 326.58 0 0 0 40.699 14.121l-72.19 125.038z m159.174-177.37c-70.517 0-136.813-27.461-186.676-77.323-49.863-49.863-77.324-116.16-77.324-186.677s27.461-136.813 77.324-186.676 116.159-77.324 186.676-77.324 136.813 27.461 186.677 77.324c49.862 49.863 77.323 116.16 77.323 186.677s-27.461 136.813-77.323 186.677c-49.863 49.862-116.16 77.322-186.677 77.322z m127.683 38.211c26.577-11.241 51.303-25.788 73.882-43.419l70.058 121.343-56.517-15.143c-17.069-4.577-34.618 5.557-39.191 22.627l-15.144 56.516-73.787-127.803a326.693 326.693 0 0 0 40.699-14.121z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-me-->
<view a:if="{{name === 'line-me'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M924.781 337.608c-22.566-53.352-54.864-101.259-95.997-142.393-41.134-41.133-89.041-73.431-142.393-95.997C631.14 75.85 572.465 64 512 64S392.86 75.85 337.608 99.219c-53.352 22.565-101.259 54.864-142.393 95.997-41.133 41.133-73.431 89.041-95.997 142.392C75.85 392.861 64 451.535 64 512c0 60.466 11.85 119.14 35.219 174.392 22.565 53.352 54.864 101.26 95.997 142.393s89.041 73.431 142.392 95.997C392.861 948.15 451.535 960 512 960c60.466 0 119.14-11.85 174.392-35.219 53.352-22.566 101.26-54.864 142.393-95.997 41.133-41.134 73.431-89.041 95.997-142.393C948.15 631.14 960 572.465 960 512s-11.85-119.14-35.219-174.392zM512 896c-100.473 0-195.052-38.332-267.047-108.068 19.964-49.448 53.065-92.077 96.576-124.087C391.189 627.311 450.138 608 512 608s120.811 19.311 170.472 55.845c43.511 32.01 76.611 74.639 96.575 124.087C707.051 857.668 612.473 896 512 896z m0-352c-70.58 0-128-57.42-128-128s57.42-128 128-128c70.579 0 128 57.42 128 128s-57.421 128-128 128z m312.938 190.693a351.927 351.927 0 0 0-28.331-45.862 353.207 353.207 0 0 0-76.21-76.538c-27.19-20.003-56.657-35.77-87.691-47.1C676.166 529.963 704 476.175 704 416c0-105.869-86.131-192-192-192s-192 86.131-192 192c0 60.175 27.834 113.963 71.295 149.193-31.034 11.33-60.502 27.097-87.692 47.1a353.225 353.225 0 0 0-76.209 76.538 351.819 351.819 0 0 0-28.331 45.862C152.898 670.113 128 592.971 128 512c0-102.57 39.943-199 112.471-271.529C312.999 167.943 409.43 128 512 128s199.001 39.943 271.529 112.471S896 409.43 896 512c0 80.971-24.899 158.113-71.062 222.693z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-team-->
<view a:if="{{name === 'line-team'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M669.102 654.089c39.847-23.658 73.758-56.33 99.051-95.648 31.072-48.305 47.496-104.344 47.496-162.058 0-79.952-31.053-155.122-87.437-211.665C671.804 128.152 596.798 97 517.013 97s-154.791 31.152-211.199 87.719c-56.384 56.543-87.437 131.713-87.437 211.665 0 60.505 17.923 118.808 51.831 168.605 27.755 40.762 64.655 73.7 107.763 96.426l-57.494 218.441c-4.498 17.091 5.71 34.593 22.801 39.091 17.095 4.498 34.593-5.71 39.092-22.801l64.276-244.211c4.079-15.498-3.935-31.622-18.75-37.73-42.518-17.528-78.753-47.002-104.787-85.236-26.646-39.133-40.731-84.98-40.731-132.584 0-62.89 24.418-122.011 68.755-166.473C395.445 185.473 454.356 161 517.013 161s121.567 24.473 165.881 68.911c44.337 44.461 68.755 103.583 68.755 166.473 0 45.41-12.905 89.476-37.322 127.434-23.799 36.997-57.281 66.507-96.829 85.341-14.104 6.717-21.298 22.637-17.016 37.662l71.233 249.951c4.009 14.066 16.824 23.238 30.758 23.238a32 32 0 0 0 8.787-1.234c16.996-4.844 26.848-22.548 22.004-39.545l-64.162-225.142z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M236.612 619.767c-30.481 0-51.497-21.59-63.76-39.703C155.412 554.305 145 519.582 145 487.18c0-35.3 16.112-75.12 39.182-96.834 12.869-12.113 13.482-32.365 1.369-45.234-12.114-12.869-32.365-13.481-45.234-1.369C104.283 377.658 81 433.961 81 487.18c0 45.359 14.163 92.292 38.856 128.766 20.209 29.85 45.943 50.547 74.768 60.681l-62.74 188.692c-5.576 16.771 3.498 34.886 20.269 40.462a31.976 31.976 0 0 0 10.102 1.644c13.405 0 25.898-8.493 30.36-21.912l74.363-223.647a32 32 0 0 0-30.366-42.099zM911.144 615.945C935.837 579.472 950 532.539 950 487.18c0-53.219-23.283-109.521-59.316-143.438-12.87-12.112-33.121-11.5-45.234 1.369s-11.5 33.122 1.369 45.234C869.888 412.06 886 451.879 886 487.18c0 32.402-10.412 67.125-27.853 92.885-12.263 18.112-33.278 39.703-63.76 39.703a32 32 0 0 0-30.365 42.097l74.363 223.647c4.462 13.42 16.954 21.912 30.36 21.912 3.347 0 6.751-0.529 10.102-1.644 16.771-5.576 25.845-23.691 20.269-40.462l-62.74-188.692c28.824-10.133 54.559-30.831 74.768-60.681z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-userconfirmation-->
<view a:if="{{name === 'line-userconfirmation'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M731.701 579.397c8.85-15.298 3.622-34.873-11.676-43.723-24.291-14.052-49.921-25.536-76.437-34.357a226.986 226.986 0 0 0 26.803-22.925C712.7 436.084 736 379.833 736 320s-23.3-116.084-65.608-158.392S571.832 96 512 96c-59.833 0-116.084 23.3-158.393 65.608C311.3 203.916 288 260.167 288 320s23.3 116.084 65.607 158.392a226.96 226.96 0 0 0 26.734 22.875 416.054 416.054 0 0 0-30.278 11.437c-49.541 20.954-94.026 50.945-132.221 89.14s-68.185 82.68-89.139 132.221C107.003 785.371 96 839.854 96 896c0 17.673 14.327 32 32 32s32-14.327 32-32c0-94.022 36.614-182.418 103.099-248.901C329.582 580.614 417.978 544 512 544c61.892 0 122.744 16.277 175.979 47.073 15.299 8.851 34.874 3.621 43.722-11.676zM352 320c0-88.224 71.775-160 160-160s160 71.776 160 160-71.775 160-160 160-160-71.776-160-160zM918.628 663.373c-12.499-12.499-32.76-12.496-45.255-0.001L685.999 850.745l-82.105-82.106c-12.496-12.496-32.758-12.496-45.254 0-12.497 12.497-12.497 32.759 0 45.256l104.732 104.732c0.28 0.28 0.569 0.547 0.857 0.815 0.113 0.105 0.222 0.217 0.336 0.32 6.079 5.493 13.757 8.239 21.435 8.239 8.189 0 16.379-3.124 22.628-9.373l0.038-0.04 209.961-209.96c12.497-12.497 12.497-32.759 0.001-45.255z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-user-->
<view a:if="{{name === 'line-user'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M895.296 734.064c-20.954-49.541-50.945-94.026-89.14-132.221s-82.68-68.186-132.221-89.14a416.054 416.054 0 0 0-30.278-11.437 226.96 226.96 0 0 0 26.734-22.875C712.7 436.084 736 379.833 736 320s-23.3-116.084-65.608-158.392C628.084 119.3 571.832 96 512 96c-59.833 0-116.084 23.3-158.393 65.608C311.3 203.916 288 260.167 288 320s23.3 116.084 65.607 158.392a226.96 226.96 0 0 0 26.734 22.875 416.054 416.054 0 0 0-30.278 11.437c-49.541 20.954-94.026 50.945-132.221 89.14s-68.185 82.68-89.139 132.221C107.003 785.371 96 839.854 96 896c0 17.673 14.327 32 32 32s32-14.327 32-32c0-94.022 36.614-182.418 103.099-248.901C329.582 580.614 417.978 544 512 544s182.417 36.614 248.901 103.099S864 801.978 864 896c0 17.673 14.327 32 32 32s32-14.327 32-32c0-56.146-11.004-110.629-32.704-161.936zM352 320c0-88.224 71.775-160 160-160s160 71.776 160 160-71.775 160-160 160-160-71.776-160-160z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-reduceuser-->
<view a:if="{{name === 'line-reduceuser'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M731.701 579.397c8.85-15.298 3.622-34.873-11.676-43.723-24.291-14.052-49.921-25.536-76.437-34.357a226.986 226.986 0 0 0 26.803-22.925C712.7 436.084 736 379.833 736 320s-23.3-116.084-65.608-158.392C628.084 119.3 571.832 96 512 96c-59.833 0-116.084 23.3-158.393 65.608C311.3 203.916 288 260.167 288 320s23.3 116.084 65.607 158.392a226.96 226.96 0 0 0 26.734 22.875 416.054 416.054 0 0 0-30.278 11.437c-49.541 20.954-94.026 50.945-132.221 89.14s-68.185 82.68-89.139 132.221C107.003 785.371 96 839.854 96 896c0 17.673 14.327 32 32 32s32-14.327 32-32c0-94.022 36.614-182.418 103.099-248.901C329.582 580.614 417.978 544 512 544c61.892 0 122.744 16.277 175.979 47.073 15.299 8.851 34.874 3.621 43.722-11.676zM512 480c-88.225 0-160-71.776-160-160s71.775-160 160-160 160 71.776 160 160-71.775 160-160 160zM895 759H687c-17.673 0-32 14.327-32 32s14.327 32 32 32h208c17.673 0 32-14.327 32-32s-14.327-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-scan-->
<view a:if="{{name === 'line-scan'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M128 352c17.673 0 32-14.327 32-32V192c0-17.645 14.355-32 32-32h128c17.673 0 32-14.327 32-32s-14.327-32-32-32H192c-52.935 0-96 43.065-96 96v128c0 17.673 14.327 32 32 32zM320 864H192c-17.645 0-32-14.355-32-32V704c0-17.673-14.327-32-32-32s-32 14.327-32 32v128c0 52.935 43.065 96 96 96h128c17.673 0 32-14.327 32-32s-14.327-32-32-32zM896 672c-17.673 0-32 14.327-32 32v128c0 17.645-14.355 32-32 32H704c-17.673 0-32 14.327-32 32s14.327 32 32 32h128c52.935 0 96-43.065 96-96V704c0-17.673-14.327-32-32-32zM832 96H704c-17.673 0-32 14.327-32 32s14.327 32 32 32h128c17.645 0 32 14.355 32 32v128c0 17.673 14.327 32 32 32s32-14.327 32-32V192c0-52.935-43.065-96-96-96zM160 512c0 17.673 14.327 32 32 32h640c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32H192c-17.673 0-32 14.327-32 32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-check-->
<view a:if="{{name === 'line-check'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M416 896H224c-17.6 0-32-14.4-32-32V160c0-17.6 14.4-32 32-32h576c17.6 0 32 14.4 32 32v256c0 17.7 14.3 32 32 32s32-14.3 32-32V160c0-52.9-43.1-96-96-96H224c-52.9 0-96 43.1-96 96v704c0 52.9 43.1 96 96 96h192c17.7 0 32-14.3 32-32s-14.3-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M704 288c0-17.7-14.3-32-32-32H352c-17.7 0-32 14.3-32 32s14.3 32 32 32h320c17.7 0 32-14.3 32-32zM352 480c-17.7 0-32 14.3-32 32s14.3 32 32 32h192c17.7 0 32-14.3 32-32s-14.3-32-32-32H352zM950.6 649.4c-12.5-12.5-32.8-12.5-45.3 0L672 882.7 534.6 745.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l160 160c4.7 4.7 10.5 7.6 16.5 8.8 2 0.4 4.1 0.6 6.1 0.6s4.1-0.2 6.1-0.6c6.1-1.2 11.8-4.1 16.5-8.8l256-256c12.6-12.6 12.6-32.8 0.1-45.3z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-schedule-->
<view a:if="{{name === 'line-schedule'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M864 192h-96v-32c0-17.673-14.327-32-32-32s-32 14.327-32 32v32H320v-32c0-17.673-14.327-32-32-32s-32 14.327-32 32v32h-96c-52.935 0-96 43.065-96 96v512c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V288c0-52.935-43.065-96-96-96z m-704 64h96v32c0 17.673 14.327 32 32 32s32-14.327 32-32v-32h384v32c0 17.673 14.327 32 32 32s32-14.327 32-32v-32h96c17.645 0 32 14.355 32 32v96H128v-96c0-17.645 14.355-32 32-32z m704 576H160c-17.645 0-32-14.355-32-32V448h768v352c0 17.645-14.355 32-32 32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M625.137 521.373L466.745 679.765l-67.882-67.882c-12.497-12.498-32.758-12.498-45.255 0-12.497 12.496-12.497 32.758 0 45.254l90.51 90.51c6.249 6.249 14.438 9.373 22.627 9.373s16.379-3.124 22.627-9.373l181.019-181.019c12.497-12.497 12.497-32.759 0-45.255-12.495-12.498-32.756-12.498-45.254 0z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--pay-successful-->
<view a:if="{{name === 'pay-successful'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M912 404.94C912 179.08 732.92 0 507.06 0 291.08 0 112 179.08 112 404.94 112 620.92 291.08 800 507.06 800 732.92 800 912 620.92 912 404.94z' fill='{{(isStr ? colors : colors[0]) || 'rgb(9,187,7)'}}' /%3E%3Cpath d='M316.092 429.124c-3.064-3.168-3.408-8.648-0.804-12.188l13.664-18.6a7.8 7.8 0 0 1 11.116-1.56l94.936 72.76c7.008 5.372 18.216 5.124 24.968-0.5l249.032-207.464c3.4-2.832 8.72-2.592 11.844 0.496l11.76 11.616c3.148 3.108 3.14 8.14 0.02 11.208L459.08 554.104a15.8 15.8 0 0 1-22.5-0.28L316.092 429.12z' fill='{{(isStr ? colors : colors[1]) || 'rgb(255,255,255)'}}' /%3E%3Cpath d='M224 924a286 52 0 1 0 572 0 286 52 0 1 0-572 0Z' fill='{{(isStr ? colors : colors[2]) || 'rgb(242,244,248)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Discount-->
<view a:if="{{name === 'Discount'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M870.4 390.229333l30.72 30.72c24.32 23.893333 37.546667 56.32 37.546667 90.453334a126.933333 126.933333 0 0 1-37.546667 91.733333l-0.426667 0.426667-30.293333 30.293333c-11.989333 11.946667-18.816 28.16-18.816 45.269333v43.904c0 70.826667-57.6 128.469333-128.469333 128.469334h-43.946667c-17.066667 0-33.28 6.784-45.226667 18.730666l-30.762666 30.72c-25.173333 25.216-58.026667 37.546667-90.922667 37.546667a129.28 129.28 0 0 1-90.88-37.077333l-31.189333-31.189334c-11.946667-11.946667-28.16-18.730667-45.226667-18.730666h-43.946667a128.597333 128.597333 0 0 1-128.512-128.469334v-43.904c0-17.109333-6.826667-33.322667-18.773333-45.696l-30.72-30.293333a128.938667 128.938667 0 0 1-0.426667-181.717333l31.146667-31.189334c11.946667-11.946667 18.773333-28.16 18.773333-45.653333v-43.52c0-70.826667 57.6-128.384 128.469334-128.384h43.946666c17.066667 0 33.28-6.869333 45.269334-18.816l30.72-30.72a128.768 128.768 0 0 1 181.845333-0.384l31.146667 31.104c11.946667 11.946667 28.16 18.816 45.226666 18.816h43.946667a128.554667 128.554667 0 0 1 128.512 128.384v43.989333c0 17.024 6.826667 33.237333 18.773333 45.184z m-468.266667 268.8c10.24 0 19.626667-3.84 26.453334-11.093333l219.392-219.264a37.632 37.632 0 0 0 0-52.949333 37.205333 37.205333 0 0 0-52.48 0l-219.392 219.306666c-14.506667 14.506667-14.506667 38.4 0 52.906667 6.826667 7.253333 16.213333 11.093333 26.026666 11.093333z m182.272-37.546666a37.333333 37.333333 0 1 0 74.666667 0.426666 37.333333 37.333333 0 0 0-74.666667-0.426666z m-181.845333-256.426667c20.48 0 37.12 16.64 37.12 37.12 0 20.949333-16.64 37.546667-37.12 37.546667a37.546667 37.546667 0 0 1-37.546667-37.546667c0-20.48 17.066667-37.12 37.546667-37.12z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Buy-->
<view a:if="{{name === 'Buy'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M602.496 479.274667h118.101333c17.92 0 32-14.805333 32-32.64a32 32 0 0 0-32-32.682667h-118.101333a32 32 0 0 0-32 32.682667c0 17.834667 14.08 32.64 32 32.64z m258.389333-226.389334c26.026667 0 43.050667 9.173333 60.117334 29.184 17.066667 20.053333 20.053333 48.768 16.213333 74.837334l-40.533333 285.653333c-7.68 54.912-53.76 95.36-107.861334 95.36H323.669333c-56.704 0-103.594667-44.373333-108.288-101.845333L176.213333 161.408 111.786667 150.101333a32.725333 32.725333 0 0 1 11.093333-64.426666l101.674667 15.658666c14.506667 2.645333 25.173333 14.805333 26.453333 29.610667l8.106667 97.536c1.28 13.994667 12.373333 24.405333 26.026666 24.405333h575.744z m-544 553.813334c-35.84 0-64.853333 29.653333-64.853333 66.218666 0 36.138667 29.013333 65.749333 64.853333 65.749334 35.370667 0 64.341333-29.610667 64.341334-65.749334 0-36.565333-29.013333-66.176-64.384-66.176z m479.573334 0c-35.797333 0-64.768 29.653333-64.768 66.218666 0 36.138667 29.013333 65.749333 64.810666 65.749334 35.370667 0 64.341333-29.610667 64.341334-65.749334 0-36.565333-29.013333-66.176-64.341334-66.176z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Ticket-->
<view a:if="{{name === 'Ticket'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M929.578667 449.664a31.36 31.36 0 0 1-21.845334 8.832c-30.549333 0-55.296 24.021333-55.296 53.333333 0 29.44 24.448 53.333333 54.698667 53.674667 17.066667 0.170667 31.530667 11.733333 31.530667 28.288v102.826667C938.666667 783.104 866.474667 853.333333 777.344 853.333333h-134.528a24.832 24.832 0 0 1-25.173333-24.448v-86.613333a30.208 30.208 0 0 0-30.976-30.037333 30.464 30.464 0 0 0-30.933334 30.037333v86.613333a24.789333 24.789333 0 0 1-25.130666 24.448H246.656C157.952 853.333333 85.333333 783.189333 85.333333 696.576v-102.826667c0-16.512 14.506667-28.074667 31.530667-28.245333 30.293333-0.298667 54.698667-24.234667 54.698667-53.717333 0-28.458667-23.893333-50.090667-55.296-50.090667a31.402667 31.402667 0 0 1-21.845334-8.832 29.610667 29.610667 0 0 1-9.088-21.248V327.808C85.333333 241.365333 158.122667 170.666667 247.082667 170.666667h283.52c13.866667 0 25.173333 10.922667 25.173333 24.448v102.613333a30.72 30.72 0 0 0 30.933333 30.08c17.322667 0 30.933333-13.653333 30.933334-30.08V195.114667c0-13.525333 11.264-24.448 25.173333-24.448h134.528C866.432 170.666667 938.666667 240.810667 938.666667 327.424v100.992a29.610667 29.610667 0 0 1-9.088 21.248z m-342.869334 184.789333c17.322667 0 30.933333-13.653333 30.933334-30.037333v-160.341333a30.464 30.464 0 0 0-30.976-30.08 30.72 30.72 0 0 0-30.933334 30.08v160.341333a30.72 30.72 0 0 0 30.976 30.037333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Send-->
<view a:if="{{name === 'Send'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M914.56 110.165333a82.474667 82.474667 0 0 0-82.346667-21.461333L145.408 288.384a81.92 81.92 0 0 0-59.050667 64.938667c-6.058667 32 15.146667 72.704 42.794667 89.685333l214.741333 132.010667a55.637333 55.637333 0 0 0 68.693334-8.277334l245.888-247.424a31.317333 31.317333 0 0 1 45.226666 0c12.373333 12.458667 12.373333 32.64 0 45.525334l-246.314666 247.466666c-18.261333 18.346667-21.674667 46.933333-8.234667 69.12l131.2 216.874667c15.36 25.770667 41.813333 40.362667 70.826667 40.362667 3.413333 0 7.253333 0 10.709333-0.426667 33.28-4.266667 59.733333-27.050667 69.546667-59.306667l203.648-685.866666c8.96-29.226667 0.853333-61.013333-20.48-82.901334z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--gantanhao-->
<view a:if="{{name === 'gantanhao'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M969.182003 701.303054C942.98583 761.818425 907.560805 814.394635 862.909139 859.030351 818.273423 903.682017 765.86402 939.121773 705.727336 965.303215 645.576031 991.515341 581.182839 1004.606061 512.545547 1004.606061 443.925315 1004.606061 379.349482 991.515341 318.818158 965.303215 258.302785 939.121773 205.726577 903.682017 161.090861 859.030351 116.439196 814.393527 80.999438 761.818425 54.817996 701.303054 28.605873 640.772838 15.515152 576.197117 15.515152 507.575665 15.515152 438.938373 28.605873 374.545181 54.817996 314.393878 80.999438 254.257304 116.439196 201.8479 161.090861 157.212073 205.726577 112.560409 258.302785 77.135381 318.818158 50.939208 379.349482 24.743034 443.925203 11.636364 512.545547 11.636364 581.182839 11.636364 645.576031 24.742924 705.727336 50.939208 765.8628 77.1366 818.273311 112.560409 862.909139 157.212073 907.560805 201.848897 942.984611 254.258412 969.182003 314.393878 995.378179 374.545181 1008.484848 438.938373 1008.484848 507.575665 1008.484848 576.197117 995.379287 640.772838 969.182003 701.303054L969.182003 701.303054Z' fill='{{(isStr ? colors : colors[0]) || 'rgb(255,46,0)'}}' /%3E%3Cpath d='M512 709.220647C472.332325 709.220647 440.203301 741.349668 440.203301 781.017346 440.203301 820.68502 472.332325 852.814044 512 852.814044 551.667675 852.814044 583.796699 820.68502 583.796699 781.017346 583.796699 741.349668 551.667675 709.220647 512 709.220647L512 709.220647Z' fill='{{(isStr ? colors : colors[1]) || 'rgb(255,255,255)'}}' /%3E%3Cpath d='M512 639.709091C492.184111 639.709091 476.101651 606.196596 476.101651 564.820837L440.203301 227.823683C440.203301 186.447921 472.332325 152.935427 512 152.935427 551.667675 152.935427 583.796699 186.447921 583.796699 227.823683L547.898349 564.820837C547.898349 606.196596 531.815889 639.709091 512 639.709091L512 639.709091Z' fill='{{(isStr ? colors : colors[2]) || 'rgb(255,255,255)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--user-plus-->
<view a:if="{{name === 'user-plus'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1280 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M192 256a256 256 0 1 1 512 0 256 256 0 1 1-512 0zM0 964.6C0 767.6 159.6 608 356.6 608h182.8c197 0 356.6 159.6 356.6 356.6 0 32.8-26.6 59.4-59.4 59.4H59.4C26.6 1024 0 997.4 0 964.6zM1008 624v-128h-128c-26.6 0-48-21.4-48-48s21.4-48 48-48h128v-128c0-26.6 21.4-48 48-48s48 21.4 48 48v128h128c26.6 0 48 21.4 48 48s-21.4 48-48 48h-128v128c0 26.6-21.4 48-48 48s-48-21.4-48-48z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--hospital-user-->
<view a:if="{{name === 'hospital-user'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1152 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M96 0C43 0 0 43 0 96v416h288c17.6 0 32 14.4 32 32s-14.4 32-32 32H0v128h288c17.6 0 32 14.4 32 32s-14.4 32-32 32H0v160c0 53 43 96 96 96h435.8c-12.6-20.4-19.8-44.4-19.8-70.2 0-93.8 51.6-175.6 128-218.4V96c0-53-43-96-96-96H96z m208 128h32c17.6 0 32 14.4 32 32v48h48c17.6 0 32 14.4 32 32v32c0 17.6-14.4 32-32 32h-48v48c0 17.6-14.4 32-32 32h-32c-17.6 0-32-14.4-32-32v-48h-48c-17.6 0-32-14.4-32-32v-32c0-17.6 14.4-32 32-32h48V160c0-17.6 14.4-32 32-32z m720 416a160 160 0 1 0-320 0 160 160 0 1 0 320 0zM576 954.2c0 38.6 31.2 69.8 69.8 69.8h436.4c38.6 0 69.8-31.2 69.8-69.8 0-102.8-83.4-186.2-186.2-186.2H762.2c-102.8 0-186.2 83.4-186.2 186.2z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--person-circle-plus-->
<view a:if="{{name === 'person-circle-plus'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1152 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M224 96a96 96 0 1 1 192 0 96 96 0 1 1-192 0z m80 608v256c0 35.4-28.6 64-64 64s-64-28.6-64-64V513.8l-57.2 95.2c-18.2 30.2-57.6 40-87.8 21.8s-40-57.6-21.8-87.8l116.6-194c34.8-57.8 97.2-93.2 164.6-93.2h59.4c67.4 0 129.8 35.4 164.6 93.2l89.8 149.4c-32.2 35.2-57.2 77-73.2 123-3.8-3.6-7-7.8-9.8-12.6L464 513.8V960c0 35.4-28.6 64-64 64s-64-28.6-64-64V704h-32z m560-256a288 288 0 1 1 0 576 288 288 0 1 1 0-576z m32 160c0-17.6-14.4-32-32-32s-32 14.4-32 32v96h-96c-17.6 0-32 14.4-32 32s14.4 32 32 32h96v96c0 17.6 14.4 32 32 32s32-14.4 32-32v-96h96c17.6 0 32-14.4 32-32s-14.4-32-32-32h-96v-96z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--nfc-->
<view a:if="{{name === 'nfc'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M1022.208 84.48C1019.52 42.666667 987.434667 10.922667 944.853333 8.405333c-47.018667-2.816-199.168-5.12-242.901333-5.12 78.165333 53.930667 88.832 155.477333 96.213333 344.149334 4.309333 111.786667 0.426667 503.424 0.085334 520.021333l-2.090667 106.837333-410.794667-410.88v-128.597333l326.656 326.741333c0.853333-64.682667 1.706667-148.992 1.706667-226.090666 0-75.093333-1.109333-143.104-3.242667-178.901334-12.288-205.610667-31.445333-301.952-138.794666-339.712-32.853333-11.52-63.445333-14.293333-114.474667-14.976C415.061333 1.365333 121.514667 1.578667 79.104 3.882667 34.133333 6.272 3.84 38.997333 1.792 81.066667c-2.048 41.685333-2.730667 818.090667 0 860.373333 2.645333 41.813333 34.773333 73.557333 77.312 76.032 47.018667 2.858667 199.168 3.2 242.944 3.2-78.165333-53.930667-88.874667-155.434667-96.213333-344.149333-4.266667-111.786667-0.384-503.466667 0-520.021334l2.005333-106.837333 410.837333 410.88v128.554667L311.978667 262.485333c-0.853333 64.597333-1.706667 148.906667-1.706667 226.048 0 75.093333 1.109333 143.104 3.285333 178.858667 12.288 205.653333 31.402667 301.952 138.752 339.712 32.853333 11.562667 63.445333 14.378667 114.474667 15.018667 42.112 0.512 335.701333 0.256 378.069333-2.005334 45.056-2.389333 75.306667-35.072 77.354667-77.269333 2.048-41.642667 2.730667-816.085333 0-858.368' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--add-->
<view a:if="{{name === 'add'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M711.3728 540.4672H540.4672v170.9056c0 15.6672-12.6976 28.4672-28.4672 28.4672-15.6672 0-28.4672-12.8-28.4672-28.4672V540.4672H312.6272c-15.6672 0-28.4672-12.6976-28.4672-28.4672 0-15.6672 12.8-28.4672 28.4672-28.4672h170.9056V312.6272c0-15.6672 12.6976-28.4672 28.4672-28.4672 15.6672 0 28.4672 12.8 28.4672 28.4672v170.9056h170.9056c15.6672 0 28.4672 12.6976 28.4672 28.4672-0.1024 15.6672-12.9024 28.4672-28.4672 28.4672M512 56.9344c-251.2896 0-455.0656 203.776-455.0656 455.0656s203.776 455.0656 455.0656 455.0656 455.0656-203.776 455.0656-455.0656S763.2896 56.9344 512 56.9344' fill='{{(isStr ? colors : colors[0]) || 'rgb(50,50,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--managerexam-->
<view a:if="{{name === 'managerexam'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M864.7 542.1c-31.5-31.5-70.7-52.4-113.3-61.1-45.6-31.6-98.7-48.3-154.2-48.3H575c50.8-31.5 84.7-87.7 84.7-151.7 0-98.4-80-178.4-178.4-178.4s-178.4 80-178.4 178.4c0 64 33.9 120.2 84.7 151.7h-22.2c-72.4 0-140.6 28.4-192.1 79.9-51.5 51.5-79.9 119.7-79.9 192.1v58c0 5.7 0.2 11.6 0.6 17.4l2.1 32.8h416.1c9.7 16.7 21.6 32.2 35.6 46.2 42.3 42.3 98.6 65.7 158.5 65.7s116.2-23.3 158.5-65.7 65.7-98.6 65.7-158.5-23.4-116.1-65.8-158.5zM481.3 172.6c59.8 0 108.4 48.6 108.4 108.4S541 389.4 481.3 389.4c-59.8 0-108.4-48.6-108.4-108.4s48.6-108.4 108.4-108.4z m0.8 528c0 14.3 1.4 28.5 4 42.3H163.4v-38.2c0-111.4 90.6-201.9 201.9-201.9h235.3c-19.2 10.3-37 23.5-52.9 39.3-42.3 42.4-65.6 98.7-65.6 158.5z m224.1 154.2c-85 0-154.2-69.2-154.2-154.2s69.2-154.2 154.2-154.2 154.2 69.2 154.2 154.2-69.2 154.2-154.2 154.2z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M691.1 698.6l-33.9-34-49.5 49.5 83.4 83.5 126.2-126.2-49.5-49.5z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--pushtype-->
<view a:if="{{name === 'pushtype'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M430.5 715.8H170.6V201.7h634.9v105c0 19.3 15.7 35 35 35s35-15.7 35-35v-108c0-37-30-66.9-66.9-66.9h-641c-37 0-66.9 30-66.9 66.9V719c0 37 30 66.9 66.9 66.9h263c19.3 0 35-15.7 35-35-0.1-19.4-15.8-35.1-35.1-35.1z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M910 392.2c-10.5-10.5-26.3-13.8-40.1-8.5l-470.2 182c-13.5 5.2-22.9 17.9-23.9 32.4-1 14.4 6.5 28.4 19.1 35.4L570.8 732l97.8 175.2c6.6 11.9 19.3 19.3 32.8 19.3 0.9 0 1.7 0 2.6-0.1 14.5-1 27.2-10.4 32.4-23.9l182-470.2c5.4-13.8 2.1-29.6-8.4-40.1zM696.1 812.9L640 712.4l35.7-35.7c13.7-13.7 13.7-35.8 0-49.5-13.7-13.7-35.8-13.7-49.5 0l-35.7 35.7-101.3-56.7 337.5-130.7-130.6 337.4zM238.7 288.5h378.6v70H238.7zM238.7 438.3h192.8v70H238.7z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--gdsbrand-->
<view a:if="{{name === 'gdsbrand'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M713.5 142.8H305.3l-207 243.5 414.1 513.5L926.9 386 713.5 142.8zM189.3 387.5l148.5-174.7h344.1l153.7 175.1-323 400.5-323.3-400.9z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M512.3 558.1L402.8 448.6l-49.5 49.5L512 656.8l158.3-156-49.2-49.8z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--line-examine-->
<view a:if="{{name === 'line-examine'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M416 896H224c-17.6 0-32-14.4-32-32V160c0-17.6 14.4-32 32-32h576c17.6 0 32 14.4 32 32v256c0 17.7 14.3 32 32 32s32-14.3 32-32V160c0-52.9-43.1-96-96-96H224c-52.9 0-96 43.1-96 96v704c0 52.9 43.1 96 96 96h192c17.7 0 32-14.3 32-32s-14.3-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M704 288c0-17.7-14.3-32-32-32H352c-17.7 0-32 14.3-32 32s14.3 32 32 32h320c17.7 0 32-14.3 32-32zM352 480c-17.7 0-32 14.3-32 32s14.3 32 32 32h192c17.7 0 32-14.3 32-32s-14.3-32-32-32H352zM864 768h-96v-37.5c37.2-13.2 64-48.8 64-90.5 0-52.9-43.1-96-96-96s-96 43.1-96 96c0 41.7 26.8 77.3 64 90.5V768h-96c-52.9 0-96 43.1-96 96v32c0 35.3 28.7 64 64 64h320c35.3 0 64-28.7 64-64v-32c0-52.9-43.1-96-96-96zM736 608c17.6 0 32 14.4 32 32s-14.4 32-32 32-32-14.4-32-32 14.4-32 32-32z m160 288H576v-32c0-17.6 14.4-32 32-32h256c17.6 0 32 14.4 32 32v32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--engexam-->
<view a:if="{{name === 'engexam'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M875.2 553.4V228.3H494.3V102.8h-389V868h451.8c41.1 36.7 93.6 56.8 149.1 56.8 59.9 0 116.2-23.3 158.5-65.7s65.7-98.6 65.7-158.5c0-54.7-19.5-106.4-55.2-147.2zM175.4 172.8h249v55.5h-249v-55.5z m0 167.8v-42.3h629.9v201.1c-30.4-15-64.1-22.9-99-22.9-59.9 0-116.2 23.3-158.5 65.7-42.3 42.3-65.7 98.6-65.7 158.5 0 34.3 7.7 67.4 22.1 97.4H175.4V340.6z m530.8 514.2c-85 0-154.2-69.2-154.2-154.2s69.2-154.2 154.2-154.2 154.2 69.2 154.2 154.2-69.2 154.2-154.2 154.2z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M691.1 698.6l-33.9-34-49.5 49.5 83.4 83.5 126.2-126.2-49.5-49.5zM266.4 374.8h70v266.3h-70zM408.8 374.8h70v168.8h-70zM551.3 374.8h70v72.8h-70z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--finexam-->
<view a:if="{{name === 'finexam'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M930.4 701.7c0.3-60.3-23.1-117-65.7-159.6-35.3-35.3-80.2-57.3-128.9-63.7V261.7c0-25.6-20.6-46.5-45.9-46.5h-71.5v-81.5c0-25.9-20.6-47-45.9-47H138.1c-25.3 0-45.9 21.1-45.9 47v743.8c0 8.6 2.4 16.6 6.5 23.5 0.1 0.1 0.2 0.3 0.2 0.4 0.1 0.1 0.2 0.3 0.3 0.4 8.1 13.3 22.5 22.1 38.9 22.1h549.1c6.3 0.5 12.6 0.8 19 0.8s12.7-0.3 19-0.8h21.7c11 0 21.4-3.8 29.6-10.4 32.9-10.8 63-29.3 88.2-54.4 41.8-41.8 65.4-98.3 65.7-157.4z m-768.2-440v-105h386.3v58.5H333c-19.3 0-35 15.7-35 35s15.7 35 35 35h332.8V480c-44.5 8-85.5 29.4-118.2 62-42.3 42.3-65.7 98.6-65.7 158.5 0 57.5 21.5 111.7 60.7 153.4H162.2V261.7zM722 854h-31.6c-78.5-8-139.8-75.3-138.3-156.2 1.5-82 67.7-148.9 149.6-151.3 87-2.5 158.7 67.6 158.7 154.1 0 79.7-60.8 145.5-138.4 153.4z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M691.1 698.6l-33.9-34-49.5 49.5 83.4 83.5 126.2-126.2-49.5-49.5zM409.6 356.3L338 427.9l-71.6-71.6-44.1 44 58.8 58.8h-47.5v62.3h73.7v34.8h-73.7v62.3h73.7v58.1h62.3v-58.1h73.6v-62.3h-73.6v-34.8h73.6v-62.3h-48.3l58.7-58.8z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--purexam-->
<view a:if="{{name === 'purexam'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M895.7 580.7L920 277.9c1.1-13.9-3.7-27.9-13.2-38.2-9.6-10.4-23.2-16.4-37.4-16.4H672.5c-8.4-29.4-24.4-56.2-46.7-77.3-30.6-29-70.4-45-112-45-41.6 0-81.4 16-112 45-22.3 21.1-38.3 47.9-46.7 77.3H158.4c-14.3 0-28 6-37.6 16.6-9.5 10.5-14.2 24.5-12.9 38.6l49 528.6c2.4 26 24 45.7 50.3 45.9l336.7 2.1c1.3 1.4 2.6 2.7 3.9 4 42.3 42.3 98.6 65.7 158.5 65.7s116.2-23.3 158.5-65.7 65.7-98.6 65.7-158.5c-0.1-43.1-12.2-84.3-34.8-119.9zM513.8 171c36.1 0 67.7 20.9 83.9 52.3H429.9c16.2-31.4 47.7-52.3 83.9-52.3z m334.7 122.4l-4.3 53.9H184.5l-5-53.9h669zM224.9 783.1L191 417.2h647.6l-7.8 97c-36.6-24.5-79.6-37.7-124.6-37.7-59.9 0-116.2 23.3-158.5 65.7-42.3 42.3-65.7 98.6-65.7 158.5 0 29.3 5.6 57.8 16.3 84.2l-273.4-1.8z m481.3 71.7c-85 0-154.2-69.2-154.2-154.2s69.2-154.2 154.2-154.2 154.2 69.2 154.2 154.2-69.2 154.2-154.2 154.2z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M691.1 698.6l-33.9-34-49.5 49.5 83.4 83.5 126.2-126.2-49.5-49.5z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Chart-->
<view a:if="{{name === 'Chart'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M938.666667 451.2a32.64 32.64 0 0 1-32.64 32.64h-0.384a32.256 32.256 0 0 1-32.256-33.024V352.128c0-129.834667-71.68-201.514667-201.514667-201.514667H352.128c-130.218667 0-201.514667 71.68-201.514667 201.514667v320.085333c0 129.877333 71.68 201.173333 201.514667 201.173334h319.744c130.218667 0 201.514667-71.68 201.514667-201.130667a32.64 32.64 0 1 1 65.28 0C938.666667 836.565333 836.608 938.666667 672.213333 938.666667H352.128C187.434667 938.666667 85.333333 836.608 85.333333 672.213333V352.128C85.333333 187.434667 187.392 85.333333 352.128 85.333333h319.744C835.84 85.333333 938.666667 187.392 938.666667 352.128v99.072zM296.96 712.021333V438.826667a32.256 32.256 0 0 1 33.792-31.146667 32.64 32.64 0 0 1 31.530667 33.792v272.810667a32.682667 32.682667 0 0 1-65.28-2.261334z m183.893333-399.658666v400.042666a32.64 32.64 0 0 0 65.28 0V312.32a32.64 32.64 0 0 0-65.28 0z m180.906667 399.658666v-127.573333a32.64 32.64 0 0 1 65.28 0v127.573333a32.64 32.64 0 0 1-65.28 0z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--CloseSquare-->
<view a:if="{{name === 'CloseSquare'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M906.410667 493.994667a32.298667 32.298667 0 0 0 32.256-32.256v-109.653334C938.666667 187.434667 835.84 85.333333 672.128 85.333333h-320C187.434667 85.333333 85.333333 187.392 85.333333 352.256v320C85.333333 836.608 187.392 938.666667 352.128 938.666667h320.128c164.352 0 266.410667-102.058667 266.282667-266.538667a32.682667 32.682667 0 0 0-65.28 0c0 129.450667-71.253333 201.130667-201.130667 201.130667h-320c-129.834667 0-201.514667-71.68-201.514667-201.130667v-320c0-129.834667 71.68-201.514667 201.642667-201.514667h320c129.877333 0 201.130667 71.253333 201.130667 201.514667V460.672a32.64 32.64 0 0 0 32.64 32.554667v0.768h0.426666zM418.773333 560.853333l-29.184 29.184a32.981333 32.981333 0 0 0-1.621333 46.165334l0.938667 0.682666c12.458667 12.458667 32.512 12.8 45.354666 0.768l29.056-29.013333a32.682667 32.682667 0 0 0-44.544-47.786667z m227.328 70.741334a32.725333 32.725333 0 0 1-45.653333 0.512l-1.621333-1.578667L394.88 426.666667a34.858667 34.858667 0 0 1-0.554667-47.274667 32.213333 32.213333 0 0 1 45.610667-0.554667l0.341333 0.298667 79.573334 79.616 72.405333-72.448a33.408 33.408 0 0 1 46.421333 0.298667c2.304 2.304 4.266667 4.906667 5.76 7.808a32.725333 32.725333 0 0 1-5.205333 39.466666l-72.106667 72.106667 78.421334 78.421333a32.853333 32.853333 0 0 1 0.853333 46.933334l-0.298667 0.298666z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Logout-->
<view a:if="{{name === 'Logout'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M149.333333 595.157333a31.616 31.616 0 0 1-32 31.189334 31.616 31.616 0 0 1-32-31.189334V286.464C85.333333 187.349333 168.021333 106.666667 269.738667 106.666667h202.496c101.888 0 184.746667 80.853333 184.746666 180.266666v37.717334c0 17.194667-14.293333 31.146667-32 31.146666a31.616 31.616 0 0 1-32-31.146666v-37.717334c0-65.024-54.186667-117.845333-120.746666-117.845333H269.738667C203.306667 169.088 149.333333 221.781333 149.333333 286.464v308.693333z m443.690667 104.234667c0-17.237333 14.336-31.232 32-31.232 17.621333 0 32 13.994667 32 31.232v38.144c0 99.114667-82.730667 179.797333-184.405333 179.797333H270.08C168.234667 917.333333 85.333333 836.48 85.333333 737.066667a31.573333 31.573333 0 0 1 32-31.146667 31.573333 31.573333 0 0 1 32 31.146667c0 65.024 54.186667 117.845333 120.746667 117.845333h202.538667c66.389333 0 120.405333-52.650667 120.405333-117.376v-38.144z m343.210667-199.296a32 32 0 0 0-29.568-19.328H407.466667a31.701333 31.701333 0 0 0-32.085334 31.232c0 17.237333 14.378667 31.232 32.042667 31.232h421.802667l-66.56 64.597333a30.677333 30.677333 0 0 0-0.085334 44.16 32.554667 32.554667 0 0 0 45.226667 0.085334l121.472-117.930667a30.72 30.72 0 0 0 6.954667-34.048z m-120.362667-45.226667a32.426667 32.426667 0 0 1-22.528-9.045333l-30.634667-29.568a30.634667 30.634667 0 0 1-0.213333-44.074667 32.469333 32.469333 0 0 1 45.226667-0.256l30.634666 29.525334a30.677333 30.677333 0 0 1 0.256 44.117333 32.426667 32.426667 0 0 1-22.741333 9.301333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--user-gear-->
<view a:if="{{name === 'user-gear'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1280 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M448 0a256 256 0 1 1 0 512 256 256 0 1 1 0-512z m-91.4 608h182.8c23.6 0 46.8 2.4 69 6.6-4.2 37 14.8 71.2 43.6 89.6-33.2 21.2-53.4 63.2-40 106.6 8 25.8 18.8 51 32.8 75.2s30.4 46.2 48.8 66c31.4 33.8 79.2 36.8 114.4 17.4v1.8c0 18.4 5.4 37 15.8 52.6H59.4C26.6 1024 0 997.4 0 964.6 0 767.6 159.6 608 356.6 608zM872 436.4c0-14 9-26.6 22.6-29.6 21-4.8 43-7.4 65.4-7.4s44.4 2.6 65.4 7.4c13.6 3 22.6 15.6 22.6 29.6v61.2c15.8 6.8 30.8 15.4 44.6 25.6l49.8-28.6c12.2-7 27.4-5.4 37 4.8 15.2 16.2 28.6 34.4 40.2 54.4s20.6 40.8 27 62c4.2 13.4-2.2 27.4-14.4 34.4l-50 28.8c0.8 8 1.4 16.2 1.4 24.6s-0.4 16.4-1.4 24.6l50 28.8c12.2 7 18.4 21 14.4 34.4-6.6 21.2-15.6 42-27 62s-25 38.2-40.2 54.4c-9.6 10.2-25 11.8-37 4.8L1092.6 884c-13.8 10.2-28.6 18.8-44.6 25.6v61.2c0 14-9 26.6-22.6 29.6-21 4.8-43 7.4-65.4 7.4s-44.4-2.6-65.4-7.4c-13.6-3-22.6-15.6-22.6-29.6v-61.2c-16-6.8-31.2-15.4-45-25.8l-49.4 28.6c-12.2 7-27.4 5.4-37-4.8-15.2-16.2-28.6-34.4-40.2-54.4s-20.6-40.8-27-62c-4.2-13.4 2.2-27.4 14.4-34.4l49.6-28.6c-0.8-8.2-1.4-16.4-1.4-24.8s0.4-16.6 1.4-24.8L687.6 650c-12.2-7-18.4-21-14.4-34.4 6.6-21.2 15.4-42 27-62s25-38.2 40.2-54.4c9.6-10.2 24.8-11.8 37-4.8l49.6 28.6c13.8-10.2 29-18.8 45-25.8v-60.8z m184.2 267a96.2 96.2 0 1 0-192.2 0 96.2 96.2 0 1 0 192.2 0z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--comments-->
<view a:if="{{name === 'comments'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1280 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M416.02188 704c229.8 0 416-157.6 416-352S645.82188 0 416.02188 0 0.02188 157.6 0.02188 352c0 77.2 29.4 148.6 79.2 206.8-7 18.8-17.4 35.4-28.4 49.4-9.6 12.4-19.4 22-26.6 28.6-3.6 3.2-6.6 5.8-8.6 7.4-1 0.8-1.8 1.4-2.2 1.6l-0.4 0.4C2.02188 654.4-2.77812 668.8 1.62188 681.8S18.22188 704 32.02188 704c43.6 0 87.6-11.2 124.2-25 18.4-7 35.6-14.8 50.6-22.8C268.22188 686.6 339.62188 704 416.02188 704z m480-352c0 224.6-198.2 393.8-433 414 48.6 148.8 209.8 258 401 258 76.4 0 147.8-17.4 209.4-47.8 15 8 32 15.8 50.4 22.8 36.6 13.8 80.6 25 124.2 25 13.8 0 26.2-9 30.4-22.2 4.2-13.2-0.4-27.6-11.6-35.8l-0.4-0.4c-0.4-0.4-1.2-0.8-2.2-1.6-2-1.6-5-4-8.6-7.4-7.2-6.6-17-16.2-26.6-28.6-11-14-21.4-30.8-28.4-49.4 49.8-58 79.2-129.4 79.2-206.8 0-185.6-169.8-337.8-385.2-351 0.8 10.2 1.2 20.6 1.2 31z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--house-->
<view a:if="{{name === 'house'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1152 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M1151.6 511c0 36-30 64.2-64 64.2h-64l1.4 320.4c0 5.4-0.4 10.8-1 16.2V944c0 44.2-35.8 80-80 80h-32c-2.2 0-4.4 0-6.6-0.2-2.8 0.2-5.6 0.2-8.4 0.2H784c-44.2 0-80-35.8-80-80v-176c0-35.4-28.6-64-64-64h-128c-35.4 0-64 28.6-64 64v176c0 44.2-35.8 80-80 80h-111.8c-3 0-6-0.2-9-0.4-2.4 0.2-4.8 0.4-7.2 0.4h-32c-44.2 0-80-35.8-80-80V720c0-1.8 0-3.8 0.2-5.6v-139.2H64c-36 0-64-28-64-64.2 0-18 6-34 20-48L532.8 16c14-14 30-16 44-16s30 4 42 14l510.8 449c16 14 24 30 22 48z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--comment-dots-->
<view a:if="{{name === 'comment-dots'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512.1039 896c282.8 0 512-186.2 512-416S794.9039 64 512.1039 64 0.1039 250.2 0.1039 480c0 90.2 35.4 173.6 95.4 241.8-3.8 49-22.8 92.6-42.8 125.8-11 18.4-22.2 33.2-30.4 43.2-4.2 5-7.4 8.8-9.8 11.4-1.2 1.2-2 2.2-2.6 2.8l-0.6 0.6c-9.2 9.2-11.8 22.8-6.8 34.8 5 12 16.6 19.8 29.6 19.8 57.4 0 115.2-17.8 163.2-38.6 45.8-20 84.8-43.8 108.6-61.2 63.6 23 134 35.8 208.2 35.8zM256.1039 416a64 64 0 1 1 0 128 64 64 0 1 1 0-128z m256 0a64 64 0 1 1 0 128 64 64 0 1 1 0-128z m192 64a64 64 0 1 1 128 0 64 64 0 1 1-128 0z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--calculator-->
<view a:if="{{name === 'calculator'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M298.666667 85.333333h426.666666a85.333333 85.333333 0 0 1 85.333334 85.333334v682.666666a85.333333 85.333333 0 0 1-85.333334 85.333334H298.666667a85.333333 85.333333 0 0 1-85.333334-85.333334V170.666667a85.333333 85.333333 0 0 1 85.333334-85.333334m0 85.333334v170.666666h426.666666V170.666667H298.666667m0 256v85.333333h85.333333v-85.333333H298.666667m170.666666 0v85.333333h85.333334v-85.333333h-85.333334m170.666667 0v85.333333h85.333333v-85.333333h-85.333333m-341.333333 170.666666v85.333334h85.333333v-85.333334H298.666667m170.666666 0v85.333334h85.333334v-85.333334h-85.333334m170.666667 0v85.333334h85.333333v-85.333334h-85.333333m-341.333333 170.666667v85.333333h85.333333v-85.333333H298.666667m170.666666 0v85.333333h85.333334v-85.333333h-85.333334m170.666667 0v85.333333h85.333333v-85.333333h-85.333333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--cellphone-iphone-->
<view a:if="{{name === 'cellphone-iphone'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M682.666667 768H298.666667V170.666667h384m-192 768a64 64 0 0 1-64-64 64 64 0 0 1 64-64 64 64 0 0 1 64 64 64 64 0 0 1-64 64m170.666666-896h-341.333333A106.666667 106.666667 0 0 0 213.333333 149.333333v725.333334A106.666667 106.666667 0 0 0 320 981.333333h341.333333a106.666667 106.666667 0 0 0 106.666667-106.666666v-725.333334A106.666667 106.666667 0 0 0 661.333333 42.666667z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--comment-account-outline-->
<view a:if="{{name === 'comment-account-outline'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M384 938.666667a42.666667 42.666667 0 0 1-42.666667-42.666667v-128H170.666667a85.333333 85.333333 0 0 1-85.333334-85.333333V170.666667a85.333333 85.333333 0 0 1 85.333334-85.333334h682.666666a85.333333 85.333333 0 0 1 85.333334 85.333334v512a85.333333 85.333333 0 0 1-85.333334 85.333333h-260.266666l-157.866667 158.293333c-8.533333 8.106667-19.2 12.373333-29.866667 12.373334H384m42.666667-256v131.413333L558.08 682.666667H853.333333V170.666667H170.666667v512h256m256-85.333334H341.333333v-42.666666c0-56.746667 113.92-85.333333 170.666667-85.333334s170.666667 28.586667 170.666667 85.333334v42.666666m-170.666667-341.333333a85.333333 85.333333 0 0 1 85.333333 85.333333 85.333333 85.333333 0 0 1-85.333333 85.333334 85.333333 85.333333 0 0 1-85.333333-85.333334 85.333333 85.333333 0 0 1 85.333333-85.333333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--comment-check-outline-->
<view a:if="{{name === 'comment-check-outline'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M384 938.666667a42.666667 42.666667 0 0 1-42.666667-42.666667v-128H170.666667a85.333333 85.333333 0 0 1-85.333334-85.333333V170.666667a85.333333 85.333333 0 0 1 85.333334-85.333334h682.666666a85.333333 85.333333 0 0 1 85.333334 85.333334v512a85.333333 85.333333 0 0 1-85.333334 85.333333h-260.266666l-157.866667 158.293333c-8.533333 8.106667-19.2 12.373333-29.866667 12.373334H384m42.666667-256v131.413333L558.08 682.666667H853.333333V170.666667H170.666667v512h256m277.333333-341.333334L469.333333 576 320 426.666667l60.16-60.16L469.333333 455.253333l174.506667-174.08L704 341.333333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--comment-multiple-outline-->
<view a:if="{{name === 'comment-multiple-outline'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 981.333333a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H298.666667a85.333333 85.333333 0 0 1-85.333334-85.333334V298.666667a85.333333 85.333333 0 0 1 85.333334-85.333334h597.333333a85.333333 85.333333 0 0 1 85.333333 85.333334v426.666666a85.333333 85.333333 0 0 1-85.333333 85.333334h-174.933333l-157.866667 158.293333c-8.533333 8.106667-19.2 12.373333-29.866667 12.373333H512m42.666667-256v131.413334L686.08 725.333333H896V298.666667H298.666667v426.666666h256M128 640H42.666667V128a85.333333 85.333333 0 0 1 85.333333-85.333333h682.666667v85.333333H128v512z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--comment-text-outline-->
<view a:if="{{name === 'comment-text-outline'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M384 938.666667a42.666667 42.666667 0 0 1-42.666667-42.666667v-128H170.666667a85.333333 85.333333 0 0 1-85.333334-85.333333V170.666667a85.333333 85.333333 0 0 1 85.333334-85.333334h682.666666a85.333333 85.333333 0 0 1 85.333334 85.333334v512a85.333333 85.333333 0 0 1-85.333334 85.333333h-260.266666l-157.866667 158.293333c-8.533333 8.106667-19.2 12.373333-29.866667 12.373334H384m42.666667-256v131.413333L558.08 682.666667H853.333333V170.666667H170.666667v512h256M256 298.666667h512v85.333333H256V298.666667m0 170.666666h384v85.333334H256v-85.333334z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--AddUser-->
<view a:if="{{name === 'AddUser'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M589.824 313.770667c0-92.16-74.666667-167.125333-166.528-167.125334-91.818667 0-166.485333 74.965333-166.485333 167.125334s74.666667 167.125333 166.485333 167.125333c91.861333 0 166.528-74.965333 166.528-167.125333z m61.056 0c0 125.952-102.058667 228.437333-227.584 228.437333-125.44 0-227.541333-102.485333-227.541333-228.437333S297.813333 85.333333 423.253333 85.333333c125.525333 0 227.584 102.485333 227.584 228.437334z m255.914667 114.56H851.626667V374.784a31.957333 31.957333 0 0 0-31.872-32 31.957333 31.957333 0 0 0-31.872 32v53.546667h-55.04a31.957333 31.957333 0 0 0 0 64h55.04v53.504c0 17.664 14.293333 32 31.872 32a31.957333 31.957333 0 0 0 31.872-32V492.373333h55.125333a31.957333 31.957333 0 0 0 0-64zM423.253333 624.64c-145.066667 0-337.962667 16.213333-337.962666 156.586667 0 42.026667 19.456 98.773333 112.213333 130.304a30.634667 30.634667 0 0 0 19.626667-58.026667c-63.488-21.589333-70.826667-51.584-70.826667-72.277333 0-63.232 93.226667-95.317333 276.949333-95.317334 183.765333 0 276.949333 32.341333 276.949334 96.170667 0 63.274667-93.184 95.317333-276.906667 95.317333-14.08 0-27.861333-0.170667-41.429333-0.597333-16.341333 0.128-30.890667 12.842667-31.36 29.738667a30.592 30.592 0 0 0 29.568 31.573333c14.165333 0.341333 28.586667 0.554667 43.178666 0.554667 145.109333 0 338.005333-16.256 338.005334-156.586667 0-157.44-254.421333-157.44-338.005334-157.44z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--square-phone-->
<view a:if="{{name === 'square-phone'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M192 64C121.4 64 64 121.4 64 192v640c0 70.6 57.4 128 128 128h640c70.6 0 128-57.4 128-128V192c0-70.6-57.4-128-128-128H192z m181.4 193.4c19.4-5.2 39.8 4.6 47.4 23.2l40 96c6.8 16.4 2 35.2-11.6 46.4L400 463.4c33.2 70.4 90.2 127.4 160.6 160.6l40.4-49.4c11.2-13.6 30-18.4 46.4-11.6l96 40c18.6 7.8 28.4 28 23.2 47.4l-24 88C737.8 756 722 768 704 768 456.6 768 256 567.4 256 320c0-18 12-33.8 29.4-38.6l88-24z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--message-->
<view a:if="{{name === 'message'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M128 0C57.4 0 0 57.4 0 128v576c0 70.6 57.4 128 128 128h192v160c0 12.2 6.8 23.2 17.6 28.6s23.8 4.2 33.6-3L618.6 832H896c70.6 0 128-57.4 128-128V128c0-70.6-57.4-128-128-128H128z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--id-card-->
<view a:if="{{name === 'id-card'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1152 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M0 192h1152c0-70.6-57.4-128-128-128H128C57.4 64 0 121.4 0 192z m0 64v576c0 70.6 57.4 128 128 128h896c70.6 0 128-57.4 128-128V256H0z m128 554.6c0-59 47.8-106.6 106.6-106.6h234.8c59 0 106.6 47.8 106.6 106.6 0 11.8-9.6 21.4-21.4 21.4H149.4c-11.8 0-21.4-9.6-21.4-21.4zM352 384a128 128 0 1 1 0 256 128 128 0 1 1 0-256z m352 32c0-17.6 14.4-32 32-32h256c17.6 0 32 14.4 32 32s-14.4 32-32 32H736c-17.6 0-32-14.4-32-32z m0 128c0-17.6 14.4-32 32-32h256c17.6 0 32 14.4 32 32s-14.4 32-32 32H736c-17.6 0-32-14.4-32-32z m0 128c0-17.6 14.4-32 32-32h256c17.6 0 32 14.4 32 32s-14.4 32-32 32H736c-17.6 0-32-14.4-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--mobile-retro-->
<view a:if="{{name === 'mobile-retro'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M192 128C192 57.4 249.4 0 320 0h384c70.6 0 128 57.4 128 128v768c0 70.6-57.4 128-128 128H320c-70.6 0-128-57.4-128-128V128z m128 192v128c0 35.4 28.6 64 64 64h256c35.4 0 64-28.6 64-64v-128c0-35.4-28.6-64-64-64H384c-35.4 0-64 28.6-64 64z m32 384a48 48 0 1 0 0-96 48 48 0 1 0 0 96z m48 112a48 48 0 1 0-96 0 48 48 0 1 0 96 0z m112-112a48 48 0 1 0 0-96 48 48 0 1 0 0 96z m48 112a48 48 0 1 0-96 0 48 48 0 1 0 96 0z m112-112a48 48 0 1 0 0-96 48 48 0 1 0 0 96z m48 112a48 48 0 1 0-96 0 48 48 0 1 0 96 0zM448 96c-17.6 0-32 14.4-32 32s14.4 32 32 32h128c17.6 0 32-14.4 32-32s-14.4-32-32-32h-128z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--list-->
<view a:if="{{name === 'list'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M896 256l-288 0c-17.696 0-32-14.336-32-32s14.304-32 32-32l288 0c17.696 0 32 14.336 32 32S913.696 256 896 256z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M896 416l-288 0c-17.696 0-32-14.336-32-32s14.304-32 32-32l288 0c17.696 0 32 14.336 32 32S913.696 416 896 416z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M896 672l-288 0c-17.696 0-32-14.304-32-32s14.304-32 32-32l288 0c17.696 0 32 14.304 32 32S913.696 672 896 672z' fill='{{(isStr ? colors : colors[2]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M896 832l-288 0c-17.696 0-32-14.304-32-32s14.304-32 32-32l288 0c17.696 0 32 14.304 32 32S913.696 832 896 832z' fill='{{(isStr ? colors : colors[3]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M384 480 192 480c-52.928 0-96-43.072-96-96L96 192c0-52.928 43.072-96 96-96l192 0c52.928 0 96 43.072 96 96l0 192C480 436.928 436.928 480 384 480zM192 160C174.368 160 160 174.368 160 192l0 192c0 17.632 14.368 32 32 32l192 0c17.632 0 32-14.368 32-32L416 192c0-17.632-14.368-32-32-32L192 160z' fill='{{(isStr ? colors : colors[4]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M384 928 192 928c-52.928 0-96-43.072-96-96l0-192c0-52.928 43.072-96 96-96l192 0c52.928 0 96 43.072 96 96l0 192C480 884.928 436.928 928 384 928zM192 608c-17.632 0-32 14.336-32 32l0 192c0 17.664 14.368 32 32 32l192 0c17.632 0 32-14.336 32-32l0-192c0-17.664-14.368-32-32-32L192 608z' fill='{{(isStr ? colors : colors[5]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--profile-->
<view a:if="{{name === 'profile'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M768 384c0-160-128-288-288-288s-288 128-288 288c0 108.8 57.6 201.6 147.2 249.6-121.6 48-214.4 153.6-240 288-3.2 16 6.4 35.2 25.6 38.4H128c16 0 28.8-9.6 32-25.6 28.8-150.4 160-259.2 313.6-262.4h6.4c160 0 288-128 288-288zM256 384c0-124.8 99.2-224 224-224s224 99.2 224 224c0 121.6-99.2 220.8-220.8 224H473.6C352 604.8 256 505.6 256 384zM896 704h-256c-19.2 0-32 12.8-32 32v192c0 19.2 12.8 32 32 32h256c19.2 0 32-12.8 32-32v-192c0-16-12.8-32-32-32z m-32 192h-192v-128h192v128z' fill='{{(isStr ? colors : colors[0]) || 'rgb(102,102,102)'}}' /%3E%3Cpath d='M736 864h32c19.2 0 32-12.8 32-32s-12.8-32-32-32h-32c-19.2 0-32 12.8-32 32s16 32 32 32z' fill='{{(isStr ? colors : colors[1]) || 'rgb(102,102,102)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--addressbook-->
<view a:if="{{name === 'addressbook'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M160 608c-17.664 0-32-14.304-32-32l0-160c0-17.664 14.336-32 32-32s32 14.336 32 32l0 160C192 593.696 177.664 608 160 608z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M864 928 224 928c-52.928 0-96-42.752-96-95.328L128 768c0-17.696 14.336-32 32-32s32 14.304 32 32l0 64.672C192 849.984 206.368 864 224 864l640 0c17.664 0 32-14.336 32-32L896 192c0-17.632-14.336-32-32-32L224 160C205.44 160 192 170.144 192 177.024L192 224c0 17.664-14.336 32-32 32S128 241.664 128 224L128 177.024C128 132.352 171.072 96 224 96l640 0c52.928 0 96 43.072 96 96l0 640C960 884.928 916.928 928 864 928z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M192 352 96 352c-17.664 0-32-14.336-32-32s14.336-32 32-32l96 0c17.664 0 32 14.336 32 32S209.664 352 192 352z' fill='{{(isStr ? colors : colors[2]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M192 704 96 704c-17.664 0-32-14.304-32-32s14.336-32 32-32l96 0c17.664 0 32 14.304 32 32S209.664 704 192 704z' fill='{{(isStr ? colors : colors[3]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M319.456 800.992c-14.56 0-27.712-9.984-31.136-24.736-4-17.216 6.688-34.4 23.936-38.4 4.544-1.056 97.792-22.656 167.744-30.56l0-20.032c-67.136-57.6-96-139.52-96-269.92 0-127.84 64.608-192.672 192-192.672 158.688 0 192 104.416 192 192 0 139.008-66.24 223.2-127.2 271.2l0.224 19.84c69.28 8.544 161.504 29.088 165.984 30.08 17.248 3.84 28.096 20.96 24.224 38.208-3.808 17.216-20.704 28.224-38.208 24.224-1.184-0.256-121.856-27.168-185.92-31.648-16.64-1.152-29.568-14.912-29.76-31.552l-0.672-64.672c-0.128-10.464 4.864-20.32 13.408-26.368C642.016 608.8 704 538.848 704 416.672c0-90.912-37.088-128-128-128-92.096 0-128 36.064-128 128.672 0 117.632 24.16 184.032 83.392 229.248C539.328 652.64 544 662.016 544 672l0 64.672c0 17.088-13.408 31.168-30.464 31.968-63.584 3.04-185.568 31.264-186.784 31.52C324.32 800.736 321.888 800.992 319.456 800.992z' fill='{{(isStr ? colors : colors[4]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--activity-->
<view a:if="{{name === 'activity'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M719.328 640c-64.608 0-121.44-28.736-133.12-35.072C575.776 600.768 511.52 576 460 576c-46.688 0-108 21.312-127.68 29.536-9.888 4.128-21.152 3.04-30.08-2.912C293.344 596.672 288 586.688 288 576L288 160c0-13.024 7.904-24.736 19.936-29.632C311.392 128.96 393.248 96 463.328 96c70.656 0 146.464 33.344 149.664 34.752 1.184 0.512 2.336 1.12 3.456 1.792C616.832 132.768 663.424 160 716.672 160c52.992 0 98.208-27.04 98.656-27.328 9.952-5.952 22.24-6.208 32.352-0.544C857.728 137.824 864 148.448 864 160l0 416c0 10.848-5.504 20.928-14.592 26.848C847.104 604.384 791.328 640 719.328 640zM460 512c68.928 0 148.928 33.056 152.32 34.464 1.216 0.512 2.4 1.088 3.552 1.76C616.32 548.48 665.728 576 719.328 576c33.984 0 63.776-11.2 80.672-19.136L800 209.312C777.28 217.12 748.288 224 716.672 224c-64.768 0-119.552-28.864-131.008-35.328C575.808 184.512 515.648 160 463.328 160 424.224 160 377.856 173.6 352 182.4l0 348.48C381.92 521.728 422.592 512 460 512z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M544 928 192 928 192 128c0-17.664 14.336-32 32-32s32 14.336 32 32l0 736 288 0c17.696 0 32 14.304 32 32S561.696 928 544 928z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M224 928 160.992 928c-17.664 0-32-14.304-32-32s14.336-32 32-32L224 864c17.664 0 32 14.304 32 32S241.664 928 224 928z' fill='{{(isStr ? colors : colors[2]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--friendadd-->
<view a:if="{{name === 'friendadd'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M800 387.2c0-160-128-288-288-288s-288 128-288 288c0 105.6 57.6 201.6 147.2 249.6-121.6 48-214.4 153.6-240 288-3.2 16 6.4 35.2 25.6 38.4h6.4c16 0 28.8-9.6 32-25.6 28.8-150.4 160-259.2 313.6-262.4h6.4c156.8 0 284.8-131.2 284.8-288z m-512 0c0-124.8 99.2-224 224-224s224 99.2 224 224c0 121.6-99.2 220.8-220.8 224H505.6c-121.6-6.4-217.6-105.6-217.6-224zM864 800h-64v-64c0-19.2-12.8-32-32-32s-32 12.8-32 32v64h-64c-19.2 0-32 12.8-32 32s12.8 32 32 32h64v64c0 19.2 12.8 32 32 32s32-12.8 32-32v-64h64c19.2 0 32-12.8 32-32s-12.8-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(102,102,102)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--bad-->
<view a:if="{{name === 'bad'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M127.168 767.168c-17.6 0.16-31.68 14.464-31.616 32.064 0.032 17.6 14.368 31.872 31.968 31.936 3.68 0.032 90.912 1.376 169.568 87.52 6.304 6.944 14.976 10.432 23.648 10.432 7.68 0 15.424-2.752 21.568-8.384 13.056-11.904 13.984-32.16 2.048-45.184C245.632 767.36 131.776 767.328 127.168 767.168z' fill='{{(isStr ? colors : colors[0]) || 'rgb(93,100,111)'}}' /%3E%3Cpath d='M867.872 768.672c-5.824-0.48-142.304-9.024-250.112 106.624-12.064 12.896-11.328 33.152 1.6 45.216 6.144 5.76 14.016 8.608 21.824 8.608 8.576 0 17.088-3.392 23.392-10.176 86.816-93.12 194.016-86.72 198.656-86.432 17.376 1.408 32.928-12.032 34.176-29.632C898.688 785.28 885.504 769.984 867.872 768.672z' fill='{{(isStr ? colors : colors[1]) || 'rgb(93,100,111)'}}' /%3E%3Cpath d='M926.176 546.848c-24.832-60.64-76.864-115.744-121.44-158.016 39.84-28.928 69.376-66.336 88.064-111.584 5.376-12.928 1.664-27.872-9.088-36.8-102.048-84.96-275.008-144.256-420.512-144.256-120.864 0-210.528 39.2-259.328 113.312l0.096 0.064C193.92 221.92 181.728 244.96 167.392 287.776c-0.544 1.568-0.928 3.2-1.216 4.864C159.168 319.36 157.76 347.072 161.344 375.136c-0.096 1.056-0.608 1.952-0.576 3.04 0.128 12.96 6.656 320.16 300.448 573.824 6.048 5.216 13.472 7.776 20.896 7.776 8.992 0 17.92-3.744 24.224-11.104 11.552-13.376 10.08-33.568-3.328-45.12-128.288-110.784-197.056-233.632-234.336-332.896 104.768 106.016 262.272 180.896 377.888 196.928 1.472 0.224 2.944 0.32 4.416 0.32 10.848 0 21.056-5.504 26.976-14.784 31.904-50.016 45.376-101.6 39.616-143.328 7.136 0.192 14.208 0.288 21.312 0.288 99.36 0 164.416-19.68 167.136-20.512 8.576-2.656 15.648-8.8 19.488-16.896C929.344 564.544 929.6 555.168 926.176 546.848zM738.88 546.048c-22.4 0-45.12-1.088-67.456-3.296-13.44-1.312-26.016 5.824-31.904 17.856-5.856 12.032-3.584 26.4 5.632 36.096 14.496 15.168 13.44 57.376-10.72 103.936-122.24-24.288-291.456-116.832-370.56-235.808-36.576-54.976-48.576-107.584-35.648-156.384 0.16-0.64 0.32-1.28 0.448-1.888 4.224-11.232 22.752-51.2 27.904-60.704 0.288-0.384 0.576-0.8 0.832-1.216 45.984-69.856 137.312-84.512 205.856-84.512 120.832 0 267.552 47.2 359.968 114.24-19.136 34.016-47.904 61.184-85.792 80.96-9.248 4.832-15.552 13.888-16.896 24.224-1.376 10.336 2.4 20.704 10.08 27.744 36 32.96 86.976 79.648 118.368 129.504C823.008 541.376 784.992 546.048 738.88 546.048z' fill='{{(isStr ? colors : colors[2]) || 'rgb(93,100,111)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--peoplelist-->
<view a:if="{{name === 'peoplelist'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M771.2 384c0-160-128-288-288-288s-288 128-288 288c0 108.8 57.6 201.6 147.2 249.6-121.6 48-214.4 153.6-240 288-3.2 16 6.4 35.2 25.6 38.4h6.4c16 0 28.8-9.6 32-25.6 28.8-150.4 160-259.2 313.6-262.4h6.4c153.6 0 284.8-131.2 284.8-288z m-512 0c0-124.8 99.2-224 224-224s224 99.2 224 224c0 121.6-99.2 220.8-220.8 224H476.8c-121.6-6.4-217.6-105.6-217.6-224zM704 672c0 19.2 12.8 32 32 32h160c19.2 0 32-12.8 32-32s-12.8-32-32-32h-160c-16 0-32 12.8-32 32zM896 764.8h-249.6-3.2-3.2c-19.2 0-32 12.8-32 32s12.8 32 32 32h256c19.2 0 32-12.8 32-32s-12.8-32-32-32zM896 892.8h-246.4-3.2-3.2c-19.2 0-32 12.8-32 32s12.8 32 32 32H896c19.2 0 32-12.8 32-32s-16-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(102,102,102)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--exit-->
<view a:if="{{name === 'exit'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M918.4 489.6l-160-160c-12.8-12.8-32-12.8-44.8 0-12.8 12.8-12.8 32 0 44.8l105.6 105.6L512 480c-19.2 0-32 12.8-32 32s12.8 32 32 32l307.2 0-105.6 105.6c-12.8 12.8-12.8 32 0 44.8 6.4 6.4 12.8 9.6 22.4 9.6 9.6 0 16-3.2 22.4-9.6l160-163.2c0 0 0-3.2 3.2-3.2C931.2 518.4 931.2 499.2 918.4 489.6z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M832 736c-19.2 0-32 12.8-32 32l0 64c0 19.2-12.8 32-32 32L224 864c-19.2 0-32-12.8-32-32L192 192c0-19.2 12.8-32 32-32l544 0c19.2 0 32 12.8 32 32l0 64c0 19.2 12.8 32 32 32s32-12.8 32-32L864 192c0-54.4-41.6-96-96-96L224 96C169.6 96 128 137.6 128 192l0 640c0 54.4 41.6 96 96 96l544 0c54.4 0 96-41.6 96-96l0-64C864 748.8 851.2 736 832 736z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Location-->
<view a:if="{{name === 'Location'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 85.333333h1.408c199.978667 0.768 362.026667 163.925333 361.258667 363.605334v3.968c-2.688 142.72-88.490667 261.12-160 335.36-20.309333 21.205333-41.984 41.557333-64.298667 60.586666a31.829333 31.829333 0 0 1-41.344-48.384c20.693333-17.621333 40.789333-36.565333 59.733333-56.32 63.573333-65.962667 139.904-170.325333 142.208-292.394666 0.64-167.68-132.906667-302.165333-297.813333-302.848H512c-164.309333 0-298.325333 133.205333-298.965333 297.386666a389.12 389.12 0 0 0 79.36 222.976c57.301333 76.330667 144.256 157.141333 226.901333 210.944a31.701333 31.701333 0 0 1-17.408 58.453334 31.573333 31.573333 0 0 1-17.365333-5.162667C396.032 875.904 302.933333 789.333333 241.493333 707.584a452.565333 452.565333 0 0 1-92.16-259.029333C150.101333 246.869333 312.661333 85.333333 512 85.333333z m-74.453333 367.530667c0 40.917333 33.365333 74.24 74.325333 74.24s74.325333-33.322667 74.325333-74.24c0-40.96-33.322667-74.24-74.325333-74.24a31.829333 31.829333 0 1 1 0-63.573333 138.069333 138.069333 0 0 1 138.026667 137.813333 138.069333 138.069333 0 0 1-138.026667 137.813333 138.069333 138.069333 0 0 1-138.026667-137.813333 31.829333 31.829333 0 0 1 63.701334 0z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Arrow-Right2-->
<view a:if="{{name === 'Arrow-Right2'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M330.410667 181.034667a34.730667 34.730667 0 0 0 0.085333 49.664l207.616 203.306666 3.669333 3.114667a36.138667 36.138667 0 0 0 46.805334-3.285333 34.688 34.688 0 0 0-0.085334-49.621334L380.928 180.906667l-3.669333-3.157334a36.138667 36.138667 0 0 0-46.848 3.285334z m-3.114667 615.893333a34.730667 34.730667 0 0 0 3.114667 46.08 36.138667 36.138667 0 0 0 50.474666 0.042667l312.576-306.176 3.242667-3.584a34.56 34.56 0 0 0-3.114667-46.08 36.096 36.096 0 0 0-50.517333-0.085334l-312.576 306.218667-3.2 3.584z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Arrow-RightSquare-->
<view a:if="{{name === 'Arrow-RightSquare'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M118.101333 384.512a32.682667 32.682667 0 0 1-32.64-32.64C85.333333 187.392 187.392 85.333333 351.701333 85.333333h320.170667C836.565333 85.333333 938.666667 187.392 938.666667 351.744v320C938.666667 836.565333 836.608 938.666667 671.872 938.666667h-320C188.16 938.666667 85.333333 836.565333 85.333333 671.872v-109.610667c0-17.792 14.464-32.256 32.256-32.256h0.384v0.725334c18.005333 0 32.597333 14.592 32.64 32.597333V671.872c0 130.261333 71.253333 201.514667 201.130667 201.514667h320c129.962667 0 201.642667-71.68 201.642667-201.514667v-320c0-129.450667-71.68-201.130667-201.514667-201.130667h-320c-129.834667 0-201.130667 71.68-201.130667 201.130667a32.682667 32.682667 0 0 1-32.64 32.64z m219.562667 159.488h271.189333l-105.685333 105.258667a31.957333 31.957333 0 1 0 45.141333 45.312l160.597334-159.914667v-0.042667a31.658667 31.658667 0 0 0 9.386666-22.570666V512c0-0.298667 0-0.554667-0.128-0.810667-0.042667-0.213333-0.128-0.426667-0.128-0.682666a32.170667 32.170667 0 0 0-2.176-10.709334 33.365333 33.365333 0 0 0-6.485333-9.813333 31.658667 31.658667 0 0 0-23.04-9.941333H337.664a32 32 0 0 0 0 63.957333z m265.472-160.042667L548.352 329.386667a31.914667 31.914667 0 0 0-45.312 0.085333 31.957333 31.957333 0 0 0 0.128 45.226667l54.784 54.528a32.042667 32.042667 0 0 0 45.184-45.312z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Arrow-Right-->
<view a:if="{{name === 'Arrow-Right'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M554.752 202.410667a35.84 35.84 0 0 0 0.128 50.133333l155.306667 156.970667 3.584 3.157333a34.432 34.432 0 0 0 45.738666-3.285333 35.626667 35.626667 0 0 0-0.085333-50.090667l-155.306667-157.013333-3.541333-3.114667a34.56 34.56 0 0 0-45.824 3.242667z m-396.202667 274.432A35.285333 35.285333 0 0 0 128 512c0 19.541333 15.658667 35.413333 34.901333 35.413333h613.674667l-221.696 224.042667-3.157333 3.626667a35.84 35.84 0 0 0 3.072 46.506666 34.56 34.56 0 0 0 49.322666 0.085334l281.6-284.586667 3.114667-3.584a35.669333 35.669333 0 0 0 4.522667-34.986667 34.858667 34.858667 0 0 0-32.256-21.930666H162.901333l-4.352 0.256z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Category-->
<view a:if="{{name === 'Category'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M916.949333 232.234667v122.112a126.592 126.592 0 0 1-34.858666 89.002666 121.429333 121.429333 0 0 1-86.272 37.717334h-49.664a33.536 33.536 0 0 1-29.568-33.578667c0-17.28 12.8-31.786667 29.568-33.621333h48.170666c15.232-0.085333 29.781333-6.4 40.448-17.493334s16.554667-26.069333 16.341334-41.642666v-122.453334c-0.426667-31.872-25.6-57.6-56.789334-58.026666h-120.021333c-31.36 0-56.746667 25.984-56.746667 58.026666v183.125334a33.792 33.792 0 0 1-33.877333 33.024 32.426667 32.426667 0 0 1-23.04-10.453334 33.792 33.792 0 0 1-8.96-24.106666V232.234667c-0.085333-33.28 12.8-65.194667 35.797333-88.746667a121.386667 121.386667 0 0 1 86.826667-36.821333h120.021333c67.2 1.621333 121.258667 56.96 122.624 125.568zM349.696 106.666667H229.674667C161.92 107.093333 107.093333 163.072 106.666667 232.234667v122.496c0.426667 69.12 55.296 125.013333 123.008 125.184h120.021333a121.386667 121.386667 0 0 0 86.954667-36.522667c23.04-23.466667 36.053333-55.381333 36.053333-88.661333v-122.453334C472.490667 162.986667 417.536 106.794667 349.696 106.666667z m56.746667 248.064c0.256 15.573333-5.632 30.549333-16.298667 41.642666s-25.216 17.408-40.448 17.493334H229.674667a56.192 56.192 0 0 1-41.301334-16.938667 58.624 58.624 0 0 1-16.64-42.197333v-122.453334c0-15.616 6.144-30.549333 17.066667-41.472 10.88-10.88 25.6-16.853333 40.874667-16.554666h120.021333c31.274667 0.213333 56.576 26.026667 56.746667 58.026666v122.453334z m267.861333 189.354666h120.021333c67.712 0.170667 122.581333 56.064 123.008 125.184v122.453334c-0.426667 69.205333-55.253333 125.184-123.008 125.610666h-120.021333c-67.669333-0.426667-122.410667-56.448-122.624-125.568v-122.496c0-69.12 54.912-125.184 122.624-125.184z m160.298667 288.768c10.666667-10.88 16.64-25.685333 16.512-41.088v-122.496c0-15.36-5.973333-30.122667-16.64-41.002666a56.234667 56.234667 0 0 0-40.149334-16.981334h-120.021333c-31.36 0-56.746667 25.941333-56.746667 57.984v122.453334c-0.128 15.445333 5.802667 30.293333 16.469334 41.130666 10.666667 10.922667 25.173333 16.981333 40.277333 16.896h120.021333c15.104 0.085333 29.610667-5.973333 40.277334-16.896zM106.666667 791.381333v-122.112c0-33.28 12.970667-65.194667 36.053333-88.661333a121.344 121.344 0 0 1 86.954667-36.522667h49.28a32.853333 32.853333 0 0 1 31.573333 15.658667 34.389333 34.389333 0 0 1 0 35.84 32.853333 32.853333 0 0 1-31.573333 15.701333H229.674667c-31.445333 0-56.96 25.898667-57.173334 57.984v120.96a58.88 58.88 0 0 0 57.173334 57.984h120.746666c15.146667 0.085333 29.696-5.973333 40.405334-16.853333 10.752-10.88 16.768-25.685333 16.768-41.130667v-181.632a34.090667 34.090667 0 0 1 14.848-34.688 32.554667 32.554667 0 0 1 37.034666 1.109334 34.133333 34.133333 0 0 1 12.8 35.498666v180.864c0 69.333333-55.04 125.568-122.965333 125.568H229.674667c-67.328-1.408-121.6-56.832-123.008-125.568z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Filter-->
<view a:if="{{name === 'Filter'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M85.333333 265.130667C85.333333 352.426667 157.312 423.552 245.888 423.552c88.533333 0 160.512-71.082667 160.512-158.421333 0-87.424-71.978667-158.464-160.512-158.464-12.202667 0-24.32 1.322667-36.053333 4.053333a37.12 37.12 0 0 0-28.16 44.586667 37.546667 37.546667 0 0 0 45.226666 27.733333c6.101333-1.365333 12.544-2.048 18.986667-2.048 47.018667 0 85.205333 37.717333 85.205333 84.138667 0 46.378667-38.186667 84.096-85.205333 84.096-47.061333 0-85.248-37.717333-85.248-84.096 0-4.778667 0.426667-9.472 1.152-13.994667a37.248 37.248 0 0 0-30.933333-42.794667 37.546667 37.546667 0 0 0-43.306667 30.549334c-1.450667 8.533333-2.218667 17.322667-2.218667 26.24z m507.008 37.12h308.650667c20.821333 0 37.674667-16.64 37.674667-37.12 0-20.522667-16.853333-37.205333-37.674667-37.205334h-308.650667a37.418667 37.418667 0 0 0-37.632 37.162667c0 20.522667 16.853333 37.162667 37.632 37.162667z m-160.64 493.781333H122.922667a37.418667 37.418667 0 0 1-37.632-37.12c0-20.565333 16.853333-37.205333 37.632-37.205333h308.736c20.778667 0 37.632 16.64 37.632 37.162666 0 20.48-16.853333 37.162667-37.674667 37.162667z m185.898667-37.12c0 87.338667 72.021333 158.421333 160.554667 158.421333 88.490667 0 160.512-71.082667 160.512-158.464a155.306667 155.306667 0 0 0-2.218667-26.197333 37.632 37.632 0 0 0-43.306667-30.549333 37.248 37.248 0 0 0-30.933333 42.794666 85.333333 85.333333 0 0 1 1.152 13.952c0 46.378667-38.229333 84.138667-85.205333 84.138667-47.061333 0-85.248-37.76-85.248-84.138667 0-52.736 52.224-93.824 104.405333-82.048a37.717333 37.717333 0 0 0 45.098667-27.989333 37.12 37.12 0 0 0-28.416-44.416 164.565333 164.565333 0 0 0-35.84-3.968c-88.533333 0-160.554667 71.04-160.554667 158.421333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Setting-->
<view a:if="{{name === 'Setting'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M381.525333 293.290667a115.584 115.584 0 0 1-113.066666-0.725334l-1.536-1.493333a32.213333 32.213333 0 0 1-10.752-44.970667 33.92 33.92 0 0 1 46.08-10.496c6.954667 4.010667 14.933333 6.186667 22.997333 6.4 12.416 0.469333 24.533333-3.84 33.706667-12.074666 9.130667-8.234667 14.506667-19.626667 15.018666-31.786667C373.76 137.173333 423.296 87.168 485.674667 85.333333h52.565333c63.573333 0 115.114667 50.346667 115.114667 112.426667a42.368 42.368 0 0 0 6.144 22.485333c6.144 10.538667 16.298667 18.261333 28.245333 21.418667 11.946667 3.157333 24.746667 1.536 35.456-4.565333 54.058667-28.714667 121.642667-10.88 153.514667 40.490666a32.938667 32.938667 0 0 1-12.245334 44.586667 34.218667 34.218667 0 0 1-45.653333-11.605333 47.445333 47.445333 0 0 0-63.744-16.512 117.546667 117.546667 0 0 1-112.810667-1.194667 112.042667 112.042667 0 0 1-57.6-94.72 44.8 44.8 0 0 0-13.013333-33.066667 46.976 46.976 0 0 0-33.408-13.781333h-52.565333a47.36 47.36 0 0 0-33.536 13.781333 45.141333 45.141333 0 0 0-13.312 33.066667 110.165333 110.165333 0 0 1-57.301334 95.146667z m57.301334 534.058666a46.293333 46.293333 0 0 0 46.848 45.354667l-0.426667 0.768c11.946667 0 22.997333 6.186667 28.928 16.298667 5.973333 10.069333 5.973333 22.485333 0 32.597333a33.536 33.536 0 0 1-28.885333 16.298667c-63.573333 0-115.157333-50.346667-115.157334-112.426667a42.325333 42.325333 0 0 0-6.144-22.485333 47.445333 47.445333 0 0 0-63.701333-16.853334c-54.016 28.714667-121.6 10.88-153.514667-40.490666L120.746667 701.866667c-29.44-52.778667-11.178667-118.784 41.429333-149.930667 6.997333-3.968 12.8-9.642667 16.896-16.469333 7.68-10.496 10.453333-23.637333 7.594667-36.266667a45.44 45.44 0 0 0-22.570667-29.696c-52.608-31.146667-70.826667-97.152-41.429333-149.888a34.048 34.048 0 0 1 46.421333-8.96c14.549333 9.642667 18.773333 28.757333 9.6 43.434667a44.8 44.8 0 0 0 17.28 61.824c16.682667 9.984 30.464 24.064 39.893333 40.832 29.44 52.778667 11.178667 118.784-41.429333 149.930666a44.8 44.8 0 0 0-17.28 61.866667l26.453333 44.544c6.058667 10.666667 16.256 18.389333 28.288 21.504 12.032 3.114667 24.832 1.28 35.413334-4.992a112.938667 112.938667 0 0 1 56.448-14.634667c63.573333 0 115.114667 50.346667 115.114666 112.426667z m422.570666-275.797333a45.525333 45.525333 0 0 1-21.76-27.690667 44.416 44.416 0 0 1 4.864-34.56c4.096-6.784 9.898667-12.458667 16.896-16.469333a32.170667 32.170667 0 0 0 11.52-44.586667 34.602667 34.602667 0 0 0-45.312-12.373333c-52.608 31.146667-70.826667 97.152-41.429333 149.888 9.344 18.432 23.850667 33.92 41.813333 44.629333 10.666667 5.973333 18.517333 15.829333 21.674667 27.477334 3.157333 11.605333 1.450667 23.978667-4.778667 34.346666L818.773333 716.8a46.634667 46.634667 0 0 1-63.701333 16.469333 117.546667 117.546667 0 0 0-112.981333 1.322667 112.042667 112.042667 0 0 0-57.429334 95.018667c1.834667 16.768 16.341333 29.44 33.578667 29.44a33.493333 33.493333 0 0 0 33.578667-29.44 44.8 44.8 0 0 1 23.04-39.808c14.506667-8.192 32.469333-8.021333 46.848 0.426666 54.016 28.757333 121.6 10.922667 153.472-40.448l26.112-44.586666c32.213333-53.205333 14.378667-121.856-39.893334-153.642667z m-349.653333-180.266667c-58.368 0-111.018667 34.346667-133.333333 87.04a138.496 138.496 0 0 0 31.445333 153.6 146.858667 146.858667 0 0 0 157.397333 30.336c53.888-21.930667 88.96-73.386667 88.789334-130.432 0-37.376-15.189333-73.173333-42.24-99.541333a145.706667 145.706667 0 0 0-102.058667-41.002667z m0 215.893334c-42.368 0-76.757333-33.578667-76.757333-75.008 0-41.386667 34.389333-74.922667 76.8-74.922667 42.368 0 76.714667 33.536 76.714666 74.922667s-34.346667 74.965333-76.757333 74.965333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Arrow-Down2-->
<view a:if="{{name === 'Arrow-Down2'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M180.992 330.410667a34.730667 34.730667 0 0 1 49.664 0.085333l306.218667 312.576a36.096 36.096 0 0 1-0.128 50.517333 34.602667 34.602667 0 0 1-49.621334-0.085333L180.949333 380.842667a36.138667 36.138667 0 0 1 0-50.474667z m612.266667 0.085333a34.730667 34.730667 0 0 1 49.706666-0.085333 36.138667 36.138667 0 0 1 0.128 50.517333l-203.306666 207.573333a34.688 34.688 0 0 1-49.621334 0.085334 36.138667 36.138667 0 0 1-0.170666-50.474667l203.306666-207.616z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Arrow-Down3-->
<view a:if="{{name === 'Arrow-Down3'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M716.074667 501.845333h-173.482667V159.317333A30.976 30.976 0 0 0 512 128a30.976 30.976 0 0 0-30.592 31.317333v342.528H307.925333a30.464 30.464 0 0 0-26.794666 16.213334 31.914667 31.914667 0 0 0 0.896 31.744l204.074666 331.52c5.632 9.173333 15.36 14.677333 25.898667 14.677333s20.266667-5.546667 25.898667-14.677333l126.976-206.293334a31.744 31.744 0 0 0-9.557334-43.178666 30.250667 30.250667 0 0 0-42.24 9.856L512 805.845333 363.349333 564.48h352.725334a30.976 30.976 0 0 0 30.592-31.317333 30.976 30.976 0 0 0-30.592-31.317334z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Arrow-Down-->
<view a:if="{{name === 'Arrow-Down'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M455.253333 162.901333c0-19.242667 15.872-34.901333 35.413334-34.901333 19.584 0 35.413333 15.658667 35.413333 34.901333v698.197334c0 14.165333-8.618667 26.88-21.888 32.256a35.754667 35.754667 0 0 1-38.613333-7.68l-284.586667-281.6a34.56 34.56 0 0 1 0.085333-49.28 35.84 35.84 0 0 1 50.133334 0.085333l224 221.696V162.901333z m294.869334 391.978667a35.84 35.84 0 0 1 50.133333-0.128 34.602667 34.602667 0 0 1 0.085333 49.408L643.413333 759.466667a35.626667 35.626667 0 0 1-50.133333 0.085333 34.474667 34.474667 0 0 1-0.085333-49.365333l157.013333-155.306667z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Arrow-DownCircle-->
<view a:if="{{name === 'Arrow-DownCircle'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M447.232 90.197333A430.933333 430.933333 0 0 1 512 85.333333c235.264 0 426.666667 191.402667 426.666667 426.666667s-191.402667 426.666667-426.666667 426.666667C276.736 938.666667 85.333333 747.264 85.333333 512a424.064 424.064 0 0 1 166.442667-338.218667 31.061333 31.061333 0 0 1 37.888 49.194667A362.069333 362.069333 0 0 0 147.370667 512c0 201.045333 163.541333 364.629333 364.629333 364.629333 201.045333 0 364.629333-163.584 364.629333-364.629333S713.045333 147.370667 512 147.370667a367.786667 367.786667 0 0 0-55.466667 4.181333 31.061333 31.061333 0 0 1-9.386666-61.354667zM512 631.210667a31.914667 31.914667 0 0 1-22.656-9.386667l-148.096-148.778667a32 32 0 1 1 45.312-45.184L512 553.856l125.44-125.994667a32 32 0 1 1 45.354667 45.226667l-148.138667 148.693333a31.744 31.744 0 0 1-22.656 9.429334z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Arrow-Left2-->
<view a:if="{{name === 'Arrow-Left2'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M693.589333 181.034667a34.730667 34.730667 0 0 1-0.085333 49.664l-207.616 203.306666-3.669333 3.114667a36.138667 36.138667 0 0 1-46.805334-3.285333 34.688 34.688 0 0 1 0.085334-49.621334l207.573333-203.306666 3.669333-3.157334a36.138667 36.138667 0 0 1 46.848 3.285334z m3.114667 615.893333a34.730667 34.730667 0 0 1-3.114667 46.08 36.138667 36.138667 0 0 1-50.517333 0.042667l-312.533333-306.176-3.242667-3.584a34.56 34.56 0 0 1 3.114667-46.08 36.096 36.096 0 0 1 50.517333-0.085334l312.576 306.218667 3.2 3.584z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Home1-->
<view a:if="{{name === 'Home1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M859.818667 323.882667a130.218667 130.218667 0 0 1 48.64 100.181333l-0.426667 340.224a167.338667 167.338667 0 0 1-167.296 167.338667h-74.24c-44.714667 0-81.024-36.181333-81.237333-80.896v-153.6a21.504 21.504 0 0 0-21.504-21.504h-111.317334a21.504 21.504 0 0 0-21.504 21.888v58.624a33.408 33.408 0 0 1-66.773333 0v-58.624a88.661333 88.661333 0 0 1 88.661333-88.661334h111.317334a88.661333 88.661333 0 0 1 88.32 88.661334v153.216c0 7.808 6.272 14.08 14.08 14.08h76.032a100.181333 100.181333 0 0 0 100.181333-100.138667V424.832a62.72 62.72 0 0 0-23.722667-48.213333l-256.384-204.458667a87.552 87.552 0 0 0-111.317333 0l-82.346667 61.568a34.048 34.048 0 0 1-39.68-55.253333L410.496 119.466667a153.984 153.984 0 0 1 192.896 0l256.426667 204.416z m-585.813334 540.586666h170.666667v0.725334a33.408 33.408 0 1 1 0 66.816h-170.666667A167.68 167.68 0 0 1 106.666667 764.629333V424.832a129.109333 129.109333 0 0 1 50.090666-100.181333l30.805334-23.04A33.408 33.408 0 0 1 226.133333 354.730667l-30.037333 22.272a62.72 62.72 0 0 0-22.613333 47.872v339.456a100.906667 100.906667 0 0 0 100.522666 100.181333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Arrow-Left-->
<view a:if="{{name === 'Arrow-Left'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M674.005333 461.226667l157.866667-13.994667c35.413333 0 64.128 29.013333 64.128 64.768a64.426667 64.426667 0 0 1-64.128 64.768l-157.866667-13.952c-27.733333 0-50.304-22.741333-50.304-50.773333 0-28.16 22.528-50.816 50.346667-50.816z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' opacity='.4' /%3E%3Cpath d='M144 463.786667c2.474667-2.474667 11.690667-13.013333 20.352-21.76 50.517333-54.741333 182.357333-144.298667 251.306667-171.690667 10.496-4.352 36.992-13.653333 51.2-14.336a85.333333 85.333333 0 0 1 38.826666 9.386667c15.36 8.661333 27.690667 22.4 34.474667 38.570666 4.309333 11.178667 11.093333 44.8 11.093333 45.397334 6.784 36.693333 10.453333 96.384 10.453334 162.346666 0 62.805333-3.669333 120.064-9.216 157.354667-0.597333 0.682667-7.424 42.368-14.805334 56.661333a77.397333 77.397333 0 0 1-68.394666 42.282667h-2.432c-18.474667-0.597333-57.301333-16.810667-57.301334-17.408-65.28-27.392-194.048-112.597333-245.802666-169.216 0 0-14.634667-14.592-20.949334-23.637333A75.434667 75.434667 0 0 1 128 512.341333c0-18.048 5.546667-34.816 16-48.554666z' fill='{{(isStr ? colors : colors[1]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Home-->
<view a:if="{{name === 'Home'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M390.144 886.698667v-130.858667c0-33.152 26.965333-60.074667 60.330667-60.288h122.666666c33.536 0 60.714667 27.008 60.714667 60.288v130.474667c0 28.757333 23.381333 52.138667 52.352 52.352h83.626667a147.626667 147.626667 0 0 0 104.277333-42.666667 145.493333 145.493333 0 0 0 43.221333-103.338667V420.949333c0-31.36-13.994667-61.056-38.186666-81.152l-284.245334-225.706666a132.906667 132.906667 0 0 0-168.874666 3.029333L147.925333 339.754667A105.557333 105.557333 0 0 0 106.666667 420.992v371.328C106.666667 873.130667 172.672 938.666667 254.122667 938.666667h81.749333c13.952 0.085333 27.349333-5.333333 37.248-15.104 9.898667-9.728 15.488-23.04 15.488-36.864h1.536z' fill='{{(isStr ? colors : colors[0]) || 'rgb(32,14,50)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--008-man-->
<view a:if="{{name === '008-man'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M840.745552 905.424017v84.200492c0 9.426702-7.635202 17.061903-17.061904 17.061903h-622.759466c-9.426702 0-17.061903-7.635202-17.061903-17.061903v-84.200492c0-87.954111 71.958577-157.715968 159.912687-158.569063a88.38919 88.38919 0 0 0 71.574684-37.642824l-0.127964-0.127964c17.680397-25.151378 15.927287-47.811718 15.952879-65.027179l0.319911-0.789113c-76.458654-31.692485-130.246304-107.063443-130.288958-194.953571v-6.952726c-30.611187 0-53.29712-24.279088-53.275793-52.358715l0.149292-207.856636c0.021327-31.799122 25.806129-57.583923 57.60525-57.562596 25.86158-64.504658 88.372128-106.572913 157.588004-106.530258l229.418616 0.170619c11.922005 0.021327 20.175701 11.96466 15.931552 23.118879l-14.609255 38.517247c-3.519018 9.320065 1.642208 19.663843 11.196874 22.436402l11.815368 3.433708c35.414113 10.281929 62.084 42.550254 62.084 82.195719 0 16.016862-0.063982 128.540113-0.106637 198.963119 0 30.647444-24.867724 55.472513-55.515167 55.472512v6.952726c-0.063982 88.082075-54.043578 163.538342-130.715506 195.12419v0.021328l0.255929 0.597166c0 16.437011-1.925862 39.592146 15.931552 65.048506 15.558323 22.2082 41.944556 37.431683 71.873267 37.749461 87.954111 0.853095 159.912688 70.614952 159.912688 158.569063z' fill='{{(isStr ? colors : colors[0]) || 'rgb(255,223,186)'}}' /%3E%3Cpath d='M840.745552 905.424017v84.200492c0 9.426702-7.635202 17.061903-17.061904 17.061903h-622.759466c-9.426702 0-17.061903-7.635202-17.061903-17.061903v-84.200492c0-87.954111 71.958577-157.715968 159.912687-158.569063 29.084147-0.309247 55.440522-14.626317 71.596012-37.685479 57.686295 64.103703 68.44809 76.059832 84.243147 93.627194 6.782107 7.528565 18.597474 7.528565 25.379581 0 16.161888-17.974715 27.235063-30.276347 84.11305-93.482167 15.458084 22.063174 41.897636 37.224807 71.726108 37.540452 87.954111 0.853095 159.912688 70.614952 159.912688 158.569063z' fill='{{(isStr ? colors : colors[1]) || 'rgb(214,244,252)'}}' /%3E%3Cpath d='M635.746785 789.680331c84.413766 3.838928 151.680319 73.515475 151.680319 158.867646v58.138435h36.256544a17.061903 17.061903 0 0 0 17.061904-17.061903v-84.068262c0-87.983969-71.986302-157.799144-159.968139-158.705558a88.3508 88.3508 0 0 1-33.940391-7.119079c-0.266592 0.563043-10.958007 49.338759-11.090237 49.948721z' fill='{{(isStr ? colors : colors[2]) || 'rgb(189,239,252)'}}' /%3E%3Cpath d='M646.837022 739.73161c-0.264459 0.563043-21.314583 96.322974-21.18022 95.713011-2.776825 12.598083-18.721173 16.65455-27.181745 6.914336-55.999299-64.485463-48.359832-56.076078-54.030782-61.190383l64.662481-71.856205c9.384047 13.395727 22.523845 23.893063 37.730266 30.419241zM415.370978 709.169475l63.257006 70.295042-52.471751 62.179973c-8.385925 9.938559-24.526486 5.954604-27.324638-6.74585-0.134362-0.60783-20.794195-94.60612-21.060787-95.169163a88.608862 88.608862 0 0 0 37.60017-30.560002z' fill='{{(isStr ? colors : colors[3]) || 'rgb(232,248,252)'}}' /%3E%3Cpath d='M779.109426 186.925946c0 16.016862-0.063982 128.540113-0.106637 198.963119 0 30.647444-24.867724 55.472513-55.515167 55.472512 0.021327-23.204188 0.063982-79.572451 0.085309-131.440636 0.042655-56.538882-45.747228-102.392747-102.286109-102.435402l-217.539266-0.149291c-56.538882-0.042655-102.414074 45.768555-102.456729 102.307437l-0.085309 131.717892c-30.611187 0-53.29712-24.279088-53.275793-52.358715l0.149292-207.856636c0.021327-31.799122 25.806129-57.583923 57.60525-57.562596 25.86158-64.506791 88.372128-106.572913 157.588004-106.530258l229.416483 0.172752c11.934801 0.008531 20.171435 11.953996 15.940083 23.112481l-14.611387 38.519379c-3.531814 9.309401 1.627279 19.65318 11.188343 22.43427l11.8175 3.435841c35.416246 10.281929 62.086133 42.552387 62.086133 82.197851z' fill='{{(isStr ? colors : colors[4]) || 'rgb(66,67,77)'}}' /%3E%3Cpath d='M621.286822 207.485539c56.538882 0.042655 102.328764 45.89652 102.286109 102.435402-0.019195 48.321443-0.08531 138.393362-0.085309 138.393362-0.063982 88.082075-54.043578 163.538342-130.715506 195.12419v0.021328c-138.781521 57.229889-291.494085-45.150061-291.566598-195.145518 0-11.128626 0.076779-124.419664 0.085309-138.670618 0.042655-56.538882 45.917847-102.350092 102.456729-102.307437z' fill='{{(isStr ? colors : colors[5]) || 'rgb(255,235,210)'}}' /%3E%3Cpath d='M595.757949 679.140526c-52.544264 21.994926-110.288142 23.701116-166.908068 0 2.891993-11.998783 2.409994-19.77048 2.409994-35.019557l0.319911-0.789113c51.360594 21.318848 109.987426 21.361503 161.448259 0l0.31991 0.789113c0 15.253341-0.481999 23.025038 2.409994 35.019557z' fill='{{(isStr ? colors : colors[6]) || 'rgb(255,214,166)'}}' /%3E%3Cpath d='M464.496462 584.525874c34.714575 21.71767 7.340884 74.978534-30.66024 59.725192-78.13072-31.359778-132.673359-107.498521-132.673359-196.06686v-138.41469a101.908615 101.908615 0 0 1 17.964051-57.942223c14.197636-20.644903 46.173776-10.407761 46.020219 14.647644-0.004265 0.522521-0.002133 138.53199-0.002133 139.054511 0 77.868393 40.931506 142.447697 99.351462 178.996426z' fill='{{(isStr ? colors : colors[7]) || 'rgb(255,243,228)'}}' /%3E%3Cpath d='M708.622439 40.34287c-15.121112 39.869402-14.905705 38.939529-15.270404 40.820604l-166.097627-0.127965c-64.468401-0.040522-123.125092 36.452756-151.763496 93.590937-3.591531 7.165999-10.089983 12.534101-17.927795 14.206168-25.989544 5.547251-45.482768 28.649068-45.499831 56.295749l-0.021327 18.874731c-6.867416 13.734832-10.727672 29.239837-10.748999 45.640591l-0.051186 80.005397c-0.02346 34.548221-53.337642 36.838782-53.309916-0.64622l0.149292-207.856636c0.021327-31.799122 25.806129-57.583923 57.60525-57.562596 25.86158-64.504658 88.372128-106.572913 157.588004-106.530258l229.418615 0.170619c11.919872 0.021327 20.173568 11.96466 15.92942 23.118879z' fill='{{(isStr ? colors : colors[8]) || 'rgb(77,78,89)'}}' /%3E%3Cpath d='M416.714602 798.744467c3.177779 14.438636 6.568833 29.815676 9.448029 42.889359-8.385925 9.940691-24.526486 5.969533-27.322505-6.733053-8.200377-37.241869-20.888035-94.789536-21.067185-95.169163a88.628056 88.628056 0 0 0 37.600169-30.562135l18.213582 20.26101c-15.656429 19.493224-22.289244 44.766169-16.87209 69.313982z' fill='{{(isStr ? colors : colors[9]) || 'rgb(241,250,252)'}}' /%3E%3Cpath d='M388.861046 789.680331c-84.413766 3.838928-151.680319 73.515475-151.68032 158.867646v58.138435h-36.256544a17.061903 17.061903 0 0 1-17.061903-17.061903v-84.068262c0-87.983969 71.986302-157.799144 159.968139-158.705558a88.3508 88.3508 0 0 0 33.94039-7.119079c0.264459 0.563043 10.955875 49.338759 11.090238 49.948721z' fill='{{(isStr ? colors : colors[10]) || 'rgb(232,248,252)'}}' /%3E%3Cpath d='M681.549464 729.912484c-25.127918-0.017062-47.916222-12.830551-60.960047-34.275231a17.061903 17.061903 0 1 0-29.154528 17.731583 105.333792 105.333792 0 0 0 35.661511 35.584732l-17.735849 80.235733-42.597174-49.119087c7.918856-9.093994 5.685879-19.463366-0.776316-25.089529a17.05977 17.05977 0 0 0-24.072213 1.663536l-30.022551 34.477841-75.509586-84.017077a104.222636 104.222636 0 0 0 11.465599-39.939783c146.111741 42.812581 292.321587-66.370803 292.703348-218.351838 0-5.809578 0.087442-130.41479 0.09384-138.888158 0.04692-66.008238-53.333377-119.461048-119.350145-119.507968l-217.541399-0.149292c-66.338812 0-119.469579 53.604234-119.514366 119.352279l-0.076779 110.642176c-11.399484-5.909817-19.181845-17.633477-19.173313-31.259539l0.14076-207.856636c0.014929-22.336164 18.187989-40.494294 40.52202-40.494294a17.057638 17.057638 0 0 0 15.876101-10.714875C344.474504 72.694371 400.04299 34.123806 463.257342 34.123806l229.416483 0.15569-21.15676 55.796689c-3.523283 9.30087 1.629412 19.65318 11.190476 22.43427l29.56188 8.592801c28.254512 8.213174 49.669333 34.098214 49.786633 65.79923l-0.138628 202.439481a17.064036 17.064036 0 0 0 34.123807 0.025593c0-1.260448 0.140761-201.980943 0.136495-203.241391-0.157823-44.892-30.74555-85.104773-74.381367-97.790298l-11.821766-3.435841 23.362011-61.614798c4.224954-11.147821-4.018078-23.101817-15.942216-23.110347L463.278669 0c-73.219025-0.021327-138.760193 42.49267-169.158106 107.417477-35.682838 5.557915-63.086387 36.484747-63.11198 73.707422l-0.140761 207.856636c-0.021327 33.14488 23.167932 60.258377 53.431483 67.496889 3.122328 87.930651 55.822282 162.463442 129.800561 197.792245l-0.002133 4.348653c-0.025593 39.325554-31.905759 71.060694-71.286764 71.060694-92.123614-1.644341-176.211071 75.083038-176.279319 175.982868l-0.07038 100.786794a17.061903 17.061903 0 1 0 34.123806 0.021328l0.070381-100.786795c0.053318-78.393047 63.495873-141.880389 141.976362-141.880389 5.922613 0.104504 14.383184-0.64622 22.39588-2.35241l23.733108 108.061564c3.011426 13.702841 20.378311 18.070688 29.540552 7.534963l61.439914-70.651208 15.289598 17.01285-0.07038 102.222128a17.061903 17.061903 0 1 0 34.123806 0.021327l0.07038-102.388481 14.980351-17.204797 61.674515 71.116145c9.181437 10.589044 26.533392 6.159347 29.549083-7.496573l23.880267-108.031706a106.988797 106.988797 0 0 0 22.289244 2.386534c78.467693 0.051186 141.942238 63.596111 141.884654 142.076601l-0.068248 100.786794a17.061903 17.061903 0 1 0 34.123807 0.021328l0.068247-100.786795c0.07038-97.325361-78.642577-176.157752-175.987133-176.219602zM318.267421 447.934676l0.093841-138.288859c0.031991-47.022605 38.293309-85.251932 85.309515-85.251932l217.59685 0.149292c47.146304 0.031991 85.286056 38.201601 85.251932 85.360702l-0.095973 139.109962c-0.447875 106.79685-86.943193 193.240983-194.211379 193.240983-106.939744-0.074646-193.944786-86.471858-193.944786-194.320148z m96.544779 381.122395l-17.631344-80.282653a105.898968 105.898968 0 0 0 18.239175-13.941707l41.336726 45.992492z' fill='{{(isStr ? colors : colors[11]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--014-woman-->
<view a:if="{{name === '014-woman'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M858.659411 418.125868v198.396458c0 9.429165-7.637197 17.066362-17.066362 17.066362h-19.199657c-64.000991 0.087465-109.730308-43.977881-134.354935-102.163509-21.67428 50.58043-62.526884 90.985042-113.427308 112.040666l0.319994 0.789319c0 16.64397-1.943432 39.95022 16.234377 65.513497-0.127998-0.149331-0.234662-0.298661-0.341327-0.447992l-0.959983 1.066648 1.386642-0.469325c16.078646 22.576664 42.17738 36.79081 71.188062 37.098004 87.977096 0.853318 159.954477 70.633406 159.954478 158.610501V989.848994c0 9.429165-7.637197 17.066362-17.066362 17.066362h-622.922212c-9.429165 0-17.066362-7.637197-17.066362-17.066362v-84.222497c0-87.977096 71.977382-157.757183 159.954477-158.610501 28.844285-0.305061 55.013418-14.386943 71.188063-37.098004l1.386642 0.469325-0.959983-1.066648c-0.106665 0.149331-0.21333 0.298661-0.341327 0.447992 18.090344-25.439546 16.234377-48.596466 16.234376-65.513497l0.319995-0.789319c-76.585299-31.700767-130.451004-107.15542-130.451004-195.196515v2.559954c-29.460807 0-53.332381-23.871574-53.332382-53.332381V230.395886c0-117.821896 95.507628-213.329525 213.329525-213.329524h102.398172c117.821896 0 213.329525 95.507628 213.329524 213.329524v87.465106c55.380345 0 100.264877 44.884532 100.264877 100.264876z' fill='{{(isStr ? colors : colors[0]) || 'rgb(255,223,186)'}}' /%3E%3Cpath d='M858.659411 418.125868v198.396458c0 9.429165-7.637197 17.066362-17.066362 17.066362h-19.199657c-64.000991 0.087465-109.730308-43.977881-134.354935-102.163509 10.645143-24.767558 16.661036-52.009738 17.002363-80.595895 0.034133-1.395175 0.021333 4.243124 0.021333-104.723463 0-9.066505-7.08254-16.511705-16.149045-17.023696-33.752997-1.928499-63.139139-22.770793-73.961346-62.56955-3.029279-11.1998-16.298376-15.999714-25.919537-9.514497-85.491807 57.586172-186.409472 82.765456-287.824195 73.598686-9.962489-0.895984-18.538336 6.997208-18.538335 17.002363v103.230157c-29.460807 0-53.332381-23.871574-53.332382-53.332381V230.395886c0-117.821896 95.507628-213.329525 213.329525-213.329524h102.398172c117.821896 0 213.329525 95.507628 213.329524 213.329524v87.465106c55.380345 0 100.264877 44.884532 100.264877 100.264876z' fill='{{(isStr ? colors : colors[1]) || 'rgb(66,67,77)'}}' /%3E%3Cpath d='M688.910975 329.077858c9.060105 0.516257 16.153312 7.961458 16.153311 17.036496 0 108.955921 0.0128 103.319755-0.021333 104.71493-1.030382 86.266193-53.995836 161.018992-130.429671 192.636561-138.487127 57.485907-291.941454-44.63707-291.941454-195.196515v-100.670203c0-10.003021 8.567314-17.90688 18.529802-17.004496 102.370439 9.281968 203.179306-16.569304 287.830595-73.596553 9.627561-6.485218 22.883858-1.681037 25.923803 9.52303 10.570478 38.966771 39.38703 60.587718 73.954947 62.55675z' fill='{{(isStr ? colors : colors[2]) || 'rgb(255,235,210)'}}' /%3E%3Cpath d='M822.393392 905.626497V989.848994c0 9.429165-7.637197 17.066362-17.066362 17.066362h-622.922212c-9.429165 0-17.066362-7.637197-17.066362-17.066362v-84.222497c0-87.977096 71.977382-157.757183 159.954477-158.610501 29.091747-0.309328 55.452877-14.630139 71.614722-37.695327 74.157609 82.409195 57.097647 63.448467 84.265162 93.651661 6.783879 7.530532 18.602335 7.530532 25.386213 0 24.360098-27.084316 4.47352-4.983378 84.265163-93.651661a88.346156 88.346156 0 0 0 71.614721 37.695327c87.977096 0.853318 159.954477 70.633406 159.954478 158.610501z' fill='{{(isStr ? colors : colors[3]) || 'rgb(214,244,252)'}}' /%3E%3Cpath d='M625.046515 32.566885c26.062468 10.547012 17.429022 49.249254-10.679276 48.564466-4.330589-0.106665-109.854039-0.066132-107.701544-0.066132-117.81763 0-213.329525 95.511895-213.329524 213.329525v25.130218c0 15.440791-10.666476 16.003981-10.666476 28.074165v62.117291c0 26.533926-35.946025 35.27617-47.770881 11.524061a53.140385 53.140385 0 0 1-5.561501-23.743576V230.395886c0-117.81763 95.511895-213.329525 213.329525-213.329524h102.398172c28.285362 0 55.282213 5.503902 79.981505 15.500523z' fill='{{(isStr ? colors : colors[4]) || 'rgb(77,78,89)'}}' /%3E%3Cpath d='M453.380247 589.747337c31.869298 21.332952 7.579598 70.676071-28.60749 57.976565-79.542047-27.912035-142.10093-102.148576-142.100929-199.456705v-100.670203c0-10.005155 8.575847-17.898347 18.538335-17.002363a448.63199 448.63199 0 0 0 26.838988 1.623438c17.533554 0.533324 31.421306 15.011999 31.421306 32.554085v49.364452c-0.002133 74.29414 37.87879 138.105268 93.90979 175.610731z' fill='{{(isStr ? colors : colors[5]) || 'rgb(255,243,228)'}}' /%3E%3Cpath d='M397.867638 710.387317l-1.386642-0.469325c0.042666-0.042666 0.063999-0.085332 0.085332-0.149331 0.127998-0.149331 0.234662-0.298661 0.341327-0.447992zM591.250852 709.917992l-1.386642 0.469325 0.959983-1.066648c0.106665 0.149331 0.21333 0.298661 0.341327 0.447992 0.023466 0.063999 0.042666 0.106665 0.085332 0.149331z' fill='{{(isStr ? colors : colors[6]) || 'rgb(255,223,186)'}}' /%3E%3Cpath d='M577.341767 679.283872c-52.557995 22.000674-110.316964 23.70731-166.951686 0 2.892748-12.001919 2.410624-19.775647 2.410623-35.028708l0.319995-0.789319c51.374016 21.324419 110.016169 21.367085 161.49045 0l0.319994 0.789319c0 15.259461-0.479991 23.031055 2.410624 35.028708z' fill='{{(isStr ? colors : colors[7]) || 'rgb(255,214,166)'}}' /%3E%3Cpath d='M397.867638 710.387317l-1.386642-0.469325c0.042666-0.042666 0.063999-0.085332 0.085332-0.149331 0.127998-0.149331 0.234662-0.298661 0.341327-0.447992zM591.250852 709.917992l-1.386642 0.469325 0.959983-1.066648c0.106665 0.149331 0.21333 0.298661 0.341327 0.447992 0.023466 0.063999 0.042666 0.106665 0.085332 0.149331z' fill='{{(isStr ? colors : colors[8]) || 'rgb(255,223,186)'}}' /%3E%3Cpath d='M628.434188 739.89079c-0.264529 0.56319-21.320153 96.348146-21.185755 95.738024-2.77755 12.601375-18.726066 16.658903-27.188848 6.916143-56.013933-64.502315-48.37247-56.090732-54.044902-61.206374l64.80951-72.020047a88.653351 88.653351 0 0 0 37.609995 30.572254zM396.907655 709.320669l63.273537 70.313411-52.485463 62.196223c-8.388117 9.941156-24.532895 5.95616-27.331779-6.747613-0.134398-0.607989-20.799629-94.630844-21.06629-95.194033a88.632018 88.632018 0 0 0 37.609995-30.567988z' fill='{{(isStr ? colors : colors[9]) || 'rgb(232,248,252)'}}' /%3E%3Cpath d='M370.392928 789.852565c-84.435826 3.839931-151.719958 73.534687-151.719958 158.909162V1006.915356h-36.266019a17.066362 17.066362 0 0 1-17.066362-17.066362v-84.090232c0-88.006962 72.005114-157.840382 160.009943-158.747033a88.373889 88.373889 0 0 0 33.949261-7.120939c0.264529 0.56319 10.958738 49.353785 11.093135 49.961775z' fill='{{(isStr ? colors : colors[10]) || 'rgb(232,248,252)'}}' /%3E%3Cpath d='M617.341053 789.852565c84.435826 3.839931 151.719958 73.534687 151.719957 158.909162V1006.915356h36.26602a17.066362 17.066362 0 0 0 17.066362-17.066362v-84.090232c0-88.006962-72.005114-157.840382-160.009944-158.747033a88.373889 88.373889 0 0 1-33.94926-7.120939c-0.264529 0.56319-10.958738 49.353785-11.093135 49.961775z' fill='{{(isStr ? colors : colors[11]) || 'rgb(189,239,252)'}}' /%3E%3Cpath d='M398.251631 798.919069c3.17861 14.442409 6.570549 29.823468 9.450498 42.900568-8.388117 9.943289-24.532895 5.971093-27.329646-6.734813-8.20252-37.251602-20.893494-94.814307-21.07269-95.194034a88.651217 88.651217 0 0 0 37.609995-30.570121l18.218342 20.266305c-15.66052 19.498319-22.292935 44.777867-16.876499 69.332095z' fill='{{(isStr ? colors : colors[12]) || 'rgb(241,250,252)'}}' /%3E%3Cpath d='M526.014683 781.340716L557.864781 1006.915356h-127.997715l30.314126-227.281276 21.000158 23.327584c6.779612 7.530532 18.587401 7.530532 25.367014 0z' fill='{{(isStr ? colors : colors[13]) || 'rgb(255,164,184)'}}' /%3E%3Cpath d='M712.125494 574.155082c-8.853175-11.991253-17.271158-26.632058-24.084904-42.729903 10.645143-24.767558 16.661036-52.009738 17.002363-80.595895 29.699736 0 53.353714-24.121169 53.353714-53.332381V317.860992a100.612604 100.612604 0 0 1 39.295299 7.978524c23.415049 90.502917-7.509199 188.623832-85.566472 248.315566z' fill='{{(isStr ? colors : colors[14]) || 'rgb(44,44,51)'}}' /%3E%3Cpath d='M482.474127 804.401638L455.466609 1006.915356h-25.599543l30.314126-227.281276z' fill='{{(isStr ? colors : colors[15]) || 'rgb(255,153,175)'}}' /%3E%3Cpath d='M775.460896 302.049007V230.395886c0-127.039865-103.353888-230.395886-230.395886-230.395886h-102.389639C315.631239 0 212.270951 103.360288 212.270951 230.40442v167.098883c0 33.181274 23.082255 61.057043 54.029969 68.457444 6.414819 83.181448 57.594705 154.01965 129.433422 188.36997v4.494854c0 39.344364-31.879964 71.126197-71.30966 71.126196-97.376395 0-176.152588 78.78046-176.152588 176.148322V1006.915356a17.066362 17.066362 0 0 0 34.132724 0v-100.813134c0-78.494599 63.518866-142.017731 142.019864-142.015597 7.468667 0 14.926667-0.793586 22.297202-2.370091l23.811842 108.072737c3.014346 13.687222 20.377236 18.08181 29.552539 7.517732l33.622866-38.715042-20.776162 166.207166a17.068495 17.068495 0 0 0 33.870328 4.232458l24.010238-192.084038c11.534727 12.814705 11.310731 12.735773 13.444027 14.192814a17.170893 17.170893 0 0 0 6.329487 2.651686c1.587172 0.311461 3.313008 0.326394 3.281008 0.326394 9.540096 0 12.944836-5.943361 23.009723-17.518621l24.052903 192.431765a17.068495 17.068495 0 0 0 33.870329-4.232458l-20.776162-166.207166 33.622866 38.715042c9.190236 10.581144 26.542459 6.146024 29.552539-7.517732l23.813975-108.077004a107.176753 107.176753 0 0 0 22.297202 2.372224c78.492465-0.002133 142.019864 63.514599 142.019864 142.015598V1006.915356a17.066362 17.066362 0 0 0 34.132724 0v-100.813134c0-97.355062-78.784727-176.150455-176.152588-176.148321-25.134485 0-47.937277-12.799771-60.999444-34.241522-4.904446-8.048923-15.406658-10.604611-23.453448-5.695899a17.066362 17.066362 0 0 0-5.695899 23.453448c9.083571 14.911734 21.379885 26.952052 35.694296 35.568432l-17.687151 80.2695-42.642438-49.102057c7.903859-9.087838 5.680965-19.464186-0.795719-25.096085a17.066362 17.066362 0 0 0-24.07637 1.678904l-30.006931 34.508183-75.586917-83.9857a104.260539 104.260539 0 0 0 11.447262-40.044085c145.738198 42.902701 291.448663-66.484146 292.617709-217.218522 0.0192-0.73812 0.040533-121.169037-0.046933-122.568478-0.567457-9.252101-8.275052-16.018914-17.013029-16.018914-2.933281 0-23.950506 4.159926-44.534672-8.223853-32.106093-19.314855-32.266091-61.641566-32.263957-62.063959 0.089598-13.868552-15.598655-22.060406-26.926453-14.039216-70.149148 49.656713-152.432478 83.482243-241.986079 96.019619a17.066362 17.066362 0 0 0 2.346624 33.970594c2.133295 0 118.052292-10.881939 236.691241-86.861383 12.590709 47.261023 47.941544 73.460022 89.606934 75.825846v102.172043c0 107.044489-87.085378 194.129867-194.129868 194.129867-100.188078 0-194.129867-77.871676-194.129867-200.796415v-113.064648a17.066362 17.066362 0 0 0-34.132724 0v100.958198c-11.41313-6.116157-19.199657-18.152209-19.199657-31.982363v-167.098883C246.403675 122.180219 334.45117 34.132724 442.675371 34.132724H545.06501c108.219934 0 196.263163 88.043228 196.263162 196.263162v167.107417a17.066362 17.066362 0 0 0 34.132724 0v-60.809581c37.71666 7.895326 66.132153 41.402994 66.132153 81.432146v198.396458h-19.199657c-34.354587 0-66.66761-15.171996-88.651218-41.629124a17.066362 17.066362 0 0 0-26.254464 21.815078C735.980001 630.992467 777.860853 650.65505 822.393392 650.65505h36.266019a17.066362 17.066362 0 0 0 17.066362-17.066362V418.125868c0-58.900282-43.628021-107.784742-100.264877-116.076861zM396.566328 829.305727l-17.691418-80.292967a105.967175 105.967175 0 0 0 18.235408-13.95815l41.377395 45.976779z' fill='{{(isStr ? colors : colors[16]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--home1-->
<view a:if="{{name === 'home1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1025 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M992.090881 443.129l-388.66-404.16C579.320881 13.934 546.998881 0.023 512.424881 0.023s-66.896 13.776-91.006 38.946L32.889881 443.129c-40.138 41.728-34.972 77.759-28.348 92.86 4.769 10.731 20.665 40.005 66.63 40.005l56.961 0 0 310.902c0 70.604 50.736 137.104 122.797 137.104l165.454 0L416.383881 694.949c0-35.236-5.298-54.974 30.733-54.974l130.745 0c36.031 0 30.733 19.605 30.733 54.974L608.594881 1024l165.321 0c72.063 0 122.797-66.499 122.797-137.104L896.712881 575.994l56.961 0c45.966 0 61.862-29.407 66.63-40.005C1026.927881 520.888 1032.094881 484.856 992.090881 443.129L992.090881 443.129zM953.676881 512.013 832.601881 512.013l0 374.884c0 35.237-22.784 72.99-58.815 72.99L672.446881 959.887 672.446881 694.949c0-70.604-22.652-118.956-94.717-118.956L446.985881 575.993c-72.063 0-94.717 48.352-94.717 118.956l0 265.069L250.929881 960.018c-36.031 0-58.816-37.754-58.816-72.99L192.113881 512.145 71.038881 512.145c-1.191 0-2.118-4.371-3.047-4.503 2.253-3.843 6.094-13.646 12.452-20.269l388.66-403.895c11.525-12.187 27.95-19.473 43.848-19.34 15.895-0.132 31.262 7.285 42.919 19.34L944.398881 487.24c6.358 6.623 10.199 16.426 12.32 20.269-0.796 0.132-1.856 4.503-3.047 4.503L953.676881 512.013z' fill='{{(isStr ? colors : colors[0]) || 'rgb(39,38,54)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--home-->
<view a:if="{{name === 'home'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M549.61981 133.022476l319.683047 203.605334A70.851048 70.851048 0 0 1 902.095238 396.361143v434.883047A70.89981 70.89981 0 0 1 831.146667 902.095238h-282.819048l0.024381-218.112h-71.826286v218.087619L192.853333 902.095238A70.89981 70.89981 0 0 1 121.904762 831.24419V390.241524c0-24.527238 12.678095-47.299048 33.54819-60.220953l318.659048-197.485714a70.972952 70.972952 0 0 1 75.50781 0.487619zM828.952381 828.952381V397.214476L511.488 195.047619 195.047619 391.119238V828.952381h211.309714v-216.551619h212.187429v216.527238L828.952381 828.952381z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--left-arrow-->
<view a:if="{{name === 'left-arrow'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M466.70019 856.795429l-293.059047-293.059048L121.904762 512l51.712-51.712L466.70019 167.204571l51.712 51.712L261.948952 475.428571h640.24381v73.142858h-640.24381l256.487619 256.487619-51.736381 51.736381z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--left-btn-->
<view a:if="{{name === 'left-btn'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 97.52381c228.912762 0 414.47619 185.563429 414.47619 414.47619s-185.563429 414.47619-414.47619 414.47619S97.52381 740.912762 97.52381 512 283.087238 97.52381 512 97.52381z m0 73.142857C323.486476 170.666667 170.666667 323.486476 170.666667 512s152.81981 341.333333 341.333333 341.333333 341.333333-152.81981 341.333333-341.333333S700.513524 170.666667 512 170.666667z m48.761905 118.954666L612.473905 341.333333l-170.666667 170.666667 170.666667 170.666667L560.761905 734.378667 338.383238 512 560.761905 289.621333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--left-btn-fill-->
<view a:if="{{name === 'left-btn-fill'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 97.52381c228.912762 0 414.47619 185.563429 414.47619 414.47619s-185.563429 414.47619-414.47619 414.47619S97.52381 740.912762 97.52381 512 283.087238 97.52381 512 97.52381z m48.761905 192.097523L338.383238 512 560.761905 734.378667 612.473905 682.666667l-170.666667-170.666667 170.666667-170.666667L560.761905 289.621333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--home-fill-->
<view a:if="{{name === 'home-fill'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M549.61981 133.022476l319.683047 203.605334A70.851048 70.851048 0 0 1 902.095238 396.361143v434.883047A70.89981 70.89981 0 0 1 831.146667 902.095238l-248.222477-0.024381V617.910857h-153.673142v284.16L192.828952 902.095238A70.89981 70.89981 0 0 1 121.904762 831.24419V390.241524c0-24.527238 12.678095-47.299048 33.54819-60.220953l318.659048-197.485714a70.972952 70.972952 0 0 1 75.50781 0.487619z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--search-->
<view a:if="{{name === 'search'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M994.112 858.368L769.152 633.408C808.32 569.984 832 496 832 416 832 186.24 645.76 0 416 0S0 186.24 0 416 186.24 832 416 832c80 0 153.984-23.68 217.344-62.784l225.024 224.96a96.021333 96.021333 0 0 0 135.744-135.808zM416 704C256.96 704 128 575.04 128 416S256.96 128 416 128 704 256.96 704 416 575.04 704 416 704z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--money-check-->
<view a:if="{{name === 'money-check'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1152 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M128 128C57.4 128 0 185.4 0 256v512c0 70.6 57.4 128 128 128h896c70.6 0 128-57.4 128-128V256c0-70.6-57.4-128-128-128H128z m96 320h320c17.6 0 32 14.4 32 32s-14.4 32-32 32H224c-17.6 0-32-14.4-32-32s14.4-32 32-32zM192 672c0-17.6 14.4-32 32-32h704c17.6 0 32 14.4 32 32s-14.4 32-32 32H224c-17.6 0-32-14.4-32-32z m560-352h160c26.6 0 48 21.4 48 48v96c0 26.6-21.4 48-48 48h-160c-26.6 0-48-21.4-48-48v-96c0-26.6 21.4-48 48-48z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--id-card-clip-->
<view a:if="{{name === 'id-card-clip'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1152 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 0h128c35.4 0 64 28.6 64 64v128c0 35.4-28.6 64-64 64h-128c-35.4 0-64-28.6-64-64V64c0-35.4 28.6-64 64-64zM128 128h256v96c0 53 43 96 96 96h192c53 0 96-43 96-96V128h256c70.6 0 128 57.4 128 128v640c0 70.6-57.4 128-128 128H128c-70.6 0-128-57.4-128-128V256c0-70.6 57.4-128 128-128z m224 746.6c0 11.8 9.6 21.4 21.4 21.4h405.2c11.8 0 21.4-9.6 21.4-21.4 0-59-47.8-106.6-106.6-106.6H458.6c-59 0-106.6 47.8-106.6 106.6zM576 704a128 128 0 1 0 0-256 128 128 0 1 0 0 256z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--user-large-->
<view a:if="{{name === 'user-large'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 576a288 288 0 1 0 0-576 288 288 0 1 0 0 576z m-189.4 64C144.4 640 0 784.4 0 962.6c0 34 27.6 61.4 61.4 61.4h901.2c34 0 61.4-27.6 61.4-61.4 0-178.2-144.4-322.6-322.6-322.6H322.6z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--phone-->
<view a:if="{{name === 'phone'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M329.785475 49.242931c-15.399322-37.198362-55.997534-56.99749-94.795825-46.397956l-175.992248 47.997886C24.198934 60.442438 0 92.041046 0 128.039461c0 494.778208 401.182331 895.960539 895.960539 895.960539 35.998415 0 67.597023-24.198934 77.1966-58.997402l47.997886-175.992248c10.599533-38.798291-9.199595-79.396503-46.397956-94.795825l-191.991544-79.996477c-32.598564-13.599401-70.3969-4.199815-92.595922 23.198978L609.373161 736.012684c-140.793799-66.597067-254.788778-180.592046-321.385845-321.385845l98.595658-80.596451c27.398793-22.399013 36.798379-59.997358 23.198978-92.595921l-79.996477-191.991545z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Exit-->
<view a:if="{{name === 'Exit'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M511.8 959.6c-119.5 0-231.9-46.6-316.5-131.1-84.5-84.6-131.1-197-131.1-316.5s46.6-231.9 131.1-316.5c22.9-22.9 60-22.9 82.8 0 22.9 22.9 22.9 60 0 82.8-62.4 62.4-96.8 145.4-96.8 233.6 0 88.3 34.4 171.2 96.8 233.6 62.4 62.4 145.4 96.8 233.6 96.8 88.3 0 171.2-34.4 233.6-96.8s96.8-145.4 96.8-233.6c0-88.3-34.4-171.2-96.8-233.6-22.9-22.9-22.9-60 0-82.8 22.9-22.9 60-22.9 82.8 0C912.6 280 959.2 392.4 959.2 512s-46.6 231.9-131.1 316.5C743.7 913 631.3 959.6 511.8 959.6z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M511.8 500.1c-32.3 0-58.6-26.2-58.6-58.6v-319c0-32.3 26.2-58.6 58.6-58.6s58.6 26.2 58.6 58.6v319c-0.1 32.4-26.3 58.6-58.6 58.6z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--jiantou-->
<view a:if="{{name === 'jiantou'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M730.026734 0c18.713977 0 37.429954 7.144991 51.714937 21.429974 28.570965 28.572965 28.570965 74.857909 0 103.426874L394.527143 511.999375l387.214528 387.142527c28.570965 28.536965 28.570965 74.857909 0 103.428874s-74.857909 28.570965-103.428874 0L239.314333 563.713312C225.59935 549.999329 217.886359 531.427351 217.886359 511.999375s7.712991-37.999954 21.427974-51.713937L678.311797 21.427974C692.59778 7.143991 711.312757 0 730.026734 0z' fill='{{(isStr ? colors : colors[0]) || 'rgb(44,44,44)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--icon-keyboard-->
<view a:if="{{name === 'icon-keyboard'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M242.005 1000.334c-29.013-27.875-28.444-74.524 0-102.969L627.485 512l-385.48-385.365c-28.444-28.445-28.444-74.525 0-102.97s74.525-28.444 102.97 0l437.02 436.907c15.587 13.768 38.912 61.327 0 102.97l-437.02 436.906c-20.595 21.732-67.243 34.247-102.97-0.114z' fill='{{(isStr ? colors : colors[0]) || 'rgb(44,44,44)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--tuichu-->
<view a:if="{{name === 'tuichu'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1025 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M1001.65696 463.424l-332.224-332.416C648.76096 110.4 624.05696 104.256 598.52096 113.792c-26.176 9.856-41.152 33.28-41.216 64.384L557.30496 328.32 492.28096 328.32 305.65696 328.384C266.48896 328.512 239.16096 355.712 239.03296 394.624c-0.128 78.4-0.128 156.736 0 235.072 0.128 37.696 27.776 65.28 65.792 65.664l252.48 0.064L557.24096 724.48c-0.128 40.768-0.256 81.408 0.576 122.112 0.256 15.936 6.528 33.28 16.256 45.184 11.904 14.656 28.224 22.72 45.952 22.72l0 0c17.536 0 35.2-8 49.728-22.464 110.848-110.72 221.568-221.504 332.288-332.352C1031.60896 530.176 1031.48096 493.248 1001.65696 463.424z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M387.70496 805.696 387.64096 805.696l-72.576 0.128-103.872-0.064c-52.48 0-82.624-30.144-82.624-82.688L128.56896 300.48c0-52.416 30.144-82.496 82.752-82.496l68.8-0.064 75.584 0.064 42.048-0.064c30.08 0 46.528-15.104 48.896-44.8 0.896-11.072 0.64-22.336 0.256-34.88C446.13696 106.944 428.60096 89.664 397.68896 89.6L321.33696 89.6C280.44096 89.6 239.54496 89.6 198.71296 89.856 186.16896 89.92 173.30496 91.328 160.44096 94.08 68.21696 113.728 0.88896 196.416 0.37696 290.688 0.12096 341.504 0.18496 392.384 0.24896 443.136l0.064 67.648L0.18496 581.76C0.12096 632.768-0.00704 683.712 0.44096 734.784c0.896 107.776 88.768 197.056 195.904 199.04 26.368 0.512 52.8 0.64 79.168 0.64l125.376-0.256c24.768 0 42.112-15.04 45.184-39.232 1.088-8.832 1.216-17.856 1.28-26.88l0-4.48c0.128-13.312 0.256-31.552-12.544-44.48C422.45696 806.784 404.66496 805.696 387.70496 805.696z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--dianpuzhuye-->
<view a:if="{{name === 'dianpuzhuye'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M904.4 356.4L512 64.3 119.6 356.4c-24.3 18-38.6 46.5-38.6 76.8V864c0 52.9 42.9 95.8 95.8 95.8h670.4c52.9 0 95.8-42.9 95.8-95.8V433.2c0-30.3-14.3-58.8-38.6-76.8zM691.6 816.1H332.4c-19.8 0-35.9-16.2-35.9-35.9s16.2-35.9 35.9-35.9h359.2c19.8 0 35.9 16.2 35.9 35.9s-16.2 35.9-35.9 35.9z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--dingdanguanli-->
<view a:if="{{name === 'dingdanguanli'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M353.2 142.5h311.2c21.4 0 38.9-17.5 38.9-38.9s-17.5-38.9-38.9-38.9H353.2c-21.4 0-38.9 17.5-38.9 38.9s17.5 38.9 38.9 38.9z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M745.4 103.6c0 42.8-35 77.8-77.8 77.8H356.4c-42.8 0-77.8-35-77.8-77.8-107 0-194.5 87.5-194.5 194.5v466.7c0 107 87.5 194.5 194.5 194.5h466.7c107 0 194.5-87.5 194.5-194.5V298.1c0.1-107-87.5-194.5-194.4-194.5zM443.9 813.4H288.4c-21.4 0-38.9-17.5-38.9-38.9s17.5-38.9 38.9-38.9H444c21.4 0 38.9 17.5 38.9 38.9-0.1 21.4-17.6 38.9-39 38.9zM745.4 619H278.6c-21.4 0-38.9-17.5-38.9-38.9s17.5-38.9 38.9-38.9h466.7c21.4 0 38.9 17.5 38.9 38.9 0.1 21.4-17.4 38.9-38.8 38.9z m3.2-194.5H281.9c-21.4 0-38.9-17.5-38.9-38.9s17.5-38.9 38.9-38.9h466.7c21.4 0 38.9 17.5 38.9 38.9s-17.5 38.9-38.9 38.9z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--xitongguanli-->
<view a:if="{{name === 'xitongguanli'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M381.3 87.3h-238C99.7 87.3 64 123 64 166.7v238c0 43.6 35.7 79.3 79.3 79.3h238c43.6 0 79.3-35.7 79.3-79.3v-238c0.1-43.7-35.6-79.4-79.3-79.4zM381.3 563.3h-238C99.7 563.3 64 599 64 642.7v238c0 43.6 35.7 79.3 79.3 79.3h238c43.6 0 79.3-35.7 79.3-79.3v-238c0.1-43.7-35.6-79.4-79.3-79.4zM857.3 563.3h-238c-43.6 0-79.3 35.7-79.3 79.3v238c0 43.6 35.7 79.3 79.3 79.3h238c43.6 0 79.3-35.7 79.3-79.3v-238c0.1-43.6-35.6-79.3-79.3-79.3zM936.8 229.6L794.4 87.2c-30.9-30.9-81.3-30.9-112.2 0L539.8 229.6c-30.9 30.9-30.9 81.3 0 112.2l142.4 142.4c30.9 30.9 81.3 30.9 112.2 0l142.4-142.4c30.9-30.9 30.9-81.4 0-112.2z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Password-->
<view a:if="{{name === 'Password'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M696.917333 85.333333C841.557333 85.333333 938.666667 186.794667 938.666667 337.749333v348.501334C938.666667 837.205333 841.472 938.666667 696.874667 938.666667h-229.546667a32 32 0 0 1 0-64h229.546667c107.989333 0 177.792-73.941333 177.792-188.416V337.749333c0-114.474667-69.802667-188.416-177.749334-188.416H327.04C219.093333 149.333333 149.333333 223.274667 149.333333 337.749333v348.501334c0 114.474667 69.76 188.416 177.706667 188.416a32 32 0 0 1 0 64C182.442667 938.666667 85.333333 837.205333 85.333333 686.250667V337.749333C85.333333 186.794667 182.442667 85.333333 327.04 85.333333zM377.173333 400.981333c50.048 0 91.904 33.450667 105.728 78.976h242.858667a32 32 0 0 1 32 32v79.018667a32 32 0 0 1-64 0V544h-56.704v47.018667a32 32 0 0 1-64 0V544h-90.154667a110.72 110.72 0 0 1-105.728 79.018667 111.104 111.104 0 0 1-101.248-156.544 31.914667 31.914667 0 0 1 42.325334-16.042667 31.914667 31.914667 0 0 1 16.042666 42.282667A47.061333 47.061333 0 1 0 424.192 512c0-25.898667-21.077333-46.933333-47.018667-46.933333a32 32 0 0 1 0-64z' fill='{{(isStr ? colors : colors[0]) || 'rgb(0,0,0)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Profile-->
<view a:if="{{name === 'Profile'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M853.333333 782.08C853.333333 922.453333 658.517333 938.666667 512 938.666667c-14.762667 0-29.312-0.213333-43.605333-0.64a30.592 30.592 0 0 1-29.866667-31.488c0.426667-16.896 15.104-29.610667 31.573333-29.738667 13.781333 0.384 27.733333 0.554667 41.898667 0.554667 185.6 0 279.68-32 279.68-95.274667 0-63.829333-94.122667-96.213333-279.68-96.213333-185.6 0-279.68 32.085333-279.68 95.36 0 20.693333 7.381333 50.688 71.509333 72.277333 16.128 5.418667 24.746667 22.784 19.285334 38.784a30.805333 30.805333 0 0 1-39.082667 19.2C190.293333 879.957333 170.666667 823.253333 170.666667 781.226667c0-137.514667 186.922667-155.904 332.288-156.586667h25.301333c96.938667 0.469333 325.077333 9.045333 325.077333 157.44zM512 85.333333c126.72 0 229.845333 102.485333 229.845333 228.437334 0 125.866667-103.082667 228.394667-229.845333 228.394666-126.72 0-229.888-102.485333-229.888-228.394666C282.112 187.818667 385.28 85.333333 512 85.333333z m0 61.312c-92.757333 0-168.192 74.965333-168.192 167.125334 0 92.117333 75.434667 167.082667 168.192 167.082666 92.757333 0 168.192-74.965333 168.192-167.082666 0-92.16-75.434667-167.125333-168.192-167.125334z' fill='{{(isStr ? colors : colors[0]) || 'rgb(0,0,0)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--a-ShieldDone-->
<view a:if="{{name === 'a-ShieldDone'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M485.76 89.6a82.517333 82.517333 0 0 1 52.48 0l283.434667 96.725333A77.312 77.312 0 0 1 874.666667 259.029333v283.349334a351.402667 351.402667 0 0 1-89.813334 233.941333c-26.538667 29.738667-60.586667 55.296-104.021333 78.165333l-153.173333 80.426667a33.450667 33.450667 0 0 1-30.72 0l-153.514667-80.469333c-43.52-22.912-77.610667-48.469333-104.106667-78.08A351.701333 351.701333 0 0 1 149.333333 542.208c0-17.322667 14.506667-31.402667 32.298667-31.402667 17.834667 0 32.341333 14.08 32.341333 31.402667 0 70.912 26.325333 139.434667 74.154667 193.024 21.290667 23.893333 49.450667 44.8 86.016 64l138.112 72.362667 137.813333-72.362667c36.522667-19.157333 64.64-40.064 85.973334-64a289.706667 289.706667 0 0 0 74.026666-192.853333v-283.306667a14.293333 14.293333 0 0 0-9.813333-13.482667L516.906667 148.906667a15.232 15.232 0 0 0-9.728 0L223.744 245.589333a14.293333 14.293333 0 0 0-9.813333 13.44v81.066667c0 17.365333-14.464 31.445333-32.298667 31.445333a31.914667 31.914667 0 0 1-32.298667-31.445333v-81.066667c0-32.682667 21.333333-61.866667 53.034667-72.704z m140.8 304.64a32.981333 32.981333 0 0 1 45.653333 0 30.762667 30.762667 0 0 1 0 44.373333L504.32 601.6a32.768 32.768 0 0 1-45.653333 0l-81.493334-79.146667a30.762667 30.762667 0 0 1 0-44.373333 32.853333 32.853333 0 0 1 45.653334 0l58.666666 56.96z' fill='{{(isStr ? colors : colors[0]) || 'rgb(0,0,0)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Calling-->
<view a:if="{{name === 'Calling'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M181.973333 456.746667a31.317333 31.317333 0 0 1 43.690667 8.192c108.629333 158.933333 307.242667 338.005333 433.664 390.997333 51.2 23.893333 89.258667 25.344 117.546667 4.906667 22.016-13.482667 84.778667-76.245333 83.626666-108.885334-0.341333-3.84-4.693333-11.093333-12.117333-18.773333-24.149333-24.874667-105.088-86.570667-122.026667-93.013333-13.482667-2.688-27.221333 5.290667-48.384 18.688l-10.88 6.826666a31.36 31.36 0 0 1-43.093333-10.794666 31.786667 31.786667 0 0 1 10.666667-43.477334l9.813333-6.101333c23.466667-14.848 55.68-35.072 96.512-26.581333 32.042667 6.656 133.034667 90.325333 152.32 110.250666 18.133333 18.645333 28.245333 38.570667 29.952 59.264 2.730667 77.653333-100.693333 158.933333-112.512 165.76-22.613333 16.384-49.024 24.661333-78.72 24.661334-29.568 0-62.421333-8.192-98.005333-24.832-136.832-57.301333-343.808-242.816-460.117334-413.098667a31.786667 31.786667 0 0 1 8.064-43.989333zM265.258667 94.293333l7.765333 0.128c21.546667 1.749333 41.344 11.946667 59.946667 30.208 19.712 19.370667 102.826667 121.088 109.482666 153.429334 7.168 34.645333-8.32 61.696-19.584 81.450666-11.093333 19.456-14.933333 27.733333-11.093333 38.272 28.032 69.12 71.338667 125.525333 128.298667 166.485334 14.122667 10.24 17.408 29.952 7.296 44.202666a31.317333 31.317333 0 0 1-43.818667 7.338667c-66.773333-48.042667-117.376-113.706667-150.485333-195.285333-14.506667-40.32 2.730667-70.528 15.36-92.586667 9.472-16.554667 14.677333-26.453333 12.458666-36.949333-5.845333-15.232-67.157333-96.853333-91.818666-121.045334-7.68-7.552-14.805333-11.946667-20.138667-12.373333-28.245333-1.322667-91.306667 59.050667-105.258667 82.261333-21.546667 29.866667-20.053333 68.181333 3.114667 119.466667a31.744 31.744 0 0 1-15.573333 41.898667 31.317333 31.317333 0 0 1-41.6-15.701334c-32.938667-72.874667-32.384-133.717333 1.834666-180.736 7.210667-12.245333 86.954667-115.285333 161.578667-110.336z m348.757333 140.672l4.224 0.426667a216.832 216.832 0 0 1 171.434667 172.672 31.658667 31.658667 0 0 1-30.933334 37.674667 31.488 31.488 0 0 1-30.762666-25.6 153.898667 153.898667 0 0 0-121.770667-122.581334 31.701333 31.701333 0 0 1-24.874667-37.12 31.957333 31.957333 0 0 1 36.906667-25.002666zM611.456 85.333333l4.266667 0.256c170.837333 19.072 303.573333 152.576 322.730666 324.608a31.573333 31.573333 0 0 1-31.232 35.2 31.530667 31.530667 0 0 1-31.189333-28.117333c-15.914667-142.421333-125.781333-253.013333-267.306667-268.8a31.573333 31.573333 0 0 1-27.733333-34.986667 31.317333 31.317333 0 0 1 34.688-27.904z' fill='{{(isStr ? colors : colors[0]) || 'rgb(0,0,0)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Wallet-->
<view a:if="{{name === 'Wallet'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M691.797333 106.666667C827.904 106.666667 938.666667 219.946667 938.666667 359.210667v305.578666c0 139.264-110.762667 252.544-246.869334 252.544a28.8 28.8 0 0 1-28.416-29.098666c0-16.085333 12.714667-29.098667 28.416-29.098667 104.746667 0 189.994667-87.168 189.994667-194.346667V420.906667h-140.117333c-45.994667 0-83.498667 38.4-83.541334 85.504 0 47.104 37.546667 85.418667 83.541334 85.461333h58.24c15.701333 0 28.416 13.056 28.416 29.098667a28.8 28.8 0 0 1-28.416 29.098666h-58.24c-77.397333 0-140.373333-64.469333-140.416-143.658666 0-79.232 63.018667-143.658667 140.416-143.701334h140.117333v-3.498666c0-107.178667-85.248-194.346667-189.994667-194.346667h-359.68c-84.394667 0-155.221333 56.96-179.925333 134.997333h374.954667c15.701333 0 28.416 13.056 28.416 29.098667a28.757333 28.757333 0 0 1-28.416 29.141333H142.336l-0.128 1.109334v305.578666c0 107.178667 85.205333 194.346667 189.952 194.346667h180.906667c15.744 0 28.458667 13.013333 28.458666 29.098667a28.8 28.8 0 0 1-28.416 29.098666H332.16C196.053333 917.333333 85.333333 804.053333 85.333333 664.789333V359.253333C85.333333 219.946667 196.053333 106.666667 332.16 106.666667z m68.992 368c15.701333 0 28.416 13.056 28.416 29.098666a28.8 28.8 0 0 1-28.416 29.098667h-12.928a28.8 28.8 0 0 1-28.458666-29.098667c0-16.042667 12.757333-29.098667 28.458666-29.098666z' fill='{{(isStr ? colors : colors[0]) || 'rgb(0,0,0)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Work-->
<view a:if="{{name === 'Work'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M905.173333 628.266667c17.621333 0 31.914667 14.293333 31.914667 31.914666 0 0.938667-6.016 76.373333-7.381333 99.541334-2.432 40.021333-17.962667 80.853333-41.557334 109.269333-32.853333 39.594667-74.709333 58.112-131.626666 58.24h-28.501334l-7.082666 0.042667-117.76 0.042666H491.946667a32 32 0 1 1 0.042666-64H604.416l116.309333-0.042666H756.394667c38.485333-0.128 61.610667-9.984 82.474666-35.157334 14.805333-17.792 25.386667-46.165333 26.965334-72.234666 1.365333-23.552 7.936-98.133333 7.936-98.133334a31.616 31.616 0 0 1 31.402666-29.44z m-786.773333 0c16.768 0 30.08 13.056 31.402667 29.482666 0 0 6.570667 74.666667 7.978666 98.133334 1.578667 26.069333 12.117333 54.442667 26.922667 72.192 20.906667 25.258667 44.032 35.072 82.474667 35.157333l18.816 0.042667H315.605333c17.664 0 32.128 14.336 32.128 32s-14.506667 32-32.213333 32H267.136c-57.002667-0.170667-98.858667-18.688-131.712-58.325334-23.552-28.373333-39.125333-69.205333-41.514667-109.226666-1.408-23.210667-7.424-98.56-7.424-99.541334 0-17.621333 14.293333-31.914667 31.914667-31.914666z m393.386667-3.925334a32 32 0 0 1 32 32v55.210667a32 32 0 0 1-64 0v-55.210667a32 32 0 0 1 32-32zM566.869333 85.333333a126.421333 126.421333 0 0 1 125.184 110.122667h83.754667a162.645333 162.645333 0 0 1 162.432 162.432v147.925333a32.085333 32.085333 0 0 1-14.336 26.709334c-87.978667 58.197333-192.426667 97.024-301.994667 112.170666a32 32 0 0 1-35.413333-23.850666 76.970667 76.970667 0 0 0-74.922667-57.685334c-35.882667 0-66.389333 23.253333-75.904 57.856a31.701333 31.701333 0 0 1-35.285333 23.253334c-108.842667-15.146667-212.821333-53.76-300.672-111.744A31.957333 31.957333 0 0 1 85.333333 505.813333V357.888a162.816 162.816 0 0 1 162.858667-162.432L331.52 195.413333A126.378667 126.378667 0 0 1 456.704 85.333333z m0 64h-110.165333a62.293333 62.293333 0 0 0-62.250667 62.208l0.042667 15.786667a32 32 0 0 1-32 32.042667l-114.261333 0.085333A98.773333 98.773333 0 0 0 149.333333 357.888v130.432c70.016 43.434667 150.570667 73.770667 235.136 88.661333a143.146667 143.146667 0 0 1 127.104-77.824c54.656 0 103.296 30.976 126.677334 78.208 85.077333-14.976 165.888-45.482667 235.989333-89.045333V357.888a98.56 98.56 0 0 0-98.432-98.432h-264.021333a32 32 0 0 1 0-64h115.2A62.378667 62.378667 0 0 0 566.869333 149.333333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(0,0,0)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />