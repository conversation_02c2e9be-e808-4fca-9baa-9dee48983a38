/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconArrowDownCircle = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M447.232 90.197333A430.933333 430.933333 0 0 1 512 85.333333c235.264 0 426.666667 191.402667 426.666667 426.666667s-191.402667 426.666667-426.666667 426.666667C276.736 938.666667 85.333333 747.264 85.333333 512a424.064 424.064 0 0 1 166.442667-338.218667 31.061333 31.061333 0 0 1 37.888 49.194667A362.069333 362.069333 0 0 0 147.370667 512c0 201.045333 163.541333 364.629333 364.629333 364.629333 201.045333 0 364.629333-163.584 364.629333-364.629333S713.045333 147.370667 512 147.370667a367.786667 367.786667 0 0 0-55.466667 4.181333 31.061333 31.061333 0 0 1-9.386666-61.354667zM512 631.210667a31.914667 31.914667 0 0 1-22.656-9.386667l-148.096-148.778667a32 32 0 1 1 45.312-45.184L512 553.856l125.44-125.994667a32 32 0 1 1 45.354667 45.226667l-148.138667 148.693333a31.744 31.744 0 0 1-22.656 9.429334z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconArrowDownCircle.defaultProps = {
  size: 18,
};

IconArrowDownCircle = React.memo ? React.memo(IconArrowDownCircle) : IconArrowDownCircle;

export default IconArrowDownCircle;
