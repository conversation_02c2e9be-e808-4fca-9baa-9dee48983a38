/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineAccurate = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M512 384c-70.58 0-128 57.42-128 128 0 70.579 57.42 128 128 128 70.579 0 128-57.421 128-128 0-70.58-57.421-128-128-128z m0 192c-35.29 0-64-28.71-64-64s28.71-64 64-64 64 28.71 64 64-28.71 64-64 64z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M928 480h-33.312c-3.327-40.537-12.995-79.93-28.877-117.48-19.343-45.73-47.026-86.793-82.281-122.049-35.256-35.255-76.319-62.938-122.05-82.281-37.55-15.882-76.942-25.55-117.479-28.877V96c0-17.673-14.327-32-32-32-17.673 0-32 14.327-32 32v33.312c-40.537 3.327-79.93 12.995-117.48 28.878-45.73 19.342-86.793 47.025-122.049 82.281-35.255 35.255-62.938 76.319-82.281 122.049-15.882 37.55-25.55 76.943-28.877 117.48H96c-17.673 0-32 14.327-32 32s14.327 32 32 32h33.312c3.327 40.537 12.995 79.931 28.878 117.48 19.342 45.729 47.025 86.793 82.281 122.049 35.255 35.255 76.319 62.938 122.049 82.281 37.55 15.882 76.943 25.55 117.48 28.877V928c0 17.673 14.327 32 32 32s32-14.327 32-32v-33.312c40.537-3.327 79.931-12.995 117.48-28.877 45.729-19.343 86.793-47.026 122.049-82.281 35.255-35.256 62.938-76.319 82.281-122.05 15.882-37.55 25.55-76.942 28.877-117.479H928c17.673 0 32-14.327 32-32C960 494.327 945.673 480 928 480zM738.274 738.274C685.441 791.107 617.387 823.189 544 830.428V800c0-17.673-14.327-32-32-32-17.673 0-32 14.327-32 32v30.428c-73.387-7.239-141.441-39.321-194.274-92.154S200.811 617.387 193.572 544H224c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32h-30.428c7.239-73.387 39.32-141.441 92.154-194.274 52.833-52.833 120.887-84.915 194.274-92.154V224c0 17.673 14.327 32 32 32 17.673 0 32-14.327 32-32v-30.428c73.387 7.239 141.441 39.32 194.274 92.154 52.833 52.833 84.914 120.887 92.154 194.274H800c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h30.428c-7.239 73.387-39.321 141.441-92.154 194.274z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineAccurate.defaultProps = {
  size: 18,
};

IconLineAccurate = React.memo ? React.memo(IconLineAccurate) : IconLineAccurate;

export default IconLineAccurate;
