/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineHealthreport = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M800 64H224c-52.935 0-96 43.065-96 96v704c0 52.935 43.065 96 96 96h576c52.935 0 96-43.065 96-96V160c0-52.935-43.065-96-96-96z m32 800c0 17.645-14.355 32-32 32H224c-17.645 0-32-14.355-32-32V160c0-17.645 14.355-32 32-32h576c17.645 0 32 14.355 32 32v704z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M672 193c-23.854 0-46.377 8.646-64 24.448C590.377 201.646 567.854 193 544 193c-25.643 0-49.751 9.986-67.883 28.118-37.43 37.43-37.43 98.333 0 135.764l0.255-0.255 109 109C591.621 471.876 599.811 475 608 475s16.379-3.124 22.628-9.373L736.23 360.025a32.14 32.14 0 0 0 3.653-3.144c37.43-37.43 37.43-98.333 0-135.764C721.751 202.986 697.643 193 672 193z m24.894 116.105a32.257 32.257 0 0 0-2.521 2.267L608 397.745l-86.372-86.372a32.257 32.257 0 0 0-2.521-2.267c-10.155-12.555-9.402-31.065 2.266-42.733C527.417 260.329 535.452 257 544 257c7.408 0 14.43 2.504 20.105 7.105 0.704 0.87 1.458 1.713 2.268 2.522l19 19C591.621 291.876 599.811 295 608 295s16.379-3.124 22.627-9.373l19-19a32.487 32.487 0 0 0 2.268-2.522C657.57 259.504 664.592 257 672 257c8.548 0 16.583 3.329 22.627 9.373 11.668 11.668 12.422 30.177 2.267 42.732zM544 768H288c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h256c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM288 704h128c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32H288c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineHealthreport.defaultProps = {
  size: 18,
};

IconLineHealthreport = React.memo ? React.memo(IconLineHealthreport) : IconLineHealthreport;

export default IconLineHealthreport;
