/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconDuicuo2 = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M512 960A448 448 0 1 0 512 64a448 448 0 0 0 0 896z m226.304-674.24a44.8 44.8 0 0 1 0 63.36L575.424 512.064l162.88 162.88a44.8 44.8 0 1 1-63.36 63.36L512.064 575.424l-162.944 162.88a44.8 44.8 0 1 1-63.36-63.36l162.944-162.88-162.944-162.944a44.8 44.8 0 1 1 63.36-63.36l162.944 162.944 162.88-162.944a44.8 44.8 0 0 1 63.36 0z"
        fill={getIconColor(color, 0, '#F21D1D')}
      />
    </Svg>
  );
};

IconDuicuo2.defaultProps = {
  size: 18,
};

IconDuicuo2 = React.memo ? React.memo(IconDuicuo2) : IconDuicuo2;

export default IconDuicuo2;
