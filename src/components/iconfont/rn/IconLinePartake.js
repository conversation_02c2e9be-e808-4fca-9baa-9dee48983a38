/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLinePartake = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M800 640c-51.015 0-96.529 23.999-125.847 61.299l-295.619-147.81A159.67 159.67 0 0 0 384 512c0-14.346-1.909-28.252-5.466-41.49L674.153 322.7C703.471 360.001 748.985 384 800 384c88.225 0 160-71.776 160-160S888.225 64 800 64s-160 71.776-160 160c0 14.346 1.909 28.252 5.466 41.49L349.847 413.3C320.529 375.999 275.014 352 224 352c-88.224 0-160 71.776-160 160 0 88.225 71.776 160 160 160 51.014 0 96.529-23.999 125.847-61.299l295.619 147.81A159.676 159.676 0 0 0 640 800c0 88.225 71.775 160 160 160s160-71.775 160-160-71.775-160-160-160z m0-512c52.935 0 96 43.065 96 96s-43.065 96-96 96-96-43.065-96-96 43.065-96 96-96zM224 608c-52.935 0-96-43.065-96-96s43.065-96 96-96 96 43.065 96 96-43.065 96-96 96z m576 288c-52.935 0-96-43.065-96-96s43.065-96 96-96 96 43.065 96 96-43.065 96-96 96z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconLinePartake.defaultProps = {
  size: 18,
};

IconLinePartake = React.memo ? React.memo(IconLinePartake) : IconLinePartake;

export default IconLinePartake;
