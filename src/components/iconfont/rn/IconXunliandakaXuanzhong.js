/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconXunliandakaXuanzhong = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M401.6 307.029333l233.322667 133.397334 0.085333-0.021334 144.234667-38.656a42.666667 42.666667 0 0 1 22.101333 82.432L657.066667 522.816c-3.925333 1.066667-7.893333 1.536-11.733334 1.472a42.709333 42.709333 0 0 1-38.912-1.856l-84.117333-48.085333-15.189333 26.154666c0.768 0.682667 1.514667 1.386667 2.261333 2.112l0.149333 0.149334 127.936 129.28c10.453333 10.581333 14.293333 25.216 11.52 38.784 0.085333 1.130667 0.149333 2.261333 0.149334 3.413333v106.666667c0 12.288-5.205333 23.36-13.504 31.146666h111.744l56.256-97.450666a64 64 0 0 1 110.869333 64l-74.666667 129.322666a64 64 0 0 1-63.210666 31.552 63.850667 63.850667 0 0 1-8.64 0.576H170.666667a64 64 0 1 1 0-128h406.656a42.538667 42.538667 0 0 1-13.504-31.146666l-0.021334-101.994667-101.013333-102.058667-63.786667 109.909334a42.773333 42.773333 0 0 1-39.04 21.248 42.901333 42.901333 0 0 1-10.304 1.258666H192a42.666667 42.666667 0 1 1 0-85.333333h144.768l111.466667-191.957333-73.024-41.749334-112.213334 63.04c-19.925333 11.2-45.184 4.266667-56.618666-15.530666a40.789333 40.789333 0 0 1 15.36-55.978667l132.629333-74.496a41.6 41.6 0 0 1 19.157333-5.333333c9.344-1.514667 19.221333 0.042667 28.074667 5.098666z m102.613333-199.829333l257.856 148.864a154.965333 154.965333 0 1 1-257.834666-148.864zM611.605333 64a155.029333 155.029333 0 0 1 149.312 113.386667l-188.010666-108.522667A155.242667 155.242667 0 0 1 611.584 64z"
        fill={getIconColor(color, 0, '#54B277')}
      />
    </Svg>
  );
};

IconXunliandakaXuanzhong.defaultProps = {
  size: 18,
};

IconXunliandakaXuanzhong = React.memo ? React.memo(IconXunliandakaXuanzhong) : IconXunliandakaXuanzhong;

export default IconXunliandakaXuanzhong;
