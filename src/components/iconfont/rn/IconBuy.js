/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconBuy = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M602.496 479.274667h118.101333c17.92 0 32-14.805333 32-32.64a32 32 0 0 0-32-32.682667h-118.101333a32 32 0 0 0-32 32.682667c0 17.834667 14.08 32.64 32 32.64z m258.389333-226.389334c26.026667 0 43.050667 9.173333 60.117334 29.184 17.066667 20.053333 20.053333 48.768 16.213333 74.837334l-40.533333 285.653333c-7.68 54.912-53.76 95.36-107.861334 95.36H323.669333c-56.704 0-103.594667-44.373333-108.288-101.845333L176.213333 161.408 111.786667 150.101333a32.725333 32.725333 0 0 1 11.093333-64.426666l101.674667 15.658666c14.506667 2.645333 25.173333 14.805333 26.453333 29.610667l8.106667 97.536c1.28 13.994667 12.373333 24.405333 26.026666 24.405333h575.744z m-544 553.813334c-35.84 0-64.853333 29.653333-64.853333 66.218666 0 36.138667 29.013333 65.749333 64.853333 65.749334 35.370667 0 64.341333-29.610667 64.341334-65.749334 0-36.565333-29.013333-66.176-64.384-66.176z m479.573334 0c-35.797333 0-64.768 29.653333-64.768 66.218666 0 36.138667 29.013333 65.749333 64.810666 65.749334 35.370667 0 64.341333-29.610667 64.341334-65.749334 0-36.565333-29.013333-66.176-64.341334-66.176z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconBuy.defaultProps = {
  size: 18,
};

IconBuy = React.memo ? React.memo(IconBuy) : IconBuy;

export default IconBuy;
