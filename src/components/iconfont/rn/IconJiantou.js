/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconJiantou = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M730.026734 0c18.713977 0 37.429954 7.144991 51.714937 21.429974 28.570965 28.572965 28.570965 74.857909 0 103.426874L394.527143 511.999375l387.214528 387.142527c28.570965 28.536965 28.570965 74.857909 0 103.428874s-74.857909 28.570965-103.428874 0L239.314333 563.713312C225.59935 549.999329 217.886359 531.427351 217.886359 511.999375s7.712991-37.999954 21.427974-51.713937L678.311797 21.427974C692.59778 7.143991 711.312757 0 730.026734 0z"
        fill={getIconColor(color, 0, '#2C2C2C')}
      />
    </Svg>
  );
};

IconJiantou.defaultProps = {
  size: 18,
};

IconJiantou = React.memo ? React.memo(IconJiantou) : IconJiantou;

export default IconJiantou;
