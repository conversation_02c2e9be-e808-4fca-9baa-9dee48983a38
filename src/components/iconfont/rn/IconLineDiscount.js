/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineDiscount = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M928 448c3.314 0 6.51-0.504 9.516-1.439C950.542 442.51 960 430.36 960 416V256c0-52.935-43.065-96-96-96H160c-52.935 0-96 43.065-96 96v160c0 17.673 14.327 32 32 32 35.29 0 64 28.71 64 64s-28.71 64-64 64c-14.36 0-26.51 9.458-30.561 22.484A31.989 31.989 0 0 0 64 608v160c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V608c0-17.673-14.327-32-32-32-35.29 0-64-28.71-64-64s28.71-64 64-64z m-32 187.95V768c0 17.645-14.355 32-32 32H160c-17.645 0-32-14.355-32-32V635.95c55.145-14.245 96-64.417 96-123.95 0-59.534-40.855-109.705-96-123.95V256c0-17.645 14.355-32 32-32h704c17.645 0 32 14.355 32 32v132.05c-55.145 14.245-96 64.416-96 123.95s40.855 109.705 96 123.95z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M384 352c-17.673 0-32 14.327-32 32v256c0 17.673 14.327 32 32 32s32-14.327 32-32V384c0-17.673-14.327-32-32-32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineDiscount.defaultProps = {
  size: 18,
};

IconLineDiscount = React.memo ? React.memo(IconLineDiscount) : IconLineDiscount;

export default IconLineDiscount;
