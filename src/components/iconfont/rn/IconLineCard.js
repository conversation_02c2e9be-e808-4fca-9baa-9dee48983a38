/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineCard = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M864 128H160c-52.935 0-96 43.065-96 96v576c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V224c0-52.935-43.065-96-96-96z m32 672c0 17.645-14.355 32-32 32H160c-17.645 0-32-14.355-32-32V384h768v416z m0-480H128v-96c0-17.645 14.355-32 32-32h704c17.645 0 32 14.355 32 32v96z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M512 736h256c17.673 0 32-14.327 32-32s-14.327-32-32-32H512c-17.673 0-32 14.327-32 32s14.327 32 32 32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineCard.defaultProps = {
  size: 18,
};

IconLineCard = React.memo ? React.memo(IconLineCard) : IconLineCard;

export default IconLineCard;
