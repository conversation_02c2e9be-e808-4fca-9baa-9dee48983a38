/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineShipment = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M832 96H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m-192 64v32c0 17.645-14.355 32-32 32H416c-17.645 0-32-14.355-32-32v-32h256z m224 672c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h128v32c0 52.935 43.065 96 96 96h192c52.935 0 96-43.065 96-96v-32h128c17.645 0 32 14.355 32 32v640z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M672 672H352c-17.673 0-32 14.327-32 32s14.327 32 32 32h320c17.673 0 32-14.327 32-32s-14.327-32-32-32zM703.965 546.608a32.074 32.074 0 0 0-0.02-0.438c-0.02-0.341-0.047-0.681-0.078-1.021-0.02-0.223-0.039-0.445-0.063-0.667-0.027-0.24-0.059-0.48-0.091-0.719a28.624 28.624 0 0 0-0.134-0.932c-0.022-0.132-0.047-0.264-0.071-0.396a31.747 31.747 0 0 0-0.232-1.207l-0.018-0.076a31.77 31.77 0 0 0-2.494-7.182l-0.023-0.044c-0.211-0.43-0.43-0.856-0.659-1.275-0.132-0.242-0.275-0.48-0.413-0.719-0.109-0.188-0.214-0.379-0.327-0.565a31.793 31.793 0 0 0-5.815-7.043l-0.014-0.013a32.408 32.408 0 0 0-0.967-0.837c-0.09-0.076-0.179-0.153-0.269-0.227a33.464 33.464 0 0 0-0.774-0.609c-0.18-0.139-0.359-0.277-0.542-0.412-0.19-0.139-0.384-0.275-0.578-0.411a32.279 32.279 0 0 0-0.812-0.555 27.471 27.471 0 0 0-0.402-0.257 32.455 32.455 0 0 0-1.04-0.636c-0.044-0.025-0.085-0.053-0.129-0.079l-166.277-96c-15.303-8.837-34.876-3.593-43.712 11.713-8.836 15.305-3.592 34.876 11.713 43.713L552.574 516H352c-17.673 0-32 14.327-32 32s14.327 32 32 32h319.817c0.051 0 0.102 0.005 0.153 0.005 0.194 0 0.387-0.015 0.581-0.019 0.426-0.007 0.851-0.017 1.273-0.041 0.22-0.013 0.439-0.033 0.658-0.05 0.451-0.035 0.9-0.078 1.346-0.131 0.163-0.02 0.326-0.04 0.489-0.062a32.327 32.327 0 0 0 1.775-0.288l0.047-0.008a31.83 31.83 0 0 0 3.956-1.04l0.036-0.012c0.64-0.214 1.272-0.446 1.894-0.698l0.03-0.012a31.823 31.823 0 0 0 3.53-1.697c0.155-0.086 0.307-0.176 0.46-0.265a31.13 31.13 0 0 0 1.108-0.674c0.198-0.126 0.397-0.25 0.592-0.38 0.364-0.243 0.72-0.495 1.072-0.753 0.351-0.255 0.695-0.52 1.036-0.79 0.219-0.174 0.439-0.346 0.653-0.525a32.23 32.23 0 0 0 1.355-1.204c0.256-0.239 0.512-0.477 0.761-0.725a32.12 32.12 0 0 0 1.094-1.151c0.187-0.207 0.368-0.422 0.55-0.635a32.659 32.659 0 0 0 1.166-1.435 32.105 32.105 0 0 0 2.216-3.312c0.019-0.033 0.041-0.064 0.06-0.097 0.029-0.05 0.054-0.102 0.083-0.153 0.22-0.385 0.432-0.774 0.636-1.168l0.129-0.252c0.197-0.39 0.386-0.784 0.568-1.182l0.136-0.3a31.49 31.49 0 0 0 0.995-2.533c0.063-0.183 0.126-0.365 0.186-0.549 0.093-0.286 0.179-0.574 0.264-0.863 0.071-0.24 0.14-0.481 0.205-0.722 0.062-0.231 0.119-0.464 0.176-0.698 0.074-0.303 0.145-0.607 0.21-0.911 0.038-0.177 0.072-0.356 0.107-0.534 0.071-0.363 0.137-0.727 0.195-1.091 0.021-0.131 0.04-0.263 0.059-0.394 0.061-0.414 0.114-0.828 0.158-1.243l0.029-0.292c0.044-0.448 0.078-0.896 0.102-1.344 0.004-0.082 0.009-0.165 0.012-0.248 0.021-0.456 0.033-0.912 0.035-1.368 0-0.051 0.004-0.102 0.004-0.153 0-0.045-0.003-0.088-0.003-0.133 0.001-0.423-0.01-0.843-0.029-1.262z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineShipment.defaultProps = {
  size: 18,
};

IconLineShipment = React.memo ? React.memo(IconLineShipment) : IconLineShipment;

export default IconLineShipment;
