/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineLocation = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M860.462 299.156c-18.925-46.791-46.715-88.76-82.599-124.738-35.881-35.977-77.735-63.839-124.398-82.812C608.412 73.288 560.816 64 512 64s-96.411 9.288-141.465 27.606c-46.664 18.974-88.517 46.835-124.399 82.812-35.883 35.978-63.673 77.947-82.598 124.738C145.265 344.336 136 392.066 136 441.02s9.265 96.683 27.538 141.864c18.925 46.791 46.715 88.759 82.598 124.739l0.011 0.01 241.692 242.331c0.308 0.309 0.625 0.603 0.942 0.897 0.108 0.101 0.213 0.208 0.322 0.307 0.315 0.285 0.638 0.556 0.96 0.827 0.121 0.102 0.238 0.21 0.361 0.311 0.302 0.248 0.612 0.48 0.92 0.715 0.15 0.114 0.295 0.235 0.445 0.345 0.294 0.216 0.594 0.419 0.893 0.625 0.171 0.118 0.339 0.242 0.511 0.357 0.311 0.206 0.629 0.399 0.945 0.593 0.166 0.102 0.327 0.21 0.493 0.31 0.336 0.199 0.679 0.385 1.021 0.571 0.152 0.083 0.299 0.173 0.452 0.253 0.328 0.173 0.661 0.332 0.993 0.493 0.171 0.083 0.339 0.173 0.511 0.253 0.351 0.164 0.707 0.313 1.063 0.464 0.156 0.066 0.31 0.139 0.467 0.204 0.346 0.141 0.698 0.268 1.047 0.397 0.17 0.062 0.338 0.133 0.508 0.193 0.393 0.138 0.79 0.262 1.188 0.385 0.129 0.04 0.256 0.087 0.387 0.125 0.375 0.111 0.753 0.208 1.13 0.307 0.156 0.041 0.31 0.088 0.466 0.126 0.432 0.106 0.865 0.197 1.299 0.285 0.104 0.021 0.205 0.047 0.309 0.068 0.438 0.085 0.878 0.155 1.318 0.223 0.101 0.016 0.202 0.036 0.303 0.051a34.564 34.564 0 0 0 1.631 0.196c0.435 0.042 0.871 0.069 1.306 0.094 0.111 0.006 0.221 0.018 0.332 0.024a34.151 34.151 0 0 0 4.917-0.118l0.013-0.001a33.956 33.956 0 0 0 16.926-6.478c0.064-0.047 0.125-0.1 0.189-0.147 0.395-0.296 0.788-0.597 1.173-0.912 0.107-0.088 0.209-0.183 0.316-0.272a33.89 33.89 0 0 0 1.003-0.864c0.172-0.156 0.338-0.324 0.507-0.483 0.253-0.239 0.51-0.474 0.757-0.721l241.701-242.341c35.885-35.979 63.674-77.948 82.599-124.739C878.736 537.703 888 489.974 888 441.019c0-48.953-9.265-96.683-27.538-141.863zM729.555 659.087l-0.015 0.014L512 877.217 294.47 659.112l-0.01-0.011c-58.101-58.254-90.097-135.702-90.097-218.081s31.997-159.829 90.097-218.083c58.105-58.258 135.362-90.344 217.539-90.344 82.178 0 159.435 32.086 217.541 90.345 58.099 58.253 90.096 135.702 90.096 218.081 0 82.374-31.992 159.817-90.081 218.068z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M512 219.346c-94.239 0-170.909 76.927-170.909 171.483S417.761 562.312 512 562.312c94.24 0 170.909-76.927 170.909-171.483S606.24 219.346 512 219.346z m0 274.373c-56.544 0-102.545-46.156-102.545-102.89S455.456 287.939 512 287.939s102.545 46.156 102.545 102.89S568.544 493.719 512 493.719z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineLocation.defaultProps = {
  size: 18,
};

IconLineLocation = React.memo ? React.memo(IconLineLocation) : IconLineLocation;

export default IconLineLocation;
