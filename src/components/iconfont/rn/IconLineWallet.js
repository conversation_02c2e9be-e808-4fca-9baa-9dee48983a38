/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineWallet = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M864 128H160c-52.93 0-96 43.07-96 96v576c0 52.93 43.07 96 96 96h704c52.93 0 96-43.07 96-96V224c0-52.93-43.07-96-96-96z m32 512H672c-17.645 0-32-14.355-32-32V416c0-17.645 14.355-32 32-32h224v256zM672 320c-52.935 0-96 43.065-96 96v192c0 52.935 43.065 96 96 96h224v96c0 17.65-14.35 32-32 32H160c-17.65 0-32-14.35-32-32V224c0-17.65 14.35-32 32-32h704c17.65 0 32 14.35 32 32v96H672z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M768 560c26.51 0 48-21.49 48-48s-21.49-48-48-48-48 21.49-48 48 21.49 48 48 48z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineWallet.defaultProps = {
  size: 18,
};

IconLineWallet = React.memo ? React.memo(IconLineWallet) : IconLineWallet;

export default IconLineWallet;
