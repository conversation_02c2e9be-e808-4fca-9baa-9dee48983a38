/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineCalendar = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M864 192h-96v-32c0-17.673-14.327-32-32-32s-32 14.327-32 32v32H320v-32c0-17.673-14.327-32-32-32s-32 14.327-32 32v32h-96c-52.935 0-96 43.065-96 96v512c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V288c0-52.935-43.065-96-96-96z m-704 64h96v32c0 17.673 14.327 32 32 32s32-14.327 32-32v-32h384v32c0 17.673 14.327 32 32 32s32-14.327 32-32v-32h96c17.645 0 32 14.355 32 32v96H128v-96c0-17.645 14.355-32 32-32z m704 576H160c-17.645 0-32-14.355-32-32V448h768v352c0 17.645-14.355 32-32 32z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M640 608H384c-17.673 0-32 14.327-32 32s14.327 32 32 32h256c17.673 0 32-14.327 32-32s-14.327-32-32-32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineCalendar.defaultProps = {
  size: 18,
};

IconLineCalendar = React.memo ? React.memo(IconLineCalendar) : IconLineCalendar;

export default IconLineCalendar;
