/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconRefresh = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M892.586667 658.944l-170.368 30.549333a31.402667 31.402667 0 0 0-18.389334 51.2l32.256 38.144a349.013333 349.013333 0 0 1-491.562666-41.6 346.282667 346.282667 0 0 1-81.365334-203.562666h-0.426666c0-1.877333 0-3.84-0.256-5.76a38.656 38.656 0 0 0-44.16-32.512 38.869333 38.869333 0 0 0-32.597334 44.117333c0.085333 0.426667 0.256 0.810667 0.256 1.152a423.850667 423.850667 0 0 0 99.2 246.613333c152.021333 179.882667 420.949333 202.624 600.917334 50.773334l29.397333 34.773333a31.402667 31.402667 0 0 0 53.546667-9.642667l58.624-162.730666a31.36 31.36 0 0 0-35.114667-41.514667z m0 0M128 363.434667l170.325333-30.549334a31.402667 31.402667 0 0 0 18.432-51.2l-47.786666-56.618666 17.066666 20.266666a348.970667 348.970667 0 0 1 491.733334 41.386667 346.197333 346.197333 0 0 1 81.408 206.848h0.426666c0.128 1.152 0 2.432 0.213334 3.584a38.613333 38.613333 0 1 0 76.928-5.973333l-0.085334-1.109334c-0.085333-1.536 0-2.986667-0.256-4.522666l-0.298666-0.981334a423.424 423.424 0 0 0-99.2-247.893333C685.013333 56.746667 415.914667 34.133333 235.989333 186.026667l-30.890666-36.48a31.402667 31.402667 0 0 0-53.546667 9.685333L92.885333 321.92A31.402667 31.402667 0 0 0 128 363.434667z m0 0"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconRefresh.defaultProps = {
  size: 18,
};

IconRefresh = React.memo ? React.memo(IconRefresh) : IconRefresh;

export default IconRefresh;
