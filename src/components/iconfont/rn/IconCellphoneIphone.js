/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconCellphoneIphone = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M682.666667 768H298.666667V170.666667h384m-192 768a64 64 0 0 1-64-64 64 64 0 0 1 64-64 64 64 0 0 1 64 64 64 64 0 0 1-64 64m170.666666-896h-341.333333A106.666667 106.666667 0 0 0 213.333333 149.333333v725.333334A106.666667 106.666667 0 0 0 320 981.333333h341.333333a106.666667 106.666667 0 0 0 106.666667-106.666666v-725.333334A106.666667 106.666667 0 0 0 661.333333 42.666667z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconCellphoneIphone.defaultProps = {
  size: 18,
};

IconCellphoneIphone = React.memo ? React.memo(IconCellphoneIphone) : IconCellphoneIphone;

export default IconCellphoneIphone;
