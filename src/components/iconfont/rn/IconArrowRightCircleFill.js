/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconArrowRightCircleFill = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M512 85.333333c235.52 0 426.666667 191.146667 426.666667 426.666667s-191.146667 426.666667-426.666667 426.666667S85.333333 747.52 85.333333 512 276.48 85.333333 512 85.333333z m0 384H341.333333v85.333334h170.666667v128l170.666667-170.666667-170.666667-170.666667v128z"
        fill={getIconColor(color, 0, '#000000')}
      />
    </Svg>
  );
};

IconArrowRightCircleFill.defaultProps = {
  size: 18,
};

IconArrowRightCircleFill = React.memo ? React.memo(IconArrowRightCircleFill) : IconArrowRightCircleFill;

export default IconArrowRightCircleFill;
