/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLeftBtn = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M512 97.52381c228.912762 0 414.47619 185.563429 414.47619 414.47619s-185.563429 414.47619-414.47619 414.47619S97.52381 740.912762 97.52381 512 283.087238 97.52381 512 97.52381z m0 73.142857C323.486476 170.666667 170.666667 323.486476 170.666667 512s152.81981 341.333333 341.333333 341.333333 341.333333-152.81981 341.333333-341.333333S700.513524 170.666667 512 170.666667z m48.761905 118.954666L612.473905 341.333333l-170.666667 170.666667 170.666667 170.666667L560.761905 734.378667 338.383238 512 560.761905 289.621333z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconLeftBtn.defaultProps = {
  size: 18,
};

IconLeftBtn = React.memo ? React.memo(IconLeftBtn) : IconLeftBtn;

export default IconLeftBtn;
