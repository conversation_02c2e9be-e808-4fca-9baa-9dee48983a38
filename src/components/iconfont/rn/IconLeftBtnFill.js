/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLeftBtnFill = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M512 97.52381c228.912762 0 414.47619 185.563429 414.47619 414.47619s-185.563429 414.47619-414.47619 414.47619S97.52381 740.912762 97.52381 512 283.087238 97.52381 512 97.52381z m48.761905 192.097523L338.383238 512 560.761905 734.378667 612.473905 682.666667l-170.666667-170.666667 170.666667-170.666667L560.761905 289.621333z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconLeftBtnFill.defaultProps = {
  size: 18,
};

IconLeftBtnFill = React.memo ? React.memo(IconLeftBtnFill) : IconLeftBtnFill;

export default IconLeftBtnFill;
