/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconArrowDown2 = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M180.992 330.410667a34.730667 34.730667 0 0 1 49.664 0.085333l306.218667 312.576a36.096 36.096 0 0 1-0.128 50.517333 34.602667 34.602667 0 0 1-49.621334-0.085333L180.949333 380.842667a36.138667 36.138667 0 0 1 0-50.474667z m612.266667 0.085333a34.730667 34.730667 0 0 1 49.706666-0.085333 36.138667 36.138667 0 0 1 0.128 50.517333l-203.306666 207.573333a34.688 34.688 0 0 1-49.621334 0.085334 36.138667 36.138667 0 0 1-0.170666-50.474667l203.306666-207.616z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconArrowDown2.defaultProps = {
  size: 18,
};

IconArrowDown2 = React.memo ? React.memo(IconArrowDown2) : IconArrowDown2;

export default IconArrowDown2;
