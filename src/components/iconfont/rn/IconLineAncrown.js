/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineAncrown = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M935.016 351.844c-20.479-17.255-48.429-21.38-72.947-10.719l-143.824 62.367-91.254-109.815c8.447-15.248 13.815-32.065 15.767-49.734 4.342-38.947-8.397-77.514-34.967-105.842-25.324-26.979-59.517-41.932-96.47-42.101-36.836 0.026-71.079 14.624-96.404 41.106-26.924 28.151-40.049 66.71-35.977 105.792 1.885 18.023 7.286 35.177 15.886 50.72l-91.221 109.789-141.451-61.903c-24.568-10.744-52.552-6.696-73.081 10.568-20.512 17.255-29.363 44.184-23.087 70.278l108.671 451.843C182.262 905.87 210.28 928 242.79 928h538.357c32.51 0 60.528-22.13 68.133-53.792l108.739-452.146c6.26-26.059-2.558-52.963-23.003-70.218zM781.146 858.91l-539.484-0.894-108.671-451.859 1.599-1.332 165.161 72.277c14.051 6.173 30.44 2.244 40.267-9.556l124.675-150.051c10.971-13.216 10.584-32.512-0.926-45.272-9.356-10.373-14.825-22.653-16.272-36.492-1.952-18.833 4.292-37.353 17.164-50.805 12.401-12.972 28.556-19.828 46.528-19.836 17.686 0.084 34.177 7.312 46.41 20.342 12.688 13.536 18.762 32.065 16.676 50.855-1.497 13.451-7.101 25.883-16.205 35.936-11.544 12.752-11.948 32.082-0.96 45.323l124.674 150.026c9.794 11.765 26.15 15.712 40.167 9.59L889.43 404.53l-13.681-31.703 15.263 33.043-109.866 453.04z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M322.766 587.142c-3.846-17.25-20.943-28.117-38.196-24.271-17.25 3.846-28.116 20.946-24.271 38.196l35 157c3.323 14.906 16.542 25.044 31.204 25.044a32.16 32.16 0 0 0 6.993-0.773c17.25-3.846 28.116-20.946 24.271-38.196l-35.001-157z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineAncrown.defaultProps = {
  size: 18,
};

IconLineAncrown = React.memo ? React.memo(IconLineAncrown) : IconLineAncrown;

export default IconLineAncrown;
