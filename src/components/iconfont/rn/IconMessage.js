/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconMessage = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M128 0C57.4 0 0 57.4 0 128v576c0 70.6 57.4 128 128 128h192v160c0 12.2 6.8 23.2 17.6 28.6s23.8 4.2 33.6-3L618.6 832H896c70.6 0 128-57.4 128-128V128c0-70.6-57.4-128-128-128H128z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconMessage.defaultProps = {
  size: 18,
};

IconMessage = React.memo ? React.memo(IconMessage) : IconMessage;

export default IconMessage;
