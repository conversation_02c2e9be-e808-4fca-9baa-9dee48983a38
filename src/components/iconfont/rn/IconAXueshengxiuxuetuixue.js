/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconAXueshengxiuxuetuixue = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M460.597968 502.674158A251.321484 251.321484 0 0 1 212.894415 247.765931 251.321484 251.321484 0 0 1 460.597968 0a251.383862 251.383862 0 0 1 247.76593 254.877038 251.383862 251.383862 0 0 1-247.76593 247.79712z m0-464.280419v38.36255a174.658763 174.658763 0 0 0-171.539857 177.559346 174.658763 174.658763 0 0 0 171.539857 171.539856 174.658763 174.658763 0 0 0 171.539856-177.590535 174.658763 174.658763 0 0 0-171.539856-171.539857z"
        fill={getIconColor(color, 0, '#2272D4')}
      />
      <Path
        d="M536.325017 937.855179H149.36229a26.479516 26.479516 0 0 1-25.886924-25.575033 339.555349 339.555349 0 0 1 149.177297-282.572927 25.949302 25.949302 0 0 1 27.758268-0.873294 319.469591 319.469591 0 0 0 320.374073 0 26.323571 26.323571 0 0 1 27.820646 0.810916 327.235668 327.235668 0 0 1 28.444427 23.267042 265.107051 265.107051 0 0 1 84.584744-26.292382 406.455893 406.455893 0 0 0-70.050639-60.537974 103.516506 103.516506 0 0 0-109.380051-3.524365 242.245466 242.245466 0 0 1-243.087571 0 103.079859 103.079859 0 0 0-109.442428 3.617932A417.278498 417.278498 0 0 0 46.999779 915.274296a102.70559 102.70559 0 0 0 101.801108 99.305983h414.315537a256.904327 256.904327 0 0 1-26.791407-76.7251z"
        fill={getIconColor(color, 1, '#2272D4')}
      />
      <Path
        d="M877.970033 880.685623l89.668561-89.69975a31.189065 31.189065 0 0 0 0-44.101338 31.189065 31.189065 0 0 0-44.101337 0l-89.668562 89.699751-89.730939-89.73094a31.189065 31.189065 0 0 0-44.101338 0 31.189065 31.189065 0 0 0 0 44.101338l89.73094 89.730939-89.73094 89.699751a31.189065 31.189065 0 0 0 0 44.101337 30.970741 30.970741 0 0 0 22.050669 9.138396 30.970741 30.970741 0 0 0 22.050669-9.138396l89.730939-89.69975 89.668562 89.668561a30.970741 30.970741 0 0 0 22.050669 9.138396 30.970741 30.970741 0 0 0 22.050668-9.138396 31.189065 31.189065 0 0 0 0-44.101337z"
        fill={getIconColor(color, 2, '#A2C4FD')}
      />
    </Svg>
  );
};

IconAXueshengxiuxuetuixue.defaultProps = {
  size: 18,
};

IconAXueshengxiuxuetuixue = React.memo ? React.memo(IconAXueshengxiuxuetuixue) : IconAXueshengxiuxuetuixue;

export default IconAXueshengxiuxuetuixue;
