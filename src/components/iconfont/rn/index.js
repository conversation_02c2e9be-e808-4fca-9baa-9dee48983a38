/* eslint-disable */

import React from 'react';

import IconArrowRightCircleLine from './IconArrowRightCircleLine';
import IconArrowRightCircleFill from './IconArrowRightCircleFill';
import IconImage from './IconImage';
import IconPdf from './IconPdf';
import IconZhuceshibai from './IconZhuceshibai';
import IconBiyejieye from './IconBiyejieye';
import IconXunliandakaXuanzhong from './IconXunliandakaXuanzhong';
import IconAXueshengxiuxuetuixue from './IconAXueshengxiuxuetuixue';
import IconDuicuo2 from './IconDuicuo2';
import IconDuicuo1 from './IconDuicuo1';
import IconAShoucangYishoucang from './IconAShoucangYishoucang';
import IconWeishoucang from './IconWeishoucang';
import IconMimi from './IconMimi';
import <PERSON>con<PERSON><PERSON><PERSON><PERSON> from './IconMimimiji';
import <PERSON>con<PERSON>ai<PERSON> from './IconPaihang';
import IconShoucang from './IconShoucang';
import IconJiaotongtubiaoYouzhuan from './IconJiaotongtubiaoYouzhuan';
import IconLiuchengSuijiqi1 from './IconLiuchengSuijiqi1';
import IconTikuzongliang from './IconTikuzongliang';
import IconIconZhuanxianglianxi from './IconIconZhuanxianglianxi';
import IconVip1 from './IconVip1';
import IconVip from './IconVip';
import IconLineQrcode from './IconLineQrcode';
import IconLinePicture from './IconLinePicture';
import IconLineRelation from './IconLineRelation';
import IconLineReduceuser1 from './IconLineReduceuser1';
import IconLinePurchase from './IconLinePurchase';
import IconLineMatting from './IconLineMatting';
import IconLineOrganizational from './IconLineOrganizational';
import IconLineRole from './IconLineRole';
import IconLineReport from './IconLineReport';
import IconLineService from './IconLineService';
import IconLinePhone from './IconLinePhone';
import IconLineShoppingcart from './IconLineShoppingcart';
import IconLineShop from './IconLineShop';
import IconLineSee from './IconLineSee';
import IconLineSystem from './IconLineSystem';
import IconLineShipment from './IconLineShipment';
import IconLineRanking from './IconLineRanking';
import IconLineTemplate from './IconLineTemplate';
import IconLineTime from './IconLineTime';
import IconLineSend from './IconLineSend';
import IconLineNews from './IconLineNews';
import IconLineTeam1 from './IconLineTeam1';
import IconLineSafe from './IconLineSafe';
import IconLineStatistics from './IconLineStatistics';
import IconLineUserconfirmation1 from './IconLineUserconfirmation1';
import IconLineVideo from './IconLineVideo';
import IconLineWallet from './IconLineWallet';
import IconLineWorkset from './IconLineWorkset';
import IconLineShare from './IconLineShare';
import IconLineTransaction from './IconLineTransaction';
import IconLogoMoment from './IconLogoMoment';
import IconLineSignout from './IconLineSignout';
import IconLogoWechat from './IconLogoWechat';
import IconLineTask from './IconLineTask';
import IconLineAddcommodity from './IconLineAddcommodity';
import IconLineAccurate from './IconLineAccurate';
import IconLineBriefcase from './IconLineBriefcase';
import IconLineCalendar from './IconLineCalendar';
import IconLineAchievement from './IconLineAchievement';
import IconLineCheck1 from './IconLineCheck1';
import IconLineApplication from './IconLineApplication';
import IconLineBusinesscard from './IconLineBusinesscard';
import IconLineCoin from './IconLineCoin';
import IconLineCard from './IconLineCard';
import IconLineApplets from './IconLineApplets';
import IconLineCommodity from './IconLineCommodity';
import IconLineAssembly from './IconLineAssembly';
import IconLineCompany from './IconLineCompany';
import IconLineAncrown from './IconLineAncrown';
import IconLineCode from './IconLineCode';
import IconLineCollection from './IconLineCollection';
import IconLineContacts from './IconLineContacts';
import IconLineDiscount from './IconLineDiscount';
import IconLineDeleteuser from './IconLineDeleteuser';
import IconLineDelete from './IconLineDelete';
import IconLineCooperation from './IconLineCooperation';
import IconLineCollect from './IconLineCollect';
import IconLineDownload from './IconLineDownload';
import IconLineExamine1 from './IconLineExamine1';
import IconLineExpress from './IconLineExpress';
import IconLineEye from './IconLineEye';
import IconLineHealthreport from './IconLineHealthreport';
import IconLineFile from './IconLineFile';
import IconLineLocation from './IconLineLocation';
import IconLineList from './IconLineList';
import IconLineOpen from './IconLineOpen';
import IconLineFind from './IconLineFind';
import IconLineMessage from './IconLineMessage';
import IconLinePc from './IconLinePc';
import IconLinePartake from './IconLinePartake';
import IconLineLink from './IconLineLink';
import IconLineLabel from './IconLineLabel';
import IconLineOrder from './IconLineOrder';
import IconLineNotice from './IconLineNotice';
import IconLineHome from './IconLineHome';
import IconLineOverstep from './IconLineOverstep';
import IconRefresh from './IconRefresh';
import IconIdentityCard from './IconIdentityCard';
import IconLineControl from './IconLineControl';
import IconLineReward from './IconLineReward';
import IconLineMe from './IconLineMe';
import IconLineTeam from './IconLineTeam';
import IconLineUserconfirmation from './IconLineUserconfirmation';
import IconLineUser from './IconLineUser';
import IconLineReduceuser from './IconLineReduceuser';
import IconLineScan from './IconLineScan';
import IconLineCheck from './IconLineCheck';
import IconLineSchedule from './IconLineSchedule';
import IconPaySuccessful from './IconPaySuccessful';
import IconDiscount from './IconDiscount';
import IconBuy from './IconBuy';
import IconTicket from './IconTicket';
import IconSend from './IconSend';
import IconGantanhao from './IconGantanhao';
import IconUserPlus from './IconUserPlus';
import IconHospitalUser from './IconHospitalUser';
import IconPersonCirclePlus from './IconPersonCirclePlus';
import IconNfc from './IconNfc';
import IconAdd from './IconAdd';
import IconManagerexam from './IconManagerexam';
import IconPushtype from './IconPushtype';
import IconGdsbrand from './IconGdsbrand';
import IconLineExamine from './IconLineExamine';
import IconEngexam from './IconEngexam';
import IconFinexam from './IconFinexam';
import IconPurexam from './IconPurexam';
import IconChart from './IconChart';
import IconCloseSquare from './IconCloseSquare';
import IconLogout from './IconLogout';
import IconUserGear from './IconUserGear';
import IconComments from './IconComments';
import IconHouse from './IconHouse';
import IconCommentDots from './IconCommentDots';
import IconCalculator from './IconCalculator';
import IconCellphoneIphone from './IconCellphoneIphone';
import IconCommentAccountOutline from './IconCommentAccountOutline';
import IconCommentCheckOutline from './IconCommentCheckOutline';
import IconCommentMultipleOutline from './IconCommentMultipleOutline';
import IconCommentTextOutline from './IconCommentTextOutline';
import IconAddUser from './IconAddUser';
import IconSquarePhone from './IconSquarePhone';
import IconMessage from './IconMessage';
import IconIdCard from './IconIdCard';
import IconMobileRetro from './IconMobileRetro';
import IconList from './IconList';
import IconProfile from './IconProfile';
import IconAddressbook from './IconAddressbook';
import IconActivity from './IconActivity';
import IconFriendadd from './IconFriendadd';
import IconBad from './IconBad';
import IconPeoplelist from './IconPeoplelist';
import IconExit from './IconExit';
import IconLocation from './IconLocation';
import IconArrowRight2 from './IconArrowRight2';
import IconArrowRightSquare from './IconArrowRightSquare';
import IconArrowRight from './IconArrowRight';
import IconCategory from './IconCategory';
import IconFilter from './IconFilter';
import IconSetting from './IconSetting';
import IconArrowDown2 from './IconArrowDown2';
import IconArrowDown3 from './IconArrowDown3';
import IconArrowDown from './IconArrowDown';
import IconArrowDownCircle from './IconArrowDownCircle';
import IconArrowLeft2 from './IconArrowLeft2';
import IconHome1 from './IconHome1';
import IconArrowLeft from './IconArrowLeft';
import IconHome from './IconHome';
import Icon008Man from './Icon008Man';
import Icon014Woman from './Icon014Woman';
import IconHome1 from './IconHome1';
import IconHome from './IconHome';
import IconLeftArrow from './IconLeftArrow';
import IconLeftBtn from './IconLeftBtn';
import IconLeftBtnFill from './IconLeftBtnFill';
import IconHomeFill from './IconHomeFill';
import IconSearch from './IconSearch';
import IconMoneyCheck from './IconMoneyCheck';
import IconIdCardClip from './IconIdCardClip';
import IconUserLarge from './IconUserLarge';
import IconPhone from './IconPhone';
import IconExit from './IconExit';
import IconJiantou from './IconJiantou';
import IconIconKeyboard from './IconIconKeyboard';
import IconTuichu from './IconTuichu';
import IconDianpuzhuye from './IconDianpuzhuye';
import IconDingdanguanli from './IconDingdanguanli';
import IconXitongguanli from './IconXitongguanli';
import IconPassword from './IconPassword';
import IconProfile from './IconProfile';
import IconAShieldDone from './IconAShieldDone';
import IconCalling from './IconCalling';
import IconWallet from './IconWallet';
import IconWork from './IconWork';
export { default as IconArrowRightCircleLine } from './IconArrowRightCircleLine';
export { default as IconArrowRightCircleFill } from './IconArrowRightCircleFill';
export { default as IconImage } from './IconImage';
export { default as IconPdf } from './IconPdf';
export { default as IconZhuceshibai } from './IconZhuceshibai';
export { default as IconBiyejieye } from './IconBiyejieye';
export { default as IconXunliandakaXuanzhong } from './IconXunliandakaXuanzhong';
export { default as IconAXueshengxiuxuetuixue } from './IconAXueshengxiuxuetuixue';
export { default as IconDuicuo2 } from './IconDuicuo2';
export { default as IconDuicuo1 } from './IconDuicuo1';
export { default as IconAShoucangYishoucang } from './IconAShoucangYishoucang';
export { default as IconWeishoucang } from './IconWeishoucang';
export { default as IconMimi } from './IconMimi';
export { default as IconMimimiji } from './IconMimimiji';
export { default as IconPaihang } from './IconPaihang';
export { default as IconShoucang } from './IconShoucang';
export { default as IconJiaotongtubiaoYouzhuan } from './IconJiaotongtubiaoYouzhuan';
export { default as IconLiuchengSuijiqi1 } from './IconLiuchengSuijiqi1';
export { default as IconTikuzongliang } from './IconTikuzongliang';
export { default as IconIconZhuanxianglianxi } from './IconIconZhuanxianglianxi';
export { default as IconVip1 } from './IconVip1';
export { default as IconVip } from './IconVip';
export { default as IconLineQrcode } from './IconLineQrcode';
export { default as IconLinePicture } from './IconLinePicture';
export { default as IconLineRelation } from './IconLineRelation';
export { default as IconLineReduceuser1 } from './IconLineReduceuser1';
export { default as IconLinePurchase } from './IconLinePurchase';
export { default as IconLineMatting } from './IconLineMatting';
export { default as IconLineOrganizational } from './IconLineOrganizational';
export { default as IconLineRole } from './IconLineRole';
export { default as IconLineReport } from './IconLineReport';
export { default as IconLineService } from './IconLineService';
export { default as IconLinePhone } from './IconLinePhone';
export { default as IconLineShoppingcart } from './IconLineShoppingcart';
export { default as IconLineShop } from './IconLineShop';
export { default as IconLineSee } from './IconLineSee';
export { default as IconLineSystem } from './IconLineSystem';
export { default as IconLineShipment } from './IconLineShipment';
export { default as IconLineRanking } from './IconLineRanking';
export { default as IconLineTemplate } from './IconLineTemplate';
export { default as IconLineTime } from './IconLineTime';
export { default as IconLineSend } from './IconLineSend';
export { default as IconLineNews } from './IconLineNews';
export { default as IconLineTeam1 } from './IconLineTeam1';
export { default as IconLineSafe } from './IconLineSafe';
export { default as IconLineStatistics } from './IconLineStatistics';
export { default as IconLineUserconfirmation1 } from './IconLineUserconfirmation1';
export { default as IconLineVideo } from './IconLineVideo';
export { default as IconLineWallet } from './IconLineWallet';
export { default as IconLineWorkset } from './IconLineWorkset';
export { default as IconLineShare } from './IconLineShare';
export { default as IconLineTransaction } from './IconLineTransaction';
export { default as IconLogoMoment } from './IconLogoMoment';
export { default as IconLineSignout } from './IconLineSignout';
export { default as IconLogoWechat } from './IconLogoWechat';
export { default as IconLineTask } from './IconLineTask';
export { default as IconLineAddcommodity } from './IconLineAddcommodity';
export { default as IconLineAccurate } from './IconLineAccurate';
export { default as IconLineBriefcase } from './IconLineBriefcase';
export { default as IconLineCalendar } from './IconLineCalendar';
export { default as IconLineAchievement } from './IconLineAchievement';
export { default as IconLineCheck1 } from './IconLineCheck1';
export { default as IconLineApplication } from './IconLineApplication';
export { default as IconLineBusinesscard } from './IconLineBusinesscard';
export { default as IconLineCoin } from './IconLineCoin';
export { default as IconLineCard } from './IconLineCard';
export { default as IconLineApplets } from './IconLineApplets';
export { default as IconLineCommodity } from './IconLineCommodity';
export { default as IconLineAssembly } from './IconLineAssembly';
export { default as IconLineCompany } from './IconLineCompany';
export { default as IconLineAncrown } from './IconLineAncrown';
export { default as IconLineCode } from './IconLineCode';
export { default as IconLineCollection } from './IconLineCollection';
export { default as IconLineContacts } from './IconLineContacts';
export { default as IconLineDiscount } from './IconLineDiscount';
export { default as IconLineDeleteuser } from './IconLineDeleteuser';
export { default as IconLineDelete } from './IconLineDelete';
export { default as IconLineCooperation } from './IconLineCooperation';
export { default as IconLineCollect } from './IconLineCollect';
export { default as IconLineDownload } from './IconLineDownload';
export { default as IconLineExamine1 } from './IconLineExamine1';
export { default as IconLineExpress } from './IconLineExpress';
export { default as IconLineEye } from './IconLineEye';
export { default as IconLineHealthreport } from './IconLineHealthreport';
export { default as IconLineFile } from './IconLineFile';
export { default as IconLineLocation } from './IconLineLocation';
export { default as IconLineList } from './IconLineList';
export { default as IconLineOpen } from './IconLineOpen';
export { default as IconLineFind } from './IconLineFind';
export { default as IconLineMessage } from './IconLineMessage';
export { default as IconLinePc } from './IconLinePc';
export { default as IconLinePartake } from './IconLinePartake';
export { default as IconLineLink } from './IconLineLink';
export { default as IconLineLabel } from './IconLineLabel';
export { default as IconLineOrder } from './IconLineOrder';
export { default as IconLineNotice } from './IconLineNotice';
export { default as IconLineHome } from './IconLineHome';
export { default as IconLineOverstep } from './IconLineOverstep';
export { default as IconRefresh } from './IconRefresh';
export { default as IconIdentityCard } from './IconIdentityCard';
export { default as IconLineControl } from './IconLineControl';
export { default as IconLineReward } from './IconLineReward';
export { default as IconLineMe } from './IconLineMe';
export { default as IconLineTeam } from './IconLineTeam';
export { default as IconLineUserconfirmation } from './IconLineUserconfirmation';
export { default as IconLineUser } from './IconLineUser';
export { default as IconLineReduceuser } from './IconLineReduceuser';
export { default as IconLineScan } from './IconLineScan';
export { default as IconLineCheck } from './IconLineCheck';
export { default as IconLineSchedule } from './IconLineSchedule';
export { default as IconPaySuccessful } from './IconPaySuccessful';
export { default as IconDiscount } from './IconDiscount';
export { default as IconBuy } from './IconBuy';
export { default as IconTicket } from './IconTicket';
export { default as IconSend } from './IconSend';
export { default as IconGantanhao } from './IconGantanhao';
export { default as IconUserPlus } from './IconUserPlus';
export { default as IconHospitalUser } from './IconHospitalUser';
export { default as IconPersonCirclePlus } from './IconPersonCirclePlus';
export { default as IconNfc } from './IconNfc';
export { default as IconAdd } from './IconAdd';
export { default as IconManagerexam } from './IconManagerexam';
export { default as IconPushtype } from './IconPushtype';
export { default as IconGdsbrand } from './IconGdsbrand';
export { default as IconLineExamine } from './IconLineExamine';
export { default as IconEngexam } from './IconEngexam';
export { default as IconFinexam } from './IconFinexam';
export { default as IconPurexam } from './IconPurexam';
export { default as IconChart } from './IconChart';
export { default as IconCloseSquare } from './IconCloseSquare';
export { default as IconLogout } from './IconLogout';
export { default as IconUserGear } from './IconUserGear';
export { default as IconComments } from './IconComments';
export { default as IconHouse } from './IconHouse';
export { default as IconCommentDots } from './IconCommentDots';
export { default as IconCalculator } from './IconCalculator';
export { default as IconCellphoneIphone } from './IconCellphoneIphone';
export { default as IconCommentAccountOutline } from './IconCommentAccountOutline';
export { default as IconCommentCheckOutline } from './IconCommentCheckOutline';
export { default as IconCommentMultipleOutline } from './IconCommentMultipleOutline';
export { default as IconCommentTextOutline } from './IconCommentTextOutline';
export { default as IconAddUser } from './IconAddUser';
export { default as IconSquarePhone } from './IconSquarePhone';
export { default as IconMessage } from './IconMessage';
export { default as IconIdCard } from './IconIdCard';
export { default as IconMobileRetro } from './IconMobileRetro';
export { default as IconList } from './IconList';
export { default as IconProfile } from './IconProfile';
export { default as IconAddressbook } from './IconAddressbook';
export { default as IconActivity } from './IconActivity';
export { default as IconFriendadd } from './IconFriendadd';
export { default as IconBad } from './IconBad';
export { default as IconPeoplelist } from './IconPeoplelist';
export { default as IconExit } from './IconExit';
export { default as IconLocation } from './IconLocation';
export { default as IconArrowRight2 } from './IconArrowRight2';
export { default as IconArrowRightSquare } from './IconArrowRightSquare';
export { default as IconArrowRight } from './IconArrowRight';
export { default as IconCategory } from './IconCategory';
export { default as IconFilter } from './IconFilter';
export { default as IconSetting } from './IconSetting';
export { default as IconArrowDown2 } from './IconArrowDown2';
export { default as IconArrowDown3 } from './IconArrowDown3';
export { default as IconArrowDown } from './IconArrowDown';
export { default as IconArrowDownCircle } from './IconArrowDownCircle';
export { default as IconArrowLeft2 } from './IconArrowLeft2';
export { default as IconHome1 } from './IconHome1';
export { default as IconArrowLeft } from './IconArrowLeft';
export { default as IconHome } from './IconHome';
export { default as Icon008Man } from './Icon008Man';
export { default as Icon014Woman } from './Icon014Woman';
export { default as IconHome1 } from './IconHome1';
export { default as IconHome } from './IconHome';
export { default as IconLeftArrow } from './IconLeftArrow';
export { default as IconLeftBtn } from './IconLeftBtn';
export { default as IconLeftBtnFill } from './IconLeftBtnFill';
export { default as IconHomeFill } from './IconHomeFill';
export { default as IconSearch } from './IconSearch';
export { default as IconMoneyCheck } from './IconMoneyCheck';
export { default as IconIdCardClip } from './IconIdCardClip';
export { default as IconUserLarge } from './IconUserLarge';
export { default as IconPhone } from './IconPhone';
export { default as IconExit } from './IconExit';
export { default as IconJiantou } from './IconJiantou';
export { default as IconIconKeyboard } from './IconIconKeyboard';
export { default as IconTuichu } from './IconTuichu';
export { default as IconDianpuzhuye } from './IconDianpuzhuye';
export { default as IconDingdanguanli } from './IconDingdanguanli';
export { default as IconXitongguanli } from './IconXitongguanli';
export { default as IconPassword } from './IconPassword';
export { default as IconProfile } from './IconProfile';
export { default as IconAShieldDone } from './IconAShieldDone';
export { default as IconCalling } from './IconCalling';
export { default as IconWallet } from './IconWallet';
export { default as IconWork } from './IconWork';

let IconFont = ({ name, ...rest }) => {
  switch (name) {
    case 'arrow-right-circle-line':
      return <IconArrowRightCircleLine key="1" {...rest} />;
    case 'arrow-right-circle-fill':
      return <IconArrowRightCircleFill key="2" {...rest} />;
    case 'image':
      return <IconImage key="3" {...rest} />;
    case 'pdf':
      return <IconPdf key="4" {...rest} />;
    case 'zhuceshibai':
      return <IconZhuceshibai key="5" {...rest} />;
    case 'biyejieye':
      return <IconBiyejieye key="6" {...rest} />;
    case 'xunliandaka-xuanzhong':
      return <IconXunliandakaXuanzhong key="7" {...rest} />;
    case 'a-xueshengxiuxuetuixue':
      return <IconAXueshengxiuxuetuixue key="8" {...rest} />;
    case 'duicuo2':
      return <IconDuicuo2 key="9" {...rest} />;
    case 'duicuo1':
      return <IconDuicuo1 key="10" {...rest} />;
    case 'a-shoucang-yishoucang':
      return <IconAShoucangYishoucang key="11" {...rest} />;
    case 'weishoucang':
      return <IconWeishoucang key="12" {...rest} />;
    case 'mimi':
      return <IconMimi key="13" {...rest} />;
    case 'mimimiji':
      return <IconMimimiji key="14" {...rest} />;
    case 'paihang':
      return <IconPaihang key="15" {...rest} />;
    case 'shoucang':
      return <IconShoucang key="16" {...rest} />;
    case 'jiaotongtubiao-youzhuan':
      return <IconJiaotongtubiaoYouzhuan key="17" {...rest} />;
    case 'liucheng-suijiqi1':
      return <IconLiuchengSuijiqi1 key="18" {...rest} />;
    case 'tikuzongliang':
      return <IconTikuzongliang key="19" {...rest} />;
    case 'icon-zhuanxianglianxi':
      return <IconIconZhuanxianglianxi key="20" {...rest} />;
    case 'VIP1':
      return <IconVip1 key="21" {...rest} />;
    case 'VIP':
      return <IconVip key="22" {...rest} />;
    case 'line-qrcode':
      return <IconLineQrcode key="23" {...rest} />;
    case 'line-picture':
      return <IconLinePicture key="24" {...rest} />;
    case 'line-relation':
      return <IconLineRelation key="25" {...rest} />;
    case 'line-reduceuser1':
      return <IconLineReduceuser1 key="26" {...rest} />;
    case 'line-purchase':
      return <IconLinePurchase key="27" {...rest} />;
    case 'line-matting':
      return <IconLineMatting key="28" {...rest} />;
    case 'line-organizational':
      return <IconLineOrganizational key="29" {...rest} />;
    case 'line-role':
      return <IconLineRole key="30" {...rest} />;
    case 'line-report':
      return <IconLineReport key="31" {...rest} />;
    case 'line-service':
      return <IconLineService key="32" {...rest} />;
    case 'line-phone':
      return <IconLinePhone key="33" {...rest} />;
    case 'line-shoppingcart':
      return <IconLineShoppingcart key="34" {...rest} />;
    case 'line-shop':
      return <IconLineShop key="35" {...rest} />;
    case 'line-see':
      return <IconLineSee key="36" {...rest} />;
    case 'line-system':
      return <IconLineSystem key="37" {...rest} />;
    case 'line-shipment':
      return <IconLineShipment key="38" {...rest} />;
    case 'line-ranking':
      return <IconLineRanking key="39" {...rest} />;
    case 'line-template':
      return <IconLineTemplate key="40" {...rest} />;
    case 'line-time':
      return <IconLineTime key="41" {...rest} />;
    case 'line-send':
      return <IconLineSend key="42" {...rest} />;
    case 'line-news':
      return <IconLineNews key="43" {...rest} />;
    case 'line-team1':
      return <IconLineTeam1 key="44" {...rest} />;
    case 'line-safe':
      return <IconLineSafe key="45" {...rest} />;
    case 'line-statistics':
      return <IconLineStatistics key="46" {...rest} />;
    case 'line-userconfirmation1':
      return <IconLineUserconfirmation1 key="47" {...rest} />;
    case 'line-video':
      return <IconLineVideo key="48" {...rest} />;
    case 'line-wallet':
      return <IconLineWallet key="49" {...rest} />;
    case 'line-workset':
      return <IconLineWorkset key="50" {...rest} />;
    case 'line-share':
      return <IconLineShare key="51" {...rest} />;
    case 'line-transaction':
      return <IconLineTransaction key="52" {...rest} />;
    case 'logo-moment':
      return <IconLogoMoment key="53" {...rest} />;
    case 'line-signout':
      return <IconLineSignout key="54" {...rest} />;
    case 'logo-wechat':
      return <IconLogoWechat key="55" {...rest} />;
    case 'line-task':
      return <IconLineTask key="56" {...rest} />;
    case 'line-addcommodity':
      return <IconLineAddcommodity key="57" {...rest} />;
    case 'line-accurate':
      return <IconLineAccurate key="58" {...rest} />;
    case 'line-briefcase':
      return <IconLineBriefcase key="59" {...rest} />;
    case 'line-calendar':
      return <IconLineCalendar key="60" {...rest} />;
    case 'line-achievement':
      return <IconLineAchievement key="61" {...rest} />;
    case 'line-check1':
      return <IconLineCheck1 key="62" {...rest} />;
    case 'line-application':
      return <IconLineApplication key="63" {...rest} />;
    case 'line-businesscard':
      return <IconLineBusinesscard key="64" {...rest} />;
    case 'line-coin':
      return <IconLineCoin key="65" {...rest} />;
    case 'line-card':
      return <IconLineCard key="66" {...rest} />;
    case 'line-applets':
      return <IconLineApplets key="67" {...rest} />;
    case 'line-commodity':
      return <IconLineCommodity key="68" {...rest} />;
    case 'line-assembly':
      return <IconLineAssembly key="69" {...rest} />;
    case 'line-company':
      return <IconLineCompany key="70" {...rest} />;
    case 'line-ancrown':
      return <IconLineAncrown key="71" {...rest} />;
    case 'line-code':
      return <IconLineCode key="72" {...rest} />;
    case 'line-collection':
      return <IconLineCollection key="73" {...rest} />;
    case 'line-contacts':
      return <IconLineContacts key="74" {...rest} />;
    case 'line-discount':
      return <IconLineDiscount key="75" {...rest} />;
    case 'line-deleteuser':
      return <IconLineDeleteuser key="76" {...rest} />;
    case 'line-delete':
      return <IconLineDelete key="77" {...rest} />;
    case 'line-cooperation':
      return <IconLineCooperation key="78" {...rest} />;
    case 'line-collect':
      return <IconLineCollect key="79" {...rest} />;
    case 'line-download':
      return <IconLineDownload key="80" {...rest} />;
    case 'line-examine1':
      return <IconLineExamine1 key="81" {...rest} />;
    case 'line-express':
      return <IconLineExpress key="82" {...rest} />;
    case 'line-eye':
      return <IconLineEye key="83" {...rest} />;
    case 'line-healthreport':
      return <IconLineHealthreport key="84" {...rest} />;
    case 'line-file':
      return <IconLineFile key="85" {...rest} />;
    case 'line-location':
      return <IconLineLocation key="86" {...rest} />;
    case 'line-list':
      return <IconLineList key="87" {...rest} />;
    case 'line-open':
      return <IconLineOpen key="88" {...rest} />;
    case 'line-find':
      return <IconLineFind key="89" {...rest} />;
    case 'line-message':
      return <IconLineMessage key="90" {...rest} />;
    case 'line-pc':
      return <IconLinePc key="91" {...rest} />;
    case 'line-partake':
      return <IconLinePartake key="92" {...rest} />;
    case 'line-link':
      return <IconLineLink key="93" {...rest} />;
    case 'line-label':
      return <IconLineLabel key="94" {...rest} />;
    case 'line-order':
      return <IconLineOrder key="95" {...rest} />;
    case 'line-notice':
      return <IconLineNotice key="96" {...rest} />;
    case 'line-home':
      return <IconLineHome key="97" {...rest} />;
    case 'line-overstep':
      return <IconLineOverstep key="98" {...rest} />;
    case 'refresh':
      return <IconRefresh key="99" {...rest} />;
    case 'identity-card':
      return <IconIdentityCard key="100" {...rest} />;
    case 'line-control':
      return <IconLineControl key="101" {...rest} />;
    case 'line-reward':
      return <IconLineReward key="102" {...rest} />;
    case 'line-me':
      return <IconLineMe key="103" {...rest} />;
    case 'line-team':
      return <IconLineTeam key="104" {...rest} />;
    case 'line-userconfirmation':
      return <IconLineUserconfirmation key="105" {...rest} />;
    case 'line-user':
      return <IconLineUser key="106" {...rest} />;
    case 'line-reduceuser':
      return <IconLineReduceuser key="107" {...rest} />;
    case 'line-scan':
      return <IconLineScan key="108" {...rest} />;
    case 'line-check':
      return <IconLineCheck key="109" {...rest} />;
    case 'line-schedule':
      return <IconLineSchedule key="110" {...rest} />;
    case 'pay-successful':
      return <IconPaySuccessful key="111" {...rest} />;
    case 'Discount':
      return <IconDiscount key="112" {...rest} />;
    case 'Buy':
      return <IconBuy key="113" {...rest} />;
    case 'Ticket':
      return <IconTicket key="114" {...rest} />;
    case 'Send':
      return <IconSend key="115" {...rest} />;
    case 'gantanhao':
      return <IconGantanhao key="116" {...rest} />;
    case 'user-plus':
      return <IconUserPlus key="117" {...rest} />;
    case 'hospital-user':
      return <IconHospitalUser key="118" {...rest} />;
    case 'person-circle-plus':
      return <IconPersonCirclePlus key="119" {...rest} />;
    case 'nfc':
      return <IconNfc key="120" {...rest} />;
    case 'add':
      return <IconAdd key="121" {...rest} />;
    case 'managerexam':
      return <IconManagerexam key="122" {...rest} />;
    case 'pushtype':
      return <IconPushtype key="123" {...rest} />;
    case 'gdsbrand':
      return <IconGdsbrand key="124" {...rest} />;
    case 'line-examine':
      return <IconLineExamine key="125" {...rest} />;
    case 'engexam':
      return <IconEngexam key="126" {...rest} />;
    case 'finexam':
      return <IconFinexam key="127" {...rest} />;
    case 'purexam':
      return <IconPurexam key="128" {...rest} />;
    case 'Chart':
      return <IconChart key="129" {...rest} />;
    case 'CloseSquare':
      return <IconCloseSquare key="130" {...rest} />;
    case 'Logout':
      return <IconLogout key="131" {...rest} />;
    case 'user-gear':
      return <IconUserGear key="132" {...rest} />;
    case 'comments':
      return <IconComments key="133" {...rest} />;
    case 'house':
      return <IconHouse key="134" {...rest} />;
    case 'comment-dots':
      return <IconCommentDots key="135" {...rest} />;
    case 'calculator':
      return <IconCalculator key="136" {...rest} />;
    case 'cellphone-iphone':
      return <IconCellphoneIphone key="137" {...rest} />;
    case 'comment-account-outline':
      return <IconCommentAccountOutline key="138" {...rest} />;
    case 'comment-check-outline':
      return <IconCommentCheckOutline key="139" {...rest} />;
    case 'comment-multiple-outline':
      return <IconCommentMultipleOutline key="140" {...rest} />;
    case 'comment-text-outline':
      return <IconCommentTextOutline key="141" {...rest} />;
    case 'AddUser':
      return <IconAddUser key="142" {...rest} />;
    case 'square-phone':
      return <IconSquarePhone key="143" {...rest} />;
    case 'message':
      return <IconMessage key="144" {...rest} />;
    case 'id-card':
      return <IconIdCard key="145" {...rest} />;
    case 'mobile-retro':
      return <IconMobileRetro key="146" {...rest} />;
    case 'list':
      return <IconList key="147" {...rest} />;
    case 'profile':
      return <IconProfile key="148" {...rest} />;
    case 'addressbook':
      return <IconAddressbook key="149" {...rest} />;
    case 'activity':
      return <IconActivity key="150" {...rest} />;
    case 'friendadd':
      return <IconFriendadd key="151" {...rest} />;
    case 'bad':
      return <IconBad key="152" {...rest} />;
    case 'peoplelist':
      return <IconPeoplelist key="153" {...rest} />;
    case 'exit':
      return <IconExit key="154" {...rest} />;
    case 'Location':
      return <IconLocation key="155" {...rest} />;
    case 'Arrow-Right2':
      return <IconArrowRight2 key="156" {...rest} />;
    case 'Arrow-RightSquare':
      return <IconArrowRightSquare key="157" {...rest} />;
    case 'Arrow-Right':
      return <IconArrowRight key="158" {...rest} />;
    case 'Category':
      return <IconCategory key="159" {...rest} />;
    case 'Filter':
      return <IconFilter key="160" {...rest} />;
    case 'Setting':
      return <IconSetting key="161" {...rest} />;
    case 'Arrow-Down2':
      return <IconArrowDown2 key="162" {...rest} />;
    case 'Arrow-Down3':
      return <IconArrowDown3 key="163" {...rest} />;
    case 'Arrow-Down':
      return <IconArrowDown key="164" {...rest} />;
    case 'Arrow-DownCircle':
      return <IconArrowDownCircle key="165" {...rest} />;
    case 'Arrow-Left2':
      return <IconArrowLeft2 key="166" {...rest} />;
    case 'Home1':
      return <IconHome1 key="167" {...rest} />;
    case 'Arrow-Left':
      return <IconArrowLeft key="168" {...rest} />;
    case 'Home':
      return <IconHome key="169" {...rest} />;
    case '008-man':
      return <Icon008Man key="170" {...rest} />;
    case '014-woman':
      return <Icon014Woman key="171" {...rest} />;
    case 'home1':
      return <IconHome1 key="172" {...rest} />;
    case 'home':
      return <IconHome key="173" {...rest} />;
    case 'left-arrow':
      return <IconLeftArrow key="174" {...rest} />;
    case 'left-btn':
      return <IconLeftBtn key="175" {...rest} />;
    case 'left-btn-fill':
      return <IconLeftBtnFill key="176" {...rest} />;
    case 'home-fill':
      return <IconHomeFill key="177" {...rest} />;
    case 'search':
      return <IconSearch key="178" {...rest} />;
    case 'money-check':
      return <IconMoneyCheck key="179" {...rest} />;
    case 'id-card-clip':
      return <IconIdCardClip key="180" {...rest} />;
    case 'user-large':
      return <IconUserLarge key="181" {...rest} />;
    case 'phone':
      return <IconPhone key="182" {...rest} />;
    case 'Exit':
      return <IconExit key="183" {...rest} />;
    case 'jiantou':
      return <IconJiantou key="184" {...rest} />;
    case 'icon-keyboard':
      return <IconIconKeyboard key="185" {...rest} />;
    case 'tuichu':
      return <IconTuichu key="186" {...rest} />;
    case 'dianpuzhuye':
      return <IconDianpuzhuye key="187" {...rest} />;
    case 'dingdanguanli':
      return <IconDingdanguanli key="188" {...rest} />;
    case 'xitongguanli':
      return <IconXitongguanli key="189" {...rest} />;
    case 'Password':
      return <IconPassword key="190" {...rest} />;
    case 'Profile':
      return <IconProfile key="191" {...rest} />;
    case 'a-ShieldDone':
      return <IconAShieldDone key="192" {...rest} />;
    case 'Calling':
      return <IconCalling key="193" {...rest} />;
    case 'Wallet':
      return <IconWallet key="194" {...rest} />;
    case 'Work':
      return <IconWork key="195" {...rest} />;
  }

  return null;
};

IconFont = React.memo ? React.memo(IconFont) : IconFont;

export default IconFont;
