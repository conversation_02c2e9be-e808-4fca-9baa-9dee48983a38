/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineCooperation = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M933.198 390.894L733.134 190.816c-16.316-16.316-38.009-25.302-61.081-25.302-23.073 0-44.766 8.986-61.081 25.302l-58.96 58.963-98.975-98.978c-16.312-16.316-38.002-25.302-61.075-25.302s-44.764 8.985-61.08 25.302L90.802 390.894C74.485 407.21 65.5 428.903 65.5 451.977c0 23.074 8.985 44.767 25.302 61.083l360.114 360.139C467.232 889.515 488.925 898.5 512 898.5c23.074 0 44.765-8.986 61.078-25.302L933.199 513.06c33.681-33.682 33.68-88.485-0.001-122.166zM670.992 570.961c-5.629 5.627-8.729 13.109-8.729 21.068 0 7.958 3.1 15.441 8.729 21.069l38.948 38.953-37.888 37.894-78.964-78.968c-11.616-11.616-30.521-11.617-42.137 0-5.629 5.627-8.729 13.108-8.729 21.067s3.1 15.441 8.729 21.068l78.964 78.97-37.888 37.893-118.98-118.982c-11.617-11.615-30.52-11.614-42.137 0-5.629 5.627-8.729 13.108-8.729 21.067s3.1 15.44 8.729 21.067l118.979 118.986-18.944 18.947c-10.447 10.444-27.446 10.445-37.895 0L132.939 470.924c-5.062-5.062-7.85-11.791-7.85-18.947 0-7.157 2.788-13.886 7.85-18.948l240.074-240.091c5.225-5.223 12.085-7.833 18.948-7.833 6.861 0 13.724 2.612 18.947 7.833l98.976 98.979-38.956 38.954c-16.316 16.316-25.302 38.009-25.302 61.084s8.985 44.768 25.302 61.083l40.016 40.014c16.312 16.316 38.002 25.302 61.074 25.302s44.764-8.985 61.079-25.302l38.955-38.954 98.976 98.977c10.443 10.447 10.443 27.445 0 37.893l-18.951 18.948-38.955-38.956c-5.625-5.627-13.105-8.727-21.062-8.727s-15.44 3.101-21.068 8.728z m1.06-188.794c-7.956 0-15.438 3.099-21.064 8.726l-60.021 60.024c-10.447 10.445-27.445 10.445-37.895 0L513.064 410.9c-5.062-5.061-7.85-11.789-7.85-18.946s2.788-13.886 7.85-18.947l140.044-140.054c5.059-5.061 11.786-7.848 18.943-7.848s13.885 2.787 18.943 7.848l200.065 200.075c5.062 5.062 7.85 11.792 7.85 18.948s-2.787 13.886-7.85 18.948l-58.96 58.963-58.952-58.963-80.032-80.03c-5.625-5.627-13.105-8.727-21.062-8.728l-0.001 0.001z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconLineCooperation.defaultProps = {
  size: 18,
};

IconLineCooperation = React.memo ? React.memo(IconLineCooperation) : IconLineCooperation;

export default IconLineCooperation;
