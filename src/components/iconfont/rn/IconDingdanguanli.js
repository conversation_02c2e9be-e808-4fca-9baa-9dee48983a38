/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconDingdanguanli = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M353.2 142.5h311.2c21.4 0 38.9-17.5 38.9-38.9s-17.5-38.9-38.9-38.9H353.2c-21.4 0-38.9 17.5-38.9 38.9s17.5 38.9 38.9 38.9z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M745.4 103.6c0 42.8-35 77.8-77.8 77.8H356.4c-42.8 0-77.8-35-77.8-77.8-107 0-194.5 87.5-194.5 194.5v466.7c0 107 87.5 194.5 194.5 194.5h466.7c107 0 194.5-87.5 194.5-194.5V298.1c0.1-107-87.5-194.5-194.4-194.5zM443.9 813.4H288.4c-21.4 0-38.9-17.5-38.9-38.9s17.5-38.9 38.9-38.9H444c21.4 0 38.9 17.5 38.9 38.9-0.1 21.4-17.6 38.9-39 38.9zM745.4 619H278.6c-21.4 0-38.9-17.5-38.9-38.9s17.5-38.9 38.9-38.9h466.7c21.4 0 38.9 17.5 38.9 38.9 0.1 21.4-17.4 38.9-38.8 38.9z m3.2-194.5H281.9c-21.4 0-38.9-17.5-38.9-38.9s17.5-38.9 38.9-38.9h466.7c21.4 0 38.9 17.5 38.9 38.9s-17.5 38.9-38.9 38.9z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconDingdanguanli.defaultProps = {
  size: 18,
};

IconDingdanguanli = React.memo ? React.memo(IconDingdanguanli) : IconDingdanguanli;

export default IconDingdanguanli;
