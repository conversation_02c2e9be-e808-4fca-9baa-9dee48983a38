/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconSend = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M914.56 110.165333a82.474667 82.474667 0 0 0-82.346667-21.461333L145.408 288.384a81.92 81.92 0 0 0-59.050667 64.938667c-6.058667 32 15.146667 72.704 42.794667 89.685333l214.741333 132.010667a55.637333 55.637333 0 0 0 68.693334-8.277334l245.888-247.424a31.317333 31.317333 0 0 1 45.226666 0c12.373333 12.458667 12.373333 32.64 0 45.525334l-246.314666 247.466666c-18.261333 18.346667-21.674667 46.933333-8.234667 69.12l131.2 216.874667c15.36 25.770667 41.813333 40.362667 70.826667 40.362667 3.413333 0 7.253333 0 10.709333-0.426667 33.28-4.266667 59.733333-27.050667 69.546667-59.306667l203.648-685.866666c8.96-29.226667 0.853333-61.013333-20.48-82.901334z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconSend.defaultProps = {
  size: 18,
};

IconSend = React.memo ? React.memo(IconSend) : IconSend;

export default IconSend;
