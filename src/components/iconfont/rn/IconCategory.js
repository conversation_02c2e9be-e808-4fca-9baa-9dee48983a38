/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconCategory = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M916.949333 232.234667v122.112a126.592 126.592 0 0 1-34.858666 89.002666 121.429333 121.429333 0 0 1-86.272 37.717334h-49.664a33.536 33.536 0 0 1-29.568-33.578667c0-17.28 12.8-31.786667 29.568-33.621333h48.170666c15.232-0.085333 29.781333-6.4 40.448-17.493334s16.554667-26.069333 16.341334-41.642666v-122.453334c-0.426667-31.872-25.6-57.6-56.789334-58.026666h-120.021333c-31.36 0-56.746667 25.984-56.746667 58.026666v183.125334a33.792 33.792 0 0 1-33.877333 33.024 32.426667 32.426667 0 0 1-23.04-10.453334 33.792 33.792 0 0 1-8.96-24.106666V232.234667c-0.085333-33.28 12.8-65.194667 35.797333-88.746667a121.386667 121.386667 0 0 1 86.826667-36.821333h120.021333c67.2 1.621333 121.258667 56.96 122.624 125.568zM349.696 106.666667H229.674667C161.92 107.093333 107.093333 163.072 106.666667 232.234667v122.496c0.426667 69.12 55.296 125.013333 123.008 125.184h120.021333a121.386667 121.386667 0 0 0 86.954667-36.522667c23.04-23.466667 36.053333-55.381333 36.053333-88.661333v-122.453334C472.490667 162.986667 417.536 106.794667 349.696 106.666667z m56.746667 248.064c0.256 15.573333-5.632 30.549333-16.298667 41.642666s-25.216 17.408-40.448 17.493334H229.674667a56.192 56.192 0 0 1-41.301334-16.938667 58.624 58.624 0 0 1-16.64-42.197333v-122.453334c0-15.616 6.144-30.549333 17.066667-41.472 10.88-10.88 25.6-16.853333 40.874667-16.554666h120.021333c31.274667 0.213333 56.576 26.026667 56.746667 58.026666v122.453334z m267.861333 189.354666h120.021333c67.712 0.170667 122.581333 56.064 123.008 125.184v122.453334c-0.426667 69.205333-55.253333 125.184-123.008 125.610666h-120.021333c-67.669333-0.426667-122.410667-56.448-122.624-125.568v-122.496c0-69.12 54.912-125.184 122.624-125.184z m160.298667 288.768c10.666667-10.88 16.64-25.685333 16.512-41.088v-122.496c0-15.36-5.973333-30.122667-16.64-41.002666a56.234667 56.234667 0 0 0-40.149334-16.981334h-120.021333c-31.36 0-56.746667 25.941333-56.746667 57.984v122.453334c-0.128 15.445333 5.802667 30.293333 16.469334 41.130666 10.666667 10.922667 25.173333 16.981333 40.277333 16.896h120.021333c15.104 0.085333 29.610667-5.973333 40.277334-16.896zM106.666667 791.381333v-122.112c0-33.28 12.970667-65.194667 36.053333-88.661333a121.344 121.344 0 0 1 86.954667-36.522667h49.28a32.853333 32.853333 0 0 1 31.573333 15.658667 34.389333 34.389333 0 0 1 0 35.84 32.853333 32.853333 0 0 1-31.573333 15.701333H229.674667c-31.445333 0-56.96 25.898667-57.173334 57.984v120.96a58.88 58.88 0 0 0 57.173334 57.984h120.746666c15.146667 0.085333 29.696-5.973333 40.405334-16.853333 10.752-10.88 16.768-25.685333 16.768-41.130667v-181.632a34.090667 34.090667 0 0 1 14.848-34.688 32.554667 32.554667 0 0 1 37.034666 1.109334 34.133333 34.133333 0 0 1 12.8 35.498666v180.864c0 69.333333-55.04 125.568-122.965333 125.568H229.674667c-67.328-1.408-121.6-56.832-123.008-125.568z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconCategory.defaultProps = {
  size: 18,
};

IconCategory = React.memo ? React.memo(IconCategory) : IconCategory;

export default IconCategory;
