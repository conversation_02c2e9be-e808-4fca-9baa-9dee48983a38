/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineCheck1 = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M416 896H224c-17.6 0-32-14.4-32-32V160c0-17.6 14.4-32 32-32h576c17.6 0 32 14.4 32 32v256c0 17.7 14.3 32 32 32s32-14.3 32-32V160c0-52.9-43.1-96-96-96H224c-52.9 0-96 43.1-96 96v704c0 52.9 43.1 96 96 96h192c17.7 0 32-14.3 32-32s-14.3-32-32-32z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M704 288c0-17.7-14.3-32-32-32H352c-17.7 0-32 14.3-32 32s14.3 32 32 32h320c17.7 0 32-14.3 32-32zM352 480c-17.7 0-32 14.3-32 32s14.3 32 32 32h192c17.7 0 32-14.3 32-32s-14.3-32-32-32H352zM950.6 649.4c-12.5-12.5-32.8-12.5-45.3 0L672 882.7 534.6 745.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l160 160c4.7 4.7 10.5 7.6 16.5 8.8 2 0.4 4.1 0.6 6.1 0.6s4.1-0.2 6.1-0.6c6.1-1.2 11.8-4.1 16.5-8.8l256-256c12.6-12.6 12.6-32.8 0.1-45.3z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineCheck1.defaultProps = {
  size: 18,
};

IconLineCheck1 = React.memo ? React.memo(IconLineCheck1) : IconLineCheck1;

export default IconLineCheck1;
