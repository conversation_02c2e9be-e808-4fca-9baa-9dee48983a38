/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconIdCardClip = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1152 1024" width={size} height={size} {...rest}>
      <Path
        d="M512 0h128c35.4 0 64 28.6 64 64v128c0 35.4-28.6 64-64 64h-128c-35.4 0-64-28.6-64-64V64c0-35.4 28.6-64 64-64zM128 128h256v96c0 53 43 96 96 96h192c53 0 96-43 96-96V128h256c70.6 0 128 57.4 128 128v640c0 70.6-57.4 128-128 128H128c-70.6 0-128-57.4-128-128V256c0-70.6 57.4-128 128-128z m224 746.6c0 11.8 9.6 21.4 21.4 21.4h405.2c11.8 0 21.4-9.6 21.4-21.4 0-59-47.8-106.6-106.6-106.6H458.6c-59 0-106.6 47.8-106.6 106.6zM576 704a128 128 0 1 0 0-256 128 128 0 1 0 0 256z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconIdCardClip.defaultProps = {
  size: 18,
};

IconIdCardClip = React.memo ? React.memo(IconIdCardClip) : IconIdCardClip;

export default IconIdCardClip;
