/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconArrowRightCircleLine = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M512 469.333333V341.333333l170.666667 170.666667-170.666667 170.666667v-128H341.333333v-85.333334h170.666667z m0-384c235.52 0 426.666667 191.146667 426.666667 426.666667s-191.146667 426.666667-426.666667 426.666667S85.333333 747.52 85.333333 512 276.48 85.333333 512 85.333333z m0 768c188.586667 0 341.333333-152.746667 341.333333-341.333333s-152.746667-341.333333-341.333333-341.333333-341.333333 152.746667-341.333333 341.333333 152.746667 341.333333 341.333333 341.333333z"
        fill={getIconColor(color, 0, '#000000')}
      />
    </Svg>
  );
};

IconArrowRightCircleLine.defaultProps = {
  size: 18,
};

IconArrowRightCircleLine = React.memo ? React.memo(IconArrowRightCircleLine) : IconArrowRightCircleLine;

export default IconArrowRightCircleLine;
