/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconUserLarge = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M512 576a288 288 0 1 0 0-576 288 288 0 1 0 0 576z m-189.4 64C144.4 640 0 784.4 0 962.6c0 34 27.6 61.4 61.4 61.4h901.2c34 0 61.4-27.6 61.4-61.4 0-178.2-144.4-322.6-322.6-322.6H322.6z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconUserLarge.defaultProps = {
  size: 18,
};

IconUserLarge = React.memo ? React.memo(IconUserLarge) : IconUserLarge;

export default IconUserLarge;
