/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineUserconfirmation = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M731.701 579.397c8.85-15.298 3.622-34.873-11.676-43.723-24.291-14.052-49.921-25.536-76.437-34.357a226.986 226.986 0 0 0 26.803-22.925C712.7 436.084 736 379.833 736 320s-23.3-116.084-65.608-158.392S571.832 96 512 96c-59.833 0-116.084 23.3-158.393 65.608C311.3 203.916 288 260.167 288 320s23.3 116.084 65.607 158.392a226.96 226.96 0 0 0 26.734 22.875 416.054 416.054 0 0 0-30.278 11.437c-49.541 20.954-94.026 50.945-132.221 89.14s-68.185 82.68-89.139 132.221C107.003 785.371 96 839.854 96 896c0 17.673 14.327 32 32 32s32-14.327 32-32c0-94.022 36.614-182.418 103.099-248.901C329.582 580.614 417.978 544 512 544c61.892 0 122.744 16.277 175.979 47.073 15.299 8.851 34.874 3.621 43.722-11.676zM352 320c0-88.224 71.775-160 160-160s160 71.776 160 160-71.775 160-160 160-160-71.776-160-160zM918.628 663.373c-12.499-12.499-32.76-12.496-45.255-0.001L685.999 850.745l-82.105-82.106c-12.496-12.496-32.758-12.496-45.254 0-12.497 12.497-12.497 32.759 0 45.256l104.732 104.732c0.28 0.28 0.569 0.547 0.857 0.815 0.113 0.105 0.222 0.217 0.336 0.32 6.079 5.493 13.757 8.239 21.435 8.239 8.189 0 16.379-3.124 22.628-9.373l0.038-0.04 209.961-209.96c12.497-12.497 12.497-32.759 0.001-45.255z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconLineUserconfirmation.defaultProps = {
  size: 18,
};

IconLineUserconfirmation = React.memo ? React.memo(IconLineUserconfirmation) : IconLineUserconfirmation;

export default IconLineUserconfirmation;
