/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconEngexam = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M875.2 553.4V228.3H494.3V102.8h-389V868h451.8c41.1 36.7 93.6 56.8 149.1 56.8 59.9 0 116.2-23.3 158.5-65.7s65.7-98.6 65.7-158.5c0-54.7-19.5-106.4-55.2-147.2zM175.4 172.8h249v55.5h-249v-55.5z m0 167.8v-42.3h629.9v201.1c-30.4-15-64.1-22.9-99-22.9-59.9 0-116.2 23.3-158.5 65.7-42.3 42.3-65.7 98.6-65.7 158.5 0 34.3 7.7 67.4 22.1 97.4H175.4V340.6z m530.8 514.2c-85 0-154.2-69.2-154.2-154.2s69.2-154.2 154.2-154.2 154.2 69.2 154.2 154.2-69.2 154.2-154.2 154.2z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M691.1 698.6l-33.9-34-49.5 49.5 83.4 83.5 126.2-126.2-49.5-49.5zM266.4 374.8h70v266.3h-70zM408.8 374.8h70v168.8h-70zM551.3 374.8h70v72.8h-70z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconEngexam.defaultProps = {
  size: 18,
};

IconEngexam = React.memo ? React.memo(IconEngexam) : IconEngexam;

export default IconEngexam;
