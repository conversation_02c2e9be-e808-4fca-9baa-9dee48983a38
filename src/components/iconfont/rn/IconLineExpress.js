/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineExpress = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M352.715 574.459h-0.002zM352.693 381.336c-17.861 0.011-32.332 14.474-32.32 32.304v95.655H223.91c-17.861 0.011-32.332 14.474-32.32 32.304s14.5 32.274 32.361 32.263h128.764v0.597c17.86-0.001 32.339-14.455 32.339-32.284V413.639v-0.04c-0.011-17.83-14.5-32.274-32.361-32.263z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M960 799.245V188.85c0.063-51.675-40.527-94.298-92.231-96.85H400.625c-44.322 0-80.253 35.867-80.253 80.111v93.263L124.531 363.42h-4.192c-36.29 25.543-57.41 67.485-56.297 111.797v322.374a32.507 32.507 0 0 0-0.042 1.656c0 17.829 14.454 32.283 32.283 32.283h100.318v0.001c17.861 71.098 90.079 114.281 161.304 96.451 47.56-11.906 84.695-48.975 96.622-96.451h126.967c19.184 70.07 91.64 111.348 161.834 92.197 46.587-12.71 80.46-48.847 92.373-92.198h92.017c17.829 0 32.283-14.454 32.283-32.283l-0.001-0.002z m-633.04 65.167h-2.396c-37.353-1.321-66.561-32.618-65.238-69.905 1.323-37.287 32.676-66.443 70.029-65.122 36.405 1.287 65.257 31.108 65.28 67.471 0.001 37.31-30.298 67.556-67.675 67.556z m247.347-97.448h-119.78c-17.861-71.098-90.079-114.281-161.304-96.451-49.213 12.32-85.069 50.547-96.642 96.451h-67.857V477.608c-1.726-23.196 8.415-45.69 26.951-59.784L367.087 313.8h3.593l5.39-4.185 3.593-4.783a26.297 26.297 0 0 0 0-5.381 31.02 31.02 0 0 0 0-6.576c0.093-1.593 0.093-3.19 0-4.783V172.111c0-8.915 7.24-16.142 16.17-16.142h467.144c17.861-0.003 32.343 14.448 32.346 32.278 0 0.201-0.002 0.402-0.006 0.603v578.114h-58.424c-0.374-1.595-0.768-3.19-1.204-4.783-15.671-57.234-67.79-96.909-127.228-96.85h-3.593c-62.071-1.056-116.461 41.282-130.561 101.633z m131.159 97.448c-37.376 0-67.676-30.246-67.676-67.556s30.3-67.556 67.676-67.556c37.376 0 67.676 30.246 67.676 67.556s-30.299 67.556-67.676 67.556z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineExpress.defaultProps = {
  size: 18,
};

IconLineExpress = React.memo ? React.memo(IconLineExpress) : IconLineExpress;

export default IconLineExpress;
