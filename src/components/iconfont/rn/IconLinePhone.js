/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLinePhone = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M768 64H256c-52.935 0-96 43.065-96 96v704c0 52.935 43.065 96 96 96h512c52.935 0 96-43.065 96-96V160c0-52.935-43.065-96-96-96z m32 800c0 17.645-14.355 32-32 32H256c-17.645 0-32-14.355-32-32V160c0-17.645 14.355-32 32-32h512c17.645 0 32 14.355 32 32v704z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M608 192H416c-17.673 0-32 14.327-32 32s14.327 32 32 32h192c17.673 0 32-14.327 32-32s-14.327-32-32-32zM512 736c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLinePhone.defaultProps = {
  size: 18,
};

IconLinePhone = React.memo ? React.memo(IconLinePhone) : IconLinePhone;

export default IconLinePhone;
