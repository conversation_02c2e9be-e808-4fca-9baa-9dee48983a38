/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineShoppingcart = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M945.933 289.588c-13.157-17.293-33.16-27.208-54.896-27.208H156.77l-24.327-145.632c-3.13-18.731-20.727-31.368-39.654-28.285-18.776 3.127-31.444 20.864-28.315 39.612L165.77 734.499C171.338 767.816 199.922 792 233.737 792h547.795c32.486 0 60.733-22.763 67.731-54.333l108.161-387.972c5.837-20.907 1.648-42.814-11.491-60.107zM782.592 720.239c-0.236 0.84-0.892 2.075-1.06 2.924H233.738l-65.461-391.947h722.76L782.592 720.239zM320 840c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zM704 840c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M384 664h256c17.673 0 32-14.327 32-32s-14.327-32-32-32H384c-17.673 0-32 14.327-32 32s14.327 32 32 32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineShoppingcart.defaultProps = {
  size: 18,
};

IconLineShoppingcart = React.memo ? React.memo(IconLineShoppingcart) : IconLineShoppingcart;

export default IconLineShoppingcart;
