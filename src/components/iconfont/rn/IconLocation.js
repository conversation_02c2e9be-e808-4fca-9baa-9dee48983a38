/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLocation = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M512 85.333333h1.408c199.978667 0.768 362.026667 163.925333 361.258667 363.605334v3.968c-2.688 142.72-88.490667 261.12-160 335.36-20.309333 21.205333-41.984 41.557333-64.298667 60.586666a31.829333 31.829333 0 0 1-41.344-48.384c20.693333-17.621333 40.789333-36.565333 59.733333-56.32 63.573333-65.962667 139.904-170.325333 142.208-292.394666 0.64-167.68-132.906667-302.165333-297.813333-302.848H512c-164.309333 0-298.325333 133.205333-298.965333 297.386666a389.12 389.12 0 0 0 79.36 222.976c57.301333 76.330667 144.256 157.141333 226.901333 210.944a31.701333 31.701333 0 0 1-17.408 58.453334 31.573333 31.573333 0 0 1-17.365333-5.162667C396.032 875.904 302.933333 789.333333 241.493333 707.584a452.565333 452.565333 0 0 1-92.16-259.029333C150.101333 246.869333 312.661333 85.333333 512 85.333333z m-74.453333 367.530667c0 40.917333 33.365333 74.24 74.325333 74.24s74.325333-33.322667 74.325333-74.24c0-40.96-33.322667-74.24-74.325333-74.24a31.829333 31.829333 0 1 1 0-63.573333 138.069333 138.069333 0 0 1 138.026667 137.813333 138.069333 138.069333 0 0 1-138.026667 137.813333 138.069333 138.069333 0 0 1-138.026667-137.813333 31.829333 31.829333 0 0 1 63.701334 0z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconLocation.defaultProps = {
  size: 18,
};

IconLocation = React.memo ? React.memo(IconLocation) : IconLocation;

export default IconLocation;
