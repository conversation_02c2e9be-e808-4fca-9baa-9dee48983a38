/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconCommentMultipleOutline = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M512 981.333333a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H298.666667a85.333333 85.333333 0 0 1-85.333334-85.333334V298.666667a85.333333 85.333333 0 0 1 85.333334-85.333334h597.333333a85.333333 85.333333 0 0 1 85.333333 85.333334v426.666666a85.333333 85.333333 0 0 1-85.333333 85.333334h-174.933333l-157.866667 158.293333c-8.533333 8.106667-19.2 12.373333-29.866667 12.373333H512m42.666667-256v131.413334L686.08 725.333333H896V298.666667H298.666667v426.666666h256M128 640H42.666667V128a85.333333 85.333333 0 0 1 85.333333-85.333333h682.666667v85.333333H128v512z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconCommentMultipleOutline.defaultProps = {
  size: 18,
};

IconCommentMultipleOutline = React.memo ? React.memo(IconCommentMultipleOutline) : IconCommentMultipleOutline;

export default IconCommentMultipleOutline;
