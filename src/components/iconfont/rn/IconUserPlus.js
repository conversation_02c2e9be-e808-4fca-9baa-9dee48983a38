/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconUserPlus = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1280 1024" width={size} height={size} {...rest}>
      <Path
        d="M192 256a256 256 0 1 1 512 0 256 256 0 1 1-512 0zM0 964.6C0 767.6 159.6 608 356.6 608h182.8c197 0 356.6 159.6 356.6 356.6 0 32.8-26.6 59.4-59.4 59.4H59.4C26.6 1024 0 997.4 0 964.6zM1008 624v-128h-128c-26.6 0-48-21.4-48-48s21.4-48 48-48h128v-128c0-26.6 21.4-48 48-48s48 21.4 48 48v128h128c26.6 0 48 21.4 48 48s-21.4 48-48 48h-128v128c0 26.6-21.4 48-48 48s-48-21.4-48-48z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconUserPlus.defaultProps = {
  size: 18,
};

IconUserPlus = React.memo ? React.memo(IconUserPlus) : IconUserPlus;

export default IconUserPlus;
