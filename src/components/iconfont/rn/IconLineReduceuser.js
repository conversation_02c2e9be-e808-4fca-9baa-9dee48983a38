/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineReduceuser = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M731.701 579.397c8.85-15.298 3.622-34.873-11.676-43.723-24.291-14.052-49.921-25.536-76.437-34.357a226.986 226.986 0 0 0 26.803-22.925C712.7 436.084 736 379.833 736 320s-23.3-116.084-65.608-158.392C628.084 119.3 571.832 96 512 96c-59.833 0-116.084 23.3-158.393 65.608C311.3 203.916 288 260.167 288 320s23.3 116.084 65.607 158.392a226.96 226.96 0 0 0 26.734 22.875 416.054 416.054 0 0 0-30.278 11.437c-49.541 20.954-94.026 50.945-132.221 89.14s-68.185 82.68-89.139 132.221C107.003 785.371 96 839.854 96 896c0 17.673 14.327 32 32 32s32-14.327 32-32c0-94.022 36.614-182.418 103.099-248.901C329.582 580.614 417.978 544 512 544c61.892 0 122.744 16.277 175.979 47.073 15.299 8.851 34.874 3.621 43.722-11.676zM512 480c-88.225 0-160-71.776-160-160s71.775-160 160-160 160 71.776 160 160-71.775 160-160 160zM895 759H687c-17.673 0-32 14.327-32 32s14.327 32 32 32h208c17.673 0 32-14.327 32-32s-14.327-32-32-32z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconLineReduceuser.defaultProps = {
  size: 18,
};

IconLineReduceuser = React.memo ? React.memo(IconLineReduceuser) : IconLineReduceuser;

export default IconLineReduceuser;
