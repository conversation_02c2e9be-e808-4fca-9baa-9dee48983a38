/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconDiscount = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M870.4 390.229333l30.72 30.72c24.32 23.893333 37.546667 56.32 37.546667 90.453334a126.933333 126.933333 0 0 1-37.546667 91.733333l-0.426667 0.426667-30.293333 30.293333c-11.989333 11.946667-18.816 28.16-18.816 45.269333v43.904c0 70.826667-57.6 128.469333-128.469333 128.469334h-43.946667c-17.066667 0-33.28 6.784-45.226667 18.730666l-30.762666 30.72c-25.173333 25.216-58.026667 37.546667-90.922667 37.546667a129.28 129.28 0 0 1-90.88-37.077333l-31.189333-31.189334c-11.946667-11.946667-28.16-18.730667-45.226667-18.730666h-43.946667a128.597333 128.597333 0 0 1-128.512-128.469334v-43.904c0-17.109333-6.826667-33.322667-18.773333-45.696l-30.72-30.293333a128.938667 128.938667 0 0 1-0.426667-181.717333l31.146667-31.189334c11.946667-11.946667 18.773333-28.16 18.773333-45.653333v-43.52c0-70.826667 57.6-128.384 128.469334-128.384h43.946666c17.066667 0 33.28-6.869333 45.269334-18.816l30.72-30.72a128.768 128.768 0 0 1 181.845333-0.384l31.146667 31.104c11.946667 11.946667 28.16 18.816 45.226666 18.816h43.946667a128.554667 128.554667 0 0 1 128.512 128.384v43.989333c0 17.024 6.826667 33.237333 18.773333 45.184z m-468.266667 268.8c10.24 0 19.626667-3.84 26.453334-11.093333l219.392-219.264a37.632 37.632 0 0 0 0-52.949333 37.205333 37.205333 0 0 0-52.48 0l-219.392 219.306666c-14.506667 14.506667-14.506667 38.4 0 52.906667 6.826667 7.253333 16.213333 11.093333 26.026666 11.093333z m182.272-37.546666a37.333333 37.333333 0 1 0 74.666667 0.426666 37.333333 37.333333 0 0 0-74.666667-0.426666z m-181.845333-256.426667c20.48 0 37.12 16.64 37.12 37.12 0 20.949333-16.64 37.546667-37.12 37.546667a37.546667 37.546667 0 0 1-37.546667-37.546667c0-20.48 17.066667-37.12 37.546667-37.12z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconDiscount.defaultProps = {
  size: 18,
};

IconDiscount = React.memo ? React.memo(IconDiscount) : IconDiscount;

export default IconDiscount;
