/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineTemplate = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M832 96H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m-640 64h640c17.645 0 32 14.355 32 32v160H160V192c0-17.645 14.355-32 32-32z m-32 672V416h192v448H192c-17.645 0-32-14.355-32-32z m672 32H416V416h448v416c0 17.645-14.355 32-32 32z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconLineTemplate.defaultProps = {
  size: 18,
};

IconLineTemplate = React.memo ? React.memo(IconLineTemplate) : IconLineTemplate;

export default IconLineTemplate;
