/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLeftArrow = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M466.70019 856.795429l-293.059047-293.059048L121.904762 512l51.712-51.712L466.70019 167.204571l51.712 51.712L261.948952 475.428571h640.24381v73.142858h-640.24381l256.487619 256.487619-51.736381 51.736381z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconLeftArrow.defaultProps = {
  size: 18,
};

IconLeftArrow = React.memo ? React.memo(IconLeftArrow) : IconLeftArrow;

export default IconLeftArrow;
