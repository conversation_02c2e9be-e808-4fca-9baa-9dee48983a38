/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineAddcommodity = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M832 96H192c-52.9 0-96 43.1-96 96v640c0 52.9 43.1 96 96 96h320c17.7 0 32-14.3 32-32s-14.3-32-32-32H192c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h640c17.6 0 32 14.4 32 32v320c0 17.7 14.3 32 32 32s32-14.3 32-32V192c0-52.9-43.1-96-96-96z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M338.2 272c0-26.5-21.5-48-48-48s-48 21.5-48 48c0 24.3 18 44.3 41.4 47.5 22.5 29.2 50.4 53.9 82.3 72.7C410 418.3 460.5 432 512 432s102-13.7 146.1-39.7c31.8-18.8 59.8-43.6 82.3-72.7 23.4-3.2 41.4-23.3 41.4-47.5 0-26.5-21.5-48-48-48s-48 21.5-48 48c0 4 0.5 7.9 1.4 11.6-17.1 21.4-37.9 39.6-61.6 53.5C591.3 357.3 552 368 512 368s-79.3-10.7-113.6-30.9c-23.7-13.9-44.5-32.2-61.6-53.5 1-3.7 1.4-7.6 1.4-11.6zM896 759h-73v-73c0-17.7-14.3-32-32-32s-32 14.3-32 32v73h-73c-17.7 0-32 14.3-32 32s14.3 32 32 32h73v73c0 17.7 14.3 32 32 32s32-14.3 32-32v-73h73c17.7 0 32-14.3 32-32s-14.3-32-32-32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineAddcommodity.defaultProps = {
  size: 18,
};

IconLineAddcommodity = React.memo ? React.memo(IconLineAddcommodity) : IconLineAddcommodity;

export default IconLineAddcommodity;
