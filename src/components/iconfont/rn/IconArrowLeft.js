/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconArrowLeft = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M674.005333 461.226667l157.866667-13.994667c35.413333 0 64.128 29.013333 64.128 64.768a64.426667 64.426667 0 0 1-64.128 64.768l-157.866667-13.952c-27.733333 0-50.304-22.741333-50.304-50.773333 0-28.16 22.528-50.816 50.346667-50.816z"
        fill={getIconColor(color, 0, '#200E32')}
        opacity=".4"
      />
      <Path
        d="M144 463.786667c2.474667-2.474667 11.690667-13.013333 20.352-21.76 50.517333-54.741333 182.357333-144.298667 251.306667-171.690667 10.496-4.352 36.992-13.653333 51.2-14.336a85.333333 85.333333 0 0 1 38.826666 9.386667c15.36 8.661333 27.690667 22.4 34.474667 38.570666 4.309333 11.178667 11.093333 44.8 11.093333 45.397334 6.784 36.693333 10.453333 96.384 10.453334 162.346666 0 62.805333-3.669333 120.064-9.216 157.354667-0.597333 0.682667-7.424 42.368-14.805334 56.661333a77.397333 77.397333 0 0 1-68.394666 42.282667h-2.432c-18.474667-0.597333-57.301333-16.810667-57.301334-17.408-65.28-27.392-194.048-112.597333-245.802666-169.216 0 0-14.634667-14.592-20.949334-23.637333A75.434667 75.434667 0 0 1 128 512.341333c0-18.048 5.546667-34.816 16-48.554666z"
        fill={getIconColor(color, 1, '#200E32')}
      />
    </Svg>
  );
};

IconArrowLeft.defaultProps = {
  size: 18,
};

IconArrowLeft = React.memo ? React.memo(IconArrowLeft) : IconArrowLeft;

export default IconArrowLeft;
