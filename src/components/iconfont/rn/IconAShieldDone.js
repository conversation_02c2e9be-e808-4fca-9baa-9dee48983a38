/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconAShieldDone = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M485.76 89.6a82.517333 82.517333 0 0 1 52.48 0l283.434667 96.725333A77.312 77.312 0 0 1 874.666667 259.029333v283.349334a351.402667 351.402667 0 0 1-89.813334 233.941333c-26.538667 29.738667-60.586667 55.296-104.021333 78.165333l-153.173333 80.426667a33.450667 33.450667 0 0 1-30.72 0l-153.514667-80.469333c-43.52-22.912-77.610667-48.469333-104.106667-78.08A351.701333 351.701333 0 0 1 149.333333 542.208c0-17.322667 14.506667-31.402667 32.298667-31.402667 17.834667 0 32.341333 14.08 32.341333 31.402667 0 70.912 26.325333 139.434667 74.154667 193.024 21.290667 23.893333 49.450667 44.8 86.016 64l138.112 72.362667 137.813333-72.362667c36.522667-19.157333 64.64-40.064 85.973334-64a289.706667 289.706667 0 0 0 74.026666-192.853333v-283.306667a14.293333 14.293333 0 0 0-9.813333-13.482667L516.906667 148.906667a15.232 15.232 0 0 0-9.728 0L223.744 245.589333a14.293333 14.293333 0 0 0-9.813333 13.44v81.066667c0 17.365333-14.464 31.445333-32.298667 31.445333a31.914667 31.914667 0 0 1-32.298667-31.445333v-81.066667c0-32.682667 21.333333-61.866667 53.034667-72.704z m140.8 304.64a32.981333 32.981333 0 0 1 45.653333 0 30.762667 30.762667 0 0 1 0 44.373333L504.32 601.6a32.768 32.768 0 0 1-45.653333 0l-81.493334-79.146667a30.762667 30.762667 0 0 1 0-44.373333 32.853333 32.853333 0 0 1 45.653334 0l58.666666 56.96z"
        fill={getIconColor(color, 0, '#000000')}
      />
    </Svg>
  );
};

IconAShieldDone.defaultProps = {
  size: 18,
};

IconAShieldDone = React.memo ? React.memo(IconAShieldDone) : IconAShieldDone;

export default IconAShieldDone;
