/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconMimimiji = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1025 1024" width={size} height={size} {...rest}>
      <Path
        d="M223.104235 882.216261A442.846235 442.846235 0 0 1 56.987033 575.170126V172.670711a36.699649 36.699649 0 0 1 36.870611-35.901831h41.543547a573.17558 573.17558 0 0 0 176.659803-36.528689 807.563248 807.563248 0 0 0 153.86499-78.243196l24.789359-16.640214a37.440481 37.440481 0 0 1 44.221938 0l24.789359 16.640214a794.513217 794.513217 0 0 0 153.86499 78.243196 571.465969 571.465969 0 0 0 176.203907 35.844844h41.543547a37.383494 37.383494 0 0 1 36.87061 35.901831v403.18326a442.618287 442.618287 0 0 1-166.174188 308.29985 368.193222 368.193222 0 0 1-578.931271-1.253715z m470.313985-413.440926h-13.391953v-68.38444a150.274807 150.274807 0 0 0-153.864989-146.114753c-4.21704 0-8.434081 0-12.651122 0.569871A151.015638 151.015638 0 0 0 347.335967 386.941956a131.981969 131.981969 0 0 0-0.683844 13.448939v68.38444h-13.391953a49.17981 49.17981 0 0 0-53.567811 40.403807v159.563693A89.469642 89.469642 0 0 0 341.922199 739.064834h341.9222a90.210474 90.210474 0 0 0 63.19862-70.265012V511.116701a49.122823 49.122823 0 0 0-53.567812-40.34682v-1.937559z m-276.045189-56.987033a96.877956 96.877956 0 0 1 193.015082 0v53.738772H417.430018v-53.852746z"
        fill={getIconColor(color, 0, '#ED9D3E')}
      />
    </Svg>
  );
};

IconMimimiji.defaultProps = {
  size: 18,
};

IconMimimiji = React.memo ? React.memo(IconMimimiji) : IconMimimiji;

export default IconMimimiji;
