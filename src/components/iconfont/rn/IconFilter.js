/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconFilter = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M85.333333 265.130667C85.333333 352.426667 157.312 423.552 245.888 423.552c88.533333 0 160.512-71.082667 160.512-158.421333 0-87.424-71.978667-158.464-160.512-158.464-12.202667 0-24.32 1.322667-36.053333 4.053333a37.12 37.12 0 0 0-28.16 44.586667 37.546667 37.546667 0 0 0 45.226666 27.733333c6.101333-1.365333 12.544-2.048 18.986667-2.048 47.018667 0 85.205333 37.717333 85.205333 84.138667 0 46.378667-38.186667 84.096-85.205333 84.096-47.061333 0-85.248-37.717333-85.248-84.096 0-4.778667 0.426667-9.472 1.152-13.994667a37.248 37.248 0 0 0-30.933333-42.794667 37.546667 37.546667 0 0 0-43.306667 30.549334c-1.450667 8.533333-2.218667 17.322667-2.218667 26.24z m507.008 37.12h308.650667c20.821333 0 37.674667-16.64 37.674667-37.12 0-20.522667-16.853333-37.205333-37.674667-37.205334h-308.650667a37.418667 37.418667 0 0 0-37.632 37.162667c0 20.522667 16.853333 37.162667 37.632 37.162667z m-160.64 493.781333H122.922667a37.418667 37.418667 0 0 1-37.632-37.12c0-20.565333 16.853333-37.205333 37.632-37.205333h308.736c20.778667 0 37.632 16.64 37.632 37.162666 0 20.48-16.853333 37.162667-37.674667 37.162667z m185.898667-37.12c0 87.338667 72.021333 158.421333 160.554667 158.421333 88.490667 0 160.512-71.082667 160.512-158.464a155.306667 155.306667 0 0 0-2.218667-26.197333 37.632 37.632 0 0 0-43.306667-30.549333 37.248 37.248 0 0 0-30.933333 42.794666 85.333333 85.333333 0 0 1 1.152 13.952c0 46.378667-38.229333 84.138667-85.205333 84.138667-47.061333 0-85.248-37.76-85.248-84.138667 0-52.736 52.224-93.824 104.405333-82.048a37.717333 37.717333 0 0 0 45.098667-27.989333 37.12 37.12 0 0 0-28.416-44.416 164.565333 164.565333 0 0 0-35.84-3.968c-88.533333 0-160.554667 71.04-160.554667 158.421333z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconFilter.defaultProps = {
  size: 18,
};

IconFilter = React.memo ? React.memo(IconFilter) : IconFilter;

export default IconFilter;
