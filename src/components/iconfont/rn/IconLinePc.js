/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLinePc = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M832 96H192c-52.935 0-96 43.065-96 96v448c0 52.935 43.065 96 96 96h288v128H288c-17.673 0-32 14.327-32 32s14.327 32 32 32h448c17.673 0 32-14.327 32-32s-14.327-32-32-32H544V736h288c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m-640 64h640c17.645 0 32 14.355 32 32v352H160V192c0-17.645 14.355-32 32-32z m640 512H192c-17.645 0-32-14.355-32-32v-32h704v32c0 17.645-14.355 32-32 32z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconLinePc.defaultProps = {
  size: 18,
};

IconLinePc = React.memo ? React.memo(IconLinePc) : IconLinePc;

export default IconLinePc;
