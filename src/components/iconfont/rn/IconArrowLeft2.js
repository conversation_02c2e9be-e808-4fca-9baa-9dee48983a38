/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconArrowLeft2 = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M693.589333 181.034667a34.730667 34.730667 0 0 1-0.085333 49.664l-207.616 203.306666-3.669333 3.114667a36.138667 36.138667 0 0 1-46.805334-3.285333 34.688 34.688 0 0 1 0.085334-49.621334l207.573333-203.306666 3.669333-3.157334a36.138667 36.138667 0 0 1 46.848 3.285334z m3.114667 615.893333a34.730667 34.730667 0 0 1-3.114667 46.08 36.138667 36.138667 0 0 1-50.517333 0.042667l-312.533333-306.176-3.242667-3.584a34.56 34.56 0 0 1 3.114667-46.08 36.096 36.096 0 0 1 50.517333-0.085334l312.576 306.218667 3.2 3.584z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconArrowLeft2.defaultProps = {
  size: 18,
};

IconArrowLeft2 = React.memo ? React.memo(IconArrowLeft2) : IconArrowLeft2;

export default IconArrowLeft2;
