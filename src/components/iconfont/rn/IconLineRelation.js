/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineRelation = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M416 640H256c-70.58 0-128-57.421-128-128 0-70.58 57.42-128 128-128h160c17.673 0 32-14.327 32-32s-14.327-32-32-32H256c-105.869 0-192 86.131-192 192s86.131 192 192 192h160c17.673 0 32-14.327 32-32s-14.327-32-32-32zM768 320H608c-17.673 0-32 14.327-32 32s14.327 32 32 32h160c70.579 0 128 57.42 128 128 0 70.579-57.421 128-128 128H608c-17.673 0-32 14.327-32 32s14.327 32 32 32h160c105.869 0 192-86.131 192-192s-86.131-192-192-192z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M288 512c0 17.673 14.327 32 32 32h384c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32H320c-17.673 0-32 14.327-32 32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineRelation.defaultProps = {
  size: 18,
};

IconLineRelation = React.memo ? React.memo(IconLineRelation) : IconLineRelation;

export default IconLineRelation;
