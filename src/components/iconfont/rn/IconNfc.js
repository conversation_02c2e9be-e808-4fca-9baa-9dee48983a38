/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconNfc = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M1022.208 84.48C1019.52 42.666667 987.434667 10.922667 944.853333 8.405333c-47.018667-2.816-199.168-5.12-242.901333-5.12 78.165333 53.930667 88.832 155.477333 96.213333 344.149334 4.309333 111.786667 0.426667 503.424 0.085334 520.021333l-2.090667 106.837333-410.794667-410.88v-128.597333l326.656 326.741333c0.853333-64.682667 1.706667-148.992 1.706667-226.090666 0-75.093333-1.109333-143.104-3.242667-178.901334-12.288-205.610667-31.445333-301.952-138.794666-339.712-32.853333-11.52-63.445333-14.293333-114.474667-14.976C415.061333 1.365333 121.514667 1.578667 79.104 3.882667 34.133333 6.272 3.84 38.997333 1.792 81.066667c-2.048 41.685333-2.730667 818.090667 0 860.373333 2.645333 41.813333 34.773333 73.557333 77.312 76.032 47.018667 2.858667 199.168 3.2 242.944 3.2-78.165333-53.930667-88.874667-155.434667-96.213333-344.149333-4.266667-111.786667-0.384-503.466667 0-520.021334l2.005333-106.837333 410.837333 410.88v128.554667L311.978667 262.485333c-0.853333 64.597333-1.706667 148.906667-1.706667 226.048 0 75.093333 1.109333 143.104 3.285333 178.858667 12.288 205.653333 31.402667 301.952 138.752 339.712 32.853333 11.562667 63.445333 14.378667 114.474667 15.018667 42.112 0.512 335.701333 0.256 378.069333-2.005334 45.056-2.389333 75.306667-35.072 77.354667-77.269333 2.048-41.642667 2.730667-816.085333 0-858.368"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconNfc.defaultProps = {
  size: 18,
};

IconNfc = React.memo ? React.memo(IconNfc) : IconNfc;

export default IconNfc;
