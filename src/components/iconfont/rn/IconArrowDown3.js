/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconArrowDown3 = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M716.074667 501.845333h-173.482667V159.317333A30.976 30.976 0 0 0 512 128a30.976 30.976 0 0 0-30.592 31.317333v342.528H307.925333a30.464 30.464 0 0 0-26.794666 16.213334 31.914667 31.914667 0 0 0 0.896 31.744l204.074666 331.52c5.632 9.173333 15.36 14.677333 25.898667 14.677333s20.266667-5.546667 25.898667-14.677333l126.976-206.293334a31.744 31.744 0 0 0-9.557334-43.178666 30.250667 30.250667 0 0 0-42.24 9.856L512 805.845333 363.349333 564.48h352.725334a30.976 30.976 0 0 0 30.592-31.317333 30.976 30.976 0 0 0-30.592-31.317334z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconArrowDown3.defaultProps = {
  size: 18,
};

IconArrowDown3 = React.memo ? React.memo(IconArrowDown3) : IconArrowDown3;

export default IconArrowDown3;
