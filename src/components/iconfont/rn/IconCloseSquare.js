/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconCloseSquare = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M906.410667 493.994667a32.298667 32.298667 0 0 0 32.256-32.256v-109.653334C938.666667 187.434667 835.84 85.333333 672.128 85.333333h-320C187.434667 85.333333 85.333333 187.392 85.333333 352.256v320C85.333333 836.608 187.392 938.666667 352.128 938.666667h320.128c164.352 0 266.410667-102.058667 266.282667-266.538667a32.682667 32.682667 0 0 0-65.28 0c0 129.450667-71.253333 201.130667-201.130667 201.130667h-320c-129.834667 0-201.514667-71.68-201.514667-201.130667v-320c0-129.834667 71.68-201.514667 201.642667-201.514667h320c129.877333 0 201.130667 71.253333 201.130667 201.514667V460.672a32.64 32.64 0 0 0 32.64 32.554667v0.768h0.426666zM418.773333 560.853333l-29.184 29.184a32.981333 32.981333 0 0 0-1.621333 46.165334l0.938667 0.682666c12.458667 12.458667 32.512 12.8 45.354666 0.768l29.056-29.013333a32.682667 32.682667 0 0 0-44.544-47.786667z m227.328 70.741334a32.725333 32.725333 0 0 1-45.653333 0.512l-1.621333-1.578667L394.88 426.666667a34.858667 34.858667 0 0 1-0.554667-47.274667 32.213333 32.213333 0 0 1 45.610667-0.554667l0.341333 0.298667 79.573334 79.616 72.405333-72.448a33.408 33.408 0 0 1 46.421333 0.298667c2.304 2.304 4.266667 4.906667 5.76 7.808a32.725333 32.725333 0 0 1-5.205333 39.466666l-72.106667 72.106667 78.421334 78.421333a32.853333 32.853333 0 0 1 0.853333 46.933334l-0.298667 0.298666z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconCloseSquare.defaultProps = {
  size: 18,
};

IconCloseSquare = React.memo ? React.memo(IconCloseSquare) : IconCloseSquare;

export default IconCloseSquare;
