/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconIconKeyboard = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M242.005 1000.334c-29.013-27.875-28.444-74.524 0-102.969L627.485 512l-385.48-385.365c-28.444-28.445-28.444-74.525 0-102.97s74.525-28.444 102.97 0l437.02 436.907c15.587 13.768 38.912 61.327 0 102.97l-437.02 436.906c-20.595 21.732-67.243 34.247-102.97-0.114z"
        fill={getIconColor(color, 0, '#2c2c2c')}
      />
    </Svg>
  );
};

IconIconKeyboard.defaultProps = {
  size: 18,
};

IconIconKeyboard = React.memo ? React.memo(IconIconKeyboard) : IconIconKeyboard;

export default IconIconKeyboard;
