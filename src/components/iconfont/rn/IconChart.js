/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconChart = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M938.666667 451.2a32.64 32.64 0 0 1-32.64 32.64h-0.384a32.256 32.256 0 0 1-32.256-33.024V352.128c0-129.834667-71.68-201.514667-201.514667-201.514667H352.128c-130.218667 0-201.514667 71.68-201.514667 201.514667v320.085333c0 129.877333 71.68 201.173333 201.514667 201.173334h319.744c130.218667 0 201.514667-71.68 201.514667-201.130667a32.64 32.64 0 1 1 65.28 0C938.666667 836.565333 836.608 938.666667 672.213333 938.666667H352.128C187.434667 938.666667 85.333333 836.608 85.333333 672.213333V352.128C85.333333 187.434667 187.392 85.333333 352.128 85.333333h319.744C835.84 85.333333 938.666667 187.392 938.666667 352.128v99.072zM296.96 712.021333V438.826667a32.256 32.256 0 0 1 33.792-31.146667 32.64 32.64 0 0 1 31.530667 33.792v272.810667a32.682667 32.682667 0 0 1-65.28-2.261334z m183.893333-399.658666v400.042666a32.64 32.64 0 0 0 65.28 0V312.32a32.64 32.64 0 0 0-65.28 0z m180.906667 399.658666v-127.573333a32.64 32.64 0 0 1 65.28 0v127.573333a32.64 32.64 0 0 1-65.28 0z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconChart.defaultProps = {
  size: 18,
};

IconChart = React.memo ? React.memo(IconChart) : IconChart;

export default IconChart;
