/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconDuicuo1 = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M512 64a448 448 0 1 1 0 896A448 448 0 0 1 512 64z m161.664 266.304L473.728 556.992a32 32 0 0 1-43.648 4.096L336 488a39.168 39.168 0 1 0-48.064 61.888l128.448 99.84a64 64 0 0 0 87.296-8.192l228.8-259.328a39.232 39.232 0 0 0-58.816-51.84z"
        fill={getIconColor(color, 0, '#01BF1A')}
      />
    </Svg>
  );
};

IconDuicuo1.defaultProps = {
  size: 18,
};

IconDuicuo1 = React.memo ? React.memo(IconDuicuo1) : IconDuicuo1;

export default IconDuicuo1;
