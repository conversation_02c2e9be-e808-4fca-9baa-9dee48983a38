/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconCalling = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M181.973333 456.746667a31.317333 31.317333 0 0 1 43.690667 8.192c108.629333 158.933333 307.242667 338.005333 433.664 390.997333 51.2 23.893333 89.258667 25.344 117.546667 4.906667 22.016-13.482667 84.778667-76.245333 83.626666-108.885334-0.341333-3.84-4.693333-11.093333-12.117333-18.773333-24.149333-24.874667-105.088-86.570667-122.026667-93.013333-13.482667-2.688-27.221333 5.290667-48.384 18.688l-10.88 6.826666a31.36 31.36 0 0 1-43.093333-10.794666 31.786667 31.786667 0 0 1 10.666667-43.477334l9.813333-6.101333c23.466667-14.848 55.68-35.072 96.512-26.581333 32.042667 6.656 133.034667 90.325333 152.32 110.250666 18.133333 18.645333 28.245333 38.570667 29.952 59.264 2.730667 77.653333-100.693333 158.933333-112.512 165.76-22.613333 16.384-49.024 24.661333-78.72 24.661334-29.568 0-62.421333-8.192-98.005333-24.832-136.832-57.301333-343.808-242.816-460.117334-413.098667a31.786667 31.786667 0 0 1 8.064-43.989333zM265.258667 94.293333l7.765333 0.128c21.546667 1.749333 41.344 11.946667 59.946667 30.208 19.712 19.370667 102.826667 121.088 109.482666 153.429334 7.168 34.645333-8.32 61.696-19.584 81.450666-11.093333 19.456-14.933333 27.733333-11.093333 38.272 28.032 69.12 71.338667 125.525333 128.298667 166.485334 14.122667 10.24 17.408 29.952 7.296 44.202666a31.317333 31.317333 0 0 1-43.818667 7.338667c-66.773333-48.042667-117.376-113.706667-150.485333-195.285333-14.506667-40.32 2.730667-70.528 15.36-92.586667 9.472-16.554667 14.677333-26.453333 12.458666-36.949333-5.845333-15.232-67.157333-96.853333-91.818666-121.045334-7.68-7.552-14.805333-11.946667-20.138667-12.373333-28.245333-1.322667-91.306667 59.050667-105.258667 82.261333-21.546667 29.866667-20.053333 68.181333 3.114667 119.466667a31.744 31.744 0 0 1-15.573333 41.898667 31.317333 31.317333 0 0 1-41.6-15.701334c-32.938667-72.874667-32.384-133.717333 1.834666-180.736 7.210667-12.245333 86.954667-115.285333 161.578667-110.336z m348.757333 140.672l4.224 0.426667a216.832 216.832 0 0 1 171.434667 172.672 31.658667 31.658667 0 0 1-30.933334 37.674667 31.488 31.488 0 0 1-30.762666-25.6 153.898667 153.898667 0 0 0-121.770667-122.581334 31.701333 31.701333 0 0 1-24.874667-37.12 31.957333 31.957333 0 0 1 36.906667-25.002666zM611.456 85.333333l4.266667 0.256c170.837333 19.072 303.573333 152.576 322.730666 324.608a31.573333 31.573333 0 0 1-31.232 35.2 31.530667 31.530667 0 0 1-31.189333-28.117333c-15.914667-142.421333-125.781333-253.013333-267.306667-268.8a31.573333 31.573333 0 0 1-27.733333-34.986667 31.317333 31.317333 0 0 1 34.688-27.904z"
        fill={getIconColor(color, 0, '#000000')}
      />
    </Svg>
  );
};

IconCalling.defaultProps = {
  size: 18,
};

IconCalling = React.memo ? React.memo(IconCalling) : IconCalling;

export default IconCalling;
