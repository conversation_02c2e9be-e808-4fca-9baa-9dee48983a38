/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconDianpuzhuye = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M904.4 356.4L512 64.3 119.6 356.4c-24.3 18-38.6 46.5-38.6 76.8V864c0 52.9 42.9 95.8 95.8 95.8h670.4c52.9 0 95.8-42.9 95.8-95.8V433.2c0-30.3-14.3-58.8-38.6-76.8zM691.6 816.1H332.4c-19.8 0-35.9-16.2-35.9-35.9s16.2-35.9 35.9-35.9h359.2c19.8 0 35.9 16.2 35.9 35.9s-16.2 35.9-35.9 35.9z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconDianpuzhuye.defaultProps = {
  size: 18,
};

IconDianpuzhuye = React.memo ? React.memo(IconDianpuzhuye) : IconDianpuzhuye;

export default IconDianpuzhuye;
