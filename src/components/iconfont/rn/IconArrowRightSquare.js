/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconArrowRightSquare = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M118.101333 384.512a32.682667 32.682667 0 0 1-32.64-32.64C85.333333 187.392 187.392 85.333333 351.701333 85.333333h320.170667C836.565333 85.333333 938.666667 187.392 938.666667 351.744v320C938.666667 836.565333 836.608 938.666667 671.872 938.666667h-320C188.16 938.666667 85.333333 836.565333 85.333333 671.872v-109.610667c0-17.792 14.464-32.256 32.256-32.256h0.384v0.725334c18.005333 0 32.597333 14.592 32.64 32.597333V671.872c0 130.261333 71.253333 201.514667 201.130667 201.514667h320c129.962667 0 201.642667-71.68 201.642667-201.514667v-320c0-129.450667-71.68-201.130667-201.514667-201.130667h-320c-129.834667 0-201.130667 71.68-201.130667 201.130667a32.682667 32.682667 0 0 1-32.64 32.64z m219.562667 159.488h271.189333l-105.685333 105.258667a31.957333 31.957333 0 1 0 45.141333 45.312l160.597334-159.914667v-0.042667a31.658667 31.658667 0 0 0 9.386666-22.570666V512c0-0.298667 0-0.554667-0.128-0.810667-0.042667-0.213333-0.128-0.426667-0.128-0.682666a32.170667 32.170667 0 0 0-2.176-10.709334 33.365333 33.365333 0 0 0-6.485333-9.813333 31.658667 31.658667 0 0 0-23.04-9.941333H337.664a32 32 0 0 0 0 63.957333z m265.472-160.042667L548.352 329.386667a31.914667 31.914667 0 0 0-45.312 0.085333 31.957333 31.957333 0 0 0 0.128 45.226667l54.784 54.528a32.042667 32.042667 0 0 0 45.184-45.312z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconArrowRightSquare.defaultProps = {
  size: 18,
};

IconArrowRightSquare = React.memo ? React.memo(IconArrowRightSquare) : IconArrowRightSquare;

export default IconArrowRightSquare;
