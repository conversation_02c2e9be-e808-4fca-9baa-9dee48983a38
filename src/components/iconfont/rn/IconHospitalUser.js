/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconHospitalUser = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1152 1024" width={size} height={size} {...rest}>
      <Path
        d="M96 0C43 0 0 43 0 96v416h288c17.6 0 32 14.4 32 32s-14.4 32-32 32H0v128h288c17.6 0 32 14.4 32 32s-14.4 32-32 32H0v160c0 53 43 96 96 96h435.8c-12.6-20.4-19.8-44.4-19.8-70.2 0-93.8 51.6-175.6 128-218.4V96c0-53-43-96-96-96H96z m208 128h32c17.6 0 32 14.4 32 32v48h48c17.6 0 32 14.4 32 32v32c0 17.6-14.4 32-32 32h-48v48c0 17.6-14.4 32-32 32h-32c-17.6 0-32-14.4-32-32v-48h-48c-17.6 0-32-14.4-32-32v-32c0-17.6 14.4-32 32-32h48V160c0-17.6 14.4-32 32-32z m720 416a160 160 0 1 0-320 0 160 160 0 1 0 320 0zM576 954.2c0 38.6 31.2 69.8 69.8 69.8h436.4c38.6 0 69.8-31.2 69.8-69.8 0-102.8-83.4-186.2-186.2-186.2H762.2c-102.8 0-186.2 83.4-186.2 186.2z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconHospitalUser.defaultProps = {
  size: 18,
};

IconHospitalUser = React.memo ? React.memo(IconHospitalUser) : IconHospitalUser;

export default IconHospitalUser;
