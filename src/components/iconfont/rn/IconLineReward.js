/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineReward = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M511.202 268.48c-70.579 0-128 57.421-128 128s57.42 128 128 128c70.579 0 128-57.421 128-128s-57.421-128-128-128z m0 192c-35.29 0-64-28.71-64-64s28.71-64 64-64 64 28.71 64 64-28.71 64-64 64z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M874.196 847.453a32.12 32.12 0 0 0 1.408-1.894l0.062-0.096c0.419-0.619 0.809-1.255 1.181-1.899 0.083-0.143 0.168-0.284 0.249-0.429a32.755 32.755 0 0 0 2.11-4.601c0.039-0.107 0.074-0.217 0.113-0.325a32.064 32.064 0 0 0 1.13-3.962c0.04-0.186 0.071-0.373 0.108-0.559 0.064-0.325 0.13-0.65 0.184-0.977 0.045-0.273 0.079-0.546 0.117-0.819 0.033-0.24 0.07-0.48 0.098-0.721 0.036-0.308 0.061-0.615 0.088-0.923 0.018-0.209 0.04-0.418 0.054-0.627 0.021-0.318 0.033-0.636 0.044-0.953 0.008-0.203 0.018-0.405 0.021-0.608 0.005-0.311 0.002-0.621-0.002-0.931-0.002-0.215-0.003-0.429-0.01-0.643-0.009-0.291-0.027-0.58-0.044-0.869-0.014-0.237-0.027-0.474-0.046-0.712-0.021-0.261-0.05-0.52-0.077-0.779a26.946 26.946 0 0 0-0.092-0.802c-0.03-0.226-0.066-0.451-0.101-0.676a30.324 30.324 0 0 0-0.147-0.895c-0.035-0.192-0.076-0.383-0.115-0.575a30.303 30.303 0 0 0-0.21-0.976c-0.039-0.165-0.083-0.329-0.124-0.493a31.756 31.756 0 0 0-0.273-1.027c-0.043-0.15-0.092-0.299-0.137-0.448a33.033 33.033 0 0 0-0.331-1.041c-0.051-0.148-0.107-0.295-0.16-0.442-0.122-0.339-0.244-0.678-0.378-1.015-0.066-0.166-0.139-0.329-0.207-0.495-0.13-0.312-0.259-0.624-0.399-0.933-0.099-0.218-0.207-0.431-0.31-0.646-0.122-0.253-0.239-0.508-0.368-0.759a33.431 33.431 0 0 0-0.648-1.192c-0.036-0.064-0.068-0.129-0.105-0.192L760.393 609.764c22.006-25.651 39.8-54.345 53.02-85.601 17.112-40.458 25.789-83.417 25.789-127.683s-8.677-87.224-25.789-127.682c-16.521-39.062-40.167-74.136-70.28-104.249-30.112-30.113-65.187-53.758-104.248-70.28-40.459-17.112-83.417-25.789-127.683-25.789s-87.224 8.677-127.682 25.789c-39.062 16.522-74.136 40.167-104.249 70.28-30.113 30.112-53.758 65.187-70.28 104.249-17.112 40.458-25.789 83.417-25.789 127.682s8.677 87.224 25.789 127.683c13.22 31.256 31.014 59.95 53.02 85.601L147.124 808.755c-0.013 0.022-0.024 0.045-0.036 0.067-0.25 0.435-0.49 0.876-0.72 1.323-0.117 0.227-0.222 0.456-0.332 0.684-0.116 0.241-0.237 0.479-0.347 0.723-0.131 0.289-0.251 0.581-0.373 0.873-0.078 0.186-0.159 0.37-0.234 0.557-0.126 0.318-0.241 0.638-0.357 0.959-0.06 0.167-0.124 0.333-0.181 0.501a28.74 28.74 0 0 0-0.313 0.983c-0.052 0.17-0.107 0.339-0.156 0.509-0.092 0.321-0.175 0.642-0.257 0.965-0.047 0.186-0.097 0.371-0.141 0.558-0.071 0.303-0.134 0.606-0.195 0.91-0.044 0.214-0.089 0.427-0.128 0.642-0.05 0.275-0.093 0.551-0.136 0.826-0.039 0.248-0.078 0.497-0.111 0.747-0.032 0.243-0.057 0.486-0.083 0.729-0.031 0.283-0.061 0.567-0.085 0.851-0.017 0.213-0.028 0.425-0.041 0.638-0.019 0.314-0.038 0.628-0.047 0.944-0.006 0.187-0.006 0.373-0.008 0.56-0.005 0.338-0.008 0.676-0.002 1.016 0.003 0.171 0.012 0.341 0.018 0.511 0.012 0.35 0.025 0.699 0.049 1.05 0.012 0.173 0.03 0.345 0.044 0.517 0.029 0.343 0.057 0.686 0.098 1.03 0.023 0.197 0.054 0.392 0.08 0.588 0.043 0.317 0.084 0.634 0.137 0.952 0.042 0.256 0.095 0.51 0.144 0.765 0.049 0.256 0.093 0.512 0.148 0.769 0.105 0.488 0.223 0.974 0.351 1.456l0.019 0.08 0.002 0.006c0.215 0.802 0.469 1.593 0.746 2.377 0.042 0.12 0.082 0.243 0.126 0.362 0.261 0.713 0.552 1.415 0.864 2.108a31.736 31.736 0 0 0 1.236 2.471c0.085 0.153 0.175 0.302 0.263 0.454 0.365 0.63 0.746 1.251 1.155 1.857l0.091 0.14a31.81 31.81 0 0 0 1.384 1.862c0.086 0.108 0.17 0.217 0.257 0.324a31.93 31.93 0 0 0 1.523 1.733c0.074 0.079 0.151 0.155 0.226 0.233a31.868 31.868 0 0 0 3.868 3.408c0.611 0.456 1.238 0.897 1.89 1.314 0.082 0.053 0.167 0.101 0.25 0.153 0.339 0.213 0.678 0.427 1.028 0.629 0.197 0.114 0.4 0.21 0.599 0.32a29.578 29.578 0 0 0 2.264 1.129c0.224 0.101 0.445 0.207 0.672 0.303 0.699 0.297 1.403 0.57 2.113 0.814 0.033 0.011 0.065 0.025 0.097 0.036 0.775 0.263 1.556 0.491 2.341 0.691 0.174 0.045 0.352 0.079 0.527 0.121a32.07 32.07 0 0 0 1.798 0.376c0.258 0.046 0.518 0.086 0.777 0.126 0.543 0.083 1.086 0.149 1.631 0.203 0.268 0.027 0.535 0.057 0.804 0.077 0.696 0.051 1.391 0.081 2.085 0.087 0.091 0.001 0.181 0.011 0.273 0.011 1.81 0 3.641-0.171 5.475-0.491 0.232-0.04 0.466-0.07 0.697-0.115 0.274-0.054 0.549-0.127 0.823-0.188 0.412-0.092 0.826-0.179 1.234-0.288 0.024-0.006 0.049-0.01 0.073-0.017l100.228-26.856 26.857 100.229 0.002 0.006c0.215 0.802 0.469 1.593 0.746 2.377 0.042 0.12 0.082 0.243 0.126 0.362 0.261 0.713 0.552 1.415 0.864 2.108a31.736 31.736 0 0 0 1.236 2.471c0.085 0.153 0.175 0.302 0.263 0.454 0.365 0.63 0.746 1.251 1.155 1.857l0.091 0.14a31.81 31.81 0 0 0 1.384 1.862c0.086 0.108 0.17 0.217 0.257 0.324a31.93 31.93 0 0 0 1.523 1.733c0.074 0.079 0.151 0.155 0.226 0.233a31.868 31.868 0 0 0 3.868 3.408c0.611 0.456 1.238 0.897 1.89 1.314 0.082 0.053 0.167 0.101 0.25 0.153 0.339 0.213 0.678 0.427 1.028 0.629 0.197 0.114 0.4 0.21 0.599 0.32a29.578 29.578 0 0 0 2.264 1.129c0.224 0.101 0.445 0.207 0.672 0.303 0.699 0.297 1.403 0.57 2.113 0.814 0.033 0.011 0.065 0.025 0.097 0.036 0.775 0.263 1.556 0.491 2.341 0.691 0.174 0.045 0.352 0.079 0.527 0.121a32.07 32.07 0 0 0 1.798 0.376c0.258 0.046 0.518 0.086 0.777 0.126 0.543 0.083 1.086 0.149 1.631 0.203 0.268 0.027 0.535 0.057 0.804 0.077 0.696 0.051 1.391 0.081 2.085 0.087 0.091 0.001 0.181 0.011 0.273 0.011 1.874 0 3.77-0.18 5.667-0.522 0.166-0.03 0.333-0.05 0.498-0.082 0.299-0.059 0.597-0.137 0.896-0.205 0.39-0.088 0.781-0.17 1.168-0.272 0.024-0.007 0.049-0.011 0.073-0.017a32.5 32.5 0 0 0 1.734-0.52c0.13-0.043 0.258-0.091 0.387-0.136 0.483-0.166 0.96-0.341 1.431-0.527 0.154-0.061 0.307-0.125 0.46-0.188 0.478-0.198 0.949-0.407 1.413-0.626 0.111-0.052 0.223-0.103 0.333-0.156a31.828 31.828 0 0 0 3.364-1.899c0.122-0.079 0.242-0.161 0.363-0.242 0.414-0.277 0.821-0.562 1.22-0.856 0.139-0.102 0.278-0.203 0.416-0.308a32.35 32.35 0 0 0 1.41-1.134l0.2-0.176a31.409 31.409 0 0 0 2.577-2.523c0.148-0.162 0.299-0.322 0.444-0.488 0.398-0.453 0.784-0.917 1.155-1.391 0.105-0.134 0.204-0.274 0.307-0.41a32.234 32.234 0 0 0 1.189-1.677 32.38 32.38 0 0 0 1.031-1.654L488.831 728.91a32.217 32.217 0 0 0 2.351-5.05c6.644 0.398 13.317 0.622 20.025 0.622 6.948 0 13.859-0.239 20.737-0.666a32.021 32.021 0 0 0 3.234 7.859l120 207.846 0.009 0.014a31.998 31.998 0 0 0 1.351 2.125c0.275 0.399 0.559 0.792 0.851 1.179 0.106 0.141 0.209 0.285 0.318 0.424 0.369 0.471 0.752 0.931 1.148 1.382 0.15 0.171 0.305 0.336 0.458 0.503 0.279 0.305 0.563 0.604 0.854 0.899 0.167 0.169 0.334 0.338 0.504 0.502 0.382 0.369 0.772 0.729 1.173 1.079 0.093 0.081 0.184 0.163 0.277 0.243 0.441 0.376 0.894 0.739 1.357 1.092 0.147 0.112 0.296 0.22 0.444 0.329a30.9 30.9 0 0 0 1.183 0.83c0.132 0.088 0.262 0.177 0.395 0.263 1.066 0.69 2.18 1.32 3.34 1.885 0.122 0.06 0.246 0.115 0.369 0.173a31.432 31.432 0 0 0 3.257 1.322c0.139 0.048 0.276 0.1 0.416 0.146 0.568 0.187 1.143 0.362 1.727 0.518l0.069 0.016c0.405 0.107 0.814 0.194 1.223 0.285 0.281 0.063 0.561 0.138 0.842 0.193 0.187 0.037 0.377 0.06 0.564 0.093a31.91 31.91 0 0 0 5.604 0.512c0.092 0 0.182-0.011 0.273-0.011a31.65 31.65 0 0 0 2.085-0.087c0.269-0.02 0.536-0.051 0.805-0.078a31.168 31.168 0 0 0 4.212-0.707c0.173-0.041 0.347-0.075 0.519-0.119a31.688 31.688 0 0 0 2.346-0.693l0.072-0.027c0.72-0.246 1.433-0.524 2.141-0.825 0.219-0.093 0.434-0.196 0.651-0.294a31.194 31.194 0 0 0 2.27-1.132c0.202-0.111 0.407-0.208 0.607-0.324 0.355-0.205 0.699-0.422 1.043-0.638 0.076-0.048 0.153-0.092 0.229-0.14a32.69 32.69 0 0 0 1.904-1.324l0.308-0.234a31.978 31.978 0 0 0 3.564-3.179l0.206-0.212a32.165 32.165 0 0 0 1.539-1.751c0.081-0.099 0.159-0.2 0.238-0.3a32.12 32.12 0 0 0 1.408-1.894l0.062-0.096c0.419-0.619 0.809-1.255 1.181-1.899 0.083-0.143 0.168-0.284 0.249-0.429a32.755 32.755 0 0 0 2.11-4.601c0.039-0.107 0.074-0.217 0.113-0.325 0.284-0.803 0.544-1.614 0.763-2.436l26.849-100.2 100.229 26.856 0.069 0.016c0.405 0.107 0.814 0.194 1.223 0.285 0.281 0.063 0.561 0.138 0.842 0.193 0.187 0.037 0.377 0.06 0.564 0.093a31.91 31.91 0 0 0 5.604 0.512c0.092 0 0.182-0.011 0.273-0.011a31.65 31.65 0 0 0 2.085-0.087c0.269-0.02 0.536-0.051 0.805-0.078a31.168 31.168 0 0 0 4.212-0.707c0.173-0.041 0.347-0.075 0.519-0.119a31.688 31.688 0 0 0 2.346-0.693l0.072-0.027c0.72-0.246 1.433-0.524 2.141-0.825 0.219-0.093 0.434-0.196 0.651-0.294a31.194 31.194 0 0 0 2.27-1.132c0.202-0.111 0.407-0.208 0.607-0.324 0.355-0.205 0.699-0.422 1.043-0.638 0.076-0.048 0.153-0.092 0.229-0.14a32.69 32.69 0 0 0 1.904-1.324l0.308-0.234a31.978 31.978 0 0 0 3.564-3.179l0.206-0.212a32.165 32.165 0 0 0 1.539-1.751c0.075-0.099 0.153-0.2 0.233-0.3z m-522.168-9.603l-15.144-56.516a31.963 31.963 0 0 0-2.297-6.061c-6.656-13.313-21.959-20.573-36.895-16.566l-56.516 15.143 68.461-118.578c22.579 17.63 47.305 32.177 73.882 43.419a326.58 326.58 0 0 0 40.699 14.121l-72.19 125.038z m159.174-177.37c-70.517 0-136.813-27.461-186.676-77.323-49.863-49.863-77.324-116.16-77.324-186.677s27.461-136.813 77.324-186.676 116.159-77.324 186.676-77.324 136.813 27.461 186.677 77.324c49.862 49.863 77.323 116.16 77.323 186.677s-27.461 136.813-77.323 186.677c-49.863 49.862-116.16 77.322-186.677 77.322z m127.683 38.211c26.577-11.241 51.303-25.788 73.882-43.419l70.058 121.343-56.517-15.143c-17.069-4.577-34.618 5.557-39.191 22.627l-15.144 56.516-73.787-127.803a326.693 326.693 0 0 0 40.699-14.121z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineReward.defaultProps = {
  size: 18,
};

IconLineReward = React.memo ? React.memo(IconLineReward) : IconLineReward;

export default IconLineReward;
