/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineOpen = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M864 160H160c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h704c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM864 480H160c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h704c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM864 800H160c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h704c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconLineOpen.defaultProps = {
  size: 18,
};

IconLineOpen = React.memo ? React.memo(IconLineOpen) : IconLineOpen;

export default IconLineOpen;
