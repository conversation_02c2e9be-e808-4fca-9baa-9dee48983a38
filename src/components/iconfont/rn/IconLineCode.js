/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineCode = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M960 350.4c0-0.3 0-0.5-0.1-0.7 0-0.3 0-0.5-0.1-0.8 0-0.3-0.1-0.6-0.1-0.9 0-0.2 0-0.4-0.1-0.6 0-0.3-0.1-0.7-0.2-1 0-0.2-0.1-0.4-0.1-0.5-0.1-0.3-0.1-0.7-0.2-1 0-0.2-0.1-0.3-0.1-0.5-0.1-0.3-0.2-0.6-0.3-1-0.1-0.2-0.1-0.4-0.2-0.6l-0.3-0.9c-0.1-0.2-0.1-0.4-0.2-0.6-0.1-0.3-0.2-0.5-0.3-0.8-0.1-0.2-0.2-0.5-0.3-0.7s-0.2-0.4-0.3-0.7c-0.1-0.3-0.2-0.5-0.4-0.8l-0.3-0.6c-0.1-0.3-0.3-0.6-0.4-0.9-0.1-0.2-0.2-0.3-0.3-0.5-0.2-0.3-0.3-0.6-0.5-0.9-0.1-0.1-0.2-0.3-0.3-0.4l-0.6-0.9c-0.1-0.1-0.2-0.3-0.3-0.4l-0.6-0.9c-0.1-0.1-0.2-0.3-0.3-0.4-0.2-0.3-0.4-0.5-0.6-0.8-0.1-0.2-0.3-0.3-0.5-0.5s-0.4-0.4-0.6-0.7c-0.3-0.3-0.6-0.6-0.8-0.9-0.1-0.1-0.2-0.2-0.2-0.3l-192-192c-0.1-0.1-0.2-0.2-0.3-0.2-0.3-0.3-0.6-0.6-0.9-0.8l-0.6-0.6c-0.2-0.2-0.4-0.3-0.5-0.5-0.3-0.2-0.5-0.4-0.8-0.6-0.1-0.1-0.3-0.2-0.4-0.3l-0.9-0.6c-0.1-0.1-0.3-0.2-0.4-0.3l-0.9-0.6c-0.1-0.1-0.3-0.2-0.4-0.3-0.3-0.2-0.6-0.3-0.9-0.5-0.2-0.1-0.3-0.2-0.5-0.3-0.3-0.2-0.6-0.3-0.9-0.4l-0.6-0.3c-0.3-0.1-0.5-0.2-0.8-0.4-0.2-0.1-0.4-0.2-0.7-0.3-0.2-0.1-0.5-0.2-0.7-0.3-0.3-0.1-0.5-0.2-0.8-0.3-0.2-0.1-0.4-0.1-0.6-0.2l-0.9-0.3c-0.2-0.1-0.4-0.1-0.6-0.2l-0.9-0.3c-0.2 0-0.4-0.1-0.5-0.1-0.3-0.1-0.7-0.2-1-0.2-0.2 0-0.4-0.1-0.6-0.1-0.3-0.1-0.7-0.1-1-0.2-0.2 0-0.4-0.1-0.6-0.1-0.3 0-0.6-0.1-0.9-0.1-0.3 0-0.6 0-0.9-0.1H160c-52.9 0-96 43.1-96 96v576c0 52.9 43.1 96 96 96h704c52.9 0 96-43.1 96-96V352v-1.6zM864 832H160c-17.6 0-32-14.4-32-32V224c0-17.6 14.4-32 32-32h562.7L896 365.3V800c0 17.6-14.4 32-32 32z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M320 345.7c-15.3-8.8-34.9-3.6-43.7 11.7l-80 138.6s0 0.1-0.1 0.1c-0.2 0.4-0.4 0.7-0.6 1.1-0.1 0.1-0.1 0.3-0.2 0.4-0.1 0.3-0.3 0.6-0.4 0.9l-0.3 0.6-0.3 0.6c-0.1 0.3-0.2 0.6-0.4 0.9 0 0.1-0.1 0.2-0.1 0.3-0.1 0.4-0.3 0.8-0.4 1.2-0.9 3-1.4 6-1.5 9v1.4c0.1 3 0.6 6 1.5 9 0.1 0.4 0.3 0.8 0.4 1.2 0 0.1 0.1 0.2 0.1 0.3 0.1 0.3 0.2 0.6 0.4 0.9l0.3 0.6 0.3 0.6c0.1 0.3 0.3 0.6 0.4 0.9 0.1 0.1 0.1 0.3 0.2 0.4 0.2 0.4 0.4 0.7 0.6 1.1 0 0 0 0.1 0.1 0.1l80 138.6c5.9 10.3 16.7 16 27.7 16 5.4 0 10.9-1.4 16-4.3 15.3-8.8 20.5-28.4 11.7-43.7L261 512l70.8-122.6c8.7-15.3 3.5-34.8-11.8-43.7zM832 511.3c-0.1-3-0.6-6-1.5-9v-0.1c-0.1-0.4-0.3-0.8-0.4-1.2 0-0.1-0.1-0.2-0.1-0.4-0.1-0.3-0.2-0.6-0.4-0.9-0.1-0.2-0.2-0.4-0.3-0.7l-0.3-0.6c-0.1-0.3-0.3-0.6-0.4-0.9-0.1-0.1-0.1-0.3-0.2-0.4-0.2-0.4-0.4-0.7-0.6-1.1 0 0 0-0.1-0.1-0.1l-80-138.6c-8.8-15.3-28.4-20.6-43.7-11.7-15.3 8.8-20.5 28.4-11.7 43.7L763 512l-70.8 122.6c-8.8 15.3-3.6 34.9 11.7 43.7 5 2.9 10.5 4.3 16 4.3 11.1 0 21.8-5.7 27.7-16l80-138.6s0-0.1 0.1-0.1c0.2-0.4 0.4-0.7 0.6-1.1 0.1-0.1 0.1-0.3 0.2-0.4 0.1-0.3 0.3-0.6 0.4-0.9l0.3-0.6 0.3-0.6c0.1-0.3 0.2-0.6 0.4-0.9 0-0.1 0.1-0.2 0.1-0.4 0.1-0.4 0.3-0.8 0.4-1.2 0.9-2.9 1.4-6 1.5-9v-0.7c0.1-0.3 0.1-0.6 0.1-0.8zM608 345.7c-15.3-8.8-34.9-3.6-43.7 11.7l-160 277.1c-8.8 15.3-3.6 34.9 11.7 43.7 5 2.9 10.5 4.3 16 4.3 11.1 0 21.8-5.7 27.7-16l160-277.1c8.8-15.3 3.6-34.8-11.7-43.7z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineCode.defaultProps = {
  size: 18,
};

IconLineCode = React.memo ? React.memo(IconLineCode) : IconLineCode;

export default IconLineCode;
