/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineCollect = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M958.414 407.625a32 32 0 0 0-25.763-21.712l-270.749-39.775L540.757 97.962a32.002 32.002 0 0 0-57.514 0L362.097 346.137 91.349 385.913a32 32 0 0 0-17.81 54.452l196.123 193.281-46.313 273.002a32.003 32.003 0 0 0 31.552 37.353c5.14 0 10.301-1.237 15.016-3.743L512 811.583l242.083 128.674a32 32 0 0 0 46.568-33.61l-46.313-273.002 196.124-193.281a32 32 0 0 0 7.952-32.739zM697.538 599.766a32.002 32.002 0 0 0-9.087 28.145l38.221 225.297L527.02 747.087a31.994 31.994 0 0 0-15.02-3.743 31.99 31.99 0 0 0-15.019 3.743l-199.652 106.12 38.22-225.297a32 32 0 0 0-9.088-28.145L164.236 439.892 388.1 407.004a32 32 0 0 0 24.105-17.623L512 184.947l99.794 204.434a32.001 32.001 0 0 0 24.105 17.623l223.864 32.888-162.225 159.874z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconLineCollect.defaultProps = {
  size: 18,
};

IconLineCollect = React.memo ? React.memo(IconLineCollect) : IconLineCollect;

export default IconLineCollect;
