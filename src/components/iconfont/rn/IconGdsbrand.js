/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconGdsbrand = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M713.5 142.8H305.3l-207 243.5 414.1 513.5L926.9 386 713.5 142.8zM189.3 387.5l148.5-174.7h344.1l153.7 175.1-323 400.5-323.3-400.9z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M512.3 558.1L402.8 448.6l-49.5 49.5L512 656.8l158.3-156-49.2-49.8z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconGdsbrand.defaultProps = {
  size: 18,
};

IconGdsbrand = React.memo ? React.memo(IconGdsbrand) : IconGdsbrand;

export default IconGdsbrand;
