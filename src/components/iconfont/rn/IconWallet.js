/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconWallet = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M691.797333 106.666667C827.904 106.666667 938.666667 219.946667 938.666667 359.210667v305.578666c0 139.264-110.762667 252.544-246.869334 252.544a28.8 28.8 0 0 1-28.416-29.098666c0-16.085333 12.714667-29.098667 28.416-29.098667 104.746667 0 189.994667-87.168 189.994667-194.346667V420.906667h-140.117333c-45.994667 0-83.498667 38.4-83.541334 85.504 0 47.104 37.546667 85.418667 83.541334 85.461333h58.24c15.701333 0 28.416 13.056 28.416 29.098667a28.8 28.8 0 0 1-28.416 29.098666h-58.24c-77.397333 0-140.373333-64.469333-140.416-143.658666 0-79.232 63.018667-143.658667 140.416-143.701334h140.117333v-3.498666c0-107.178667-85.248-194.346667-189.994667-194.346667h-359.68c-84.394667 0-155.221333 56.96-179.925333 134.997333h374.954667c15.701333 0 28.416 13.056 28.416 29.098667a28.757333 28.757333 0 0 1-28.416 29.141333H142.336l-0.128 1.109334v305.578666c0 107.178667 85.205333 194.346667 189.952 194.346667h180.906667c15.744 0 28.458667 13.013333 28.458666 29.098667a28.8 28.8 0 0 1-28.416 29.098666H332.16C196.053333 917.333333 85.333333 804.053333 85.333333 664.789333V359.253333C85.333333 219.946667 196.053333 106.666667 332.16 106.666667z m68.992 368c15.701333 0 28.416 13.056 28.416 29.098666a28.8 28.8 0 0 1-28.416 29.098667h-12.928a28.8 28.8 0 0 1-28.458666-29.098667c0-16.042667 12.757333-29.098667 28.458666-29.098666z"
        fill={getIconColor(color, 0, '#000000')}
      />
    </Svg>
  );
};

IconWallet.defaultProps = {
  size: 18,
};

IconWallet = React.memo ? React.memo(IconWallet) : IconWallet;

export default IconWallet;
