/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconHomeFill = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M549.61981 133.022476l319.683047 203.605334A70.851048 70.851048 0 0 1 902.095238 396.361143v434.883047A70.89981 70.89981 0 0 1 831.146667 902.095238l-248.222477-0.024381V617.910857h-153.673142v284.16L192.828952 902.095238A70.89981 70.89981 0 0 1 121.904762 831.24419V390.241524c0-24.527238 12.678095-47.299048 33.54819-60.220953l318.659048-197.485714a70.972952 70.972952 0 0 1 75.50781 0.487619z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconHomeFill.defaultProps = {
  size: 18,
};

IconHomeFill = React.memo ? React.memo(IconHomeFill) : IconHomeFill;

export default IconHomeFill;
