/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconBiyejieye = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M834.56 552.96v189.44c0 20.48-5.12 35.84-20.48 51.2-76.8 81.92-184.32 122.88-296.96 122.88-112.64 5.12-220.16-40.96-296.96-122.88-10.24-15.36-20.48-30.72-20.48-51.2v-189.44l261.12 102.4c20.48 10.24 35.84 10.24 56.32 10.24 20.48 0 40.96-5.12 56.32-10.24l261.12-102.4z"
        fill={getIconColor(color, 0, '#3399FF')}
      />
      <Path
        d="M977.92 276.48L547.84 102.4c-10.24-5.12-20.48-5.12-30.72-5.12-10.24 0-20.48 0-25.6 5.12L66.56 276.48C25.6 291.84 10.24 332.8 25.6 373.76c5.12 20.48 20.48 30.72 40.96 40.96l430.08 174.08c15.36 10.24 35.84 10.24 56.32 0l424.96-174.08c35.84-15.36 56.32-56.32 40.96-97.28-10.24-20.48-25.6-35.84-40.96-40.96zM35.84 793.6c-20.48 0-35.84-15.36-35.84-35.84v-240.64c0-20.48 15.36-35.84 35.84-35.84s35.84 15.36 35.84 35.84v240.64c5.12 20.48-15.36 35.84-35.84 35.84z"
        fill={getIconColor(color, 1, '#3399FF')}
      />
    </Svg>
  );
};

IconBiyejieye.defaultProps = {
  size: 18,
};

IconBiyejieye = React.memo ? React.memo(IconBiyejieye) : IconBiyejieye;

export default IconBiyejieye;
