/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconCalculator = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M298.666667 85.333333h426.666666a85.333333 85.333333 0 0 1 85.333334 85.333334v682.666666a85.333333 85.333333 0 0 1-85.333334 85.333334H298.666667a85.333333 85.333333 0 0 1-85.333334-85.333334V170.666667a85.333333 85.333333 0 0 1 85.333334-85.333334m0 85.333334v170.666666h426.666666V170.666667H298.666667m0 256v85.333333h85.333333v-85.333333H298.666667m170.666666 0v85.333333h85.333334v-85.333333h-85.333334m170.666667 0v85.333333h85.333333v-85.333333h-85.333333m-341.333333 170.666666v85.333334h85.333333v-85.333334H298.666667m170.666666 0v85.333334h85.333334v-85.333334h-85.333334m170.666667 0v85.333334h85.333333v-85.333334h-85.333333m-341.333333 170.666667v85.333333h85.333333v-85.333333H298.666667m170.666666 0v85.333333h85.333334v-85.333333h-85.333334m170.666667 0v85.333333h85.333333v-85.333333h-85.333333z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconCalculator.defaultProps = {
  size: 18,
};

IconCalculator = React.memo ? React.memo(IconCalculator) : IconCalculator;

export default IconCalculator;
