/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconPassword = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M696.917333 85.333333C841.557333 85.333333 938.666667 186.794667 938.666667 337.749333v348.501334C938.666667 837.205333 841.472 938.666667 696.874667 938.666667h-229.546667a32 32 0 0 1 0-64h229.546667c107.989333 0 177.792-73.941333 177.792-188.416V337.749333c0-114.474667-69.802667-188.416-177.749334-188.416H327.04C219.093333 149.333333 149.333333 223.274667 149.333333 337.749333v348.501334c0 114.474667 69.76 188.416 177.706667 188.416a32 32 0 0 1 0 64C182.442667 938.666667 85.333333 837.205333 85.333333 686.250667V337.749333C85.333333 186.794667 182.442667 85.333333 327.04 85.333333zM377.173333 400.981333c50.048 0 91.904 33.450667 105.728 78.976h242.858667a32 32 0 0 1 32 32v79.018667a32 32 0 0 1-64 0V544h-56.704v47.018667a32 32 0 0 1-64 0V544h-90.154667a110.72 110.72 0 0 1-105.728 79.018667 111.104 111.104 0 0 1-101.248-156.544 31.914667 31.914667 0 0 1 42.325334-16.042667 31.914667 31.914667 0 0 1 16.042666 42.282667A47.061333 47.061333 0 1 0 424.192 512c0-25.898667-21.077333-46.933333-47.018667-46.933333a32 32 0 0 1 0-64z"
        fill={getIconColor(color, 0, '#000000')}
      />
    </Svg>
  );
};

IconPassword.defaultProps = {
  size: 18,
};

IconPassword = React.memo ? React.memo(IconPassword) : IconPassword;

export default IconPassword;
