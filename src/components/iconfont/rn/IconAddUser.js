/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconAddUser = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M589.824 313.770667c0-92.16-74.666667-167.125333-166.528-167.125334-91.818667 0-166.485333 74.965333-166.485333 167.125334s74.666667 167.125333 166.485333 167.125333c91.861333 0 166.528-74.965333 166.528-167.125333z m61.056 0c0 125.952-102.058667 228.437333-227.584 228.437333-125.44 0-227.541333-102.485333-227.541333-228.437333S297.813333 85.333333 423.253333 85.333333c125.525333 0 227.584 102.485333 227.584 228.437334z m255.914667 114.56H851.626667V374.784a31.957333 31.957333 0 0 0-31.872-32 31.957333 31.957333 0 0 0-31.872 32v53.546667h-55.04a31.957333 31.957333 0 0 0 0 64h55.04v53.504c0 17.664 14.293333 32 31.872 32a31.957333 31.957333 0 0 0 31.872-32V492.373333h55.125333a31.957333 31.957333 0 0 0 0-64zM423.253333 624.64c-145.066667 0-337.962667 16.213333-337.962666 156.586667 0 42.026667 19.456 98.773333 112.213333 130.304a30.634667 30.634667 0 0 0 19.626667-58.026667c-63.488-21.589333-70.826667-51.584-70.826667-72.277333 0-63.232 93.226667-95.317333 276.949333-95.317334 183.765333 0 276.949333 32.341333 276.949334 96.170667 0 63.274667-93.184 95.317333-276.906667 95.317333-14.08 0-27.861333-0.170667-41.429333-0.597333-16.341333 0.128-30.890667 12.842667-31.36 29.738667a30.592 30.592 0 0 0 29.568 31.573333c14.165333 0.341333 28.586667 0.554667 43.178666 0.554667 145.109333 0 338.005333-16.256 338.005334-156.586667 0-157.44-254.421333-157.44-338.005334-157.44z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconAddUser.defaultProps = {
  size: 18,
};

IconAddUser = React.memo ? React.memo(IconAddUser) : IconAddUser;

export default IconAddUser;
