/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineShare = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M896 544c-17.673 0-32 14.327-32 32v256c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h256c17.673 0 32-14.327 32-32s-14.327-32-32-32H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V576c0-17.673-14.327-32-32-32z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M927.996 127.828a31.33 31.33 0 0 0-0.036-1.405c-0.013-0.271-0.038-0.538-0.058-0.807-0.019-0.25-0.033-0.501-0.057-0.751-0.032-0.328-0.075-0.652-0.117-0.977-0.024-0.188-0.044-0.376-0.072-0.563-0.051-0.35-0.113-0.696-0.176-1.042-0.029-0.163-0.055-0.327-0.088-0.49-0.069-0.35-0.148-0.695-0.228-1.041-0.037-0.161-0.072-0.323-0.112-0.484a32.505 32.505 0 0 0-0.269-0.998c-0.049-0.173-0.095-0.346-0.147-0.518a29.848 29.848 0 0 0-0.297-0.92c-0.065-0.196-0.129-0.392-0.198-0.586-0.098-0.274-0.203-0.545-0.308-0.816-0.087-0.224-0.172-0.448-0.264-0.67-0.098-0.237-0.203-0.471-0.306-0.705-0.111-0.251-0.221-0.501-0.339-0.749-0.096-0.202-0.197-0.401-0.297-0.601a32.914 32.914 0 0 0-0.417-0.815c-0.093-0.174-0.191-0.344-0.287-0.515a31.28 31.28 0 0 0-0.49-0.852c-0.095-0.158-0.196-0.313-0.294-0.47-0.178-0.284-0.356-0.568-0.544-0.848-0.106-0.157-0.216-0.31-0.325-0.466-0.188-0.27-0.376-0.54-0.573-0.806-0.129-0.174-0.265-0.342-0.397-0.512-0.185-0.238-0.367-0.478-0.559-0.712-0.187-0.227-0.382-0.447-0.574-0.669-0.149-0.171-0.292-0.345-0.445-0.514a32.155 32.155 0 0 0-2.246-2.246c-0.174-0.158-0.354-0.307-0.532-0.46-0.216-0.187-0.429-0.377-0.65-0.558-0.239-0.197-0.484-0.383-0.728-0.572-0.165-0.128-0.328-0.259-0.496-0.384-0.272-0.202-0.549-0.394-0.825-0.587-0.148-0.103-0.294-0.209-0.444-0.309a29.825 29.825 0 0 0-0.871-0.558c-0.149-0.093-0.296-0.188-0.446-0.279a31.845 31.845 0 0 0-0.875-0.503c-0.164-0.092-0.326-0.185-0.491-0.274-0.279-0.149-0.56-0.291-0.842-0.431-0.19-0.095-0.379-0.191-0.572-0.283-0.259-0.123-0.52-0.238-0.782-0.353-0.223-0.099-0.445-0.198-0.671-0.292-0.234-0.097-0.47-0.187-0.705-0.278-0.259-0.1-0.517-0.2-0.779-0.294-0.208-0.074-0.418-0.142-0.627-0.212-0.291-0.097-0.582-0.194-0.877-0.283-0.188-0.057-0.378-0.108-0.567-0.161a30.17 30.17 0 0 0-0.95-0.256c-0.179-0.044-0.359-0.083-0.538-0.124a29.392 29.392 0 0 0-0.988-0.216c-0.184-0.036-0.368-0.066-0.552-0.099-0.326-0.058-0.651-0.117-0.98-0.165-0.214-0.032-0.429-0.055-0.644-0.082-0.298-0.038-0.596-0.078-0.897-0.108-0.291-0.029-0.582-0.046-0.874-0.066-0.229-0.016-0.456-0.038-0.686-0.049a32.743 32.743 0 0 0-1.598-0.04H640c-17.673 0-32 14.327-32 32s14.327 32 32 32h178.746L489.373 489.373c-12.497 12.497-12.497 32.758 0 45.254C495.621 540.876 503.811 544 512 544c8.189 0 16.379-3.124 22.627-9.373L864 205.254V384c0 17.673 14.327 32 32 32s32-14.327 32-32V128c0-0.058-0.004-0.114-0.004-0.172z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineShare.defaultProps = {
  size: 18,
};

IconLineShare = React.memo ? React.memo(IconLineShare) : IconLineShare;

export default IconLineShare;
