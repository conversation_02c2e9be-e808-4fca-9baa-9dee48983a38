/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconIdCard = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1152 1024" width={size} height={size} {...rest}>
      <Path
        d="M0 192h1152c0-70.6-57.4-128-128-128H128C57.4 64 0 121.4 0 192z m0 64v576c0 70.6 57.4 128 128 128h896c70.6 0 128-57.4 128-128V256H0z m128 554.6c0-59 47.8-106.6 106.6-106.6h234.8c59 0 106.6 47.8 106.6 106.6 0 11.8-9.6 21.4-21.4 21.4H149.4c-11.8 0-21.4-9.6-21.4-21.4zM352 384a128 128 0 1 1 0 256 128 128 0 1 1 0-256z m352 32c0-17.6 14.4-32 32-32h256c17.6 0 32 14.4 32 32s-14.4 32-32 32H736c-17.6 0-32-14.4-32-32z m0 128c0-17.6 14.4-32 32-32h256c17.6 0 32 14.4 32 32s-14.4 32-32 32H736c-17.6 0-32-14.4-32-32z m0 128c0-17.6 14.4-32 32-32h256c17.6 0 32 14.4 32 32s-14.4 32-32 32H736c-17.6 0-32-14.4-32-32z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconIdCard.defaultProps = {
  size: 18,
};

IconIdCard = React.memo ? React.memo(IconIdCard) : IconIdCard;

export default IconIdCard;
