/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconHouse = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1152 1024" width={size} height={size} {...rest}>
      <Path
        d="M1151.6 511c0 36-30 64.2-64 64.2h-64l1.4 320.4c0 5.4-0.4 10.8-1 16.2V944c0 44.2-35.8 80-80 80h-32c-2.2 0-4.4 0-6.6-0.2-2.8 0.2-5.6 0.2-8.4 0.2H784c-44.2 0-80-35.8-80-80v-176c0-35.4-28.6-64-64-64h-128c-35.4 0-64 28.6-64 64v176c0 44.2-35.8 80-80 80h-111.8c-3 0-6-0.2-9-0.4-2.4 0.2-4.8 0.4-7.2 0.4h-32c-44.2 0-80-35.8-80-80V720c0-1.8 0-3.8 0.2-5.6v-139.2H64c-36 0-64-28-64-64.2 0-18 6-34 20-48L532.8 16c14-14 30-16 44-16s30 4 42 14l510.8 449c16 14 24 30 22 48z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconHouse.defaultProps = {
  size: 18,
};

IconHouse = React.memo ? React.memo(IconHouse) : IconHouse;

export default IconHouse;
