/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineRanking = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M480 800h64c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96h-64c-52.935 0-96 43.065-96 96v512c0 52.935 43.065 96 96 96z m-32-608c0-17.645 14.355-32 32-32h64c17.645 0 32 14.355 32 32v512c0 17.645-14.355 32-32 32h-64c-17.645 0-32-14.355-32-32V192zM160 800h64c52.935 0 96-43.065 96-96V352c0-52.935-43.065-96-96-96h-64c-52.935 0-96 43.065-96 96v352c0 52.935 43.065 96 96 96z m-32-448c0-17.645 14.355-32 32-32h64c17.645 0 32 14.355 32 32v352c0 17.645-14.355 32-32 32h-64c-17.645 0-32-14.355-32-32V352zM864 416h-64c-52.935 0-96 43.065-96 96v192c0 52.935 43.065 96 96 96h64c52.935 0 96-43.065 96-96V512c0-52.935-43.065-96-96-96z m32 288c0 17.645-14.355 32-32 32h-64c-17.645 0-32-14.355-32-32V512c0-17.645 14.355-32 32-32h64c17.645 0 32 14.355 32 32v192zM928 864H96c-17.673 0-32 14.327-32 32s14.327 32 32 32h832c17.673 0 32-14.327 32-32s-14.327-32-32-32z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconLineRanking.defaultProps = {
  size: 18,
};

IconLineRanking = React.memo ? React.memo(IconLineRanking) : IconLineRanking;

export default IconLineRanking;
