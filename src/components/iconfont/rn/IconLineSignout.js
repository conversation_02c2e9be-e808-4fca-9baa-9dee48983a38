/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineSignout = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M576 864H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h384c17.673 0 32-14.327 32-32s-14.327-32-32-32H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h384c17.673 0 32-14.327 32-32s-14.327-32-32-32z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M920.323 532.782c0.135-0.158 0.273-0.312 0.405-0.472 0.226-0.274 0.44-0.554 0.655-0.834 0.1-0.13 0.203-0.258 0.302-0.389 0.221-0.296 0.431-0.598 0.64-0.9 0.086-0.124 0.174-0.246 0.258-0.371 0.205-0.306 0.401-0.616 0.594-0.927 0.081-0.131 0.165-0.26 0.245-0.391 0.183-0.304 0.357-0.611 0.529-0.919 0.084-0.15 0.169-0.298 0.251-0.449 0.155-0.289 0.302-0.581 0.447-0.873 0.09-0.181 0.182-0.362 0.269-0.545 0.126-0.265 0.244-0.533 0.362-0.801 0.096-0.217 0.193-0.434 0.284-0.654 0.099-0.238 0.191-0.478 0.283-0.718 0.099-0.256 0.198-0.511 0.29-0.769 0.075-0.209 0.143-0.42 0.213-0.63a33.1 33.1 0 0 0 0.283-0.876c0.056-0.187 0.107-0.375 0.16-0.562 0.09-0.318 0.178-0.635 0.258-0.957 0.043-0.174 0.081-0.349 0.121-0.524 0.077-0.333 0.153-0.666 0.219-1.002 0.035-0.177 0.063-0.356 0.095-0.533 0.06-0.332 0.12-0.664 0.169-0.999 0.03-0.204 0.052-0.41 0.078-0.615 0.039-0.309 0.081-0.617 0.111-0.928 0.027-0.271 0.042-0.543 0.062-0.815 0.018-0.248 0.041-0.495 0.053-0.744 0.026-0.527 0.04-1.055 0.04-1.582v-0.001c0-0.527-0.014-1.054-0.04-1.581-0.012-0.251-0.036-0.498-0.054-0.748-0.02-0.271-0.035-0.541-0.062-0.811-0.031-0.313-0.072-0.622-0.112-0.932-0.026-0.203-0.048-0.407-0.078-0.61-0.049-0.337-0.11-0.671-0.17-1.004-0.032-0.176-0.06-0.352-0.094-0.528a30.045 30.045 0 0 0-0.221-1.008c-0.04-0.172-0.077-0.345-0.119-0.517-0.081-0.324-0.17-0.645-0.261-0.966-0.052-0.184-0.102-0.369-0.157-0.552-0.09-0.298-0.188-0.593-0.286-0.887-0.069-0.207-0.136-0.414-0.209-0.619-0.094-0.263-0.195-0.522-0.295-0.782-0.091-0.235-0.181-0.471-0.278-0.705-0.093-0.225-0.192-0.446-0.29-0.668-0.116-0.263-0.232-0.527-0.356-0.787-0.089-0.188-0.184-0.374-0.277-0.56a32.957 32.957 0 0 0-0.439-0.857c-0.085-0.158-0.174-0.313-0.262-0.469a29.758 29.758 0 0 0-0.517-0.898c-0.085-0.141-0.174-0.278-0.261-0.418a31.86 31.86 0 0 0-0.577-0.9c-0.092-0.137-0.188-0.269-0.282-0.404a34.13 34.13 0 0 0-0.616-0.867c-0.109-0.147-0.224-0.289-0.336-0.433-0.204-0.265-0.407-0.53-0.621-0.79-0.153-0.186-0.313-0.365-0.47-0.548-0.182-0.212-0.361-0.426-0.549-0.634a33.396 33.396 0 0 0-0.899-0.946c-0.064-0.065-0.122-0.132-0.187-0.196l-224-224c-12.498-12.497-32.758-12.497-45.256 0-12.496 12.497-12.496 32.758 0 45.255L818.744 480H384c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h434.745L649.372 713.372c-12.496 12.497-12.496 32.759 0 45.256C655.621 764.876 663.811 768 672 768s16.379-3.124 22.628-9.372l224-224c0.139-0.139 0.267-0.283 0.403-0.424 0.227-0.235 0.456-0.469 0.676-0.711 0.212-0.233 0.412-0.473 0.616-0.711z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineSignout.defaultProps = {
  size: 18,
};

IconLineSignout = React.memo ? React.memo(IconLineSignout) : IconLineSignout;

export default IconLineSignout;
