/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconMoneyCheck = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1152 1024" width={size} height={size} {...rest}>
      <Path
        d="M128 128C57.4 128 0 185.4 0 256v512c0 70.6 57.4 128 128 128h896c70.6 0 128-57.4 128-128V256c0-70.6-57.4-128-128-128H128z m96 320h320c17.6 0 32 14.4 32 32s-14.4 32-32 32H224c-17.6 0-32-14.4-32-32s14.4-32 32-32zM192 672c0-17.6 14.4-32 32-32h704c17.6 0 32 14.4 32 32s-14.4 32-32 32H224c-17.6 0-32-14.4-32-32z m560-352h160c26.6 0 48 21.4 48 48v96c0 26.6-21.4 48-48 48h-160c-26.6 0-48-21.4-48-48v-96c0-26.6 21.4-48 48-48z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconMoneyCheck.defaultProps = {
  size: 18,
};

IconMoneyCheck = React.memo ? React.memo(IconMoneyCheck) : IconMoneyCheck;

export default IconMoneyCheck;
