/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconZhuceshibai = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1391 1024" width={size} height={size} {...rest}>
      <Path
        d="M1050.964505 234.645126a75.119845 75.119845 0 0 0-53.597204-21.979961H625.936155a75.138485 75.138485 0 0 0-53.558679 21.999845 75.137243 75.137243 0 0 0-21.999845 53.557437V873.879612a75.139728 75.139728 0 0 0 21.999845 53.557437A75.138485 75.138485 0 0 0 625.936155 949.436893h371.431146a75.121087 75.121087 0 0 0 53.597204-21.981204 75.113631 75.113631 0 0 0 22.024699-53.576077V288.222447a75.111146 75.111146 0 0 0-22.024699-53.577321z"
        fill={getIconColor(color, 0, '#DFEFF7')}
      />
      <Path
        d="M606.299961 873.878369v-585.655922a75.137243 75.137243 0 0 1 21.999845-53.557437 75.140971 75.140971 0 0 1 53.559922-21.999845h-55.92233a75.138485 75.138485 0 0 0-53.55868 21.999845 75.137243 75.137243 0 0 0-21.999844 53.557437V873.879612a75.139728 75.139728 0 0 0 21.999844 53.557437A75.138485 75.138485 0 0 0 625.937398 949.436893h55.92233a75.143456 75.143456 0 0 1-53.559922-21.999844 75.142214 75.142214 0 0 1-21.999845-53.55868z"
        fill={getIconColor(color, 1, '#C7E6F9')}
      />
      <Path
        d="M299.99099 384.520699c-0.69965-7.826641-7.61165-13.602796-15.437048-12.903146a14.634252 14.634252 0 0 0-9.565204 5.069049 13.669903 13.669903 0 0 0-3.181359 10.323262 14.610641 14.610641 0 0 0 5.142369 9.563961 13.103223 13.103223 0 0 0 9.009708 3.385165h1.125903c7.827883-0.69965 13.604039-7.61165 12.905631-15.438291z m-8.669204 11.414369a11.539883 11.539883 0 0 1-12.766446-1.399301 10.566835 10.566835 0 0 1-4.053748-7.805515 11.546097 11.546097 0 0 1 2.593554-8.49398 10.587961 10.587961 0 0 1 7.82664-4.056233h1.033942a11.455379 11.455379 0 0 1 11.516272 10.48233 11.534913 11.534913 0 0 1-6.150214 11.272699zM1199.57499 189.799146c-0.970563-10.827806-10.534524-18.822214-21.361087-17.852894a20.235184 20.235184 0 0 0-13.232466 7.012661 18.904233 18.904233 0 0 0-4.404194 14.283805 20.222757 20.222757 0 0 0 7.117048 13.233709 18.125049 18.125049 0 0 0 12.465709 4.682563h1.559612c10.826563-0.965592 18.820971-10.529553 17.855378-21.359844z m-11.995961 15.793708a15.966447 15.966447 0 0 1-17.664-1.936155 14.620583 14.620583 0 0 1-5.610874-10.801709 15.971417 15.971417 0 0 1 3.590214-11.753631 14.64668 14.64668 0 0 1 10.829048-5.612116h1.431612c8.283961-0.074563 15.230757 6.245903 15.932893 14.503767a15.957748 15.957748 0 0 1-8.508893 15.599844zM168.874252 171.866718a3.060816 3.060816 0 0 1-3.057087-3.057087v-19.769165l-13.961942 13.961942a3.134136 3.134136 0 0 1-4.431534-4.430291l13.964428-13.961942h-19.803962a3.069515 3.069515 0 1 1 0-6.142758h19.803962l-13.964428-13.795417a2.914175 2.914175 0 0 1 0-4.430291 2.915417 2.915417 0 0 1 2.21701-1.016544c0.851262 0 1.656544 0.369087 2.214524 1.016544l13.961942 13.827728v-19.771651a3.054602 3.054602 0 1 1 5.97499 0v19.771651l14.097398-13.964427a2.922874 2.922874 0 0 1 4.431534 0 2.920388 2.920388 0 0 1 0 4.431534l-13.964427 13.96567h19.769165a3.069515 3.069515 0 1 1 0 6.140271h-19.769165l13.964427 13.96567c0.791612 0.787883 1.098563 1.942369 0.810253 3.023534a3.14035 3.14035 0 0 1-2.214524 2.21701 3.136621 3.136621 0 0 1-3.027263-0.813981l-14.097398-13.994252v19.769165c0.335534 1.712466-1.373204 3.055845-3.053359 3.055845h0.135456zM120.141049 910.39565h-8.449243v-8.450485c0-1.405515-1.137087-2.542602-2.542602-2.542602s-2.542602 1.137087-2.542602 2.542602v8.450485h-8.449243a2.542602 2.542602 0 1 0 0 5.085204h8.449243v8.450486a2.527689 2.527689 0 0 0 2.530175 2.527689l0.026097-0.109359a2.536388 2.536388 0 0 0 2.530175-2.531418v-8.336155h8.449242a2.543845 2.543845 0 0 0-0.001242-5.086447zM1199.655767 819.01235c0 16.750602-11.342291 29.92466-25.368854 29.92466 14.067573 0 25.368854 13.39899 25.368854 29.92466 0-16.750602 11.343534-29.92466 25.366369-29.92466-14.068816 0-25.366369-13.397748-25.366369-29.92466z m0 0"
        fill={getIconColor(color, 2, '#CCCCCC')}
      />
      <Path
        d="M1254.105476 1018.617786H1180.060583a7.044971 7.044971 0 1 1 0-14.089941h74.044893a7.044971 7.044971 0 1 1 0 14.089941zM1126.209864 1018.617786H141.251107a7.044971 7.044971 0 1 1 0-14.089941h984.958757a7.044971 7.044971 0 1 1 0 14.089941zM109.147961 1018.617786H85.328777a7.044971 7.044971 0 1 1 0-14.089941H109.147961a7.044971 7.044971 0 1 1 0 14.089941z"
        fill={getIconColor(color, 3, '#7ECAFC')}
      />
      <Path
        d="M997.845748 956.483107l-0.524428-0.002486H625.936155c-21.764971 0.10066-43.114874-8.643107-58.539495-24.061514-15.420893-15.425864-24.190757-36.77701-24.065243-58.581748V288.222447c-0.126757-21.762485 8.64435-43.113631 24.062758-58.538253 15.315262-15.309049 36.473786-24.065243 58.114485-24.065243l0.467262 0.001243h371.390136l0.524427-0.001243c21.597204 0 42.744544 8.74501 58.053593 24.044117 15.434563 15.41965 24.215612 36.779495 24.088854 58.601631v108.475651a7.044971 7.044971 0 1 1-14.089942 0v-108.515418c0.104388-18.12132-7.170485-35.817631-19.959301-48.592777-12.78633-12.777631-30.50501-20.067417-48.57165-19.918291H625.936155c-18.100194-0.135456-35.797748 7.161786-48.577864 19.936932-12.773903 12.778874-20.040078 30.468971-19.935689 48.534369v585.696932c-0.104388 18.105165 7.161786 35.796505 19.936932 48.576621 12.686913 12.681942 30.219184 19.936932 48.150369 19.936932l0.386485-0.001242H997.368544l0.436194 0.001242c17.977165 0 35.498252-7.246291 48.181437-19.919533 12.790058-12.776388 20.064932-30.472699 19.960543-48.551767V561.087379a7.044971 7.044971 0 1 1 14.089942 0v312.79099c0.128 21.779883-8.654291 43.140971-24.090097 58.560621-15.307806 15.296621-36.458874 24.042874-58.100815 24.044117z"
        fill={getIconColor(color, 4, '#7ECAFC')}
      />
      <Path
        d="M1072.989204 474.307107a7.046214 7.046214 0 0 1-7.044971-7.044971v-34.485437a7.044971 7.044971 0 1 1 14.089942 0V467.262136a7.044971 7.044971 0 0 1-7.044971 7.044971z"
        fill={getIconColor(color, 5, '#7ECAFC')}
      />
      <Path
        d="M914.265476 321.635417a75.119845 75.119845 0 0 0-53.597204-21.979961H489.237126a75.138485 75.138485 0 0 0-53.558679 21.999845 75.137243 75.137243 0 0 0-21.999845 53.557437V936.015534a75.139728 75.139728 0 0 0 21.999845 53.557437A75.138485 75.138485 0 0 0 489.237126 1011.572816h371.431146a75.121087 75.121087 0 0 0 53.597204-21.981204 75.113631 75.113631 0 0 0 22.024699-53.576078V375.212738a75.111146 75.111146 0 0 0-22.024699-53.577321z"
        fill={getIconColor(color, 6, '#DFEFF7')}
      />
      <Path
        d="M914.265476 321.635417a75.119845 75.119845 0 0 0-53.597204-21.979961h-62.135922a75.119845 75.119845 0 0 1 53.597203 21.979961 75.116117 75.116117 0 0 1 22.024699 53.577321V936.015534a75.10866 75.10866 0 0 1-22.024699 53.576078A75.121087 75.121087 0 0 1 798.53235 1011.572816h62.135922a75.121087 75.121087 0 0 0 53.597204-21.981204 75.113631 75.113631 0 0 0 22.024699-53.576078V375.212738a75.111146 75.111146 0 0 0-22.024699-53.577321z"
        fill={getIconColor(color, 7, '#C7E6F9')}
      />
      <Path
        d="M861.146718 1018.619029l-0.524427-0.002485H489.237126c-21.777398 0.152854-43.114874-8.643107-58.539495-24.061515-15.420893-15.425864-24.190757-36.77701-24.065243-58.581747v-20.503612a7.046214 7.046214 0 0 1 14.089942 0v20.544621c-0.104388 18.105165 7.161786 35.796505 19.936932 48.576622 12.778874 12.773903 30.40932 20.033864 48.535612 19.935689h371.472155l0.436194 0.001243c17.977165 0 35.498252-7.245049 48.181437-19.919534 12.790058-12.776388 20.064932-30.472699 19.960544-48.551767V375.212738c0.104388-18.12132-7.170485-35.817631-19.959301-48.592777-12.785087-12.777631-30.480155-20.004039-48.571651-19.919534H489.237126c-18.133748-0.105631-35.797748 7.160544-48.577864 19.936932-12.773903 12.778874-20.040078 30.468971-19.935689 48.534369v193.16567a7.046214 7.046214 0 0 1-14.089942 0V375.212738c-0.126757-21.762485 8.64435-43.113631 24.062757-58.538253 15.425864-15.420893 36.816777-24.199456 58.582991-24.064H860.669515l0.524427-0.001242c21.597204 0 42.744544 8.74501 58.053592 24.042874 15.434563 15.41965 24.215612 36.779495 24.088854 58.601631V936.015534c0.128 21.779883-8.654291 43.140971-24.090097 58.560621-15.306563 15.295379-36.458874 24.041631-58.099573 24.042874z"
        fill={getIconColor(color, 8, '#7ECAFC')}
      />
      <Path
        d="M330.61033 751.17732m-161.754718 0a161.754718 161.754718 0 1 0 323.509437 0 161.754718 161.754718 0 1 0-323.509437 0Z"
        fill={getIconColor(color, 9, '#7ECAFC')}
      />
      <Path
        d="M830.240311 780.791301H564.630369a7.044971 7.044971 0 1 1 0-14.089942h265.609942a7.044971 7.044971 0 1 1 0 14.089942zM830.240311 688.019883H564.630369a7.044971 7.044971 0 1 1 0-14.089941h265.609942a7.044971 7.044971 0 1 1 0 14.089941zM830.240311 595.248466H484.141981a7.044971 7.044971 0 1 1 0-14.089942h346.09833a7.044971 7.044971 0 1 1 0 14.089942zM830.240311 502.477049H484.141981a7.046214 7.046214 0 0 1 0-14.089942h346.09833a7.046214 7.046214 0 0 1 0 14.089942z"
        fill={getIconColor(color, 10, '#7ECAFC')}
      />
      <Path
        d="M424.684117 845.248621c-4.916194 4.917437-12.885748 4.917437-17.801942 0L236.536544 674.905476c-4.916194-4.914951-4.916194-12.88699 0-17.803185 4.916194-4.912466 12.884505-4.916194 17.800699 0l170.345631 170.346874c4.916194 4.916194 4.916194 12.884505 0.001243 17.799456z"
        fill={getIconColor(color, 11, '#FFFFFF')}
      />
      <Path
        d="M236.536544 845.248621c-4.916194-4.914951-4.917437-12.883262 0-17.799456l170.345631-170.346874c4.916194-4.916194 12.885748-4.912466 17.800699 0 4.916194 4.916194 4.917437 12.884505 0 17.803185L254.337243 845.248621c-4.916194 4.917437-12.885748 4.917437-17.800699 0z"
        fill={getIconColor(color, 12, '#FFFFFF')}
      />
    </Svg>
  );
};

IconZhuceshibai.defaultProps = {
  size: 18,
};

IconZhuceshibai = React.memo ? React.memo(IconZhuceshibai) : IconZhuceshibai;

export default IconZhuceshibai;
