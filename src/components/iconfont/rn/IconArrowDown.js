/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconArrowDown = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M455.253333 162.901333c0-19.242667 15.872-34.901333 35.413334-34.901333 19.584 0 35.413333 15.658667 35.413333 34.901333v698.197334c0 14.165333-8.618667 26.88-21.888 32.256a35.754667 35.754667 0 0 1-38.613333-7.68l-284.586667-281.6a34.56 34.56 0 0 1 0.085333-49.28 35.84 35.84 0 0 1 50.133334 0.085333l224 221.696V162.901333z m294.869334 391.978667a35.84 35.84 0 0 1 50.133333-0.128 34.602667 34.602667 0 0 1 0.085333 49.408L643.413333 759.466667a35.626667 35.626667 0 0 1-50.133333 0.085333 34.474667 34.474667 0 0 1-0.085333-49.365333l157.013333-155.306667z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconArrowDown.defaultProps = {
  size: 18,
};

IconArrowDown = React.memo ? React.memo(IconArrowDown) : IconArrowDown;

export default IconArrowDown;
