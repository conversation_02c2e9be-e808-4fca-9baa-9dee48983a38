/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLogoMoment = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M895.197 350.039l-0.028-0.067c-2.918-6.775-5.437-12.34-7.898-17.45a10.1 10.1 0 0 0-0.607-1.252c-20.169-41.944-47.309-80.128-80.675-113.502-38.231-38.24-82.724-68.23-132.236-89.136C622.559 106.979 568.122 96 511.955 96c-56.212 0-110.682 11.011-161.882 32.72-49.518 20.908-94.011 50.899-132.242 89.139-38.232 38.241-68.216 82.743-89.11 132.251C107.009 401.246 96 455.698 96 511.955c0 56.224 11.008 110.706 32.74 161.984 2.896 6.724 5.396 12.25 7.835 17.315 0.189 0.493 0.426 0.994 0.723 1.496 20.165 41.902 47.286 80.047 80.623 113.392 38.232 38.24 82.725 68.231 132.242 89.14C401.587 916.992 456.052 928 512.045 928c56.214 0 110.684-11.01 161.882-32.719 49.518-20.908 94.01-50.899 132.243-89.14 38.231-38.24 68.215-82.742 89.118-132.271C916.994 622.436 928 567.959 928 511.955c0-55.861-11.036-110.338-32.803-161.916zM671.421 489.36V196.636c33.375 16.925 63.663 38.931 90.212 65.562l0.01 0.01c23.275 23.28 43.104 49.479 59.015 77.961L671.421 489.36z m155.778 182.186c-16.871 33.258-38.875 63.559-65.557 90.246-23.271 23.276-49.464 43.109-77.939 59.026L534.542 671.546h292.657z m-630.58-319.091c16.872-33.259 38.876-63.56 65.557-90.247 23.274-23.279 49.465-43.111 77.942-59.027l149.24 149.274H196.619z m425.453 495.336c-35.537 11.606-72.506 17.481-110.118 17.481-32.898 0-65.441-4.492-96.84-13.359V640.787l206.958 207.004zM511.955 608.818c-53.398 0-96.84-43.453-96.84-96.864s43.442-96.864 96.84-96.864c53.397 0 96.839 43.453 96.839 96.864s-43.442 96.864-96.839 96.864zM401.84 176.208c35.538-11.607 72.503-17.481 110.115-17.481 32.898 0 65.436 4.492 96.839 13.36v211.041L401.84 176.208z m463.351 335.837c0 32.909-4.49 65.455-13.357 96.864H640.671l207.042-207.008c11.604 35.547 17.478 72.525 17.478 110.144z m-482.122-96.954L176.167 622.118c-11.585-35.298-17.449-72.281-17.449-110.164 0-32.906 4.491-65.453 13.357-96.864h210.994v0.001z m-30.672 119.468v292.742c-33.204-16.789-63.495-38.776-90.221-65.509-23.273-23.278-43.103-49.479-59.016-77.963l149.237-149.27z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </Svg>
  );
};

IconLogoMoment.defaultProps = {
  size: 18,
};

IconLogoMoment = React.memo ? React.memo(IconLogoMoment) : IconLogoMoment;

export default IconLogoMoment;
