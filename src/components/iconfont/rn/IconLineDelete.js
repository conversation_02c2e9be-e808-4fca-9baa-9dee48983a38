/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLineDelete = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M928 224H704v-64c0-52.935-43.065-96-96-96H416c-52.935 0-96 43.065-96 96v64H96c-17.673 0-32 14.327-32 32s14.327 32 32 32h96v576c0 52.935 43.065 96 96 96h448c52.935 0 96-43.065 96-96V288h96c17.673 0 32-14.327 32-32s-14.327-32-32-32z m-544-64c0-17.645 14.355-32 32-32h192c17.645 0 32 14.355 32 32v64H384v-64z m384 704c0 17.645-14.355 32-32 32H288c-17.645 0-32-14.355-32-32V288h512v576z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <Path
        d="M448 720V464c0-17.673-14.327-32-32-32s-32 14.327-32 32v256c0 17.673 14.327 32 32 32s32-14.327 32-32zM640 720V464c0-17.673-14.327-32-32-32s-32 14.327-32 32v256c0 17.673 14.327 32 32 32s32-14.327 32-32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </Svg>
  );
};

IconLineDelete.defaultProps = {
  size: 18,
};

IconLineDelete = React.memo ? React.memo(IconLineDelete) : IconLineDelete;

export default IconLineDelete;
