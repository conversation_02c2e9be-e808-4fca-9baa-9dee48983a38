/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconIconZhuanxianglianxi = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M0 0m398.222222 0l227.555556 0q398.222222 0 398.222222 398.222222l0 227.555556q0 398.222222-398.222222 398.222222l-227.555556 0q-398.222222 0-398.222222-398.222222l0-227.555556q0-398.222222 398.222222-398.222222Z"
        fill={getIconColor(color, 0, '#00DCA8')}
      />
      <Path
        d="M693.361778 199.168H330.581333a113.777778 113.777778 0 0 0-113.777777 113.777778v398.222222a113.777778 113.777778 0 0 0 113.777777 113.777778h362.780445a113.777778 113.777778 0 0 0 113.777778-113.777778v-398.222222a113.777778 113.777778 0 0 0-113.777778-113.777778z m-242.403556 312.888889h-129.365333a28.444444 28.444444 0 0 1 0-56.888889h129.365333a28.444444 28.444444 0 0 1 0 56.888889z m-129.365333-134.4a28.444444 28.444444 0 0 1 0-56.888889h240.298667a28.444444 28.444444 0 0 1 0 56.888889z m416.142222 283.278222l-30.293333 29.639111a9.500444 9.500444 0 0 0-2.844445 8.533334l7.168 41.671111a9.557333 9.557333 0 0 1-13.937777 10.097777l-37.518223-19.655111a9.614222 9.614222 0 0 0-8.874666 0l-37.489778 19.712a9.614222 9.614222 0 0 1-12.970667-3.925333 9.870222 9.870222 0 0 1-0.967111-6.229333l7.196445-41.756445a9.557333 9.557333 0 0 0-2.844445-8.533333l-30.321778-29.525333a9.585778 9.585778 0 0 1 5.290667-16.355556l41.898667-6.058667a9.642667 9.642667 0 0 0 7.224889-5.290666l18.801777-37.973334a9.585778 9.585778 0 0 1 17.237334 0l18.716444 37.973334a9.728 9.728 0 0 0 7.253333 5.290666l41.984 6.058667a9.585778 9.585778 0 0 1 8.135112 10.808889 9.472 9.472 0 0 1-2.844445 5.461333z"
        fill={getIconColor(color, 1, '#FFFFFF')}
      />
    </Svg>
  );
};

IconIconZhuanxianglianxi.defaultProps = {
  size: 18,
};

IconIconZhuanxianglianxi = React.memo ? React.memo(IconIconZhuanxianglianxi) : IconIconZhuanxianglianxi;

export default IconIconZhuanxianglianxi;
