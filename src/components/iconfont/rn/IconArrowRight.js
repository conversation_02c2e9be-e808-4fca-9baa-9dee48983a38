/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconArrowRight = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M554.752 202.410667a35.84 35.84 0 0 0 0.128 50.133333l155.306667 156.970667 3.584 3.157333a34.432 34.432 0 0 0 45.738666-3.285333 35.626667 35.626667 0 0 0-0.085333-50.090667l-155.306667-157.013333-3.541333-3.114667a34.56 34.56 0 0 0-45.824 3.242667z m-396.202667 274.432A35.285333 35.285333 0 0 0 128 512c0 19.541333 15.658667 35.413333 34.901333 35.413333h613.674667l-221.696 224.042667-3.157333 3.626667a35.84 35.84 0 0 0 3.072 46.506666 34.56 34.56 0 0 0 49.322666 0.085334l281.6-284.586667 3.114667-3.584a35.669333 35.669333 0 0 0 4.522667-34.986667 34.858667 34.858667 0 0 0-32.256-21.930666H162.901333l-4.352 0.256z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </Svg>
  );
};

IconArrowRight.defaultProps = {
  size: 18,
};

IconArrowRight = React.memo ? React.memo(IconArrowRight) : IconArrowRight;

export default IconArrowRight;
