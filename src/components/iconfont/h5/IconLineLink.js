/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineLink = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M901.816 122.207C864.309 84.671 814.349 64 761.141 64c-53.205 0-103.178 20.67-140.714 58.205L461.799 280.83c-13.431 13.429-13.431 35.281 0 48.712 6.5 6.499 15.15 10.079 24.356 10.079s17.856-3.58 24.356-10.08l158.633-158.626c24.532-24.533 57.197-38.044 91.977-38.044s67.447 13.511 91.979 38.044c50.717 50.717 50.717 133.238 0 183.954L665.101 542.865c-24.532 24.532-57.197 38.043-91.977 38.043s-67.446-13.511-91.979-38.044c-6.5-6.5-15.151-10.08-24.356-10.08s-17.856 3.581-24.354 10.079c-6.502 6.498-10.082 15.149-10.082 24.356 0 9.206 3.581 17.857 10.081 24.357 37.519 37.519 87.472 58.187 140.668 58.199h0.051c53.194-0.012 103.146-20.681 140.667-58.199l187.995-187.99c37.529-37.504 58.194-87.463 58.187-140.675-0.008-53.198-20.672-103.167-58.186-140.704zM573.1 641.18h0.05-0.05z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M537.861 684.379c-9.206 0-17.857 3.58-24.359 10.08L354.875 853.085c-24.533 24.531-57.199 38.041-91.979 38.041-34.781 0-67.446-13.51-91.977-38.041-24.534-24.532-38.045-57.199-38.045-91.978 0-34.78 13.511-67.444 38.045-91.974l187.996-187.996c24.534-24.533 57.199-38.044 91.98-38.044 34.78 0 67.447 13.511 91.978 38.043 6.499 6.5 15.15 10.081 24.356 10.081 9.207 0 17.857-3.58 24.359-10.08 13.43-13.429 13.43-35.281 0.002-48.71-37.51-37.535-87.468-58.206-140.675-58.206-53.206 0-103.179 20.671-140.713 58.204l-187.995 187.99C84.672 657.924 64 707.882 64 761.089c0 53.205 20.67 103.176 58.205 140.706 37.516 37.52 87.472 58.19 140.664 58.205h0.054c53.191-0.015 103.148-20.685 140.663-58.204l158.63-158.626c6.5-6.5 10.08-15.151 10.08-24.357 0-9.207-3.581-17.857-10.079-24.353-6.498-6.501-15.149-10.081-24.356-10.081zM262.869 951.403h0.054-0.054z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineLink.defaultProps = {
  size: 18,
};

export default IconLineLink;
