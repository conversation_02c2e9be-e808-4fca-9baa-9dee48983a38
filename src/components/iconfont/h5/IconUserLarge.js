/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconUserLarge = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M512 576a288 288 0 1 0 0-576 288 288 0 1 0 0 576z m-189.4 64C144.4 640 0 784.4 0 962.6c0 34 27.6 61.4 61.4 61.4h901.2c34 0 61.4-27.6 61.4-61.4 0-178.2-144.4-322.6-322.6-322.6H322.6z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconUserLarge.defaultProps = {
  size: 18,
};

export default IconUserLarge;
