/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconSetting = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M381.525333 293.290667a115.584 115.584 0 0 1-113.066666-0.725334l-1.536-1.493333a32.213333 32.213333 0 0 1-10.752-44.970667 33.92 33.92 0 0 1 46.08-10.496c6.954667 4.010667 14.933333 6.186667 22.997333 6.4 12.416 0.469333 24.533333-3.84 33.706667-12.074666 9.130667-8.234667 14.506667-19.626667 15.018666-31.786667C373.76 137.173333 423.296 87.168 485.674667 85.333333h52.565333c63.573333 0 115.114667 50.346667 115.114667 112.426667a42.368 42.368 0 0 0 6.144 22.485333c6.144 10.538667 16.298667 18.261333 28.245333 21.418667 11.946667 3.157333 24.746667 1.536 35.456-4.565333 54.058667-28.714667 121.642667-10.88 153.514667 40.490666a32.938667 32.938667 0 0 1-12.245334 44.586667 34.218667 34.218667 0 0 1-45.653333-11.605333 47.445333 47.445333 0 0 0-63.744-16.512 117.546667 117.546667 0 0 1-112.810667-1.194667 112.042667 112.042667 0 0 1-57.6-94.72 44.8 44.8 0 0 0-13.013333-33.066667 46.976 46.976 0 0 0-33.408-13.781333h-52.565333a47.36 47.36 0 0 0-33.536 13.781333 45.141333 45.141333 0 0 0-13.312 33.066667 110.165333 110.165333 0 0 1-57.301334 95.146667z m57.301334 534.058666a46.293333 46.293333 0 0 0 46.848 45.354667l-0.426667 0.768c11.946667 0 22.997333 6.186667 28.928 16.298667 5.973333 10.069333 5.973333 22.485333 0 32.597333a33.536 33.536 0 0 1-28.885333 16.298667c-63.573333 0-115.157333-50.346667-115.157334-112.426667a42.325333 42.325333 0 0 0-6.144-22.485333 47.445333 47.445333 0 0 0-63.701333-16.853334c-54.016 28.714667-121.6 10.88-153.514667-40.490666L120.746667 701.866667c-29.44-52.778667-11.178667-118.784 41.429333-149.930667 6.997333-3.968 12.8-9.642667 16.896-16.469333 7.68-10.496 10.453333-23.637333 7.594667-36.266667a45.44 45.44 0 0 0-22.570667-29.696c-52.608-31.146667-70.826667-97.152-41.429333-149.888a34.048 34.048 0 0 1 46.421333-8.96c14.549333 9.642667 18.773333 28.757333 9.6 43.434667a44.8 44.8 0 0 0 17.28 61.824c16.682667 9.984 30.464 24.064 39.893333 40.832 29.44 52.778667 11.178667 118.784-41.429333 149.930666a44.8 44.8 0 0 0-17.28 61.866667l26.453333 44.544c6.058667 10.666667 16.256 18.389333 28.288 21.504 12.032 3.114667 24.832 1.28 35.413334-4.992a112.938667 112.938667 0 0 1 56.448-14.634667c63.573333 0 115.114667 50.346667 115.114666 112.426667z m422.570666-275.797333a45.525333 45.525333 0 0 1-21.76-27.690667 44.416 44.416 0 0 1 4.864-34.56c4.096-6.784 9.898667-12.458667 16.896-16.469333a32.170667 32.170667 0 0 0 11.52-44.586667 34.602667 34.602667 0 0 0-45.312-12.373333c-52.608 31.146667-70.826667 97.152-41.429333 149.888 9.344 18.432 23.850667 33.92 41.813333 44.629333 10.666667 5.973333 18.517333 15.829333 21.674667 27.477334 3.157333 11.605333 1.450667 23.978667-4.778667 34.346666L818.773333 716.8a46.634667 46.634667 0 0 1-63.701333 16.469333 117.546667 117.546667 0 0 0-112.981333 1.322667 112.042667 112.042667 0 0 0-57.429334 95.018667c1.834667 16.768 16.341333 29.44 33.578667 29.44a33.493333 33.493333 0 0 0 33.578667-29.44 44.8 44.8 0 0 1 23.04-39.808c14.506667-8.192 32.469333-8.021333 46.848 0.426666 54.016 28.757333 121.6 10.922667 153.472-40.448l26.112-44.586666c32.213333-53.205333 14.378667-121.856-39.893334-153.642667z m-349.653333-180.266667c-58.368 0-111.018667 34.346667-133.333333 87.04a138.496 138.496 0 0 0 31.445333 153.6 146.858667 146.858667 0 0 0 157.397333 30.336c53.888-21.930667 88.96-73.386667 88.789334-130.432 0-37.376-15.189333-73.173333-42.24-99.541333a145.706667 145.706667 0 0 0-102.058667-41.002667z m0 215.893334c-42.368 0-76.757333-33.578667-76.757333-75.008 0-41.386667 34.389333-74.922667 76.8-74.922667 42.368 0 76.714667 33.536 76.714666 74.922667s-34.346667 74.965333-76.757333 74.965333z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </svg>
  );
};

IconSetting.defaultProps = {
  size: 18,
};

export default IconSetting;
