/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineCompany = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M832 352H608V192c0-52.935-43.065-96-96-96H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V448c0-52.935-43.065-96-96-96zM544 864H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h320c17.645 0 32 14.355 32 32v672z m320-32c0 17.645-14.355 32-32 32H608V416h224c17.645 0 32 14.355 32 32v384z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M768 512h-64c-17.673 0-32 14.327-32 32s14.327 32 32 32h64c17.673 0 32-14.327 32-32s-14.327-32-32-32zM768 704h-64c-17.673 0-32 14.327-32 32s14.327 32 32 32h64c17.673 0 32-14.327 32-32s-14.327-32-32-32zM416 480H288c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h128c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM416 288H288c-17.673 0-32 14.327-32 32s14.327 32 32 32h128c17.673 0 32-14.327 32-32s-14.327-32-32-32zM416 672H288c-17.673 0-32 14.327-32 32s14.327 32 32 32h128c17.673 0 32-14.327 32-32s-14.327-32-32-32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineCompany.defaultProps = {
  size: 18,
};

export default IconLineCompany;
