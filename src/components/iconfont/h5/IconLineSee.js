/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineSee = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M921.7 394.2c-23.5-34.2-56.7-64.6-98.8-90.5C739.4 252.3 629 224 512 224s-227.4 28.3-310.9 79.7c-42 25.9-75.3 56.3-98.8 90.5C76.9 431.2 64 470.8 64 512s12.9 80.8 38.3 117.8c23.5 34.2 56.7 64.6 98.8 90.5C284.6 771.7 395 800 512 800s227.4-28.3 310.9-79.7c42-25.9 75.3-56.3 98.8-90.5 25.4-37 38.3-76.6 38.3-117.8s-12.9-80.8-38.3-117.8zM789.4 665.8c-35.5 21.8-77.1 39.1-123.5 51.1C617.2 729.6 565.4 736 512 736s-105.2-6.4-153.9-19.1c-46.5-12.1-88-29.3-123.5-51.1C165.9 623.5 128 568.8 128 512s37.9-111.5 106.6-153.8c35.5-21.8 77.1-39.1 123.5-51.1C406.8 294.4 458.6 288 512 288s105.2 6.4 153.9 19.1c46.5 12.1 88 29.3 123.5 51.1C858.1 400.5 896 455.2 896 512s-37.9 111.5-106.6 153.8z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M512 384c-70.6 0-128 57.4-128 128s57.4 128 128 128 128-57.4 128-128-57.4-128-128-128z m0 192c-35.3 0-64-28.7-64-64s28.7-64 64-64 64 28.7 64 64-28.7 64-64 64z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineSee.defaultProps = {
  size: 18,
};

export default IconLineSee;
