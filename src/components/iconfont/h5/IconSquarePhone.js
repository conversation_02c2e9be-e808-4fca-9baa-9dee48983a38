/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconSquarePhone = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M192 64C121.4 64 64 121.4 64 192v640c0 70.6 57.4 128 128 128h640c70.6 0 128-57.4 128-128V192c0-70.6-57.4-128-128-128H192z m181.4 193.4c19.4-5.2 39.8 4.6 47.4 23.2l40 96c6.8 16.4 2 35.2-11.6 46.4L400 463.4c33.2 70.4 90.2 127.4 160.6 160.6l40.4-49.4c11.2-13.6 30-18.4 46.4-11.6l96 40c18.6 7.8 28.4 28 23.2 47.4l-24 88C737.8 756 722 768 704 768 456.6 768 256 567.4 256 320c0-18 12-33.8 29.4-38.6l88-24z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconSquarePhone.defaultProps = {
  size: 18,
};

export default IconSquarePhone;
