/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconTikuzongliang = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1248 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M43.866656 545.344085l534.928652-314.6001a88.793166 88.793166 0 0 1 90.040718 0l534.928652 314.6001a88.793166 88.793166 0 0 1 0 153.177703l-534.982893 313.244065a88.793166 88.793166 0 0 1-89.71527 0L43.866656 698.630271a88.793166 88.793166 0 0 1 0-153.177703z"
        fill={getIconColor(color, 0, '#3C64D1')}
      />
      <path
        d="M43.920898 326.85974l534.928652-314.6001a88.793166 88.793166 0 0 1 90.040718 0l534.928652 314.6001a88.793166 88.793166 0 0 1 0 153.177704l-535.091377 313.244064a88.793166 88.793166 0 0 1-89.715269 0L43.920898 480.037444a88.793166 88.793166 0 0 1 0-153.177704z"
        fill={getIconColor(color, 1, '#8DB0FF')}
      />
      <path
        d="M665.039129 190.117179l483.99598 284.387642-484.212946 283.574021a88.793166 88.793166 0 0 1-89.71527 0L90.893947 474.504821 574.889928 190.062938a88.793166 88.793166 0 0 1 90.040718 0z"
        fill={getIconColor(color, 2, '#4F7EDC')}
      />
    </svg>
  );
};

IconTikuzongliang.defaultProps = {
  size: 18,
};

export default IconTikuzongliang;
