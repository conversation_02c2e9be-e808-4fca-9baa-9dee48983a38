/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineNews = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M864 112H160c-52.935 0-96 43.065-96 96v480c0 52.935 43.065 96 96 96h210.746l118.627 118.627C495.621 908.876 503.811 912 512 912c8.189 0 16.379-3.124 22.627-9.373L653.254 784H864c52.935 0 96-43.065 96-96V208c0-52.935-43.065-96-96-96z m32 576c0 17.645-14.355 32-32 32H640.02c-0.538 0-1.076 0.014-1.613 0.04-0.212 0.01-0.421 0.031-0.631 0.045-0.309 0.021-0.619 0.04-0.927 0.07-0.289 0.028-0.575 0.067-0.861 0.103-0.227 0.029-0.454 0.053-0.681 0.087-0.319 0.047-0.634 0.104-0.949 0.16-0.194 0.035-0.389 0.066-0.583 0.104-0.325 0.064-0.647 0.138-0.969 0.212-0.185 0.042-0.37 0.082-0.554 0.128-0.315 0.078-0.627 0.165-0.938 0.253-0.193 0.054-0.387 0.106-0.579 0.164-0.29 0.088-0.577 0.183-0.864 0.279-0.214 0.071-0.428 0.141-0.641 0.217-0.258 0.092-0.512 0.191-0.767 0.289-0.24 0.092-0.479 0.184-0.717 0.283-0.222 0.092-0.441 0.19-0.661 0.287-0.265 0.117-0.529 0.233-0.791 0.358-0.19 0.09-0.377 0.186-0.565 0.28-0.284 0.141-0.567 0.283-0.847 0.434-0.164 0.088-0.326 0.181-0.489 0.273-0.294 0.164-0.587 0.33-0.877 0.504-0.15 0.091-0.297 0.186-0.446 0.279-0.292 0.182-0.583 0.365-0.871 0.558-0.151 0.101-0.298 0.208-0.447 0.312-0.275 0.192-0.551 0.383-0.821 0.584-0.173 0.128-0.34 0.263-0.51 0.395-0.238 0.185-0.478 0.367-0.712 0.559-0.233 0.192-0.459 0.392-0.687 0.59-0.165 0.143-0.333 0.281-0.495 0.429-0.399 0.361-0.789 0.732-1.169 1.112L512 834.746 406.632 729.378a33.24 33.24 0 0 0-1.154-1.098c-0.178-0.162-0.362-0.314-0.544-0.471-0.212-0.184-0.422-0.371-0.64-0.549-0.242-0.199-0.489-0.387-0.734-0.577-0.163-0.126-0.323-0.255-0.488-0.378-0.275-0.205-0.555-0.399-0.835-0.594-0.145-0.101-0.288-0.204-0.434-0.303-0.29-0.195-0.585-0.38-0.88-0.564-0.146-0.091-0.289-0.184-0.437-0.273a30.054 30.054 0 0 0-0.884-0.508c-0.161-0.09-0.32-0.182-0.482-0.269a32.5 32.5 0 0 0-0.851-0.436c-0.187-0.093-0.373-0.188-0.562-0.278a31.135 31.135 0 0 0-0.793-0.359c-0.219-0.097-0.437-0.194-0.659-0.286-0.238-0.099-0.479-0.191-0.719-0.284-0.254-0.098-0.508-0.197-0.765-0.289-0.213-0.076-0.428-0.146-0.642-0.217a32.129 32.129 0 0 0-0.863-0.279c-0.192-0.058-0.386-0.11-0.579-0.164a29.355 29.355 0 0 0-0.937-0.253c-0.185-0.046-0.37-0.086-0.555-0.128a30.811 30.811 0 0 0-0.968-0.212c-0.194-0.038-0.389-0.07-0.583-0.104-0.315-0.056-0.63-0.113-0.949-0.16-0.227-0.033-0.454-0.058-0.681-0.087-0.287-0.036-0.572-0.075-0.861-0.103-0.309-0.03-0.618-0.049-0.927-0.07-0.211-0.014-0.419-0.035-0.631-0.045a30.98 30.98 0 0 0-1.613-0.04H160c-17.645 0-32-14.355-32-32V208c0-17.645 14.355-32 32-32h704c17.645 0 32 14.355 32 32v480z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M512 400.002c-26.508 0-47.998 21.489-47.998 47.998 0 26.508 21.489 47.998 47.998 47.998 26.508 0 47.998-21.489 47.998-47.998 0-26.508-21.49-47.998-47.998-47.998zM320.01 400.002c-26.508 0-47.998 21.489-47.998 47.998 0 26.508 21.489 47.998 47.997 47.998s47.998-21.489 47.998-47.998c0-26.508-21.489-47.998-47.997-47.998zM703.99 400.002c-26.508 0-47.998 21.489-47.998 47.998 0 26.508 21.489 47.998 47.998 47.998 26.508 0 47.997-21.489 47.997-47.998 0.001-26.508-21.488-47.998-47.997-47.998z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineNews.defaultProps = {
  size: 18,
};

export default IconLineNews;
