/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineScan = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M128 352c17.673 0 32-14.327 32-32V192c0-17.645 14.355-32 32-32h128c17.673 0 32-14.327 32-32s-14.327-32-32-32H192c-52.935 0-96 43.065-96 96v128c0 17.673 14.327 32 32 32zM320 864H192c-17.645 0-32-14.355-32-32V704c0-17.673-14.327-32-32-32s-32 14.327-32 32v128c0 52.935 43.065 96 96 96h128c17.673 0 32-14.327 32-32s-14.327-32-32-32zM896 672c-17.673 0-32 14.327-32 32v128c0 17.645-14.355 32-32 32H704c-17.673 0-32 14.327-32 32s14.327 32 32 32h128c52.935 0 96-43.065 96-96V704c0-17.673-14.327-32-32-32zM832 96H704c-17.673 0-32 14.327-32 32s14.327 32 32 32h128c17.645 0 32 14.355 32 32v128c0 17.673 14.327 32 32 32s32-14.327 32-32V192c0-52.935-43.065-96-96-96zM160 512c0 17.673 14.327 32 32 32h640c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32H192c-17.673 0-32 14.327-32 32z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconLineScan.defaultProps = {
  size: 18,
};

export default IconLineScan;
