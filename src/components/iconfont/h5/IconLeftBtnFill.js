/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLeftBtnFill = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M512 97.52381c228.912762 0 414.47619 185.563429 414.47619 414.47619s-185.563429 414.47619-414.47619 414.47619S97.52381 740.912762 97.52381 512 283.087238 97.52381 512 97.52381z m48.761905 192.097523L338.383238 512 560.761905 734.378667 612.473905 682.666667l-170.666667-170.666667 170.666667-170.666667L560.761905 289.621333z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconLeftBtnFill.defaultProps = {
  size: 18,
};

export default IconLeftBtnFill;
