/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineOverstep = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M832 676V348c55.1-14.2 96-64.4 96-124 0-70.6-57.4-128-128-128-59.5 0-109.7 40.9-124 96H348c-14.2-55.1-64.4-96-124-96-70.6 0-128 57.4-128 128 0 59.5 40.9 109.7 96 124v328c-55.1 14.2-96 64.4-96 124 0 70.6 57.4 128 128 128 59.5 0 109.7-40.9 124-96h328c14.2 55.1 64.4 96 124 96 70.6 0 128-57.4 128-128 0-59.5-40.9-109.7-96-124z m-32-516c35.3 0 64 28.7 64 64s-28.7 64-64 64-64-28.7-64-64 28.7-64 64-64z m-640 64c0-35.3 28.7-64 64-64s64 28.7 64 64-28.7 64-64 64-64-28.7-64-64z m64 640c-35.3 0-64-28.7-64-64s28.7-64 64-64 64 28.7 64 64-28.7 64-64 64z m452-96H348c-11.6-44.9-47-80.3-92-92V348c44.9-11.6 80.3-47 92-92h328c11.6 44.9 47 80.3 92 92v328c-44.9 11.7-80.3 47.1-92 92z m124 96c-35.3 0-64-28.7-64-64s28.7-64 64-64 64 28.7 64 64-28.7 64-64 64z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M444.1 398.9c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l181 181c6.2 6.2 14.4 9.4 22.6 9.4s16.4-3.1 22.6-9.4c12.5-12.5 12.5-32.8 0-45.3l-180.9-181z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineOverstep.defaultProps = {
  size: 18,
};

export default IconLineOverstep;
