/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconManagerexam = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M864.7 542.1c-31.5-31.5-70.7-52.4-113.3-61.1-45.6-31.6-98.7-48.3-154.2-48.3H575c50.8-31.5 84.7-87.7 84.7-151.7 0-98.4-80-178.4-178.4-178.4s-178.4 80-178.4 178.4c0 64 33.9 120.2 84.7 151.7h-22.2c-72.4 0-140.6 28.4-192.1 79.9-51.5 51.5-79.9 119.7-79.9 192.1v58c0 5.7 0.2 11.6 0.6 17.4l2.1 32.8h416.1c9.7 16.7 21.6 32.2 35.6 46.2 42.3 42.3 98.6 65.7 158.5 65.7s116.2-23.3 158.5-65.7 65.7-98.6 65.7-158.5-23.4-116.1-65.8-158.5zM481.3 172.6c59.8 0 108.4 48.6 108.4 108.4S541 389.4 481.3 389.4c-59.8 0-108.4-48.6-108.4-108.4s48.6-108.4 108.4-108.4z m0.8 528c0 14.3 1.4 28.5 4 42.3H163.4v-38.2c0-111.4 90.6-201.9 201.9-201.9h235.3c-19.2 10.3-37 23.5-52.9 39.3-42.3 42.4-65.6 98.7-65.6 158.5z m224.1 154.2c-85 0-154.2-69.2-154.2-154.2s69.2-154.2 154.2-154.2 154.2 69.2 154.2 154.2-69.2 154.2-154.2 154.2z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M691.1 698.6l-33.9-34-49.5 49.5 83.4 83.5 126.2-126.2-49.5-49.5z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconManagerexam.defaultProps = {
  size: 18,
};

export default IconManagerexam;
