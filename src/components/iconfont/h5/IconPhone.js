/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconPhone = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M329.785475 49.242931c-15.399322-37.198362-55.997534-56.99749-94.795825-46.397956l-175.992248 47.997886C24.198934 60.442438 0 92.041046 0 128.039461c0 494.778208 401.182331 895.960539 895.960539 895.960539 35.998415 0 67.597023-24.198934 77.1966-58.997402l47.997886-175.992248c10.599533-38.798291-9.199595-79.396503-46.397956-94.795825l-191.991544-79.996477c-32.598564-13.599401-70.3969-4.199815-92.595922 23.198978L609.373161 736.012684c-140.793799-66.597067-254.788778-180.592046-321.385845-321.385845l98.595658-80.596451c27.398793-22.399013 36.798379-59.997358 23.198978-92.595921l-79.996477-191.991545z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconPhone.defaultProps = {
  size: 18,
};

export default IconPhone;
