/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineApplication = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M895.746 276.142l-368-208a32.004 32.004 0 0 0-31.492 0l-368 208A32 32 0 0 0 112 304v416a32 32 0 0 0 16.254 27.858l368 208A31.987 31.987 0 0 0 512 960c5.431 0 10.86-1.381 15.746-4.142l368-208A32 32 0 0 0 912 720V304a32 32 0 0 0-16.254-27.858zM848 701.329L512 891.242 176 701.329V322.671l336-189.913 336 189.913v378.658z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M480 320v173.525l-150.277 86.762c-15.305 8.837-20.549 28.407-11.713 43.713 5.927 10.267 16.683 16.005 27.743 16.005a31.858 31.858 0 0 0 15.97-4.292L512 548.951l150.277 86.762a31.858 31.858 0 0 0 15.97 4.292c11.06 0 21.816-5.739 27.743-16.005 8.837-15.306 3.593-34.876-11.713-43.713L544 493.525V320c0-17.673-14.327-32-32-32-17.673 0-32 14.327-32 32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineApplication.defaultProps = {
  size: 18,
};

export default IconLineApplication;
