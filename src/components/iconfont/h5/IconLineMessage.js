/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineMessage = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M864 128H160c-52.935 0-96 43.065-96 96v576c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V224c0-52.935-43.065-96-96-96z m32 672c0 17.645-14.355 32-32 32H160c-17.645 0-32-14.355-32-32V224c0-17.645 14.355-32 32-32h704c17.645 0 32 14.355 32 32v576z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M747.33 270.957L512 470.082 276.67 270.957c-13.491-11.417-33.683-9.733-45.098 3.758-11.416 13.491-9.733 33.683 3.758 45.098L491.26 536.37c0.304 0.259 0.616 0.506 0.928 0.752 0.113 0.089 0.224 0.182 0.338 0.269 0.279 0.215 0.563 0.42 0.848 0.625a27.33 27.33 0 0 0 1.806 1.205c0.252 0.156 0.504 0.312 0.759 0.46 0.26 0.151 0.523 0.294 0.786 0.437 0.187 0.102 0.372 0.208 0.56 0.306 0.463 0.24 0.932 0.467 1.403 0.683 0.106 0.048 0.213 0.092 0.319 0.139 0.396 0.176 0.795 0.345 1.196 0.504 0.116 0.046 0.232 0.089 0.348 0.134 0.414 0.159 0.83 0.308 1.248 0.449l0.268 0.09c1.576 0.514 3.181 0.902 4.801 1.164l0.179 0.027a31.683 31.683 0 0 0 3.044 0.326c0.104 0.006 0.209 0.015 0.313 0.02 0.526 0.026 1.053 0.043 1.579 0.043h0.032c0.526 0 1.053-0.017 1.579-0.043 0.105-0.005 0.21-0.014 0.314-0.02a32.336 32.336 0 0 0 3.041-0.326l0.182-0.027c1.62-0.262 3.226-0.65 4.802-1.164l0.263-0.088c0.421-0.141 0.84-0.292 1.256-0.451 0.113-0.043 0.227-0.086 0.34-0.131 0.405-0.16 0.806-0.33 1.206-0.508 0.104-0.046 0.208-0.089 0.312-0.136 0.473-0.216 0.942-0.444 1.407-0.685 0.184-0.095 0.363-0.199 0.545-0.298 0.268-0.146 0.537-0.292 0.802-0.446 0.252-0.146 0.501-0.3 0.749-0.454a32.604 32.604 0 0 0 1.364-0.891c0.153-0.106 0.305-0.215 0.456-0.324 0.281-0.201 0.561-0.404 0.836-0.616 0.12-0.092 0.238-0.19 0.357-0.285 0.306-0.242 0.612-0.484 0.911-0.739L788.67 319.813c13.491-11.416 15.174-31.607 3.759-45.098-11.416-13.493-31.61-15.174-45.099-3.758z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineMessage.defaultProps = {
  size: 18,
};

export default IconLineMessage;
