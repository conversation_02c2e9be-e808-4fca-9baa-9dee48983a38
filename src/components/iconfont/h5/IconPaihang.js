/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconPaihang = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M0 0m228.968944 0l566.062112 0q228.968944 0 228.968944 228.968944l0 566.062112q0 228.968944-228.968944 228.968944l-566.062112 0q-228.968944 0-228.968944-228.968944l0-566.062112q0-228.968944 228.968944-228.968944Z"
        fill={getIconColor(color, 0, '#34CCFE')}
      />
      <path
        d="M240.417391 291.299379h101.763975v259.498137c-56.202971 0-101.763975-45.561004-101.763975-101.763976V291.299379zM783.582609 291.299379h-101.763975v259.498137c56.202971 0 101.763975-45.561004 101.763975-101.763976V291.299379z"
        fill={getIconColor(color, 1, '#FFFFFF')}
        opacity=".5"
      />
      <path
        d="M325.64472 212.432298h372.71056v303.383851c0 94.071891-69.701963 171.862817-160.278261 184.545153V758.141615h79.503105c14.402147 0 26.077019 11.674872 26.077019 26.077019s-11.674872 26.077019-26.077019 26.077018h-209.888199c-14.402147 0-26.077019-11.674872-26.077018-26.077018s11.674872-26.077019 26.077018-26.077019H485.922981v-57.780313c-90.576298-12.682335-160.278261-90.473262-160.278261-184.545153V212.432298z"
        fill={getIconColor(color, 2, '#FFFFFF')}
      />
      <path
        d="M498.78086 346.332065c5.069118-10.622887 20.188701-10.622887 25.257818 0l15.424875 32.326598a13.992547 13.992547 0 0 0 10.799702 7.846003l35.511811 4.681143c11.668512 1.537908 16.34075 15.91843 7.804025 24.021386l-25.977799 24.659955a13.992547 13.992547 0 0 0-4.125257 12.695056l6.521799 35.220512c2.143404 11.571836-10.088626 20.459647-20.432935 14.84482l-31.480685-17.084899a13.986186 13.986186 0 0 0-13.34889 0l-31.480685 17.084899c-10.344308 5.614827-22.576338-3.272984-20.434207-14.84482l6.521799-35.220512a13.98873 13.98873 0 0 0-4.125257-12.695056l-25.977799-24.659955c-8.535453-8.102957-3.863215-22.483478 7.805297-24.021386l35.511811-4.681143a13.993819 13.993819 0 0 0 10.799702-7.846003l15.424875-32.326598z"
        fill={getIconColor(color, 3, '#34CCFE')}
      />
    </svg>
  );
};

IconPaihang.defaultProps = {
  size: 18,
};

export default IconPaihang;
