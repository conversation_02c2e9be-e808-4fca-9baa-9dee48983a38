/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineDeleteuser = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M731.701 579.397c8.85-15.298 3.622-34.873-11.676-43.723-24.291-14.052-49.921-25.536-76.437-34.357a226.986 226.986 0 0 0 26.803-22.925C712.7 436.084 736 379.833 736 320s-23.3-116.084-65.608-158.392C628.084 119.3 571.832 96 512 96c-59.833 0-116.084 23.3-158.393 65.608C311.3 203.916 288 260.167 288 320s23.3 116.084 65.607 158.392a226.96 226.96 0 0 0 26.734 22.875 416.054 416.054 0 0 0-30.278 11.437c-49.541 20.954-94.026 50.945-132.221 89.14s-68.185 82.68-89.139 132.221C107.003 785.371 96 839.854 96 896c0 17.673 14.327 32 32 32s32-14.327 32-32c0-94.022 36.614-182.418 103.099-248.901C329.582 580.614 417.978 544 512 544c61.892 0 122.744 16.277 175.979 47.073 15.299 8.851 34.874 3.621 43.722-11.676zM352 320c0-88.224 71.775-160 160-160s160 71.776 160 160-71.775 160-160 160-160-71.776-160-160zM836.255 791l82.373-82.372c12.497-12.497 12.497-32.759 0.001-45.255-12.499-12.499-32.76-12.496-45.255-0.001L791 745.745l-82.374-82.373c-12.496-12.496-32.757-12.497-45.255 0.001-12.496 12.496-12.496 32.758 0.001 45.255L745.745 791l-82.373 82.372c-12.497 12.497-12.497 32.759-0.001 45.255 6.25 6.25 14.438 9.373 22.628 9.373 8.188 0 16.38-3.125 22.627-9.372L791 836.255l82.374 82.373C879.621 924.876 887.811 928 896 928s16.379-3.124 22.628-9.373c12.496-12.496 12.496-32.758-0.001-45.255L836.255 791z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconLineDeleteuser.defaultProps = {
  size: 18,
};

export default IconLineDeleteuser;
