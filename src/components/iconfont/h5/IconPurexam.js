/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconPurexam = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M895.7 580.7L920 277.9c1.1-13.9-3.7-27.9-13.2-38.2-9.6-10.4-23.2-16.4-37.4-16.4H672.5c-8.4-29.4-24.4-56.2-46.7-77.3-30.6-29-70.4-45-112-45-41.6 0-81.4 16-112 45-22.3 21.1-38.3 47.9-46.7 77.3H158.4c-14.3 0-28 6-37.6 16.6-9.5 10.5-14.2 24.5-12.9 38.6l49 528.6c2.4 26 24 45.7 50.3 45.9l336.7 2.1c1.3 1.4 2.6 2.7 3.9 4 42.3 42.3 98.6 65.7 158.5 65.7s116.2-23.3 158.5-65.7 65.7-98.6 65.7-158.5c-0.1-43.1-12.2-84.3-34.8-119.9zM513.8 171c36.1 0 67.7 20.9 83.9 52.3H429.9c16.2-31.4 47.7-52.3 83.9-52.3z m334.7 122.4l-4.3 53.9H184.5l-5-53.9h669zM224.9 783.1L191 417.2h647.6l-7.8 97c-36.6-24.5-79.6-37.7-124.6-37.7-59.9 0-116.2 23.3-158.5 65.7-42.3 42.3-65.7 98.6-65.7 158.5 0 29.3 5.6 57.8 16.3 84.2l-273.4-1.8z m481.3 71.7c-85 0-154.2-69.2-154.2-154.2s69.2-154.2 154.2-154.2 154.2 69.2 154.2 154.2-69.2 154.2-154.2 154.2z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M691.1 698.6l-33.9-34-49.5 49.5 83.4 83.5 126.2-126.2-49.5-49.5z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconPurexam.defaultProps = {
  size: 18,
};

export default IconPurexam;
