/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconExit = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M511.8 959.6c-119.5 0-231.9-46.6-316.5-131.1-84.5-84.6-131.1-197-131.1-316.5s46.6-231.9 131.1-316.5c22.9-22.9 60-22.9 82.8 0 22.9 22.9 22.9 60 0 82.8-62.4 62.4-96.8 145.4-96.8 233.6 0 88.3 34.4 171.2 96.8 233.6 62.4 62.4 145.4 96.8 233.6 96.8 88.3 0 171.2-34.4 233.6-96.8s96.8-145.4 96.8-233.6c0-88.3-34.4-171.2-96.8-233.6-22.9-22.9-22.9-60 0-82.8 22.9-22.9 60-22.9 82.8 0C912.6 280 959.2 392.4 959.2 512s-46.6 231.9-131.1 316.5C743.7 913 631.3 959.6 511.8 959.6z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M511.8 500.1c-32.3 0-58.6-26.2-58.6-58.6v-319c0-32.3 26.2-58.6 58.6-58.6s58.6 26.2 58.6 58.6v319c-0.1 32.4-26.3 58.6-58.6 58.6z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconExit.defaultProps = {
  size: 18,
};

export default IconExit;
