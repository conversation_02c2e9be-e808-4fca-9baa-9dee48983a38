/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineQrcode = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M384 96H192c-52.935 0-96 43.065-96 96v192c0 52.935 43.065 96 96 96h192c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m32 288c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h192c17.645 0 32 14.355 32 32v192zM384 544H192c-52.935 0-96 43.065-96 96v192c0 52.935 43.065 96 96 96h192c52.935 0 96-43.065 96-96V640c0-52.935-43.065-96-96-96z m32 288c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V640c0-17.645 14.355-32 32-32h192c17.645 0 32 14.355 32 32v192zM832 96H640c-52.935 0-96 43.065-96 96v192c0 52.935 43.065 96 96 96h192c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m32 288c0 17.645-14.355 32-32 32H640c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h192c17.645 0 32 14.355 32 32v192zM608 576c-17.673 0-32 14.327-32 32v32c0 17.673 14.327 32 32 32s32-14.327 32-32v-32c0-17.673-14.327-32-32-32zM608 704c-17.673 0-32 14.327-32 32v128c0 17.673 14.327 32 32 32s32-14.327 32-32V736c0-17.673-14.327-32-32-32zM864 576c-17.673 0-32 14.327-32 32v32c0 17.673 14.327 32 32 32s32-14.327 32-32v-32c0-17.673-14.327-32-32-32zM864 704c-17.673 0-32 14.327-32 32v128c0 17.673 14.327 32 32 32s32-14.327 32-32V736c0-17.673-14.327-32-32-32zM736 800c-17.673 0-32 14.327-32 32v32c0 17.673 14.327 32 32 32s32-14.327 32-32v-32c0-17.673-14.327-32-32-32zM736 576c-17.673 0-32 14.327-32 32v128c0 17.673 14.327 32 32 32s32-14.327 32-32V608c0-17.673-14.327-32-32-32z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconLineQrcode.defaultProps = {
  size: 18,
};

export default IconLineQrcode;
