/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconComments = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1280 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M416.02188 704c229.8 0 416-157.6 416-352S645.82188 0 416.02188 0 0.02188 157.6 0.02188 352c0 77.2 29.4 148.6 79.2 206.8-7 18.8-17.4 35.4-28.4 49.4-9.6 12.4-19.4 22-26.6 28.6-3.6 3.2-6.6 5.8-8.6 7.4-1 0.8-1.8 1.4-2.2 1.6l-0.4 0.4C2.02188 654.4-2.77812 668.8 1.62188 681.8S18.22188 704 32.02188 704c43.6 0 87.6-11.2 124.2-25 18.4-7 35.6-14.8 50.6-22.8C268.22188 686.6 339.62188 704 416.02188 704z m480-352c0 224.6-198.2 393.8-433 414 48.6 148.8 209.8 258 401 258 76.4 0 147.8-17.4 209.4-47.8 15 8 32 15.8 50.4 22.8 36.6 13.8 80.6 25 124.2 25 13.8 0 26.2-9 30.4-22.2 4.2-13.2-0.4-27.6-11.6-35.8l-0.4-0.4c-0.4-0.4-1.2-0.8-2.2-1.6-2-1.6-5-4-8.6-7.4-7.2-6.6-17-16.2-26.6-28.6-11-14-21.4-30.8-28.4-49.4 49.8-58 79.2-129.4 79.2-206.8 0-185.6-169.8-337.8-385.2-351 0.8 10.2 1.2 20.6 1.2 31z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconComments.defaultProps = {
  size: 18,
};

export default IconComments;
