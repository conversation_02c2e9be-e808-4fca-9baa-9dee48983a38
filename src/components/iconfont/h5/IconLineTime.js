/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineTime = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M924.781 337.608c-22.566-53.352-54.864-101.259-95.997-142.393-41.133-41.133-89.041-73.431-142.393-95.997C631.14 75.85 572.466 64 512 64S392.86 75.85 337.608 99.219c-53.352 22.565-101.26 54.864-142.393 95.997s-73.431 89.041-95.997 142.392C75.85 392.861 64 451.535 64 512s11.85 119.139 35.219 174.391c22.566 53.352 54.864 101.26 95.997 142.393s89.041 73.431 142.393 95.997C392.86 948.15 451.534 960 512 960s119.14-11.85 174.392-35.219c53.352-22.566 101.26-54.864 142.393-95.997 41.133-41.134 73.431-89.041 95.997-142.393C948.15 631.14 960 572.465 960 512s-11.85-119.14-35.219-174.392zM783.529 783.529C711.001 856.057 614.57 896 512 896s-199.001-39.943-271.529-112.471S128 614.57 128 512s39.943-199 112.471-271.529C312.999 167.943 409.43 128 512 128s199.001 39.943 271.529 112.471S896 409.43 896 512s-39.943 199.001-112.471 271.529z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M544 498.745V224c0-17.673-14.327-32-32-32s-32 14.327-32 32v288l0.002 0.085c0.001 0.498 0.013 0.997 0.038 1.495 0.013 0.26 0.037 0.517 0.056 0.775 0.019 0.261 0.034 0.523 0.06 0.784 0.031 0.32 0.074 0.636 0.114 0.953 0.025 0.196 0.046 0.392 0.075 0.587 0.05 0.342 0.111 0.68 0.172 1.018 0.031 0.172 0.058 0.344 0.092 0.516 0.067 0.342 0.145 0.681 0.223 1.019 0.039 0.169 0.075 0.338 0.117 0.506 0.081 0.327 0.172 0.651 0.263 0.974 0.051 0.182 0.1 0.364 0.155 0.544 0.091 0.3 0.189 0.596 0.288 0.893 0.068 0.205 0.135 0.41 0.208 0.614 0.095 0.265 0.196 0.526 0.297 0.788 0.09 0.234 0.18 0.467 0.276 0.699 0.094 0.227 0.194 0.45 0.293 0.674 0.115 0.261 0.231 0.523 0.353 0.782 0.09 0.19 0.186 0.378 0.28 0.566 0.142 0.285 0.285 0.569 0.436 0.851 0.086 0.159 0.176 0.316 0.264 0.474 0.168 0.3 0.337 0.6 0.515 0.896 0.085 0.14 0.174 0.278 0.26 0.416 0.189 0.303 0.379 0.605 0.579 0.902 0.091 0.135 0.186 0.266 0.279 0.4 0.203 0.292 0.406 0.584 0.619 0.871 0.108 0.146 0.222 0.286 0.333 0.43 0.205 0.266 0.409 0.532 0.623 0.793 0.149 0.181 0.306 0.356 0.458 0.534 0.187 0.217 0.369 0.437 0.563 0.65 0.274 0.302 0.557 0.594 0.841 0.885 0.082 0.084 0.159 0.171 0.242 0.255L693.02 738.275c6.249 6.249 14.438 9.373 22.628 9.373s16.379-3.124 22.627-9.373c12.497-12.496 12.497-32.758 0-45.255L544 498.745z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineTime.defaultProps = {
  size: 18,
};

export default IconLineTime;
