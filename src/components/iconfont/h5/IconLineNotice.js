/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineNotice = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M648.084 113.604c-22.308-11.621-48.997-9.856-69.647 4.605L295.962 316.372l-16.415 10.735H163.234C126.153 327.108 96 357.27 96 394.342v235.317c0 37.073 30.153 67.234 67.234 67.234h116.378l298.825 208.9c11.556 8.109 25.032 12.228 38.574 12.228 10.604 0 21.257-2.528 31.057-7.634 22.356-11.621 36.227-34.47 36.227-59.642V173.246c0-25.171-13.871-48.028-36.211-59.642z m-31.022 737.141L332.829 651.309l-24.95-16.217a33.613 33.613 0 0 0-18.319-5.433H163.234V394.342H289.56a33.606 33.606 0 0 0 18.401-5.482l25.705-16.833 283.396-198.779v677.497zM885.236 300.241c-6.884-16.278-25.663-23.89-41.938-17.007-16.276 6.884-23.891 25.661-17.007 41.938C851.313 384.329 864 447.187 864 512.001c0 64.433-12.543 126.95-37.281 185.814-6.847 16.293 0.811 35.051 17.104 41.898a31.913 31.913 0 0 0 12.384 2.507c12.497 0 24.369-7.366 29.515-19.61C913.775 655.854 928 584.994 928 512c0-73.425-14.388-144.671-42.764-211.759z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconLineNotice.defaultProps = {
  size: 18,
};

export default IconLineNotice;
