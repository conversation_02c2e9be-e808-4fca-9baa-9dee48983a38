/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineDownload = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M896 480c-17.673 0-32 14.327-32 32v320c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V512c0-17.673-14.327-32-32-32s-32 14.327-32 32v320c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V512c0-17.673-14.327-32-32-32z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M234.928 440.083l256 224c0.1 0.087 0.203 0.168 0.304 0.254 0.254 0.217 0.508 0.432 0.767 0.64 0.127 0.102 0.257 0.199 0.386 0.299l0.202 0.157c0.103 0.079 0.204 0.161 0.308 0.239 0.298 0.223 0.602 0.434 0.907 0.645 0.14 0.097 0.279 0.196 0.42 0.291 0.288 0.193 0.58 0.377 0.873 0.56 0.151 0.094 0.301 0.19 0.453 0.281 0.085 0.051 0.168 0.103 0.253 0.153 0.21 0.123 0.422 0.239 0.634 0.357 0.138 0.077 0.274 0.156 0.413 0.231 0.127 0.068 0.254 0.134 0.382 0.201 0.195 0.101 0.39 0.198 0.586 0.295 0.125 0.062 0.247 0.127 0.372 0.187 0.177 0.085 0.356 0.163 0.534 0.244 0.167 0.076 0.334 0.151 0.502 0.224l0.124 0.056c0.072 0.031 0.142 0.065 0.214 0.096 0.249 0.105 0.501 0.2 0.752 0.299 0.116 0.045 0.231 0.093 0.348 0.137 0.101 0.038 0.201 0.08 0.302 0.117 0.133 0.049 0.269 0.091 0.403 0.138 0.334 0.118 0.669 0.234 1.006 0.34 0.202 0.064 0.407 0.12 0.611 0.18 0.275 0.081 0.549 0.161 0.825 0.234 0.221 0.058 0.445 0.111 0.668 0.164 0.266 0.064 0.532 0.127 0.799 0.184a38.923 38.923 0 0 0 1.5 0.282 29.71 29.71 0 0 0 1.55 0.215c0.204 0.023 0.407 0.048 0.612 0.067 0.342 0.033 0.685 0.057 1.027 0.078 0.164 0.01 0.326 0.024 0.49 0.032a32.6 32.6 0 0 0 1.532 0.039H512.013c0.51 0 1.021-0.015 1.531-0.039 0.166-0.008 0.331-0.022 0.496-0.033 0.34-0.021 0.68-0.045 1.019-0.078 0.209-0.02 0.416-0.044 0.623-0.068 0.291-0.033 0.581-0.069 0.871-0.111 0.227-0.032 0.452-0.068 0.677-0.104 0.269-0.044 0.537-0.093 0.805-0.144a26.758 26.758 0 0 0 1.472-0.318c0.231-0.055 0.461-0.109 0.69-0.17 0.265-0.07 0.528-0.148 0.791-0.225 0.215-0.063 0.432-0.122 0.645-0.19 0.313-0.099 0.623-0.208 0.934-0.316 0.158-0.055 0.318-0.105 0.475-0.163 0.099-0.036 0.198-0.078 0.297-0.116 0.115-0.043 0.228-0.09 0.342-0.135 0.25-0.098 0.502-0.193 0.75-0.297 0.098-0.042 0.194-0.088 0.292-0.131l0.055-0.025c0.166-0.072 0.331-0.147 0.496-0.222 0.177-0.081 0.355-0.158 0.531-0.243 0.132-0.063 0.26-0.131 0.391-0.196 0.191-0.094 0.381-0.189 0.57-0.287 0.127-0.066 0.253-0.131 0.379-0.199 0.145-0.078 0.287-0.161 0.431-0.241 0.207-0.115 0.414-0.228 0.619-0.348 0.084-0.049 0.167-0.101 0.251-0.152 0.159-0.095 0.314-0.194 0.471-0.292 0.287-0.179 0.572-0.359 0.854-0.548 0.149-0.1 0.295-0.204 0.442-0.306 0.298-0.206 0.595-0.413 0.887-0.631 0.115-0.086 0.225-0.176 0.339-0.263l0.16-0.124c0.135-0.105 0.271-0.207 0.405-0.315 0.254-0.204 0.504-0.416 0.753-0.629 0.104-0.089 0.211-0.172 0.314-0.262l256-224c13.3-11.638 14.648-31.854 3.01-45.155s-31.854-14.647-45.154-3.01L544 569.48V128c0-17.673-14.327-32-32-32-17.673 0-32 14.327-32 32v441.48L277.072 391.917c-13.301-11.638-33.518-10.29-45.155 3.01-11.637 13.301-10.29 33.518 3.011 45.156z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineDownload.defaultProps = {
  size: 18,
};

export default IconLineDownload;
