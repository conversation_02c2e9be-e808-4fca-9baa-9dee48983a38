/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineEye = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M800 512c0-46.9-32.9-89.3-92.7-119.1-52.7-26.4-122-40.9-195.3-40.9s-142.6 14.5-195.3 40.9C256.9 422.7 224 465.1 224 512s32.9 89.3 92.7 119.1c52.7 26.4 122 40.9 195.3 40.9s142.6-14.5 195.3-40.9C767.1 601.3 800 558.9 800 512z m-512 0c0-45.3 95.8-96 224-96s224 50.7 224 96-95.8 96-224 96-224-50.7-224-96zM896 608c-17.7 0-32 14.3-32 32v192c0 17.6-14.4 32-32 32H640c-17.7 0-32 14.3-32 32s14.3 32 32 32h192c52.9 0 96-43.1 96-96V640c0-17.7-14.3-32-32-32zM832 96H640c-17.7 0-32 14.3-32 32s14.3 32 32 32h192c17.6 0 32 14.4 32 32v192c0 17.7 14.3 32 32 32s32-14.3 32-32V192c0-52.9-43.1-96-96-96zM128 416c17.7 0 32-14.3 32-32V192c0-17.6 14.4-32 32-32h192c17.7 0 32-14.3 32-32s-14.3-32-32-32H192c-52.9 0-96 43.1-96 96v192c0 17.7 14.3 32 32 32zM384 864H192c-17.6 0-32-14.4-32-32V640c0-17.7-14.3-32-32-32s-32 14.3-32 32v192c0 52.9 43.1 96 96 96h192c17.7 0 32-14.3 32-32s-14.3-32-32-32z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M512 464c-26.5 0-48 21.5-48 48s21.5 48 48 48 48-21.5 48-48-21.5-48-48-48z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineEye.defaultProps = {
  size: 18,
};

export default IconLineEye;
