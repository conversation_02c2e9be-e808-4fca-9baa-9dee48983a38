/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineExamine1 = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M416 896H224c-17.6 0-32-14.4-32-32V160c0-17.6 14.4-32 32-32h576c17.6 0 32 14.4 32 32v256c0 17.7 14.3 32 32 32s32-14.3 32-32V160c0-52.9-43.1-96-96-96H224c-52.9 0-96 43.1-96 96v704c0 52.9 43.1 96 96 96h192c17.7 0 32-14.3 32-32s-14.3-32-32-32z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M704 288c0-17.7-14.3-32-32-32H352c-17.7 0-32 14.3-32 32s14.3 32 32 32h320c17.7 0 32-14.3 32-32zM352 480c-17.7 0-32 14.3-32 32s14.3 32 32 32h192c17.7 0 32-14.3 32-32s-14.3-32-32-32H352zM864 768h-96v-37.5c37.2-13.2 64-48.8 64-90.5 0-52.9-43.1-96-96-96s-96 43.1-96 96c0 41.7 26.8 77.3 64 90.5V768h-96c-52.9 0-96 43.1-96 96v32c0 35.3 28.7 64 64 64h320c35.3 0 64-28.7 64-64v-32c0-52.9-43.1-96-96-96zM736 608c17.6 0 32 14.4 32 32s-14.4 32-32 32-32-14.4-32-32 14.4-32 32-32z m160 288H576v-32c0-17.6 14.4-32 32-32h256c17.6 0 32 14.4 32 32v32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineExamine1.defaultProps = {
  size: 18,
};

export default IconLineExamine1;
