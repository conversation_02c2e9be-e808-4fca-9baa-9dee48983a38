/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineBriefcase = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M864 256h-64v-32c0-52.9-43.1-96-96-96H320c-52.9 0-96 43.1-96 96v32h-64c-52.9 0-96 43.1-96 96v448c0 52.9 43.1 96 96 96h704c52.9 0 96-43.1 96-96V352c0-52.9-43.1-96-96-96z m-576-32c0-17.6 14.4-32 32-32h384c17.6 0 32 14.4 32 32v32H288v-32z m608 576c0 17.6-14.4 32-32 32H160c-17.6 0-32-14.4-32-32V544h260c14.2 55.1 64.4 96 124 96 59.5 0 109.7-40.9 124-96h260v256zM448 512c0-35.3 28.7-64 64-64s64 28.7 64 64-28.7 64-64 64-64-28.7-64-64z m448-32H636c-14.2-55.1-64.4-96-124-96-59.5 0-109.7 40.9-124 96H128V352c0-17.6 14.4-32 32-32h704c17.6 0 32 14.4 32 32v128z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconLineBriefcase.defaultProps = {
  size: 18,
};

export default IconLineBriefcase;
