/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconJiaotongtubiaoYouzhuan = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M968.704 451.072l-392.192-392.192c-29.184-29.184-75.776-29.184-104.96 0L78.848 451.072c-29.184 29.184-29.184 75.776 0 104.96L471.04 948.224c29.184 29.184 75.776 29.184 104.96 0l392.192-392.192c29.184-29.184 29.184-75.776 0.512-104.96z m-392.192 100.352v-49.152H405.504v121.856H335.36V435.2h241.152V382.976l135.68 84.48-135.68 83.968z"
        fill={getIconColor(color, 0, '#FBB03B')}
      />
    </svg>
  );
};

IconJiaotongtubiaoYouzhuan.defaultProps = {
  size: 18,
};

export default IconJiaotongtubiaoYouzhuan;
