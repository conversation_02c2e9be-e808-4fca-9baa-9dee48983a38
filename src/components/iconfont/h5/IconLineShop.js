/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineShop = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M928 342.01c0.06-12.75-5-27.04-55.02-138.29-11.91-26.48-23.19-51.57-30.46-68.74C832.49 111.3 809.36 96 783.61 96H242.2c-25.6 0-48.66 15.16-58.74 38.59-7.39 17.17-18.94 42.37-31.11 68.98C101.19 315.31 96 329.65 96 342.48c0.06 53.18 22.84 102.76 61.4 137.23V832c0 52.935 43.065 96 96 96h517.215c52.935 0 96-43.065 96-96h-0.015V479.32c38.61-34.55 61.41-84.28 61.4-137.31z m-717.46-111.8c12.41-27.11 24.16-52.79 31.66-70.21h541.41c7.41 17.49 18.88 43.02 30.99 69.96 17.92 39.86 44.63 99.28 49.36 114.8-0.84 36.22-17.91 69.73-45.81 91.73a31.844 31.844 0 0 0-5.08 3.77c-3.76 2.65-7.67 5.12-11.77 7.35-15.95 8.75-35.33 13.23-57.44 13.34-35.72 0-69.25-15.53-91.99-42.62-12.2-14.5-36.89-14.48-49.03 0.01-22.54 26.85-55.62 42.41-90.34 42.69-35.46-0.21-68.77-15.75-91.4-42.6a31.971 31.971 0 0 0-24.47-11.39h-0.03a32 32 0 0 0-24.47 11.42c-22.78 27.13-56.24 42.75-91.71 42.85-21.41 0-40.17-4.13-55.8-12.28-0.28-0.17-0.58-0.32-0.86-0.48-3.99-2.13-7.82-4.47-11.5-6.99-2.3-2.36-4.98-4.37-7.92-5.92a120.182 120.182 0 0 1-44.29-90.17c4.92-15.71 32.19-75.28 50.49-115.26zM770.615 864H253.4c-17.645 0-32-14.355-32-32V516.67c18.17 5.73 37.94 8.64 59.11 8.64 21.42-0.07 42.35-3.81 61.97-10.85a183.46 183.46 0 0 0 54.2-30.69c32.3 26.26 72.96 41 115.87 41.26 42.31-0.33 82.69-15.11 114.82-41.37 32.53 26.54 73.52 41.29 116.65 41.29 20.97-0.1 40.57-3.03 58.58-8.7V832h0.015c0 17.645-14.355 32-32 32z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M512 640c-17.673 0-32 14.327-32 32v96c0 17.673 14.327 32 32 32 17.674 0 32-14.327 32-32v-96c0-17.673-14.327-32-32-32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineShop.defaultProps = {
  size: 18,
};

export default IconLineShop;
