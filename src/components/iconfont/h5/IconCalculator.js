/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconCalculator = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M298.666667 85.333333h426.666666a85.333333 85.333333 0 0 1 85.333334 85.333334v682.666666a85.333333 85.333333 0 0 1-85.333334 85.333334H298.666667a85.333333 85.333333 0 0 1-85.333334-85.333334V170.666667a85.333333 85.333333 0 0 1 85.333334-85.333334m0 85.333334v170.666666h426.666666V170.666667H298.666667m0 256v85.333333h85.333333v-85.333333H298.666667m170.666666 0v85.333333h85.333334v-85.333333h-85.333334m170.666667 0v85.333333h85.333333v-85.333333h-85.333333m-341.333333 170.666666v85.333334h85.333333v-85.333334H298.666667m170.666666 0v85.333334h85.333334v-85.333334h-85.333334m170.666667 0v85.333334h85.333333v-85.333334h-85.333333m-341.333333 170.666667v85.333333h85.333333v-85.333333H298.666667m170.666666 0v85.333333h85.333334v-85.333333h-85.333334m170.666667 0v85.333333h85.333333v-85.333333h-85.333333z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconCalculator.defaultProps = {
  size: 18,
};

export default IconCalculator;
