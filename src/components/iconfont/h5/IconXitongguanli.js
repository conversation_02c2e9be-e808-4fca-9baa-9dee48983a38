/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconXitongguanli = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M381.3 87.3h-238C99.7 87.3 64 123 64 166.7v238c0 43.6 35.7 79.3 79.3 79.3h238c43.6 0 79.3-35.7 79.3-79.3v-238c0.1-43.7-35.6-79.4-79.3-79.4zM381.3 563.3h-238C99.7 563.3 64 599 64 642.7v238c0 43.6 35.7 79.3 79.3 79.3h238c43.6 0 79.3-35.7 79.3-79.3v-238c0.1-43.7-35.6-79.4-79.3-79.4zM857.3 563.3h-238c-43.6 0-79.3 35.7-79.3 79.3v238c0 43.6 35.7 79.3 79.3 79.3h238c43.6 0 79.3-35.7 79.3-79.3v-238c0.1-43.6-35.6-79.3-79.3-79.3zM936.8 229.6L794.4 87.2c-30.9-30.9-81.3-30.9-112.2 0L539.8 229.6c-30.9 30.9-30.9 81.3 0 112.2l142.4 142.4c30.9 30.9 81.3 30.9 112.2 0l142.4-142.4c30.9-30.9 30.9-81.4 0-112.2z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconXitongguanli.defaultProps = {
  size: 18,
};

export default IconXitongguanli;
