/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconMobileRetro = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M192 128C192 57.4 249.4 0 320 0h384c70.6 0 128 57.4 128 128v768c0 70.6-57.4 128-128 128H320c-70.6 0-128-57.4-128-128V128z m128 192v128c0 35.4 28.6 64 64 64h256c35.4 0 64-28.6 64-64v-128c0-35.4-28.6-64-64-64H384c-35.4 0-64 28.6-64 64z m32 384a48 48 0 1 0 0-96 48 48 0 1 0 0 96z m48 112a48 48 0 1 0-96 0 48 48 0 1 0 96 0z m112-112a48 48 0 1 0 0-96 48 48 0 1 0 0 96z m48 112a48 48 0 1 0-96 0 48 48 0 1 0 96 0z m112-112a48 48 0 1 0 0-96 48 48 0 1 0 0 96z m48 112a48 48 0 1 0-96 0 48 48 0 1 0 96 0zM448 96c-17.6 0-32 14.4-32 32s14.4 32 32 32h128c17.6 0 32-14.4 32-32s-14.4-32-32-32h-128z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconMobileRetro.defaultProps = {
  size: 18,
};

export default IconMobileRetro;
