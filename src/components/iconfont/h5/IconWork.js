/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconWork = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M905.173333 628.266667c17.621333 0 31.914667 14.293333 31.914667 31.914666 0 0.938667-6.016 76.373333-7.381333 99.541334-2.432 40.021333-17.962667 80.853333-41.557334 109.269333-32.853333 39.594667-74.709333 58.112-131.626666 58.24h-28.501334l-7.082666 0.042667-117.76 0.042666H491.946667a32 32 0 1 1 0.042666-64H604.416l116.309333-0.042666H756.394667c38.485333-0.128 61.610667-9.984 82.474666-35.157334 14.805333-17.792 25.386667-46.165333 26.965334-72.234666 1.365333-23.552 7.936-98.133333 7.936-98.133334a31.616 31.616 0 0 1 31.402666-29.44z m-786.773333 0c16.768 0 30.08 13.056 31.402667 29.482666 0 0 6.570667 74.666667 7.978666 98.133334 1.578667 26.069333 12.117333 54.442667 26.922667 72.192 20.906667 25.258667 44.032 35.072 82.474667 35.157333l18.816 0.042667H315.605333c17.664 0 32.128 14.336 32.128 32s-14.506667 32-32.213333 32H267.136c-57.002667-0.170667-98.858667-18.688-131.712-58.325334-23.552-28.373333-39.125333-69.205333-41.514667-109.226666-1.408-23.210667-7.424-98.56-7.424-99.541334 0-17.621333 14.293333-31.914667 31.914667-31.914666z m393.386667-3.925334a32 32 0 0 1 32 32v55.210667a32 32 0 0 1-64 0v-55.210667a32 32 0 0 1 32-32zM566.869333 85.333333a126.421333 126.421333 0 0 1 125.184 110.122667h83.754667a162.645333 162.645333 0 0 1 162.432 162.432v147.925333a32.085333 32.085333 0 0 1-14.336 26.709334c-87.978667 58.197333-192.426667 97.024-301.994667 112.170666a32 32 0 0 1-35.413333-23.850666 76.970667 76.970667 0 0 0-74.922667-57.685334c-35.882667 0-66.389333 23.253333-75.904 57.856a31.701333 31.701333 0 0 1-35.285333 23.253334c-108.842667-15.146667-212.821333-53.76-300.672-111.744A31.957333 31.957333 0 0 1 85.333333 505.813333V357.888a162.816 162.816 0 0 1 162.858667-162.432L331.52 195.413333A126.378667 126.378667 0 0 1 456.704 85.333333z m0 64h-110.165333a62.293333 62.293333 0 0 0-62.250667 62.208l0.042667 15.786667a32 32 0 0 1-32 32.042667l-114.261333 0.085333A98.773333 98.773333 0 0 0 149.333333 357.888v130.432c70.016 43.434667 150.570667 73.770667 235.136 88.661333a143.146667 143.146667 0 0 1 127.104-77.824c54.656 0 103.296 30.976 126.677334 78.208 85.077333-14.976 165.888-45.482667 235.989333-89.045333V357.888a98.56 98.56 0 0 0-98.432-98.432h-264.021333a32 32 0 0 1 0-64h115.2A62.378667 62.378667 0 0 0 566.869333 149.333333z"
        fill={getIconColor(color, 0, '#000000')}
      />
    </svg>
  );
};

IconWork.defaultProps = {
  size: 18,
};

export default IconWork;
