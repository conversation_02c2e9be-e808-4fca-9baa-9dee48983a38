/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLogoWechat = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M308.788 298.212c-13.238 0-26.634 4.411-36.753 12.101-11.914 9.054-18.475 21.8-18.475 35.89s6.561 26.835 18.475 35.89c10.09 7.668 23.438 12.075 36.639 12.101 0.319 0.005 0.636 0.008 0.953 0.008 13.056 0 24.868-4.553 33.354-12.881 8.875-8.708 13.762-21.18 13.762-35.117 0-13.569-4.78-25.826-13.459-34.513-8.681-8.693-20.932-13.479-34.496-13.479zM549.856 346.606c0-13.569-4.78-25.826-13.46-34.514-8.683-8.69-20.934-13.477-34.497-13.477-13.238 0-26.634 4.411-36.753 12.101-11.914 9.054-18.475 21.8-18.475 35.89 0 29.642 28.667 47.99 55.228 47.99 28.237 0.001 47.957-19.734 47.957-47.99z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M928 601.773c0-56.698-25.041-111.096-70.468-153.134-41.971-39.21-97.243-64.224-156.525-71.012-13.899-57.481-51.261-110.031-105.729-148.492C539.443 189.712 469.695 168 398.88 168 231.871 168 96 285.129 96 429.101c0 38.591 9.743 75.517 28.957 109.75 17.878 31.853 43.692 60.866 76.807 86.347l-21.49 64.891c-3.457 9.876-0.506 20.64 7.721 28.127l0.474 0.409c8.092 6.627 19.638 8.019 28.73 3.469l88.298-44.191 8.032 1.787h0.547l3.838 0.76c26.362 5.224 51.261 10.158 80.966 10.158 3.076 0 16.632-0.868 27.902-3.02 39.851 84.708 135.453 140.915 241.566 140.915 27.203 0 54.896-6.078 79.19-12.158l67.198 36.515 0.652 0.261c0.306 0.123 0.622 0.257 0.946 0.394 2.491 1.05 5.902 2.487 9.978 2.487 8.214 0 13.567-3.21 16.745-6.146 7.647-6.154 10.985-16.404 8.725-26.968l-0.108-0.505-15.967-52.21C895.294 720.644 928 661.097 928 601.773z m-519.331 28.218c-3.708 0.137-7.339 0.221-11.001 0.221-26.133 0-48.938-4.786-73.083-9.854l-15.641-2.888-0.478-0.078c-5.433-0.779-10.55 0.196-13.885 1.149l-1.103 0.315-37.639 19.079 6.911-20.078c3.729-10.559 0.099-22.025-9.261-29.232l-0.456-0.334c-63.076-44.003-95.058-97.291-95.058-158.382 0-53.152 24.747-103.358 69.682-141.371 45.54-38.524 106.061-59.74 170.414-59.74 109.807 0 209.281 63.301 238.381 149.422-59.762 4.473-115.446 27.178-158.184 64.768-46.949 41.294-72.805 95.674-72.805 153.124 0.001 11.109 1.076 22.454 3.206 33.879z m374.329 102.726c-8.748 6.034-12.655 17.691-9.345 27.934l1.616 5.371-17.759-9.727-0.671-0.269c-0.305-0.122-0.621-0.256-0.945-0.393-2.491-1.051-5.903-2.489-9.979-2.489-2.077 0-4.443 0.127-6.813 0.93l-4.553 1.125c-22.42 5.55-45.603 11.29-67.414 11.29-108.999 0-197.676-73.892-197.676-164.716 0-90.602 88.677-164.312 197.676-164.312 106.714 0 196.868 75.431 196.868 164.816 0.377 45.036-28.389 91.353-81.005 130.44z"
        fill={getIconColor(color, 1, '#333333')}
      />
      <path
        d="M777.442 510.93c-8.958-8.091-21.158-12.547-34.354-12.547-21.12 0-41.088 20.178-41.088 41.52 0 21.343 19.968 41.521 41.088 41.521 29.621 0 47.956-21.552 47.956-41.521 0-10.605-4.958-21.165-13.602-28.973zM625.942 510.93c-8.958-8.091-21.158-12.547-34.354-12.547-21.328 0-41.492 20.178-41.492 41.52 0 21.343 20.164 41.521 41.492 41.521 29.621 0 47.956-21.552 47.956-41.521 0-10.605-4.958-21.165-13.602-28.973z"
        fill={getIconColor(color, 2, '#333333')}
      />
    </svg>
  );
};

IconLogoWechat.defaultProps = {
  size: 18,
};

export default IconLogoWechat;
