/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconBad = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M127.168 767.168c-17.6 0.16-31.68 14.464-31.616 32.064 0.032 17.6 14.368 31.872 31.968 31.936 3.68 0.032 90.912 1.376 169.568 87.52 6.304 6.944 14.976 10.432 23.648 10.432 7.68 0 15.424-2.752 21.568-8.384 13.056-11.904 13.984-32.16 2.048-45.184C245.632 767.36 131.776 767.328 127.168 767.168z"
        fill={getIconColor(color, 0, '#5D646F')}
      />
      <path
        d="M867.872 768.672c-5.824-0.48-142.304-9.024-250.112 106.624-12.064 12.896-11.328 33.152 1.6 45.216 6.144 5.76 14.016 8.608 21.824 8.608 8.576 0 17.088-3.392 23.392-10.176 86.816-93.12 194.016-86.72 198.656-86.432 17.376 1.408 32.928-12.032 34.176-29.632C898.688 785.28 885.504 769.984 867.872 768.672z"
        fill={getIconColor(color, 1, '#5D646F')}
      />
      <path
        d="M926.176 546.848c-24.832-60.64-76.864-115.744-121.44-158.016 39.84-28.928 69.376-66.336 88.064-111.584 5.376-12.928 1.664-27.872-9.088-36.8-102.048-84.96-275.008-144.256-420.512-144.256-120.864 0-210.528 39.2-259.328 113.312l0.096 0.064C193.92 221.92 181.728 244.96 167.392 287.776c-0.544 1.568-0.928 3.2-1.216 4.864C159.168 319.36 157.76 347.072 161.344 375.136c-0.096 1.056-0.608 1.952-0.576 3.04 0.128 12.96 6.656 320.16 300.448 573.824 6.048 5.216 13.472 7.776 20.896 7.776 8.992 0 17.92-3.744 24.224-11.104 11.552-13.376 10.08-33.568-3.328-45.12-128.288-110.784-197.056-233.632-234.336-332.896 104.768 106.016 262.272 180.896 377.888 196.928 1.472 0.224 2.944 0.32 4.416 0.32 10.848 0 21.056-5.504 26.976-14.784 31.904-50.016 45.376-101.6 39.616-143.328 7.136 0.192 14.208 0.288 21.312 0.288 99.36 0 164.416-19.68 167.136-20.512 8.576-2.656 15.648-8.8 19.488-16.896C929.344 564.544 929.6 555.168 926.176 546.848zM738.88 546.048c-22.4 0-45.12-1.088-67.456-3.296-13.44-1.312-26.016 5.824-31.904 17.856-5.856 12.032-3.584 26.4 5.632 36.096 14.496 15.168 13.44 57.376-10.72 103.936-122.24-24.288-291.456-116.832-370.56-235.808-36.576-54.976-48.576-107.584-35.648-156.384 0.16-0.64 0.32-1.28 0.448-1.888 4.224-11.232 22.752-51.2 27.904-60.704 0.288-0.384 0.576-0.8 0.832-1.216 45.984-69.856 137.312-84.512 205.856-84.512 120.832 0 267.552 47.2 359.968 114.24-19.136 34.016-47.904 61.184-85.792 80.96-9.248 4.832-15.552 13.888-16.896 24.224-1.376 10.336 2.4 20.704 10.08 27.744 36 32.96 86.976 79.648 118.368 129.504C823.008 541.376 784.992 546.048 738.88 546.048z"
        fill={getIconColor(color, 2, '#5D646F')}
      />
    </svg>
  );
};

IconBad.defaultProps = {
  size: 18,
};

export default IconBad;
