/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineFile = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M800 64H352c-52.935 0-96 43.065-96 96v32h-32c-52.935 0-96 43.065-96 96v576c0 52.935 43.065 96 96 96h448c52.935 0 96-43.065 96-96v-32h32c52.935 0 96-43.065 96-96V160c0-52.935-43.065-96-96-96z m-96 800c0 17.645-14.355 32-32 32H224c-17.645 0-32-14.355-32-32V288c0-17.645 14.355-32 32-32h448c17.645 0 32 14.355 32 32v576z m128-128c0 17.645-14.355 32-32 32h-32V288c0-52.935-43.065-96-96-96H320v-32c0-17.645 14.355-32 32-32h448c17.645 0 32 14.355 32 32v576z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M608 383.99H288c-17.673 0-32 14.327-32 32s14.327 32 32 32h320c17.673 0 32-14.327 32-32s-14.327-32-32-32zM480 576H288c-17.673 0-32 14.327-32 32s14.327 32 32 32h192c17.673 0 32-14.327 32-32s-14.327-32-32-32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineFile.defaultProps = {
  size: 18,
};

export default IconLineFile;
