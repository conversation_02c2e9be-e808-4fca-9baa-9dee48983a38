/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineContacts = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M800 64H288c-52.935 0-96 43.065-96 96v96h-32c-17.673 0-32 14.327-32 32s14.327 32 32 32h32v384h-32c-17.673 0-32 14.327-32 32s14.327 32 32 32h32v96c0 52.935 43.065 96 96 96h512c52.935 0 96-43.065 96-96V160c0-52.935-43.065-96-96-96z m32 800c0 17.645-14.355 32-32 32H288c-17.645 0-32-14.355-32-32v-96h32c17.673 0 32-14.327 32-32s-14.327-32-32-32h-32V320h32c17.673 0 32-14.327 32-32s-14.327-32-32-32h-32v-96c0-17.645 14.355-32 32-32h512c17.645 0 32 14.355 32 32v704z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M736 256c-17.673 0-32 14.327-32 32v448c0 17.673 14.327 32 32 32s32-14.327 32-32V288c0-17.673-14.327-32-32-32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineContacts.defaultProps = {
  size: 18,
};

export default IconLineContacts;
