/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const Icon008Man = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M840.745552 905.424017v84.200492c0 9.426702-7.635202 17.061903-17.061904 17.061903h-622.759466c-9.426702 0-17.061903-7.635202-17.061903-17.061903v-84.200492c0-87.954111 71.958577-157.715968 159.912687-158.569063a88.38919 88.38919 0 0 0 71.574684-37.642824l-0.127964-0.127964c17.680397-25.151378 15.927287-47.811718 15.952879-65.027179l0.319911-0.789113c-76.458654-31.692485-130.246304-107.063443-130.288958-194.953571v-6.952726c-30.611187 0-53.29712-24.279088-53.275793-52.358715l0.149292-207.856636c0.021327-31.799122 25.806129-57.583923 57.60525-57.562596 25.86158-64.504658 88.372128-106.572913 157.588004-106.530258l229.418616 0.170619c11.922005 0.021327 20.175701 11.96466 15.931552 23.118879l-14.609255 38.517247c-3.519018 9.320065 1.642208 19.663843 11.196874 22.436402l11.815368 3.433708c35.414113 10.281929 62.084 42.550254 62.084 82.195719 0 16.016862-0.063982 128.540113-0.106637 198.963119 0 30.647444-24.867724 55.472513-55.515167 55.472512v6.952726c-0.063982 88.082075-54.043578 163.538342-130.715506 195.12419v0.021328l0.255929 0.597166c0 16.437011-1.925862 39.592146 15.931552 65.048506 15.558323 22.2082 41.944556 37.431683 71.873267 37.749461 87.954111 0.853095 159.912688 70.614952 159.912688 158.569063z"
        fill={getIconColor(color, 0, '#FFDFBA')}
      />
      <path
        d="M840.745552 905.424017v84.200492c0 9.426702-7.635202 17.061903-17.061904 17.061903h-622.759466c-9.426702 0-17.061903-7.635202-17.061903-17.061903v-84.200492c0-87.954111 71.958577-157.715968 159.912687-158.569063 29.084147-0.309247 55.440522-14.626317 71.596012-37.685479 57.686295 64.103703 68.44809 76.059832 84.243147 93.627194 6.782107 7.528565 18.597474 7.528565 25.379581 0 16.161888-17.974715 27.235063-30.276347 84.11305-93.482167 15.458084 22.063174 41.897636 37.224807 71.726108 37.540452 87.954111 0.853095 159.912688 70.614952 159.912688 158.569063z"
        fill={getIconColor(color, 1, '#D6F4FC')}
      />
      <path
        d="M635.746785 789.680331c84.413766 3.838928 151.680319 73.515475 151.680319 158.867646v58.138435h36.256544a17.061903 17.061903 0 0 0 17.061904-17.061903v-84.068262c0-87.983969-71.986302-157.799144-159.968139-158.705558a88.3508 88.3508 0 0 1-33.940391-7.119079c-0.266592 0.563043-10.958007 49.338759-11.090237 49.948721z"
        fill={getIconColor(color, 2, '#BDEFFC')}
      />
      <path
        d="M646.837022 739.73161c-0.264459 0.563043-21.314583 96.322974-21.18022 95.713011-2.776825 12.598083-18.721173 16.65455-27.181745 6.914336-55.999299-64.485463-48.359832-56.076078-54.030782-61.190383l64.662481-71.856205c9.384047 13.395727 22.523845 23.893063 37.730266 30.419241zM415.370978 709.169475l63.257006 70.295042-52.471751 62.179973c-8.385925 9.938559-24.526486 5.954604-27.324638-6.74585-0.134362-0.60783-20.794195-94.60612-21.060787-95.169163a88.608862 88.608862 0 0 0 37.60017-30.560002z"
        fill={getIconColor(color, 3, '#E8F8FC')}
      />
      <path
        d="M779.109426 186.925946c0 16.016862-0.063982 128.540113-0.106637 198.963119 0 30.647444-24.867724 55.472513-55.515167 55.472512 0.021327-23.204188 0.063982-79.572451 0.085309-131.440636 0.042655-56.538882-45.747228-102.392747-102.286109-102.435402l-217.539266-0.149291c-56.538882-0.042655-102.414074 45.768555-102.456729 102.307437l-0.085309 131.717892c-30.611187 0-53.29712-24.279088-53.275793-52.358715l0.149292-207.856636c0.021327-31.799122 25.806129-57.583923 57.60525-57.562596 25.86158-64.506791 88.372128-106.572913 157.588004-106.530258l229.416483 0.172752c11.934801 0.008531 20.171435 11.953996 15.940083 23.112481l-14.611387 38.519379c-3.531814 9.309401 1.627279 19.65318 11.188343 22.43427l11.8175 3.435841c35.416246 10.281929 62.086133 42.552387 62.086133 82.197851z"
        fill={getIconColor(color, 4, '#42434D')}
      />
      <path
        d="M621.286822 207.485539c56.538882 0.042655 102.328764 45.89652 102.286109 102.435402-0.019195 48.321443-0.08531 138.393362-0.085309 138.393362-0.063982 88.082075-54.043578 163.538342-130.715506 195.12419v0.021328c-138.781521 57.229889-291.494085-45.150061-291.566598-195.145518 0-11.128626 0.076779-124.419664 0.085309-138.670618 0.042655-56.538882 45.917847-102.350092 102.456729-102.307437z"
        fill={getIconColor(color, 5, '#FFEBD2')}
      />
      <path
        d="M595.757949 679.140526c-52.544264 21.994926-110.288142 23.701116-166.908068 0 2.891993-11.998783 2.409994-19.77048 2.409994-35.019557l0.319911-0.789113c51.360594 21.318848 109.987426 21.361503 161.448259 0l0.31991 0.789113c0 15.253341-0.481999 23.025038 2.409994 35.019557z"
        fill={getIconColor(color, 6, '#FFD6A6')}
      />
      <path
        d="M464.496462 584.525874c34.714575 21.71767 7.340884 74.978534-30.66024 59.725192-78.13072-31.359778-132.673359-107.498521-132.673359-196.06686v-138.41469a101.908615 101.908615 0 0 1 17.964051-57.942223c14.197636-20.644903 46.173776-10.407761 46.020219 14.647644-0.004265 0.522521-0.002133 138.53199-0.002133 139.054511 0 77.868393 40.931506 142.447697 99.351462 178.996426z"
        fill={getIconColor(color, 7, '#FFF3E4')}
      />
      <path
        d="M708.622439 40.34287c-15.121112 39.869402-14.905705 38.939529-15.270404 40.820604l-166.097627-0.127965c-64.468401-0.040522-123.125092 36.452756-151.763496 93.590937-3.591531 7.165999-10.089983 12.534101-17.927795 14.206168-25.989544 5.547251-45.482768 28.649068-45.499831 56.295749l-0.021327 18.874731c-6.867416 13.734832-10.727672 29.239837-10.748999 45.640591l-0.051186 80.005397c-0.02346 34.548221-53.337642 36.838782-53.309916-0.64622l0.149292-207.856636c0.021327-31.799122 25.806129-57.583923 57.60525-57.562596 25.86158-64.504658 88.372128-106.572913 157.588004-106.530258l229.418615 0.170619c11.919872 0.021327 20.173568 11.96466 15.92942 23.118879z"
        fill={getIconColor(color, 8, '#4D4E59')}
      />
      <path
        d="M416.714602 798.744467c3.177779 14.438636 6.568833 29.815676 9.448029 42.889359-8.385925 9.940691-24.526486 5.969533-27.322505-6.733053-8.200377-37.241869-20.888035-94.789536-21.067185-95.169163a88.628056 88.628056 0 0 0 37.600169-30.562135l18.213582 20.26101c-15.656429 19.493224-22.289244 44.766169-16.87209 69.313982z"
        fill={getIconColor(color, 9, '#F1FAFC')}
      />
      <path
        d="M388.861046 789.680331c-84.413766 3.838928-151.680319 73.515475-151.68032 158.867646v58.138435h-36.256544a17.061903 17.061903 0 0 1-17.061903-17.061903v-84.068262c0-87.983969 71.986302-157.799144 159.968139-158.705558a88.3508 88.3508 0 0 0 33.94039-7.119079c0.264459 0.563043 10.955875 49.338759 11.090238 49.948721z"
        fill={getIconColor(color, 10, '#E8F8FC')}
      />
      <path
        d="M681.549464 729.912484c-25.127918-0.017062-47.916222-12.830551-60.960047-34.275231a17.061903 17.061903 0 1 0-29.154528 17.731583 105.333792 105.333792 0 0 0 35.661511 35.584732l-17.735849 80.235733-42.597174-49.119087c7.918856-9.093994 5.685879-19.463366-0.776316-25.089529a17.05977 17.05977 0 0 0-24.072213 1.663536l-30.022551 34.477841-75.509586-84.017077a104.222636 104.222636 0 0 0 11.465599-39.939783c146.111741 42.812581 292.321587-66.370803 292.703348-218.351838 0-5.809578 0.087442-130.41479 0.09384-138.888158 0.04692-66.008238-53.333377-119.461048-119.350145-119.507968l-217.541399-0.149292c-66.338812 0-119.469579 53.604234-119.514366 119.352279l-0.076779 110.642176c-11.399484-5.909817-19.181845-17.633477-19.173313-31.259539l0.14076-207.856636c0.014929-22.336164 18.187989-40.494294 40.52202-40.494294a17.057638 17.057638 0 0 0 15.876101-10.714875C344.474504 72.694371 400.04299 34.123806 463.257342 34.123806l229.416483 0.15569-21.15676 55.796689c-3.523283 9.30087 1.629412 19.65318 11.190476 22.43427l29.56188 8.592801c28.254512 8.213174 49.669333 34.098214 49.786633 65.79923l-0.138628 202.439481a17.064036 17.064036 0 0 0 34.123807 0.025593c0-1.260448 0.140761-201.980943 0.136495-203.241391-0.157823-44.892-30.74555-85.104773-74.381367-97.790298l-11.821766-3.435841 23.362011-61.614798c4.224954-11.147821-4.018078-23.101817-15.942216-23.110347L463.278669 0c-73.219025-0.021327-138.760193 42.49267-169.158106 107.417477-35.682838 5.557915-63.086387 36.484747-63.11198 73.707422l-0.140761 207.856636c-0.021327 33.14488 23.167932 60.258377 53.431483 67.496889 3.122328 87.930651 55.822282 162.463442 129.800561 197.792245l-0.002133 4.348653c-0.025593 39.325554-31.905759 71.060694-71.286764 71.060694-92.123614-1.644341-176.211071 75.083038-176.279319 175.982868l-0.07038 100.786794a17.061903 17.061903 0 1 0 34.123806 0.021328l0.070381-100.786795c0.053318-78.393047 63.495873-141.880389 141.976362-141.880389 5.922613 0.104504 14.383184-0.64622 22.39588-2.35241l23.733108 108.061564c3.011426 13.702841 20.378311 18.070688 29.540552 7.534963l61.439914-70.651208 15.289598 17.01285-0.07038 102.222128a17.061903 17.061903 0 1 0 34.123806 0.021327l0.07038-102.388481 14.980351-17.204797 61.674515 71.116145c9.181437 10.589044 26.533392 6.159347 29.549083-7.496573l23.880267-108.031706a106.988797 106.988797 0 0 0 22.289244 2.386534c78.467693 0.051186 141.942238 63.596111 141.884654 142.076601l-0.068248 100.786794a17.061903 17.061903 0 1 0 34.123807 0.021328l0.068247-100.786795c0.07038-97.325361-78.642577-176.157752-175.987133-176.219602zM318.267421 447.934676l0.093841-138.288859c0.031991-47.022605 38.293309-85.251932 85.309515-85.251932l217.59685 0.149292c47.146304 0.031991 85.286056 38.201601 85.251932 85.360702l-0.095973 139.109962c-0.447875 106.79685-86.943193 193.240983-194.211379 193.240983-106.939744-0.074646-193.944786-86.471858-193.944786-194.320148z m96.544779 381.122395l-17.631344-80.282653a105.898968 105.898968 0 0 0 18.239175-13.941707l41.336726 45.992492z"
        fill={getIconColor(color, 11, '#333333')}
      />
    </svg>
  );
};

Icon008Man.defaultProps = {
  size: 18,
};

export default Icon008Man;
