/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconActivity = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M719.328 640c-64.608 0-121.44-28.736-133.12-35.072C575.776 600.768 511.52 576 460 576c-46.688 0-108 21.312-127.68 29.536-9.888 4.128-21.152 3.04-30.08-2.912C293.344 596.672 288 586.688 288 576L288 160c0-13.024 7.904-24.736 19.936-29.632C311.392 128.96 393.248 96 463.328 96c70.656 0 146.464 33.344 149.664 34.752 1.184 0.512 2.336 1.12 3.456 1.792C616.832 132.768 663.424 160 716.672 160c52.992 0 98.208-27.04 98.656-27.328 9.952-5.952 22.24-6.208 32.352-0.544C857.728 137.824 864 148.448 864 160l0 416c0 10.848-5.504 20.928-14.592 26.848C847.104 604.384 791.328 640 719.328 640zM460 512c68.928 0 148.928 33.056 152.32 34.464 1.216 0.512 2.4 1.088 3.552 1.76C616.32 548.48 665.728 576 719.328 576c33.984 0 63.776-11.2 80.672-19.136L800 209.312C777.28 217.12 748.288 224 716.672 224c-64.768 0-119.552-28.864-131.008-35.328C575.808 184.512 515.648 160 463.328 160 424.224 160 377.856 173.6 352 182.4l0 348.48C381.92 521.728 422.592 512 460 512z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M544 928 192 928 192 128c0-17.664 14.336-32 32-32s32 14.336 32 32l0 736 288 0c17.696 0 32 14.304 32 32S561.696 928 544 928z"
        fill={getIconColor(color, 1, '#333333')}
      />
      <path
        d="M224 928 160.992 928c-17.664 0-32-14.304-32-32s14.336-32 32-32L224 864c17.664 0 32 14.304 32 32S241.664 928 224 928z"
        fill={getIconColor(color, 2, '#333333')}
      />
    </svg>
  );
};

IconActivity.defaultProps = {
  size: 18,
};

export default IconActivity;
