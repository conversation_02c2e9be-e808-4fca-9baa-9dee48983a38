/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconVip1 = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M515.4816 517.632m-436.2752 0a436.2752 436.2752 0 1 0 872.5504 0 436.2752 436.2752 0 1 0-872.5504 0Z"
        fill={getIconColor(color, 0, '#DB90F6')}
      />
      <path
        d="M86.8864 598.9888a428.5952 354.9696 0 1 0 857.1904 0 428.5952 354.9696 0 1 0-857.1904 0Z"
        fill={getIconColor(color, 1, '#D186F7')}
      />
      <path
        d="M110.6432 680.2944a404.8384 273.664 0 1 0 809.6768 0 404.8384 273.664 0 1 0-809.6768 0Z"
        fill={getIconColor(color, 2, '#C276F6')}
      />
      <path
        d="M153.7024 761.6a361.7792 192.3072 0 1 0 723.5584 0 361.7792 192.3072 0 1 0-723.5584 0Z"
        fill={getIconColor(color, 3, '#AF64F6')}
      />
      <path
        d="M224.8704 842.9568c77.1584 69.0176 178.9952 111.0016 290.6624 111.0016s213.504-41.984 290.6624-111.0016c-77.1584-69.0176-178.9952-111.0016-290.6624-111.0016s-213.504 41.984-290.6624 111.0016z"
        fill={getIconColor(color, 4, '#A25BF4')}
      />
      <path
        d="M306.2272 330.4448h131.328v233.5232l197.4272-233.5232h150.6816l-331.3664 416.8704H334.4384V383.3344h-28.2112z"
        fill={getIconColor(color, 5, '#FFFFFF')}
      />
    </svg>
  );
};

IconVip1.defaultProps = {
  size: 18,
};

export default IconVip1;
