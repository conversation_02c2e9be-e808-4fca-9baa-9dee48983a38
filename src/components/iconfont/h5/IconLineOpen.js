/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineOpen = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M864 160H160c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h704c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM864 480H160c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h704c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM864 800H160c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h704c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconLineOpen.defaultProps = {
  size: 18,
};

export default IconLineOpen;
