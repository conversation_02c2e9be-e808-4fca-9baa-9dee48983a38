/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconIconKeyboard = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M242.005 1000.334c-29.013-27.875-28.444-74.524 0-102.969L627.485 512l-385.48-385.365c-28.444-28.445-28.444-74.525 0-102.97s74.525-28.444 102.97 0l437.02 436.907c15.587 13.768 38.912 61.327 0 102.97l-437.02 436.906c-20.595 21.732-67.243 34.247-102.97-0.114z"
        fill={getIconColor(color, 0, '#2c2c2c')}
      />
    </svg>
  );
};

IconIconKeyboard.defaultProps = {
  size: 18,
};

export default IconIconKeyboard;
