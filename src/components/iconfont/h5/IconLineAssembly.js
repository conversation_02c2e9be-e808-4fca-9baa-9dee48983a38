/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineAssembly = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M830.8 534.4c-5.5-102-59.3-195.1-144.7-250.8 0.7-6.6 1.1-13.2 1.1-20 0-97-79-176-176-176s-176 79-176 176c0 6.8 0.4 13.4 1.1 20C250.9 339.5 197 433 191.7 535.1c-62.1 27.2-105.5 89.3-105.5 161.3 0 97 79 176 176 176 39.3 0 75.7-13 105-34.9 44.5 22.4 93.8 34.2 144.1 34.2 50.6 0 100.2-11.9 144.9-34.6 29.4 22.2 66 35.3 105.6 35.3 97 0 176-79 176-176 0-72.6-44.1-135.1-107-162zM511.3 151.7c61.8 0 112 50.2 112 112s-50.2 112-112 112-112-50.2-112-112 50.2-112 112-112zM262.2 808.3c-61.8 0-112-50.2-112-112s50.2-112 112-112 112 50.2 112 112-50.3 112-112 112z m249.1-0.6c-34.1 0-67.7-6.8-98.8-19.8 16.3-26.7 25.7-58 25.7-91.6 0-97-79-176-176-176-1.7 0-3.3 0-5 0.1 8.5-68.6 44.4-131 99.4-172.8 29.9 54.8 88 92 154.6 92 66.7 0 124.8-37.3 154.7-92 55 41.7 91 104 99.4 172.7h-3.5c-97 0-176 79-176 176 0 33.3 9.3 64.5 25.4 91.1-31.4 13.3-65.4 20.3-99.9 20.3z m250.5 0.6c-61.8 0-112-50.2-112-112s50.2-112 112-112 112 50.2 112 112-50.2 112-112 112z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconLineAssembly.defaultProps = {
  size: 18,
};

export default IconLineAssembly;
