/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineBusinesscard = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M864 128H160c-52.935 0-96 43.065-96 96v576c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V224c0-52.935-43.065-96-96-96z m32 672c0 17.645-14.355 32-32 32H160c-17.645 0-32-14.355-32-32V224c0-17.645 14.355-32 32-32h704c17.645 0 32 14.355 32 32v576z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M757.189 520.625C773.896 501.056 784 475.687 784 448c0-61.757-50.243-112-112-112s-112 50.243-112 112c0 27.687 10.104 53.056 26.811 72.625C541.897 548.989 512 599.065 512 656c0 17.673 14.327 32 32 32s32-14.327 32-32c0-52.935 43.065-96 96-96s96 43.065 96 96c0 17.673 14.327 32 32 32s32-14.327 32-32c0-56.935-29.897-107.011-74.811-135.375zM624 448c0-26.467 21.532-48 48-48s48 21.533 48 48-21.532 48-48 48-48-21.533-48-48zM384 352H256c-17.673 0-32 14.327-32 32s14.327 32 32 32h128c17.673 0 32-14.327 32-32s-14.327-32-32-32zM384 480H256c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h128c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM384 608H256c-17.673 0-32 14.327-32 32s14.327 32 32 32h128c17.673 0 32-14.327 32-32s-14.327-32-32-32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineBusinesscard.defaultProps = {
  size: 18,
};

export default IconLineBusinesscard;
