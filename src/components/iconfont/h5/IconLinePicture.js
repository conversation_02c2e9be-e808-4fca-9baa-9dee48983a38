/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLinePicture = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M864 128H160c-52.935 0-96 43.065-96 96v576c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V224c0-52.935-43.065-96-96-96z m32 672c0 17.645-14.355 32-32 32H160c-17.645 0-32-14.355-32-32V224c0-17.645 14.355-32 32-32h704c17.645 0 32 14.355 32 32v576z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M745.372 425.373L575.999 594.745 470.627 489.373c-12.497-12.497-32.758-12.497-45.255 0l-192 192c-12.497 12.497-12.497 32.759 0 45.256C239.621 732.876 247.811 736 256 736s16.379-3.124 22.627-9.372L448 557.255l105.334 105.334 0.038 0.04a31.852 31.852 0 0 0 16.516 8.786 32.117 32.117 0 0 0 12.224 0 31.86 31.86 0 0 0 16.516-8.786l192-192c12.496-12.497 12.496-32.758 0-45.255-12.498-12.499-32.758-12.499-45.256-0.001zM304 416c26.51 0 48-21.49 48-48s-21.49-48-48-48-48 21.49-48 48 21.49 48 48 48z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLinePicture.defaultProps = {
  size: 18,
};

export default IconLinePicture;
