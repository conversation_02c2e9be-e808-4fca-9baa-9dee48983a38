/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineSystem = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M832 97H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V193c0-52.935-43.065-96-96-96z m32 736c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V193c0-17.645 14.355-32 32-32h640c17.645 0 32 14.355 32 32v640z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M768 321H544a32.36 32.36 0 0 0-3.986 0.256C525.858 265.981 475.625 225 416 225s-109.857 40.981-124.014 96.256A32.165 32.165 0 0 0 288 321h-32c-17.673 0-32 14.327-32 32s14.327 32 32 32h32a32.36 32.36 0 0 0 3.986-0.256C306.142 440.019 356.375 481 416 481s109.857-40.981 124.014-96.256c1.307 0.163 2.635 0.256 3.986 0.256h224c17.673 0 32-14.327 32-32s-14.327-32-32-32z m-352 96c-35.29 0-64-28.71-64-64s28.71-64 64-64 64 28.71 64 64-28.71 64-64 64zM768 641h-32a32.36 32.36 0 0 0-3.986 0.256C717.858 585.981 667.625 545 608 545s-109.857 40.981-124.014 96.256A32.165 32.165 0 0 0 480 641H288c-17.673 0-32 14.327-32 32s14.327 32 32 32h192a32.36 32.36 0 0 0 3.986-0.256C498.142 760.019 548.375 801 608 801s109.857-40.981 124.014-96.256c1.307 0.163 2.635 0.256 3.986 0.256h32c17.673 0 32-14.327 32-32s-14.327-32-32-32z m-160 96c-35.29 0-64-28.71-64-64s28.71-64 64-64 64 28.71 64 64-28.71 64-64 64z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineSystem.defaultProps = {
  size: 18,
};

export default IconLineSystem;
