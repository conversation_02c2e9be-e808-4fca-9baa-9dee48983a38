/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineLabel = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M773.818 64H250.182C200.456 64 160 107.065 160 160v768c0 0.133 0.008 0.264 0.01 0.397 0.004 0.342 0.011 0.684 0.026 1.027 0.009 0.195 0.021 0.389 0.033 0.584 0.021 0.333 0.045 0.666 0.076 0.999 0.019 0.206 0.042 0.411 0.065 0.616a29.602 29.602 0 0 0 0.221 1.598 32.635 32.635 0 0 0 0.488 2.393 29.106 29.106 0 0 0 0.406 1.5c0.073 0.245 0.148 0.49 0.226 0.733a34.617 34.617 0 0 0 0.804 2.206 32.265 32.265 0 0 0 0.983 2.18 31.832 31.832 0 0 0 2.472 4.148c0.113 0.16 0.225 0.321 0.341 0.48 0.201 0.276 0.409 0.546 0.619 0.815 0.082 0.105 0.158 0.214 0.242 0.318 0.037 0.046 0.077 0.088 0.114 0.134 0.291 0.359 0.59 0.71 0.896 1.055 0.086 0.098 0.17 0.2 0.257 0.297 0.367 0.406 0.745 0.802 1.133 1.188 0.205 0.205 0.417 0.399 0.627 0.598 0.189 0.179 0.377 0.359 0.571 0.533 0.249 0.224 0.502 0.44 0.757 0.655a31.654 31.654 0 0 0 1.312 1.053c0.154 0.117 0.31 0.23 0.465 0.344 0.288 0.21 0.577 0.419 0.871 0.619 0.158 0.107 0.319 0.209 0.479 0.314 0.297 0.194 0.592 0.39 0.894 0.573 0.213 0.129 0.432 0.249 0.648 0.374 0.392 0.226 0.786 0.444 1.186 0.652 0.344 0.18 0.689 0.357 1.04 0.525 0.152 0.072 0.307 0.138 0.46 0.208 0.374 0.171 0.75 0.337 1.132 0.494 0.133 0.055 0.268 0.106 0.402 0.159 0.41 0.162 0.823 0.316 1.241 0.462 0.112 0.039 0.225 0.076 0.337 0.114 0.445 0.149 0.895 0.289 1.348 0.418 0.092 0.026 0.183 0.053 0.275 0.078 0.474 0.131 0.952 0.251 1.435 0.36 0.078 0.018 0.155 0.037 0.233 0.054 0.492 0.108 0.989 0.203 1.489 0.288l0.213 0.038c0.498 0.081 0.999 0.149 1.504 0.207 0.074 0.009 0.148 0.02 0.222 0.028 0.488 0.052 0.979 0.091 1.473 0.121 0.089 0.005 0.178 0.015 0.267 0.02 0.499 0.026 1.002 0.038 1.508 0.041 0.074 0 0.147 0.006 0.221 0.006 0.51 0 1.02-0.016 1.53-0.04 0.113-0.005 0.224-0.014 0.337-0.021 0.391-0.023 0.782-0.052 1.173-0.089 0.149-0.014 0.298-0.03 0.447-0.046 0.357-0.039 0.713-0.085 1.07-0.136 0.151-0.022 0.303-0.042 0.453-0.066 0.386-0.061 0.772-0.131 1.157-0.207 0.105-0.021 0.211-0.038 0.316-0.059 0.477-0.099 0.953-0.208 1.427-0.329 0.138-0.035 0.275-0.076 0.412-0.113 0.333-0.09 0.666-0.182 0.997-0.283 0.186-0.057 0.369-0.118 0.553-0.178a33.113 33.113 0 0 0 1.42-0.498 37.055 37.055 0 0 0 1.372-0.554c0.26-0.112 0.518-0.231 0.776-0.351 0.188-0.087 0.377-0.173 0.563-0.263 0.262-0.128 0.522-0.265 0.782-0.401 0.175-0.091 0.352-0.179 0.524-0.273a32.477 32.477 0 0 0 1.857-1.107c0.196-0.125 0.393-0.246 0.586-0.376 0.226-0.151 0.447-0.31 0.669-0.467 0.177-0.125 0.355-0.247 0.529-0.377 0.215-0.158 0.424-0.322 0.635-0.486 0.128-0.099 0.258-0.193 0.385-0.294L512 712.979l300.01 240.008c0.129 0.103 0.261 0.199 0.391 0.299 0.208 0.161 0.415 0.323 0.626 0.479 0.18 0.133 0.364 0.26 0.546 0.388 0.216 0.153 0.43 0.307 0.651 0.455 0.215 0.144 0.434 0.279 0.651 0.417 0.243 0.154 0.486 0.309 0.733 0.457 0.354 0.212 0.71 0.418 1.069 0.615 0.158 0.086 0.319 0.166 0.478 0.249 0.276 0.145 0.552 0.289 0.831 0.426 0.174 0.085 0.351 0.165 0.527 0.247 0.271 0.126 0.542 0.251 0.815 0.369a32.976 32.976 0 0 0 1.376 0.555 27.915 27.915 0 0 0 1.427 0.499c0.173 0.056 0.345 0.114 0.519 0.167 0.35 0.107 0.701 0.205 1.053 0.299 0.12 0.032 0.238 0.068 0.358 0.099 0.479 0.122 0.96 0.233 1.442 0.333 0.094 0.019 0.189 0.035 0.283 0.053 0.395 0.078 0.791 0.149 1.188 0.212 0.146 0.023 0.293 0.043 0.44 0.064a30.394 30.394 0 0 0 1.52 0.182c0.393 0.037 0.786 0.067 1.179 0.09l0.333 0.021c0.51 0.024 1.02 0.04 1.531 0.04 0.074 0 0.147-0.006 0.221-0.006a32.804 32.804 0 0 0 1.508-0.041c0.088-0.005 0.176-0.015 0.264-0.02 0.495-0.03 0.988-0.069 1.477-0.121 0.073-0.008 0.145-0.019 0.218-0.028 0.507-0.058 1.01-0.126 1.509-0.207 0.069-0.011 0.138-0.025 0.207-0.037a30.863 30.863 0 0 0 1.496-0.29l0.227-0.052c0.485-0.11 0.965-0.23 1.441-0.362l0.269-0.076a31.245 31.245 0 0 0 2.934-0.996c0.132-0.052 0.265-0.103 0.397-0.157 0.383-0.157 0.761-0.324 1.136-0.496 0.152-0.069 0.306-0.135 0.457-0.207 0.351-0.167 0.695-0.345 1.039-0.524 0.409-0.213 0.812-0.437 1.213-0.668 0.207-0.12 0.417-0.234 0.621-0.358 0.305-0.185 0.603-0.382 0.902-0.578 0.157-0.103 0.315-0.202 0.47-0.308 0.296-0.201 0.587-0.412 0.878-0.624 0.153-0.112 0.307-0.224 0.458-0.338a32.182 32.182 0 0 0 1.311-1.052c0.256-0.216 0.511-0.434 0.762-0.66 0.192-0.173 0.378-0.351 0.565-0.528 0.211-0.2 0.425-0.396 0.631-0.602 0.388-0.386 0.765-0.782 1.133-1.188 0.085-0.094 0.166-0.194 0.251-0.289 0.309-0.349 0.612-0.704 0.906-1.067 0.036-0.044 0.074-0.085 0.109-0.129 0.08-0.101 0.153-0.205 0.232-0.306a24.214 24.214 0 0 0 0.961-1.296c0.196-0.278 0.388-0.558 0.575-0.843 0.111-0.168 0.218-0.338 0.325-0.508a32.63 32.63 0 0 0 0.828-1.389 33.284 33.284 0 0 0 1.118-2.183 33.498 33.498 0 0 0 1.171-2.88 28.95 28.95 0 0 0 0.478-1.48 30.562 30.562 0 0 0 0.736-3.016 32.26 32.26 0 0 0 0.517-4.085c0.012-0.193 0.024-0.386 0.033-0.581 0.015-0.344 0.022-0.688 0.026-1.032 0.002-0.131 0.01-0.261 0.01-0.393V160c0.003-52.935-40.452-96-90.179-96zM224 160c0-17.645 11.745-32 26.182-32h523.637C788.255 128 800 142.355 800 160v701.421L532.466 647.394c-0.048-0.04-0.098-0.077-0.146-0.117l-0.331-0.264c-0.031-0.025-0.063-0.047-0.094-0.071a31.42 31.42 0 0 0-1.162-0.882l-0.141-0.1c-0.384-0.274-0.772-0.54-1.166-0.795l-0.23-0.147a31.111 31.111 0 0 0-1.074-0.656c-0.128-0.075-0.257-0.148-0.386-0.221a34.642 34.642 0 0 0-1.495-0.794 37.286 37.286 0 0 0-1.515-0.705 29.802 29.802 0 0 0-1.957-0.772c-0.36-0.129-0.721-0.251-1.084-0.366l-0.326-0.101c-0.394-0.12-0.789-0.234-1.186-0.338l-0.266-0.068a31.222 31.222 0 0 0-1.237-0.29l-0.264-0.054a32.398 32.398 0 0 0-2.715-0.434c-0.141-0.016-0.283-0.03-0.424-0.045a31.332 31.332 0 0 0-1.056-0.092c-0.189-0.013-0.379-0.022-0.568-0.032a30.471 30.471 0 0 0-0.906-0.035c-0.246-0.006-0.491-0.006-0.737-0.006s-0.492 0.001-0.737 0.006c-0.302 0.007-0.604 0.02-0.906 0.035-0.19 0.01-0.379 0.019-0.569 0.032-0.352 0.024-0.704 0.056-1.055 0.092-0.142 0.015-0.284 0.028-0.425 0.045a32.95 32.95 0 0 0-2.714 0.433l-0.268 0.055c-0.412 0.088-0.823 0.184-1.232 0.288l-0.272 0.069a32.859 32.859 0 0 0-3.034 0.965c-0.31 0.116-0.618 0.237-0.926 0.364a27.496 27.496 0 0 0-1.344 0.587 32.141 32.141 0 0 0-2.269 1.166c-0.123 0.07-0.245 0.139-0.367 0.21-0.369 0.215-0.733 0.438-1.094 0.668l-0.21 0.134c-0.402 0.261-0.797 0.531-1.189 0.811l-0.116 0.083c-0.402 0.29-0.798 0.591-1.188 0.902l-0.078 0.059-0.272 0.218c-0.07 0.057-0.142 0.111-0.212 0.169L224 861.421V160z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M352 288h320c17.673 0 32-14.327 32-32s-14.327-32-32-32H352c-17.673 0-32 14.327-32 32s14.327 32 32 32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineLabel.defaultProps = {
  size: 18,
};

export default IconLineLabel;
