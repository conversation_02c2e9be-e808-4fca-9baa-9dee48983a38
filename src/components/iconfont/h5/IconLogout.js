/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLogout = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M149.333333 595.157333a31.616 31.616 0 0 1-32 31.189334 31.616 31.616 0 0 1-32-31.189334V286.464C85.333333 187.349333 168.021333 106.666667 269.738667 106.666667h202.496c101.888 0 184.746667 80.853333 184.746666 180.266666v37.717334c0 17.194667-14.293333 31.146667-32 31.146666a31.616 31.616 0 0 1-32-31.146666v-37.717334c0-65.024-54.186667-117.845333-120.746666-117.845333H269.738667C203.306667 169.088 149.333333 221.781333 149.333333 286.464v308.693333z m443.690667 104.234667c0-17.237333 14.336-31.232 32-31.232 17.621333 0 32 13.994667 32 31.232v38.144c0 99.114667-82.730667 179.797333-184.405333 179.797333H270.08C168.234667 917.333333 85.333333 836.48 85.333333 737.066667a31.573333 31.573333 0 0 1 32-31.146667 31.573333 31.573333 0 0 1 32 31.146667c0 65.024 54.186667 117.845333 120.746667 117.845333h202.538667c66.389333 0 120.405333-52.650667 120.405333-117.376v-38.144z m343.210667-199.296a32 32 0 0 0-29.568-19.328H407.466667a31.701333 31.701333 0 0 0-32.085334 31.232c0 17.237333 14.378667 31.232 32.042667 31.232h421.802667l-66.56 64.597333a30.677333 30.677333 0 0 0-0.085334 44.16 32.554667 32.554667 0 0 0 45.226667 0.085334l121.472-117.930667a30.72 30.72 0 0 0 6.954667-34.048z m-120.362667-45.226667a32.426667 32.426667 0 0 1-22.528-9.045333l-30.634667-29.568a30.634667 30.634667 0 0 1-0.213333-44.074667 32.469333 32.469333 0 0 1 45.226667-0.256l30.634666 29.525334a30.677333 30.677333 0 0 1 0.256 44.117333 32.426667 32.426667 0 0 1-22.741333 9.301333z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </svg>
  );
};

IconLogout.defaultProps = {
  size: 18,
};

export default IconLogout;
