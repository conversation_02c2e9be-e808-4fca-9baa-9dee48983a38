/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconPaySuccessful = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M912 404.94C912 179.08 732.92 0 507.06 0 291.08 0 112 179.08 112 404.94 112 620.92 291.08 800 507.06 800 732.92 800 912 620.92 912 404.94z"
        fill={getIconColor(color, 0, '#09BB07')}
      />
      <path
        d="M316.092 429.124c-3.064-3.168-3.408-8.648-0.804-12.188l13.664-18.6a7.8 7.8 0 0 1 11.116-1.56l94.936 72.76c7.008 5.372 18.216 5.124 24.968-0.5l249.032-207.464c3.4-2.832 8.72-2.592 11.844 0.496l11.76 11.616c3.148 3.108 3.14 8.14 0.02 11.208L459.08 554.104a15.8 15.8 0 0 1-22.5-0.28L316.092 429.12z"
        fill={getIconColor(color, 1, '#FFFFFF')}
      />
      <path
        d="M224 924a286 52 0 1 0 572 0 286 52 0 1 0-572 0Z"
        fill={getIconColor(color, 2, '#F2F4F8')}
      />
    </svg>
  );
};

IconPaySuccessful.defaultProps = {
  size: 18,
};

export default IconPaySuccessful;
