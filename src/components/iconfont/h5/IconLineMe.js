/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineMe = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M924.781 337.608c-22.566-53.352-54.864-101.259-95.997-142.393-41.134-41.133-89.041-73.431-142.393-95.997C631.14 75.85 572.465 64 512 64S392.86 75.85 337.608 99.219c-53.352 22.565-101.259 54.864-142.393 95.997-41.133 41.133-73.431 89.041-95.997 142.392C75.85 392.861 64 451.535 64 512c0 60.466 11.85 119.14 35.219 174.392 22.565 53.352 54.864 101.26 95.997 142.393s89.041 73.431 142.392 95.997C392.861 948.15 451.535 960 512 960c60.466 0 119.14-11.85 174.392-35.219 53.352-22.566 101.26-54.864 142.393-95.997 41.133-41.134 73.431-89.041 95.997-142.393C948.15 631.14 960 572.465 960 512s-11.85-119.14-35.219-174.392zM512 896c-100.473 0-195.052-38.332-267.047-108.068 19.964-49.448 53.065-92.077 96.576-124.087C391.189 627.311 450.138 608 512 608s120.811 19.311 170.472 55.845c43.511 32.01 76.611 74.639 96.575 124.087C707.051 857.668 612.473 896 512 896z m0-352c-70.58 0-128-57.42-128-128s57.42-128 128-128c70.579 0 128 57.42 128 128s-57.421 128-128 128z m312.938 190.693a351.927 351.927 0 0 0-28.331-45.862 353.207 353.207 0 0 0-76.21-76.538c-27.19-20.003-56.657-35.77-87.691-47.1C676.166 529.963 704 476.175 704 416c0-105.869-86.131-192-192-192s-192 86.131-192 192c0 60.175 27.834 113.963 71.295 149.193-31.034 11.33-60.502 27.097-87.692 47.1a353.225 353.225 0 0 0-76.209 76.538 351.819 351.819 0 0 0-28.331 45.862C152.898 670.113 128 592.971 128 512c0-102.57 39.943-199 112.471-271.529C312.999 167.943 409.43 128 512 128s199.001 39.943 271.529 112.471S896 409.43 896 512c0 80.971-24.899 158.113-71.062 222.693z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconLineMe.defaultProps = {
  size: 18,
};

export default IconLineMe;
