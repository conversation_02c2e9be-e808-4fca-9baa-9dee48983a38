/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineVideo = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M864 128H160c-52.935 0-96 43.065-96 96v576c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V224c0-52.935-43.065-96-96-96z m-704 64h704c17.645 0 32 14.355 32 32v96H128v-96c0-17.645 14.355-32 32-32z m704 640H160c-17.645 0-32-14.355-32-32V384h768v416c0 17.645-14.355 32-32 32z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M656.124 580.359l-192-112A31.998 31.998 0 0 0 416 496v224a32 32 0 0 0 48.124 27.641l192-112a32 32 0 0 0 0-55.282zM480 664.287V551.713L576.492 608 480 664.287z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineVideo.defaultProps = {
  size: 18,
};

export default IconLineVideo;
