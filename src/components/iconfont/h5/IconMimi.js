/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconMimi = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M261.76 793.41H119.95c-27.57 0-50-22.43-50-50V271.56c0-27.57 22.43-50 50-50h141.81c16.57 0 30 13.43 30 30s-13.43 30-30 30H129.95v451.85h131.81c16.57 0 30 13.43 30 30s-13.43 30-30 30z"
        fill={getIconColor(color, 0, '#017BFE')}
      />
      <path
        d="M899.47 797.3L432.53 911.76c-25.2 6.18-49.52-12.9-49.52-38.85V144.64c0-25.89 24.22-44.96 49.38-38.88l466.94 112.69c17.96 4.33 30.62 20.41 30.62 38.88v501.13c-0.01 18.41-12.59 34.46-30.48 38.84z"
        fill={getIconColor(color, 1, '#B7D9FF')}
      />
      <path
        d="M422.86 942.94c-15.46 0-30.55-5.14-43.05-14.95-17.03-13.36-26.79-33.43-26.79-55.08V144.64c0-21.59 9.73-41.64 26.7-55s38.73-18.11 59.73-13.05l466.94 112.69c31.55 7.61 53.58 35.59 53.58 68.05v501.13a69.85 69.85 0 0 1-53.33 67.99L439.67 940.9a70.467 70.467 0 0 1-16.81 2.04z m0.07-808.32c-2.87 0-4.99 1.27-6.11 2.16-1.42 1.12-3.81 3.63-3.81 7.86v728.28c0 4.23 2.4 6.75 3.83 7.87 1.43 1.12 4.44 2.85 8.55 1.84l466.94-114.46 7.14 29.14-7.14-29.14a9.97 9.97 0 0 0 7.62-9.71V257.32c0-4.64-3.15-8.63-7.65-9.72L425.35 134.92c-0.85-0.21-1.66-0.3-2.42-0.3z"
        fill={getIconColor(color, 2, '#017BFE')}
      />
      <path
        d="M513.28 508.87m-45 0a45 45 0 1 0 90 0 45 45 0 1 0-90 0Z"
        fill={getIconColor(color, 3, '#017BFE')}
      />
    </svg>
  );
};

IconMimi.defaultProps = {
  size: 18,
};

export default IconMimi;
