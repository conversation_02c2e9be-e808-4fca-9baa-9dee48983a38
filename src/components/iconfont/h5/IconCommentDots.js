/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconCommentDots = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M512.1039 896c282.8 0 512-186.2 512-416S794.9039 64 512.1039 64 0.1039 250.2 0.1039 480c0 90.2 35.4 173.6 95.4 241.8-3.8 49-22.8 92.6-42.8 125.8-11 18.4-22.2 33.2-30.4 43.2-4.2 5-7.4 8.8-9.8 11.4-1.2 1.2-2 2.2-2.6 2.8l-0.6 0.6c-9.2 9.2-11.8 22.8-6.8 34.8 5 12 16.6 19.8 29.6 19.8 57.4 0 115.2-17.8 163.2-38.6 45.8-20 84.8-43.8 108.6-61.2 63.6 23 134 35.8 208.2 35.8zM256.1039 416a64 64 0 1 1 0 128 64 64 0 1 1 0-128z m256 0a64 64 0 1 1 0 128 64 64 0 1 1 0-128z m192 64a64 64 0 1 1 128 0 64 64 0 1 1-128 0z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconCommentDots.defaultProps = {
  size: 18,
};

export default IconCommentDots;
