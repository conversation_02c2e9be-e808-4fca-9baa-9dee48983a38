/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconTicket = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M929.578667 449.664a31.36 31.36 0 0 1-21.845334 8.832c-30.549333 0-55.296 24.021333-55.296 53.333333 0 29.44 24.448 53.333333 54.698667 53.674667 17.066667 0.170667 31.530667 11.733333 31.530667 28.288v102.826667C938.666667 783.104 866.474667 853.333333 777.344 853.333333h-134.528a24.832 24.832 0 0 1-25.173333-24.448v-86.613333a30.208 30.208 0 0 0-30.976-30.037333 30.464 30.464 0 0 0-30.933334 30.037333v86.613333a24.789333 24.789333 0 0 1-25.130666 24.448H246.656C157.952 853.333333 85.333333 783.189333 85.333333 696.576v-102.826667c0-16.512 14.506667-28.074667 31.530667-28.245333 30.293333-0.298667 54.698667-24.234667 54.698667-53.717333 0-28.458667-23.893333-50.090667-55.296-50.090667a31.402667 31.402667 0 0 1-21.845334-8.832 29.610667 29.610667 0 0 1-9.088-21.248V327.808C85.333333 241.365333 158.122667 170.666667 247.082667 170.666667h283.52c13.866667 0 25.173333 10.922667 25.173333 24.448v102.613333a30.72 30.72 0 0 0 30.933333 30.08c17.322667 0 30.933333-13.653333 30.933334-30.08V195.114667c0-13.525333 11.264-24.448 25.173333-24.448h134.528C866.432 170.666667 938.666667 240.810667 938.666667 327.424v100.992a29.610667 29.610667 0 0 1-9.088 21.248z m-342.869334 184.789333c17.322667 0 30.933333-13.653333 30.933334-30.037333v-160.341333a30.464 30.464 0 0 0-30.976-30.08 30.72 30.72 0 0 0-30.933334 30.08v160.341333a30.72 30.72 0 0 0 30.976 30.037333z"
        fill={getIconColor(color, 0, '#200E32')}
      />
    </svg>
  );
};

IconTicket.defaultProps = {
  size: 18,
};

export default IconTicket;
