/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconGdsbrand = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M713.5 142.8H305.3l-207 243.5 414.1 513.5L926.9 386 713.5 142.8zM189.3 387.5l148.5-174.7h344.1l153.7 175.1-323 400.5-323.3-400.9z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M512.3 558.1L402.8 448.6l-49.5 49.5L512 656.8l158.3-156-49.2-49.8z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconGdsbrand.defaultProps = {
  size: 18,
};

export default IconGdsbrand;
