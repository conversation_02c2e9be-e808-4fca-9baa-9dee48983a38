/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconUserGear = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1280 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M448 0a256 256 0 1 1 0 512 256 256 0 1 1 0-512z m-91.4 608h182.8c23.6 0 46.8 2.4 69 6.6-4.2 37 14.8 71.2 43.6 89.6-33.2 21.2-53.4 63.2-40 106.6 8 25.8 18.8 51 32.8 75.2s30.4 46.2 48.8 66c31.4 33.8 79.2 36.8 114.4 17.4v1.8c0 18.4 5.4 37 15.8 52.6H59.4C26.6 1024 0 997.4 0 964.6 0 767.6 159.6 608 356.6 608zM872 436.4c0-14 9-26.6 22.6-29.6 21-4.8 43-7.4 65.4-7.4s44.4 2.6 65.4 7.4c13.6 3 22.6 15.6 22.6 29.6v61.2c15.8 6.8 30.8 15.4 44.6 25.6l49.8-28.6c12.2-7 27.4-5.4 37 4.8 15.2 16.2 28.6 34.4 40.2 54.4s20.6 40.8 27 62c4.2 13.4-2.2 27.4-14.4 34.4l-50 28.8c0.8 8 1.4 16.2 1.4 24.6s-0.4 16.4-1.4 24.6l50 28.8c12.2 7 18.4 21 14.4 34.4-6.6 21.2-15.6 42-27 62s-25 38.2-40.2 54.4c-9.6 10.2-25 11.8-37 4.8L1092.6 884c-13.8 10.2-28.6 18.8-44.6 25.6v61.2c0 14-9 26.6-22.6 29.6-21 4.8-43 7.4-65.4 7.4s-44.4-2.6-65.4-7.4c-13.6-3-22.6-15.6-22.6-29.6v-61.2c-16-6.8-31.2-15.4-45-25.8l-49.4 28.6c-12.2 7-27.4 5.4-37-4.8-15.2-16.2-28.6-34.4-40.2-54.4s-20.6-40.8-27-62c-4.2-13.4 2.2-27.4 14.4-34.4l49.6-28.6c-0.8-8.2-1.4-16.4-1.4-24.8s0.4-16.6 1.4-24.8L687.6 650c-12.2-7-18.4-21-14.4-34.4 6.6-21.2 15.4-42 27-62s25-38.2 40.2-54.4c9.6-10.2 24.8-11.8 37-4.8l49.6 28.6c13.8-10.2 29-18.8 45-25.8v-60.8z m184.2 267a96.2 96.2 0 1 0-192.2 0 96.2 96.2 0 1 0 192.2 0z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconUserGear.defaultProps = {
  size: 18,
};

export default IconUserGear;
