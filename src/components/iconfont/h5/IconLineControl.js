/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineControl = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M864 208H443.334c-13.803-46.213-56.693-80-107.334-80s-93.531 33.787-107.334 80H160c-17.673 0-32 14.327-32 32s14.327 32 32 32h68.666c13.803 46.213 56.693 80 107.334 80s93.531-33.787 107.334-80H864c17.673 0 32-14.327 32-32 0-17.674-14.327-32-32-32z m-528 79.999c-26.467 0-48-21.533-48-48s21.533-48 48-48 48 21.533 48 48-21.533 48-48 48zM864 751.999H443.334c-13.802-46.213-56.693-80-107.334-80s-93.531 33.787-107.334 80H160c-17.673 0-32 14.327-32 32s14.327 32 32 32h68.666c13.802 46.213 56.693 80 107.334 80s93.531-33.787 107.334-80H864c17.673 0 32-14.327 32-32s-14.327-32-32-32z m-528 80c-26.467 0-48-21.532-48-48C288 757.532 309.533 736 336 736s48 21.533 48 48-21.533 47.999-48 47.999zM864 480h-68.667C781.531 433.787 738.64 400 688 400s-93.531 33.787-107.333 80H160c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h420.667C594.47 590.213 637.36 623.999 688 623.999S781.531 590.213 795.333 544H864c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32z m-176 79.999c-26.467 0-47.999-21.532-47.999-47.999s21.532-48 47.999-48 47.999 21.533 47.999 48-21.532 47.999-47.999 47.999z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconLineControl.defaultProps = {
  size: 18,
};

export default IconLineControl;
