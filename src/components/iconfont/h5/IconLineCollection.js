/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineCollection = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M416 896H224c-17.6 0-32-14.4-32-32V160c0-17.6 14.4-32 32-32h576c17.6 0 32 14.4 32 32v256c0 17.7 14.3 32 32 32s32-14.3 32-32V160c0-52.9-43.1-96-96-96H224c-52.9 0-96 43.1-96 96v704c0 52.9 43.1 96 96 96h192c17.7 0 32-14.3 32-32s-14.3-32-32-32z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M352 256c-17.7 0-32 14.3-32 32s14.3 32 32 32h320c17.7 0 32-14.3 32-32s-14.3-32-32-32H352zM544 480H352c-17.7 0-32 14.3-32 32s14.3 32 32 32h192c17.7 0 32-14.3 32-32s-14.3-32-32-32zM928 800h-96v-64h96c17.7 0 32-14.3 32-32s-14.3-32-32-32h-82.7l67.9-67.9c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L800 626.7l-67.9-67.9c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l67.9 67.9H672c-17.7 0-32 14.3-32 32s14.3 32 32 32h96v64h-96c-17.7 0-32 14.3-32 32s14.3 32 32 32h96v64c0 17.7 14.3 32 32 32s32-14.3 32-32v-64h96c17.7 0 32-14.3 32-32s-14.3-32-32-32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineCollection.defaultProps = {
  size: 18,
};

export default IconLineCollection;
