/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconFriendadd = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M800 387.2c0-160-128-288-288-288s-288 128-288 288c0 105.6 57.6 201.6 147.2 249.6-121.6 48-214.4 153.6-240 288-3.2 16 6.4 35.2 25.6 38.4h6.4c16 0 28.8-9.6 32-25.6 28.8-150.4 160-259.2 313.6-262.4h6.4c156.8 0 284.8-131.2 284.8-288z m-512 0c0-124.8 99.2-224 224-224s224 99.2 224 224c0 121.6-99.2 220.8-220.8 224H505.6c-121.6-6.4-217.6-105.6-217.6-224zM864 800h-64v-64c0-19.2-12.8-32-32-32s-32 12.8-32 32v64h-64c-19.2 0-32 12.8-32 32s12.8 32 32 32h64v64c0 19.2 12.8 32 32 32s32-12.8 32-32v-64h64c19.2 0 32-12.8 32-32s-12.8-32-32-32z"
        fill={getIconColor(color, 0, '#666666')}
      />
    </svg>
  );
};

IconFriendadd.defaultProps = {
  size: 18,
};

export default IconFriendadd;
