/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconIdentityCard = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M585.152 430.336h295.104v51.84H585.152v-51.84z m89.984 380.736l-2.496-2.496H92.544a40.832 40.832 0 0 1-41.28-41.28V223.488c0-23.168 18.176-43.776 41.28-43.776h826.432c23.104 0 41.28 18.112 41.28 41.28v326.336l2.496 2.496c13.12 7.488 25.6 18.112 35.648 31.296l10.048 13.12V210.368a80 80 0 0 0-79.36-80H79.36A80 80 0 0 0 0 210.368v569.472c0 43.776 35.648 80 79.36 80h628.928l-7.552-10.688a347.2 347.2 0 0 1-25.6-38.08zM585.152 544.128h87.488v51.904H585.152v-51.904z m0-225.024h295.104v51.904H585.152v-51.904z m-418.24-20.672a34.56 34.56 0 0 0-35.648 33.792v338.88c0 18.112 15.616 33.792 35.648 33.792H485.12a34.56 34.56 0 0 0 35.648-33.792V334.72c0-18.112-18.112-33.728-35.648-33.728H166.912v-2.56z m36.224 359.424c0-7.488 2.496-25.6 10.048-36.224 2.496-2.496 4.992-4.992 4.992-7.488 7.488-10.624 15.616-23.168 51.264-28.8a53.568 53.568 0 0 0 30.656-15.616c9.984-15.616 7.488-36.288 7.488-43.776v-18.176s-23.168-31.232-23.168-54.4v-23.104s0-33.792 15.68-51.904a33.664 33.664 0 0 1 23.104-10.624l35.648 13.12h2.496c35.648 0 38.72 46.912 38.72 49.408v28.736s0 36.288-23.104 46.912l-2.496 2.496v15.616c-2.496 7.552-10.048 31.296 0 46.912 4.992 10.624 15.616 15.616 28.16 15.616 38.72 5.056 51.264 20.672 58.752 28.8l4.992 4.992s4.992 4.992 9.984 36.288H201.984v1.28l1.152-0.064z m651.456-105.6c-95.04 0-169.408 75.008-169.408 170.688 0 93.12 76.864 170.688 169.408 170.688 95.04 0 169.408-75.008 169.408-170.688s-76.928-170.688-169.408-170.688z m-2.496 274.432c-7.552 7.552-23.168 7.552-33.152 0l-71.936-72.512c-7.488-7.552-7.488-23.168 0-31.296 7.552-7.488 23.168-7.488 33.152 0l56.256 56.896 110.656-108.736c7.552-7.488 23.168-7.488 33.152 0 7.488 7.488 7.488 23.168 0 31.296l-128.128 124.352z"
        fill={getIconColor(color, 0, '#999999')}
      />
    </svg>
  );
};

IconIdentityCard.defaultProps = {
  size: 18,
};

export default IconIdentityCard;
