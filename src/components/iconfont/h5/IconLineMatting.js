/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineMatting = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M832 288h-96v-96c0-52.9-43.1-96-96-96H192c-52.9 0-96 43.1-96 96v448c0 52.9 43.1 96 96 96h96v96c0 52.9 43.1 96 96 96h448c52.9 0 96-43.1 96-96V384c0-52.9-43.1-96-96-96zM160 640V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v96h-32c-17.7 0-32 14.3-32 32s14.3 32 32 32h32v288c0 17.6-14.4 32-32 32H352v-32c0-17.7-14.3-32-32-32s-32 14.3-32 32v32h-96c-17.6 0-32-14.4-32-32z m704 192c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32v-96h288c52.9 0 96-43.1 96-96V352h96c17.6 0 32 14.4 32 32v448z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M320 576c17.7 0 32-14.3 32-32v-64c0-17.7-14.3-32-32-32s-32 14.3-32 32v64c0 17.7 14.3 32 32 32zM480 352h64c17.7 0 32-14.3 32-32s-14.3-32-32-32h-64c-17.7 0-32 14.3-32 32s14.3 32 32 32zM320 416c17.7 0 32-14.3 32-32 0-17.6 14.4-32 32-32 17.7 0 32-14.3 32-32s-14.3-32-32-32c-52.9 0-96 43.1-96 96 0 17.7 14.3 32 32 32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineMatting.defaultProps = {
  size: 18,
};

export default IconLineMatting;
