/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineTask = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M832 96H192c-52.935 0-96 43.065-96 96v640c0 52.935 43.065 96 96 96h640c52.935 0 96-43.065 96-96V192c0-52.935-43.065-96-96-96z m32 736c0 17.645-14.355 32-32 32H192c-17.645 0-32-14.355-32-32V192c0-17.645 14.355-32 32-32h640c17.645 0 32 14.355 32 32v640z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M352 544c-70.58 0-128 57.421-128 128s57.42 128 128 128 128-57.421 128-128-57.42-128-128-128z m0 192c-35.29 0-64-28.71-64-64s28.71-64 64-64 64 28.71 64 64-28.71 64-64 64zM425.627 260.627L311.89 374.365l-33.127-33.127c-12.497-12.497-32.758-12.497-45.255 0s-12.497 32.758 0 45.255l53.301 53.301c0.719 0.893 1.49 1.759 2.319 2.589a31.847 31.847 0 0 0 13.748 8.117 32.088 32.088 0 0 0 9.26 1.366c8.189 0 16.379-3.124 22.627-9.373a32.28 32.28 0 0 0 3.078-3.568l133.042-133.042c12.497-12.497 12.497-32.758 0-45.255-12.498-12.498-32.758-12.498-45.256-0.001zM768 320H576c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h192c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32zM768 640H576c-17.673 0-32 14.327-32 32 0 17.673 14.327 32 32 32h192c17.673 0 32-14.327 32-32 0-17.673-14.327-32-32-32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineTask.defaultProps = {
  size: 18,
};

export default IconLineTask;
