/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineCalendar = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M864 192h-96v-32c0-17.673-14.327-32-32-32s-32 14.327-32 32v32H320v-32c0-17.673-14.327-32-32-32s-32 14.327-32 32v32h-96c-52.935 0-96 43.065-96 96v512c0 52.935 43.065 96 96 96h704c52.935 0 96-43.065 96-96V288c0-52.935-43.065-96-96-96z m-704 64h96v32c0 17.673 14.327 32 32 32s32-14.327 32-32v-32h384v32c0 17.673 14.327 32 32 32s32-14.327 32-32v-32h96c17.645 0 32 14.355 32 32v96H128v-96c0-17.645 14.355-32 32-32z m704 576H160c-17.645 0-32-14.355-32-32V448h768v352c0 17.645-14.355 32-32 32z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M640 608H384c-17.673 0-32 14.327-32 32s14.327 32 32 32h256c17.673 0 32-14.327 32-32s-14.327-32-32-32z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineCalendar.defaultProps = {
  size: 18,
};

export default IconLineCalendar;
