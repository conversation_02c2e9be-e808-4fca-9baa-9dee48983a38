/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineRole = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M775.6 84.8C770.1 73.8 759 67 746.8 67H277.2c-12.3 0-23.3 6.8-28.9 17.8-5.5 11-4.3 23.9 3.1 33.7l126.8 167.4-86.1 388.6c-2.1 9.3 0.1 19 6 26.5l188.1 243.1c0.8 1.1 1.7 2.1 2.7 3.1l0.5 0.5 0.6 0.6c0.3 0.3 0.7 0.6 1 0.9l0.1 0.1c4.4 3.8 9.8 6.3 15.6 7.2h0.1c0.7 0.1 1.5 0.2 2.2 0.3 0.2 0 0.4 0 0.7 0.1 0.7 0.1 1.4 0.1 2.2 0.1h0.4c0.7 0 1.5 0 2.2-0.1 0.2 0 0.4 0 0.7-0.1 0.7-0.1 1.5-0.2 2.2-0.3h0.1c5.8-1 11.1-3.4 15.6-7.2 0 0 0.1 0 0.1-0.1 0.4-0.3 0.7-0.6 1-0.9l0.5-0.5 0.6-0.6c0.9-1 1.8-2 2.6-3L726 701c5.8-7.6 8-17.2 6-26.5l-86.3-388.6 126.8-167.4c7.4-9.8 8.6-22.7 3.1-33.7zM429.1 246.2l-87.1-115h340l-87.1 115H429.1z m83 626.1L358.5 673.7l80.4-363.2h146.2l80.6 363.2-153.6 198.6z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconLineRole.defaultProps = {
  size: 18,
};

export default IconLineRole;
