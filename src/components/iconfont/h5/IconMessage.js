/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconMessage = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M128 0C57.4 0 0 57.4 0 128v576c0 70.6 57.4 128 128 128h192v160c0 12.2 6.8 23.2 17.6 28.6s23.8 4.2 33.6-3L618.6 832H896c70.6 0 128-57.4 128-128V128c0-70.6-57.4-128-128-128H128z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconMessage.defaultProps = {
  size: 18,
};

export default IconMessage;
