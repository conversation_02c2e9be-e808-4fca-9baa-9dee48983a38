/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineCoin = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M924.781 337.608c-22.566-53.352-54.864-101.259-95.997-142.393-41.134-41.133-89.041-73.431-142.393-95.997C631.14 75.85 572.465 64 512 64S392.86 75.85 337.608 99.219c-53.352 22.565-101.259 54.864-142.393 95.997-41.133 41.133-73.431 89.041-95.997 142.392C75.85 392.861 64 451.535 64 512c0 60.466 11.85 119.14 35.219 174.392 22.565 53.352 54.864 101.26 95.997 142.393s89.041 73.431 142.392 95.997C392.861 948.15 451.535 960 512 960c60.466 0 119.14-11.85 174.392-35.219 53.352-22.566 101.26-54.864 142.393-95.997 41.133-41.134 73.431-89.041 95.997-142.393C948.15 631.14 960 572.465 960 512s-11.85-119.14-35.219-174.392zM783.529 783.529C711.001 856.057 614.57 896 512 896s-199.001-39.943-271.529-112.471S128 614.57 128 512s39.943-199 112.471-271.529C312.999 167.943 409.43 128 512 128s199.001 39.943 271.529 112.471S896 409.43 896 512s-39.943 199.001-112.471 271.529z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M656 496c17.673 0 32-14.327 32-32s-14.327-32-32-32h-98.496l105.623-105.623c12.497-12.497 12.497-32.758 0-45.255-12.496-12.497-32.758-12.497-45.254 0L512 386.995 406.627 281.623c-12.497-12.497-32.758-12.497-45.255 0s-12.497 32.758 0 45.255L466.495 432H368c-17.673 0-32 14.327-32 32s14.327 32 32 32h112v64H368c-17.673 0-32 14.327-32 32s14.327 32 32 32h112v96.25c0 17.673 14.327 32 32 32 17.673 0 32-14.327 32-32V624h112c17.673 0 32-14.327 32-32s-14.327-32-32-32H544v-64h112z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineCoin.defaultProps = {
  size: 18,
};

export default IconLineCoin;
