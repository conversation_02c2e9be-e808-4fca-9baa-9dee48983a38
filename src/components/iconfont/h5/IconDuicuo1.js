/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconDuicuo1 = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M512 64a448 448 0 1 1 0 896A448 448 0 0 1 512 64z m161.664 266.304L473.728 556.992a32 32 0 0 1-43.648 4.096L336 488a39.168 39.168 0 1 0-48.064 61.888l128.448 99.84a64 64 0 0 0 87.296-8.192l228.8-259.328a39.232 39.232 0 0 0-58.816-51.84z"
        fill={getIconColor(color, 0, '#01BF1A')}
      />
    </svg>
  );
};

IconDuicuo1.defaultProps = {
  size: 18,
};

export default IconDuicuo1;
