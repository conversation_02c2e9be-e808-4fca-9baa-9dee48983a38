/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconPersonCirclePlus = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1152 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M224 96a96 96 0 1 1 192 0 96 96 0 1 1-192 0z m80 608v256c0 35.4-28.6 64-64 64s-64-28.6-64-64V513.8l-57.2 95.2c-18.2 30.2-57.6 40-87.8 21.8s-40-57.6-21.8-87.8l116.6-194c34.8-57.8 97.2-93.2 164.6-93.2h59.4c67.4 0 129.8 35.4 164.6 93.2l89.8 149.4c-32.2 35.2-57.2 77-73.2 123-3.8-3.6-7-7.8-9.8-12.6L464 513.8V960c0 35.4-28.6 64-64 64s-64-28.6-64-64V704h-32z m560-256a288 288 0 1 1 0 576 288 288 0 1 1 0-576z m32 160c0-17.6-14.4-32-32-32s-32 14.4-32 32v96h-96c-17.6 0-32 14.4-32 32s14.4 32 32 32h96v96c0 17.6 14.4 32 32 32s32-14.4 32-32v-96h96c17.6 0 32-14.4 32-32s-14.4-32-32-32h-96v-96z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconPersonCirclePlus.defaultProps = {
  size: 18,
};

export default IconPersonCirclePlus;
