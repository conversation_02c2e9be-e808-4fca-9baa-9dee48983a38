/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconAddressbook = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M160 608c-17.664 0-32-14.304-32-32l0-160c0-17.664 14.336-32 32-32s32 14.336 32 32l0 160C192 593.696 177.664 608 160 608z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M864 928 224 928c-52.928 0-96-42.752-96-95.328L128 768c0-17.696 14.336-32 32-32s32 14.304 32 32l0 64.672C192 849.984 206.368 864 224 864l640 0c17.664 0 32-14.336 32-32L896 192c0-17.632-14.336-32-32-32L224 160C205.44 160 192 170.144 192 177.024L192 224c0 17.664-14.336 32-32 32S128 241.664 128 224L128 177.024C128 132.352 171.072 96 224 96l640 0c52.928 0 96 43.072 96 96l0 640C960 884.928 916.928 928 864 928z"
        fill={getIconColor(color, 1, '#333333')}
      />
      <path
        d="M192 352 96 352c-17.664 0-32-14.336-32-32s14.336-32 32-32l96 0c17.664 0 32 14.336 32 32S209.664 352 192 352z"
        fill={getIconColor(color, 2, '#333333')}
      />
      <path
        d="M192 704 96 704c-17.664 0-32-14.304-32-32s14.336-32 32-32l96 0c17.664 0 32 14.304 32 32S209.664 704 192 704z"
        fill={getIconColor(color, 3, '#333333')}
      />
      <path
        d="M319.456 800.992c-14.56 0-27.712-9.984-31.136-24.736-4-17.216 6.688-34.4 23.936-38.4 4.544-1.056 97.792-22.656 167.744-30.56l0-20.032c-67.136-57.6-96-139.52-96-269.92 0-127.84 64.608-192.672 192-192.672 158.688 0 192 104.416 192 192 0 139.008-66.24 223.2-127.2 271.2l0.224 19.84c69.28 8.544 161.504 29.088 165.984 30.08 17.248 3.84 28.096 20.96 24.224 38.208-3.808 17.216-20.704 28.224-38.208 24.224-1.184-0.256-121.856-27.168-185.92-31.648-16.64-1.152-29.568-14.912-29.76-31.552l-0.672-64.672c-0.128-10.464 4.864-20.32 13.408-26.368C642.016 608.8 704 538.848 704 416.672c0-90.912-37.088-128-128-128-92.096 0-128 36.064-128 128.672 0 117.632 24.16 184.032 83.392 229.248C539.328 652.64 544 662.016 544 672l0 64.672c0 17.088-13.408 31.168-30.464 31.968-63.584 3.04-185.568 31.264-186.784 31.52C324.32 800.736 321.888 800.992 319.456 800.992z"
        fill={getIconColor(color, 4, '#333333')}
      />
    </svg>
  );
};

IconAddressbook.defaultProps = {
  size: 18,
};

export default IconAddressbook;
