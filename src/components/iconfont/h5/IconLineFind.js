/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineFind = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M924.781 337.608c-22.566-53.352-54.864-101.259-95.997-142.393-41.134-41.133-89.041-73.431-142.393-95.997C631.14 75.85 572.465 64 512 64S392.86 75.85 337.608 99.219c-53.352 22.565-101.259 54.864-142.393 95.997-41.133 41.133-73.431 89.041-95.997 142.392C75.85 392.861 64 451.535 64 512c0 60.466 11.85 119.14 35.219 174.392 22.565 53.352 54.864 101.26 95.997 142.393s89.041 73.431 142.392 95.997C392.861 948.15 451.535 960 512 960c60.466 0 119.14-11.85 174.392-35.219 53.352-22.566 101.26-54.864 142.393-95.997 41.133-41.134 73.431-89.041 95.997-142.393C948.15 631.14 960 572.465 960 512s-11.85-119.14-35.219-174.392zM783.529 783.529C711.001 856.057 614.57 896 512 896s-199.001-39.943-271.529-112.471S128 614.57 128 512s39.943-199 112.471-271.529C312.999 167.943 409.43 128 512 128s199.001 39.943 271.529 112.471S896 409.43 896 512s-39.943 199.001-112.471 271.529z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M725.015 330.202c-0.007-0.293-0.024-0.584-0.039-0.876-0.012-0.226-0.02-0.451-0.036-0.677-0.024-0.333-0.06-0.665-0.095-0.996-0.02-0.185-0.034-0.37-0.057-0.555-0.048-0.39-0.108-0.777-0.17-1.163-0.021-0.128-0.036-0.255-0.058-0.383a32.24 32.24 0 0 0-0.294-1.486l-0.011-0.053a32.051 32.051 0 0 0-1.411-4.63c-0.082-0.21-0.172-0.415-0.257-0.622-0.11-0.265-0.217-0.531-0.333-0.794-0.14-0.316-0.29-0.624-0.439-0.933-0.071-0.146-0.138-0.294-0.211-0.44a31.156 31.156 0 0 0-0.568-1.07c-0.05-0.091-0.097-0.182-0.148-0.272a31.793 31.793 0 0 0-0.655-1.105l-0.13-0.213a32.374 32.374 0 0 0-0.705-1.07l-0.155-0.227a31.802 31.802 0 0 0-0.714-0.978c-0.075-0.099-0.149-0.199-0.225-0.297a31.139 31.139 0 0 0-0.683-0.846c-0.111-0.133-0.221-0.267-0.334-0.398a36.594 36.594 0 0 0-1.091-1.206 30.9 30.9 0 0 0-0.507-0.517 30.061 30.061 0 0 0-0.65-0.637c-0.126-0.119-0.254-0.236-0.382-0.353a32.073 32.073 0 0 0-0.826-0.735c-0.084-0.072-0.169-0.142-0.253-0.212-0.325-0.272-0.654-0.54-0.991-0.8l-0.143-0.108c-0.37-0.283-0.747-0.559-1.131-0.826l-0.061-0.042a32.452 32.452 0 0 0-1.263-0.831 32.26 32.26 0 0 0-1.287-0.763l-0.061-0.033c-0.42-0.234-0.845-0.461-1.278-0.678-0.059-0.03-0.12-0.057-0.179-0.086a33.345 33.345 0 0 0-1.192-0.561c-0.142-0.063-0.287-0.12-0.431-0.181-0.325-0.138-0.649-0.277-0.98-0.405-0.34-0.132-0.685-0.252-1.03-0.373-0.137-0.048-0.27-0.102-0.409-0.148a31.952 31.952 0 0 0-3.124-0.869c-0.112-0.025-0.225-0.044-0.337-0.068a32.58 32.58 0 0 0-1.207-0.239c-0.182-0.032-0.363-0.055-0.545-0.083-0.334-0.052-0.668-0.105-1.005-0.147-0.221-0.027-0.442-0.046-0.664-0.069-0.297-0.03-0.594-0.063-0.893-0.085-0.245-0.018-0.49-0.027-0.735-0.04-0.276-0.014-0.551-0.03-0.827-0.037-0.264-0.007-0.526-0.004-0.79-0.004-0.258 0-0.515-0.003-0.773 0.004-0.283 0.007-0.565 0.024-0.848 0.038-0.238 0.012-0.475 0.021-0.713 0.038-0.308 0.023-0.614 0.056-0.92 0.087-0.213 0.022-0.425 0.04-0.638 0.066-0.348 0.043-0.694 0.097-1.039 0.152-0.17 0.027-0.339 0.048-0.509 0.078-0.421 0.073-0.839 0.159-1.256 0.248-0.096 0.021-0.192 0.037-0.288 0.058a31.98 31.98 0 0 0-3.129 0.871l-271.387 90.462c-0.545 0.179-1.084 0.373-1.618 0.581-0.222 0.086-0.438 0.181-0.656 0.271-0.253 0.105-0.507 0.206-0.757 0.318-0.328 0.145-0.648 0.301-0.969 0.456-0.134 0.065-0.27 0.127-0.403 0.194-0.374 0.187-0.74 0.384-1.103 0.585-0.079 0.044-0.159 0.085-0.238 0.129a32.35 32.35 0 0 0-1.142 0.677l-0.173 0.106c-0.376 0.236-0.745 0.481-1.11 0.732-0.061 0.042-0.124 0.083-0.185 0.126-0.347 0.242-0.687 0.492-1.022 0.747-0.083 0.063-0.167 0.125-0.249 0.189-0.303 0.235-0.6 0.476-0.893 0.72-0.117 0.098-0.235 0.195-0.351 0.294-0.25 0.215-0.494 0.434-0.737 0.656-0.157 0.143-0.313 0.286-0.467 0.432-0.194 0.184-0.383 0.372-0.572 0.561-0.195 0.195-0.389 0.39-0.579 0.59-0.139 0.147-0.276 0.296-0.412 0.446-0.229 0.25-0.455 0.502-0.676 0.759-0.093 0.108-0.183 0.218-0.274 0.327-0.251 0.301-0.498 0.605-0.739 0.916l-0.171 0.226c-0.261 0.343-0.516 0.691-0.764 1.046l-0.107 0.157c-0.257 0.372-0.507 0.75-0.749 1.135l-0.09 0.147c-0.238 0.383-0.47 0.772-0.692 1.167-0.039 0.069-0.075 0.139-0.113 0.208-0.206 0.373-0.408 0.749-0.6 1.133-0.061 0.121-0.117 0.245-0.177 0.367-0.161 0.332-0.322 0.664-0.472 1.004-0.105 0.235-0.2 0.475-0.299 0.713-0.097 0.233-0.197 0.463-0.289 0.699-0.206 0.53-0.399 1.065-0.577 1.606l-90.461 271.384a32.106 32.106 0 0 0-0.879 3.156c-0.009 0.042-0.016 0.084-0.026 0.126-0.103 0.469-0.198 0.941-0.28 1.416-0.024 0.137-0.041 0.273-0.063 0.41-0.06 0.378-0.119 0.756-0.166 1.136-0.024 0.193-0.039 0.386-0.06 0.578-0.034 0.324-0.069 0.647-0.093 0.973-0.017 0.23-0.025 0.46-0.037 0.69-0.015 0.289-0.032 0.577-0.039 0.866-0.006 0.26-0.003 0.519-0.004 0.779 0 0.259-0.003 0.518 0.004 0.779 0.007 0.29 0.024 0.578 0.039 0.866 0.012 0.23 0.02 0.46 0.037 0.69 0.024 0.325 0.059 0.649 0.093 0.973 0.02 0.193 0.036 0.386 0.06 0.579 0.047 0.381 0.106 0.759 0.166 1.136 0.022 0.137 0.039 0.273 0.063 0.41 0.082 0.475 0.177 0.947 0.28 1.416 0.009 0.042 0.016 0.084 0.026 0.126a32.085 32.085 0 0 0 1.708 5.343c0.056 0.135 0.109 0.271 0.167 0.404a32.097 32.097 0 0 0 1.235 2.528c0.303 0.559 0.617 1.111 0.953 1.651 0.133 0.214 0.272 0.42 0.41 0.63 0.281 0.429 0.572 0.851 0.873 1.267 0.146 0.201 0.289 0.404 0.439 0.601 0.397 0.522 0.812 1.03 1.241 1.527 0.139 0.161 0.281 0.318 0.424 0.476 0.452 0.503 0.916 0.997 1.401 1.472 0.026 0.025 0.05 0.052 0.076 0.078a32.287 32.287 0 0 0 1.694 1.523c0.081 0.068 0.161 0.136 0.243 0.203a31.781 31.781 0 0 0 3.808 2.699l0.151 0.092a31.86 31.86 0 0 0 2.033 1.116c0.068 0.034 0.138 0.066 0.207 0.099 0.593 0.292 1.198 0.566 1.815 0.823 0.146 0.061 0.291 0.125 0.438 0.184 0.59 0.236 1.187 0.461 1.798 0.665 0.044 0.015 0.087 0.024 0.131 0.038 0.632 0.208 1.274 0.386 1.919 0.555 0.179 0.047 0.357 0.106 0.536 0.15 0.714 0.174 1.437 0.315 2.164 0.44 0.111 0.019 0.222 0.048 0.333 0.066 1.653 0.265 3.335 0.403 5.032 0.403h0.008c1.698 0 3.381-0.139 5.035-0.404 0.09-0.015 0.181-0.038 0.271-0.054 0.747-0.126 1.49-0.273 2.224-0.452 0.178-0.043 0.356-0.103 0.534-0.149a32.04 32.04 0 0 0 1.915-0.554c0.045-0.015 0.09-0.024 0.135-0.039l271.167-90.389 0.038-0.013 0.324-0.108a32.52 32.52 0 0 0 1.803-0.667c0.142-0.057 0.282-0.119 0.424-0.178a32.563 32.563 0 0 0 1.84-0.834c0.061-0.03 0.124-0.058 0.185-0.089a31.544 31.544 0 0 0 2.047-1.124l0.131-0.08a31.852 31.852 0 0 0 4.053-2.901 31.265 31.265 0 0 0 1.71-1.538l0.046-0.047c0.497-0.486 0.972-0.991 1.434-1.506 0.137-0.152 0.275-0.304 0.409-0.46 0.433-0.502 0.852-1.014 1.252-1.54 0.146-0.192 0.285-0.389 0.427-0.585 0.308-0.424 0.605-0.855 0.891-1.293 0.128-0.197 0.26-0.39 0.385-0.59 0.343-0.55 0.663-1.114 0.971-1.684a32.167 32.167 0 0 0 1.232-2.526c0.056-0.129 0.107-0.26 0.161-0.39 0.323-0.773 0.623-1.556 0.885-2.353l90.452-271.356c0.349-1.042 0.645-2.103 0.886-3.179l0.011-0.053c0.109-0.492 0.208-0.988 0.294-1.486 0.022-0.128 0.038-0.255 0.058-0.383 0.062-0.387 0.122-0.774 0.17-1.163 0.023-0.185 0.038-0.37 0.057-0.555 0.035-0.331 0.07-0.663 0.095-0.996 0.017-0.226 0.025-0.452 0.036-0.677 0.015-0.292 0.032-0.583 0.039-0.876 0.006-0.26 0.003-0.519 0.004-0.778 0-0.256 0.003-0.515-0.003-0.775z m-147.803 247.01l-195.635 65.212 65.212-195.635 195.635-65.212-65.212 195.635z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineFind.defaultProps = {
  size: 18,
};

export default IconLineFind;
