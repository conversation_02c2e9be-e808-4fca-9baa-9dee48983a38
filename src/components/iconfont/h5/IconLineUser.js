/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineUser = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M895.296 734.064c-20.954-49.541-50.945-94.026-89.14-132.221s-82.68-68.186-132.221-89.14a416.054 416.054 0 0 0-30.278-11.437 226.96 226.96 0 0 0 26.734-22.875C712.7 436.084 736 379.833 736 320s-23.3-116.084-65.608-158.392C628.084 119.3 571.832 96 512 96c-59.833 0-116.084 23.3-158.393 65.608C311.3 203.916 288 260.167 288 320s23.3 116.084 65.607 158.392a226.96 226.96 0 0 0 26.734 22.875 416.054 416.054 0 0 0-30.278 11.437c-49.541 20.954-94.026 50.945-132.221 89.14s-68.185 82.68-89.139 132.221C107.003 785.371 96 839.854 96 896c0 17.673 14.327 32 32 32s32-14.327 32-32c0-94.022 36.614-182.418 103.099-248.901C329.582 580.614 417.978 544 512 544s182.417 36.614 248.901 103.099S864 801.978 864 896c0 17.673 14.327 32 32 32s32-14.327 32-32c0-56.146-11.004-110.629-32.704-161.936zM352 320c0-88.224 71.775-160 160-160s160 71.776 160 160-71.775 160-160 160-160-71.776-160-160z"
        fill={getIconColor(color, 0, '#333333')}
      />
    </svg>
  );
};

IconLineUser.defaultProps = {
  size: 18,
};

export default IconLineUser;
