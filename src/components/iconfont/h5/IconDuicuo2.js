/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconDuicuo2 = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M512 960A448 448 0 1 0 512 64a448 448 0 0 0 0 896z m226.304-674.24a44.8 44.8 0 0 1 0 63.36L575.424 512.064l162.88 162.88a44.8 44.8 0 1 1-63.36 63.36L512.064 575.424l-162.944 162.88a44.8 44.8 0 1 1-63.36-63.36l162.944-162.88-162.944-162.944a44.8 44.8 0 1 1 63.36-63.36l162.944 162.944 162.88-162.944a44.8 44.8 0 0 1 63.36 0z"
        fill={getIconColor(color, 0, '#F21D1D')}
      />
    </svg>
  );
};

IconDuicuo2.defaultProps = {
  size: 18,
};

export default IconDuicuo2;
