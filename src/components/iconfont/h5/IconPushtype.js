/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconPushtype = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M430.5 715.8H170.6V201.7h634.9v105c0 19.3 15.7 35 35 35s35-15.7 35-35v-108c0-37-30-66.9-66.9-66.9h-641c-37 0-66.9 30-66.9 66.9V719c0 37 30 66.9 66.9 66.9h263c19.3 0 35-15.7 35-35-0.1-19.4-15.8-35.1-35.1-35.1z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M910 392.2c-10.5-10.5-26.3-13.8-40.1-8.5l-470.2 182c-13.5 5.2-22.9 17.9-23.9 32.4-1 14.4 6.5 28.4 19.1 35.4L570.8 732l97.8 175.2c6.6 11.9 19.3 19.3 32.8 19.3 0.9 0 1.7 0 2.6-0.1 14.5-1 27.2-10.4 32.4-23.9l182-470.2c5.4-13.8 2.1-29.6-8.4-40.1zM696.1 812.9L640 712.4l35.7-35.7c13.7-13.7 13.7-35.8 0-49.5-13.7-13.7-35.8-13.7-49.5 0l-35.7 35.7-101.3-56.7 337.5-130.7-130.6 337.4zM238.7 288.5h378.6v70H238.7zM238.7 438.3h192.8v70H238.7z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconPushtype.defaultProps = {
  size: 18,
};

export default IconPushtype;
