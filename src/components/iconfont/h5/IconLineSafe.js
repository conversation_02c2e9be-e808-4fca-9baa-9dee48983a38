/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLineSafe = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M512 64L128 207.921v251.135c-0.014 141.513 68.574 274.488 184.512 357.718L512 960l199.488-143.289C827.389 733.507 895.973 600.588 896 459.119V208.047L512 64z m320 395.056c-0.009 121.27-58.793 235.216-158.144 306.543L512 881.848 350.144 765.599C250.793 694.272 192.009 580.326 192 459.056V251.514l320-120.04 320 120.04v207.542z"
        fill={getIconColor(color, 0, '#333333')}
      />
      <path
        d="M384 384c0 59.534 40.855 109.705 96 123.95V704c0 17.673 14.327 32 32 32 17.673 0 32-14.327 32-32v-64h64c17.673 0 32-14.327 32-32s-14.327-32-32-32h-64v-68.05c55.145-14.245 96-64.416 96-123.95 0-70.58-57.421-128-128-128-70.58 0-128 57.42-128 128z m192 0c0 35.29-28.71 64-64 64s-64-28.71-64-64 28.71-64 64-64 64 28.71 64 64z"
        fill={getIconColor(color, 1, '#333333')}
      />
    </svg>
  );
};

IconLineSafe.defaultProps = {
  size: 18,
};

export default IconLineSafe;
