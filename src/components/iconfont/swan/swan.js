Component({
    properties: {
        // arrow-right-circle-line | arrow-right-circle-fill | image | pdf | zhuceshibai | biyejieye | xunliandaka-xuanzhong | a-xueshengxiuxuetuixue | duicuo2 | duicuo1 | a-shoucang-yishoucang | weishoucang | mimi | mimimiji | paihang | shoucang | jiaotongtubiao-youzhuan | liucheng-suijiqi1 | tikuzongliang | icon-zhuanxianglianxi | VIP1 | VIP | line-qrcode | line-picture | line-relation | line-reduceuser1 | line-purchase | line-matting | line-organizational | line-role | line-report | line-service | line-phone | line-shoppingcart | line-shop | line-see | line-system | line-shipment | line-ranking | line-template | line-time | line-send | line-news | line-team1 | line-safe | line-statistics | line-userconfirmation1 | line-video | line-wallet | line-workset | line-share | line-transaction | logo-moment | line-signout | logo-wechat | line-task | line-addcommodity | line-accurate | line-briefcase | line-calendar | line-achievement | line-check1 | line-application | line-businesscard | line-coin | line-card | line-applets | line-commodity | line-assembly | line-company | line-ancrown | line-code | line-collection | line-contacts | line-discount | line-deleteuser | line-delete | line-cooperation | line-collect | line-download | line-examine1 | line-express | line-eye | line-healthreport | line-file | line-location | line-list | line-open | line-find | line-message | line-pc | line-partake | line-link | line-label | line-order | line-notice | line-home | line-overstep | refresh | identity-card | line-control | line-reward | line-me | line-team | line-userconfirmation | line-user | line-reduceuser | line-scan | line-check | line-schedule | pay-successful | Discount | Buy | Ticket | Send | gantanhao | user-plus | hospital-user | person-circle-plus | nfc | add | managerexam | pushtype | gdsbrand | line-examine | engexam | finexam | purexam | Chart | CloseSquare | Logout | user-gear | comments | house | comment-dots | calculator | cellphone-iphone | comment-account-outline | comment-check-outline | comment-multiple-outline | comment-text-outline | AddUser | square-phone | message | id-card | mobile-retro | list | profile | addressbook | activity | friendadd | bad | peoplelist | exit | Location | Arrow-Right2 | Arrow-RightSquare | Arrow-Right | Category | Filter | Setting | Arrow-Down2 | Arrow-Down3 | Arrow-Down | Arrow-DownCircle | Arrow-Left2 | Home1 | Arrow-Left | Home | 008-man | 014-woman | home1 | home | left-arrow | left-btn | left-btn-fill | home-fill | search | money-check | id-card-clip | user-large | phone | Exit | jiantou | icon-keyboard | tuichu | dianpuzhuye | dingdanguanli | xitongguanli | Password | Profile | a-ShieldDone | Calling | Wallet | Work
        name: {
            type: String,
        },
        // string | string[]
        color: {
            type: null,
            value: '',
            observer: function (color) {
                this.setData({
                    colors: this.fixColor(color),
                    isStr: typeof color === 'string',
                });
            }
        },
        size: {
            type: Number,
            value: 18,
            observer: function (size) {
                try {
                    const systemInfo = swan.getSystemInfoSync();
                    const windowWidth = systemInfo && systemInfo.windowWidth ? systemInfo.windowWidth : 375;
                    this.setData({
                        svgSize: size / 750 * windowWidth,
                    });
                } catch (error) {
                    console.warn('获取系统信息失败:', error);
                    this.setData({
                        svgSize: size / 750 * 375,
                    });
                }
            },
        },
    },
    data: {
        colors: '',
        svgSize: (function () {
            try {
                const systemInfo = swan.getSystemInfoSync();
                const windowWidth = systemInfo && systemInfo.windowWidth ? systemInfo.windowWidth : 375;
                return 18 / 750 * windowWidth;
            } catch (error) {
                console.warn('获取系统信息失败:', error);
                return 18 / 750 * 375;
            }
        })(),
        quot: '"',
        isStr: true,
    },
    methods: {
        fixColor: function () {
            var color = this.data.color;
            var hex2rgb = this.hex2rgb;

            if (typeof color === 'string') {
                return color.indexOf('#') === 0 ? hex2rgb(color) : color;
            }

            return color.map(function (item) {
                return item.indexOf('#') === 0 ? hex2rgb(item) : item;
            });
        },
        hex2rgb: function (hex) {
            var rgb = [];

            hex = hex.substr(1);

            if (hex.length === 3) {
                hex = hex.replace(/(.)/g, '$1$1');
            }

            hex.replace(/../g, function (color) {
                rgb.push(parseInt(color, 0x10));
                return color;
            });

            return 'rgb(' + rgb.join(',') + ')';
        }
    }
});
