import {Component} from 'react'
import './dialog.scss'
import {Dialog} from '@nutui/nutui-react-taro';


class dialog extends Component {
    props = {
        title: '',
        content: '',
        visible: false,
        okClick: () => {
        },
        cancelClick: () => {
        }
    }

    constructor(props) {
        super(props);
    }

    show = () => {
        console.log('show')
    }

    render() {
        return (
            <>
                <Dialog
                    title={this.props.title}
                    visible={this.props.visible}
                    onOk={() => {
                        this.props.okClick();
                    }}
                    onCancel={() => {
                        this.props.cancelClick();
                    }}
                >
                    {this.props.content}
                </Dialog>
            </>
        )
    }
}

export default dialog;
