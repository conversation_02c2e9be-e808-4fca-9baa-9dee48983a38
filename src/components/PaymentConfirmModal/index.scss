.payment-confirm-modal {
    &.closing {
        .modal-mask {
            animation: fadeOut 0.3s ease forwards;
        }

        .modal-content {
            animation: slideDown 0.3s ease forwards;
        }
    }

    .modal-mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        z-index: 100;
        opacity: 0;
        animation: fadeIn 0.3s ease forwards;
    }

    .modal-content {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        background: #fff;
        border-radius: 24rpx 24rpx 0 0;
        z-index: 101;
        padding: 40rpx;
        transform: translateY(100%);
        animation: slideUp 0.3s ease forwards;

        .modal-header {
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40rpx;

            .title-wrapper {
                position: absolute;
                left: 50%;
                transform: translateX(-50%);

                .title {
                    font-size: 36rpx;
                    font-weight: 500;
                    color: #333;
                }
            }

            .close-btn {
                font-size: 48rpx;
                color: #999;
                line-height: 1;
                position: relative;
                z-index: 1;
            }
        }

        .title-section {
            text-align: center;
            margin-bottom: 40rpx;

            .title {
                font-size: 36rpx;
                font-weight: 500;
                color: #333;
            }
        }

        .merchant-section {
            text-align: center;
            margin-bottom: 40rpx;

            .merchant-name {
                font-size: 32rpx;
                color: #333;
            }
        }

        .amount-section {
            text-align: center;
            margin-top: 100rpx;
            margin-bottom: 100rpx;

            .amount {
                .currency {
                    font-size: 40rpx;
                    margin-right: 8rpx;
                }

                .value {
                    font-size: 72rpx;
                    font-weight: 500;
                }
            }
        }

        .payment-method {
            margin-bottom: 60rpx;

            .method-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 24rpx 0;
                border-bottom: 1px solid #f5f5f5;

                .method-label {
                    color: #666;
                    font-size: 28rpx;
                }

                .method-value {
                    display: flex;
                    align-items: center;
                    gap: 4px;

                    .wechat-icon {
                        color: #07c160;
                        font-size: 16px;
                    }
                }
            }
        }

        .confirm-btn {
            height: 88rpx !important;
            font-size: 32rpx !important;
            border-radius: 44rpx !important;
            background: #2b65d9 !important;
        }
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(100%);
    }
}
