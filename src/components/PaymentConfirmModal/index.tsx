import React, { useState, useEffect } from "react";
import { View, Text } from "@tarojs/components";
import { Button } from "@nutui/nutui-react-taro";
import "./index.scss";
import { Close } from "@nutui/icons-react-taro";
import request from "@/service/request";
import Taro from "@tarojs/taro";
import { message } from "@/components/MessageApi/MessageApiSingleton";

interface PaymentConfirmModalProps {
    visible: boolean;
    amount: string;
    merchantName: string;
    remark: string;
    accountId: string;
    onClose: () => void;
    onConfirm: () => void;
}

const PaymentConfirmModal: React.FC<PaymentConfirmModalProps> = ({ visible, amount, merchantName, remark, accountId, onClose, onConfirm }) => {
    const [isClosing, setIsClosing] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        if (!visible) {
            setIsClosing(false);
        }
    }, [visible]);

    const handleClose = () => {
        setIsClosing(true);
        setTimeout(() => {
            onClose();
        }, 300);
    };

    const handleConfirm = async () => {
        if (!accountId) {
            message.error("商户信息无效");
            return;
        }

        setIsLoading(true);
        message.openLoading("正在处理支付");

        try {
            const response = await request.put<any>(`/Pay/CreatePay/${accountId}`, {
                PayMoney: parseFloat(amount),
                remark: remark,
            });

            if (response && response.success) {
                const payInfo = response.data.SignData;
                setIsLoading(true);

                Taro.requestPayment({
                    timeStamp: payInfo.timeStamp,
                    nonceStr: payInfo.nonceStr,
                    package: payInfo.package,
                    signType: payInfo.signType,
                    paySign: payInfo.paySign,
                    success: function () {
                        message.closeLoading();
                        message.success("支付成功");
                        handleClose();
                        onConfirm();
                    },
                    fail: function (res) {
                        message.closeLoading();
                        setIsLoading(false);
                        if (res.errMsg === "requestPayment:fail cancel") {
                            message.error("交易已取消");
                        } else {
                            message.error("支付失败：" + res.errMsg);
                        }
                    },
                });
            } else {
                message.closeLoading();
                message.error(response?.message || "支付失败");
            }
        } catch (error) {
            message.closeLoading();
            message.error("支付失败，请重试");
        } finally {
            setIsLoading(false);
        }
    };

    if (!visible && !isClosing) return null;

    return (
        <View className={`payment-confirm-modal ${isClosing ? "closing" : ""}`}>
            <View className="modal-mask" onClick={handleClose} />
            <View className="modal-content">
                <View className="modal-header">
                    <View className="title-wrapper">
                        <Text className="title">确认支付</Text>
                    </View>
                    <View className="close-btn" onClick={handleClose}>
                        <Close />
                    </View>
                </View>

                <View className="merchant-section">
                    <Text className="merchant-name">{merchantName}</Text>
                </View>

                <View className="amount-section">
                    <Text className="amount">
                        <Text className="currency">¥</Text>
                        <Text className="value">{amount}</Text>
                    </Text>
                </View>

                <View className="payment-method">
                    <View className="method-item">
                        <Text className="method-label">支付方式</Text>
                        <View className="method-value">
                            <Text>微信支付</Text>
                            <Text className="arrow">›</Text>
                        </View>
                    </View>
                </View>

                <Button className="confirm-btn" block type="primary" onClick={handleConfirm} loading={isLoading} disabled={isLoading}>
                    {isLoading ? "处理中..." : "支付"}
                </Button>
            </View>
        </View>
    );
};

export default PaymentConfirmModal;
