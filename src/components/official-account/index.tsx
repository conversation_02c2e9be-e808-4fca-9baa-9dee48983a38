import React from "react";
import { View } from "@tarojs/components";
import Taro from "@tarojs/taro";

// 声明微信原生组件类型
declare namespace JSX {
    interface IntrinsicElements {
        "official-account": {
            className?: string;
            onLoad?: () => void;
            onError?: (e: any) => void;
        };
    }
}

// 定义 Props 类型
interface OfficialAccountProps {
    className?: string;
    onLoad?: () => void;
    onError?: (e: any) => void;
}

const OfficialAccount: React.FC<OfficialAccountProps> = (props) => {
    // 使用React.useEffect来确保在组件挂载后通过Taro API操作native components
    React.useEffect(() => {
        // 微信环境下才会有官方账号组件
        if (Taro.getEnv() === Taro.ENV_TYPE.WEAPP) {
            console.log("Rendering official account component");
        }
    }, []);

    // 使用dangerouslySetInnerHTML来直接插入原生组件标签
    return (
        <View>
            {/* 微信原生组件 */}
            {/* @ts-ignore */}
            <official-account className={props.className} onLoad={props.onLoad} onError={props.onError} />
        </View>
    );
};

export default OfficialAccount;
