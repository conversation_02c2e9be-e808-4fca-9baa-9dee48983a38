import Taro from "@tarojs/taro";
import { Component } from "react";
import "./index.scss";
import "@utils/app.scss";
import { NavBar } from "@nutui/nutui-react-taro";
import IconFont from "@components/iconfont";

interface State {
    statusBarHeight: number;
}

interface Props {
    title: string;
    leftClick?: any;
    homeClick?: any;
    Bg?: string;
    BgColor?: string;
    FontColor?: string;
    hideLeft?: boolean;
    bottomContent?: React.ReactNode;
}

let onLeftClick: any = undefined;
let onHomeClick: any = undefined;

class TopNavBar extends Component<Props, State> {
    constructor(props: Props | Readonly<Props>) {
        super(props);

        this.state = {
            statusBarHeight: 0,
        };

        onLeftClick = this.props.leftClick;
        onHomeClick = this.props.homeClick;
    }

    componentDidMount() {
        // 延迟获取系统信息，确保组件完全挂载
        setTimeout(() => {
            Taro.getSystemInfo({})
                .then((res) => {
                    this.setState({
                        statusBarHeight: res.statusBarHeight ? res.statusBarHeight : 0,
                    });
                })
                .catch((error) => {
                    console.warn("获取系统信息失败:", error);
                    // 使用默认值
                    this.setState({
                        statusBarHeight: 20,
                    });
                });
        }, 100);
    }

    leftClick() {
        if (onLeftClick) {
            onLeftClick();
        } else {
            Taro.navigateBack({
                delta: 1, //表示回到上一页面
            });
        }
    }

    homeClick() {
        if (onHomeClick) {
            onHomeClick();
        } else {
            const entryPagePath = Taro.getApp().config.entryPagePath;
            Taro.reLaunch({
                url: "/" + entryPagePath,
            });
        }
    }

    render() {
        // 如果状态栏高度还没有获取到，先不渲染 NavBar
        if (this.state.statusBarHeight === 0) {
            return <view style={{ height: "88px", backgroundColor: this.props.BgColor || "#fff" }}>{/* 占位元素，防止页面跳动 */}</view>;
        }

        return (
            <>
                <view
                    style={{
                        paddingTop: `${this.state.statusBarHeight}px`,
                        background: `${this.props.Bg !== "" && this.props.Bg ? this.props.Bg : ""}`,
                        backgroundColor: `${this.props.BgColor !== "" && this.props.BgColor ? this.props.BgColor : "#fff"}`,
                        position: "fixed",
                        top: "0px",
                        width: "100%",
                        zIndex: 19,
                    }}
                >
                    <NavBar
                        // leftShow={false}
                        style={{
                            background: `${this.props.Bg !== "" && this.props.Bg ? this.props.Bg : ""}`,
                            backgroundColor: `${this.props.BgColor !== "" && this.props.BgColor ? this.props.BgColor : "#fff"}`,
                            // boxShadow: `${this.props.BgColor !== '' && this.props.BgColor ? '0 0 black' : '0 1px 1px 0px #edeef1'}`,
                            WebkitBoxShadow: `${this.props.BgColor !== "" && this.props.BgColor ? "0 0 black" : "0px 1px 7px 0px rgb(237, 238, 241)"}`,
                            marginBottom: "0px",
                            position: "relative",
                            width: "100%",
                        }}
                        // onClickTitle={(e) => { }}
                        // onClickBack={(e) => {
                        //     console.log('onClickBack');
                        // }}
                        back={
                            !this.props.hideLeft && (
                                <>
                                    {this.props.leftClick && (
                                        <view onClick={this.leftClick}>
                                            <IconFont
                                                name="Arrow-Left2"
                                                size={45}
                                                color={`${this.props.FontColor !== "" && this.props.FontColor ? this.props.FontColor : "#000"}`}
                                            />
                                        </view>
                                    )}
                                    <view
                                        onClick={this.homeClick}
                                        style={{
                                            marginLeft: "15rpx",
                                        }}
                                    >
                                        <IconFont name="Home1" size={45} color={`${this.props.FontColor !== "" && this.props.FontColor ? this.props.FontColor : "#000"}`} />
                                    </view>
                                </>
                            )
                        }
                    >
                        <view
                            style={{
                                color: `${this.props.FontColor !== "" && this.props.FontColor ? this.props.FontColor : "#000"}`,
                                fontWeight: 380,
                                fontSize: 16,
                            }}
                        >
                            {this.props.title}
                        </view>
                    </NavBar>
                    {this.props.bottomContent && <view style={{ width: "100%" }}>{this.props.bottomContent}</view>}
                </view>
                <view style={{ height: `${this.state.statusBarHeight + 44}px` }}></view>
            </>
        );
    }
}

export default TopNavBar;
