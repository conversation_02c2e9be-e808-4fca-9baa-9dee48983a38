{"version": "2.0.0", "tasks": [{"type": "npm", "script": "build:weapp", "group": "build", "problemMatcher": [], "label": "npm: build:weapp", "detail": "taro build --type weapp"}, {"label": "Git Push Daily", "type": "shell", "command": "git", "args": ["add", ".", "&&", "git", "commit", "-m", "daily", "&&", "git", "push", "-u", "origin", "master", "-f"], "group": {"kind": "build", "isDefault": true}, "problemMatcher": []}]}