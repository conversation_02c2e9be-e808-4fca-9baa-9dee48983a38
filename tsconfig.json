{"compilerOptions": {"skipLibCheck": true, "baseUrl": ".", "target": "es2017", "module": "commonjs", "removeComments": false, "preserveConstEnums": true, "moduleResolution": "node", "experimentalDecorators": true, "noImplicitAny": false, "allowSyntheticDefaultImports": true, "outDir": "lib", "noUnusedLocals": true, "noUnusedParameters": true, "strictNullChecks": true, "sourceMap": true, "rootDir": ".", "jsx": "preserve", "allowJs": true, "resolveJsonModule": true, "typeRoots": ["node_modules/@types", "types"], "paths": {"@/*": ["src/*"], "@types/*": ["src/types/*"], "@components/*": ["src/components/*"], "@pages/*": ["src/pages/*"], "@utils/*": ["src/utils/*"], "@service/*": ["src/service/*"], "@pay/*": ["src/pay/*"], "@config/*": ["src/config/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "types/*.d.ts"]}