{"name": "pan<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "default", "typescript": true, "css": "sass"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.24.7", "@nutui/icons-react-taro": "^1.0.2", "@nutui/nutui-biz": "^1.0.0-beta.2", "@nutui/nutui-react": "^2.6.16", "@nutui/nutui-react-taro": "^2.6.16", "@tarojs/helper": "^4.0.9", "@tarojs/plugin-framework-react": "^4.0.9", "@tarojs/plugin-html": "^4.0.9", "@tarojs/plugin-platform-alipay": "^4.0.9", "@tarojs/plugin-platform-h5": "^4.0.9", "@tarojs/plugin-platform-jd": "^4.0.9", "@tarojs/plugin-platform-qq": "^4.0.9", "@tarojs/plugin-platform-swan": "^4.0.9", "@tarojs/plugin-platform-tt": "^4.0.9", "@tarojs/plugin-platform-weapp": "^4.0.9", "@tarojs/react": "^4.0.9", "@tarojs/router": "^4.0.9", "@tarojs/runtime": "^4.0.9", "@tarojs/shared": "^4.0.9", "@tarojs/taro": "^4.0.9", "@tarojs/taro-h5": "^4.0.9", "base-64": "^1.0.0", "dayjs": "^1.11.13", "jest-worker": "^29.7.0", "moment": "^2.29.4", "react": "^18.3.1", "react-dom": "^18.3.1", "taro-code": "^4.0.1", "taro-cropper": "^1.2.4"}, "devDependencies": {"@babel/core": "^7.24.7", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.15", "@tarojs/cli": "^4.0.9", "@tarojs/webpack5-runner": "^4.0.9", "@types/react": "^18.3.3", "@types/sass": "1.43.1", "@types/webpack-env": "^1.18.5", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "autoprefixer": "^10.4.20", "babel-plugin-import": "^1.13.6", "babel-preset-taro": "^3.6.32", "eslint": "^9.5.0", "eslint-config-taro": "^4.0.9", "eslint-plugin-import": "^2.27.5", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "postcss": "^8.4.49", "react-refresh": "^0.14.2", "stylelint": "^16.6.1", "tailwindcss": "^3.4.17", "taro-iconfont-cli": "^3.3.0", "typescript": "^5.5.2", "webpack": "5.92.1"}}