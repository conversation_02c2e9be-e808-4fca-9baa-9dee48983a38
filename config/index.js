import path from 'path'


const config = {
    projectName: 'PandaWxApp',
    date: '2023-9-28',
    designWidth(input) {
        // 配置 NutUI 375 尺寸
        if (input?.file?.replace(/\\+/g, '/').indexOf('@nutui') > -1) {
            return 375
        }
        // 全局使用 Taro 默认的 750 尺寸
        return 750
    },
    deviceRatio: {
        640: 2.34 / 2,
        750: 1,
        828: 1.81 / 2,
        375: 2 / 1,
    },
    sourceRoot: 'src',
    outputRoot: 'dist',
    defineConstants: {},
    copy: {
        patterns: [],
        options: {}
    },
    framework: 'react',
    compiler: {
        type: 'webpack5',
        prebundle: {
            enable: false
        }
    },
    cache: {
        enable: false // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
    },
    // cache: {
    //     enable: true,
    //     type: 'filesystem',
    //     cacheDirectory: 'node_modules/.cache/taro-build'
    //   },
    mini: {
        enableExtract: true,
        miniCssExtractPluginOption: {
            //忽略css文件引入顺序
            ignoreOrder: true
        },
        webpackChain(chain) {
            // 处理字体等二进制文件
            chain.module
                .rule('asset')
                .test(/\.(woff2?|eot|ttf|otf|png|jpg|jpeg|gif|svg)(\?.*)?$/)
                .type('asset')
                .parser({
                    dataUrlCondition: {
                        maxSize: 10 * 1024 // 10kb
                    }
                });
        },
        postcss: {
            pxtransform: {
                enable: true,
                config: {
                    selectorBlackList: ['nut-']
                }
            },
            url: {
                enable: true,
                config: {
                    limit: 1024 // 设定转换尺寸上限
                }
            },
            cssModules: {
                enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
                config: {
                    namingPattern: 'module', // 转换模式，取值为 global/module
                    generateScopedName: '[name]__[local]___[hash:base64:5]'
                }
            }
        },
    },
    h5: {
        esnextModules: ['taro-cropper'],
        publicPath: '/',
        staticDirectory: 'static',
        router: {
            mode: "browser", // hash 或者是 browser
        },
        postcss: {
            autoprefixer: {
                enable: true,
                config: {}
            },
            cssModules: {
                enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
                config: {
                    namingPattern: 'module', // 转换模式，取值为 global/module
                    generateScopedName: '[name]__[local]___[hash:base64:5]'
                }
            }
        }
    },
    rn: {
        appName: 'taroDemo',
        postcss: {
            cssModules: {
                enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
            }
        }
    },
    plugins: ['@tarojs/plugin-html'],
    sass: {
        data: `@use "@nutui/nutui-react-taro/dist/styles/variables.scss" as *;`
    },
    alias: {
        '@assets': path.resolve(__dirname, '..', 'src/assets'),
        '@/assets': path.resolve(__dirname, '..', 'src/assets'),
        '@components': path.resolve(__dirname, '..', 'src/components'),
        '@/components': path.resolve(__dirname, '..', 'src/components'),
        '@utils': path.resolve(__dirname, '..', 'src/utils'),
        '@/utils': path.resolve(__dirname, '..', 'src/utils'),
        "@config": path.resolve(__dirname, '..', 'src/config'),
        "@/config": path.resolve(__dirname, '..', 'src/config'),
        "@images": path.resolve(__dirname, '..', 'src/images'),
        "@/images": path.resolve(__dirname, '..', 'src/images'),
        "@pages": path.resolve(__dirname, '..', 'src/pages'),
        "@/pages": path.resolve(__dirname, '..', 'src/pages'),
        "@service": path.resolve(__dirname, '..', 'src/service'),
        "@/service": path.resolve(__dirname, '..', 'src/service'),
        "@pay": path.resolve(__dirname, '..', 'src/pay'),
        "@/pay": path.resolve(__dirname, '..', 'src/pay'),
    },
}

module.exports = function (merge) {

    if (process.env.NODE_ENV === 'development') {
        return merge({}, config, require('./dev'))
    }
    return merge({}, config, require('./prod'))
}
