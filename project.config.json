{"miniprogramRoot": "dist/", "pluginRoot": "plugin/", "projectname": "PandaWxApp", "description": "", "appid": "wxc77987296fe617c5", "setting": {"urlCheck": false, "es6": false, "enhance": false, "compileHotReLoad": false, "postcss": false, "minified": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "packNpmRelationList": [], "condition": true, "minifyWXML": true}, "compileType": "miniprogram", "libVersion": "3.0.2", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}, "projectArchitecture": "multiPlatform", "simulatorPluginLibVersion": {"wxext14566970e7e9f62": "3.0.2-8"}}